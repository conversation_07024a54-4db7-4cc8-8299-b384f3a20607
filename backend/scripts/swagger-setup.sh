#!/bin/bash

# 九翼ERP系统 - Swagger文档快速设置脚本
# 解决工具路径问题，支持多种环境

set -e

# 颜色定义
BLUE='\033[34m'
GREEN='\033[32m'
YELLOW='\033[33m'
RED='\033[31m'
NC='\033[0m'

echo -e "${BLUE}🚀 九翼ERP系统 - Swagger文档快速设置${NC}"
echo "================================================"

# 获取Go环境信息
echo -e "${BLUE}📋 检查Go环境...${NC}"
if ! command -v go &> /dev/null; then
    echo -e "${RED}❌ Go未安装或未添加到PATH${NC}"
    exit 1
fi

GOPATH=$(go env GOPATH)
GOBIN=$(go env GOBIN)
if [ -z "$GOBIN" ]; then
    GOBIN="$GOPATH/bin"
fi

echo -e "${GREEN}✅ Go环境正常${NC}"
echo "Go版本: $(go version)"
echo "GOPATH: $GOPATH"
echo "GOBIN: $GOBIN"

# 检查项目目录
echo -e "${BLUE}📂 检查项目目录...${NC}"
if [ ! -f "go.mod" ]; then
    echo -e "${RED}❌ 请在项目根目录运行此脚本${NC}"
    exit 1
fi
echo -e "${GREEN}✅ 项目目录正确${NC}"

# 检查并添加GOBIN到PATH
echo -e "${BLUE}🔍 检查PATH配置...${NC}"
if [[ ":$PATH:" != *":$GOBIN:"* ]]; then
    echo -e "${YELLOW}⚠️  GOBIN未在PATH中，临时添加...${NC}"
    export PATH="$PATH:$GOBIN"
    echo "建议将以下命令添加到您的 ~/.bashrc 或 ~/.zshrc："
    echo "export PATH=\"\$PATH:$GOBIN\""
else
    echo -e "${GREEN}✅ GOBIN已在PATH中${NC}"
fi

# 安装Swagger工具
echo -e "${BLUE}🔧 安装Swagger工具...${NC}"
echo "安装到: $GOBIN"

# 安装swag工具
echo "正在安装 swag..."
if go install github.com/swaggo/swag/cmd/swag@latest; then
    echo -e "${GREEN}✅ swag工具安装成功${NC}"
else
    echo -e "${RED}❌ swag工具安装失败${NC}"
    exit 1
fi

# 验证swag工具
echo -e "${BLUE}🔍 验证swag工具...${NC}"
SWAG_PATH="$GOBIN/swag"
if [ -f "$SWAG_PATH" ]; then
    echo -e "${GREEN}✅ swag工具文件存在: $SWAG_PATH${NC}"
    if "$SWAG_PATH" --version &> /dev/null; then
        echo -e "${GREEN}✅ swag工具可执行${NC}"
        "$SWAG_PATH" --version
    else
        echo -e "${RED}❌ swag工具不可执行${NC}"
        ls -la "$SWAG_PATH"
        exit 1
    fi
elif command -v swag &> /dev/null; then
    echo -e "${GREEN}✅ swag工具在PATH中可用${NC}"
    swag --version
    SWAG_PATH="swag"
else
    echo -e "${RED}❌ swag工具未找到${NC}"
    echo "检查以下路径："
    echo "  - $SWAG_PATH"
    echo "  - PATH中的swag命令"
    ls -la "$GOBIN/" | grep swag || echo "GOBIN中未找到swag相关文件"
    exit 1
fi

# 安装可选的swagger验证工具
echo -e "${BLUE}🔧 安装swagger验证工具 (可选)...${NC}"
if go install github.com/go-swagger/go-swagger/cmd/swagger@latest; then
    echo -e "${GREEN}✅ swagger验证工具安装成功${NC}"
else
    echo -e "${YELLOW}⚠️  swagger验证工具安装失败，但不影响文档生成${NC}"
fi

# 安装依赖
echo -e "${BLUE}📦 安装项目依赖...${NC}"
if go mod tidy; then
    echo -e "${GREEN}✅ 依赖安装成功${NC}"
else
    echo -e "${RED}❌ 依赖安装失败${NC}"
    exit 1
fi

# 创建文档目录
echo -e "${BLUE}📁 创建文档目录...${NC}"
mkdir -p docs
mkdir -p api/openapi

# 生成Swagger文档
echo -e "${BLUE}📝 生成Swagger文档...${NC}"
echo "使用工具: $SWAG_PATH"
echo "命令: $SWAG_PATH init -g cmd/api/main.go -o docs --parseInternal --parseDependency --parseDepth 2"

if "$SWAG_PATH" init -g cmd/api/main.go -o docs --parseInternal --parseDependency --parseDepth 2; then
    echo -e "${GREEN}✅ Swagger文档生成成功${NC}"
else
    echo -e "${RED}❌ Swagger文档生成失败${NC}"
    echo -e "${YELLOW}💡 可能的原因:${NC}"
    echo "  1. 代码中的Swagger注释格式有误"
    echo "  2. 导入的包有问题"
    echo "  3. main.go文件路径不正确"
    echo ""
    echo -e "${BLUE}🔍 检查文件结构:${NC}"
    ls -la cmd/api/
    exit 1
fi

# 验证生成的文档
echo -e "${BLUE}🔍 验证生成的文档...${NC}"
if [ -f "docs/swagger.json" ]; then
    echo -e "${GREEN}✅ swagger.json 生成成功${NC}"
    echo "文件大小: $(wc -c < docs/swagger.json) 字节"
else
    echo -e "${RED}❌ swagger.json 未生成${NC}"
    ls -la docs/
    exit 1
fi

if [ -f "docs/docs.go" ]; then
    echo -e "${GREEN}✅ docs.go 生成成功${NC}"
else
    echo -e "${YELLOW}⚠️  docs.go 未生成，可能影响Gin集成${NC}"
fi

# 复制到openapi目录
echo -e "${BLUE}📋 复制文档到OpenAPI目录...${NC}"
cp docs/swagger.json api/openapi/ 2>/dev/null || true
if [ -f "docs/swagger.yaml" ]; then
    cp docs/swagger.yaml api/openapi/ 2>/dev/null || true
fi

# 验证文档（如果有验证工具）
echo -e "${BLUE}🔍 验证Swagger文档...${NC}"
SWAGGER_VALIDATOR="$GOBIN/swagger"
if [ -f "$SWAGGER_VALIDATOR" ]; then
    if "$SWAGGER_VALIDATOR" validate docs/swagger.json; then
        echo -e "${GREEN}✅ 文档验证通过${NC}"
    else
        echo -e "${YELLOW}⚠️  文档验证有警告，但可以继续使用${NC}"
    fi
elif command -v swagger &> /dev/null; then
    if swagger validate docs/swagger.json; then
        echo -e "${GREEN}✅ 文档验证通过${NC}"
    else
        echo -e "${YELLOW}⚠️  文档验证有警告，但可以继续使用${NC}"
    fi
else
    echo -e "${YELLOW}⚠️  swagger验证工具未安装，跳过验证${NC}"
fi

echo ""
echo -e "${GREEN}🎉 Swagger设置完成!${NC}"
echo "================================================"
echo -e "${BLUE}📖 使用说明:${NC}"
echo ""
echo -e "1. ${GREEN}启动应用:${NC}"
echo "   make dev"
echo ""  
echo -e "2. ${GREEN}访问Swagger UI:${NC}"
echo "   http://localhost:8080/swagger/index.html"
echo ""
echo -e "3. ${GREEN}常用命令:${NC}"
echo "   make swagger-gen        # 重新生成文档"
echo "   make swagger-validate   # 验证文档格式"
echo "   make swagger-serve      # 启动文档服务"
echo "   make swagger-export     # 导出OpenAPI文件"
echo "   make swagger-clean      # 清理生成的文档"
echo "   make swagger-check-env  # 检查环境配置"
echo ""
echo -e "4. ${GREEN}开发流程:${NC}"
echo "   - 在Handler方法上添加Swagger注释"
echo "   - 运行 make swagger-gen 重新生成文档"
echo "   - 在浏览器中查看更新后的文档"
echo ""
echo -e "5. ${GREEN}故障排除:${NC}"
echo "   - 如果命令不可用，请检查PATH配置"
echo "   - 运行 make swagger-check-env 检查环境"
echo "   - 使用 make swagger-gen-force 强制重新安装"
echo ""
echo -e "${YELLOW}💡 提示:${NC}"
echo "   - 详细的注释规范请查看: docs/swagger-guide.md"
echo "   - 建议将 $GOBIN 添加到您的永久PATH中"
echo "   - 生产环境可以考虑禁用Swagger UI访问"
echo "   - Docker构建会自动处理所有依赖和文档生成"
echo ""
echo -e "${BLUE}🔧 环境信息摘要:${NC}"
echo "   Go版本: $(go version | cut -d' ' -f3)"
echo "   GOBIN: $GOBIN"
echo "   swag路径: $SWAG_PATH"
echo "   文档目录: $(pwd)/docs"
echo "" 