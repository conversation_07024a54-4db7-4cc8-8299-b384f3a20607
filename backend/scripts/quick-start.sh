#!/bin/bash

# 九翼跨境电商ERP系统 - 快速启动脚本
# Quick Start Script for Nine Wings ERP System

set -e

# 颜色定义
BLUE='\033[34m'
GREEN='\033[32m'
YELLOW='\033[33m'
RED='\033[31m'
NC='\033[0m'

echo -e "${BLUE}九翼跨境电商ERP系统 - 快速启动${NC}"
echo "=========================================="

# 检查Docker是否安装
if ! command -v docker &> /dev/null; then
    echo -e "${RED}错误: Docker未安装，请先安装Docker${NC}"
    exit 1
fi

if ! command -v docker-compose &> /dev/null; then
    echo -e "${RED}错误: Docker Compose未安装，请先安装Docker Compose${NC}"
    exit 1
fi

# 检查Make是否安装
if ! command -v make &> /dev/null; then
    echo -e "${YELLOW}警告: Make未安装，将使用docker-compose命令${NC}"
    USE_MAKE=false
else
    USE_MAKE=true
fi

echo
echo -e "${YELLOW}选择启动环境:${NC}"
echo "1) 开发环境 (development) - 包含热重载和开发工具"
echo "2) 测试环境 (testing) - 用于测试和CI/CD"
echo "3) 生产环境 (production) - 包含监控和负载均衡"
echo

read -p "请选择环境 [1-3]: " choice

case $choice in
    1)
        ENV="development"
        ENV_NAME="开发环境"
        PORTS="API: http://localhost:8080, Adminer: http://localhost:8081, Redis Commander: http://localhost:8082"
        ;;
    2)
        ENV="testing"
        ENV_NAME="测试环境"
        PORTS="API: http://localhost:8080"
        ;;
    3)
        ENV="production"
        ENV_NAME="生产环境"
        PORTS="API: http://localhost:8080, Grafana: http://localhost:3000, Prometheus: http://localhost:9001"
        ;;
    *)
        echo -e "${RED}无效选择，退出${NC}"
        exit 1
        ;;
esac

echo
echo -e "${BLUE}正在启动 ${ENV_NAME}...${NC}"

# 创建必要的目录
echo -e "${YELLOW}创建必要的目录...${NC}"
mkdir -p logs/${ENV}
mkdir -p uploads/${ENV}
mkdir -p data/${ENV}/postgres
mkdir -p data/${ENV}/redis

# 复制环境配置
if [ ! -f ".env" ]; then
    echo -e "${YELLOW}创建环境配置文件...${NC}"
    cp .env.example .env
    sed -i.bak "s/^APP_ENV=.*/APP_ENV=${ENV}/" .env && rm -f .env.bak
fi

# 启动服务
echo -e "${BLUE}启动Docker服务...${NC}"
if [ "$USE_MAKE" = true ]; then
    case $ENV in
        "development")
            make dev
            ;;
        "testing")
            make test-env
            ;;
        "production")
            make prod
            ;;
    esac
else
    echo "使用docker-compose启动服务..."
    docker-compose -f docker/docker-compose.yml -f docker/docker-compose.${ENV}.yml up -d --build
fi

# 等待服务启动
echo -e "${YELLOW}等待服务启动...${NC}"
sleep 10

# 检查服务状态
echo -e "${BLUE}检查服务状态...${NC}"
if [ "$USE_MAKE" = true ]; then
    make status
else
    docker-compose -f docker/docker-compose.yml -f docker/docker-compose.${ENV}.yml ps
fi

# 健康检查
echo -e "${BLUE}进行健康检查...${NC}"
max_attempts=30
attempt=1

while [ $attempt -le $max_attempts ]; do
    if curl -f -s http://localhost:8080/health > /dev/null 2>&1; then
        echo -e "${GREEN}✓ API服务健康检查通过${NC}"
        break
    else
        if [ $attempt -eq $max_attempts ]; then
            echo -e "${RED}✗ API服务健康检查失败${NC}"
            echo "请检查日志: make logs 或 docker-compose logs"
            exit 1
        fi
        echo "等待API服务启动... ($attempt/$max_attempts)"
        sleep 2
        ((attempt++))
    fi
done

# 显示启动成功信息
echo
echo -e "${GREEN}🎉 ${ENV_NAME} 启动成功!${NC}"
echo "=========================================="
echo -e "${YELLOW}服务访问地址:${NC}"
echo "${PORTS}"
echo
echo -e "${YELLOW}健康检查:${NC} http://localhost:8080/health"
echo -e "${YELLOW}API文档:${NC} http://localhost:8080/swagger (如果已配置)"
echo
echo -e "${YELLOW}常用命令:${NC}"
if [ "$USE_MAKE" = true ]; then
    echo "查看日志: make logs"
    echo "查看状态: make status"
    echo "停止服务: make down"
    echo "数据库迁移: make db-migrate"
    echo "运行测试: make test"
else
    echo "查看日志: docker-compose -f docker/docker-compose.yml -f docker/docker-compose.${ENV}.yml logs"
    echo "查看状态: docker-compose -f docker/docker-compose.yml -f docker/docker-compose.${ENV}.yml ps"
    echo "停止服务: docker-compose -f docker/docker-compose.yml -f docker/docker-compose.${ENV}.yml down"
fi
echo
echo -e "${BLUE}开始使用九翼跨境电商ERP系统！${NC}" 