// Package openapi Code generated by swaggo/swag. DO NOT EDIT
package openapi

import "github.com/swaggo/swag"

const docTemplate = `{
    "schemes": {{ marshal .Schemes }},
    "swagger": "2.0",
    "info": {
        "description": "{{escape .Description}}",
        "title": "{{.Title}}",
        "termsOfService": "http://swagger.io/terms/",
        "contact": {
            "name": "API支持团队",
            "url": "http://www.swagger.io/support",
            "email": "<EMAIL>"
        },
        "license": {
            "name": "MIT",
            "url": "https://opensource.org/licenses/MIT"
        },
        "version": "{{.Version}}"
    },
    "host": "{{.Host}}",
    "basePath": "{{.BasePath}}",
    "paths": {
        "/auth/change-password": {
            "post": {
                "security": [
                    {
                        "ApiKeyAuth": []
                    }
                ],
                "description": "允许已认证的用户修改其密码",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "认证"
                ],
                "summary": "修改密码",
                "parameters": [
                    {
                        "description": "修改密码请求",
                        "name": "request",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/backend_internal_application_command_model.ChangePasswordCommand"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "密码修改成功",
                        "schema": {
                            "allOf": [
                                {
                                    "$ref": "#/definitions/backend_pkg_common_response.APIResponse"
                                },
                                {
                                    "type": "object",
                                    "properties": {
                                        "data": {
                                            "$ref": "#/definitions/backend_internal_application_command_model.OperationResult"
                                        }
                                    }
                                }
                            ]
                        }
                    },
                    "400": {
                        "description": "无效请求",
                        "schema": {
                            "$ref": "#/definitions/backend_pkg_common_response.APIResponse"
                        }
                    },
                    "401": {
                        "description": "无效的旧密码",
                        "schema": {
                            "$ref": "#/definitions/backend_pkg_common_response.APIResponse"
                        }
                    }
                }
            }
        },
        "/auth/check-permission": {
            "post": {
                "security": [
                    {
                        "ApiKeyAuth": []
                    }
                ],
                "description": "检查用户是否有特定资源的操作权限",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "权限"
                ],
                "summary": "检查用户权限",
                "parameters": [
                    {
                        "description": "权限检查请求",
                        "name": "request",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/backend_internal_application_query_model.CheckPermissionQuery"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "权限检查结果",
                        "schema": {
                            "allOf": [
                                {
                                    "$ref": "#/definitions/backend_pkg_common_response.APIResponse"
                                },
                                {
                                    "type": "object",
                                    "properties": {
                                        "data": {
                                            "$ref": "#/definitions/backend_internal_application_query_model.PermissionCheckResult"
                                        }
                                    }
                                }
                            ]
                        }
                    },
                    "400": {
                        "description": "无效请求",
                        "schema": {
                            "$ref": "#/definitions/backend_pkg_common_response.APIResponse"
                        }
                    }
                }
            }
        },
        "/auth/login": {
            "post": {
                "description": "认证用户并返回可访问的租户列表",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "认证"
                ],
                "summary": "用户登录 - 阶段一",
                "parameters": [
                    {
                        "description": "登录凭据",
                        "name": "request",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/backend_internal_application_command_model.LoginCommand"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "登录成功，返回租户列表",
                        "schema": {
                            "allOf": [
                                {
                                    "$ref": "#/definitions/backend_pkg_common_response.APIResponse"
                                },
                                {
                                    "type": "object",
                                    "properties": {
                                        "data": {
                                            "$ref": "#/definitions/backend_internal_application_command_model.LoginResult"
                                        }
                                    }
                                }
                            ]
                        }
                    },
                    "400": {
                        "description": "无效请求",
                        "schema": {
                            "$ref": "#/definitions/backend_pkg_common_response.APIResponse"
                        }
                    },
                    "401": {
                        "description": "无效凭据",
                        "schema": {
                            "$ref": "#/definitions/backend_pkg_common_response.APIResponse"
                        }
                    }
                }
            }
        },
        "/auth/logout": {
            "post": {
                "security": [
                    {
                        "ApiKeyAuth": []
                    }
                ],
                "description": "用户注销登录，撤销令牌",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "认证"
                ],
                "summary": "用户注销",
                "parameters": [
                    {
                        "description": "注销请求",
                        "name": "request",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/backend_internal_application_command_model.LogoutCommand"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "注销成功",
                        "schema": {
                            "allOf": [
                                {
                                    "$ref": "#/definitions/backend_pkg_common_response.APIResponse"
                                },
                                {
                                    "type": "object",
                                    "properties": {
                                        "data": {
                                            "$ref": "#/definitions/backend_internal_application_command_model.OperationResult"
                                        }
                                    }
                                }
                            ]
                        }
                    }
                }
            }
        },
        "/auth/profile": {
            "get": {
                "security": [
                    {
                        "ApiKeyAuth": []
                    }
                ],
                "description": "获取当前认证用户的个人资料信息",
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "认证"
                ],
                "summary": "获取用户个人资料",
                "responses": {
                    "200": {
                        "description": "成功返回个人资料",
                        "schema": {
                            "allOf": [
                                {
                                    "$ref": "#/definitions/backend_pkg_common_response.APIResponse"
                                },
                                {
                                    "type": "object",
                                    "properties": {
                                        "data": {
                                            "$ref": "#/definitions/backend_internal_application_query_model.UserAuthResult"
                                        }
                                    }
                                }
                            ]
                        }
                    },
                    "401": {
                        "description": "用户未认证",
                        "schema": {
                            "$ref": "#/definitions/backend_pkg_common_response.APIResponse"
                        }
                    }
                }
            }
        },
        "/auth/refresh": {
            "post": {
                "description": "使用刷新令牌获取新的访问令牌",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "认证"
                ],
                "summary": "刷新访问令牌",
                "parameters": [
                    {
                        "description": "刷新令牌请求",
                        "name": "request",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/backend_internal_application_command_model.RefreshTokenCommand"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "令牌刷新成功",
                        "schema": {
                            "allOf": [
                                {
                                    "$ref": "#/definitions/backend_pkg_common_response.APIResponse"
                                },
                                {
                                    "type": "object",
                                    "properties": {
                                        "data": {
                                            "$ref": "#/definitions/backend_internal_application_command_model.AuthResult"
                                        }
                                    }
                                }
                            ]
                        }
                    },
                    "400": {
                        "description": "无效请求",
                        "schema": {
                            "$ref": "#/definitions/backend_pkg_common_response.APIResponse"
                        }
                    },
                    "401": {
                        "description": "无效的刷新令牌",
                        "schema": {
                            "$ref": "#/definitions/backend_pkg_common_response.APIResponse"
                        }
                    }
                }
            }
        },
        "/auth/reset-password": {
            "post": {
                "description": "通过验证码重置用户密码",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "认证"
                ],
                "summary": "重置密码",
                "parameters": [
                    {
                        "description": "重置密码请求",
                        "name": "request",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/backend_internal_application_command_model.ResetPasswordCommand"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "密码重置成功",
                        "schema": {
                            "allOf": [
                                {
                                    "$ref": "#/definitions/backend_pkg_common_response.APIResponse"
                                },
                                {
                                    "type": "object",
                                    "properties": {
                                        "data": {
                                            "$ref": "#/definitions/backend_internal_application_command_model.OperationResult"
                                        }
                                    }
                                }
                            ]
                        }
                    },
                    "400": {
                        "description": "无效请求",
                        "schema": {
                            "$ref": "#/definitions/backend_pkg_common_response.APIResponse"
                        }
                    },
                    "401": {
                        "description": "无效的验证码",
                        "schema": {
                            "$ref": "#/definitions/backend_pkg_common_response.APIResponse"
                        }
                    }
                }
            }
        },
        "/auth/roles/list/{tenant_id}": {
            "get": {
                "security": [
                    {
                        "ApiKeyAuth": []
                    }
                ],
                "description": "获取指定租户下的角色列表",
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "角色"
                ],
                "summary": "获取角色列表",
                "parameters": [
                    {
                        "type": "string",
                        "description": "租户ID",
                        "name": "tenant_id",
                        "in": "path",
                        "required": true
                    },
                    {
                        "type": "integer",
                        "default": 1,
                        "description": "页码",
                        "name": "page",
                        "in": "query"
                    },
                    {
                        "type": "integer",
                        "default": 20,
                        "description": "每页大小",
                        "name": "page_size",
                        "in": "query"
                    },
                    {
                        "enum": [
                            "name",
                            "created_at",
                            "updated_at"
                        ],
                        "type": "string",
                        "description": "排序字段",
                        "name": "sort_by",
                        "in": "query"
                    },
                    {
                        "type": "boolean",
                        "default": false,
                        "description": "是否降序",
                        "name": "sort_desc",
                        "in": "query"
                    }
                ],
                "responses": {
                    "200": {
                        "description": "角色列表",
                        "schema": {
                            "allOf": [
                                {
                                    "$ref": "#/definitions/backend_pkg_common_response.APIResponse"
                                },
                                {
                                    "type": "object",
                                    "properties": {
                                        "data": {
                                            "$ref": "#/definitions/backend_internal_application_query_model.RoleListResult"
                                        }
                                    }
                                }
                            ]
                        }
                    },
                    "400": {
                        "description": "无效请求",
                        "schema": {
                            "$ref": "#/definitions/backend_pkg_common_response.APIResponse"
                        }
                    }
                }
            }
        },
        "/auth/roles/{tenant_id}": {
            "get": {
                "security": [
                    {
                        "ApiKeyAuth": []
                    }
                ],
                "description": "获取用户在指定租户下的角色列表",
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "角色"
                ],
                "summary": "获取用户角色",
                "parameters": [
                    {
                        "type": "string",
                        "description": "租户ID",
                        "name": "tenant_id",
                        "in": "path",
                        "required": true
                    }
                ],
                "responses": {
                    "200": {
                        "description": "用户角色列表",
                        "schema": {
                            "allOf": [
                                {
                                    "$ref": "#/definitions/backend_pkg_common_response.APIResponse"
                                },
                                {
                                    "type": "object",
                                    "properties": {
                                        "data": {
                                            "$ref": "#/definitions/backend_internal_application_query_model.UserRolesResult"
                                        }
                                    }
                                }
                            ]
                        }
                    },
                    "400": {
                        "description": "无效请求",
                        "schema": {
                            "$ref": "#/definitions/backend_pkg_common_response.APIResponse"
                        }
                    }
                }
            }
        },
        "/auth/select-tenant": {
            "post": {
                "security": [
                    {
                        "ApiKeyAuth": []
                    }
                ],
                "description": "选择一个租户并返回JWT令牌",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "认证"
                ],
                "summary": "用户登录 - 阶段二",
                "parameters": [
                    {
                        "description": "租户选择",
                        "name": "request",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/backend_internal_application_command_model.SelectTenantCommand"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "租户已选择，令牌已生成",
                        "schema": {
                            "allOf": [
                                {
                                    "$ref": "#/definitions/backend_pkg_common_response.APIResponse"
                                },
                                {
                                    "type": "object",
                                    "properties": {
                                        "data": {
                                            "$ref": "#/definitions/backend_internal_application_command_model.AuthResult"
                                        }
                                    }
                                }
                            ]
                        }
                    },
                    "400": {
                        "description": "无效请求",
                        "schema": {
                            "$ref": "#/definitions/backend_pkg_common_response.APIResponse"
                        }
                    },
                    "403": {
                        "description": "租户访问被拒绝",
                        "schema": {
                            "$ref": "#/definitions/backend_pkg_common_response.APIResponse"
                        }
                    }
                }
            }
        },
        "/auth/validate-token": {
            "post": {
                "description": "验证JWT令牌的有效性",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "认证"
                ],
                "summary": "验证令牌",
                "parameters": [
                    {
                        "description": "令牌验证请求",
                        "name": "request",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/backend_internal_application_query_model.ValidateTokenQuery"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "令牌验证结果",
                        "schema": {
                            "allOf": [
                                {
                                    "$ref": "#/definitions/backend_pkg_common_response.APIResponse"
                                },
                                {
                                    "type": "object",
                                    "properties": {
                                        "data": {
                                            "$ref": "#/definitions/backend_internal_application_query_model.TokenValidationResult"
                                        }
                                    }
                                }
                            ]
                        }
                    },
                    "400": {
                        "description": "无效请求",
                        "schema": {
                            "$ref": "#/definitions/backend_pkg_common_response.APIResponse"
                        }
                    }
                }
            }
        },
        "/demo/hello": {
            "get": {
                "description": "这是一个演示接口，用于展示基本的GET请求处理",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "系统管理"
                ],
                "summary": "演示问候接口",
                "parameters": [
                    {
                        "type": "string",
                        "description": "用户名称",
                        "name": "name",
                        "in": "query"
                    },
                    {
                        "maximum": 120,
                        "minimum": 1,
                        "type": "integer",
                        "description": "用户年龄",
                        "name": "age",
                        "in": "query"
                    },
                    {
                        "enum": [
                            "json",
                            "xml"
                        ],
                        "type": "string",
                        "default": "json",
                        "description": "返回格式",
                        "name": "format",
                        "in": "query"
                    }
                ],
                "responses": {
                    "200": {
                        "description": "请求成功",
                        "schema": {
                            "allOf": [
                                {
                                    "$ref": "#/definitions/backend_pkg_common_response.APIResponse"
                                },
                                {
                                    "type": "object",
                                    "properties": {
                                        "data": {
                                            "type": "object",
                                            "additionalProperties": true
                                        }
                                    }
                                }
                            ]
                        }
                    },
                    "400": {
                        "description": "请求参数错误",
                        "schema": {
                            "$ref": "#/definitions/backend_pkg_common_response.APIResponse"
                        }
                    },
                    "500": {
                        "description": "服务器内部错误",
                        "schema": {
                            "$ref": "#/definitions/backend_pkg_common_response.APIResponse"
                        }
                    }
                }
            }
        },
        "/demo/process": {
            "post": {
                "description": "这是一个演示接口，用于展示POST请求处理和数据验证",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "系统管理"
                ],
                "summary": "演示数据处理接口",
                "parameters": [
                    {
                        "description": "处理请求数据",
                        "name": "request",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/DemoRequest"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "处理成功",
                        "schema": {
                            "allOf": [
                                {
                                    "$ref": "#/definitions/backend_pkg_common_response.APIResponse"
                                },
                                {
                                    "type": "object",
                                    "properties": {
                                        "data": {
                                            "$ref": "#/definitions/DemoResponse"
                                        }
                                    }
                                }
                            ]
                        }
                    },
                    "201": {
                        "description": "创建成功",
                        "schema": {
                            "allOf": [
                                {
                                    "$ref": "#/definitions/backend_pkg_common_response.APIResponse"
                                },
                                {
                                    "type": "object",
                                    "properties": {
                                        "data": {
                                            "$ref": "#/definitions/DemoResponse"
                                        }
                                    }
                                }
                            ]
                        }
                    },
                    "400": {
                        "description": "请求参数错误",
                        "schema": {
                            "$ref": "#/definitions/backend_pkg_common_response.APIResponse"
                        }
                    },
                    "422": {
                        "description": "数据验证失败",
                        "schema": {
                            "$ref": "#/definitions/backend_pkg_common_response.APIResponse"
                        }
                    },
                    "500": {
                        "description": "服务器内部错误",
                        "schema": {
                            "$ref": "#/definitions/backend_pkg_common_response.APIResponse"
                        }
                    }
                }
            }
        },
        "/subscriptions": {
            "get": {
                "description": "分页获取订阅列表，支持过滤和排序",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "订阅管理"
                ],
                "summary": "获取订阅列表",
                "parameters": [
                    {
                        "type": "integer",
                        "default": 1,
                        "description": "页码",
                        "name": "page",
                        "in": "query"
                    },
                    {
                        "type": "integer",
                        "default": 10,
                        "description": "每页数量",
                        "name": "page_size",
                        "in": "query"
                    },
                    {
                        "type": "string",
                        "default": "created_at",
                        "description": "排序字段",
                        "name": "sort_by",
                        "in": "query"
                    },
                    {
                        "type": "boolean",
                        "default": true,
                        "description": "是否降序",
                        "name": "sort_desc",
                        "in": "query"
                    },
                    {
                        "type": "string",
                        "description": "租户ID",
                        "name": "tenant_id",
                        "in": "query"
                    },
                    {
                        "type": "string",
                        "description": "计划类型",
                        "name": "plan_type",
                        "in": "query"
                    },
                    {
                        "type": "string",
                        "description": "订阅状态",
                        "name": "status",
                        "in": "query"
                    },
                    {
                        "type": "string",
                        "description": "计费周期",
                        "name": "billing_cycle",
                        "in": "query"
                    }
                ],
                "responses": {
                    "200": {
                        "description": "获取成功",
                        "schema": {
                            "allOf": [
                                {
                                    "$ref": "#/definitions/backend_pkg_common_response.APIResponse"
                                },
                                {
                                    "type": "object",
                                    "properties": {
                                        "data": {
                                            "$ref": "#/definitions/backend_internal_application_dto.SubscriptionListDTO"
                                        }
                                    }
                                }
                            ]
                        }
                    },
                    "500": {
                        "description": "服务器内部错误",
                        "schema": {
                            "$ref": "#/definitions/backend_pkg_common_response.APIResponse"
                        }
                    }
                }
            },
            "post": {
                "description": "为租户创建新的订阅计划",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "订阅管理"
                ],
                "summary": "创建订阅",
                "parameters": [
                    {
                        "description": "创建订阅请求",
                        "name": "request",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/backend_internal_application_dto.CreateSubscriptionRequestDTO"
                        }
                    }
                ],
                "responses": {
                    "201": {
                        "description": "创建成功",
                        "schema": {
                            "allOf": [
                                {
                                    "$ref": "#/definitions/backend_pkg_common_response.APIResponse"
                                },
                                {
                                    "type": "object",
                                    "properties": {
                                        "data": {
                                            "$ref": "#/definitions/backend_internal_application_dto.TenantSubscriptionDTO"
                                        }
                                    }
                                }
                            ]
                        }
                    },
                    "400": {
                        "description": "请求参数错误",
                        "schema": {
                            "$ref": "#/definitions/backend_pkg_common_response.APIResponse"
                        }
                    },
                    "409": {
                        "description": "订阅已存在",
                        "schema": {
                            "$ref": "#/definitions/backend_pkg_common_response.APIResponse"
                        }
                    },
                    "500": {
                        "description": "服务器内部错误",
                        "schema": {
                            "$ref": "#/definitions/backend_pkg_common_response.APIResponse"
                        }
                    }
                }
            }
        },
        "/subscriptions/expired": {
            "get": {
                "description": "获取已过期的订阅列表",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "订阅管理"
                ],
                "summary": "获取已过期的订阅",
                "parameters": [
                    {
                        "type": "integer",
                        "default": 1,
                        "description": "页码",
                        "name": "page",
                        "in": "query"
                    },
                    {
                        "type": "integer",
                        "default": 10,
                        "description": "每页数量",
                        "name": "page_size",
                        "in": "query"
                    }
                ],
                "responses": {
                    "200": {
                        "description": "获取成功",
                        "schema": {
                            "allOf": [
                                {
                                    "$ref": "#/definitions/backend_pkg_common_response.APIResponse"
                                },
                                {
                                    "type": "object",
                                    "properties": {
                                        "data": {
                                            "$ref": "#/definitions/backend_internal_application_dto.SubscriptionListDTO"
                                        }
                                    }
                                }
                            ]
                        }
                    },
                    "500": {
                        "description": "服务器内部错误",
                        "schema": {
                            "$ref": "#/definitions/backend_pkg_common_response.APIResponse"
                        }
                    }
                }
            }
        },
        "/subscriptions/expiring": {
            "get": {
                "description": "获取指定天数内即将过期的订阅列表",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "订阅管理"
                ],
                "summary": "获取即将过期的订阅",
                "parameters": [
                    {
                        "type": "integer",
                        "default": 30,
                        "description": "天数",
                        "name": "days",
                        "in": "query"
                    },
                    {
                        "type": "integer",
                        "default": 1,
                        "description": "页码",
                        "name": "page",
                        "in": "query"
                    },
                    {
                        "type": "integer",
                        "default": 10,
                        "description": "每页数量",
                        "name": "page_size",
                        "in": "query"
                    }
                ],
                "responses": {
                    "200": {
                        "description": "获取成功",
                        "schema": {
                            "allOf": [
                                {
                                    "$ref": "#/definitions/backend_pkg_common_response.APIResponse"
                                },
                                {
                                    "type": "object",
                                    "properties": {
                                        "data": {
                                            "$ref": "#/definitions/backend_internal_application_dto.SubscriptionListDTO"
                                        }
                                    }
                                }
                            ]
                        }
                    },
                    "500": {
                        "description": "服务器内部错误",
                        "schema": {
                            "$ref": "#/definitions/backend_pkg_common_response.APIResponse"
                        }
                    }
                }
            }
        },
        "/subscriptions/statistics": {
            "get": {
                "description": "获取订阅相关的统计信息",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "订阅管理"
                ],
                "summary": "获取订阅统计",
                "parameters": [
                    {
                        "type": "string",
                        "description": "租户ID",
                        "name": "tenant_id",
                        "in": "query"
                    },
                    {
                        "type": "string",
                        "description": "开始日期 (YYYY-MM-DD)",
                        "name": "start_date",
                        "in": "query"
                    },
                    {
                        "type": "string",
                        "description": "结束日期 (YYYY-MM-DD)",
                        "name": "end_date",
                        "in": "query"
                    }
                ],
                "responses": {
                    "200": {
                        "description": "获取成功",
                        "schema": {
                            "allOf": [
                                {
                                    "$ref": "#/definitions/backend_pkg_common_response.APIResponse"
                                },
                                {
                                    "type": "object",
                                    "properties": {
                                        "data": {
                                            "$ref": "#/definitions/backend_internal_application_dto.SubscriptionStatisticsDTO"
                                        }
                                    }
                                }
                            ]
                        }
                    },
                    "500": {
                        "description": "服务器内部错误",
                        "schema": {
                            "$ref": "#/definitions/backend_pkg_common_response.APIResponse"
                        }
                    }
                }
            }
        },
        "/subscriptions/trial": {
            "get": {
                "description": "获取试用期订阅列表",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "订阅管理"
                ],
                "summary": "获取试用订阅",
                "parameters": [
                    {
                        "type": "integer",
                        "default": 1,
                        "description": "页码",
                        "name": "page",
                        "in": "query"
                    },
                    {
                        "type": "integer",
                        "default": 10,
                        "description": "每页数量",
                        "name": "page_size",
                        "in": "query"
                    }
                ],
                "responses": {
                    "200": {
                        "description": "获取成功",
                        "schema": {
                            "allOf": [
                                {
                                    "$ref": "#/definitions/backend_pkg_common_response.APIResponse"
                                },
                                {
                                    "type": "object",
                                    "properties": {
                                        "data": {
                                            "$ref": "#/definitions/backend_internal_application_dto.SubscriptionListDTO"
                                        }
                                    }
                                }
                            ]
                        }
                    },
                    "500": {
                        "description": "服务器内部错误",
                        "schema": {
                            "$ref": "#/definitions/backend_pkg_common_response.APIResponse"
                        }
                    }
                }
            }
        },
        "/subscriptions/{id}": {
            "get": {
                "description": "根据订阅ID获取订阅详细信息",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "订阅管理"
                ],
                "summary": "获取订阅详情",
                "parameters": [
                    {
                        "type": "string",
                        "description": "订阅ID",
                        "name": "id",
                        "in": "path",
                        "required": true
                    }
                ],
                "responses": {
                    "200": {
                        "description": "获取成功",
                        "schema": {
                            "allOf": [
                                {
                                    "$ref": "#/definitions/backend_pkg_common_response.APIResponse"
                                },
                                {
                                    "type": "object",
                                    "properties": {
                                        "data": {
                                            "$ref": "#/definitions/backend_internal_application_dto.TenantSubscriptionDTO"
                                        }
                                    }
                                }
                            ]
                        }
                    },
                    "404": {
                        "description": "订阅不存在",
                        "schema": {
                            "$ref": "#/definitions/backend_pkg_common_response.APIResponse"
                        }
                    },
                    "500": {
                        "description": "服务器内部错误",
                        "schema": {
                            "$ref": "#/definitions/backend_pkg_common_response.APIResponse"
                        }
                    }
                }
            }
        },
        "/subscriptions/{id}/cancel": {
            "put": {
                "description": "取消指定的订阅",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "订阅管理"
                ],
                "summary": "取消订阅",
                "parameters": [
                    {
                        "type": "string",
                        "description": "订阅ID",
                        "name": "id",
                        "in": "path",
                        "required": true
                    },
                    {
                        "description": "取消订阅请求",
                        "name": "request",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/backend_internal_application_dto.CancelSubscriptionRequestDTO"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "取消成功",
                        "schema": {
                            "allOf": [
                                {
                                    "$ref": "#/definitions/backend_pkg_common_response.APIResponse"
                                },
                                {
                                    "type": "object",
                                    "properties": {
                                        "data": {
                                            "$ref": "#/definitions/backend_internal_application_dto.TenantSubscriptionDTO"
                                        }
                                    }
                                }
                            ]
                        }
                    },
                    "400": {
                        "description": "请求参数错误",
                        "schema": {
                            "$ref": "#/definitions/backend_pkg_common_response.APIResponse"
                        }
                    },
                    "404": {
                        "description": "订阅不存在",
                        "schema": {
                            "$ref": "#/definitions/backend_pkg_common_response.APIResponse"
                        }
                    },
                    "500": {
                        "description": "服务器内部错误",
                        "schema": {
                            "$ref": "#/definitions/backend_pkg_common_response.APIResponse"
                        }
                    }
                }
            }
        },
        "/subscriptions/{id}/extend-trial": {
            "put": {
                "description": "延长指定订阅的试用期",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "订阅管理"
                ],
                "summary": "延长试用期",
                "parameters": [
                    {
                        "type": "string",
                        "description": "订阅ID",
                        "name": "id",
                        "in": "path",
                        "required": true
                    },
                    {
                        "description": "延长试用期请求",
                        "name": "request",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/backend_internal_application_dto.ExtendTrialRequestDTO"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "延长成功",
                        "schema": {
                            "allOf": [
                                {
                                    "$ref": "#/definitions/backend_pkg_common_response.APIResponse"
                                },
                                {
                                    "type": "object",
                                    "properties": {
                                        "data": {
                                            "$ref": "#/definitions/backend_internal_application_dto.TenantSubscriptionDTO"
                                        }
                                    }
                                }
                            ]
                        }
                    },
                    "400": {
                        "description": "请求参数错误",
                        "schema": {
                            "$ref": "#/definitions/backend_pkg_common_response.APIResponse"
                        }
                    },
                    "404": {
                        "description": "订阅不存在",
                        "schema": {
                            "$ref": "#/definitions/backend_pkg_common_response.APIResponse"
                        }
                    },
                    "500": {
                        "description": "服务器内部错误",
                        "schema": {
                            "$ref": "#/definitions/backend_pkg_common_response.APIResponse"
                        }
                    }
                }
            }
        },
        "/subscriptions/{id}/limits": {
            "put": {
                "description": "更新指定订阅的使用限制",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "订阅管理"
                ],
                "summary": "更新订阅限制",
                "parameters": [
                    {
                        "type": "string",
                        "description": "订阅ID",
                        "name": "id",
                        "in": "path",
                        "required": true
                    },
                    {
                        "description": "更新订阅限制请求",
                        "name": "request",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/backend_internal_application_dto.UpdateQuotaLimitsRequestDTO"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "更新成功",
                        "schema": {
                            "allOf": [
                                {
                                    "$ref": "#/definitions/backend_pkg_common_response.APIResponse"
                                },
                                {
                                    "type": "object",
                                    "properties": {
                                        "data": {
                                            "$ref": "#/definitions/backend_internal_application_dto.TenantSubscriptionDTO"
                                        }
                                    }
                                }
                            ]
                        }
                    },
                    "400": {
                        "description": "请求参数错误",
                        "schema": {
                            "$ref": "#/definitions/backend_pkg_common_response.APIResponse"
                        }
                    },
                    "404": {
                        "description": "订阅不存在",
                        "schema": {
                            "$ref": "#/definitions/backend_pkg_common_response.APIResponse"
                        }
                    },
                    "500": {
                        "description": "服务器内部错误",
                        "schema": {
                            "$ref": "#/definitions/backend_pkg_common_response.APIResponse"
                        }
                    }
                }
            }
        },
        "/subscriptions/{id}/renew": {
            "put": {
                "description": "为订阅续费",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "订阅管理"
                ],
                "summary": "续费订阅",
                "parameters": [
                    {
                        "type": "string",
                        "description": "订阅ID",
                        "name": "id",
                        "in": "path",
                        "required": true
                    },
                    {
                        "description": "续费订阅请求",
                        "name": "request",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/backend_internal_application_dto.RenewSubscriptionRequestDTO"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "续费成功",
                        "schema": {
                            "allOf": [
                                {
                                    "$ref": "#/definitions/backend_pkg_common_response.APIResponse"
                                },
                                {
                                    "type": "object",
                                    "properties": {
                                        "data": {
                                            "$ref": "#/definitions/backend_internal_application_dto.TenantSubscriptionDTO"
                                        }
                                    }
                                }
                            ]
                        }
                    },
                    "400": {
                        "description": "请求参数错误",
                        "schema": {
                            "$ref": "#/definitions/backend_pkg_common_response.APIResponse"
                        }
                    },
                    "404": {
                        "description": "订阅不存在",
                        "schema": {
                            "$ref": "#/definitions/backend_pkg_common_response.APIResponse"
                        }
                    },
                    "500": {
                        "description": "服务器内部错误",
                        "schema": {
                            "$ref": "#/definitions/backend_pkg_common_response.APIResponse"
                        }
                    }
                }
            }
        },
        "/tenants": {
            "get": {
                "description": "使用新分页框架获取租户列表，支持高级过滤和排序",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "租户管理"
                ],
                "summary": "获取租户列表（新分页）",
                "parameters": [
                    {
                        "type": "integer",
                        "default": 1,
                        "description": "页码",
                        "name": "page",
                        "in": "query"
                    },
                    {
                        "type": "integer",
                        "default": 10,
                        "description": "每页数量",
                        "name": "page_size",
                        "in": "query"
                    },
                    {
                        "type": "string",
                        "default": "created_at",
                        "description": "排序字段",
                        "name": "sort_by",
                        "in": "query"
                    },
                    {
                        "type": "boolean",
                        "default": true,
                        "description": "是否降序",
                        "name": "sort_desc",
                        "in": "query"
                    },
                    {
                        "type": "string",
                        "description": "搜索关键词",
                        "name": "search",
                        "in": "query"
                    }
                ],
                "responses": {
                    "200": {
                        "description": "获取成功",
                        "schema": {
                            "allOf": [
                                {
                                    "$ref": "#/definitions/backend_pkg_common_response.APIResponse"
                                },
                                {
                                    "type": "object",
                                    "properties": {
                                        "data": {
                                            "$ref": "#/definitions/backend_internal_application_pagination_dto.PaginationResponseDTO-backend_internal_application_dto_TenantDTO"
                                        }
                                    }
                                }
                            ]
                        }
                    },
                    "500": {
                        "description": "服务器内部错误",
                        "schema": {
                            "$ref": "#/definitions/backend_pkg_common_response.APIResponse"
                        }
                    }
                }
            },
            "post": {
                "description": "创建新的租户",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "租户管理"
                ],
                "summary": "创建租户",
                "parameters": [
                    {
                        "description": "创建租户请求",
                        "name": "request",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/backend_internal_application_dto.CreateTenantRequestDTO"
                        }
                    }
                ],
                "responses": {
                    "201": {
                        "description": "创建成功",
                        "schema": {
                            "allOf": [
                                {
                                    "$ref": "#/definitions/backend_pkg_common_response.APIResponse"
                                },
                                {
                                    "type": "object",
                                    "properties": {
                                        "data": {
                                            "$ref": "#/definitions/backend_internal_application_dto.TenantDTO"
                                        }
                                    }
                                }
                            ]
                        }
                    },
                    "400": {
                        "description": "请求参数错误",
                        "schema": {
                            "$ref": "#/definitions/backend_pkg_common_response.APIResponse"
                        }
                    },
                    "409": {
                        "description": "域名已存在",
                        "schema": {
                            "$ref": "#/definitions/backend_pkg_common_response.APIResponse"
                        }
                    },
                    "500": {
                        "description": "服务器内部错误",
                        "schema": {
                            "$ref": "#/definitions/backend_pkg_common_response.APIResponse"
                        }
                    }
                }
            }
        },
        "/tenants/batch-status": {
            "put": {
                "description": "批量更新多个租户的状态",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "租户管理"
                ],
                "summary": "批量更新租户状态",
                "parameters": [
                    {
                        "description": "批量更新状态请求",
                        "name": "request",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/backend_internal_application_dto.BatchUpdateStatusRequestDTO"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "更新成功",
                        "schema": {
                            "$ref": "#/definitions/backend_pkg_common_response.APIResponse"
                        }
                    },
                    "400": {
                        "description": "请求参数错误",
                        "schema": {
                            "$ref": "#/definitions/backend_pkg_common_response.APIResponse"
                        }
                    },
                    "500": {
                        "description": "服务器内部错误",
                        "schema": {
                            "$ref": "#/definitions/backend_pkg_common_response.APIResponse"
                        }
                    }
                }
            }
        },
        "/tenants/search": {
            "get": {
                "description": "使用新分页框架搜索租户，支持高级过滤条件",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "租户管理"
                ],
                "summary": "搜索租户（新分页）",
                "parameters": [
                    {
                        "type": "integer",
                        "default": 1,
                        "description": "页码",
                        "name": "page",
                        "in": "query"
                    },
                    {
                        "type": "integer",
                        "default": 10,
                        "description": "每页数量",
                        "name": "page_size",
                        "in": "query"
                    },
                    {
                        "type": "string",
                        "default": "created_at",
                        "description": "排序字段",
                        "name": "sort_by",
                        "in": "query"
                    },
                    {
                        "type": "boolean",
                        "default": true,
                        "description": "是否降序",
                        "name": "sort_desc",
                        "in": "query"
                    },
                    {
                        "type": "string",
                        "description": "搜索关键词",
                        "name": "search",
                        "in": "query"
                    },
                    {
                        "enum": [
                            "active",
                            "inactive",
                            "suspended",
                            "expired",
                            "terminated"
                        ],
                        "type": "string",
                        "description": "租户状态",
                        "name": "status",
                        "in": "query"
                    },
                    {
                        "enum": [
                            "system",
                            "enterprise",
                            "professional",
                            "basic",
                            "trial"
                        ],
                        "type": "string",
                        "description": "租户类型",
                        "name": "type",
                        "in": "query"
                    },
                    {
                        "type": "string",
                        "description": "行业",
                        "name": "industry",
                        "in": "query"
                    },
                    {
                        "type": "string",
                        "description": "国家",
                        "name": "country",
                        "in": "query"
                    },
                    {
                        "type": "string",
                        "description": "省份",
                        "name": "province",
                        "in": "query"
                    },
                    {
                        "type": "string",
                        "description": "城市",
                        "name": "city",
                        "in": "query"
                    },
                    {
                        "type": "string",
                        "format": "date-time",
                        "description": "创建时间起始",
                        "name": "created_from",
                        "in": "query"
                    },
                    {
                        "type": "string",
                        "format": "date-time",
                        "description": "创建时间结束",
                        "name": "created_to",
                        "in": "query"
                    }
                ],
                "responses": {
                    "200": {
                        "description": "搜索成功",
                        "schema": {
                            "allOf": [
                                {
                                    "$ref": "#/definitions/backend_pkg_common_response.APIResponse"
                                },
                                {
                                    "type": "object",
                                    "properties": {
                                        "data": {
                                            "$ref": "#/definitions/backend_internal_application_pagination_dto.PaginationResponseDTO-backend_internal_application_dto_TenantDTO"
                                        }
                                    }
                                }
                            ]
                        }
                    },
                    "400": {
                        "description": "请求参数错误",
                        "schema": {
                            "$ref": "#/definitions/backend_pkg_common_response.APIResponse"
                        }
                    },
                    "500": {
                        "description": "服务器内部错误",
                        "schema": {
                            "$ref": "#/definitions/backend_pkg_common_response.APIResponse"
                        }
                    }
                }
            }
        },
        "/tenants/validate-domain": {
            "post": {
                "description": "检查域名是否可以使用",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "租户管理"
                ],
                "summary": "验证域名可用性",
                "parameters": [
                    {
                        "description": "验证域名请求",
                        "name": "request",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/backend_internal_application_dto.ValidateDomainRequestDTO"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "验证成功",
                        "schema": {
                            "allOf": [
                                {
                                    "$ref": "#/definitions/backend_pkg_common_response.APIResponse"
                                },
                                {
                                    "type": "object",
                                    "properties": {
                                        "data": {
                                            "type": "object",
                                            "additionalProperties": {
                                                "type": "boolean"
                                            }
                                        }
                                    }
                                }
                            ]
                        }
                    },
                    "400": {
                        "description": "请求参数错误",
                        "schema": {
                            "$ref": "#/definitions/backend_pkg_common_response.APIResponse"
                        }
                    },
                    "500": {
                        "description": "服务器内部错误",
                        "schema": {
                            "$ref": "#/definitions/backend_pkg_common_response.APIResponse"
                        }
                    }
                }
            }
        },
        "/tenants/{id}": {
            "get": {
                "description": "根据租户ID获取租户详细信息",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "租户管理"
                ],
                "summary": "获取租户详情",
                "parameters": [
                    {
                        "type": "string",
                        "description": "租户ID",
                        "name": "id",
                        "in": "path",
                        "required": true
                    }
                ],
                "responses": {
                    "200": {
                        "description": "获取成功",
                        "schema": {
                            "allOf": [
                                {
                                    "$ref": "#/definitions/backend_pkg_common_response.APIResponse"
                                },
                                {
                                    "type": "object",
                                    "properties": {
                                        "data": {
                                            "$ref": "#/definitions/backend_internal_application_dto.TenantDTO"
                                        }
                                    }
                                }
                            ]
                        }
                    },
                    "404": {
                        "description": "租户不存在",
                        "schema": {
                            "$ref": "#/definitions/backend_pkg_common_response.APIResponse"
                        }
                    },
                    "500": {
                        "description": "服务器内部错误",
                        "schema": {
                            "$ref": "#/definitions/backend_pkg_common_response.APIResponse"
                        }
                    }
                }
            },
            "put": {
                "description": "更新租户的基本信息",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "租户管理"
                ],
                "summary": "更新租户信息",
                "parameters": [
                    {
                        "type": "string",
                        "description": "租户ID",
                        "name": "id",
                        "in": "path",
                        "required": true
                    },
                    {
                        "description": "更新租户请求",
                        "name": "request",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/backend_internal_application_dto.UpdateTenantRequestDTO"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "更新成功",
                        "schema": {
                            "allOf": [
                                {
                                    "$ref": "#/definitions/backend_pkg_common_response.APIResponse"
                                },
                                {
                                    "type": "object",
                                    "properties": {
                                        "data": {
                                            "$ref": "#/definitions/backend_internal_application_dto.TenantDTO"
                                        }
                                    }
                                }
                            ]
                        }
                    },
                    "400": {
                        "description": "请求参数错误",
                        "schema": {
                            "$ref": "#/definitions/backend_pkg_common_response.APIResponse"
                        }
                    },
                    "404": {
                        "description": "租户不存在",
                        "schema": {
                            "$ref": "#/definitions/backend_pkg_common_response.APIResponse"
                        }
                    },
                    "500": {
                        "description": "服务器内部错误",
                        "schema": {
                            "$ref": "#/definitions/backend_pkg_common_response.APIResponse"
                        }
                    }
                }
            },
            "delete": {
                "description": "软删除租户",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "租户管理"
                ],
                "summary": "删除租户",
                "parameters": [
                    {
                        "type": "string",
                        "description": "租户ID",
                        "name": "id",
                        "in": "path",
                        "required": true
                    }
                ],
                "responses": {
                    "200": {
                        "description": "删除成功",
                        "schema": {
                            "$ref": "#/definitions/backend_pkg_common_response.APIResponse"
                        }
                    },
                    "404": {
                        "description": "租户不存在",
                        "schema": {
                            "$ref": "#/definitions/backend_pkg_common_response.APIResponse"
                        }
                    },
                    "500": {
                        "description": "服务器内部错误",
                        "schema": {
                            "$ref": "#/definitions/backend_pkg_common_response.APIResponse"
                        }
                    }
                }
            }
        },
        "/tenants/{id}/activate": {
            "put": {
                "description": "激活指定的租户",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "租户管理"
                ],
                "summary": "激活租户",
                "parameters": [
                    {
                        "type": "string",
                        "description": "租户ID",
                        "name": "id",
                        "in": "path",
                        "required": true
                    }
                ],
                "responses": {
                    "200": {
                        "description": "激活成功",
                        "schema": {
                            "$ref": "#/definitions/backend_pkg_common_response.APIResponse"
                        }
                    },
                    "404": {
                        "description": "租户不存在",
                        "schema": {
                            "$ref": "#/definitions/backend_pkg_common_response.APIResponse"
                        }
                    },
                    "500": {
                        "description": "服务器内部错误",
                        "schema": {
                            "$ref": "#/definitions/backend_pkg_common_response.APIResponse"
                        }
                    }
                }
            }
        },
        "/tenants/{id}/health": {
            "get": {
                "description": "获取指定租户的健康状态检查结果",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "租户管理"
                ],
                "summary": "获取租户健康状态",
                "parameters": [
                    {
                        "type": "string",
                        "description": "租户ID",
                        "name": "id",
                        "in": "path",
                        "required": true
                    }
                ],
                "responses": {
                    "200": {
                        "description": "获取成功",
                        "schema": {
                            "allOf": [
                                {
                                    "$ref": "#/definitions/backend_pkg_common_response.APIResponse"
                                },
                                {
                                    "type": "object",
                                    "properties": {
                                        "data": {
                                            "$ref": "#/definitions/backend_internal_application_dto.TenantHealthDTO"
                                        }
                                    }
                                }
                            ]
                        }
                    },
                    "404": {
                        "description": "租户不存在",
                        "schema": {
                            "$ref": "#/definitions/backend_pkg_common_response.APIResponse"
                        }
                    },
                    "500": {
                        "description": "服务器内部错误",
                        "schema": {
                            "$ref": "#/definitions/backend_pkg_common_response.APIResponse"
                        }
                    }
                }
            }
        },
        "/tenants/{id}/quota": {
            "get": {
                "description": "获取指定租户的配额使用情况",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "租户管理"
                ],
                "summary": "获取租户配额信息",
                "parameters": [
                    {
                        "type": "string",
                        "description": "租户ID",
                        "name": "id",
                        "in": "path",
                        "required": true
                    }
                ],
                "responses": {
                    "200": {
                        "description": "获取成功",
                        "schema": {
                            "allOf": [
                                {
                                    "$ref": "#/definitions/backend_pkg_common_response.APIResponse"
                                },
                                {
                                    "type": "object",
                                    "properties": {
                                        "data": {
                                            "$ref": "#/definitions/backend_internal_application_dto.TenantQuotaDTO"
                                        }
                                    }
                                }
                            ]
                        }
                    },
                    "404": {
                        "description": "租户不存在",
                        "schema": {
                            "$ref": "#/definitions/backend_pkg_common_response.APIResponse"
                        }
                    },
                    "500": {
                        "description": "服务器内部错误",
                        "schema": {
                            "$ref": "#/definitions/backend_pkg_common_response.APIResponse"
                        }
                    }
                }
            },
            "put": {
                "description": "更新指定租户的配额限制",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "租户管理"
                ],
                "summary": "更新租户配额",
                "parameters": [
                    {
                        "type": "string",
                        "description": "租户ID",
                        "name": "id",
                        "in": "path",
                        "required": true
                    },
                    {
                        "description": "更新配额请求",
                        "name": "request",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/backend_internal_application_dto.UpdateQuotaLimitsRequestDTO"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "更新成功",
                        "schema": {
                            "allOf": [
                                {
                                    "$ref": "#/definitions/backend_pkg_common_response.APIResponse"
                                },
                                {
                                    "type": "object",
                                    "properties": {
                                        "data": {
                                            "$ref": "#/definitions/backend_internal_application_dto.TenantQuotaDTO"
                                        }
                                    }
                                }
                            ]
                        }
                    },
                    "400": {
                        "description": "请求参数错误",
                        "schema": {
                            "$ref": "#/definitions/backend_pkg_common_response.APIResponse"
                        }
                    },
                    "404": {
                        "description": "租户不存在",
                        "schema": {
                            "$ref": "#/definitions/backend_pkg_common_response.APIResponse"
                        }
                    },
                    "500": {
                        "description": "服务器内部错误",
                        "schema": {
                            "$ref": "#/definitions/backend_pkg_common_response.APIResponse"
                        }
                    }
                }
            }
        },
        "/tenants/{id}/suspend": {
            "put": {
                "description": "暂停指定的租户",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "租户管理"
                ],
                "summary": "暂停租户",
                "parameters": [
                    {
                        "type": "string",
                        "description": "租户ID",
                        "name": "id",
                        "in": "path",
                        "required": true
                    },
                    {
                        "description": "暂停租户请求",
                        "name": "request",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/backend_internal_application_dto.UpdateTenantStatusRequestDTO"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "暂停成功",
                        "schema": {
                            "$ref": "#/definitions/backend_pkg_common_response.APIResponse"
                        }
                    },
                    "400": {
                        "description": "请求参数错误",
                        "schema": {
                            "$ref": "#/definitions/backend_pkg_common_response.APIResponse"
                        }
                    },
                    "404": {
                        "description": "租户不存在",
                        "schema": {
                            "$ref": "#/definitions/backend_pkg_common_response.APIResponse"
                        }
                    },
                    "500": {
                        "description": "服务器内部错误",
                        "schema": {
                            "$ref": "#/definitions/backend_pkg_common_response.APIResponse"
                        }
                    }
                }
            }
        },
        "/tenants/{id}/terminate": {
            "put": {
                "description": "终止指定的租户",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "租户管理"
                ],
                "summary": "终止租户",
                "parameters": [
                    {
                        "type": "string",
                        "description": "租户ID",
                        "name": "id",
                        "in": "path",
                        "required": true
                    },
                    {
                        "description": "终止租户请求",
                        "name": "request",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/backend_internal_application_dto.UpdateTenantStatusRequestDTO"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "终止成功",
                        "schema": {
                            "$ref": "#/definitions/backend_pkg_common_response.APIResponse"
                        }
                    },
                    "400": {
                        "description": "请求参数错误",
                        "schema": {
                            "$ref": "#/definitions/backend_pkg_common_response.APIResponse"
                        }
                    },
                    "404": {
                        "description": "租户不存在",
                        "schema": {
                            "$ref": "#/definitions/backend_pkg_common_response.APIResponse"
                        }
                    },
                    "500": {
                        "description": "服务器内部错误",
                        "schema": {
                            "$ref": "#/definitions/backend_pkg_common_response.APIResponse"
                        }
                    }
                }
            }
        },
        "/tenants/{tenant_id}/subscription": {
            "get": {
                "description": "获取指定租户的当前订阅信息",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "订阅管理"
                ],
                "summary": "获取租户订阅",
                "parameters": [
                    {
                        "type": "string",
                        "description": "租户ID",
                        "name": "tenant_id",
                        "in": "path",
                        "required": true
                    }
                ],
                "responses": {
                    "200": {
                        "description": "获取成功",
                        "schema": {
                            "allOf": [
                                {
                                    "$ref": "#/definitions/backend_pkg_common_response.APIResponse"
                                },
                                {
                                    "type": "object",
                                    "properties": {
                                        "data": {
                                            "$ref": "#/definitions/backend_internal_application_dto.TenantSubscriptionDTO"
                                        }
                                    }
                                }
                            ]
                        }
                    },
                    "404": {
                        "description": "订阅不存在",
                        "schema": {
                            "$ref": "#/definitions/backend_pkg_common_response.APIResponse"
                        }
                    },
                    "500": {
                        "description": "服务器内部错误",
                        "schema": {
                            "$ref": "#/definitions/backend_pkg_common_response.APIResponse"
                        }
                    }
                }
            }
        },
        "/users": {
            "get": {
                "security": [
                    {
                        "ApiKeyAuth": []
                    }
                ],
                "description": "获取用户列表",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "用户管理"
                ],
                "summary": "获取用户列表",
                "parameters": [
                    {
                        "type": "string",
                        "description": "租户ID",
                        "name": "tenant_id",
                        "in": "query",
                        "required": true
                    },
                    {
                        "type": "integer",
                        "default": 1,
                        "description": "页码",
                        "name": "page",
                        "in": "query"
                    },
                    {
                        "type": "integer",
                        "default": 10,
                        "description": "每页数量",
                        "name": "page_size",
                        "in": "query"
                    },
                    {
                        "type": "string",
                        "description": "搜索关键词",
                        "name": "keyword",
                        "in": "query"
                    },
                    {
                        "type": "string",
                        "description": "用户状态筛选",
                        "name": "status",
                        "in": "query"
                    }
                ],
                "responses": {
                    "200": {
                        "description": "获取成功",
                        "schema": {
                            "allOf": [
                                {
                                    "$ref": "#/definitions/backend_pkg_common_response.APIResponse"
                                },
                                {
                                    "type": "object",
                                    "properties": {
                                        "data": {
                                            "$ref": "#/definitions/backend_internal_application_query_model.UserListResult"
                                        }
                                    }
                                }
                            ]
                        }
                    },
                    "400": {
                        "description": "请求参数错误",
                        "schema": {
                            "$ref": "#/definitions/backend_pkg_common_response.APIResponse"
                        }
                    },
                    "500": {
                        "description": "服务器内部错误",
                        "schema": {
                            "$ref": "#/definitions/backend_pkg_common_response.APIResponse"
                        }
                    }
                }
            }
        },
        "/users/register": {
            "post": {
                "description": "注册新用户账户",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "用户管理"
                ],
                "summary": "用户注册",
                "parameters": [
                    {
                        "description": "注册请求",
                        "name": "request",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/backend_internal_application_usecase_user.RegisterUserRequest"
                        }
                    }
                ],
                "responses": {
                    "201": {
                        "description": "注册成功",
                        "schema": {
                            "allOf": [
                                {
                                    "$ref": "#/definitions/backend_pkg_common_response.APIResponse"
                                },
                                {
                                    "type": "object",
                                    "properties": {
                                        "data": {
                                            "$ref": "#/definitions/backend_internal_application_usecase_user.RegisterUserResponse"
                                        }
                                    }
                                }
                            ]
                        }
                    },
                    "400": {
                        "description": "无效的请求体",
                        "schema": {
                            "$ref": "#/definitions/backend_pkg_common_response.APIResponse"
                        }
                    },
                    "409": {
                        "description": "用户已存在",
                        "schema": {
                            "$ref": "#/definitions/backend_pkg_common_response.APIResponse"
                        }
                    },
                    "500": {
                        "description": "服务器内部错误",
                        "schema": {
                            "$ref": "#/definitions/backend_pkg_common_response.APIResponse"
                        }
                    }
                }
            }
        },
        "/users/{user_id}/activate": {
            "post": {
                "security": [
                    {
                        "ApiKeyAuth": []
                    }
                ],
                "description": "激活指定的用户账户",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "用户管理"
                ],
                "summary": "激活用户",
                "parameters": [
                    {
                        "type": "string",
                        "description": "用户ID",
                        "name": "user_id",
                        "in": "path",
                        "required": true
                    }
                ],
                "responses": {
                    "200": {
                        "description": "激活成功",
                        "schema": {
                            "allOf": [
                                {
                                    "$ref": "#/definitions/backend_pkg_common_response.APIResponse"
                                },
                                {
                                    "type": "object",
                                    "properties": {
                                        "data": {
                                            "$ref": "#/definitions/backend_internal_application_command_model.ActivateUserResult"
                                        }
                                    }
                                }
                            ]
                        }
                    },
                    "400": {
                        "description": "无效的请求参数",
                        "schema": {
                            "$ref": "#/definitions/backend_pkg_common_response.APIResponse"
                        }
                    },
                    "404": {
                        "description": "用户不存在",
                        "schema": {
                            "$ref": "#/definitions/backend_pkg_common_response.APIResponse"
                        }
                    },
                    "500": {
                        "description": "服务器内部错误",
                        "schema": {
                            "$ref": "#/definitions/backend_pkg_common_response.APIResponse"
                        }
                    }
                }
            }
        },
        "/users/{user_id}/deactivate": {
            "post": {
                "security": [
                    {
                        "ApiKeyAuth": []
                    }
                ],
                "description": "禁用指定的用户账户",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "用户管理"
                ],
                "summary": "禁用用户",
                "parameters": [
                    {
                        "type": "string",
                        "description": "用户ID",
                        "name": "user_id",
                        "in": "path",
                        "required": true
                    },
                    {
                        "description": "禁用请求",
                        "name": "request",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/backend_internal_application_usecase_user.DeactivateUserRequest"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "禁用成功",
                        "schema": {
                            "allOf": [
                                {
                                    "$ref": "#/definitions/backend_pkg_common_response.APIResponse"
                                },
                                {
                                    "type": "object",
                                    "properties": {
                                        "data": {
                                            "$ref": "#/definitions/backend_internal_application_command_model.DeactivateUserResult"
                                        }
                                    }
                                }
                            ]
                        }
                    },
                    "400": {
                        "description": "无效的请求体",
                        "schema": {
                            "$ref": "#/definitions/backend_pkg_common_response.APIResponse"
                        }
                    },
                    "404": {
                        "description": "用户不存在",
                        "schema": {
                            "$ref": "#/definitions/backend_pkg_common_response.APIResponse"
                        }
                    },
                    "500": {
                        "description": "服务器内部错误",
                        "schema": {
                            "$ref": "#/definitions/backend_pkg_common_response.APIResponse"
                        }
                    }
                }
            }
        },
        "/users/{user_id}/profile": {
            "get": {
                "security": [
                    {
                        "ApiKeyAuth": []
                    }
                ],
                "description": "获取指定用户的详细资料信息",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "用户管理"
                ],
                "summary": "获取用户资料",
                "parameters": [
                    {
                        "type": "string",
                        "description": "用户ID",
                        "name": "user_id",
                        "in": "path",
                        "required": true
                    }
                ],
                "responses": {
                    "200": {
                        "description": "获取成功",
                        "schema": {
                            "allOf": [
                                {
                                    "$ref": "#/definitions/backend_pkg_common_response.APIResponse"
                                },
                                {
                                    "type": "object",
                                    "properties": {
                                        "data": {
                                            "$ref": "#/definitions/backend_internal_application_query_model.UserQueryResult"
                                        }
                                    }
                                }
                            ]
                        }
                    },
                    "400": {
                        "description": "无效的请求参数",
                        "schema": {
                            "$ref": "#/definitions/backend_pkg_common_response.APIResponse"
                        }
                    },
                    "404": {
                        "description": "用户不存在",
                        "schema": {
                            "$ref": "#/definitions/backend_pkg_common_response.APIResponse"
                        }
                    },
                    "500": {
                        "description": "服务器内部错误",
                        "schema": {
                            "$ref": "#/definitions/backend_pkg_common_response.APIResponse"
                        }
                    }
                }
            },
            "put": {
                "security": [
                    {
                        "ApiKeyAuth": []
                    }
                ],
                "description": "更新用户的个人资料信息",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "用户管理"
                ],
                "summary": "更新用户资料",
                "parameters": [
                    {
                        "type": "string",
                        "description": "用户ID",
                        "name": "user_id",
                        "in": "path",
                        "required": true
                    },
                    {
                        "description": "更新请求",
                        "name": "request",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/backend_internal_application_usecase_user.UpdateUserProfileRequest"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "更新成功",
                        "schema": {
                            "allOf": [
                                {
                                    "$ref": "#/definitions/backend_pkg_common_response.APIResponse"
                                },
                                {
                                    "type": "object",
                                    "properties": {
                                        "data": {
                                            "$ref": "#/definitions/backend_internal_application_command_model.UpdateUserResult"
                                        }
                                    }
                                }
                            ]
                        }
                    },
                    "400": {
                        "description": "无效的请求体",
                        "schema": {
                            "$ref": "#/definitions/backend_pkg_common_response.APIResponse"
                        }
                    },
                    "404": {
                        "description": "用户不存在",
                        "schema": {
                            "$ref": "#/definitions/backend_pkg_common_response.APIResponse"
                        }
                    },
                    "500": {
                        "description": "服务器内部错误",
                        "schema": {
                            "$ref": "#/definitions/backend_pkg_common_response.APIResponse"
                        }
                    }
                }
            }
        }
    },
    "definitions": {
        "DemoRequest": {
            "type": "object",
            "required": [
                "age",
                "email",
                "name",
                "type"
            ],
            "properties": {
                "age": {
                    "description": "年龄",
                    "type": "integer",
                    "maximum": 120,
                    "minimum": 1,
                    "example": 25
                },
                "email": {
                    "description": "邮箱地址",
                    "type": "string",
                    "example": "<EMAIL>"
                },
                "name": {
                    "description": "用户名",
                    "type": "string",
                    "maxLength": 50,
                    "minLength": 1,
                    "example": "张三"
                },
                "type": {
                    "description": "用户类型: admin=管理员, user=普通用户, guest=访客",
                    "type": "string",
                    "enum": [
                        "admin",
                        "user",
                        "guest"
                    ],
                    "example": "user"
                }
            }
        },
        "DemoResponse": {
            "type": "object",
            "properties": {
                "id": {
                    "description": "处理结果ID",
                    "type": "string",
                    "example": "demo_123456"
                },
                "processed_at": {
                    "description": "处理时间戳",
                    "type": "string",
                    "example": "2023-01-01T00:00:00Z"
                },
                "status": {
                    "description": "处理状态",
                    "type": "string",
                    "example": "success"
                },
                "user_info": {
                    "description": "用户信息",
                    "allOf": [
                        {
                            "$ref": "#/definitions/DemoUserInfo"
                        }
                    ]
                }
            }
        },
        "DemoUserInfo": {
            "type": "object",
            "properties": {
                "display_name": {
                    "description": "显示名称",
                    "type": "string",
                    "example": "张三"
                },
                "is_vip": {
                    "description": "是否为VIP用户",
                    "type": "boolean",
                    "example": false
                },
                "level": {
                    "description": "用户级别",
                    "type": "string",
                    "example": "standard"
                }
            }
        },
        "backend_internal_application_command_model.ActivateUserResult": {
            "type": "object",
            "properties": {
                "activated_at": {
                    "type": "string"
                },
                "status": {
                    "type": "string"
                },
                "user_id": {
                    "type": "string"
                }
            }
        },
        "backend_internal_application_command_model.AuthResult": {
            "type": "object",
            "properties": {
                "access_token": {
                    "type": "string"
                },
                "expires_at": {
                    "type": "string"
                },
                "expires_in": {
                    "type": "integer"
                },
                "permissions": {
                    "type": "array",
                    "items": {
                        "type": "string"
                    }
                },
                "refresh_token": {
                    "type": "string"
                },
                "roles": {
                    "type": "array",
                    "items": {
                        "type": "string"
                    }
                },
                "tenant_id": {
                    "type": "string"
                },
                "token_type": {
                    "type": "string"
                },
                "user_id": {
                    "type": "string"
                }
            }
        },
        "backend_internal_application_command_model.ChangePasswordCommand": {
            "type": "object",
            "required": [
                "new_password",
                "old_password",
                "user_id"
            ],
            "properties": {
                "new_password": {
                    "type": "string",
                    "maxLength": 255,
                    "minLength": 8
                },
                "old_password": {
                    "type": "string",
                    "maxLength": 255,
                    "minLength": 6
                },
                "user_id": {
                    "type": "string",
                    "maxLength": 36
                }
            }
        },
        "backend_internal_application_command_model.DeactivateUserResult": {
            "type": "object",
            "properties": {
                "deactivated_at": {
                    "type": "string"
                },
                "status": {
                    "type": "string"
                },
                "user_id": {
                    "type": "string"
                }
            }
        },
        "backend_internal_application_command_model.LoginCommand": {
            "type": "object",
            "required": [
                "credential",
                "identifier",
                "identity_type"
            ],
            "properties": {
                "credential": {
                    "type": "string",
                    "maxLength": 255,
                    "minLength": 6
                },
                "device_id": {
                    "type": "string",
                    "maxLength": 64
                },
                "device_info": {
                    "type": "string",
                    "maxLength": 500
                },
                "identifier": {
                    "type": "string",
                    "maxLength": 255,
                    "minLength": 1
                },
                "identity_type": {
                    "type": "string",
                    "enum": [
                        "username",
                        "email",
                        "phone"
                    ]
                },
                "remember_me": {
                    "type": "boolean"
                }
            }
        },
        "backend_internal_application_command_model.LoginResult": {
            "type": "object",
            "properties": {
                "avatar": {
                    "type": "string"
                },
                "display_name": {
                    "type": "string"
                },
                "email": {
                    "type": "string"
                },
                "login_time": {
                    "type": "string"
                },
                "phone": {
                    "type": "string"
                },
                "requires_mfa": {
                    "type": "boolean"
                },
                "tenants": {
                    "type": "array",
                    "items": {
                        "$ref": "#/definitions/backend_internal_application_command_model.Tenant"
                    }
                },
                "user_id": {
                    "type": "string"
                },
                "username": {
                    "type": "string"
                }
            }
        },
        "backend_internal_application_command_model.LogoutCommand": {
            "type": "object",
            "required": [
                "logout_type",
                "tenant_id",
                "user_id"
            ],
            "properties": {
                "device_id": {
                    "type": "string",
                    "maxLength": 64
                },
                "logout_type": {
                    "type": "string",
                    "enum": [
                        "current",
                        "all"
                    ]
                },
                "tenant_id": {
                    "type": "string",
                    "maxLength": 36
                },
                "user_id": {
                    "type": "string",
                    "maxLength": 36
                }
            }
        },
        "backend_internal_application_command_model.OperationResult": {
            "type": "object",
            "properties": {
                "data": {
                    "type": "object",
                    "additionalProperties": true
                },
                "message": {
                    "type": "string"
                },
                "success": {
                    "type": "boolean"
                },
                "timestamp": {
                    "type": "string"
                }
            }
        },
        "backend_internal_application_command_model.RefreshTokenCommand": {
            "type": "object",
            "required": [
                "refresh_token"
            ],
            "properties": {
                "device_id": {
                    "type": "string",
                    "maxLength": 64
                },
                "refresh_token": {
                    "type": "string"
                }
            }
        },
        "backend_internal_application_command_model.ResetPasswordCommand": {
            "type": "object",
            "required": [
                "identifier",
                "identity_type",
                "new_password",
                "verify_code"
            ],
            "properties": {
                "identifier": {
                    "type": "string",
                    "maxLength": 255,
                    "minLength": 1
                },
                "identity_type": {
                    "type": "string",
                    "enum": [
                        "email",
                        "phone"
                    ]
                },
                "new_password": {
                    "type": "string",
                    "maxLength": 255,
                    "minLength": 8
                },
                "verify_code": {
                    "type": "string"
                }
            }
        },
        "backend_internal_application_command_model.SelectTenantCommand": {
            "type": "object",
            "required": [
                "tenant_id",
                "user_id"
            ],
            "properties": {
                "device_id": {
                    "type": "string",
                    "maxLength": 64
                },
                "tenant_id": {
                    "type": "string",
                    "maxLength": 36
                },
                "user_id": {
                    "type": "string",
                    "maxLength": 36
                }
            }
        },
        "backend_internal_application_command_model.Tenant": {
            "type": "object",
            "properties": {
                "domain": {
                    "type": "string"
                },
                "permissions": {
                    "type": "array",
                    "items": {
                        "type": "string"
                    }
                },
                "role": {
                    "type": "string"
                },
                "tenant_id": {
                    "type": "string"
                },
                "tenant_name": {
                    "type": "string"
                }
            }
        },
        "backend_internal_application_command_model.UpdateUserResult": {
            "type": "object",
            "properties": {
                "email": {
                    "type": "string"
                },
                "status": {
                    "type": "string"
                },
                "updated_at": {
                    "type": "string"
                },
                "user_id": {
                    "type": "string"
                },
                "username": {
                    "type": "string"
                },
                "version": {
                    "type": "integer"
                }
            }
        },
        "backend_internal_application_dto.BatchUpdateStatusRequestDTO": {
            "type": "object",
            "required": [
                "status",
                "tenant_ids"
            ],
            "properties": {
                "reason": {
                    "type": "string",
                    "maxLength": 500
                },
                "status": {
                    "type": "string",
                    "enum": [
                        "active",
                        "suspended",
                        "terminated"
                    ]
                },
                "tenant_ids": {
                    "type": "array",
                    "minItems": 1,
                    "items": {
                        "type": "string"
                    }
                }
            }
        },
        "backend_internal_application_dto.CancelSubscriptionRequestDTO": {
            "type": "object",
            "required": [
                "reason"
            ],
            "properties": {
                "reason": {
                    "type": "string",
                    "maxLength": 500
                },
                "refund_amount": {
                    "type": "number",
                    "minimum": 0
                },
                "refund_reason": {
                    "type": "string",
                    "maxLength": 500
                }
            }
        },
        "backend_internal_application_dto.CreateSubscriptionRequestDTO": {
            "type": "object",
            "required": [
                "billing_cycle",
                "currency",
                "end_date",
                "plan_id",
                "plan_name",
                "price",
                "start_date",
                "tenant_id"
            ],
            "properties": {
                "api_limit": {
                    "type": "integer",
                    "minimum": 1
                },
                "billing_cycle": {
                    "type": "string",
                    "enum": [
                        "monthly",
                        "yearly",
                        "lifetime"
                    ]
                },
                "currency": {
                    "type": "string"
                },
                "end_date": {
                    "type": "string"
                },
                "order_limit": {
                    "type": "integer",
                    "minimum": 1
                },
                "plan_id": {
                    "type": "string"
                },
                "plan_name": {
                    "type": "string"
                },
                "price": {
                    "type": "number"
                },
                "product_limit": {
                    "type": "integer",
                    "minimum": 1
                },
                "start_date": {
                    "type": "string"
                },
                "storage_limit": {
                    "type": "integer",
                    "minimum": 1
                },
                "tenant_id": {
                    "type": "string"
                },
                "user_limit": {
                    "type": "integer",
                    "minimum": 1
                }
            }
        },
        "backend_internal_application_dto.CreateTenantRequestDTO": {
            "type": "object",
            "required": [
                "domain",
                "name",
                "type"
            ],
            "properties": {
                "address": {
                    "type": "string",
                    "maxLength": 255
                },
                "city": {
                    "type": "string",
                    "maxLength": 50
                },
                "contact_email": {
                    "type": "string"
                },
                "contact_phone": {
                    "type": "string"
                },
                "country": {
                    "type": "string",
                    "maxLength": 50
                },
                "description": {
                    "type": "string",
                    "maxLength": 500
                },
                "display_name": {
                    "type": "string",
                    "maxLength": 100
                },
                "domain": {
                    "type": "string",
                    "maxLength": 100,
                    "minLength": 3
                },
                "industry": {
                    "type": "string",
                    "maxLength": 100
                },
                "name": {
                    "type": "string",
                    "maxLength": 100,
                    "minLength": 2
                },
                "province": {
                    "type": "string",
                    "maxLength": 50
                },
                "type": {
                    "type": "string",
                    "enum": [
                        "system",
                        "enterprise",
                        "professional",
                        "basic",
                        "trial"
                    ]
                }
            }
        },
        "backend_internal_application_dto.ExtendTrialRequestDTO": {
            "type": "object",
            "required": [
                "days",
                "reason"
            ],
            "properties": {
                "days": {
                    "type": "integer",
                    "maximum": 90,
                    "minimum": 1
                },
                "reason": {
                    "type": "string",
                    "maxLength": 500
                }
            }
        },
        "backend_internal_application_dto.HealthAlertDTO": {
            "type": "object",
            "properties": {
                "created_at": {
                    "type": "string"
                },
                "level": {
                    "description": "info, warning, critical",
                    "type": "string"
                },
                "message": {
                    "type": "string"
                },
                "severity": {
                    "description": "low, medium, high",
                    "type": "string"
                },
                "type": {
                    "description": "tenant_status, subscription_expired, quota_exceeded",
                    "type": "string"
                }
            }
        },
        "backend_internal_application_dto.QuotaAlertDTO": {
            "type": "object",
            "properties": {
                "created_at": {
                    "type": "string"
                },
                "current": {
                    "description": "当前使用率",
                    "type": "number"
                },
                "level": {
                    "description": "warning, critical",
                    "type": "string"
                },
                "message": {
                    "type": "string"
                },
                "threshold": {
                    "description": "告警阈值",
                    "type": "number"
                },
                "type": {
                    "description": "user, storage, api, product, order",
                    "type": "string"
                }
            }
        },
        "backend_internal_application_dto.RenewSubscriptionRequestDTO": {
            "type": "object",
            "required": [
                "billing_cycle",
                "currency",
                "end_date",
                "price",
                "start_date"
            ],
            "properties": {
                "billing_cycle": {
                    "type": "string",
                    "enum": [
                        "monthly",
                        "yearly",
                        "lifetime"
                    ]
                },
                "currency": {
                    "type": "string"
                },
                "end_date": {
                    "type": "string"
                },
                "price": {
                    "type": "number"
                },
                "start_date": {
                    "type": "string"
                }
            }
        },
        "backend_internal_application_dto.SubscriptionListDTO": {
            "type": "object",
            "properties": {
                "has_next": {
                    "type": "boolean"
                },
                "has_previous": {
                    "type": "boolean"
                },
                "items": {
                    "type": "array",
                    "items": {
                        "$ref": "#/definitions/backend_internal_application_dto.TenantSubscriptionDTO"
                    }
                },
                "page": {
                    "type": "integer"
                },
                "page_size": {
                    "type": "integer"
                },
                "total": {
                    "type": "integer"
                },
                "total_pages": {
                    "type": "integer"
                }
            }
        },
        "backend_internal_application_dto.SubscriptionStatisticsDTO": {
            "type": "object",
            "properties": {
                "active_subscriptions": {
                    "type": "integer"
                },
                "arr": {
                    "description": "年经常性收入",
                    "type": "number"
                },
                "average_revenue": {
                    "description": "平均收入",
                    "type": "number"
                },
                "billing_cycle_distribution": {
                    "type": "object",
                    "additionalProperties": {
                        "type": "integer"
                    }
                },
                "churn_rate": {
                    "description": "关键指标",
                    "type": "number"
                },
                "currency_distribution": {
                    "type": "object",
                    "additionalProperties": {
                        "type": "integer"
                    }
                },
                "expired_subscriptions": {
                    "type": "integer"
                },
                "generated_at": {
                    "description": "生成时间",
                    "type": "string"
                },
                "mrr": {
                    "description": "月经常性收入",
                    "type": "number"
                },
                "plan_distribution": {
                    "type": "object",
                    "additionalProperties": {
                        "type": "integer"
                    }
                },
                "renewal_rate": {
                    "description": "续费率",
                    "type": "number"
                },
                "revenue_by_currency": {
                    "type": "object",
                    "additionalProperties": {
                        "type": "number"
                    }
                },
                "revenue_by_plan": {
                    "type": "object",
                    "additionalProperties": {
                        "type": "number"
                    }
                },
                "status_distribution": {
                    "description": "状态分布",
                    "type": "object",
                    "additionalProperties": {
                        "type": "integer"
                    }
                },
                "total_revenue": {
                    "description": "收入统计",
                    "type": "number"
                },
                "total_subscriptions": {
                    "type": "integer"
                },
                "trial_subscriptions": {
                    "type": "integer"
                }
            }
        },
        "backend_internal_application_dto.TenantDTO": {
            "type": "object",
            "properties": {
                "address": {
                    "type": "string"
                },
                "city": {
                    "type": "string"
                },
                "contact_email": {
                    "type": "string"
                },
                "contact_phone": {
                    "type": "string"
                },
                "country": {
                    "type": "string"
                },
                "created_at": {
                    "description": "审计信息",
                    "type": "string"
                },
                "description": {
                    "type": "string"
                },
                "display_name": {
                    "type": "string"
                },
                "domain": {
                    "type": "string"
                },
                "expiry_date": {
                    "type": "string"
                },
                "id": {
                    "type": "string"
                },
                "industry": {
                    "type": "string"
                },
                "max_api_quota": {
                    "type": "integer"
                },
                "max_storage": {
                    "type": "integer"
                },
                "max_users": {
                    "description": "配额信息",
                    "type": "integer"
                },
                "name": {
                    "type": "string"
                },
                "province": {
                    "type": "string"
                },
                "settings": {
                    "type": "object",
                    "additionalProperties": true
                },
                "status": {
                    "type": "string"
                },
                "status_display": {
                    "type": "string"
                },
                "subscription_plan": {
                    "description": "订阅信息",
                    "type": "string"
                },
                "type": {
                    "type": "string"
                },
                "updated_at": {
                    "type": "string"
                },
                "version": {
                    "type": "integer"
                }
            }
        },
        "backend_internal_application_dto.TenantHealthDTO": {
            "type": "object",
            "properties": {
                "alerts": {
                    "type": "array",
                    "items": {
                        "$ref": "#/definitions/backend_internal_application_dto.HealthAlertDTO"
                    }
                },
                "checked_at": {
                    "type": "string"
                },
                "domain": {
                    "type": "string"
                },
                "health_score": {
                    "description": "0-100分",
                    "type": "integer"
                },
                "is_healthy": {
                    "type": "boolean"
                },
                "last_healthy_at": {
                    "type": "string"
                },
                "tenant_id": {
                    "type": "string"
                },
                "tenant_name": {
                    "type": "string"
                }
            }
        },
        "backend_internal_application_dto.TenantQuotaDTO": {
            "type": "object",
            "properties": {
                "alerts": {
                    "type": "array",
                    "items": {
                        "$ref": "#/definitions/backend_internal_application_dto.QuotaAlertDTO"
                    }
                },
                "api_limit_month": {
                    "type": "integer"
                },
                "api_reset_date": {
                    "type": "string"
                },
                "api_used_month": {
                    "description": "API配额",
                    "type": "integer"
                },
                "api_utilization": {
                    "type": "number"
                },
                "email_limit_month": {
                    "type": "integer"
                },
                "email_used_month": {
                    "type": "integer"
                },
                "file_upload_limit": {
                    "type": "integer"
                },
                "file_upload_used": {
                    "description": "其他配额",
                    "type": "integer"
                },
                "is_over_quota": {
                    "description": "状态信息",
                    "type": "boolean"
                },
                "last_updated": {
                    "type": "string"
                },
                "order_limit_month": {
                    "type": "integer"
                },
                "order_used_month": {
                    "description": "订单配额",
                    "type": "integer"
                },
                "order_utilization": {
                    "type": "number"
                },
                "product_limit": {
                    "type": "integer"
                },
                "product_used": {
                    "description": "商品配额",
                    "type": "integer"
                },
                "product_utilization": {
                    "type": "number"
                },
                "storage_limit": {
                    "type": "integer"
                },
                "storage_used": {
                    "description": "存储配额",
                    "type": "integer"
                },
                "storage_utilization": {
                    "type": "number"
                },
                "tenant_id": {
                    "type": "string"
                },
                "user_limit": {
                    "type": "integer"
                },
                "user_used": {
                    "description": "用户配额",
                    "type": "integer"
                },
                "user_utilization": {
                    "type": "number"
                }
            }
        },
        "backend_internal_application_dto.TenantSubscriptionDTO": {
            "type": "object",
            "properties": {
                "api_limit": {
                    "type": "integer"
                },
                "auto_renewal": {
                    "type": "boolean"
                },
                "billing_cycle": {
                    "type": "string"
                },
                "can_extend_trial": {
                    "type": "boolean"
                },
                "cancel_reason": {
                    "type": "string"
                },
                "canceled_at": {
                    "description": "取消信息",
                    "type": "string"
                },
                "canceled_by_user": {
                    "type": "string"
                },
                "created_at": {
                    "description": "审计信息",
                    "type": "string"
                },
                "currency": {
                    "type": "string"
                },
                "days_until_expiry": {
                    "description": "到期信息",
                    "type": "integer"
                },
                "end_date": {
                    "type": "string"
                },
                "features": {
                    "description": "功能权限",
                    "type": "array",
                    "items": {
                        "type": "string"
                    }
                },
                "formatted_price": {
                    "type": "string"
                },
                "id": {
                    "type": "string"
                },
                "is_expired": {
                    "type": "boolean"
                },
                "is_expiring_soon": {
                    "type": "boolean"
                },
                "is_trial_period": {
                    "description": "试用信息",
                    "type": "boolean"
                },
                "order_limit": {
                    "type": "integer"
                },
                "plan_id": {
                    "type": "string"
                },
                "plan_name": {
                    "type": "string"
                },
                "price": {
                    "description": "计费信息",
                    "type": "number"
                },
                "product_limit": {
                    "type": "integer"
                },
                "renewal_date": {
                    "type": "string"
                },
                "start_date": {
                    "description": "订阅周期",
                    "type": "string"
                },
                "status": {
                    "type": "string"
                },
                "status_display": {
                    "type": "string"
                },
                "storage_limit": {
                    "type": "integer"
                },
                "tenant_id": {
                    "type": "string"
                },
                "trial_end_date": {
                    "type": "string"
                },
                "trial_extensions": {
                    "type": "integer"
                },
                "updated_at": {
                    "type": "string"
                },
                "user_limit": {
                    "description": "使用限制",
                    "type": "integer"
                }
            }
        },
        "backend_internal_application_dto.UpdateQuotaLimitsRequestDTO": {
            "type": "object",
            "properties": {
                "api_limit": {
                    "type": "integer",
                    "minimum": 1
                },
                "email_limit": {
                    "type": "integer",
                    "minimum": 1
                },
                "file_upload_limit": {
                    "type": "integer",
                    "minimum": 1
                },
                "order_limit": {
                    "type": "integer",
                    "minimum": 1
                },
                "product_limit": {
                    "type": "integer",
                    "minimum": 1
                },
                "storage_limit": {
                    "type": "integer",
                    "minimum": 1
                },
                "user_limit": {
                    "type": "integer",
                    "minimum": 1
                }
            }
        },
        "backend_internal_application_dto.UpdateTenantRequestDTO": {
            "type": "object",
            "properties": {
                "address": {
                    "type": "string",
                    "maxLength": 255
                },
                "city": {
                    "type": "string",
                    "maxLength": 50
                },
                "contact_email": {
                    "type": "string"
                },
                "contact_phone": {
                    "type": "string"
                },
                "country": {
                    "type": "string",
                    "maxLength": 50
                },
                "description": {
                    "type": "string",
                    "maxLength": 500
                },
                "display_name": {
                    "type": "string",
                    "maxLength": 100
                },
                "industry": {
                    "type": "string",
                    "maxLength": 100
                },
                "name": {
                    "type": "string",
                    "maxLength": 100,
                    "minLength": 2
                },
                "province": {
                    "type": "string",
                    "maxLength": 50
                }
            }
        },
        "backend_internal_application_dto.UpdateTenantStatusRequestDTO": {
            "type": "object",
            "required": [
                "status"
            ],
            "properties": {
                "reason": {
                    "type": "string",
                    "maxLength": 500
                },
                "status": {
                    "type": "string",
                    "enum": [
                        "active",
                        "suspended",
                        "terminated"
                    ]
                }
            }
        },
        "backend_internal_application_dto.ValidateDomainRequestDTO": {
            "type": "object",
            "required": [
                "domain"
            ],
            "properties": {
                "domain": {
                    "type": "string",
                    "maxLength": 100,
                    "minLength": 3
                }
            }
        },
        "backend_internal_application_pagination_dto.PaginationLinksDTO": {
            "type": "object",
            "properties": {
                "first": {
                    "type": "string",
                    "example": "/api/users?page=1\u0026page_size=10"
                },
                "last": {
                    "type": "string",
                    "example": "/api/users?page=10\u0026page_size=10"
                },
                "next": {
                    "type": "string",
                    "example": "/api/users?page=2\u0026page_size=10"
                },
                "previous": {
                    "type": "string",
                    "example": "/api/users?page=0\u0026page_size=10"
                },
                "self": {
                    "type": "string",
                    "example": "/api/users?page=1\u0026page_size=10"
                }
            }
        },
        "backend_internal_application_pagination_dto.PaginationMetaDTO": {
            "type": "object",
            "properties": {
                "count": {
                    "type": "integer",
                    "example": 10
                },
                "has_next": {
                    "type": "boolean",
                    "example": true
                },
                "has_previous": {
                    "type": "boolean",
                    "example": false
                },
                "page": {
                    "type": "integer",
                    "example": 1
                },
                "page_size": {
                    "type": "integer",
                    "example": 10
                },
                "total": {
                    "type": "integer",
                    "example": 100
                },
                "total_pages": {
                    "type": "integer",
                    "example": 10
                }
            }
        },
        "backend_internal_application_pagination_dto.PaginationResponseDTO-backend_internal_application_dto_TenantDTO": {
            "type": "object",
            "properties": {
                "items": {
                    "type": "array",
                    "items": {
                        "$ref": "#/definitions/backend_internal_application_dto.TenantDTO"
                    }
                },
                "links": {
                    "$ref": "#/definitions/backend_internal_application_pagination_dto.PaginationLinksDTO"
                },
                "pagination": {
                    "$ref": "#/definitions/backend_internal_application_pagination_dto.PaginationMetaDTO"
                },
                "performance": {
                    "$ref": "#/definitions/backend_internal_application_pagination_dto.PerformanceDTO"
                }
            }
        },
        "backend_internal_application_pagination_dto.PerformanceDTO": {
            "type": "object",
            "properties": {
                "from_cache": {
                    "type": "boolean",
                    "example": false
                },
                "query_time": {
                    "type": "string",
                    "example": "15ms"
                },
                "total_time": {
                    "type": "string",
                    "example": "25ms"
                }
            }
        },
        "backend_internal_application_query_model.CheckPermissionQuery": {
            "type": "object",
            "required": [
                "action",
                "resource",
                "tenant_id",
                "user_id"
            ],
            "properties": {
                "action": {
                    "type": "string",
                    "maxLength": 50
                },
                "resource": {
                    "type": "string",
                    "maxLength": 100
                },
                "tenant_id": {
                    "type": "string",
                    "maxLength": 36
                },
                "user_id": {
                    "type": "string",
                    "maxLength": 36
                }
            }
        },
        "backend_internal_application_query_model.PermissionCheckResult": {
            "type": "object",
            "properties": {
                "action": {
                    "type": "string"
                },
                "allowed": {
                    "type": "boolean"
                },
                "reason": {
                    "type": "string"
                },
                "resource": {
                    "type": "string"
                },
                "roles": {
                    "type": "array",
                    "items": {
                        "type": "string"
                    }
                },
                "tenant_id": {
                    "type": "string"
                },
                "user_id": {
                    "type": "string"
                }
            }
        },
        "backend_internal_application_query_model.Role": {
            "type": "object",
            "properties": {
                "created_at": {
                    "type": "string"
                },
                "description": {
                    "type": "string"
                },
                "permissions": {
                    "type": "array",
                    "items": {
                        "type": "string"
                    }
                },
                "role_id": {
                    "type": "string"
                },
                "role_name": {
                    "type": "string"
                }
            }
        },
        "backend_internal_application_query_model.RoleListResult": {
            "type": "object",
            "properties": {
                "page": {
                    "type": "integer"
                },
                "page_size": {
                    "type": "integer"
                },
                "roles": {
                    "type": "array",
                    "items": {
                        "$ref": "#/definitions/backend_internal_application_query_model.Role"
                    }
                },
                "total": {
                    "type": "integer"
                },
                "total_pages": {
                    "type": "integer"
                }
            }
        },
        "backend_internal_application_query_model.TokenValidationResult": {
            "type": "object",
            "properties": {
                "error": {
                    "type": "string"
                },
                "expires_at": {
                    "type": "string"
                },
                "permissions": {
                    "type": "array",
                    "items": {
                        "type": "string"
                    }
                },
                "roles": {
                    "type": "array",
                    "items": {
                        "type": "string"
                    }
                },
                "tenant_id": {
                    "type": "string"
                },
                "user_id": {
                    "type": "string"
                },
                "valid": {
                    "type": "boolean"
                }
            }
        },
        "backend_internal_application_query_model.UserAuthResult": {
            "type": "object",
            "properties": {
                "avatar": {
                    "type": "string"
                },
                "created_at": {
                    "type": "string"
                },
                "display_name": {
                    "type": "string"
                },
                "email": {
                    "type": "string"
                },
                "last_login_at": {
                    "type": "string"
                },
                "phone": {
                    "type": "string"
                },
                "status": {
                    "type": "string"
                },
                "updated_at": {
                    "type": "string"
                },
                "user_id": {
                    "type": "string"
                },
                "username": {
                    "type": "string"
                }
            }
        },
        "backend_internal_application_query_model.UserListResult": {
            "type": "object",
            "properties": {
                "page": {
                    "type": "integer"
                },
                "page_size": {
                    "type": "integer"
                },
                "total": {
                    "type": "integer"
                },
                "total_pages": {
                    "type": "integer"
                },
                "users": {
                    "type": "array",
                    "items": {
                        "$ref": "#/definitions/backend_internal_application_query_model.UserQueryResult"
                    }
                }
            }
        },
        "backend_internal_application_query_model.UserProfileResult": {
            "type": "object",
            "properties": {
                "avatar": {
                    "type": "string"
                },
                "first_name": {
                    "type": "string"
                },
                "language": {
                    "type": "string"
                },
                "last_name": {
                    "type": "string"
                },
                "nickname": {
                    "type": "string"
                },
                "timezone": {
                    "type": "string"
                }
            }
        },
        "backend_internal_application_query_model.UserQueryResult": {
            "type": "object",
            "properties": {
                "business_id": {
                    "type": "string"
                },
                "created_at": {
                    "type": "string"
                },
                "email": {
                    "type": "string"
                },
                "metadata": {
                    "type": "object",
                    "additionalProperties": true
                },
                "permissions": {
                    "type": "array",
                    "items": {
                        "type": "string"
                    }
                },
                "phone": {
                    "type": "string"
                },
                "profile": {
                    "$ref": "#/definitions/backend_internal_application_query_model.UserProfileResult"
                },
                "roles": {
                    "type": "array",
                    "items": {
                        "type": "string"
                    }
                },
                "status": {
                    "type": "string"
                },
                "tenants": {
                    "type": "array",
                    "items": {
                        "$ref": "#/definitions/backend_internal_application_query_model.UserTenantResult"
                    }
                },
                "updated_at": {
                    "type": "string"
                },
                "user_id": {
                    "type": "string"
                },
                "username": {
                    "type": "string"
                },
                "version": {
                    "type": "integer"
                }
            }
        },
        "backend_internal_application_query_model.UserRolesResult": {
            "type": "object",
            "properties": {
                "roles": {
                    "type": "array",
                    "items": {
                        "$ref": "#/definitions/backend_internal_application_query_model.Role"
                    }
                },
                "tenant_id": {
                    "type": "string"
                },
                "user_id": {
                    "type": "string"
                }
            }
        },
        "backend_internal_application_query_model.UserTenantResult": {
            "type": "object",
            "properties": {
                "joined_at": {
                    "type": "string"
                },
                "role_id": {
                    "type": "string"
                },
                "role_name": {
                    "type": "string"
                },
                "status": {
                    "type": "string"
                },
                "tenant_id": {
                    "type": "string"
                },
                "tenant_name": {
                    "type": "string"
                }
            }
        },
        "backend_internal_application_query_model.ValidateTokenQuery": {
            "type": "object",
            "required": [
                "token"
            ],
            "properties": {
                "token": {
                    "type": "string"
                }
            }
        },
        "backend_internal_application_usecase_user.DeactivateUserRequest": {
            "type": "object",
            "required": [
                "tenant_id",
                "user_id"
            ],
            "properties": {
                "reason": {
                    "type": "string"
                },
                "tenant_id": {
                    "type": "string"
                },
                "user_id": {
                    "type": "string"
                }
            }
        },
        "backend_internal_application_usecase_user.RegisterUserRequest": {
            "type": "object",
            "required": [
                "email",
                "password",
                "phone",
                "tenant_id",
                "username"
            ],
            "properties": {
                "email": {
                    "type": "string"
                },
                "first_name": {
                    "type": "string"
                },
                "language": {
                    "type": "string"
                },
                "last_name": {
                    "type": "string"
                },
                "password": {
                    "type": "string",
                    "minLength": 8
                },
                "phone": {
                    "type": "string"
                },
                "tenant_id": {
                    "type": "string"
                },
                "timezone": {
                    "type": "string"
                },
                "username": {
                    "type": "string",
                    "maxLength": 50,
                    "minLength": 3
                }
            }
        },
        "backend_internal_application_usecase_user.RegisterUserResponse": {
            "type": "object",
            "properties": {
                "email": {
                    "type": "string"
                },
                "first_name": {
                    "type": "string"
                },
                "last_name": {
                    "type": "string"
                },
                "phone": {
                    "type": "string"
                },
                "user_id": {
                    "type": "string"
                },
                "username": {
                    "type": "string"
                }
            }
        },
        "backend_internal_application_usecase_user.UpdateUserProfileRequest": {
            "type": "object",
            "required": [
                "tenant_id",
                "user_id"
            ],
            "properties": {
                "avatar": {
                    "type": "string"
                },
                "first_name": {
                    "type": "string"
                },
                "language": {
                    "type": "string"
                },
                "last_name": {
                    "type": "string"
                },
                "nickname": {
                    "type": "string"
                },
                "tenant_id": {
                    "type": "string"
                },
                "timezone": {
                    "type": "string"
                },
                "user_id": {
                    "type": "string"
                }
            }
        },
        "backend_pkg_common_response.APIResponse": {
            "type": "object",
            "properties": {
                "code": {
                    "type": "integer"
                },
                "data": {},
                "message": {
                    "type": "string"
                },
                "trace_id": {
                    "type": "string"
                }
            }
        }
    },
    "securityDefinitions": {
        "ApiKeyAuth": {
            "description": "Bearer JWT令牌认证，格式：Bearer {token}",
            "type": "apiKey",
            "name": "Authorization",
            "in": "header"
        }
    },
    "tags": [
        {
            "description": "用户认证和授权相关接口",
            "name": "认证"
        },
        {
            "description": "用户信息管理接口",
            "name": "用户管理"
        },
        {
            "description": "多租户管理接口",
            "name": "租户管理"
        },
        {
            "description": "产品信息管理接口",
            "name": "产品管理"
        },
        {
            "description": "订单处理相关接口",
            "name": "订单管理"
        },
        {
            "description": "采购流程管理接口",
            "name": "采购管理"
        },
        {
            "description": "库存监控和管理接口",
            "name": "库存管理"
        },
        {
            "description": "财务数据和报表接口",
            "name": "财务管理"
        },
        {
            "description": "系统配置和监控接口",
            "name": "系统管理"
        }
    ]
}`

// SwaggerInfo holds exported Swagger Info so clients can modify it
var SwaggerInfo = &swag.Spec{
	Version:          "1.0",
	Host:             "localhost:8080",
	BasePath:         "/api/v1",
	Schemes:          []string{},
	Title:            "九翼跨境电商ERP系统API",
	Description:      "基于DDD架构的跨境电商ERP管理系统",
	InfoInstanceName: "swagger",
	SwaggerTemplate:  docTemplate,
	LeftDelim:        "{{",
	RightDelim:       "}}",
}

func init() {
	swag.Register(SwaggerInfo.InstanceName(), SwaggerInfo)
}
