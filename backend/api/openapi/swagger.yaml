basePath: /api/v1
definitions:
  DemoRequest:
    properties:
      age:
        description: 年龄
        example: 25
        maximum: 120
        minimum: 1
        type: integer
      email:
        description: 邮箱地址
        example: <PERSON><PERSON><PERSON>@example.com
        type: string
      name:
        description: 用户名
        example: 张三
        maxLength: 50
        minLength: 1
        type: string
      type:
        description: '用户类型: admin=管理员, user=普通用户, guest=访客'
        enum:
        - admin
        - user
        - guest
        example: user
        type: string
    required:
    - age
    - email
    - name
    - type
    type: object
  DemoResponse:
    properties:
      id:
        description: 处理结果ID
        example: demo_123456
        type: string
      processed_at:
        description: 处理时间戳
        example: "2023-01-01T00:00:00Z"
        type: string
      status:
        description: 处理状态
        example: success
        type: string
      user_info:
        allOf:
        - $ref: '#/definitions/DemoUserInfo'
        description: 用户信息
    type: object
  DemoUserInfo:
    properties:
      display_name:
        description: 显示名称
        example: 张三
        type: string
      is_vip:
        description: 是否为VIP用户
        example: false
        type: boolean
      level:
        description: 用户级别
        example: standard
        type: string
    type: object
  backend_internal_application_command_model.ActivateUserResult:
    properties:
      activated_at:
        type: string
      status:
        type: string
      user_id:
        type: string
    type: object
  backend_internal_application_command_model.AuthResult:
    properties:
      access_token:
        type: string
      expires_at:
        type: string
      expires_in:
        type: integer
      permissions:
        items:
          type: string
        type: array
      refresh_token:
        type: string
      roles:
        items:
          type: string
        type: array
      tenant_id:
        type: string
      token_type:
        type: string
      user_id:
        type: string
    type: object
  backend_internal_application_command_model.ChangePasswordCommand:
    properties:
      new_password:
        maxLength: 255
        minLength: 8
        type: string
      old_password:
        maxLength: 255
        minLength: 6
        type: string
      user_id:
        maxLength: 36
        type: string
    required:
    - new_password
    - old_password
    - user_id
    type: object
  backend_internal_application_command_model.DeactivateUserResult:
    properties:
      deactivated_at:
        type: string
      status:
        type: string
      user_id:
        type: string
    type: object
  backend_internal_application_command_model.LoginCommand:
    properties:
      credential:
        maxLength: 255
        minLength: 6
        type: string
      device_id:
        maxLength: 64
        type: string
      device_info:
        maxLength: 500
        type: string
      identifier:
        maxLength: 255
        minLength: 1
        type: string
      identity_type:
        enum:
        - username
        - email
        - phone
        type: string
      remember_me:
        type: boolean
    required:
    - credential
    - identifier
    - identity_type
    type: object
  backend_internal_application_command_model.LoginResult:
    properties:
      avatar:
        type: string
      display_name:
        type: string
      email:
        type: string
      login_time:
        type: string
      phone:
        type: string
      requires_mfa:
        type: boolean
      tenants:
        items:
          $ref: '#/definitions/backend_internal_application_command_model.Tenant'
        type: array
      user_id:
        type: string
      username:
        type: string
    type: object
  backend_internal_application_command_model.LogoutCommand:
    properties:
      device_id:
        maxLength: 64
        type: string
      logout_type:
        enum:
        - current
        - all
        type: string
      tenant_id:
        maxLength: 36
        type: string
      user_id:
        maxLength: 36
        type: string
    required:
    - logout_type
    - tenant_id
    - user_id
    type: object
  backend_internal_application_command_model.OperationResult:
    properties:
      data:
        additionalProperties: true
        type: object
      message:
        type: string
      success:
        type: boolean
      timestamp:
        type: string
    type: object
  backend_internal_application_command_model.RefreshTokenCommand:
    properties:
      device_id:
        maxLength: 64
        type: string
      refresh_token:
        type: string
    required:
    - refresh_token
    type: object
  backend_internal_application_command_model.ResetPasswordCommand:
    properties:
      identifier:
        maxLength: 255
        minLength: 1
        type: string
      identity_type:
        enum:
        - email
        - phone
        type: string
      new_password:
        maxLength: 255
        minLength: 8
        type: string
      verify_code:
        type: string
    required:
    - identifier
    - identity_type
    - new_password
    - verify_code
    type: object
  backend_internal_application_command_model.SelectTenantCommand:
    properties:
      device_id:
        maxLength: 64
        type: string
      tenant_id:
        maxLength: 36
        type: string
      user_id:
        maxLength: 36
        type: string
    required:
    - tenant_id
    - user_id
    type: object
  backend_internal_application_command_model.Tenant:
    properties:
      domain:
        type: string
      permissions:
        items:
          type: string
        type: array
      role:
        type: string
      tenant_id:
        type: string
      tenant_name:
        type: string
    type: object
  backend_internal_application_command_model.UpdateUserResult:
    properties:
      email:
        type: string
      status:
        type: string
      updated_at:
        type: string
      user_id:
        type: string
      username:
        type: string
      version:
        type: integer
    type: object
  backend_internal_application_dto.BatchUpdateStatusRequestDTO:
    properties:
      reason:
        maxLength: 500
        type: string
      status:
        enum:
        - active
        - suspended
        - terminated
        type: string
      tenant_ids:
        items:
          type: string
        minItems: 1
        type: array
    required:
    - status
    - tenant_ids
    type: object
  backend_internal_application_dto.CancelSubscriptionRequestDTO:
    properties:
      reason:
        maxLength: 500
        type: string
      refund_amount:
        minimum: 0
        type: number
      refund_reason:
        maxLength: 500
        type: string
    required:
    - reason
    type: object
  backend_internal_application_dto.CreateSubscriptionRequestDTO:
    properties:
      api_limit:
        minimum: 1
        type: integer
      billing_cycle:
        enum:
        - monthly
        - yearly
        - lifetime
        type: string
      currency:
        type: string
      end_date:
        type: string
      order_limit:
        minimum: 1
        type: integer
      plan_id:
        type: string
      plan_name:
        type: string
      price:
        type: number
      product_limit:
        minimum: 1
        type: integer
      start_date:
        type: string
      storage_limit:
        minimum: 1
        type: integer
      tenant_id:
        type: string
      user_limit:
        minimum: 1
        type: integer
    required:
    - billing_cycle
    - currency
    - end_date
    - plan_id
    - plan_name
    - price
    - start_date
    - tenant_id
    type: object
  backend_internal_application_dto.CreateTenantRequestDTO:
    properties:
      address:
        maxLength: 255
        type: string
      city:
        maxLength: 50
        type: string
      contact_email:
        type: string
      contact_phone:
        type: string
      country:
        maxLength: 50
        type: string
      description:
        maxLength: 500
        type: string
      display_name:
        maxLength: 100
        type: string
      domain:
        maxLength: 100
        minLength: 3
        type: string
      industry:
        maxLength: 100
        type: string
      name:
        maxLength: 100
        minLength: 2
        type: string
      province:
        maxLength: 50
        type: string
      type:
        enum:
        - system
        - enterprise
        - professional
        - basic
        - trial
        type: string
    required:
    - domain
    - name
    - type
    type: object
  backend_internal_application_dto.ExtendTrialRequestDTO:
    properties:
      days:
        maximum: 90
        minimum: 1
        type: integer
      reason:
        maxLength: 500
        type: string
    required:
    - days
    - reason
    type: object
  backend_internal_application_dto.HealthAlertDTO:
    properties:
      created_at:
        type: string
      level:
        description: info, warning, critical
        type: string
      message:
        type: string
      severity:
        description: low, medium, high
        type: string
      type:
        description: tenant_status, subscription_expired, quota_exceeded
        type: string
    type: object
  backend_internal_application_dto.QuotaAlertDTO:
    properties:
      created_at:
        type: string
      current:
        description: 当前使用率
        type: number
      level:
        description: warning, critical
        type: string
      message:
        type: string
      threshold:
        description: 告警阈值
        type: number
      type:
        description: user, storage, api, product, order
        type: string
    type: object
  backend_internal_application_dto.RenewSubscriptionRequestDTO:
    properties:
      billing_cycle:
        enum:
        - monthly
        - yearly
        - lifetime
        type: string
      currency:
        type: string
      end_date:
        type: string
      price:
        type: number
      start_date:
        type: string
    required:
    - billing_cycle
    - currency
    - end_date
    - price
    - start_date
    type: object
  backend_internal_application_dto.SubscriptionListDTO:
    properties:
      has_next:
        type: boolean
      has_previous:
        type: boolean
      items:
        items:
          $ref: '#/definitions/backend_internal_application_dto.TenantSubscriptionDTO'
        type: array
      page:
        type: integer
      page_size:
        type: integer
      total:
        type: integer
      total_pages:
        type: integer
    type: object
  backend_internal_application_dto.SubscriptionStatisticsDTO:
    properties:
      active_subscriptions:
        type: integer
      arr:
        description: 年经常性收入
        type: number
      average_revenue:
        description: 平均收入
        type: number
      billing_cycle_distribution:
        additionalProperties:
          type: integer
        type: object
      churn_rate:
        description: 关键指标
        type: number
      currency_distribution:
        additionalProperties:
          type: integer
        type: object
      expired_subscriptions:
        type: integer
      generated_at:
        description: 生成时间
        type: string
      mrr:
        description: 月经常性收入
        type: number
      plan_distribution:
        additionalProperties:
          type: integer
        type: object
      renewal_rate:
        description: 续费率
        type: number
      revenue_by_currency:
        additionalProperties:
          type: number
        type: object
      revenue_by_plan:
        additionalProperties:
          type: number
        type: object
      status_distribution:
        additionalProperties:
          type: integer
        description: 状态分布
        type: object
      total_revenue:
        description: 收入统计
        type: number
      total_subscriptions:
        type: integer
      trial_subscriptions:
        type: integer
    type: object
  backend_internal_application_dto.TenantDTO:
    properties:
      address:
        type: string
      city:
        type: string
      contact_email:
        type: string
      contact_phone:
        type: string
      country:
        type: string
      created_at:
        description: 审计信息
        type: string
      description:
        type: string
      display_name:
        type: string
      domain:
        type: string
      expiry_date:
        type: string
      id:
        type: string
      industry:
        type: string
      max_api_quota:
        type: integer
      max_storage:
        type: integer
      max_users:
        description: 配额信息
        type: integer
      name:
        type: string
      province:
        type: string
      settings:
        additionalProperties: true
        type: object
      status:
        type: string
      status_display:
        type: string
      subscription_plan:
        description: 订阅信息
        type: string
      type:
        type: string
      updated_at:
        type: string
      version:
        type: integer
    type: object
  backend_internal_application_dto.TenantHealthDTO:
    properties:
      alerts:
        items:
          $ref: '#/definitions/backend_internal_application_dto.HealthAlertDTO'
        type: array
      checked_at:
        type: string
      domain:
        type: string
      health_score:
        description: 0-100分
        type: integer
      is_healthy:
        type: boolean
      last_healthy_at:
        type: string
      tenant_id:
        type: string
      tenant_name:
        type: string
    type: object
  backend_internal_application_dto.TenantQuotaDTO:
    properties:
      alerts:
        items:
          $ref: '#/definitions/backend_internal_application_dto.QuotaAlertDTO'
        type: array
      api_limit_month:
        type: integer
      api_reset_date:
        type: string
      api_used_month:
        description: API配额
        type: integer
      api_utilization:
        type: number
      email_limit_month:
        type: integer
      email_used_month:
        type: integer
      file_upload_limit:
        type: integer
      file_upload_used:
        description: 其他配额
        type: integer
      is_over_quota:
        description: 状态信息
        type: boolean
      last_updated:
        type: string
      order_limit_month:
        type: integer
      order_used_month:
        description: 订单配额
        type: integer
      order_utilization:
        type: number
      product_limit:
        type: integer
      product_used:
        description: 商品配额
        type: integer
      product_utilization:
        type: number
      storage_limit:
        type: integer
      storage_used:
        description: 存储配额
        type: integer
      storage_utilization:
        type: number
      tenant_id:
        type: string
      user_limit:
        type: integer
      user_used:
        description: 用户配额
        type: integer
      user_utilization:
        type: number
    type: object
  backend_internal_application_dto.TenantSubscriptionDTO:
    properties:
      api_limit:
        type: integer
      auto_renewal:
        type: boolean
      billing_cycle:
        type: string
      can_extend_trial:
        type: boolean
      cancel_reason:
        type: string
      canceled_at:
        description: 取消信息
        type: string
      canceled_by_user:
        type: string
      created_at:
        description: 审计信息
        type: string
      currency:
        type: string
      days_until_expiry:
        description: 到期信息
        type: integer
      end_date:
        type: string
      features:
        description: 功能权限
        items:
          type: string
        type: array
      formatted_price:
        type: string
      id:
        type: string
      is_expired:
        type: boolean
      is_expiring_soon:
        type: boolean
      is_trial_period:
        description: 试用信息
        type: boolean
      order_limit:
        type: integer
      plan_id:
        type: string
      plan_name:
        type: string
      price:
        description: 计费信息
        type: number
      product_limit:
        type: integer
      renewal_date:
        type: string
      start_date:
        description: 订阅周期
        type: string
      status:
        type: string
      status_display:
        type: string
      storage_limit:
        type: integer
      tenant_id:
        type: string
      trial_end_date:
        type: string
      trial_extensions:
        type: integer
      updated_at:
        type: string
      user_limit:
        description: 使用限制
        type: integer
    type: object
  backend_internal_application_dto.UpdateQuotaLimitsRequestDTO:
    properties:
      api_limit:
        minimum: 1
        type: integer
      email_limit:
        minimum: 1
        type: integer
      file_upload_limit:
        minimum: 1
        type: integer
      order_limit:
        minimum: 1
        type: integer
      product_limit:
        minimum: 1
        type: integer
      storage_limit:
        minimum: 1
        type: integer
      user_limit:
        minimum: 1
        type: integer
    type: object
  backend_internal_application_dto.UpdateTenantRequestDTO:
    properties:
      address:
        maxLength: 255
        type: string
      city:
        maxLength: 50
        type: string
      contact_email:
        type: string
      contact_phone:
        type: string
      country:
        maxLength: 50
        type: string
      description:
        maxLength: 500
        type: string
      display_name:
        maxLength: 100
        type: string
      industry:
        maxLength: 100
        type: string
      name:
        maxLength: 100
        minLength: 2
        type: string
      province:
        maxLength: 50
        type: string
    type: object
  backend_internal_application_dto.UpdateTenantStatusRequestDTO:
    properties:
      reason:
        maxLength: 500
        type: string
      status:
        enum:
        - active
        - suspended
        - terminated
        type: string
    required:
    - status
    type: object
  backend_internal_application_dto.ValidateDomainRequestDTO:
    properties:
      domain:
        maxLength: 100
        minLength: 3
        type: string
    required:
    - domain
    type: object
  backend_internal_application_pagination_dto.PaginationLinksDTO:
    properties:
      first:
        example: /api/users?page=1&page_size=10
        type: string
      last:
        example: /api/users?page=10&page_size=10
        type: string
      next:
        example: /api/users?page=2&page_size=10
        type: string
      previous:
        example: /api/users?page=0&page_size=10
        type: string
      self:
        example: /api/users?page=1&page_size=10
        type: string
    type: object
  backend_internal_application_pagination_dto.PaginationMetaDTO:
    properties:
      count:
        example: 10
        type: integer
      has_next:
        example: true
        type: boolean
      has_previous:
        example: false
        type: boolean
      page:
        example: 1
        type: integer
      page_size:
        example: 10
        type: integer
      total:
        example: 100
        type: integer
      total_pages:
        example: 10
        type: integer
    type: object
  backend_internal_application_pagination_dto.PaginationResponseDTO-backend_internal_application_dto_TenantDTO:
    properties:
      items:
        items:
          $ref: '#/definitions/backend_internal_application_dto.TenantDTO'
        type: array
      links:
        $ref: '#/definitions/backend_internal_application_pagination_dto.PaginationLinksDTO'
      pagination:
        $ref: '#/definitions/backend_internal_application_pagination_dto.PaginationMetaDTO'
      performance:
        $ref: '#/definitions/backend_internal_application_pagination_dto.PerformanceDTO'
    type: object
  backend_internal_application_pagination_dto.PerformanceDTO:
    properties:
      from_cache:
        example: false
        type: boolean
      query_time:
        example: 15ms
        type: string
      total_time:
        example: 25ms
        type: string
    type: object
  backend_internal_application_query_model.CheckPermissionQuery:
    properties:
      action:
        maxLength: 50
        type: string
      resource:
        maxLength: 100
        type: string
      tenant_id:
        maxLength: 36
        type: string
      user_id:
        maxLength: 36
        type: string
    required:
    - action
    - resource
    - tenant_id
    - user_id
    type: object
  backend_internal_application_query_model.PermissionCheckResult:
    properties:
      action:
        type: string
      allowed:
        type: boolean
      reason:
        type: string
      resource:
        type: string
      roles:
        items:
          type: string
        type: array
      tenant_id:
        type: string
      user_id:
        type: string
    type: object
  backend_internal_application_query_model.Role:
    properties:
      created_at:
        type: string
      description:
        type: string
      permissions:
        items:
          type: string
        type: array
      role_id:
        type: string
      role_name:
        type: string
    type: object
  backend_internal_application_query_model.RoleListResult:
    properties:
      page:
        type: integer
      page_size:
        type: integer
      roles:
        items:
          $ref: '#/definitions/backend_internal_application_query_model.Role'
        type: array
      total:
        type: integer
      total_pages:
        type: integer
    type: object
  backend_internal_application_query_model.TokenValidationResult:
    properties:
      error:
        type: string
      expires_at:
        type: string
      permissions:
        items:
          type: string
        type: array
      roles:
        items:
          type: string
        type: array
      tenant_id:
        type: string
      user_id:
        type: string
      valid:
        type: boolean
    type: object
  backend_internal_application_query_model.UserAuthResult:
    properties:
      avatar:
        type: string
      created_at:
        type: string
      display_name:
        type: string
      email:
        type: string
      last_login_at:
        type: string
      phone:
        type: string
      status:
        type: string
      updated_at:
        type: string
      user_id:
        type: string
      username:
        type: string
    type: object
  backend_internal_application_query_model.UserListResult:
    properties:
      page:
        type: integer
      page_size:
        type: integer
      total:
        type: integer
      total_pages:
        type: integer
      users:
        items:
          $ref: '#/definitions/backend_internal_application_query_model.UserQueryResult'
        type: array
    type: object
  backend_internal_application_query_model.UserProfileResult:
    properties:
      avatar:
        type: string
      first_name:
        type: string
      language:
        type: string
      last_name:
        type: string
      nickname:
        type: string
      timezone:
        type: string
    type: object
  backend_internal_application_query_model.UserQueryResult:
    properties:
      business_id:
        type: string
      created_at:
        type: string
      email:
        type: string
      metadata:
        additionalProperties: true
        type: object
      permissions:
        items:
          type: string
        type: array
      phone:
        type: string
      profile:
        $ref: '#/definitions/backend_internal_application_query_model.UserProfileResult'
      roles:
        items:
          type: string
        type: array
      status:
        type: string
      tenants:
        items:
          $ref: '#/definitions/backend_internal_application_query_model.UserTenantResult'
        type: array
      updated_at:
        type: string
      user_id:
        type: string
      username:
        type: string
      version:
        type: integer
    type: object
  backend_internal_application_query_model.UserRolesResult:
    properties:
      roles:
        items:
          $ref: '#/definitions/backend_internal_application_query_model.Role'
        type: array
      tenant_id:
        type: string
      user_id:
        type: string
    type: object
  backend_internal_application_query_model.UserTenantResult:
    properties:
      joined_at:
        type: string
      role_id:
        type: string
      role_name:
        type: string
      status:
        type: string
      tenant_id:
        type: string
      tenant_name:
        type: string
    type: object
  backend_internal_application_query_model.ValidateTokenQuery:
    properties:
      token:
        type: string
    required:
    - token
    type: object
  backend_internal_application_usecase_user.DeactivateUserRequest:
    properties:
      reason:
        type: string
      tenant_id:
        type: string
      user_id:
        type: string
    required:
    - tenant_id
    - user_id
    type: object
  backend_internal_application_usecase_user.RegisterUserRequest:
    properties:
      email:
        type: string
      first_name:
        type: string
      language:
        type: string
      last_name:
        type: string
      password:
        minLength: 8
        type: string
      phone:
        type: string
      tenant_id:
        type: string
      timezone:
        type: string
      username:
        maxLength: 50
        minLength: 3
        type: string
    required:
    - email
    - password
    - phone
    - tenant_id
    - username
    type: object
  backend_internal_application_usecase_user.RegisterUserResponse:
    properties:
      email:
        type: string
      first_name:
        type: string
      last_name:
        type: string
      phone:
        type: string
      user_id:
        type: string
      username:
        type: string
    type: object
  backend_internal_application_usecase_user.UpdateUserProfileRequest:
    properties:
      avatar:
        type: string
      first_name:
        type: string
      language:
        type: string
      last_name:
        type: string
      nickname:
        type: string
      tenant_id:
        type: string
      timezone:
        type: string
      user_id:
        type: string
    required:
    - tenant_id
    - user_id
    type: object
  backend_pkg_common_response.APIResponse:
    properties:
      code:
        type: integer
      data: {}
      message:
        type: string
      trace_id:
        type: string
    type: object
host: localhost:8080
info:
  contact:
    email: <EMAIL>
    name: API支持团队
    url: http://www.swagger.io/support
  description: 基于DDD架构的跨境电商ERP管理系统
  license:
    name: MIT
    url: https://opensource.org/licenses/MIT
  termsOfService: http://swagger.io/terms/
  title: 九翼跨境电商ERP系统API
  version: "1.0"
paths:
  /auth/change-password:
    post:
      consumes:
      - application/json
      description: 允许已认证的用户修改其密码
      parameters:
      - description: 修改密码请求
        in: body
        name: request
        required: true
        schema:
          $ref: '#/definitions/backend_internal_application_command_model.ChangePasswordCommand'
      produces:
      - application/json
      responses:
        "200":
          description: 密码修改成功
          schema:
            allOf:
            - $ref: '#/definitions/backend_pkg_common_response.APIResponse'
            - properties:
                data:
                  $ref: '#/definitions/backend_internal_application_command_model.OperationResult'
              type: object
        "400":
          description: 无效请求
          schema:
            $ref: '#/definitions/backend_pkg_common_response.APIResponse'
        "401":
          description: 无效的旧密码
          schema:
            $ref: '#/definitions/backend_pkg_common_response.APIResponse'
      security:
      - ApiKeyAuth: []
      summary: 修改密码
      tags:
      - 认证
  /auth/check-permission:
    post:
      consumes:
      - application/json
      description: 检查用户是否有特定资源的操作权限
      parameters:
      - description: 权限检查请求
        in: body
        name: request
        required: true
        schema:
          $ref: '#/definitions/backend_internal_application_query_model.CheckPermissionQuery'
      produces:
      - application/json
      responses:
        "200":
          description: 权限检查结果
          schema:
            allOf:
            - $ref: '#/definitions/backend_pkg_common_response.APIResponse'
            - properties:
                data:
                  $ref: '#/definitions/backend_internal_application_query_model.PermissionCheckResult'
              type: object
        "400":
          description: 无效请求
          schema:
            $ref: '#/definitions/backend_pkg_common_response.APIResponse'
      security:
      - ApiKeyAuth: []
      summary: 检查用户权限
      tags:
      - 权限
  /auth/login:
    post:
      consumes:
      - application/json
      description: 认证用户并返回可访问的租户列表
      parameters:
      - description: 登录凭据
        in: body
        name: request
        required: true
        schema:
          $ref: '#/definitions/backend_internal_application_command_model.LoginCommand'
      produces:
      - application/json
      responses:
        "200":
          description: 登录成功，返回租户列表
          schema:
            allOf:
            - $ref: '#/definitions/backend_pkg_common_response.APIResponse'
            - properties:
                data:
                  $ref: '#/definitions/backend_internal_application_command_model.LoginResult'
              type: object
        "400":
          description: 无效请求
          schema:
            $ref: '#/definitions/backend_pkg_common_response.APIResponse'
        "401":
          description: 无效凭据
          schema:
            $ref: '#/definitions/backend_pkg_common_response.APIResponse'
      summary: 用户登录 - 阶段一
      tags:
      - 认证
  /auth/logout:
    post:
      consumes:
      - application/json
      description: 用户注销登录，撤销令牌
      parameters:
      - description: 注销请求
        in: body
        name: request
        required: true
        schema:
          $ref: '#/definitions/backend_internal_application_command_model.LogoutCommand'
      produces:
      - application/json
      responses:
        "200":
          description: 注销成功
          schema:
            allOf:
            - $ref: '#/definitions/backend_pkg_common_response.APIResponse'
            - properties:
                data:
                  $ref: '#/definitions/backend_internal_application_command_model.OperationResult'
              type: object
      security:
      - ApiKeyAuth: []
      summary: 用户注销
      tags:
      - 认证
  /auth/profile:
    get:
      description: 获取当前认证用户的个人资料信息
      produces:
      - application/json
      responses:
        "200":
          description: 成功返回个人资料
          schema:
            allOf:
            - $ref: '#/definitions/backend_pkg_common_response.APIResponse'
            - properties:
                data:
                  $ref: '#/definitions/backend_internal_application_query_model.UserAuthResult'
              type: object
        "401":
          description: 用户未认证
          schema:
            $ref: '#/definitions/backend_pkg_common_response.APIResponse'
      security:
      - ApiKeyAuth: []
      summary: 获取用户个人资料
      tags:
      - 认证
  /auth/refresh:
    post:
      consumes:
      - application/json
      description: 使用刷新令牌获取新的访问令牌
      parameters:
      - description: 刷新令牌请求
        in: body
        name: request
        required: true
        schema:
          $ref: '#/definitions/backend_internal_application_command_model.RefreshTokenCommand'
      produces:
      - application/json
      responses:
        "200":
          description: 令牌刷新成功
          schema:
            allOf:
            - $ref: '#/definitions/backend_pkg_common_response.APIResponse'
            - properties:
                data:
                  $ref: '#/definitions/backend_internal_application_command_model.AuthResult'
              type: object
        "400":
          description: 无效请求
          schema:
            $ref: '#/definitions/backend_pkg_common_response.APIResponse'
        "401":
          description: 无效的刷新令牌
          schema:
            $ref: '#/definitions/backend_pkg_common_response.APIResponse'
      summary: 刷新访问令牌
      tags:
      - 认证
  /auth/reset-password:
    post:
      consumes:
      - application/json
      description: 通过验证码重置用户密码
      parameters:
      - description: 重置密码请求
        in: body
        name: request
        required: true
        schema:
          $ref: '#/definitions/backend_internal_application_command_model.ResetPasswordCommand'
      produces:
      - application/json
      responses:
        "200":
          description: 密码重置成功
          schema:
            allOf:
            - $ref: '#/definitions/backend_pkg_common_response.APIResponse'
            - properties:
                data:
                  $ref: '#/definitions/backend_internal_application_command_model.OperationResult'
              type: object
        "400":
          description: 无效请求
          schema:
            $ref: '#/definitions/backend_pkg_common_response.APIResponse'
        "401":
          description: 无效的验证码
          schema:
            $ref: '#/definitions/backend_pkg_common_response.APIResponse'
      summary: 重置密码
      tags:
      - 认证
  /auth/roles/{tenant_id}:
    get:
      description: 获取用户在指定租户下的角色列表
      parameters:
      - description: 租户ID
        in: path
        name: tenant_id
        required: true
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: 用户角色列表
          schema:
            allOf:
            - $ref: '#/definitions/backend_pkg_common_response.APIResponse'
            - properties:
                data:
                  $ref: '#/definitions/backend_internal_application_query_model.UserRolesResult'
              type: object
        "400":
          description: 无效请求
          schema:
            $ref: '#/definitions/backend_pkg_common_response.APIResponse'
      security:
      - ApiKeyAuth: []
      summary: 获取用户角色
      tags:
      - 角色
  /auth/roles/list/{tenant_id}:
    get:
      description: 获取指定租户下的角色列表
      parameters:
      - description: 租户ID
        in: path
        name: tenant_id
        required: true
        type: string
      - default: 1
        description: 页码
        in: query
        name: page
        type: integer
      - default: 20
        description: 每页大小
        in: query
        name: page_size
        type: integer
      - description: 排序字段
        enum:
        - name
        - created_at
        - updated_at
        in: query
        name: sort_by
        type: string
      - default: false
        description: 是否降序
        in: query
        name: sort_desc
        type: boolean
      produces:
      - application/json
      responses:
        "200":
          description: 角色列表
          schema:
            allOf:
            - $ref: '#/definitions/backend_pkg_common_response.APIResponse'
            - properties:
                data:
                  $ref: '#/definitions/backend_internal_application_query_model.RoleListResult'
              type: object
        "400":
          description: 无效请求
          schema:
            $ref: '#/definitions/backend_pkg_common_response.APIResponse'
      security:
      - ApiKeyAuth: []
      summary: 获取角色列表
      tags:
      - 角色
  /auth/select-tenant:
    post:
      consumes:
      - application/json
      description: 选择一个租户并返回JWT令牌
      parameters:
      - description: 租户选择
        in: body
        name: request
        required: true
        schema:
          $ref: '#/definitions/backend_internal_application_command_model.SelectTenantCommand'
      produces:
      - application/json
      responses:
        "200":
          description: 租户已选择，令牌已生成
          schema:
            allOf:
            - $ref: '#/definitions/backend_pkg_common_response.APIResponse'
            - properties:
                data:
                  $ref: '#/definitions/backend_internal_application_command_model.AuthResult'
              type: object
        "400":
          description: 无效请求
          schema:
            $ref: '#/definitions/backend_pkg_common_response.APIResponse'
        "403":
          description: 租户访问被拒绝
          schema:
            $ref: '#/definitions/backend_pkg_common_response.APIResponse'
      security:
      - ApiKeyAuth: []
      summary: 用户登录 - 阶段二
      tags:
      - 认证
  /auth/validate-token:
    post:
      consumes:
      - application/json
      description: 验证JWT令牌的有效性
      parameters:
      - description: 令牌验证请求
        in: body
        name: request
        required: true
        schema:
          $ref: '#/definitions/backend_internal_application_query_model.ValidateTokenQuery'
      produces:
      - application/json
      responses:
        "200":
          description: 令牌验证结果
          schema:
            allOf:
            - $ref: '#/definitions/backend_pkg_common_response.APIResponse'
            - properties:
                data:
                  $ref: '#/definitions/backend_internal_application_query_model.TokenValidationResult'
              type: object
        "400":
          description: 无效请求
          schema:
            $ref: '#/definitions/backend_pkg_common_response.APIResponse'
      summary: 验证令牌
      tags:
      - 认证
  /demo/hello:
    get:
      consumes:
      - application/json
      description: 这是一个演示接口，用于展示基本的GET请求处理
      parameters:
      - description: 用户名称
        in: query
        name: name
        type: string
      - description: 用户年龄
        in: query
        maximum: 120
        minimum: 1
        name: age
        type: integer
      - default: json
        description: 返回格式
        enum:
        - json
        - xml
        in: query
        name: format
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: 请求成功
          schema:
            allOf:
            - $ref: '#/definitions/backend_pkg_common_response.APIResponse'
            - properties:
                data:
                  additionalProperties: true
                  type: object
              type: object
        "400":
          description: 请求参数错误
          schema:
            $ref: '#/definitions/backend_pkg_common_response.APIResponse'
        "500":
          description: 服务器内部错误
          schema:
            $ref: '#/definitions/backend_pkg_common_response.APIResponse'
      summary: 演示问候接口
      tags:
      - 系统管理
  /demo/process:
    post:
      consumes:
      - application/json
      description: 这是一个演示接口，用于展示POST请求处理和数据验证
      parameters:
      - description: 处理请求数据
        in: body
        name: request
        required: true
        schema:
          $ref: '#/definitions/DemoRequest'
      produces:
      - application/json
      responses:
        "200":
          description: 处理成功
          schema:
            allOf:
            - $ref: '#/definitions/backend_pkg_common_response.APIResponse'
            - properties:
                data:
                  $ref: '#/definitions/DemoResponse'
              type: object
        "201":
          description: 创建成功
          schema:
            allOf:
            - $ref: '#/definitions/backend_pkg_common_response.APIResponse'
            - properties:
                data:
                  $ref: '#/definitions/DemoResponse'
              type: object
        "400":
          description: 请求参数错误
          schema:
            $ref: '#/definitions/backend_pkg_common_response.APIResponse'
        "422":
          description: 数据验证失败
          schema:
            $ref: '#/definitions/backend_pkg_common_response.APIResponse'
        "500":
          description: 服务器内部错误
          schema:
            $ref: '#/definitions/backend_pkg_common_response.APIResponse'
      summary: 演示数据处理接口
      tags:
      - 系统管理
  /subscriptions:
    get:
      consumes:
      - application/json
      description: 分页获取订阅列表，支持过滤和排序
      parameters:
      - default: 1
        description: 页码
        in: query
        name: page
        type: integer
      - default: 10
        description: 每页数量
        in: query
        name: page_size
        type: integer
      - default: created_at
        description: 排序字段
        in: query
        name: sort_by
        type: string
      - default: true
        description: 是否降序
        in: query
        name: sort_desc
        type: boolean
      - description: 租户ID
        in: query
        name: tenant_id
        type: string
      - description: 计划类型
        in: query
        name: plan_type
        type: string
      - description: 订阅状态
        in: query
        name: status
        type: string
      - description: 计费周期
        in: query
        name: billing_cycle
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: 获取成功
          schema:
            allOf:
            - $ref: '#/definitions/backend_pkg_common_response.APIResponse'
            - properties:
                data:
                  $ref: '#/definitions/backend_internal_application_dto.SubscriptionListDTO'
              type: object
        "500":
          description: 服务器内部错误
          schema:
            $ref: '#/definitions/backend_pkg_common_response.APIResponse'
      summary: 获取订阅列表
      tags:
      - 订阅管理
    post:
      consumes:
      - application/json
      description: 为租户创建新的订阅计划
      parameters:
      - description: 创建订阅请求
        in: body
        name: request
        required: true
        schema:
          $ref: '#/definitions/backend_internal_application_dto.CreateSubscriptionRequestDTO'
      produces:
      - application/json
      responses:
        "201":
          description: 创建成功
          schema:
            allOf:
            - $ref: '#/definitions/backend_pkg_common_response.APIResponse'
            - properties:
                data:
                  $ref: '#/definitions/backend_internal_application_dto.TenantSubscriptionDTO'
              type: object
        "400":
          description: 请求参数错误
          schema:
            $ref: '#/definitions/backend_pkg_common_response.APIResponse'
        "409":
          description: 订阅已存在
          schema:
            $ref: '#/definitions/backend_pkg_common_response.APIResponse'
        "500":
          description: 服务器内部错误
          schema:
            $ref: '#/definitions/backend_pkg_common_response.APIResponse'
      summary: 创建订阅
      tags:
      - 订阅管理
  /subscriptions/{id}:
    get:
      consumes:
      - application/json
      description: 根据订阅ID获取订阅详细信息
      parameters:
      - description: 订阅ID
        in: path
        name: id
        required: true
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: 获取成功
          schema:
            allOf:
            - $ref: '#/definitions/backend_pkg_common_response.APIResponse'
            - properties:
                data:
                  $ref: '#/definitions/backend_internal_application_dto.TenantSubscriptionDTO'
              type: object
        "404":
          description: 订阅不存在
          schema:
            $ref: '#/definitions/backend_pkg_common_response.APIResponse'
        "500":
          description: 服务器内部错误
          schema:
            $ref: '#/definitions/backend_pkg_common_response.APIResponse'
      summary: 获取订阅详情
      tags:
      - 订阅管理
  /subscriptions/{id}/cancel:
    put:
      consumes:
      - application/json
      description: 取消指定的订阅
      parameters:
      - description: 订阅ID
        in: path
        name: id
        required: true
        type: string
      - description: 取消订阅请求
        in: body
        name: request
        required: true
        schema:
          $ref: '#/definitions/backend_internal_application_dto.CancelSubscriptionRequestDTO'
      produces:
      - application/json
      responses:
        "200":
          description: 取消成功
          schema:
            allOf:
            - $ref: '#/definitions/backend_pkg_common_response.APIResponse'
            - properties:
                data:
                  $ref: '#/definitions/backend_internal_application_dto.TenantSubscriptionDTO'
              type: object
        "400":
          description: 请求参数错误
          schema:
            $ref: '#/definitions/backend_pkg_common_response.APIResponse'
        "404":
          description: 订阅不存在
          schema:
            $ref: '#/definitions/backend_pkg_common_response.APIResponse'
        "500":
          description: 服务器内部错误
          schema:
            $ref: '#/definitions/backend_pkg_common_response.APIResponse'
      summary: 取消订阅
      tags:
      - 订阅管理
  /subscriptions/{id}/extend-trial:
    put:
      consumes:
      - application/json
      description: 延长指定订阅的试用期
      parameters:
      - description: 订阅ID
        in: path
        name: id
        required: true
        type: string
      - description: 延长试用期请求
        in: body
        name: request
        required: true
        schema:
          $ref: '#/definitions/backend_internal_application_dto.ExtendTrialRequestDTO'
      produces:
      - application/json
      responses:
        "200":
          description: 延长成功
          schema:
            allOf:
            - $ref: '#/definitions/backend_pkg_common_response.APIResponse'
            - properties:
                data:
                  $ref: '#/definitions/backend_internal_application_dto.TenantSubscriptionDTO'
              type: object
        "400":
          description: 请求参数错误
          schema:
            $ref: '#/definitions/backend_pkg_common_response.APIResponse'
        "404":
          description: 订阅不存在
          schema:
            $ref: '#/definitions/backend_pkg_common_response.APIResponse'
        "500":
          description: 服务器内部错误
          schema:
            $ref: '#/definitions/backend_pkg_common_response.APIResponse'
      summary: 延长试用期
      tags:
      - 订阅管理
  /subscriptions/{id}/limits:
    put:
      consumes:
      - application/json
      description: 更新指定订阅的使用限制
      parameters:
      - description: 订阅ID
        in: path
        name: id
        required: true
        type: string
      - description: 更新订阅限制请求
        in: body
        name: request
        required: true
        schema:
          $ref: '#/definitions/backend_internal_application_dto.UpdateQuotaLimitsRequestDTO'
      produces:
      - application/json
      responses:
        "200":
          description: 更新成功
          schema:
            allOf:
            - $ref: '#/definitions/backend_pkg_common_response.APIResponse'
            - properties:
                data:
                  $ref: '#/definitions/backend_internal_application_dto.TenantSubscriptionDTO'
              type: object
        "400":
          description: 请求参数错误
          schema:
            $ref: '#/definitions/backend_pkg_common_response.APIResponse'
        "404":
          description: 订阅不存在
          schema:
            $ref: '#/definitions/backend_pkg_common_response.APIResponse'
        "500":
          description: 服务器内部错误
          schema:
            $ref: '#/definitions/backend_pkg_common_response.APIResponse'
      summary: 更新订阅限制
      tags:
      - 订阅管理
  /subscriptions/{id}/renew:
    put:
      consumes:
      - application/json
      description: 为订阅续费
      parameters:
      - description: 订阅ID
        in: path
        name: id
        required: true
        type: string
      - description: 续费订阅请求
        in: body
        name: request
        required: true
        schema:
          $ref: '#/definitions/backend_internal_application_dto.RenewSubscriptionRequestDTO'
      produces:
      - application/json
      responses:
        "200":
          description: 续费成功
          schema:
            allOf:
            - $ref: '#/definitions/backend_pkg_common_response.APIResponse'
            - properties:
                data:
                  $ref: '#/definitions/backend_internal_application_dto.TenantSubscriptionDTO'
              type: object
        "400":
          description: 请求参数错误
          schema:
            $ref: '#/definitions/backend_pkg_common_response.APIResponse'
        "404":
          description: 订阅不存在
          schema:
            $ref: '#/definitions/backend_pkg_common_response.APIResponse'
        "500":
          description: 服务器内部错误
          schema:
            $ref: '#/definitions/backend_pkg_common_response.APIResponse'
      summary: 续费订阅
      tags:
      - 订阅管理
  /subscriptions/expired:
    get:
      consumes:
      - application/json
      description: 获取已过期的订阅列表
      parameters:
      - default: 1
        description: 页码
        in: query
        name: page
        type: integer
      - default: 10
        description: 每页数量
        in: query
        name: page_size
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: 获取成功
          schema:
            allOf:
            - $ref: '#/definitions/backend_pkg_common_response.APIResponse'
            - properties:
                data:
                  $ref: '#/definitions/backend_internal_application_dto.SubscriptionListDTO'
              type: object
        "500":
          description: 服务器内部错误
          schema:
            $ref: '#/definitions/backend_pkg_common_response.APIResponse'
      summary: 获取已过期的订阅
      tags:
      - 订阅管理
  /subscriptions/expiring:
    get:
      consumes:
      - application/json
      description: 获取指定天数内即将过期的订阅列表
      parameters:
      - default: 30
        description: 天数
        in: query
        name: days
        type: integer
      - default: 1
        description: 页码
        in: query
        name: page
        type: integer
      - default: 10
        description: 每页数量
        in: query
        name: page_size
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: 获取成功
          schema:
            allOf:
            - $ref: '#/definitions/backend_pkg_common_response.APIResponse'
            - properties:
                data:
                  $ref: '#/definitions/backend_internal_application_dto.SubscriptionListDTO'
              type: object
        "500":
          description: 服务器内部错误
          schema:
            $ref: '#/definitions/backend_pkg_common_response.APIResponse'
      summary: 获取即将过期的订阅
      tags:
      - 订阅管理
  /subscriptions/statistics:
    get:
      consumes:
      - application/json
      description: 获取订阅相关的统计信息
      parameters:
      - description: 租户ID
        in: query
        name: tenant_id
        type: string
      - description: 开始日期 (YYYY-MM-DD)
        in: query
        name: start_date
        type: string
      - description: 结束日期 (YYYY-MM-DD)
        in: query
        name: end_date
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: 获取成功
          schema:
            allOf:
            - $ref: '#/definitions/backend_pkg_common_response.APIResponse'
            - properties:
                data:
                  $ref: '#/definitions/backend_internal_application_dto.SubscriptionStatisticsDTO'
              type: object
        "500":
          description: 服务器内部错误
          schema:
            $ref: '#/definitions/backend_pkg_common_response.APIResponse'
      summary: 获取订阅统计
      tags:
      - 订阅管理
  /subscriptions/trial:
    get:
      consumes:
      - application/json
      description: 获取试用期订阅列表
      parameters:
      - default: 1
        description: 页码
        in: query
        name: page
        type: integer
      - default: 10
        description: 每页数量
        in: query
        name: page_size
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: 获取成功
          schema:
            allOf:
            - $ref: '#/definitions/backend_pkg_common_response.APIResponse'
            - properties:
                data:
                  $ref: '#/definitions/backend_internal_application_dto.SubscriptionListDTO'
              type: object
        "500":
          description: 服务器内部错误
          schema:
            $ref: '#/definitions/backend_pkg_common_response.APIResponse'
      summary: 获取试用订阅
      tags:
      - 订阅管理
  /tenants:
    get:
      consumes:
      - application/json
      description: 使用新分页框架获取租户列表，支持高级过滤和排序
      parameters:
      - default: 1
        description: 页码
        in: query
        name: page
        type: integer
      - default: 10
        description: 每页数量
        in: query
        name: page_size
        type: integer
      - default: created_at
        description: 排序字段
        in: query
        name: sort_by
        type: string
      - default: true
        description: 是否降序
        in: query
        name: sort_desc
        type: boolean
      - description: 搜索关键词
        in: query
        name: search
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: 获取成功
          schema:
            allOf:
            - $ref: '#/definitions/backend_pkg_common_response.APIResponse'
            - properties:
                data:
                  $ref: '#/definitions/backend_internal_application_pagination_dto.PaginationResponseDTO-backend_internal_application_dto_TenantDTO'
              type: object
        "500":
          description: 服务器内部错误
          schema:
            $ref: '#/definitions/backend_pkg_common_response.APIResponse'
      summary: 获取租户列表（新分页）
      tags:
      - 租户管理
    post:
      consumes:
      - application/json
      description: 创建新的租户
      parameters:
      - description: 创建租户请求
        in: body
        name: request
        required: true
        schema:
          $ref: '#/definitions/backend_internal_application_dto.CreateTenantRequestDTO'
      produces:
      - application/json
      responses:
        "201":
          description: 创建成功
          schema:
            allOf:
            - $ref: '#/definitions/backend_pkg_common_response.APIResponse'
            - properties:
                data:
                  $ref: '#/definitions/backend_internal_application_dto.TenantDTO'
              type: object
        "400":
          description: 请求参数错误
          schema:
            $ref: '#/definitions/backend_pkg_common_response.APIResponse'
        "409":
          description: 域名已存在
          schema:
            $ref: '#/definitions/backend_pkg_common_response.APIResponse'
        "500":
          description: 服务器内部错误
          schema:
            $ref: '#/definitions/backend_pkg_common_response.APIResponse'
      summary: 创建租户
      tags:
      - 租户管理
  /tenants/{id}:
    delete:
      consumes:
      - application/json
      description: 软删除租户
      parameters:
      - description: 租户ID
        in: path
        name: id
        required: true
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: 删除成功
          schema:
            $ref: '#/definitions/backend_pkg_common_response.APIResponse'
        "404":
          description: 租户不存在
          schema:
            $ref: '#/definitions/backend_pkg_common_response.APIResponse'
        "500":
          description: 服务器内部错误
          schema:
            $ref: '#/definitions/backend_pkg_common_response.APIResponse'
      summary: 删除租户
      tags:
      - 租户管理
    get:
      consumes:
      - application/json
      description: 根据租户ID获取租户详细信息
      parameters:
      - description: 租户ID
        in: path
        name: id
        required: true
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: 获取成功
          schema:
            allOf:
            - $ref: '#/definitions/backend_pkg_common_response.APIResponse'
            - properties:
                data:
                  $ref: '#/definitions/backend_internal_application_dto.TenantDTO'
              type: object
        "404":
          description: 租户不存在
          schema:
            $ref: '#/definitions/backend_pkg_common_response.APIResponse'
        "500":
          description: 服务器内部错误
          schema:
            $ref: '#/definitions/backend_pkg_common_response.APIResponse'
      summary: 获取租户详情
      tags:
      - 租户管理
    put:
      consumes:
      - application/json
      description: 更新租户的基本信息
      parameters:
      - description: 租户ID
        in: path
        name: id
        required: true
        type: string
      - description: 更新租户请求
        in: body
        name: request
        required: true
        schema:
          $ref: '#/definitions/backend_internal_application_dto.UpdateTenantRequestDTO'
      produces:
      - application/json
      responses:
        "200":
          description: 更新成功
          schema:
            allOf:
            - $ref: '#/definitions/backend_pkg_common_response.APIResponse'
            - properties:
                data:
                  $ref: '#/definitions/backend_internal_application_dto.TenantDTO'
              type: object
        "400":
          description: 请求参数错误
          schema:
            $ref: '#/definitions/backend_pkg_common_response.APIResponse'
        "404":
          description: 租户不存在
          schema:
            $ref: '#/definitions/backend_pkg_common_response.APIResponse'
        "500":
          description: 服务器内部错误
          schema:
            $ref: '#/definitions/backend_pkg_common_response.APIResponse'
      summary: 更新租户信息
      tags:
      - 租户管理
  /tenants/{id}/activate:
    put:
      consumes:
      - application/json
      description: 激活指定的租户
      parameters:
      - description: 租户ID
        in: path
        name: id
        required: true
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: 激活成功
          schema:
            $ref: '#/definitions/backend_pkg_common_response.APIResponse'
        "404":
          description: 租户不存在
          schema:
            $ref: '#/definitions/backend_pkg_common_response.APIResponse'
        "500":
          description: 服务器内部错误
          schema:
            $ref: '#/definitions/backend_pkg_common_response.APIResponse'
      summary: 激活租户
      tags:
      - 租户管理
  /tenants/{id}/health:
    get:
      consumes:
      - application/json
      description: 获取指定租户的健康状态检查结果
      parameters:
      - description: 租户ID
        in: path
        name: id
        required: true
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: 获取成功
          schema:
            allOf:
            - $ref: '#/definitions/backend_pkg_common_response.APIResponse'
            - properties:
                data:
                  $ref: '#/definitions/backend_internal_application_dto.TenantHealthDTO'
              type: object
        "404":
          description: 租户不存在
          schema:
            $ref: '#/definitions/backend_pkg_common_response.APIResponse'
        "500":
          description: 服务器内部错误
          schema:
            $ref: '#/definitions/backend_pkg_common_response.APIResponse'
      summary: 获取租户健康状态
      tags:
      - 租户管理
  /tenants/{id}/quota:
    get:
      consumes:
      - application/json
      description: 获取指定租户的配额使用情况
      parameters:
      - description: 租户ID
        in: path
        name: id
        required: true
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: 获取成功
          schema:
            allOf:
            - $ref: '#/definitions/backend_pkg_common_response.APIResponse'
            - properties:
                data:
                  $ref: '#/definitions/backend_internal_application_dto.TenantQuotaDTO'
              type: object
        "404":
          description: 租户不存在
          schema:
            $ref: '#/definitions/backend_pkg_common_response.APIResponse'
        "500":
          description: 服务器内部错误
          schema:
            $ref: '#/definitions/backend_pkg_common_response.APIResponse'
      summary: 获取租户配额信息
      tags:
      - 租户管理
    put:
      consumes:
      - application/json
      description: 更新指定租户的配额限制
      parameters:
      - description: 租户ID
        in: path
        name: id
        required: true
        type: string
      - description: 更新配额请求
        in: body
        name: request
        required: true
        schema:
          $ref: '#/definitions/backend_internal_application_dto.UpdateQuotaLimitsRequestDTO'
      produces:
      - application/json
      responses:
        "200":
          description: 更新成功
          schema:
            allOf:
            - $ref: '#/definitions/backend_pkg_common_response.APIResponse'
            - properties:
                data:
                  $ref: '#/definitions/backend_internal_application_dto.TenantQuotaDTO'
              type: object
        "400":
          description: 请求参数错误
          schema:
            $ref: '#/definitions/backend_pkg_common_response.APIResponse'
        "404":
          description: 租户不存在
          schema:
            $ref: '#/definitions/backend_pkg_common_response.APIResponse'
        "500":
          description: 服务器内部错误
          schema:
            $ref: '#/definitions/backend_pkg_common_response.APIResponse'
      summary: 更新租户配额
      tags:
      - 租户管理
  /tenants/{id}/suspend:
    put:
      consumes:
      - application/json
      description: 暂停指定的租户
      parameters:
      - description: 租户ID
        in: path
        name: id
        required: true
        type: string
      - description: 暂停租户请求
        in: body
        name: request
        required: true
        schema:
          $ref: '#/definitions/backend_internal_application_dto.UpdateTenantStatusRequestDTO'
      produces:
      - application/json
      responses:
        "200":
          description: 暂停成功
          schema:
            $ref: '#/definitions/backend_pkg_common_response.APIResponse'
        "400":
          description: 请求参数错误
          schema:
            $ref: '#/definitions/backend_pkg_common_response.APIResponse'
        "404":
          description: 租户不存在
          schema:
            $ref: '#/definitions/backend_pkg_common_response.APIResponse'
        "500":
          description: 服务器内部错误
          schema:
            $ref: '#/definitions/backend_pkg_common_response.APIResponse'
      summary: 暂停租户
      tags:
      - 租户管理
  /tenants/{id}/terminate:
    put:
      consumes:
      - application/json
      description: 终止指定的租户
      parameters:
      - description: 租户ID
        in: path
        name: id
        required: true
        type: string
      - description: 终止租户请求
        in: body
        name: request
        required: true
        schema:
          $ref: '#/definitions/backend_internal_application_dto.UpdateTenantStatusRequestDTO'
      produces:
      - application/json
      responses:
        "200":
          description: 终止成功
          schema:
            $ref: '#/definitions/backend_pkg_common_response.APIResponse'
        "400":
          description: 请求参数错误
          schema:
            $ref: '#/definitions/backend_pkg_common_response.APIResponse'
        "404":
          description: 租户不存在
          schema:
            $ref: '#/definitions/backend_pkg_common_response.APIResponse'
        "500":
          description: 服务器内部错误
          schema:
            $ref: '#/definitions/backend_pkg_common_response.APIResponse'
      summary: 终止租户
      tags:
      - 租户管理
  /tenants/{tenant_id}/subscription:
    get:
      consumes:
      - application/json
      description: 获取指定租户的当前订阅信息
      parameters:
      - description: 租户ID
        in: path
        name: tenant_id
        required: true
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: 获取成功
          schema:
            allOf:
            - $ref: '#/definitions/backend_pkg_common_response.APIResponse'
            - properties:
                data:
                  $ref: '#/definitions/backend_internal_application_dto.TenantSubscriptionDTO'
              type: object
        "404":
          description: 订阅不存在
          schema:
            $ref: '#/definitions/backend_pkg_common_response.APIResponse'
        "500":
          description: 服务器内部错误
          schema:
            $ref: '#/definitions/backend_pkg_common_response.APIResponse'
      summary: 获取租户订阅
      tags:
      - 订阅管理
  /tenants/batch-status:
    put:
      consumes:
      - application/json
      description: 批量更新多个租户的状态
      parameters:
      - description: 批量更新状态请求
        in: body
        name: request
        required: true
        schema:
          $ref: '#/definitions/backend_internal_application_dto.BatchUpdateStatusRequestDTO'
      produces:
      - application/json
      responses:
        "200":
          description: 更新成功
          schema:
            $ref: '#/definitions/backend_pkg_common_response.APIResponse'
        "400":
          description: 请求参数错误
          schema:
            $ref: '#/definitions/backend_pkg_common_response.APIResponse'
        "500":
          description: 服务器内部错误
          schema:
            $ref: '#/definitions/backend_pkg_common_response.APIResponse'
      summary: 批量更新租户状态
      tags:
      - 租户管理
  /tenants/search:
    get:
      consumes:
      - application/json
      description: 使用新分页框架搜索租户，支持高级过滤条件
      parameters:
      - default: 1
        description: 页码
        in: query
        name: page
        type: integer
      - default: 10
        description: 每页数量
        in: query
        name: page_size
        type: integer
      - default: created_at
        description: 排序字段
        in: query
        name: sort_by
        type: string
      - default: true
        description: 是否降序
        in: query
        name: sort_desc
        type: boolean
      - description: 搜索关键词
        in: query
        name: search
        type: string
      - description: 租户状态
        enum:
        - active
        - inactive
        - suspended
        - expired
        - terminated
        in: query
        name: status
        type: string
      - description: 租户类型
        enum:
        - system
        - enterprise
        - professional
        - basic
        - trial
        in: query
        name: type
        type: string
      - description: 行业
        in: query
        name: industry
        type: string
      - description: 国家
        in: query
        name: country
        type: string
      - description: 省份
        in: query
        name: province
        type: string
      - description: 城市
        in: query
        name: city
        type: string
      - description: 创建时间起始
        format: date-time
        in: query
        name: created_from
        type: string
      - description: 创建时间结束
        format: date-time
        in: query
        name: created_to
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: 搜索成功
          schema:
            allOf:
            - $ref: '#/definitions/backend_pkg_common_response.APIResponse'
            - properties:
                data:
                  $ref: '#/definitions/backend_internal_application_pagination_dto.PaginationResponseDTO-backend_internal_application_dto_TenantDTO'
              type: object
        "400":
          description: 请求参数错误
          schema:
            $ref: '#/definitions/backend_pkg_common_response.APIResponse'
        "500":
          description: 服务器内部错误
          schema:
            $ref: '#/definitions/backend_pkg_common_response.APIResponse'
      summary: 搜索租户（新分页）
      tags:
      - 租户管理
  /tenants/validate-domain:
    post:
      consumes:
      - application/json
      description: 检查域名是否可以使用
      parameters:
      - description: 验证域名请求
        in: body
        name: request
        required: true
        schema:
          $ref: '#/definitions/backend_internal_application_dto.ValidateDomainRequestDTO'
      produces:
      - application/json
      responses:
        "200":
          description: 验证成功
          schema:
            allOf:
            - $ref: '#/definitions/backend_pkg_common_response.APIResponse'
            - properties:
                data:
                  additionalProperties:
                    type: boolean
                  type: object
              type: object
        "400":
          description: 请求参数错误
          schema:
            $ref: '#/definitions/backend_pkg_common_response.APIResponse'
        "500":
          description: 服务器内部错误
          schema:
            $ref: '#/definitions/backend_pkg_common_response.APIResponse'
      summary: 验证域名可用性
      tags:
      - 租户管理
  /users:
    get:
      consumes:
      - application/json
      description: 获取用户列表
      parameters:
      - description: 租户ID
        in: query
        name: tenant_id
        required: true
        type: string
      - default: 1
        description: 页码
        in: query
        name: page
        type: integer
      - default: 10
        description: 每页数量
        in: query
        name: page_size
        type: integer
      - description: 搜索关键词
        in: query
        name: keyword
        type: string
      - description: 用户状态筛选
        in: query
        name: status
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: 获取成功
          schema:
            allOf:
            - $ref: '#/definitions/backend_pkg_common_response.APIResponse'
            - properties:
                data:
                  $ref: '#/definitions/backend_internal_application_query_model.UserListResult'
              type: object
        "400":
          description: 请求参数错误
          schema:
            $ref: '#/definitions/backend_pkg_common_response.APIResponse'
        "500":
          description: 服务器内部错误
          schema:
            $ref: '#/definitions/backend_pkg_common_response.APIResponse'
      security:
      - ApiKeyAuth: []
      summary: 获取用户列表
      tags:
      - 用户管理
  /users/{user_id}/activate:
    post:
      consumes:
      - application/json
      description: 激活指定的用户账户
      parameters:
      - description: 用户ID
        in: path
        name: user_id
        required: true
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: 激活成功
          schema:
            allOf:
            - $ref: '#/definitions/backend_pkg_common_response.APIResponse'
            - properties:
                data:
                  $ref: '#/definitions/backend_internal_application_command_model.ActivateUserResult'
              type: object
        "400":
          description: 无效的请求参数
          schema:
            $ref: '#/definitions/backend_pkg_common_response.APIResponse'
        "404":
          description: 用户不存在
          schema:
            $ref: '#/definitions/backend_pkg_common_response.APIResponse'
        "500":
          description: 服务器内部错误
          schema:
            $ref: '#/definitions/backend_pkg_common_response.APIResponse'
      security:
      - ApiKeyAuth: []
      summary: 激活用户
      tags:
      - 用户管理
  /users/{user_id}/deactivate:
    post:
      consumes:
      - application/json
      description: 禁用指定的用户账户
      parameters:
      - description: 用户ID
        in: path
        name: user_id
        required: true
        type: string
      - description: 禁用请求
        in: body
        name: request
        required: true
        schema:
          $ref: '#/definitions/backend_internal_application_usecase_user.DeactivateUserRequest'
      produces:
      - application/json
      responses:
        "200":
          description: 禁用成功
          schema:
            allOf:
            - $ref: '#/definitions/backend_pkg_common_response.APIResponse'
            - properties:
                data:
                  $ref: '#/definitions/backend_internal_application_command_model.DeactivateUserResult'
              type: object
        "400":
          description: 无效的请求体
          schema:
            $ref: '#/definitions/backend_pkg_common_response.APIResponse'
        "404":
          description: 用户不存在
          schema:
            $ref: '#/definitions/backend_pkg_common_response.APIResponse'
        "500":
          description: 服务器内部错误
          schema:
            $ref: '#/definitions/backend_pkg_common_response.APIResponse'
      security:
      - ApiKeyAuth: []
      summary: 禁用用户
      tags:
      - 用户管理
  /users/{user_id}/profile:
    get:
      consumes:
      - application/json
      description: 获取指定用户的详细资料信息
      parameters:
      - description: 用户ID
        in: path
        name: user_id
        required: true
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: 获取成功
          schema:
            allOf:
            - $ref: '#/definitions/backend_pkg_common_response.APIResponse'
            - properties:
                data:
                  $ref: '#/definitions/backend_internal_application_query_model.UserQueryResult'
              type: object
        "400":
          description: 无效的请求参数
          schema:
            $ref: '#/definitions/backend_pkg_common_response.APIResponse'
        "404":
          description: 用户不存在
          schema:
            $ref: '#/definitions/backend_pkg_common_response.APIResponse'
        "500":
          description: 服务器内部错误
          schema:
            $ref: '#/definitions/backend_pkg_common_response.APIResponse'
      security:
      - ApiKeyAuth: []
      summary: 获取用户资料
      tags:
      - 用户管理
    put:
      consumes:
      - application/json
      description: 更新用户的个人资料信息
      parameters:
      - description: 用户ID
        in: path
        name: user_id
        required: true
        type: string
      - description: 更新请求
        in: body
        name: request
        required: true
        schema:
          $ref: '#/definitions/backend_internal_application_usecase_user.UpdateUserProfileRequest'
      produces:
      - application/json
      responses:
        "200":
          description: 更新成功
          schema:
            allOf:
            - $ref: '#/definitions/backend_pkg_common_response.APIResponse'
            - properties:
                data:
                  $ref: '#/definitions/backend_internal_application_command_model.UpdateUserResult'
              type: object
        "400":
          description: 无效的请求体
          schema:
            $ref: '#/definitions/backend_pkg_common_response.APIResponse'
        "404":
          description: 用户不存在
          schema:
            $ref: '#/definitions/backend_pkg_common_response.APIResponse'
        "500":
          description: 服务器内部错误
          schema:
            $ref: '#/definitions/backend_pkg_common_response.APIResponse'
      security:
      - ApiKeyAuth: []
      summary: 更新用户资料
      tags:
      - 用户管理
  /users/register:
    post:
      consumes:
      - application/json
      description: 注册新用户账户
      parameters:
      - description: 注册请求
        in: body
        name: request
        required: true
        schema:
          $ref: '#/definitions/backend_internal_application_usecase_user.RegisterUserRequest'
      produces:
      - application/json
      responses:
        "201":
          description: 注册成功
          schema:
            allOf:
            - $ref: '#/definitions/backend_pkg_common_response.APIResponse'
            - properties:
                data:
                  $ref: '#/definitions/backend_internal_application_usecase_user.RegisterUserResponse'
              type: object
        "400":
          description: 无效的请求体
          schema:
            $ref: '#/definitions/backend_pkg_common_response.APIResponse'
        "409":
          description: 用户已存在
          schema:
            $ref: '#/definitions/backend_pkg_common_response.APIResponse'
        "500":
          description: 服务器内部错误
          schema:
            $ref: '#/definitions/backend_pkg_common_response.APIResponse'
      summary: 用户注册
      tags:
      - 用户管理
securityDefinitions:
  ApiKeyAuth:
    description: Bearer JWT令牌认证，格式：Bearer {token}
    in: header
    name: Authorization
    type: apiKey
swagger: "2.0"
tags:
- description: 用户认证和授权相关接口
  name: 认证
- description: 用户信息管理接口
  name: 用户管理
- description: 多租户管理接口
  name: 租户管理
- description: 产品信息管理接口
  name: 产品管理
- description: 订单处理相关接口
  name: 订单管理
- description: 采购流程管理接口
  name: 采购管理
- description: 库存监控和管理接口
  name: 库存管理
- description: 财务数据和报表接口
  name: 财务管理
- description: 系统配置和监控接口
  name: 系统管理
