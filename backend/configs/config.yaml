# 九翼跨境电商ERP系统配置文件
# 使用标准YAML格式，包含默认值
# 通过环境变量覆盖配置，viper自动绑定

# 应用环境
env: development

# 服务器配置
server:
  host: 0.0.0.0
  port: 8080
  read_timeout: 30s
  write_timeout: 30s
  idle_timeout: 60s
  grpc:
    enable: false
    port: 9090

# 数据库配置
database:
  driver: postgres
  host: localhost
  port: 5432
  database: erp_development
  username: postgres
  password: postgres
  ssl_mode: disable
  max_open_conns: 25
  max_idle_conns: 5
  conn_max_lifetime: 5m
  conn_max_idle_time: 30m
  # 迁移配置
  migration:
    auto_migrate: true              # 开发环境自动迁移
    migrations_dir: migrations/postgres
    fail_on_error: true             # 迁移失败时停止应用启动
    log_migrations: true            # 记录迁移日志

# Redis配置
redis:
  host: localhost
  port: 6379
  password: ""
  db: 0
  pool_size: 10
  
  # 连接池高级配置
  min_idle_conns: 2
  max_conn_age: 30m
  pool_timeout: 4s
  idle_timeout: 5m
  idle_check_freq: 1m
  
  # 操作超时配置
  dial_timeout: 5s
  read_timeout: 3s
  write_timeout: 3s
  
  # 重试配置
  max_retries: 3
  min_retry_backoff: 8ms
  max_retry_backoff: 512ms

# 消息队列配置
message_queue:
  provider: redis
  redis:
    host: localhost
    port: 6379
    password: ""
    db: 1
    pool_size: 10
    max_retries: 3
    retry_delay: 1s

# 日志配置
logger:
  level: info
  format: json
  output: stdout
  file: logs/app.log
  max_size: 100
  max_age: 30
  max_backups: 10
  compress: true

  # 性能阈值配置
  thresholds:
    slow_request: 2s          # 慢请求阈值
    slow_database: 100ms      # 慢数据库查询阈值
    slow_cache: 50ms          # 慢缓存操作阈值
    slow_external: 5s         # 慢外部调用阈值

  # 采样配置
  sampling:
    enable: true
    debug_rate: 1.0           # Debug日志采样率
    info_rate: 1.0            # Info日志采样率
    warn_rate: 1.0            # Warn日志采样率
    error_rate: 1.0           # Error日志采样率

  # 结构化日志配置
  structured:
    enable: true
    include_caller: true      # 包含调用者信息
    include_stacktrace: true  # 包含堆栈信息（错误级别）
    max_field_length: 1000    # 最大字段长度

  # 敏感信息过滤
  sensitive_fields:
    - password
    - secret
    - token
    - key
    - authorization

# 认证配置
auth:
  # JWT配置
  jwt:
    secret_key: development_jwt_secret_key_change_in_production_very_long_secret
    access_token_duration: 24h
    refresh_token_duration: 168h
    issuer: 9-wings-erp

  # Casbin配置
  casbin:
    model_path: configs/casbin_model.conf
    table_name: casbin_rules

  # CORS配置
  cors:
    allowed_origins: ["*"]
    allowed_methods: ["GET", "POST", "PUT", "DELETE", "OPTIONS"]
    allowed_headers: ["*"]

  # 限流配置
  rate_limit:
    enable: true
    rps: 100
    burst: 200

# 监控配置
monitoring:
  metrics:
    enable: true
    path: /metrics
    port: 9090

  tracing:
    enable: true                                    # 启用追踪
    endpoint: http://localhost:14268/api/traces
    service: 9-wings-erp
    environment: development
    version: 1.0.0
    sampling_rate: 1.0                             # 采样率

  health:
    enable: true
    path: /health

  # 告警配置
  alerting:
    enable: true

    # 错误率告警
    error_rate:
      threshold: 0.05                              # 5%错误率阈值
      window: 5m                                   # 时间窗口

    # 响应时间告警
    response_time:
      p95_threshold: 2s                            # P95响应时间阈值
      p99_threshold: 5s                            # P99响应时间阈值
      window: 5m

    # 数据库性能告警
    database:
      slow_query_threshold: 100ms                  # 慢查询阈值
      connection_pool_threshold: 0.8               # 连接池使用率阈值

    # 缓存性能告警
    cache:
      hit_rate_threshold: 0.8                      # 缓存命中率阈值
      response_time_threshold: 50ms                # 缓存响应时间阈值

    # 外部服务告警
    external:
      timeout_threshold: 10s                       # 超时阈值
      error_rate_threshold: 0.1                    # 外部服务错误率阈值

  # 日志聚合配置
  log_aggregation:
    enable: true
    buffer_size: 1000                              # 缓冲区大小
    flush_interval: 10s                            # 刷新间隔

    # 错误聚合
    error_aggregation:
      enable: true
      window: 1m                                   # 聚合窗口
      max_errors_per_window: 100                   # 每个窗口最大错误数

    # 性能指标聚合
    performance_aggregation:
      enable: true
      window: 30s
      percentiles: [50, 90, 95, 99]               # 百分位数

# 业务配置
business:
  # 雪花ID配置
  snowflake:
    machine_id: 1

  # 文件上传配置
  upload:
    max_size: 10485760
    allowed_types: ["jpg", "jpeg", "png", "gif", "pdf", "doc", "docx", "xls", "xlsx"]
    path: uploads

  # 缓存配置
  cache:
    default_ttl: 1h
    max_keys: 10000
    
    # 分域缓存策略
    strategies:
      user_profile:     # 用户档案缓存
        ttl: 2h
        max_size: 1000
        enabled: true
        compress: false
      product_info:     # 商品信息缓存
        ttl: 30m
        max_size: 5000
        enabled: true
        compress: true
      session:          # 会话缓存
        ttl: 24h
        max_size: 2000
        enabled: true
        compress: false
      inventory:        # 库存缓存
        ttl: 5m
        max_size: 3000
        enabled: true
        compress: false
      order:           # 订单缓存
        ttl: 15m
        max_size: 2000
        enabled: true
        compress: true
    
    # Redis缓存配置
    redis:
      serialization: json      # json | msgpack | protobuf
      compression: false       # 全局压缩开关
      key_prefix: erp         # 键前缀
      key_separator: ":"      # 键分隔符
      
      # 批量操作配置
      batch_size: 100
      batch_timeout: 5s
      
      # 故障转移配置
      failover_enabled: true
      failover_timeout: 3s
      circuit_breaker_enabled: true
    
      # 监控配置
  metrics:
    enabled: true
    collect_interval: 30s
    hit_rate_window: 5m
    slow_query_threshold: 100ms

  # UUID生成器配置
  uuid:
    # 根命名空间（九翼ERP系统的唯一标识）
    root_namespace: "com.9wings.erp"
    # 是否启用语义化UUID（包含业务含义）
    enable_semantic: true
    # 语义化UUID失败时是否降级到随机UUID
    fallback_to_random: true

  # ID生成器配置
  id_generator:
    # 默认ID领域
    default_domain: "generic"
    # 序列号默认前缀
    default_prefix: "SEQ"
    # 序列号默认长度
    default_length: 8
    # 是否用零填充序列号
    pad_with_zeros: true
    # 是否启用ID缓存
    enable_cache: true
    # 是否启用ID验证
    validation_enabled: true
    # 是否启用指标统计
    metrics_enabled: false
    # 缓存大小
    cache_size: 10000
    # 缓存TTL（秒）
    cache_ttl: 300
    # 是否启用分布式缓存
    enable_distributed_cache: false
    
    # 性能和容错配置
    fallback_to_v4: true        # 失败时降级到UUID v4
    enable_namespace_cache: true # 启用命名空间缓存
    
    # 领域策略配置
    strategies:
      user:                     # 用户领域
        required_attrs: ["tenant_id", "key"]
        optional_attrs: ["username", "email", "phone"]
        attribute_order: ["username", "tenant_id", "key"]
        
      product:                  # 商品领域
        required_attrs: ["tenant_id", "key"]
        optional_attrs: ["sku", "category", "brand", "model"]
        attribute_order: ["sku", "category", "brand", "tenant_id", "key"]
        
      order:                    # 订单领域
        required_attrs: ["tenant_id", "key"]
        optional_attrs: ["order_number", "channel", "payment_method"]
        attribute_order: ["order_number", "channel", "tenant_id", "key"]
        
      inventory:                # 库存领域
        required_attrs: ["tenant_id", "key"]
        optional_attrs: ["warehouse_code", "location", "batch_number"]
        attribute_order: ["warehouse_code", "location", "tenant_id", "key"]
        
      finance:                  # 财务领域
        required_attrs: ["tenant_id", "key"]
        optional_attrs: ["account_code", "currency", "type"]
        attribute_order: ["account_code", "currency", "tenant_id", "key"]
        
      purchase:                 # 采购领域
        required_attrs: ["tenant_id", "key"]
        optional_attrs: ["supplier_code", "po_number", "category"]
        attribute_order: ["po_number", "supplier_code", "tenant_id", "key"]
        
      tenant:                   # 租户领域
        required_attrs: ["key"]
        optional_attrs: ["company_code", "region", "industry"]
        attribute_order: ["company_code", "region", "key"]
        
      platform:                 # 平台集成领域
        required_attrs: ["tenant_id", "key"]
        optional_attrs: ["platform_type", "store_id", "channel"]
        attribute_order: ["platform_type", "store_id", "tenant_id", "key"]

  