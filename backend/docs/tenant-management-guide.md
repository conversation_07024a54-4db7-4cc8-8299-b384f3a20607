# 租户管理模块使用指南

## 概述

九翼跨境电商ERP系统的租户管理模块提供了完整的多租户SaaS架构支持，包括租户生命周期管理、订阅管理、配额控制等核心功能。

## 架构特性

### 🏗️ DDD分层架构
- **领域层**: 纯业务逻辑，包含租户、订阅、配额实体和业务规则
- **应用层**: 用例编排，提供CQRS命令查询分离
- **适配器层**: HTTP接口和数据库持久化实现
- **基础设施层**: 技术组件支持

### 🔐 多租户数据隔离
- 行级数据隔离，每个租户数据完全独立
- 租户上下文自动注入，确保数据安全
- 支持租户级别的个性化配置

## 核心功能

### 1. 租户管理

**租户CRUD操作**:
- `POST /api/v1/tenants` - 创建租户
- `GET /api/v1/tenants` - 查询租户列表
- `GET /api/v1/tenants/{id}` - 获取租户详情
- `PUT /api/v1/tenants/{id}` - 更新租户信息
- `DELETE /api/v1/tenants/{id}` - 删除租户

**租户状态管理**:
- `POST /api/v1/tenants/{id}/activate` - 激活租户
- `POST /api/v1/tenants/{id}/suspend` - 暂停租户
- `POST /api/v1/tenants/{id}/deactivate` - 停用租户

**租户查询和统计**:
- `GET /api/v1/tenants/search` - 高级搜索
- `GET /api/v1/tenants/statistics` - 租户统计数据
- `GET /api/v1/tenants/active` - 活跃租户列表

### 2. 订阅管理

**订阅生命周期**:
- `POST /api/v1/subscriptions` - 创建订阅
- `GET /api/v1/subscriptions` - 查询订阅列表
- `PUT /api/v1/subscriptions/{id}` - 更新订阅
- `POST /api/v1/subscriptions/{id}/renew` - 续费订阅
- `POST /api/v1/subscriptions/{id}/cancel` - 取消订阅

**订阅查询**:
- `GET /api/v1/subscriptions/expiring` - 即将到期订阅
- `GET /api/v1/subscriptions/expired` - 已过期订阅
- `GET /api/v1/subscriptions/trial` - 试用订阅
- `GET /api/v1/subscriptions/statistics` - 订阅统计

### 3. 配额管理

**配额控制**:
- `GET /api/v1/tenants/{id}/quota` - 获取租户配额
- `PUT /api/v1/tenants/{id}/quota` - 更新配额限制
- `POST /api/v1/tenants/{id}/quota/reset` - 重置配额使用量

**配额监控**:
- `GET /api/v1/tenants/{id}/quota/usage` - 配额使用情况
- `GET /api/v1/tenants/quota/exceeded` - 超限租户列表
- `POST /api/v1/tenants/quota/batch-reset` - 批量重置配额

## 数据模型

### 租户实体 (Tenant)
```go
type Tenant struct {
    BusinessID   string           // 业务ID
    Name         string           // 租户名称
    Domain       string           // 租户域名
    DisplayName  string           // 显示名称
    Description  string           // 描述
    Type         TenantType       // 租户类型
    Status       TenantStatus     // 状态
    Industry     string           // 所属行业
    Country      string           // 国家
    Province     string           // 省份
    City         string           // 城市
    ContactEmail string           // 联系邮箱
    ContactPhone string           // 联系电话
    CreatedAt    time.Time        // 创建时间
    UpdatedAt    time.Time        // 更新时间
    DeletedAt    *gorm.DeletedAt  // 软删除
}
```

### 订阅实体 (TenantSubscription)
```go
type TenantSubscription struct {
    BusinessID      string                    // 业务ID
    TenantID        string                    // 租户ID
    PlanType        SubscriptionPlanType      // 订阅计划类型
    Status          SubscriptionStatus        // 订阅状态
    StartDate       time.Time                 // 开始日期
    EndDate         time.Time                 // 结束日期
    TrialEndDate    *time.Time               // 试用结束日期
    AutoRenew       bool                     // 自动续费
    RenewalPrice    decimal.Decimal          // 续费价格
    Currency        string                   // 货币
    PaymentMethod   string                   // 支付方式
    CreatedAt       time.Time                // 创建时间
    UpdatedAt       time.Time                // 更新时间
}
```

### 配额实体 (TenantQuota)
```go
type TenantQuota struct {
    BusinessID       string          // 业务ID
    TenantID         string          // 租户ID
    UserLimit        int             // 用户数限制
    UserUsed         int             // 已使用用户数
    StorageLimit     int64           // 存储限制(MB)
    StorageUsed      int64           // 已使用存储(MB)
    APILimitMonth    int             // 月API调用限制
    APIUsedMonth     int             // 月已使用API调用
    CreatedAt        time.Time       // 创建时间
    UpdatedAt        time.Time       // 更新时间
}
```

## 使用示例

### 创建租户
```bash
curl -X POST "http://localhost:8080/api/v1/tenants" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer <token>" \
  -d '{
    "name": "测试公司",
    "domain": "test-company",
    "display_name": "测试公司有限公司",
    "description": "这是一个测试租户",
    "type": "enterprise",
    "industry": "电商",
    "country": "中国",
    "province": "广东省",
    "city": "深圳市",
    "contact_email": "<EMAIL>",
    "contact_phone": "13800138000"
  }'
```

### 查询租户列表
```bash
curl -X GET "http://localhost:8080/api/v1/tenants?page=1&page_size=10&status=active" \
  -H "Authorization: Bearer <token>"
```

### 更新租户配额
```bash
curl -X PUT "http://localhost:8080/api/v1/tenants/{tenant_id}/quota" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer <token>" \
  -d '{
    "user_limit": 100,
    "storage_limit": 10240,
    "api_limit_month": 100000
  }'
```

## 权限控制

### 租户管理权限
- `tenant:create` - 创建租户权限
- `tenant:read` - 查看租户权限
- `tenant:update` - 更新租户权限
- `tenant:delete` - 删除租户权限
- `tenant:manage` - 租户管理权限（包含所有操作）

### 订阅管理权限
- `subscription:create` - 创建订阅权限
- `subscription:read` - 查看订阅权限
- `subscription:update` - 更新订阅权限
- `subscription:cancel` - 取消订阅权限

### 配额管理权限
- `quota:read` - 查看配额权限
- `quota:update` - 更新配额权限
- `quota:reset` - 重置配额权限

## 错误处理

### 常见错误码
- `3001` - 租户不存在
- `3002` - 租户名称已存在
- `3003` - 租户域名已存在
- `3004` - 租户状态无效
- `3010` - 订阅不存在
- `3011` - 订阅已过期
- `3020` - 配额超限

### 错误响应格式
```json
{
  "code": 3001,
  "message": "租户不存在",
  "details": {
    "tenant_id": "tenant_123"
  },
  "timestamp": "2024-01-01T12:00:00Z"
}
```

## 开发指南

### 添加新的租户功能
1. 在领域层定义业务逻辑
2. 在应用层编写用例
3. 在适配器层实现HTTP接口和数据库操作
4. 编写单元测试和集成测试

### 扩展租户属性
1. 更新实体定义
2. 创建数据库迁移脚本
3. 更新DTO和API接口
4. 更新相关测试

## 监控和运维

### 关键指标
- 活跃租户数量
- 新增租户趋势
- 配额使用率
- 订阅到期提醒
- 系统性能指标

### 日志监控
- 租户创建/更新/删除操作
- 配额超限告警
- 订阅到期提醒
- 异常操作审计

## 最佳实践

### 性能优化
- 使用数据库索引优化查询
- 实现缓存机制减少数据库访问
- 分页查询大量数据
- 异步处理耗时操作

### 安全考虑
- 严格的租户数据隔离
- 权限验证和授权检查
- 输入数据验证和清理
- 操作审计日志记录

### 可扩展性
- 模块化设计便于功能扩展
- 配置化的业务规则
- 插件化的支付和通知机制
- 微服务化的服务拆分

---

## 技术支持

如有问题或建议，请联系开发团队或查看项目文档。 