# 九翼ERP系统 Swagger文档规范

## 概述

本文档定义了九翼跨境电商ERP系统中Swagger API文档的标准规范，确保文档的一致性、完整性和可维护性。

## 1. 总体架构

### 1.1 文档生成方式
- **主要方式**: 使用 `swaggo/swag` 工具，通过Go代码注释自动生成
- **辅助方式**: 手动维护 `api/openapi/swagger.yaml` 作为补充和参考
- **输出目录**: `docs/` (自动生成的文档)

### 1.2 文件结构
```
├── docs/                    # 自动生成的Swagger文档
│   ├── docs.go             # 生成的Go文档代码
│   ├── swagger.json        # JSON格式文档
│   └── swagger.yaml        # YAML格式文档
├── api/openapi/            # 手动维护的OpenAPI规范
│   └── swagger.yaml        # 基础模板和复杂schema定义
└── internal/adapters/http/handler/  # 包含Swagger注释的Handler
```

## 2. 注释规范

### 2.1 全局注释 (main.go)

在 `cmd/api/main.go` 中定义全局信息：

```go
// @title 九翼跨境电商ERP系统API
// @version 1.0
// @description 基于DDD架构的跨境电商ERP管理系统
// @termsOfService http://swagger.io/terms/

// @contact.name API支持团队
// @contact.url http://www.swagger.io/support
// @contact.email <EMAIL>

// @license.name MIT
// @license.url https://opensource.org/licenses/MIT

// @host localhost:8080
// @BasePath /api/v1

// @securityDefinitions.apikey ApiKeyAuth
// @in header
// @name Authorization
// @description Bearer JWT令牌认证，格式：Bearer {token}
```

### 2.2 标签定义

```go
// @tag.name 认证
// @tag.description 用户认证和授权相关接口

// @tag.name 用户管理
// @tag.description 用户信息管理接口
```

### 2.3 Handler方法注释

#### 基本格式
```go
// @Summary 接口简要描述
// @Description 接口详细描述，可以使用多行
// @Tags 标签名称
// @Accept json
// @Produce json
// @Param 参数名 参数位置 参数类型 是否必需 "参数说明"
// @Success 状态码 {object} 响应类型 "成功描述"
// @Failure 状态码 {object} 响应类型 "失败描述"  
// @Security 安全模式名称
// @Router 路径 [方法]
```

#### 实际示例
```go
// @Summary 用户登录 - 阶段一
// @Description 认证用户并返回可访问的租户列表。
// @Tags 认证
// @Accept json
// @Produce json
// @Param request body security.LoginRequest true "登录凭据"
// @Success 200 {object} response.APIResponse{data=security.LoginResponse} "登录成功，返回租户列表"
// @Failure 400 {object} response.APIResponse "无效请求"
// @Failure 401 {object} response.APIResponse "无效凭据"
// @Router /auth/login [post]
func (h *AuthHandler) Login(c *gin.Context) {
    // ...
}
```

### 2.4 参数注释规范

#### 2.4.1 路径参数
```go
// @Param id path string true "用户ID"
```

#### 2.4.2 查询参数
```go
// @Param page query int false "页码" default(1) minimum(1)
// @Param size query int false "每页数量" default(10) minimum(1) maximum(100)
// @Param keyword query string false "搜索关键词"
```

#### 2.4.3 请求体参数
```go
// @Param request body dto.CreateUserRequest true "创建用户请求"
```

#### 2.4.4 头部参数
```go
// @Param X-Tenant-ID header string true "租户ID"
```

### 2.5 响应注释规范

#### 2.5.1 成功响应
```go
// @Success 200 {object} response.APIResponse{data=dto.UserResponse} "获取成功"
// @Success 201 {object} response.APIResponse{data=dto.UserResponse} "创建成功"
// @Success 204 "删除成功"
```

#### 2.5.2 错误响应
```go
// @Failure 400 {object} response.APIResponse "请求参数错误"
// @Failure 401 {object} response.APIResponse "未认证"
// @Failure 403 {object} response.APIResponse "权限不足"
// @Failure 404 {object} response.APIResponse "资源不存在"
// @Failure 500 {object} response.APIResponse "服务器内部错误"
```

### 2.6 数据模型注释

#### 2.6.1 结构体注释
```go
// User 用户信息
type User struct {
    // 用户ID
    ID string `json:"id" example:"123456789"`
    
    // 用户名
    // required: true
    // minLength: 3
    // maxLength: 20
    Username string `json:"username" validate:"required,min=3,max=20" example:"johnuser"`
    
    // 邮箱地址
    Email string `json:"email" validate:"required,email" example:"<EMAIL>"`
    
    // 创建时间
    CreatedAt time.Time `json:"created_at" example:"2023-01-01T00:00:00Z"`
    
    // 用户状态: active=激活, inactive=未激活, suspended=已暂停
    Status string `json:"status" enums:"active,inactive,suspended" example:"active"`
} // @name User
```

#### 2.6.2 枚举注释
```go
// UserStatus 用户状态枚举
// @Description 用户账户状态
type UserStatus string

const (
    // UserStatusActive 激活状态
    UserStatusActive UserStatus = "active"
    // UserStatusInactive 未激活状态  
    UserStatusInactive UserStatus = "inactive"
    // UserStatusSuspended 已暂停状态
    UserStatusSuspended UserStatus = "suspended"
)
```

## 3. 标准响应格式

### 3.1 统一响应结构
```go
// APIResponse 统一API响应格式
type APIResponse struct {
    // 请求是否成功
    Success bool `json:"success" example:"true"`
    
    // 响应消息
    Message string `json:"message" example:"操作成功"`
    
    // 响应数据
    Data interface{} `json:"data,omitempty"`
    
    // 错误代码（失败时返回）
    ErrorCode string `json:"error_code,omitempty" example:"INVALID_PARAMS"`
    
    // 响应时间戳
    Timestamp time.Time `json:"timestamp" example:"2023-01-01T00:00:00Z"`
} // @name APIResponse
```

### 3.2 分页响应格式
```go
// PaginatedResponse 分页响应格式  
type PaginatedResponse struct {
    // 数据列表
    Items interface{} `json:"items"`
    
    // 分页信息
    Pagination PaginationMeta `json:"pagination"`
} // @name PaginatedResponse

// PaginationMeta 分页元信息
type PaginationMeta struct {
    // 当前页码
    Page int `json:"page" example:"1"`
    
    // 每页数量  
    Size int `json:"size" example:"10"`
    
    // 总记录数
    Total int64 `json:"total" example:"100"`
    
    // 总页数
    TotalPages int `json:"total_pages" example:"10"`
    
    // 是否有下一页
    HasNext bool `json:"has_next" example:"true"`
    
    // 是否有上一页
    HasPrev bool `json:"has_prev" example:"false"`
} // @name PaginationMeta
```

## 4. 安全认证注释

### 4.1 JWT认证
```go
// @Security ApiKeyAuth
```

### 4.2 可选认证
```go
// @Security ApiKeyAuth || {}
```

## 5. 标签分类规范

| 标签名称 | 描述 | 使用场景 |
|---------|------|----------|
| 认证 | 用户认证和授权 | 登录、注销、权限验证 |
| 用户管理 | 用户信息管理 | 用户CRUD操作 |
| 租户管理 | 多租户管理 | 租户配置、切换 |
| 产品管理 | 产品信息管理 | 商品管理、分类 |
| 订单管理 | 订单处理 | 订单创建、查询、更新 |
| 库存管理 | 库存监控 | 库存查询、调整 |
| 财务管理 | 财务数据 | 财务报表、结算 |
| 采购管理 | 采购流程 | 采购单、供应商 |
| 系统管理 | 系统配置 | 系统设置、监控 |

## 6. 开发流程

### 6.1 编写流程
1. 在Handler方法上添加Swagger注释
2. 定义请求/响应的DTO结构体并添加注释  
3. 运行 `make swagger-gen` 生成文档
4. 通过 `http://localhost:8080/swagger/index.html` 查看效果
5. 验证文档准确性

### 6.2 更新流程
1. 修改Handler注释或DTO结构
2. 重新生成文档: `make swagger-gen`
3. 验证更新效果
4. 提交代码

### 6.3 质量检查
- 运行 `make swagger-validate` 验证文档格式
- 确保所有公开接口都有完整的注释
- 检查示例数据的准确性
- 验证认证要求的正确性

## 7. 最佳实践

### 7.1 注释编写
- 使用中文描述，简洁明了
- 提供具体的示例值
- 标注必需参数和可选参数
- 说明参数的取值范围和格式要求

### 7.2 数据模型
- 使用有意义的字段名
- 提供适当的验证规则
- 添加枚举值说明
- 包含示例数据

### 7.3 错误处理
- 定义标准的错误码
- 提供详细的错误描述
- 覆盖所有可能的错误场景

### 7.4 版本管理
- 使用语义化版本号
- 在重大变更时更新版本
- 保持向后兼容性

## 8. 常用命令

```bash
# 安装Swagger工具
make swagger-install

# 生成Swagger文档
make swagger-gen  

# 验证文档格式
make swagger-validate

# 启动文档服务
make swagger-serve

# 导出OpenAPI文件
make swagger-export

# 清理生成的文档
make swagger-clean
```

## 9. 故障排除

### 9.1 常见问题
1. **文档生成失败**: 检查Go代码语法和注释格式
2. **类型识别错误**: 确保import了相关包  
3. **路由不匹配**: 验证@Router路径与实际路由一致
4. **认证配置错误**: 检查@Security注释和securityDefinitions

### 9.2 调试技巧
- 使用 `swag init --debug` 查看详细信息
- 检查生成的 `docs.go` 文件内容
- 对比手动编写的OpenAPI规范文件

## 10. 扩展配置

### 10.1 自定义配置
```bash
# 指定输出目录
swag init -o ./custom-docs

# 指定解析深度
swag init --parseDepth 3

# 包含内部包
swag init --parseInternal

# 解析依赖包
swag init --parseDependency
```

### 10.2 CI/CD集成
```yaml
# .github/workflows/swagger.yml
- name: Generate Swagger Documentation
  run: |
    make swagger-install
    make swagger-gen
    make swagger-validate
``` 