# 错误处理最佳实践指南

## 📋 目录

- [概述](#概述)
- [错误处理架构](#错误处理架构)
- [使用指南](#使用指南)
- [最佳实践](#最佳实践)
- [常见错误类型](#常见错误类型)
- [示例代码](#示例代码)
- [故障排除](#故障排除)

## 🎯 概述

九翼跨境电商ERP系统采用统一的错误处理架构，基于DDD六边形架构原则，提供了完整的错误翻译、传播和响应机制。

### 核心特性

- ✅ **统一错误模型**：标准化的AppError结构
- ✅ **类型安全**：强类型错误码和错误类型
- ✅ **流式API**：链式调用的错误构建器
- ✅ **自动翻译**：数据库错误自动翻译为业务错误
- ✅ **HTTP集成**：错误自动转换为HTTP响应
- ✅ **业务语义化**：提供业务友好的错误创建函数

## 🏗️ 错误处理架构

### 架构层次

```
┌─────────────────────────────────────────┐
│           HTTP 响应层                    │
│  (pkg/common/middleware/error_middleware) │
├─────────────────────────────────────────┤
│         业务错误封装层                   │
│     (internal/shared/errors)            │
├─────────────────────────────────────────┤
│         核心错误模型层                   │
│      (pkg/common/errors)                │
├─────────────────────────────────────────┤
│        数据库错误适配层                  │
│    (pkg/adapters/database)              │
└─────────────────────────────────────────┘
```

### 错误流转过程

1. **数据库错误** → 数据库适配器翻译 → **AppError**
2. **业务逻辑错误** → 业务错误函数 → **AppError**
3. **AppError** → HTTP中间件 → **HTTP响应**

## 📖 使用指南

### 1. 在仓储层处理数据库错误

```go
import (
    "backend/pkg/adapters/database"
    sharedErrors "backend/internal/shared/errors"
)

func (r *UserRepository) FindByID(ctx context.Context, id string) (*User, error) {
    var user User
    err := r.db.WithContext(ctx).Where("id = ?", id).First(&user).Error
    if err != nil {
        // 使用数据库错误适配器自动翻译
        if database.IsNotFoundError(err) {
            return nil, sharedErrors.UserNotFound(id)
        }
        return nil, database.TranslateDBError(err)
    }
    return &user, nil
}

func (r *UserRepository) Create(ctx context.Context, user *User) error {
    if err := r.db.WithContext(ctx).Create(user).Error; err != nil {
        // 自动翻译数据库错误
        return database.TranslateDBError(err)
    }
    return nil
}
```

### 2. 在业务逻辑层创建业务错误

```go
import sharedErrors "backend/internal/shared/errors"

func (s *UserService) CreateUser(ctx context.Context, req *CreateUserRequest) error {
    // 验证业务规则
    if req.Email == "" {
        return sharedErrors.Validation("邮箱不能为空")
    }
    
    // 检查用户是否已存在
    existing, err := s.userRepo.FindByEmail(ctx, req.Email)
    if err != nil && !sharedErrors.IsNotFoundError(err) {
        return err // 传播数据库错误
    }
    if existing != nil {
        return sharedErrors.UserExists(req.Email)
    }
    
    // 创建用户
    user := NewUser(req.Email, req.Username)
    if err := s.userRepo.Create(ctx, user); err != nil {
        return sharedErrors.UserCreateFailed("数据库保存失败", err)
    }
    
    return nil
}
```

### 3. 在HTTP处理器中处理错误

```go
import (
    sharedErrors "backend/internal/shared/errors"
    "backend/pkg/common/response"
)

func (h *UserHandler) CreateUser(c *gin.Context) {
    var req CreateUserRequest
    if err := c.ShouldBindJSON(&req); err != nil {
        // 使用统一错误处理
        sharedErrors.HandleError(c, sharedErrors.Validation("请求参数无效"))
        return
    }
    
    err := h.userService.CreateUser(c.Request.Context(), &req)
    if err != nil {
        // 错误会被中间件自动处理
        sharedErrors.HandleError(c, err)
        return
    }
    
    // 成功响应
    response.Created(c, gin.H{"message": "用户创建成功"})
}
```

## 🎯 最佳实践

### 1. 错误创建原则

#### ✅ 推荐做法

```go
// 使用业务语义化的错误函数
return sharedErrors.UserNotFound(userID)
return sharedErrors.ProductOutOfStock(productID, requested, available)
return sharedErrors.InventoryInsufficient(productID, warehouseID, requested, available)

// 包含详细的上下文信息
return sharedErrors.OrderCreateFailed("库存不足", err)
```

#### ❌ 避免做法

```go
// 不要直接返回原始错误
return err

// 不要使用通用错误消息
return errors.New("操作失败")

// 不要丢失错误上下文
return sharedErrors.Internal("出错了")
```

### 2. 错误传播原则

#### ✅ 正确的错误传播

```go
func (s *OrderService) CreateOrder(ctx context.Context, req *CreateOrderRequest) error {
    // 检查商品库存
    product, err := s.productRepo.FindByID(ctx, req.ProductID)
    if err != nil {
        // 直接传播仓储层的错误（已经是AppError）
        return err
    }
    
    if product.Stock < req.Quantity {
        // 创建新的业务错误
        return sharedErrors.ProductOutOfStock(req.ProductID, req.Quantity, product.Stock)
    }
    
    // 创建订单
    order := NewOrder(req)
    if err := s.orderRepo.Create(ctx, order); err != nil {
        // 包装错误，提供更多上下文
        return sharedErrors.OrderCreateFailed("保存订单失败", err)
    }
    
    return nil
}
```

### 3. 错误检查原则

#### ✅ 使用类型安全的错误检查

```go
import (
    commonErrors "backend/pkg/common/errors"
    "backend/pkg/adapters/database"
)

// 检查错误类型
if appErr := commonErrors.As(err); appErr != nil {
    switch appErr.Type {
    case commonErrors.TypeNotFound:
        // 处理未找到错误
    case commonErrors.TypeConflict:
        // 处理冲突错误
    }
}

// 检查数据库错误类型
if database.IsNotFoundError(err) {
    // 处理记录未找到
}
if database.IsConstraintError(err) {
    // 处理约束违反
}
```

### 4. HTTP错误处理原则

#### ✅ 使用统一的错误处理中间件

```go
// 在路由器中注册错误中间件
router.Use(middleware.ErrorMiddleware(logger))

// 在处理器中使用HandleError
func (h *Handler) SomeAction(c *gin.Context) {
    err := h.service.DoSomething()
    if err != nil {
        sharedErrors.HandleError(c, err)
        return
    }
    response.Success(c, result)
}
```

## 📝 常见错误类型

### 业务错误

| 错误函数 | 使用场景 | HTTP状态码 |
|---------|---------|-----------|
| `UserNotFound(userID)` | 用户不存在 | 404 |
| `UserExists(email)` | 用户已存在 | 409 |
| `ProductOutOfStock(productID, requested, available)` | 商品库存不足 | 409 |
| `OrderNotFound(orderID)` | 订单不存在 | 404 |
| `InventoryInsufficient(productID, warehouseID, requested, available)` | 库存不足 | 409 |
| `TokenExpired()` | Token过期 | 401 |
| `Permission(message)` | 权限不足 | 403 |
| `Validation(message)` | 验证失败 | 400 |

### 数据库错误

| 数据库错误 | 翻译后的错误类型 | HTTP状态码 |
|-----------|----------------|-----------|
| `gorm.ErrRecordNotFound` | `TypeNotFound` | 404 |
| PostgreSQL唯一约束违反 | `TypeConflict` | 409 |
| PostgreSQL外键约束违反 | `TypeConflict` | 409 |
| PostgreSQL非空约束违反 | `TypeValidation` | 400 |
| 数据库连接错误 | `TypeExternal` | 503 |
| 数据库超时 | `TypeExternal` | 503 |

## 💡 示例代码

### 完整的用户服务示例

```go
package service

import (
    "context"
    
    "backend/internal/domain/user/entity"
    "backend/internal/domain/user/repository"
    sharedErrors "backend/internal/shared/errors"
    "backend/pkg/adapters/database"
)

type UserService struct {
    userRepo repository.UserRepository
}

func (s *UserService) GetUser(ctx context.Context, userID string) (*entity.User, error) {
    user, err := s.userRepo.FindByID(ctx, userID)
    if err != nil {
        // 仓储层已经处理了错误翻译，直接传播
        return nil, err
    }
    return user, nil
}

func (s *UserService) CreateUser(ctx context.Context, email, username string) (*entity.User, error) {
    // 业务验证
    if email == "" {
        return nil, sharedErrors.Validation("邮箱不能为空")
    }
    
    // 检查邮箱是否已存在
    existing, err := s.userRepo.FindByEmail(ctx, email)
    if err != nil {
        // 检查是否是"未找到"错误
        if appErr := commonErrors.As(err); appErr != nil && appErr.Type == commonErrors.TypeNotFound {
            // 用户不存在，可以继续创建
        } else {
            // 其他错误，直接返回
            return nil, err
        }
    } else {
        // 用户已存在
        return nil, sharedErrors.UserExists(email)
    }
    
    // 创建用户
    user := entity.NewUser(email, username)
    if err := s.userRepo.Create(ctx, user); err != nil {
        return nil, sharedErrors.UserCreateFailed("保存用户失败", err)
    }
    
    return user, nil
}

func (s *UserService) UpdateUser(ctx context.Context, userID string, updates map[string]interface{}) error {
    // 检查用户是否存在
    user, err := s.userRepo.FindByID(ctx, userID)
    if err != nil {
        return err // 传播错误（可能是UserNotFound）
    }
    
    // 应用更新
    if email, ok := updates["email"].(string); ok && email != "" {
        // 检查新邮箱是否已被使用
        existing, err := s.userRepo.FindByEmail(ctx, email)
        if err == nil && existing.ID != user.ID {
            return sharedErrors.UserExists(email)
        }
        user.Email = email
    }
    
    // 保存更新
    if err := s.userRepo.Update(ctx, user); err != nil {
        return sharedErrors.UserUpdateFailed(userID, "保存更新失败", err)
    }
    
    return nil
}
```

## 🔧 故障排除

### 常见问题

#### 1. 错误没有被正确翻译

**问题**：数据库错误没有被翻译为AppError

**解决方案**：
```go
// 确保使用数据库错误适配器
import "backend/pkg/adapters/database"

// 替换
return commonErrors.Database(err)

// 为
return database.TranslateDBError(err)
```

#### 2. HTTP响应格式不统一

**问题**：错误响应格式不一致

**解决方案**：
```go
// 确保注册了错误中间件
router.Use(middleware.ErrorMiddleware(logger))

// 使用统一的错误处理
sharedErrors.HandleError(c, err)
```

#### 3. 错误信息丢失

**问题**：错误传播过程中丢失了原始错误信息

**解决方案**：
```go
// 使用Wrap方法保留原始错误
return sharedErrors.OrderCreateFailed("创建订单失败", originalErr)

// 或者直接传播已经包装好的错误
return err
```

### 调试技巧

1. **查看错误详细信息**：
```go
if appErr := commonErrors.As(err); appErr != nil {
    log.Printf("错误类型: %s, 错误码: %s, 详细信息: %+v", 
        appErr.Type, appErr.Code, appErr.Details)
}
```

2. **检查错误堆栈**：
```go
// AppError自动包含堆栈信息
fmt.Printf("错误堆栈: %s", appErr.Stack())
```

3. **使用TraceID追踪错误**：
```go
// 错误响应中包含TraceID，便于日志关联
// 在日志中搜索相同的TraceID可以追踪完整的请求流程
```

---

## 📚 相关文档

- [DDD架构指南](./ddd-architecture.md)
- [API设计规范](./api-design-guidelines.md)
- [测试最佳实践](./testing-best-practices.md)

---

**更新时间**：2024-12-19  
**版本**：v1.0.0
