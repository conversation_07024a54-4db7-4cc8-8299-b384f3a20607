---
description: 
globs: 
alwaysApply: true
---
# 九翼跨境电商ERP系统 - 项目概览

## 🚀 项目基本信息

**项目名称**：九翼跨境电商ERP系统 (Nine Wings Cross-Border E-Commerce ERP)  
**技术栈**：Go 1.21+ / PostgreSQL 17+ / Redis 7+ / Docker  
**架构模式**：DDD六边形架构 + CQRS + 多租户SaaS  
**开发方法**：测试驱动开发(TDD) + 持续集成/部署(CI/CD)

## 📋 核心业务领域

### 🛡️ 安全与认证领域
**目标**：提供安全可靠的多阶段认证和精细化权限控制

**核心功能**：
- **两阶段登录流程**：用户身份验证 → 租户选择 → 正式Token
- **JWT Token管理**：Access/Refresh/PreAuth三种Token类型
- **RBAC权限控制**：基于Casbin的细粒度权限管理
- **多租户数据隔离**：行级数据隔离保证数据安全

**关键文件**：
- [认证处理器](mdc:internal/adapters/http/handler/auth_handler.go) - HTTP接口层
- [认证用例](mdc:internal/application/usecase/security/auth_usecase.go) - 业务逻辑编排
- [JWT管理器](mdc:pkg/infrastructure/security/jwt/jwt_manager.go) - Token生成验证
- [权限中间件](mdc:internal/adapters/http/middleware/permission_middleware.go) - 请求权限拦截

### 👥 用户管理领域
**目标**：完整的用户生命周期管理和组织架构支持

**核心功能**：
- **用户注册与认证**：邮箱注册、密码管理、身份验证
- **用户资料管理**：个人信息、头像、偏好设置
- **角色权限分配**：用户角色绑定、权限继承
- **多租户关联**：用户-租户关系管理

**关键文件**：
- [用户实体](mdc:internal/domain/user/entity/user.go) - 用户聚合根
- [用户仓储](mdc:internal/domain/user/repository/user_repository.go) - 数据访问接口
- [用户服务](mdc:internal/domain/user/service) - 领域业务逻辑
- [用户处理器](mdc:internal/adapters/http/handler) - HTTP接口

### 🏢 租户管理领域
**目标**：支持多租户SaaS架构的企业组织管理

**核心功能**：
- **租户创建与配置**：企业注册、基础配置
- **数据隔离策略**：确保租户间数据完全隔离
- **用户租户关联**：管理用户在不同租户中的权限
- **租户级别配置**：个性化业务配置

**关键文件**：
- [租户实体](mdc:internal/domain/tenant/entity/tenant.go) - 租户聚合根
- [租户上下文](mdc:internal/shared/context/tenant.go) - 租户上下文管理

### 📦 商品管理领域
**目标**：跨境电商商品全生命周期管理

**核心功能**：
- **商品信息管理**：基础信息、SKU、分类、属性
- **多语言商品描述**：支持不同市场的本地化
- **商品图片管理**：多媒体资源管理
- **价格体系管理**：多币种、分级定价

**关键文件**：
- [商品领域](mdc:internal/domain/product) - 商品业务逻辑

### 📋 订单管理领域
**目标**：完整的订单处理流程和状态管理

**核心功能**：
- **订单生命周期**：创建、支付、发货、配送、完成
- **订单状态管理**：状态机模式管理订单流转
- **支付集成**：多种支付方式集成
- **物流跟踪**：订单配送状态跟踪

**关键文件**：
- [订单领域](mdc:internal/domain/order) - 订单业务逻辑

### 📊 库存管理领域
**目标**：精确的库存跟踪和智能补货

**核心功能**：
- **实时库存跟踪**：准确的库存数量管理
- **库存预警**：低库存、过期商品告警
- **库存调拨**：仓库间货物转移
- **盘点管理**：定期库存盘点

**关键文件**：
- [库存领域](mdc:internal/domain/inventory) - 库存业务逻辑

### 💰 财务管理领域
**目标**：完整的财务核算和多币种支持

**核心功能**：
- **成本核算**：商品成本、运营成本计算
- **多币种支持**：汇率管理、币种转换
- **财务报表**：收入、支出、利润分析
- **税务管理**：跨境税务处理

**关键文件**：
- [财务领域](mdc:internal/domain/finance) - 财务业务逻辑

## 🏗️ 技术架构

### 分层架构设计

**领域层 (Domain)**：[internal/domain/](mdc:internal/domain)
- 纯业务逻辑，不依赖任何外部技术
- 实体、值对象、领域服务、业务规约
- 仓储接口定义

**应用层 (Application)**：[internal/application/](mdc:internal/application)
- 用例编排和业务流程协调
- CQRS命令查询分离
- DTO数据传输对象

**适配器层 (Adapters)**：[internal/adapters/](mdc:internal/adapters)
- HTTP接口适配器
- 数据库持久化适配器
- 外部服务集成适配器
- 消息队列适配器

**基础设施层 (Infrastructure)**：[pkg/infrastructure/](mdc:pkg/infrastructure)
- 技术组件和工具
- 配置管理、数据库连接、缓存管理
- JWT、权限管理、日志记录

### 核心技术组件

**Web框架**：Gin HTTP框架
- [路由配置](mdc:internal/adapters/http/router/router.go)
- [中间件](mdc:internal/adapters/http/middleware) - 认证、权限、日志、追踪

**数据存储**：
- **PostgreSQL**：[数据库管理](mdc:pkg/infrastructure/database) - 主要业务数据
- **Redis**：[缓存管理](mdc:pkg/infrastructure/cache) - 缓存、会话、消息队列

**配置管理**：
- [配置加载器](mdc:pkg/infrastructure/config) - Viper + 环境变量
- [主配置文件](mdc:configs/config.yaml) - YAML格式配置

**依赖注入**：
- [Wire框架](mdc:internal/infrastructure/di) - 编译时依赖注入
- [提供者配置](mdc:internal/infrastructure/di/provider) - 服务提供者

**消息队列**：
- [Redis消息队列](mdc:pkg/infrastructure/mq) - 基于Redis Streams
- 支持普通队列、发布订阅、延迟队列

## 🛠️ 开发工具链

### 代码质量保证

**静态分析工具**：
- **golangci-lint**：代码规范检查
- **gosec**：安全漏洞扫描
- **govulncheck**：依赖漏洞检查

**测试框架**：
- **单元测试**：[test/unit/](mdc:test/unit) - 80%测试覆盖率要求
- **集成测试**：[test/integration/](mdc:test/integration) - 真实环境测试
- **Mock生成**：[test/mock/](mdc:test/mock) - Mockery生成Mock对象

### 开发效率工具

**自动化构建**：[Makefile](mdc:Makefile)
- 60+ 个开发任务命令
- 开发环境、测试、构建、部署一键操作
- Docker容器化支持

**API文档**：
- **Swagger**：[OpenAPI规范](mdc:api/openapi/swagger.yaml)
- 自动生成API文档和测试界面
- 完整的请求响应示例

**数据库管理**：
- **迁移工具**：[migrate工具](mdc:cmd/migrate/main.go)
- **迁移脚本**：[PostgreSQL迁移](mdc:migrations/postgres)
- 版本化数据库Schema管理

## 🔐 安全特性

### 认证授权
- **多阶段认证**：分段式登录提升安全性
- **JWT Token管理**：支持Token撤销和黑名单
- **RBAC权限控制**：细粒度权限管理
- **多租户隔离**：数据完全隔离

### 数据安全
- **密码安全**：bcrypt + salt哈希
- **SQL注入防护**：GORM参数化查询
- **输入验证**：完整的数据验证机制
- **审计日志**：完整的操作审计

## 🚀 部署和运维

### 容器化部署
- **Docker**：[Dockerfile](mdc:docker/Dockerfile) 多阶段构建
- **Docker Compose**：[编排文件](mdc:docker) 多环境支持
- **健康检查**：应用健康状态监控

### 监控和调试
- **结构化日志**：Zap日志框架
- **性能监控**：pprof性能分析
- **指标监控**：Prometheus指标暴露
- **链路追踪**：HTTP请求追踪

## 📊 项目状态

### 当前实现状态
- ✅ **基础架构**：DDD分层架构完整实现
- ✅ **认证系统**：多阶段认证和JWT管理
- ✅ **权限系统**：Casbin RBAC权限控制
- ✅ **用户管理**：完整的用户CRUD操作
- ✅ **多租户**：租户数据隔离和上下文管理
- ✅ **测试体系**：单元测试和集成测试框架
- ✅ **开发工具**：完整的Makefile工具链

### 开发中功能
- 🔄 **商品管理**：商品信息和SKU管理
- 🔄 **订单处理**：订单生命周期管理
- 🔄 **库存管理**：库存跟踪和预警
- 🔄 **财务模块**：成本核算和报表

### 规划功能
- 📋 **平台集成**：Amazon、Shopify等平台对接
- 📋 **报表分析**：业务数据分析和可视化
- 📋 **消息通知**：邮件、短信、推送通知
- 📋 **API网关**：统一API管理和限流

## 🏃‍♂️ 快速开始

### 开发环境搭建
```bash
# 1. 克隆项目
git clone <repository>
cd nine-wings-erp/backend

# 2. 启动依赖服务
make dev-services

# 3. 数据库迁移
make migrate-up

# 4. 启动开发服务器
make dev-watch

# 5. 验证启动
curl http://localhost:8080/health
```

### 常用开发命令
```bash
make help          # 查看所有可用命令
make test           # 运行测试
make lint           # 代码检查
make quality-check  # 完整质量检查
make swagger        # 生成API文档
```

## 📚 相关文档

- [README.md](mdc:README.md) - 项目概述和快速开始
- [DDD架构规则](mdc:.cursor/rules/ddd-architecture.mdc) - 架构设计指南
- [代码规范](mdc:.cursor/rules/coding-standards.mdc) - 编码规范和质量标准
- [认证系统](mdc:.cursor/rules/authentication-system.mdc) - 认证架构详解
- [开发工作流](mdc:.cursor/rules/development-workflow.mdc) - 开发流程指南
