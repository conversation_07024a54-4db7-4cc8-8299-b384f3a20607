---
description: 
globs: internal/adapters/persistence/**/*.go,migrations/postgres/**/*.sql
alwaysApply: false
---
# 持久化与数据迁移规则

本文档统一规定了项目中关于数据持久化、数据库选型、GORM使用、JSONB字段设计以及数据库迁移的全部策略与规范。

---

## 一、 混合存储策略

### 1. 存储架构原则
根据业务数据的特性，我们采用混合存储架构，选择最适合的技术来存储不同类型的数据。

#### A. 关系型数据存储 (PostgreSQL)
- **实现路径**: `internal/adapters/persistence/relational`
- **技术栈**: PostgreSQL + GORM
- **适用场景**: 需要ACID事务、强一致性、复杂关联查询的核心业务数据。
- **涵盖领域**:
  - **订单 (Order)**: 交易核心，需强事务保证。
  - **采购 (Purchase)**: 与供应商和库存紧密关联。
  - **库存 (Inventory)**: 涉及精确的数量计算和事务。
  - **财务 (Finance)**: 对数据一致性要求极高。

#### B. 文档型数据存储 (PostgreSQL JSONB)
- **实现路径**: `internal/adapters/persistence/document`
- **技术栈**: PostgreSQL JSONB + GORM
- **适用场景**: 结构灵活、字段多变、需要进行文档式查询的非核心或扩展属性数据。此方案作为当前阶段的高效实现，未来可平滑迁移至MongoDB等专用文档数据库。
- **涵盖领域**:
  - **商品 (Product)**: 商品属性、规格灵活多变。
  - **租户 (Tenant)**: 租户的个性化配置，结构复杂。
  - **审计 (Audit)**: 审计日志，写入频繁，结构半固定。

#### C. 缓存层 (Redis)
- **实现路径**: `internal/adapters/persistence/cache/redis`
- **技术栈**: Redis + go-redis
- **适用场景**: 热点数据缓存（如商品信息、配置）、分布式锁、会话存储、消息队列等。

### 2. GORM实体定义规范
```go
// 关系型实体示例 (订单)
type Order struct {
    ID          int64           `gorm:"primaryKey;autoIncrement:false"`
    BusinessID  string          `gorm:"uniqueIndex;size:36"`
    TenantID    string          `gorm:"index;size:36"`
    OrderNo     string          `gorm:"uniqueIndex;size:100"`
    Status      int             `gorm:"index"`
    TotalAmount decimal.Decimal `gorm:"type:decimal(15,2)"`
    // ... 其他字段
}

// 文档型实体示例 (商品)
type Product struct {
    ID          int64          `gorm:"primaryKey;autoIncrement:false"`
    BusinessID  string         `gorm:"uniqueIndex;size:36"`
    TenantID    string         `gorm:"index;size:36"`
    SKU         string         `gorm:"uniqueIndex;size:100"`
    
    // JSONB字段
    Name        datatypes.JSON `gorm:"type:jsonb;not null"` // 多语言名称
    Attributes  datatypes.JSON `gorm:"type:jsonb"`        // 灵活属性
    
    // 数组字段
    Images      pq.StringArray `gorm:"type:text[]"`
    
    // ... 其他字段
}
```

---

## 二、 PostgreSQL JSONB 使用指南

### 1. JSONB 字段设计
- **优先使用JSONB**: 相比JSON，JSONB以二进制格式存储，支持索引，查询效率更高。
- **结构化定义**: 即使是灵活的JSONB，也应在代码中为其定义清晰的Go结构体，以便于序列化和反序列化，增强代码可读性。

```go
// 多语言名称结构
type ProductName struct {
    ZhCN string `json:"zh_cn,omitempty"`
    EnUS string `json:"en_us,omitempty"`
}

// 商品属性结构
type ProductAttributes struct {
    Color    string                 `json:"color,omitempty"`
    Size     string                 `json:"size,omitempty"`
    Weight   float64                `json:"weight,omitempty"`
    Custom   map[string]interface{} `json:"custom,omitempty"` // 完全自定义属性
}
```

### 2. JSONB 查询操作
- **路径操作符**: 使用 `->` 获取JSON对象字段，使用 `->>` 获取文本表示。
- **包含操作符**: 使用 `@>` 判断一个JSONB文档是否包含另一个指定的JSONB文档。这是查询特定属性或嵌套结构的最常用方法。

```go
// 按多语言名称查询
func (r *ProductRepository) FindByName(ctx context.Context, lang, keyword string) ([]*entity.Product, error) {
    var products []*entity.Product
    query := fmt.Sprintf("name->>'%s' ILIKE ?", lang) // 使用 ->> 获取文本
    err := r.db.WithContext(ctx).Where(query, "%"+keyword+"%").Find(&products).Error
    return products, err
}

// 按属性包含关系查询
func (r *ProductRepository) FindByAttributesContain(ctx context.Context, requiredAttrs map[string]interface{}) ([]*entity.Product, error) {
    var products []*entity.Product
    jsonBytes, err := json.Marshal(requiredAttrs)
    if err != nil {
        return nil, err
    }
    // 使用 @> 操作符
    err = r.db.WithContext(ctx).Where("attributes @> ?", string(jsonBytes)).Find(&products).Error
    return products, err
}
```

### 3. JSONB 索引优化
- **禁止使用GORM标签定义索引**: 所有索引必须通过Goose迁移文件创建，以确保对索引的精确控制和版本管理。
- **通用GIN索引**: 为整个JSONB字段创建GIN索引，以支持对其内部所有键值对的查询。
- **表达式索引**: 为JSONB中频繁查询的特定字段创建表达式索引，查询性能优于通用GIN索引。

```sql
-- 迁移文件中创建JSONB索引
-- +goose Up

-- 为整个JSONB字段创建通用GIN索引
CREATE INDEX IF NOT EXISTS idx_products_attributes_gin ON products USING GIN (attributes);

-- 为特定高频查询字段创建表达式索引
CREATE INDEX IF NOT EXISTS idx_products_color ON products USING GIN ((attributes->>'color'));
CREATE INDEX IF NOT EXISTS idx_products_name_zh_cn ON products USING GIN ((name->>'zh_cn'));
```

---

## 三、 Goose 数据库迁移管理

### 1. 迁移策略
- **禁用GORM AutoMigrate**: 严禁在生产环境中使用GORM的 `AutoMigrate` 功能，因为它不可控且危险。
- **SQL优先**: 所有数据库结构变更（DDL）都必须通过Goose管理的 `.sql` 迁移文件进行。
- **版本化管理**: 每个迁移文件代表数据库的一个版本，文件名以时间戳或序列号开头，确保执行顺序。

### 2. 迁移文件规范
- **目录结构**: 所有PostgreSQL迁移文件都存放在 `migrations/postgres/` 目录下。
- **Up/Down结构**: 每个迁移文件必须包含 `+goose Up` 和 `+goose Down` 两部分，确保迁移是可逆的。`Down` 部分的逻辑必须能精确地回滚 `Up` 部分的操作。
- **幂等性**: 迁移脚本应尽可能保持幂等性，例如使用 `CREATE TABLE IF NOT EXISTS`、`DROP INDEX IF EXISTS` 等语句，以避免重复执行时出错。
- **原子性**: 将相关的DDL操作（如创建表和它的索引）放在一个迁移文件中，以保证业务功能的原子性。

```sql
-- migrations/postgres/2025041500001_create_example_table.sql

-- +goose Up
-- 每个指令都应是独立的，并以分号结尾
CREATE TABLE IF NOT EXISTS example_table (
    id              BIGINT PRIMARY KEY,
    business_id     VARCHAR(36) NOT NULL UNIQUE,
    created_at      TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);
CREATE INDEX IF NOT EXISTS idx_example_business_id ON example_table(business_id);

-- +goose Down
DROP TABLE IF EXISTS example_table;
```

### 3. 迁移工具与流程
- **迁移CLI**: 项目提供位于 `cmd/migrate/` 的命令行工具来执行迁移操作。
- **Makefile集成**: 使用 `Makefile` 封装常用的迁移命令，如 `make migrate-up`, `make migrate-status`。
- **部署流程**: 在CI/CD流程中，部署新版本应用之前，必须先执行数据库迁移。
- **启动检查**: 应用在启动时可以加入一个检查步骤，验证数据库版本是否与当前代码版本兼容，如有未执行的迁移则发出警告或拒绝启动。
