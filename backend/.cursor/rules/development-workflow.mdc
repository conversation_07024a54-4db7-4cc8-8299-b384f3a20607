---
description: 
globs: test/**/*,internal/infrastructure/**/*
alwaysApply: false
---
# 开发工作流程指南

## 开发流程概述

九翼跨境电商ERP系统采用**测试驱动开发(TDD)**和**持续集成/持续部署(CI/CD)**的现代化开发工作流，确保代码质量和快速迭代。

## 🚀 快速开始开发

### 环境搭建

**1. 克隆和初始化**
```bash
git clone <repository>
cd nine-wings-erp/backend

# 安装依赖
go mod download

# 配置环境变量
cp .env.example .env.development
```

**2. 启动开发环境**
```bash
# 使用Docker快速启动基础服务
make dev-services

# 数据库迁移
make migrate-up

# 启动开发服务器（热重载）
make dev-watch
```

**3. 验证环境**
```bash
# 健康检查
curl http://localhost:8080/health

# API文档
open http://localhost:8080/swagger/index.html
```

## 🔧 开发工具集

### 必备工具

项目使用[Makefile](mdc:Makefile)提供完整的开发工具链：

**开发环境**
```bash
make dev          # 启动开发服务器
make dev-watch    # 启动热重载开发服务器  
make dev-services # 启动PostgreSQL和Redis服务
make dev-clean    # 清理开发环境
```

**代码生成**
```bash
make wire         # 生成依赖注入代码
make swagger      # 生成API文档
make mock         # 生成Mock对象
```

**代码质量**
```bash
make lint         # 静态代码分析
make fmt          # 代码格式化
make security     # 安全漏洞扫描
make quality      # 完整质量检查
```

**测试**
```bash
make test         # 运行所有测试
make test-unit    # 单元测试
make test-integration # 集成测试
make test-coverage    # 测试覆盖率报告
```

**数据库**
```bash
make migrate-up       # 执行数据库迁移
make migrate-down     # 回滚数据库迁移
make migrate-reset    # 重置数据库
make seed-data        # 导入测试数据
```

## 📁 项目结构导航

### 核心开发目录

**应用入口**：
- [API服务入口](mdc:cmd/api/main.go) - HTTP服务启动点
- [数据库迁移工具](mdc:cmd/migrate/main.go) - 数据库版本管理
- [后台任务处理器](mdc:cmd/worker) - 异步任务处理

**业务逻辑核心**：
- [领域层](mdc:internal/domain) - 业务核心，不依赖外部技术
- [应用层](mdc:internal/application) - 用例编排和业务流程
- [适配器层](mdc:internal/adapters) - 外部技术集成
- [共享组件](mdc:internal/shared) - 内部共享代码

**基础设施**：
- [公共组件](mdc:pkg) - 可复用的基础设施包
- [配置文件](mdc:configs) - 应用配置和环境变量
- [数据库迁移](mdc:migrations) - SQL迁移脚本

**测试和文档**：
- [测试代码](mdc:test) - 单元测试、集成测试、Mock对象
- [API文档](mdc:api) - OpenAPI规范和Protobuf定义
- [部署配置](mdc:docker) - Docker和K8s部署文件

## 🧪 测试策略

### 测试金字塔

**单元测试 (80%)**：
- 位置：[test/unit/](mdc:test/unit)
- 覆盖：领域实体、值对象、领域服务
- 要求：快速、隔离、聚焦业务逻辑
- 示例：[用户实体测试](mdc:test/unit/domain/user_entity_test.go)

**集成测试 (15%)**：
- 位置：[test/integration/](mdc:test/integration)
- 覆盖：数据库操作、缓存操作、外部API
- 要求：真实环境、端到端验证
- 示例：[认证集成测试](mdc:test/integration/auth_integration_test.go)

**端到端测试 (5%)**：
- 覆盖：完整业务流程
- 要求：模拟真实用户场景

### 测试工具和Mock

**Mock生成**：[generate.go](mdc:test/mock/generate.go)
```go
//go:generate mockery --name=UserRepository --output=. --filename=user_repository_mock.go
//go:generate mockery --name=JWTManager --output=. --filename=jwt_manager_mock.go
```

**测试工具**：[testutil](mdc:test/testutil)
- [数据库测试工具](mdc:test/testutil/db.go) - 测试数据库初始化
- [测试数据固件](mdc:test/testutil/fixtures.go) - 测试数据生成

### 测试规范

**测试文件命名**：
- 单元测试：`{source_file}_test.go`
- 集成测试：`{feature}_integration_test.go`
- Mock文件：`{interface}_mock.go`

**测试方法命名**：
```go
func TestUserEntity_UpdateProfile_Success(t *testing.T) {
    // Given - 准备测试数据
    // When - 执行测试操作  
    // Then - 验证测试结果
}
```

**测试覆盖率要求**：
- 整体覆盖率：≥ 80%
- 核心业务逻辑：≥ 90%
- 关键安全功能：100%

## 🔄 开发流程

### 功能开发标准流程

**1. 需求分析和设计**
- 明确业务需求和验收标准
- 识别聚合边界和领域事件
- 设计API接口和数据模型
- 制定测试计划

**2. 分支管理**
```bash
# 从develop分支创建功能分支
git checkout develop
git pull origin develop
git checkout -b feature/ERP-123-user-management

# 开发完成后合并到develop
git push origin feature/ERP-123-user-management
# 创建Pull Request
```

**3. 测试驱动开发(TDD)**
```bash
# 1. 编写失败的测试
make test-unit  # 应该失败

# 2. 编写最少代码使测试通过
make test-unit  # 应该通过

# 3. 重构代码
make test-unit  # 应该继续通过
```

**4. 代码质量检查**
```bash
# 提交前必须通过质量检查
make quality-check

# 包含以下检查：
# - golangci-lint 静态代码分析
# - gosec 安全漏洞扫描  
# - govulncheck 依赖漏洞检查
# - 测试覆盖率验证
```

**5. 提交和代码审查**
```bash
# 遵循约定式提交规范
git commit -m "feat(user): 添加用户资料更新功能

- 支持用户昵称和头像更新
- 添加输入验证和权限检查
- 完成单元测试和集成测试

Closes #123"
```

### 分支策略

**主要分支**：
- `main` - 生产环境分支，只接受来自release分支的合并
- `develop` - 开发集成分支，功能开发的目标分支
- `release/v1.x.x` - 发布准备分支，代码冻结和测试
- `hotfix/ERP-456-critical-fix` - 生产环境紧急修复

**功能分支**：
- `feature/ERP-123-feature-name` - 功能开发分支
- `bugfix/ERP-456-bug-description` - Bug修复分支
- `refactor/ERP-789-code-improvement` - 代码重构分支

## 🏗️ 构建和部署

### 本地构建

**应用构建**：
```bash
# 构建二进制文件
make build

# 交叉编译
make build-linux   # Linux版本
make build-windows # Windows版本
make build-mac     # macOS版本
```

**Docker构建**：
```bash
# 构建开发镜像
make docker-build-dev

# 构建生产镜像  
make docker-build-prod

# 运行容器
make docker-run
```

### 环境管理

**配置管理**：
- [开发环境](mdc:configs/config.yaml) - 默认配置
- [测试环境配置](mdc:.env.testing) - 测试专用配置
- [生产环境配置](mdc:.env.production) - 生产环境变量

**环境切换**：
```bash
# 开发环境
export APP_ENV=development
make dev

# 测试环境
export APP_ENV=testing  
make test-env

# 生产环境
export APP_ENV=production
make prod
```

### 数据库版本管理

**迁移管理**：
```bash
# 创建新迁移
make migrate-create name=add_user_profile_table

# 执行迁移
make migrate-up

# 回滚迁移
make migrate-down

# 查看迁移状态
make migrate-status
```

**迁移文件结构**：[migrations/postgres/](mdc:migrations/postgres)
```
migrations/postgres/
├── 00001_create_tenants_table.sql
├── 00002_create_user_tables.sql
├── 00003_create_user_auths_table.sql
└── ...
```

## 🔍 代码审查

### Pull Request规范

**PR检查清单**：
- [ ] 功能完整实现，通过所有测试
- [ ] 代码遵循项目规范和最佳实践
- [ ] 测试覆盖率达到要求 (≥80%)
- [ ] 无安全漏洞和代码异味
- [ ] API文档已更新
- [ ] 数据库迁移脚本已提供
- [ ] 配置文件已更新

**审查重点**：
1. **架构合规性** - 是否遵循DDD分层架构
2. **业务逻辑正确性** - 是否满足业务需求
3. **代码质量** - 可读性、可维护性、性能
4. **安全性** - 是否存在安全风险
5. **测试完整性** - 测试覆盖和质量

### 自动化检查

**CI/CD流水线**：
1. **代码检查** - 格式化、静态分析、安全扫描
2. **测试执行** - 单元测试、集成测试、覆盖率检查
3. **构建验证** - 应用构建、Docker镜像构建
4. **质量门禁** - 代码质量、测试覆盖率达标检查

**质量标准**：
- 测试覆盖率 ≥ 80%
- 无高危安全漏洞
- 代码复杂度 ≤ 10
- 技术债务等级 ≤ B

## 📊 监控和调试

### 开发调试

**日志配置**：
```yaml
# configs/config.yaml
log:
  level: "debug"    # 开发环境使用debug级别
  format: "console" # 控制台友好格式
  output: "stdout"  # 标准输出
```

**调试工具**：
- **pprof性能分析** - `http://localhost:8080/debug/pprof/`
- **健康检查** - `http://localhost:8080/health`
- **指标监控** - `http://localhost:8080/metrics`

### 性能优化

**数据库优化**：
- 查询性能分析：`EXPLAIN ANALYZE`
- 慢查询日志监控
- 索引优化建议

**应用性能**：
- 内存使用分析
- Goroutine泄露检测
- HTTP请求链路追踪

## 🚀 部署流程

### 容器化部署

**开发环境部署**：
```bash
# 启动完整开发环境
docker-compose -f docker/docker-compose.development.yml up -d

# 查看服务状态
make docker-status

# 查看日志
make docker-logs
```

**生产环境部署**：
```bash
# 构建生产镜像
make docker-build-prod

# 推送到镜像仓库
make docker-push

# 生产环境部署
kubectl apply -f k8s/
```

### 发布流程

**版本发布**：
1. 代码冻结和集成测试
2. 创建release分支
3. 执行完整回归测试
4. 生产环境部署
5. 监控和回滚准备

**回滚机制**：
- 数据库迁移回滚脚本
- 应用版本快速回退
- 配置文件版本管理

## 📚 文档维护

### 文档类型

**代码文档**：
- Go代码内联注释（中文）
- 包级别功能说明
- 复杂业务逻辑解释

**API文档**：
- [Swagger规范](mdc:api/openapi/swagger.yaml)
- 请求响应示例
- 错误码说明

**架构文档**：
- [README.md](mdc:README.md) - 项目概述和快速开始
- [架构设计文档](mdc:docs) - 详细技术文档
- [部署指南](mdc:docker/README.md) - 部署和运维手册

### 文档更新规范

**何时更新文档**：
- 新增API接口
- 修改业务流程  
- 调整架构设计
- 变更部署配置

**文档质量要求**：
- 准确性：与代码实现保持一致
- 完整性：覆盖主要功能和流程
- 可用性：提供清晰的使用指南
- 时效性：及时更新变更内容
