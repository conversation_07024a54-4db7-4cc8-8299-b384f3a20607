---
description: 
globs: 
alwaysApply: true
---
# 认证系统架构导航

## 认证系统概述

九翼跨境电商ERP系统实现了**多阶段认证**架构，支持分段式登录流程，具备完整的JWT Token管理、Redis缓存、RBAC权限控制和多租户数据隔离能力。

## 核心认证流程

### 🔐 两阶段登录流程

#### 第一阶段：用户身份验证
```
POST /api/v1/auth/login
{
  "identity_type": "email",
  "identifier": "<EMAIL>", 
  "credential": "password123"
}
```

**处理流程**：
1. [认证处理器](mdc:internal/adapters/http/handler/auth_handler.go) 接收登录请求
2. [认证用例](mdc:internal/application/usecase/security/auth_usecase.go) 验证用户身份
3. [用户仓储](mdc:internal/domain/user/repository/user_repository.go) 查询用户信息
4. 验证密码并生成**预认证Token**
5. 返回可用租户列表

#### 第二阶段：租户选择
```
POST /api/v1/auth/select-tenant
Authorization: Bearer <pre_auth_token>
{
  "tenant_id": "tenant_123"
}
```

**处理流程**：
1. 验证预认证Token的有效性
2. [租户仓储](mdc:internal/domain/tenant) 验证用户-租户关联关系
3. [Casbin权限管理](mdc:pkg/infrastructure/security/casbin) 加载用户权限
4. 生成正式的**Access Token**和**Refresh Token**
5. [Token缓存](mdc:internal/adapters/persistence/cache/redis/token_cache_repository.go) 存储Token信息

## JWT Token管理架构

### 🎫 Token类型设计

系统支持三种Token类型，定义在[JWT Claims](mdc:internal/domain/security/valueobject/jwt_claims.go)：

```go
type TokenType string

const (
    TokenTypeAccess   TokenType = "access"   // 正式访问Token
    TokenTypeRefresh  TokenType = "refresh"  // 刷新Token  
    TokenTypePreAuth  TokenType = "pre_auth" // 预认证Token
)
```

### 🔑 JWT Claims结构
```go
type JWTClaims struct {
    UserID    string    `json:"user_id"`    // 用户业务ID
    TenantID  string    `json:"tenant_id"`  // 租户业务ID (PreAuth时为空)
    Roles     []string  `json:"roles"`      // 角色列表
    JTI       string    `json:"jti"`        // JWT唯一标识符
    TokenType TokenType `json:"token_type"` // Token类型
    jwt.RegisteredClaims
}
```

### 🏗️ Token生成和管理

**JWT Manager**：[jwt_manager.go](mdc:pkg/infrastructure/security/jwt/jwt_manager.go)
- Token生成、验证、解析
- 支持不同过期时间配置
- 集成Redis缓存管理

**Token缓存仓储**：[token_cache_repository.go](mdc:internal/domain/security/repository/token_cache_repository.go)
```go
type TokenCacheRepository interface {
    SaveTokenInfo(ctx context.Context, tokenInfo *TokenInfo) error
    GetTokenInfo(ctx context.Context, jti string) (*TokenInfo, error)
    AddToBlacklist(ctx context.Context, jti string, expiresAt time.Time) error
    IsTokenBlacklisted(ctx context.Context, jti string) (bool, error)
    RevokeAllUserTokens(ctx context.Context, userID, tenantID string) error
}
```

## 权限控制系统

### 🎭 RBAC权限模型

**Casbin权限管理**：[casbin_manager.go](mdc:pkg/infrastructure/security/casbin/casbin_manager.go)
- 基于RBAC模型的细粒度权限控制
- 支持角色继承和权限组合
- 动态权限加载和缓存

**权限配置**：[casbin_model.conf](mdc:configs/casbin_model.conf)
```ini
[request_definition]
r = sub, obj, act, tenant

[policy_definition]
p = sub, obj, act, tenant

[role_definition] 
g = _, _, _

[policy_effect]
e = some(where (p.eft == allow))

[matchers]
m = g(r.sub, p.sub, r.tenant) && r.obj == p.obj && r.act == p.act && r.tenant == p.tenant
```

### 🛡️ 权限中间件

**认证中间件**：[auth_middleware.go](mdc:internal/adapters/http/middleware/auth_middleware.go)
1. 提取和验证JWT Token
2. 检查Token是否在黑名单中
3. 解析用户身份和租户信息
4. 将认证信息注入请求上下文

**权限中间件**：[permission_middleware.go](mdc:internal/adapters/http/middleware/permission_middleware.go)
1. 从上下文获取用户信息
2. 基于请求路径和方法确定所需权限
3. 调用Casbin进行权限验证
4. 拒绝未授权访问

## 多租户数据隔离

### 🏢 租户上下文管理

**租户上下文**：[tenant.go](mdc:internal/shared/context/tenant.go)
```go
type TenantContext struct {
    TenantID   string
    UserID     string
    Roles      []string
    RequestID  string
}

func GetTenantContext(ctx context.Context) (*TenantContext, bool)
func SetTenantContext(ctx context.Context, tenant *TenantContext) context.Context
```

### 🔒 数据隔离策略

**行级数据隔离**：
- 所有业务实体包含`TenantID`字段
- 数据库查询自动添加租户过滤条件
- GORM作用域自动处理租户隔离

**示例实现**：
```go
func (r *userRepository) ListByTenant(ctx context.Context, tenantID valueobject.TenantID) ([]*entity.User, error) {
    var users []*entity.User
    err := r.db.WithContext(ctx).
        Where("tenant_id = ?", tenantID.String()).
        Where("deleted_at IS NULL").
        Find(&users).Error
    return users, err
}
```

## 安全特性实现

### 🔐 Token安全管理

**Token黑名单**：[blacklist_repository.go](mdc:internal/adapters/persistence/repository/cache/redis/blacklist_repository.go)
- Redis存储被撤销的Token JTI
- 支持TTL自动过期清理
- 登出时自动加入黑名单

**会话管理**：[session_repository.go](mdc:internal/adapters/persistence/repository/cache/redis/session_repository.go)
- 跟踪用户活跃会话
- 支持多设备并发控制
- 异常检测和强制下线

### 🛡️ 安全审计

**审计日志**：
- 记录所有认证和授权操作
- 包含用户ID、租户ID、IP地址、操作时间
- 异常登录行为检测和告警

**示例日志**：
```json
{
  "timestamp": "2024-01-01T12:00:00Z",
  "level": "info", 
  "message": "用户登录成功",
  "user_id": "user_123",
  "tenant_id": "tenant_456",
  "ip_address": "*************",
  "device_info": "Mozilla/5.0...",
  "request_id": "req_789"
}
```

## API端点和路由

### 🌐 认证相关API

**主要端点**：
- `POST /api/v1/auth/login` - 第一阶段登录
- `POST /api/v1/auth/select-tenant` - 第二阶段选择租户
- `POST /api/v1/auth/refresh` - 刷新Access Token
- `POST /api/v1/auth/logout` - 用户登出
- `GET /api/v1/auth/profile` - 获取用户信息

**路由配置**：[router.go](mdc:internal/adapters/http/router/router.go)
```go
// 认证路由组 (无需认证)
authGroup := v1.Group("/auth")
{
    authGroup.POST("/login", authHandler.Login)
    authGroup.POST("/refresh", authHandler.RefreshToken)
}

// 受保护路由组 (需要认证)
protectedGroup := v1.Group("/")
protectedGroup.Use(middleware.AuthMiddleware(), middleware.PermissionMiddleware())
{
    protectedGroup.POST("/auth/select-tenant", authHandler.SelectTenant)
    protectedGroup.GET("/auth/profile", authHandler.GetProfile)
    protectedGroup.POST("/auth/logout", authHandler.Logout)
}
```

## 配置和部署

### ⚙️ 认证相关配置

**JWT配置**：[config.yaml](mdc:configs/config.yaml)
```yaml
jwt:
  secret: "your-super-secret-jwt-key"
  access_token_ttl: "15m"    # Access Token 15分钟
  refresh_token_ttl: "7d"    # Refresh Token 7天
  pre_auth_token_ttl: "5m"   # PreAuth Token 5分钟
```

**Redis配置**：
```yaml
redis:
  host: "localhost"
  port: 6379
  database: 0
  password: ""
  pool_size: 10
```

### 🚀 部署注意事项

**生产环境安全配置**：
1. ✅ 使用强随机JWT密钥
2. ✅ 启用Redis认证
3. ✅ 配置HTTPS传输加密
4. ✅ 设置合理的Token过期时间
5. ✅ 启用请求频率限制
6. ✅ 配置安全头部
7. ✅ 监控异常登录行为

**高可用部署**：
- JWT无状态设计支持水平扩展
- Redis集群保证Token缓存高可用
- PostgreSQL主从复制保证数据可靠性
- 负载均衡器分发认证请求

## 故障排查

### 🔍 常见问题和解决方案

**Token验证失败**：
1. 检查JWT密钥配置是否正确
2. 验证Token是否过期
3. 确认Token是否在黑名单中
4. 检查Redis连接状态

**权限验证失败**：
1. 确认Casbin权限规则配置
2. 检查用户角色分配
3. 验证租户权限隔离
4. 确认权限中间件配置

**多租户问题**：
1. 检查TenantID是否正确传递
2. 验证租户上下文注入
3. 确认数据库查询过滤条件
4. 检查租户权限隔离

### 📊 监控指标

**关键指标**：
- 登录成功率
- Token验证延迟
- 权限检查耗时
- Redis缓存命中率
- 异常登录次数
