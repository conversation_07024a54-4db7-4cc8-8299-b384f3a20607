---
description: 
globs: internal/domain/**/*.go
alwaysApply: false
---
# 领域设计规则

## 领域模型设计原则

### 1. 实体设计规则
- 实体必须有唯一标识符
- 实体状态变更通过领域服务完成
- 实体不允许直接依赖外部技术
- 实体文件放在 `internal/domain/{domain}/entity/` 下

示例路径：
- [product entity](mdc:internal/domain/product/entity)
- [order entity](mdc:internal/domain/order/entity)
- [purchase entity](mdc:internal/domain/purchase/entity)
- [inventory entity](mdc:internal/domain/inventory/entity)

### 2. 值对象设计规则
- 值对象必须是不可变的
- 值对象通过值相等性比较
- 复杂的值对象需要验证逻辑
- 值对象文件放在 `internal/domain/{domain}/valueobject/` 下

### 3. 聚合根设计规则
- 聚合根控制聚合内的一致性边界
- 外部只能通过聚合根访问聚合内对象
- 聚合根负责发布领域事件
- 一个聚合应该在一个事务中修改

### 4. 领域服务设计规则
- 领域服务包含不属于任何实体的业务逻辑
- 领域服务接口和实现都放在同一个领域内
- 服务接口定义在 `internal/domain/{domain}/service/` 下
- 服务实现也在同一目录下

### 5. 仓储接口设计规则
- 仓储接口必须在领域层定义
- 仓储接口只关心聚合根的持久化
- 接口定义在 `internal/domain/{domain}/repository/` 下
- 具体实现在 `internal/adapters/persistence/` 下

## 领域事件设计

### 1. 事件定义
- 事件表示已经发生的业务事实
- 事件名称使用过去时态
- 事件文件放在 `internal/domain/{domain}/event/` 下

### 2. 事件发布
- 聚合根负责发布领域事件
- 事件在业务操作完成后发布
- 使用事件总线进行发布和订阅

### 3. 跨领域通信
- 领域间通过事件进行异步通信
- 避免领域间的直接调用
- 事件处理器在应用层实现

## 业务规则验证

### 1. 规约模式 (Specification)
- 复杂业务规则使用规约模式
- 规约可以组合和重用
- 规约文件放在 `internal/domain/{domain}/specification/` 下

### 2. 不变量保护
- 实体和聚合根必须保护自身的不变量
- 在状态变更时验证业务规则
- 验证失败抛出领域异常

## 示例领域结构

```
internal/domain/product/
├── entity/           # 商品实体
├── valueobject/      # 商品相关值对象
├── service/          # 商品领域服务
├── repository/       # 商品仓储接口
├── event/            # 商品领域事件
└── specification/    # 商品业务规则
```
