---
description: 
globs: *.go
alwaysApply: false
---
# 代码规范与质量标准

## 项目概述

九翼跨境电商ERP系统遵循严格的Go语言编码规范，注重代码质量、可维护性和团队协作效率。

## 命名规范

### 包命名规范
- **使用单数形式**：`user`、`order`、`product`（不是`users`、`orders`）
- **避免泛化词汇**：禁止使用`common`、`util`、`helper`等模糊命名
- **业务导向命名**：`inventory`、`finance`、`security`等明确的业务领域

### 文件组织规范
```
{domain}/
├── entity/           # 实体文件：user.go, role.go
├── valueobject/      # 值对象文件：user_profile.go, email.go  
├── repository/       # 仓储接口：user_repository.go
├── service/          # 领域服务：authentication_service.go
└── event/           # 领域事件：user_events.go
```

### 结构体和接口命名
- **PascalCase**：`UserRepository`、`OrderService`、`ProductManager`
- **接口后缀**：使用`-er`后缀，如`Manager`、`Generator`、`Validator`
- **业务术语**：使用领域专用术语，如`Product`、`Invoice`、`Shipment`

## 实体设计规范

### BaseEntity继承
所有实体必须继承[基础实体](mdc:internal/shared/types/base_entity.go)：

```go
type User struct {
    shared.BaseEntity
    UserID      valueobject.UserID      `gorm:"uniqueIndex;not null"`
    TenantID    valueobject.TenantID    `gorm:"index;not null"`
    Email       valueobject.Email       `gorm:"uniqueIndex;not null"`
    Profile     valueobject.UserProfile `gorm:"embedded"`
}
```

### 值对象实现标准
所有值对象必须实现验证和比较方法：

```go
type UserID struct {
    value string
}

func (u UserID) String() string { return u.value }
func (u UserID) IsValid() bool { return u.value != "" }
func (u UserID) Equals(other ValueObject) bool { /* 实现比较逻辑 */ }
```

## 错误处理规范

### 错误分层设计
- **基础设施错误**：[pkg/common/errors](mdc:pkg/common/errors) (错误码1000-2999)
- **业务错误**：[internal/shared/errors](mdc:internal/shared/errors) (错误码3000-9999)

### 错误码分配
- `1000-1999`：数据库错误
- `2000-2999`：缓存、网络、配置等基础设施错误  
- `3000-3999`：用户认证授权错误
- `4000-4999`：订单管理错误
- `5000-5999`：商品管理错误
- `6000-6999`：库存管理错误
- `7000-7999`：财务管理错误
- `8000-8999`：跨境电商平台集成错误

### 错误使用示例
```go
func (s *AuthService) Login(ctx context.Context, req *LoginRequest) error {
    user, err := s.userRepo.FindByEmail(ctx, req.Email)
    if err != nil {
        if errors.IsNotFound(err) {
            return errors.NewBusinessError(3001, "用户不存在")
        }
        return errors.Wrap(err, "查询用户失败")
    }
    
    if !user.VerifyPassword(req.Password) {
        return errors.NewBusinessError(3002, "密码错误")
    }
    
    return nil
}
```

## 仓储模式实现

### 接口定义规范
Domain层定义仓储接口，包含完整的CRUD和业务查询方法：

```go
type UserRepository interface {
    // 基础CRUD
    Create(ctx context.Context, user *entity.User) error
    GetByID(ctx context.Context, id valueobject.UserID) (*entity.User, error)
    Update(ctx context.Context, user *entity.User) error
    Delete(ctx context.Context, id valueobject.UserID) error
    
    // 业务查询
    FindByEmail(ctx context.Context, email valueobject.Email) (*entity.User, error)
    ListByTenant(ctx context.Context, tenantID valueobject.TenantID) ([]*entity.User, error)
    
    // 批量操作
    BatchCreate(ctx context.Context, users []*entity.User) error
}
```

### 实现规范
Adapters层实现具体的数据访问逻辑：

```go
func (r *userRepository) Create(ctx context.Context, user *entity.User) error {
    if err := r.db.WithContext(ctx).Create(user).Error; err != nil {
        if errors.IsConstraintViolation(err) {
            return errors.NewConflictError("用户已存在")
        }
        return errors.NewSystemError(1001, "创建用户失败", err)
    }
    return nil
}
```

## 用例设计规范

### 用例方法结构
应用层用例方法必须按照固定模式组织：

1. **输入验证**：验证请求参数的合法性
2. **业务规则检查**：执行业务约束验证
3. **执行业务操作**：调用领域服务和仓储
4. **发布领域事件**：通知其他聚合状态变更
5. **返回结果**：组装响应数据

### 事务边界
- 应用层用例方法是事务边界
- 跨聚合操作必须使用数据库事务
- 长事务要拆分为多个短事务

## 测试规范

### 测试金字塔比例
- **单元测试**：80%（快速、隔离、聚焦业务逻辑）
- **集成测试**：15%（数据库、缓存、外部服务）
- **端到端测试**：5%（完整业务流程）

### 测试文件组织
```
test/
├── unit/                # 单元测试
│   ├── domain/         # 领域层测试
│   └── application/    # 应用层测试
├── integration/        # 集成测试
└── mock/              # Mock对象
```

### 测试命名规范
- 测试文件：`{source_file}_test.go`
- 测试方法：`Test{StructName}_{MethodName}`
- 测试用例：使用描述性名称，如`"成功创建用户"`、`"邮箱重复应返回错误"`

## 日志记录规范

### 日志级别使用
- **INFO**：业务流程关键节点
- **WARN**：业务异常但不影响系统运行
- **ERROR**：系统错误需要关注
- **DEBUG**：开发调试信息

### 结构化日志示例
```go
logger.Info("用户登录成功", 
    zap.String("user_id", userID),
    zap.String("tenant_id", tenantID),
    zap.String("ip_address", clientIP),
    zap.Duration("duration", requestDuration))
```

## 配置管理规范

### 配置文件结构
- **主配置**：[config.yaml](mdc:configs/config.yaml) - 包含默认值的标准YAML
- **环境特定**：`.env.development`、`.env.production` - 环境变量覆盖
- **本地覆盖**：`.env.local` - 本地开发专用（不提交代码库）

### 环境变量映射
配置字段自动映射到环境变量：
- `database.host` → `DATABASE_HOST`
- `database.port` → `DATABASE_PORT`
- `jwt.secret` → `JWT_SECRET`

## 安全规范

### 认证和授权
- **JWT Token**：使用[JWT管理器](mdc:pkg/infrastructure/security/jwt/jwt_manager.go)
- **Redis缓存**：Token信息缓存到Redis，支持撤销
- **RBAC权限**：基于[Casbin](mdc:pkg/infrastructure/security/casbin/casbin_manager.go)的权限控制
- **多租户隔离**：通过TenantID实现数据隔离

### 数据安全
- **敏感数据加密**：密码使用bcrypt + salt哈希
- **SQL注入防护**：使用GORM参数化查询
- **输入验证**：所有用户输入必须验证和清洗

## 性能优化规范

### 数据库优化
- **索引策略**：为查询字段创建合适索引
- **连接池管理**：合理配置数据库连接池
- **避免N+1查询**：使用预加载或批量查询
- **分页查询**：大数据量查询必须分页

### 缓存策略
- **应用缓存**：用例结果缓存（5-15分钟）
- **查询缓存**：复杂查询结果缓存（1-5分钟）
- **实体缓存**：核心实体缓存（30分钟-2小时）

## 文档和注释规范

### 代码注释
- **包注释**：每个包必须有概述注释
- **公开API注释**：所有公开的函数、方法、类型必须有注释
- **复杂逻辑注释**：解释为什么这样做，而不是做什么
- **中文注释**：项目内部代码使用中文注释

### API文档
- **Swagger注解**：所有HTTP接口必须有Swagger注解
- **示例数据**：提供完整的请求和响应示例
- **错误码说明**：列出可能的错误码和含义

## 质量检查

### 自动化检查
项目使用[Makefile](mdc:Makefile)提供的质量检查命令：

```bash
make lint           # 静态代码分析 (golangci-lint)
make security-check # 安全漏洞扫描 (gosec)
make test-coverage  # 测试覆盖率检查 (>= 80%)
make quality-check  # 完整质量检查
```

### 质量标准
- **测试覆盖率**：≥ 80%
- **代码复杂度**：单个函数复杂度 ≤ 10
- **安全漏洞**：零容忍，必须修复所有安全问题
- **代码规范**：必须通过golangci-lint检查

## 版本控制规范

### 提交信息格式
遵循[约定式提交](mdc:https:/www.conventionalcommits.org/zh-hans)规范：

```
<类型>[可选的作用域]: <描述>

[可选的正文]

[可选的脚注]
```

### 分支策略
- `main`：生产环境分支
- `develop`：开发集成分支  
- `feature/ERP-123-功能描述`：功能开发分支
- `hotfix/ERP-456-问题描述`：热修复分支

### 代码审查
- Pull Request必须包含完整的功能测试
- 至少需要2人参与代码审查
- 必须通过所有自动化检查
- 需要更新相关文档
