---
description:
globs:
alwaysApply: false
---
# CQRS混合架构设计规则

## CQRS架构概述

### 1. 混合模式设计原则
基于现有六边形架构，采用CQRS混合模式：
- **简单操作使用CQRS**：单一职责的创建、更新、删除和查询
- **复杂流程保持UseCase**：需要事务协调的业务流程
- **渐进式迁移**：不破坏现有架构，逐步引入CQRS

### 2. 架构分层
```
┌─────────────────────────────────────────────────────────────┐
│                    HTTP/gRPC Adapters                       │
└─────────────────────┬─────────────────┬─────────────────────┘
                      │                 │
                ┌─────▼─────┐     ┌─────▼─────┐
                │  Command  │     │   Query   │
                │  Gateway  │     │  Gateway  │
                └─────┬─────┘     └─────┬─────┘
                      │                 │
            ┌─────────▼─────────┐ ┌─────▼─────┐
            │ Command Handlers  │ │Query      │
            │ + Complex UseCase │ │Handlers   │
            └─────────┬─────────┘ └─────┬─────┘
                      │                 │
                ┌─────▼─────┐     ┌─────▼─────┐
                │  Domain   │     │Read Model │
                │  Model    │     │+ Write DB │
                │(Write DB) │     │           │
                └─────┬─────┘     └───────────┘
                      │
                ┌─────▼─────┐
                │Event Bus  │
                │& Message  │
                │   Queue   │
                └───────────┘
```

## 应用层目录结构规范

### 1. 扩展后的Application层
```
internal/application/
├── usecase/          # 保留：复杂业务流程用例
│   ├── order/        # 复杂订单处理流程
│   ├── purchase/     # 复杂采购流程
│   ├── inventory/    # 复杂库存流程
│   ├── finance/      # 复杂财务流程
│   ├── product/      # 简单商品操作将迁移到Command
│   └── tenant/       # 租户管理流程
├── command/          # 新增：命令处理（写操作）
│   ├── handler/      # 命令处理器实现
│   ├── model/        # 命令模型定义
│   └── gateway/      # 命令网关（路由）
├── query/            # 新增：查询处理（读操作）
│   ├── handler/      # 查询处理器实现
│   ├── model/        # 查询模型定义
│   ├── view/         # 查询视图模型
│   └── gateway/      # 查询网关（路由）
├── readmodel/        # 新增：读模型管理
│   ├── repository/   # 读模型仓储接口
│   ├── projection/   # 投影构建器
│   └── synchronizer/ # 读写模型同步器
├── dto/              # 保留：数据传输对象
├── assembler/        # 保留：DTO组装器
└── event/            # 扩展：事件处理
    ├── handler/      # 应用事件处理器
    ├── projection/   # 查询投影构建器
    └── synchronizer/ # 读模型同步器
```

### 2. 扩展现有Event框架
```
pkg/event/            # 扩展现有事件框架支持CQRS
├── bus/              # 统一总线管理
│   ├── event.go      # 现有：事件总线
│   ├── command.go    # 新增：命令总线
│   ├── query.go      # 新增：查询总线
│   └── cqrs.go       # 新增：CQRS统一总线
├── publisher/        # 现有：事件发布器
├── dispatcher/       # 现有：事件分发器（可扩展支持命令/查询）
├── handler/          # 现有：事件处理器接口
├── command/          # 新增：命令框架
│   ├── handler.go    # 命令处理器接口
│   └── command.go    # 命令接口定义
└── query/            # 新增：查询框架
    ├── handler.go    # 查询处理器接口
    └── query.go      # 查询接口定义
```

## 使用场景划分

### 1. 适合Command Handler的场景
- **简单CRUD操作**：单一实体的创建、更新、删除
- **单一职责操作**：只涉及一个聚合根的操作
- **无复杂事务**：不需要跨多个聚合的事务协调

**示例操作**：
```go
// 适合Command Handler
- CreateProduct        // 创建商品
- UpdateProductPrice   // 更新商品价格
- DeleteProduct        // 删除商品
- CreateCategory       // 创建分类
- UpdateTenantConfig   // 更新租户配置
```

### 2. 保持UseCase的场景
- **复杂业务流程**：涉及多个聚合根的协调
- **事务一致性**：需要强事务保证的操作
- **业务规则复杂**：包含复杂业务逻辑和验证

**示例操作**：
```go
// 保持UseCase
- ProcessOrder         // 处理订单（订单+库存+支付）
- ExecutePurchase      // 执行采购（采购+库存+财务）
- InventoryTransfer    // 库存调拨（多仓库事务）
- OrderRefund          // 订单退款（订单+库存+财务+支付）
- MonthlyClosing       // 月末结账（财务+库存+审计）
```

### 3. 适合Query Handler的场景
- **所有读操作**：无论简单还是复杂
- **数据聚合**：需要跨表聚合的查询
- **性能优化**：可以使用专门的读模型优化

**示例操作**：
```go
// 所有查询都使用Query Handler
- GetProductByID       // 获取商品详情
- SearchProducts       // 商品搜索
- GetOrderHistory      // 订单历史
- InventoryReport      // 库存报表
- FinancialDashboard   // 财务看板
```

## 命令模型设计规范

### 1. 命令接口定义
```go
// pkg/cqrs/command/command.go
type Command interface {
    CommandType() string    // 命令类型
    AggregateID() string   // 聚合根ID
    TenantID() string      // 租户ID
}
```

### 2. 命令基础结构
```go
// 所有命令继承基础结构
type BaseCommand struct {
    ID        string    `json:"id"`
    Type      string    `json:"type"`
    AggrID    string    `json:"aggregate_id"`
    Tenant    string    `json:"tenant_id"`
    Timestamp time.Time `json:"timestamp"`
    Version   int       `json:"version"`
}
```

### 3. 命令处理器接口
```go
// pkg/cqrs/command/handler.go
type Handler interface {
    Handle(ctx context.Context, cmd Command) error
}
```

## 查询模型设计规范

### 1. 查询接口定义
```go
// pkg/cqrs/query/query.go
type Query interface {
    QueryType() string    // 查询类型
    TenantID() string     // 租户ID
}
```

### 2. 查询基础结构
```go
// 基础查询结构
type BaseQuery struct {
    Type   string `json:"type"`
    Tenant string `json:"tenant_id"`
}

// 分页查询结构
type PagedQuery struct {
    BaseQuery
    Page     int               `json:"page"`
    PageSize int               `json:"page_size"`
    Filters  map[string]string `json:"filters"`
    Sort     string            `json:"sort"`
}
```

### 3. 查询处理器接口
```go
// pkg/cqrs/query/handler.go
type Handler interface {
    Handle(ctx context.Context, query Query) (interface{}, error)
}
```

## 读模型设计规范

### 1. 读模型与写模型分离
- **写模型**：使用现有的实体和仓储
- **读模型**：针对查询优化的视图模型
- **同步机制**：通过事件驱动更新读模型

### 2. 视图模型设计
```go
// 查询视图模型设计原则
type ProductView struct {
    // 基础信息（来自Product实体）
    ID         string    `json:"id"`
    TenantID   string    `json:"tenant_id"`
    SKU        string    `json:"sku"`
    Name       map[string]string `json:"name"`
    
    // 聚合信息（来自多个数据源）
    TotalStock    int     `json:"total_stock"`    // 来自Inventory
    AvgRating     float64 `json:"avg_rating"`     // 来自Review
    OrderCount    int64   `json:"order_count"`    // 来自Order
    LastOrderAt   *time.Time `json:"last_order_at"` // 来自Order
}
```

### 3. 投影同步策略
- **实时同步**：关键业务数据通过事件实时更新
- **批量同步**：统计数据可以定时批量更新
- **按需同步**：低频访问数据可以查询时动态聚合

## 事件处理扩展规范

### 1. 事件处理器分类
```go
// 业务事件处理器（保持现有）
type ProductEventHandler struct {
    // 处理业务逻辑相关的事件
}

// 投影构建器（新增）
type ProductProjectionBuilder struct {
    // 专门负责构建查询投影
}

// 同步器（新增）  
type ProductViewSynchronizer struct {
    // 负责读写模型同步
}
```

### 2. 事件订阅规范
- **业务处理器**：订阅业务相关事件，处理跨领域协调
- **投影构建器**：订阅所有相关事件，维护查询视图
- **同步器**：订阅特定事件，保持读写模型一致性

## 迁移路径规范

### 1. 第一阶段：基础框架（1-2周）
- 创建CQRS框架组件
- 创建目录结构
- 建立命令和查询总线

### 2. 第二阶段：查询分离（2-3周）
- 迁移所有查询操作到Query Handler
- 建立基础读模型
- 优化查询性能

### 3. 第三阶段：简单命令（3-4周）
- 迁移简单CRUD操作到Command Handler
- 建立投影同步机制
- 完善事件处理

### 4. 第四阶段：复杂优化（按需）
- 评估复杂UseCase是否需要拆分
- 优化读写模型分离
- 性能调优和监控

## 兼容性规范

### 1. 向后兼容
- 现有UseCase继续可用
- 现有API保持不变
- 现有事件处理机制保持不变

### 2. 渐进迁移
- 新功能优先使用CQRS
- 现有功能按优先级迁移
- 保持系统稳定性

### 3. 共存模式
- CQRS和UseCase可以共存
- 根据业务复杂度选择合适模式
- 保持架构的灵活性
