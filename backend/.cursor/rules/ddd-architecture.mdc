---
description: 
globs: 
alwaysApply: true
---
# DDD架构导航规则

## 项目架构概述

这是一个基于**DDD六边形架构**的Go语言跨境电商ERP系统，严格遵循领域驱动设计原则。

## 核心架构分层

### 🏛️ 领域层 (Domain Layer) - internal/domain/
领域层是系统的核心，包含纯业务逻辑，**禁止依赖任何外部技术**。

**主要聚合：**
- [用户聚合](mdc:internal/domain/user) - 用户管理、认证、角色权限
- [安全聚合](mdc:internal/domain/security) - Token管理、会话控制、审计
- [商品聚合](mdc:internal/domain/product) - 商品信息、SKU、分类
- [订单聚合](mdc:internal/domain/order) - 订单生命周期、状态管理
- [库存聚合](mdc:internal/domain/inventory) - 库存跟踪、调拨、预警
- [财务聚合](mdc:internal/domain/finance) - 成本核算、多币种

**领域层组织结构：**
```
domain/{聚合名}/
├── entity/          # 实体和聚合根
├── valueobject/     # 值对象
├── repository/      # 仓储接口定义
├── service/         # 领域服务
├── event/           # 领域事件
└── specification/   # 业务规约
```

### 🎯 应用层 (Application Layer) - internal/application/
应用层负责用例编排和业务流程协调，只能依赖Domain层接口。

**核心组件：**
- [业务用例](mdc:internal/application/usecase) - 复杂业务流程编排
- [CQRS命令](mdc:internal/application/command) - 写操作处理
- [CQRS查询](mdc:internal/application/query) - 读操作处理
- [DTO组装器](mdc:internal/application/assembler) - 数据转换
- [数据传输对象](mdc:internal/application/dto) - 接口数据结构

### 🔌 适配器层 (Adapters Layer) - internal/adapters/
适配器层实现Domain层定义的接口，处理外部技术集成。

**主要适配器：**
- [HTTP适配器](mdc:internal/adapters/http) - REST API接口
- [持久化适配器](mdc:internal/adapters/persistence) - 数据库操作实现
- [外部服务适配器](mdc:internal/adapters/external) - 第三方API集成
- [消息队列适配器](mdc:internal/adapters/messaging) - 异步消息处理

### ⚙️ 基础设施层 (Infrastructure Layer) - pkg/infrastructure/
基础设施层提供技术组件和工具，支撑上层业务逻辑。

**核心组件：**
- [配置管理](mdc:pkg/infrastructure/config) - 配置加载和验证
- [数据库管理](mdc:pkg/infrastructure/database) - 连接池、迁移
- [缓存管理](mdc:pkg/infrastructure/cache) - Redis缓存抽象
- [JWT管理](mdc:pkg/infrastructure/security/jwt) - Token生成验证
- [权限管理](mdc:pkg/infrastructure/security/casbin) - RBAC权限控制

## 关键设计模式

### 仓储模式 (Repository Pattern)
- **接口定义**：在Domain层定义仓储接口
- **具体实现**：在Adapters层实现具体的数据访问逻辑
- **示例**：[用户仓储接口](mdc:internal/domain/user/repository/user_repository.go) → [用户仓储实现](mdc:internal/adapters/persistence/repository/relational/user/user_repository.go)

### CQRS模式 (Command Query Responsibility Segregation)
- **命令处理**：[命令处理器](mdc:internal/application/command) 负责写操作
- **查询处理**：[查询处理器](mdc:internal/application/query) 负责读操作
- **复杂用例**：[用例层](mdc:internal/application/usecase) 处理跨聚合的复杂业务流程

### 事件驱动模式
- **领域事件**：在聚合内发布状态变更事件
- **事件处理**：通过事件总线实现跨聚合通信
- **示例**：[用户事件](mdc:internal/domain/user/event/user_events.go)

## 依赖注入规则

项目使用Wire进行编译时依赖注入：
- **依赖配置**：[依赖注入配置](mdc:internal/infrastructure/di)
- **Provider定义**：[服务提供者](mdc:internal/infrastructure/di/provider)
- **Wire生成**：[Wire生成代码](mdc:internal/infrastructure/di/injector/wire_gen.go)

## 多租户架构

系统原生支持多租户SaaS架构：
- **租户实体**：[租户管理](mdc:internal/domain/tenant/entity/tenant.go)
- **数据隔离**：通过TenantID实现行级数据隔离
- **上下文传递**：[租户上下文](mdc:internal/shared/context/tenant.go)

## 错误处理分层

- **基础设施错误**：[pkg/common/errors](mdc:pkg/common/errors) (错误码1000-2999)
- **业务错误**：[internal/shared/errors](mdc:internal/shared/errors) (错误码3000-9999)
- **错误中间件**：[错误处理中间件](mdc:pkg/common/errors/middleware.go)

## 核心入口点

- **API服务入口**：[main.go](mdc:cmd/api/main.go)
- **数据库迁移**：[migrate工具](mdc:cmd/migrate/main.go)
- **路由配置**：[路由定义](mdc:internal/adapters/http/router/router.go)
- **配置文件**：[主配置](mdc:configs/config.yaml)

## 重要约定

1. **Domain层纯净性**：领域层代码不得包含任何外部技术依赖
2. **接口优先**：先定义接口，再实现具体逻辑
3. **单向依赖**：内层不依赖外层，通过接口反转依赖
4. **聚合边界**：严格控制聚合间的交互，避免直接调用
5. **事务边界**：应用层用例方法是事务边界
