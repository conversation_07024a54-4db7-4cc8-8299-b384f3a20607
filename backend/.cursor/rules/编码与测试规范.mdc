---
description: 
globs: *.go
alwaysApply: false
---
# 编码与测试规范

本文档统一规定了项目在代码编写和软件测试方面的所有规范，旨在提高代码质量、可维护性和团队协作效率。

---

## 第一部分：编码规范

### 1. 通用Go语言规范
- **包命名**: 使用简短、有意义的单数小写单词，如 `product`。禁止使用下划线或驼峰。
- **文件命名**: 使用小写字母和下划线，如 `product_repository.go`。
- **变量与类型命名**:
    - **结构体/接口**: 使用驼峰命名法（PascalCase），如 `ProductService`。
    - **常量**: 使用大写字母和下划线，如 `MAX_CONNECTIONS`。
    - **变量**: 使用小驼峰命名法（camelCase），如 `productID`。
- **上下文传递**: 所有需要与外部（如数据库、API）交互的函数，其第一个参数必须是 `context.Context`。

### 2. 错误处理
- **统一错误类型**: 使用 `pkg/common/errors` 中定义的错误工厂来创建标准化的业务错误。
- **错误码**: 每个业务错误都应关联一个唯一的错误码，便于前端和监控系统识别。
- **领域错误**: 特定领域的业务逻辑错误应定义在各自的领域包内，如 `internal/domain/product/errors.go`。

### 3. 日志记录
- **结构化日志**: 所有日志都必须是结构化的（如JSON格式），并使用 `pkg/infrastructure/logger` 提供的日志组件。
- **核心信息**: 日志中必须包含 `trace_id` 和 `tenant_id`，以便于链路追踪和问题排查。
- **信息脱敏**: 在记录日志前，必须对密码、密钥、个人信息等敏感数据进行脱敏处理。

### 4. 核心业务代码规范

#### A. 实体 (Entity) 设计
- **双ID架构**: 实体必须包含`ID`（技术ID，雪花算法生成）和`BusinessID`（业务ID，UUID生成）。`ID`用于内部关联，`BusinessID`用于对外暴露。
- **乐观锁**: 对于需要并发控制的实体，应加入 `Version` 字段，并在更新时递增。
- **业务方法**: 实体的行为（业务逻辑）应封装在实体的方法中，而不是散落在服务层。

```go
type Product struct {
    ID          int64           `gorm:"primaryKey;autoIncrement:false" json:"-"`
    BusinessID  string          `gorm:"uniqueIndex;size:36" json:"id"`
    TenantID    string          `gorm:"index;size:36"`
    Version     int             `gorm:"default:1"` // 乐观锁
    // ... 其他字段
}

// UpdateName 是一个业务方法
func (p *Product) UpdateName(name map[string]string) {
    p.Name = datatypes.JSON(name)
    p.Version++
    p.UpdatedAt = time.Now()
}
```

#### B. 值对象 (Value Object) 设计
- **不可变性**: 值对象在创建后其状态不能被修改。任何修改操作都应返回一个新的值对象实例。
- **无副作用**: 值对象的方法不应修改其内部状态。

#### C. 仓储 (Repository) 接口设计
- **面向聚合根**: 仓储接口的操作对象必须是聚合根。
- **关注持久化**: 仓储的职责仅限于对象的持久化和检索，不应包含业务逻辑。
- **分页查询**: 列表查询方法应同时返回结果集和总数，以支持分页。

### 5. API设计
- **RESTful风格**: 遵循RESTful设计原则。
- **统一响应格式**: 所有API响应都应封装在统一的结构体中，包含 `code`, `message`, `data`, `trace_id`。
- **分页响应**: 分页查询的响应应使用标准的分页结构，包含 `items`, `total`, `page`, `page_size` 等信息。

---

## 第二部分：测试规范

### 1. 测试分层策略 (测试金字塔)
- **单元测试 (80%)**: 构成测试主体。专注于单个函数或模块的逻辑，无外部依赖。
- **集成测试 (15%)**: 测试模块间的交互，如服务层与数据库的交互。
- **端到端测试 (5%)**: 模拟真实用户场景，测试完整的业务流程。

### 2. 测试工具链
- **核心框架**: Go标准库 `testing`。
- **断言库**: `github.com/stretchr/testify/assert` (非致命断言) 和 `require` (致命断言)。
- **Mock框架**: `github.com/golang/mock/gomock`，用于生成依赖接口的Mock对象。
- **测试套件**: `github.com/stretchr/testify/suite`，用于组织需要共享Setup/Teardown逻辑的复杂测试。

### 3. 单元测试规范
- **命名**: 测试函数名遵循 `Test{FunctionName}_{Scenario}_{ExpectedResult}` 格式，如 `TestCreateProduct_InvalidSKU_ReturnsError`。
- **表驱动测试**: 对于有多种输入和预期输出的函数，优先使用表驱动的测试风格，增强可读性和可维护性。
- **Mock依赖**: 必须Mock所有外部依赖（如仓储、事件总线），确保测试的隔离性和速度。

```go
// Application层单元测试示例
func TestCreateProductUseCase_Execute(t *testing.T) {
    ctrl := gomock.NewController(t)
    defer ctrl.Finish()
    
    mockRepo := mock_product.NewMockProductRepository(ctrl) // Mock仓储
    usecase := NewCreateProductUseCase(mockRepo)
    
    // 定义Mock期望
    mockRepo.EXPECT().Save(gomock.Any(), gomock.Any()).Return(nil)
    
    // 执行并断言
    _, err := usecase.Execute(context.Background(), &CreateProductRequest{...})
    require.NoError(t, err)
}
```

### 4. 集成测试规范
- **独立目录**: 集成测试代码统一放在 `test/integration` 目录下。
- **测试数据库**: 集成测试必须连接到一个独立的测试数据库，严禁连接开发或生产数据库。
- **数据清理**: 每个测试用例执行前后都必须清理测试数据，确保测试间的独立性。推荐使用`testify/suite`来管理`SetupTest`和`TearDownTest`。
- **HTTP API测试**: 使用 `net/http/httptest` 创建测试服务器来测试API端点，验证请求处理、参数绑定、响应格式等。

```go
// 数据库集成测试套件示例
type ProductRepositoryTestSuite struct {
    suite.Suite
    db   *gorm.DB
    repo product.ProductRepository
}

// SetupSuite 在套件开始前运行一次
func (s *ProductRepositoryTestSuite) SetupSuite() {
    s.db = setupTestDatabase() // 连接测试数据库
    s.repo = NewProductRepository(s.db)
}

// SetupTest 在每个测试用例前运行
func (s *ProductRepositoryTestSuite) SetupTest() {
    s.db.Exec("TRUNCATE TABLE products") // 清理数据
}

func (s *ProductRepositoryTestSuite) TestSaveAndFind() {
    // ... 测试逻辑 ...
}
```
