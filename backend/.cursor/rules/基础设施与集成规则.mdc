---
description: 
globs: 
alwaysApply: true
---
# 基础设施与集成规则

本文档统一规定了项目中关于基础设施（配置、消息队列）和外部平台集成的所有架构原则、策略和开发规范。

---

## 第一部分：配置管理规则

### 1. 核心架构原则
- **Loader + Validator分离**: 配置加载过程分为两步，Loader负责从文件和环境加载原始数据，Validator负责验证和转换数据，职责清晰。
- **标准YAML与环境变量**: 配置文件使用标准YAML格式，并包含合理的默认值。环境变量用于覆盖配置，通过Viper的 `AutomaticEnv()` 和 `SetEnvKeyReplacer()` 自动绑定，无需在配置文件中使用占位符。
- **优先级**: 加载顺序为 `环境变量` > `config.yaml` > `环境特定.yaml`，`.env.local` 文件具有最高优先级以方便本地调试。

### 2. 目录与文件结构
```
configs/
├── config.yaml           # 主配置文件（含默认值）
└── ...                   # 其他环境特定配置文件 (如 development.yaml)

.env.example              # 环境变量模板
.env.development          # 开发环境
.env.local                # 本地覆盖（不提交）
```

### 3. 配置加载实现
- **Loader**: 使用 `viper` 和 `godotenv` 实现，负责读取 `config.yaml` 和 `.env` 文件，并合并配置。
- **Validator**: 使用 `go-playground/validator` 对加载后的配置结构体进行数据验证，确保核心配置的正确性。

```go
// pkg/infrastructure/config/loader.go (简化示例)
func (l *Loader) Load() (*Config, error) {
    // 1. 加载 .env 文件
    godotenv.Load(".env.local", ".env.development", ".env")

    // 2. 配置Viper
    v := viper.New()
    v.SetConfigName("config")
    v.SetConfigType("yaml")
    v.AddConfigPath(l.configPath)
    v.AutomaticEnv() // 自动绑定环境变量
    v.SetEnvKeyReplacer(strings.NewReplacer(".", "_"))

    // 3. 读取和解析配置
    if err := v.ReadInConfig(); err != nil { /* ... */ }
    var config Config
    if err := v.Unmarshal(&config); err != nil { /* ... */ }
    
    // 4. 返回待验证的配置
    return &config, nil
}
```

---

## 第二部分：消息队列策略 (基于Redis)

### 1. 渐进式演进
项目初期使用Redis作为统一的消息队列解决方案，利用其多功能性。未来当业务量增长时，可平滑迁移至Kafka等更专业的消息中间件，而抽象接口层保持不变。

### 2. 通用消息队列接口
为了实现未来平滑迁移，所有消息队列操作都必须通过定义的通用接口进行。
- **路径**: `pkg/infrastructure/mq/interfaces.go`
- **核心接口**:
    - `Producer`: 用于发送普通消息、批量消息和延迟消息。
    - `Consumer`: 用于订阅主题并处理消息。
    - `Publisher`/`Subscriber`: 用于发布/订阅模式。
    - `MQManager`: 管理所有MQ组件的生命周期。

```go
// pkg/infrastructure/mq/interfaces.go
type Producer interface {
    Send(ctx context.Context, topic string, payload []byte, headers ...map[string]string) error
    SendDelayed(ctx context.Context, topic string, payload []byte, delay time.Duration, headers ...map[string]string) error
    Close() error
}

type Consumer interface {
    Subscribe(ctx context.Context, topic string, handler MessageHandler) error
    Close() error
}
```

### 3. Redis实现策略
- **普通队列**: 使用 **Redis Streams** (`XADD`, `XREADGROUP`)，提供持久化、消费组和消息追溯能力。
- **发布/订阅**: 使用 **Redis Pub/Sub** (`PUBLISH`, `SUBSCRIBE`)，用于实时事件通知。
- **延迟队列**: 使用 **Redis Sorted Sets** (`ZADD`, `ZRANGEBYSCORE`)，将消息按执行时间戳排序，由一个定时任务轮询处理到期任务。

---

## 第三部分：跨境电商平台集成架构

### 1. 架构分层
平台集成严格遵循项目的六边形架构，确保平台特定逻辑与核心业务逻辑解耦。

- **Domain层 (`internal/domain/platform/`)**: 定义平台无关的集成接口、实体和值对象。例如，定义`ProductSyncService`接口，但不包含任何与Amazon或Shopify相关的代码。
- **Application层 (`internal/application/usecase/platform/`)**: 编排平台数据同步的用例，如"同步商品用例"，它会调用Domain层的接口，处理业务逻辑转换，并使用仓储进行持久化。
- **Adapters层 (`internal/adapters/external/platform/`)**: 实现具体的平台适配器。每个平台（如Amazon, Shopify）都有一个独立的适配器，负责实现Domain层定义的接口。这里处理所有与第三方API交互的细节，如认证、HTTP请求、数据格式映射、限流和重试。

### 2. 核心接口与适配器模式
```go
// 1. Domain层定义接口
// internal/domain/platform/service/product_sync_service.go
type ProductSyncService interface {
    SyncProducts(ctx context.Context, tenantID string, platformType valueobject.PlatformType) (*SyncResult, error)
}

// 2. Adapters层实现具体平台的适配器
// internal/adapters/external/platform/amazon/amazon_adapter.go
type AmazonAdapter struct { /* ... 依赖http客户端、认证器等 ... */ }

func NewAmazonAdapter(...) platform.ProductSyncService {
    return &AmazonAdapter{...}
}

// 实现接口方法
func (a *AmazonAdapter) SyncProducts(ctx context.Context, tenantID string, platformType valueobject.PlatformType) (*SyncResult, error) {
    // 1. 调用Amazon SDK或HTTP客户端获取商品数据
    // 2. 将Amazon返回的数据映射到平台无关的数据结构
    // 3. 返回结果
}
```

### 3. 数据流与DTO转换
- **外部数据 -> 内部实体**: 数据从外部平台API获取后，在**Adapter层**被映射成平台无关的结构。进入**Application层**后，由**Assembler**将其转换为核心领域的业务实体（如`domain/product/entity.Product`）。
- **内部实体 -> API响应**: 从数据库查询出的实体，在**Application层**由**Assembler**转换为DTO（Data Transfer Object），最终通过API返回给前端。

### 4. 依赖注入
通过依赖注入容器（Wire），根据配置将特定平台的适配器（如`AmazonAdapter`）绑定到Domain层定义的接口（如`ProductSyncService`）上。这使得切换或增加新平台时，只需开发新的适配器并调整DI配置即可，无需修改上层业务代码。
