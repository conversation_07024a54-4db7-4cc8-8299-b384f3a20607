# 九翼跨境电商ERP系统 - 环境变量配置示例
# 复制此文件为对应环境的.env文件并根据实际环境修改配置值
# 例如：cp .env.example .env.development

# ===========================================
# 基础环境配置
# ===========================================

# 应用运行环境: development, testing, production
APP_ENV=development

# 配置文件路径（可选，默认为 configs）
ERP_CONFIG_PATH=configs

# ===========================================
# 服务器配置
# ===========================================

# HTTP服务器配置
ERP_SERVER_HOST=0.0.0.0
ERP_SERVER_PORT=8080
ERP_SERVER_READ_TIMEOUT=30s
ERP_SERVER_WRITE_TIMEOUT=30s
ERP_SERVER_IDLE_TIMEOUT=120s

# gRPC服务器配置
ERP_GRPC_ENABLE=false
ERP_GRPC_PORT=9090

# ===========================================
# 数据库配置
# ===========================================

# PostgreSQL数据库连接配置
ERP_DB_DRIVER=postgres
ERP_DB_HOST=localhost
ERP_DB_PORT=5432
ERP_DB_NAME=erp_development
ERP_DB_USER=erp_dev
ERP_DB_PASSWORD=dev_password_123
ERP_DB_SSL_MODE=disable

# 数据库连接池配置
ERP_DB_MAX_OPEN_CONNS=100
ERP_DB_MAX_IDLE_CONNS=10
ERP_DB_CONN_MAX_LIFETIME=1h
ERP_DB_CONN_MAX_IDLE_TIME=10m

# 数据库迁移配置
ERP_DB_MIGRATION_AUTO_MIGRATE=true
ERP_DB_MIGRATION_MIGRATIONS_DIR=migrations/postgres
ERP_DB_MIGRATION_FAIL_ON_ERROR=true
ERP_DB_MIGRATION_LOG_MIGRATIONS=true

# ===========================================
# Redis配置
# ===========================================

# Redis连接配置
ERP_REDIS_HOST=localhost
ERP_REDIS_PORT=6379
ERP_REDIS_PASSWORD=
ERP_REDIS_DB=0
ERP_REDIS_POOL_SIZE=10

# ===========================================
# 消息队列配置
# ===========================================

# 消息队列提供者
ERP_MQ_PROVIDER=redis

# Redis消息队列配置
ERP_MQ_REDIS_ADDR=localhost:6379
ERP_MQ_REDIS_PASSWORD=
ERP_MQ_REDIS_DB=1
ERP_MQ_REDIS_POOL_SIZE=10
ERP_MQ_CONSUMER_GROUP=erp-consumer-group

# 延时队列配置
ERP_MQ_DELAYED_QUEUE_ENABLE=true
ERP_MQ_DELAYED_QUEUE_SCAN_INTERVAL=1s

# 消息持久化配置
ERP_MQ_RETENTION_TTL=24h
ERP_MQ_RETENTION_MAX_LEN=10000
ERP_MQ_RETENTION_APPROXIMATE=true

# Kafka配置（未来使用）
ERP_MQ_KAFKA_ENABLE=false
ERP_MQ_KAFKA_BROKERS=localhost:9092
ERP_MQ_KAFKA_CONSUMER_GROUP=erp-consumer-group
ERP_MQ_KAFKA_BATCH_SIZE=100
ERP_MQ_KAFKA_TIMEOUT=30s

# 消息处理配置
ERP_MQ_MAX_RETRIES=3
ERP_MQ_RETRY_DELAY=5s
ERP_MQ_DEAD_LETTER_TOPIC=dlq

# ===========================================
# 日志配置
# ===========================================

# 日志级别: debug, info, warn, error
ERP_LOG_LEVEL=info
# 日志格式: json, text
ERP_LOG_FORMAT=json
# 日志输出: stdout, stderr, file
ERP_LOG_OUTPUT=stdout
# 日志文件路径（当output为file时）
ERP_LOG_FILE=
# 日志轮转配置
ERP_LOG_MAX_SIZE=100
ERP_LOG_MAX_AGE=7
ERP_LOG_MAX_BACKUPS=5
ERP_LOG_COMPRESS=true

# ===========================================
# 安全配置
# ===========================================

# JWT密钥配置（生产环境必须设置）
ERP_JWT_SECRET=development_jwt_secret_key_change_in_production_very_long_secret_at_least_32_characters
ERP_JWT_ISSUER=nine-wings-erp
ERP_JWT_AUDIENCE=erp-users
ERP_JWT_EXPIRATION=24h

# CORS配置
ERP_CORS_ALLOWED_ORIGINS=*
ERP_CORS_ALLOWED_METHODS=GET,POST,PUT,DELETE,PATCH,OPTIONS
ERP_CORS_ALLOWED_HEADERS=*

# 限流配置
ERP_RATE_LIMIT_ENABLE=true
ERP_RATE_LIMIT_RPS=100
ERP_RATE_LIMIT_BURST=200

# ===========================================
# 监控配置
# ===========================================

# 指标配置
ERP_METRICS_ENABLE=true
ERP_METRICS_PATH=/metrics
ERP_METRICS_PORT=9100

# 链路追踪配置
ERP_TRACING_ENABLE=false
ERP_TRACING_ENDPOINT=
ERP_TRACING_SERVICE=nine-wings-erp

# 健康检查配置
ERP_HEALTH_ENABLE=true
ERP_HEALTH_PATH=/health

# ===========================================
# 业务配置
# ===========================================

# 雪花ID机器ID（0-1023）
ERP_SNOWFLAKE_MACHINE_ID=1

# 文件上传配置
ERP_UPLOAD_MAX_SIZE=10485760
ERP_UPLOAD_ALLOWED_TYPES=image/jpeg,image/png,image/gif,application/pdf
ERP_UPLOAD_PATH=./var/uploads

# 缓存配置
ERP_CACHE_DEFAULT_TTL=1h
ERP_CACHE_MAX_KEYS=10000

# ===========================================
# 环境特定配置示例
# ===========================================

# 开发环境专用配置
# ERP_LOG_LEVEL=debug
# ERP_LOG_FORMAT=text
# ERP_RATE_LIMIT_ENABLE=false
# ERP_TRACING_ENABLE=true
# ERP_UPLOAD_PATH=./var/uploads/dev

# 生产环境专用配置
# ERP_DB_HOST=prod-postgres.internal
# ERP_DB_SSL_MODE=require
# ERP_REDIS_HOST=prod-redis.internal
# ERP_JWT_SECRET=VERY_STRONG_SECRET_KEY_FOR_PRODUCTION_AT_LEAST_64_CHARACTERS
# ERP_LOG_LEVEL=warn
# ERP_LOG_FORMAT=json
# ERP_SNOWFLAKE_MACHINE_ID=10

# 测试环境专用配置
# ERP_DB_NAME=erp_test
# ERP_LOG_LEVEL=error
# ERP_CACHE_DEFAULT_TTL=1m 