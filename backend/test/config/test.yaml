# 测试环境配置
database:
  driver: postgres
  host: localhost
  port: 5432
  database: erp_test
  username: postgres
  password: postgres
  ssl_mode: disable
  max_open_conns: 10
  max_idle_conns: 5
  use_test_container: true
  migration_path: ../../migrations/postgres
  fixtures_path: ../fixtures/data

cache:
  redis:
    host: localhost
    port: 6379
    password: ""
    database: 1
    use_test_container: true

http:
  port: 8081
  host: localhost
  timeout: 30

auth:
  jwt_secret: test-jwt-secret-key-for-testing-only
  token_expiry: 3600
  refresh_expiry: 86400

external:
  payment_service:
    base_url: http://localhost:8082
    api_key: test-api-key
    timeout: 30

logging:
  level: debug
  format: json
  output: stdout
