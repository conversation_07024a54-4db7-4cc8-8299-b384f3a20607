# 九翼跨境电商ERP系统 - 测试文档

## 概述

本目录包含九翼跨境电商ERP系统的所有测试用例，遵循标准的Go测试实践和项目的六边形架构原则。

## 目录结构

```
test/
├── unit/                     # 单元测试
│   ├── user_entity_test.go      # 用户实体测试
│   ├── user_service_test.go     # 用户服务测试 
│   ├── user_repository_unit_test.go  # 用户仓储单元测试
│   ├── product_entity_test.go   # 商品实体测试
│   └── performance_benchmark_test.go  # 性能基准测试
├── integration/              # 集成测试
│   ├── security_integration_test.go # 安全认证集成测试
│   ├── security_handler_test.go     # 安全认证处理器测试
│   ├── user_repository_test.go      # 用户仓储集成测试
│   └── user_workflow_test.go        # 用户工作流测试
├── mock/                     # Mock对象
│   └── user_repository_mock.go      # 用户仓储Mock
├── testutil/                 # 测试工具
│   └── fixtures.go                  # 测试数据构建器
├── Makefile                  # 测试运行工具
└── README.md                 # 本文档
```

## 测试分类

### 1. 单元测试 (Unit Tests)

**位置**: `test/unit/`

**特点**:
- 测试单个组件的行为
- 使用Mock对象隔离外部依赖
- 快速执行，覆盖率要求90%+

**已实现的测试**:

#### 用户实体测试 (`user_entity_test.go`)
- 测试用户实体的所有业务方法
- 包含状态转换、验证、领域事件等测试
- 覆盖775行代码，包含13个主要测试方法

#### 用户服务测试 (`user_service_test.go`)
- 测试用户服务层的业务逻辑
- 使用Mock仓储进行依赖隔离
- 包含创建、查询、更新、删除等操作测试

#### 商品实体测试 (`product_entity_test.go`)
- 测试商品实体的业务规则
- 包含价格更新、状态管理、属性操作等测试
- 验证商品的生命周期管理

#### 性能基准测试 (`performance_benchmark_test.go`)
- 测试关键操作的性能表现
- 包含并发安全性测试
- 内存使用和CPU性能分析

### 2. 集成测试 (Integration Tests)

**位置**: `test/integration/`

**特点**:
- 测试组件间的交互
- 使用真实或接近真实的依赖
- 验证端到端的业务流程

**已实现的测试**:

#### 认证集成测试 (`auth_integration_test.go`)
- 完整的用户注册和登录流程测试
- 包含JWT token管理和刷新
- 测试权限验证和授权流程

#### 用户工作流测试 (`user_workflow_test.go`)
- 测试完整的用户生命周期
- 包含多租户操作验证
- 状态转换和事件处理测试

### 3. Mock对象

**位置**: `test/mock/`

**用途**:
- 提供测试专用的依赖实现
- 支持精确的行为验证
- 使用gomock框架生成

#### 用户仓储Mock (`user_repository_mock.go`)
- 实现用户仓储接口的所有方法
- 支持期望设置和调用验证
- 用于单元测试中的依赖注入

### 4. 测试工具

**位置**: `test/testutil/`

#### 测试数据构建器 (`fixtures.go`)
- **UserBuilder**: 流式API构建测试用户
- **预定义函数**: ValidUser(), PendingUser(), etc.
- **批量创建**: MultipleUsers(), UsersForTenant()
- **断言辅助**: AssertUserEqual(), AssertUserActive(), etc.
- **随机数据**: RandomUsername(), RandomEmail(), etc.

**使用示例**:
```go
// 创建测试用户
user := testutil.NewUserBuilder().
    WithUsername("testuser").
    WithEmail("<EMAIL>").
    Active().
    Build()

// 批量创建
users := testutil.UsersForTenant("tenant-1", 5)

// 预定义用户
activeUser := testutil.ValidUser()
pendingUser := testutil.PendingUser()
```

## 测试运行

### 使用Makefile（推荐）

```bash
# 查看所有可用命令
make help

# 运行所有测试
make test

# 运行单元测试
make test-unit

# 运行集成测试
make test-integration

# 性能基准测试
make test-benchmark

# 生成覆盖率报告
make test-coverage

# 监控文件变化自动测试
make test-watch
```

### 直接使用Go命令

```bash
# 运行所有测试
go test ./test/unit/... ./test/integration/...

# 运行单元测试
go test ./test/unit/...

# 运行集成测试
go test ./test/integration/...

# 运行性能测试
go test -bench=. ./test/unit/...

# 生成覆盖率
go test -coverprofile=coverage.out ./test/...
go tool cover -html=coverage.out
```

## 测试标准

### 命名规范

**测试文件**: `{package}_{type}_test.go`
- `user_entity_test.go` - 用户实体测试
- `product_service_test.go` - 商品服务测试
- `security_integration_test.go` - 安全认证集成测试

**测试函数**: `Test{Function}_{Scenario}_{Expected}`
```go
func TestUser_Activate_ValidUser_Success(t *testing.T)
func TestUser_Activate_AlreadyActive_ReturnsError(t *testing.T)
func TestCreateProduct_InvalidSKU_ReturnsValidationError(t *testing.T)
```

### 覆盖率要求

- **Domain层**: 最低90%覆盖率
- **Application层**: 最低85%覆盖率
- **Adapters层**: 最低75%覆盖率
- **整体项目**: 最低80%覆盖率

### 测试结构

使用AAA模式 (Arrange-Act-Assert):

```go
func TestUserService_CreateUser_ValidInput_Success(t *testing.T) {
    // Arrange (准备)
    ctrl := gomock.NewController(t)
    defer ctrl.Finish()
    
    mockRepo := mock.NewMockUserRepository(ctrl)
    mockRepo.EXPECT().Save(gomock.Any(), gomock.Any()).Return(nil)
    
    service := NewUserService(mockRepo)
    request := &CreateUserRequest{Username: "test", Email: "<EMAIL>"}
    
    // Act (执行)
    result, err := service.CreateUser(context.Background(), request)
    
    // Assert (验证)
    assert.NoError(t, err)
    assert.NotNil(t, result)
    assert.Equal(t, "test", result.Username)
}
```

## 最佳实践

### 1. 测试隔离
- 每个测试独立运行
- 使用Setup/Teardown清理数据
- Mock外部依赖

### 2. 测试数据
- 使用Builder模式创建测试数据
- 提供合理的默认值
- 支持定制化配置

### 3. 错误测试
- 测试正常流程和异常流程
- 验证错误类型和错误消息
- 覆盖边界条件

### 4. 性能测试
- 基准测试关键操作
- 监控内存使用
- 检测竞态条件

### 5. 集成测试
- 测试真实的业务场景
- 验证组件间交互
- 使用测试套件组织复杂测试

## 持续集成

测试在CI/CD流水线中自动运行：

```yaml
# GitHub Actions 示例
- name: Run Tests
  run: |
    go test -race -coverprofile=coverage.out ./test/...
    go tool cover -func=coverage.out
```

## 故障排除

### 常见问题

1. **Mock期望不匹配**
   - 检查Mock设置的参数和实际调用是否一致
   - 使用`gomock.Any()`匹配任意参数

2. **测试数据冲突**
   - 确保每个测试使用独立的测试数据
   - 在TearDown中清理测试数据

3. **并发测试失败**
   - 检查是否有竞态条件
   - 使用`go test -race`检测

### 调试技巧

```go
// 打印调试信息
t.Logf("User status: %v", user.Status)

// 跳过长时间运行的测试
if testing.Short() {
    t.Skip("Skipping long-running test in short mode")
}

// 临时失败的测试
t.Skip("TODO: Fix this test")
```

## 扩展测试

### 添加新的测试

1. 在相应目录创建测试文件
2. 遵循命名规范
3. 添加必要的Mock对象
4. 更新Makefile（如需要）

### 测试模板

```go
package unit

import (
    "testing"
    "context"
    
    "backend/test/testutil"
    "github.com/stretchr/testify/assert"
    "github.com/golang/mock/gomock"
)

func TestNewFeature_ValidInput_Success(t *testing.T) {
    // Setup
    ctrl := gomock.NewController(t)
    defer ctrl.Finish()
    
    // Test implementation
    // ...
    
    // Assertions
    assert.NoError(t, err)
}
```

## 相关资源

- [Go Testing包文档](https://pkg.go.dev/testing)
- [Testify断言库](https://github.com/stretchr/testify)
- [GoMock框架](https://github.com/golang/mock)
- [项目架构文档](../README.md)

---

**维护者**: 九翼跨境电商ERP开发团队  
**更新时间**: 2024年1月  
**版本**: 1.0.0 