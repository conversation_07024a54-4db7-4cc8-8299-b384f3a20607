# 九翼跨境电商ERP系统 - 测试Makefile

.PHONY: test test-unit test-integration test-benchmark test-coverage test-watch test-clean help

# 默认目标
help:
	@echo "九翼跨境电商ERP系统 - 测试命令"
	@echo ""
	@echo "可用命令:"
	@echo "  test              - 运行所有测试"
	@echo "  test-unit         - 运行单元测试"
	@echo "  test-integration  - 运行集成测试"
	@echo "  test-benchmark    - 运行性能基准测试"
	@echo "  test-coverage     - 运行测试并生成覆盖率报告"
	@echo "  test-watch        - 监控文件变化并自动运行测试"
	@echo "  test-clean        - 清理测试缓存和临时文件"
	@echo "  test-verbose      - 运行详细输出的测试"
	@echo "  test-short        - 运行快速测试（跳过长时间运行的测试）"
	@echo "  generate-mocks    - 生成Mock对象"
	@echo ""

# 运行所有测试
test:
	@echo "运行所有测试..."
	cd .. && go test -v ./test/unit/... ./test/integration/...

# 运行单元测试
test-unit:
	@echo "运行单元测试..."
	cd .. && go test -v ./test/unit/...

# 运行集成测试
test-integration:
	@echo "运行集成测试..."
	cd .. && go test -v ./test/integration/...

# 运行性能基准测试
test-benchmark:
	@echo "运行性能基准测试..."
	cd .. && go test -bench=. -benchmem ./test/unit/...

# 运行测试并生成覆盖率报告
test-coverage:
	@echo "运行测试并生成覆盖率报告..."
	cd .. && go test -coverprofile=coverage.out ./test/unit/... ./test/integration/...
	cd .. && go tool cover -html=coverage.out -o test/coverage.html
	@echo "覆盖率报告已生成: test/coverage.html"

# 运行详细输出的测试
test-verbose:
	@echo "运行详细输出的测试..."
	cd .. && go test -v -race ./test/unit/... ./test/integration/...

# 运行快速测试
test-short:
	@echo "运行快速测试..."
	cd .. && go test -short ./test/unit/... ./test/integration/...

# 监控文件变化并自动运行测试
test-watch:
	@echo "监控文件变化并自动运行测试..."
	@echo "需要安装 entr 工具: brew install entr (macOS) 或 apt-get install entr (Ubuntu)"
	cd .. && find . -name "*.go" -not -path "./vendor/*" | entr -c go test ./test/unit/...

# 清理测试缓存和临时文件
test-clean:
	@echo "清理测试缓存和临时文件..."
	cd .. && go clean -testcache
	rm -f coverage.out coverage.html

# 生成Mock对象
generate-mocks:
	@echo "生成Mock对象..."
	cd .. && go generate ./...
	@echo "Mock对象生成完成"

# 特定包的测试
test-user:
	@echo "运行用户相关测试..."
	cd .. && go test -v ./test/unit/user*_test.go ./test/integration/user*_test.go

test-security:
	@echo "运行安全认证相关测试..."
	cd .. && go test -v ./test/unit/security*_test.go ./test/integration/security*_test.go

# 并行测试
test-parallel:
	@echo "运行并行测试..."
	cd .. && go test -parallel 4 ./test/unit/... ./test/integration/...

# 测试特定功能
test-entity:
	@echo "运行实体测试..."
	cd .. && go test -v ./test/unit/*entity*_test.go

test-service:
	@echo "运行服务测试..."
	cd .. && go test -v ./test/unit/*service*_test.go

test-repository:
	@echo "运行仓储测试..."
	cd .. && go test -v ./test/unit/*repository*_test.go ./test/integration/*repository*_test.go

# 性能相关测试
test-performance:
	@echo "运行性能测试..."
	cd .. && go test -bench=. -benchtime=5s -count=3 ./test/unit/performance*_test.go

test-memory:
	@echo "运行内存使用测试..."
	cd .. && go test -bench=. -benchmem -memprofile=mem.prof ./test/unit/...

test-cpu:
	@echo "运行CPU性能测试..."
	cd .. && go test -bench=. -cpuprofile=cpu.prof ./test/unit/...

# 测试环境设置
setup-test-env:
	@echo "设置测试环境..."
	@echo "安装测试依赖..."
	cd .. && go mod tidy
	cd .. && go get -t ./...

# 检查测试覆盖率
check-coverage:
	@echo "检查测试覆盖率..."
	cd .. && go test -coverprofile=coverage.out ./...
	cd .. && go tool cover -func=coverage.out | tail -1
	@echo "覆盖率报告已检查"

# 运行竞态条件检测
test-race:
	@echo "运行竞态条件检测..."
	cd .. && go test -race ./test/unit/... ./test/integration/...

# 运行失败重试测试
test-retry:
	@echo "运行失败重试测试..."
	cd .. && go test -count=3 ./test/unit/... ./test/integration/...

# 测试超时设置
test-timeout:
	@echo "运行带超时的测试..."
	cd .. && go test -timeout=30s ./test/unit/... ./test/integration/...

# 只运行失败的测试
test-failfast:
	@echo "运行快速失败测试..."
	cd .. && go test -failfast ./test/unit/... ./test/integration/...

# 生成测试报告
test-report:
	@echo "生成测试报告..."
	cd .. && go test -json ./test/unit/... ./test/integration/... > test/test-report.json
	@echo "测试报告已生成: test/test-report.json"

# 验证测试文件
validate-tests:
	@echo "验证测试文件..."
	cd .. && go vet ./test/...
	cd .. && gofmt -l ./test/
	@echo "测试文件验证完成"

# Docker中运行测试
test-docker:
	@echo "在Docker中运行测试..."
	cd .. && docker run --rm -v $(PWD):/app -w /app golang:1.21 go test ./test/unit/... ./test/integration/...

# 生成测试标签
test-tags:
	@echo "使用构建标签运行测试..."
	cd .. && go test -tags=unit ./test/unit/...
	cd .. && go test -tags=integration ./test/integration/...

# 压力测试
stress-test:
	@echo "运行压力测试..."
	cd .. && go test -bench=. -benchtime=60s -count=5 ./test/unit/performance*_test.go

# 测试依赖检查
check-test-deps:
	@echo "检查测试依赖..."
	cd .. && go list -f '{{.TestImports}}' ./test/unit/... ./test/integration/...

# 清理并重新运行所有测试
test-fresh: test-clean test
	@echo "清理后重新运行测试完成"

# 生成测试覆盖率徽章
coverage-badge:
	@echo "生成测试覆盖率徽章..."
	cd .. && go test -coverprofile=coverage.out ./...
	cd .. && go tool cover -func=coverage.out | grep total | awk '{print $$3}' > test/coverage.txt
	@echo "覆盖率文件已生成: test/coverage.txt"

# 测试统计信息
test-stats:
	@echo "生成测试统计信息..."
	@echo "单元测试文件数量:"
	@find unit/ -name "*_test.go" | wc -l
	@echo "集成测试文件数量:"
	@find integration/ -name "*_test.go" | wc -l
	@echo "Mock文件数量:"
	@find mock/ -name "*.go" | wc -l
	@echo "测试工具文件数量:"
	@find testutil/ -name "*.go" | wc -l 