# Mockery配置文件
# 用于统一管理Mock生成配置

# 全局配置
all: true
keeptree: false
with-expecter: true
mockname: "Mock{{.InterfaceName}}"
filename: "{{.MockName}}.go"
outpkg: "{{.PackageName}}_mocks"
structname: "{{.MockName}}"

# 包配置
packages:
  # 领域层
  backend/internal/domain/user/repository:
    config:
      all: true
      dir: "{{.InterfaceDir}}"
      output: "./domain/user"
      outpkg: "user_mocks"
      
  backend/internal/domain/user/service:
    config:
      all: true
      dir: "{{.InterfaceDir}}"
      output: "./domain/user"
      outpkg: "user_mocks"
      
  backend/internal/domain/tenant/repository:
    config:
      all: true
      dir: "{{.InterfaceDir}}"
      output: "./domain/tenant"
      outpkg: "tenant_mocks"
      
  backend/internal/domain/tenant/service:
    config:
      all: true
      dir: "{{.InterfaceDir}}"
      output: "./domain/tenant"
      outpkg: "tenant_mocks"
      
  backend/internal/domain/product/repository:
    config:
      all: true
      dir: "{{.InterfaceDir}}"
      output: "./domain/product"
      outpkg: "product_mocks"
      
  backend/internal/domain/product/service:
    config:
      all: true
      dir: "{{.InterfaceDir}}"
      output: "./domain/product"
      outpkg: "product_mocks"

  # 应用层
  backend/internal/application/usecase:
    config:
      all: true
      dir: "{{.InterfaceDir}}"
      output: "./application/usecase"
      outpkg: "usecase_mocks"
      
  backend/internal/application/service:
    config:
      all: true
      dir: "{{.InterfaceDir}}"
      output: "./application/service"
      outpkg: "service_mocks"

  # 基础设施层
  backend/pkg/infrastructure/database:
    config:
      all: true
      dir: "{{.InterfaceDir}}"
      output: "./infrastructure/database"
      outpkg: "database_mocks"
      
  backend/pkg/infrastructure/cache:
    config:
      all: true
      dir: "{{.InterfaceDir}}"
      output: "./infrastructure/cache"
      outpkg: "cache_mocks"
      
  backend/pkg/infrastructure/messaging:
    config:
      all: true
      dir: "{{.InterfaceDir}}"
      output: "./infrastructure/messaging"
      outpkg: "messaging_mocks"

  # 共享组件
  backend/pkg/shared/logger:
    config:
      all: true
      dir: "{{.InterfaceDir}}"
      output: "./shared"
      outpkg: "shared_mocks"
      
  backend/pkg/shared/metrics:
    config:
      all: true
      dir: "{{.InterfaceDir}}"
      output: "./shared"
      outpkg: "shared_mocks"
      
  backend/pkg/shared/id:
    config:
      all: true
      dir: "{{.InterfaceDir}}"
      output: "./shared"
      outpkg: "shared_mocks"
