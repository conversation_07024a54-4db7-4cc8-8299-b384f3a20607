// Code generated by mockery v2.53.4. DO NOT EDIT.

package mock

import (
	"context"
	"time"

	repository "backend/internal/domain/auth/repository"
	"github.com/stretchr/testify/mock"
)

// TokenCacheRepository is an autogenerated mock type for the TokenCacheRepository type
type TokenCacheRepository struct {
	mock.Mock
}

// AddToBlacklist provides a mock function with given fields: ctx, jti, expiresAt
func (_m *TokenCacheRepository) AddToBlacklist(ctx context.Context, jti string, expiresAt time.Time) error {
	ret := _m.Called(ctx, jti, expiresAt)

	if len(ret) == 0 {
		panic("no return value specified for AddToBlacklist")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func(context.Context, string, time.Time) error); ok {
		r0 = rf(ctx, jti, expiresAt)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// AddUserToken provides a mock function with given fields: ctx, userID, tenantID, jti
func (_m *TokenCacheRepository) AddUserToken(ctx context.Context, userID string, tenantID string, jti string) error {
	ret := _m.Called(ctx, userID, tenantID, jti)

	if len(ret) == 0 {
		panic("no return value specified for AddUserToken")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func(context.Context, string, string, string) error); ok {
		r0 = rf(ctx, userID, tenantID, jti)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// BatchRevoke provides a mock function with given fields: ctx, jtis
func (_m *TokenCacheRepository) BatchRevoke(ctx context.Context, jtis []string) error {
	ret := _m.Called(ctx, jtis)

	if len(ret) == 0 {
		panic("no return value specified for BatchRevoke")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func(context.Context, []string) error); ok {
		r0 = rf(ctx, jtis)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// CleanupExpiredTokens provides a mock function with given fields: ctx
func (_m *TokenCacheRepository) CleanupExpiredTokens(ctx context.Context) error {
	ret := _m.Called(ctx)

	if len(ret) == 0 {
		panic("no return value specified for CleanupExpiredTokens")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func(context.Context) error); ok {
		r0 = rf(ctx)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// DeleteTokenInfo provides a mock function with given fields: ctx, jti
func (_m *TokenCacheRepository) DeleteTokenInfo(ctx context.Context, jti string) error {
	ret := _m.Called(ctx, jti)

	if len(ret) == 0 {
		panic("no return value specified for DeleteTokenInfo")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func(context.Context, string) error); ok {
		r0 = rf(ctx, jti)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// GetActiveTokenCount provides a mock function with given fields: ctx, userID, tenantID
func (_m *TokenCacheRepository) GetActiveTokenCount(ctx context.Context, userID string, tenantID string) (int64, error) {
	ret := _m.Called(ctx, userID, tenantID)

	if len(ret) == 0 {
		panic("no return value specified for GetActiveTokenCount")
	}

	var r0 int64
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, string, string) (int64, error)); ok {
		return rf(ctx, userID, tenantID)
	}
	if rf, ok := ret.Get(0).(func(context.Context, string, string) int64); ok {
		r0 = rf(ctx, userID, tenantID)
	} else {
		r0 = ret.Get(0).(int64)
	}

	if rf, ok := ret.Get(1).(func(context.Context, string, string) error); ok {
		r1 = rf(ctx, userID, tenantID)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// GetBlacklistSize provides a mock function with given fields: ctx
func (_m *TokenCacheRepository) GetBlacklistSize(ctx context.Context) (int64, error) {
	ret := _m.Called(ctx)

	if len(ret) == 0 {
		panic("no return value specified for GetBlacklistSize")
	}

	var r0 int64
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context) (int64, error)); ok {
		return rf(ctx)
	}
	if rf, ok := ret.Get(0).(func(context.Context) int64); ok {
		r0 = rf(ctx)
	} else {
		r0 = ret.Get(0).(int64)
	}

	if rf, ok := ret.Get(1).(func(context.Context) error); ok {
		r1 = rf(ctx)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// GetDeviceTokens provides a mock function with given fields: ctx, deviceID
func (_m *TokenCacheRepository) GetDeviceTokens(ctx context.Context, deviceID string) ([]string, error) {
	ret := _m.Called(ctx, deviceID)

	if len(ret) == 0 {
		panic("no return value specified for GetDeviceTokens")
	}

	var r0 []string
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, string) ([]string, error)); ok {
		return rf(ctx, deviceID)
	}
	if rf, ok := ret.Get(0).(func(context.Context, string) []string); ok {
		r0 = rf(ctx, deviceID)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]string)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, string) error); ok {
		r1 = rf(ctx, deviceID)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// GetTokenInfo provides a mock function with given fields: ctx, jti
func (_m *TokenCacheRepository) GetTokenInfo(ctx context.Context, jti string) (*repository.TokenInfo, error) {
	ret := _m.Called(ctx, jti)

	if len(ret) == 0 {
		panic("no return value specified for GetTokenInfo")
	}

	var r0 *repository.TokenInfo
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, string) (*repository.TokenInfo, error)); ok {
		return rf(ctx, jti)
	}
	if rf, ok := ret.Get(0).(func(context.Context, string) *repository.TokenInfo); ok {
		r0 = rf(ctx, jti)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*repository.TokenInfo)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, string) error); ok {
		r1 = rf(ctx, jti)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// GetUserTokens provides a mock function with given fields: ctx, userID, tenantID
func (_m *TokenCacheRepository) GetUserTokens(ctx context.Context, userID string, tenantID string) ([]string, error) {
	ret := _m.Called(ctx, userID, tenantID)

	if len(ret) == 0 {
		panic("no return value specified for GetUserTokens")
	}

	var r0 []string
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, string, string) ([]string, error)); ok {
		return rf(ctx, userID, tenantID)
	}
	if rf, ok := ret.Get(0).(func(context.Context, string, string) []string); ok {
		r0 = rf(ctx, userID, tenantID)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]string)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, string, string) error); ok {
		r1 = rf(ctx, userID, tenantID)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// IsTokenBlacklisted provides a mock function with given fields: ctx, jti
func (_m *TokenCacheRepository) IsTokenBlacklisted(ctx context.Context, jti string) (bool, error) {
	ret := _m.Called(ctx, jti)

	if len(ret) == 0 {
		panic("no return value specified for IsTokenBlacklisted")
	}

	var r0 bool
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, string) (bool, error)); ok {
		return rf(ctx, jti)
	}
	if rf, ok := ret.Get(0).(func(context.Context, string) bool); ok {
		r0 = rf(ctx, jti)
	} else {
		r0 = ret.Get(0).(bool)
	}

	if rf, ok := ret.Get(1).(func(context.Context, string) error); ok {
		r1 = rf(ctx, jti)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// RemoveFromBlacklist provides a mock function with given fields: ctx, jti
func (_m *TokenCacheRepository) RemoveFromBlacklist(ctx context.Context, jti string) error {
	ret := _m.Called(ctx, jti)

	if len(ret) == 0 {
		panic("no return value specified for RemoveFromBlacklist")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func(context.Context, string) error); ok {
		r0 = rf(ctx, jti)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// RemoveUserToken provides a mock function with given fields: ctx, userID, tenantID, jti
func (_m *TokenCacheRepository) RemoveUserToken(ctx context.Context, userID string, tenantID string, jti string) error {
	ret := _m.Called(ctx, userID, tenantID, jti)

	if len(ret) == 0 {
		panic("no return value specified for RemoveUserToken")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func(context.Context, string, string, string) error); ok {
		r0 = rf(ctx, userID, tenantID, jti)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// RevokeAllUserTokens provides a mock function with given fields: ctx, userID, tenantID
func (_m *TokenCacheRepository) RevokeAllUserTokens(ctx context.Context, userID string, tenantID string) error {
	ret := _m.Called(ctx, userID, tenantID)

	if len(ret) == 0 {
		panic("no return value specified for RevokeAllUserTokens")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func(context.Context, string, string) error); ok {
		r0 = rf(ctx, userID, tenantID)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// RevokeDeviceTokens provides a mock function with given fields: ctx, deviceID
func (_m *TokenCacheRepository) RevokeDeviceTokens(ctx context.Context, deviceID string) error {
	ret := _m.Called(ctx, deviceID)

	if len(ret) == 0 {
		panic("no return value specified for RevokeDeviceTokens")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func(context.Context, string) error); ok {
		r0 = rf(ctx, deviceID)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// SaveTokenInfo provides a mock function with given fields: ctx, tokenInfo
func (_m *TokenCacheRepository) SaveTokenInfo(ctx context.Context, tokenInfo *repository.TokenInfo) error {
	ret := _m.Called(ctx, tokenInfo)

	if len(ret) == 0 {
		panic("no return value specified for SaveTokenInfo")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func(context.Context, *repository.TokenInfo) error); ok {
		r0 = rf(ctx, tokenInfo)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// NewTokenCacheRepository creates a new instance of TokenCacheRepository. It also registers a testing interface on the mock and a cleanup function to assert the mocks expectations.
// The first argument is typically a *testing.T value.
func NewTokenCacheRepository(t interface {
	mock.TestingT
	Cleanup(func())
}) *TokenCacheRepository {
	mock := &TokenCacheRepository{}
	mock.Mock.Test(t)

	t.Cleanup(func() { mock.AssertExpectations(t) })

	return mock
} 