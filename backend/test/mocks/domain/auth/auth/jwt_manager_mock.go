// Code generated by mockery v2.53.4. DO NOT EDIT.

package mock

import (
	mock "github.com/stretchr/testify/mock"

	time "time"

	valueobject "backend/internal/domain/auth/valueobject"
)

// JWTManager is an autogenerated mock type for the JWTManager type
type JWTManager struct {
	mock.Mock
}

// GenerateAccessToken provides a mock function with given fields: userID, tenantID, roles
func (_m *JWTManager) GenerateAccessToken(userID string, tenantID string, roles []string) (string, time.Time, error) {
	ret := _m.Called(userID, tenantID, roles)

	if len(ret) == 0 {
		panic("no return value specified for GenerateAccessToken")
	}

	var r0 string
	var r1 time.Time
	var r2 error
	if rf, ok := ret.Get(0).(func(string, string, []string) (string, time.Time, error)); ok {
		return rf(userID, tenantID, roles)
	}
	if rf, ok := ret.Get(0).(func(string, string, []string) string); ok {
		r0 = rf(userID, tenantID, roles)
	} else {
		r0 = ret.Get(0).(string)
	}

	if rf, ok := ret.Get(1).(func(string, string, []string) time.Time); ok {
		r1 = rf(userID, tenantID, roles)
	} else {
		r1 = ret.Get(1).(time.Time)
	}

	if rf, ok := ret.Get(2).(func(string, string, []string) error); ok {
		r2 = rf(userID, tenantID, roles)
	} else {
		r2 = ret.Error(2)
	}

	return r0, r1, r2
}

// GenerateRefreshToken provides a mock function with given fields: userID, tenantID
func (_m *JWTManager) GenerateRefreshToken(userID string, tenantID string) (string, time.Time, error) {
	ret := _m.Called(userID, tenantID)

	if len(ret) == 0 {
		panic("no return value specified for GenerateRefreshToken")
	}

	var r0 string
	var r1 time.Time
	var r2 error
	if rf, ok := ret.Get(0).(func(string, string) (string, time.Time, error)); ok {
		return rf(userID, tenantID)
	}
	if rf, ok := ret.Get(0).(func(string, string) string); ok {
		r0 = rf(userID, tenantID)
	} else {
		r0 = ret.Get(0).(string)
	}

	if rf, ok := ret.Get(1).(func(string, string) time.Time); ok {
		r1 = rf(userID, tenantID)
	} else {
		r1 = ret.Get(1).(time.Time)
	}

	if rf, ok := ret.Get(2).(func(string, string) error); ok {
		r2 = rf(userID, tenantID)
	} else {
		r2 = ret.Error(2)
	}

	return r0, r1, r2
}

// GeneratePreAuthToken provides a mock function with given fields: userID
func (_m *JWTManager) GeneratePreAuthToken(userID string) (string, time.Time, error) {
	ret := _m.Called(userID)

	if len(ret) == 0 {
		panic("no return value specified for GeneratePreAuthToken")
	}

	var r0 string
	var r1 time.Time
	var r2 error
	if rf, ok := ret.Get(0).(func(string) (string, time.Time, error)); ok {
		return rf(userID)
	}
	if rf, ok := ret.Get(0).(func(string) string); ok {
		r0 = rf(userID)
	} else {
		r0 = ret.Get(0).(string)
	}

	if rf, ok := ret.Get(1).(func(string) time.Time); ok {
		r1 = rf(userID)
	} else {
		r1 = ret.Get(1).(time.Time)
	}

	if rf, ok := ret.Get(2).(func(string) error); ok {
		r2 = rf(userID)
	} else {
		r2 = ret.Error(2)
	}

	return r0, r1, r2
}

// ValidateToken provides a mock function with given fields: tokenString
func (_m *JWTManager) ValidateToken(tokenString string) (*valueobject.JWTClaims, error) {
	ret := _m.Called(tokenString)

	if len(ret) == 0 {
		panic("no return value specified for ValidateToken")
	}

	var r0 *valueobject.JWTClaims
	var r1 error
	if rf, ok := ret.Get(0).(func(string) (*valueobject.JWTClaims, error)); ok {
		return rf(tokenString)
	}
	if rf, ok := ret.Get(0).(func(string) *valueobject.JWTClaims); ok {
		r0 = rf(tokenString)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*valueobject.JWTClaims)
		}
	}

	if rf, ok := ret.Get(1).(func(string) error); ok {
		r1 = rf(tokenString)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// ExtractClaims provides a mock function with given fields: tokenString
func (_m *JWTManager) ExtractClaims(tokenString string) (*valueobject.JWTClaims, error) {
	ret := _m.Called(tokenString)

	if len(ret) == 0 {
		panic("no return value specified for ExtractClaims")
	}

	var r0 *valueobject.JWTClaims
	var r1 error
	if rf, ok := ret.Get(0).(func(string) (*valueobject.JWTClaims, error)); ok {
		return rf(tokenString)
	}
	if rf, ok := ret.Get(0).(func(string) *valueobject.JWTClaims); ok {
		r0 = rf(tokenString)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*valueobject.JWTClaims)
		}
	}

	if rf, ok := ret.Get(1).(func(string) error); ok {
		r1 = rf(tokenString)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// ExtractJTI provides a mock function with given fields: tokenString
func (_m *JWTManager) ExtractJTI(tokenString string) (string, error) {
	ret := _m.Called(tokenString)

	if len(ret) == 0 {
		panic("no return value specified for ExtractJTI")
	}

	var r0 string
	var r1 error
	if rf, ok := ret.Get(0).(func(string) (string, error)); ok {
		return rf(tokenString)
	}
	if rf, ok := ret.Get(0).(func(string) string); ok {
		r0 = rf(tokenString)
	} else {
		r0 = ret.Get(0).(string)
	}

	if rf, ok := ret.Get(1).(func(string) error); ok {
		r1 = rf(tokenString)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// GetTokenExpiration provides a mock function with given fields: tokenString
func (_m *JWTManager) GetTokenExpiration(tokenString string) (time.Time, error) {
	ret := _m.Called(tokenString)

	if len(ret) == 0 {
		panic("no return value specified for GetTokenExpiration")
	}

	var r0 time.Time
	var r1 error
	if rf, ok := ret.Get(0).(func(string) (time.Time, error)); ok {
		return rf(tokenString)
	}
	if rf, ok := ret.Get(0).(func(string) time.Time); ok {
		r0 = rf(tokenString)
	} else {
		r0 = ret.Get(0).(time.Time)
	}

	if rf, ok := ret.Get(1).(func(string) error); ok {
		r1 = rf(tokenString)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// NewJWTManager creates a new instance of JWTManager. It also registers a testing interface on the mock and a cleanup function to assert the mocks expectations.
// The first argument is typically a *testing.T value.
func NewJWTManager(t interface {
	mock.TestingT
	Cleanup(func())
}) *JWTManager {
	mock := &JWTManager{}
	mock.Mock.Test(t)

	t.Cleanup(func() { mock.AssertExpectations(t) })

	return mock
}
