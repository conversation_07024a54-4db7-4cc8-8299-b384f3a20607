// Code generated by mockery v2.53.4. DO NOT EDIT.

package mock

import (
	entity "backend/internal/domain/user/entity"
	context "context"

	gorm "gorm.io/gorm"

	mock "github.com/stretchr/testify/mock"
)

// UserRepository is an autogenerated mock type for the UserRepository type
type UserRepository struct {
	mock.Mock
}

// BatchCreate provides a mock function with given fields: ctx, users
func (_m *UserRepository) BatchCreate(ctx context.Context, users []*entity.User) error {
	ret := _m.Called(ctx, users)

	if len(ret) == 0 {
		panic("no return value specified for BatchCreate")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func(context.Context, []*entity.User) error); ok {
		r0 = rf(ctx, users)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// Delete provides a mock function with given fields: ctx, businessID
func (_m *UserRepository) Delete(ctx context.Context, businessID string) error {
	ret := _m.Called(ctx, businessID)

	if len(ret) == 0 {
		panic("no return value specified for Delete")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func(context.Context, string) error); ok {
		r0 = rf(ctx, businessID)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// ExistsByBusinessID provides a mock function with given fields: ctx, businessID
func (_m *UserRepository) ExistsByBusinessID(ctx context.Context, businessID string) (bool, error) {
	ret := _m.Called(ctx, businessID)

	if len(ret) == 0 {
		panic("no return value specified for ExistsByBusinessID")
	}

	var r0 bool
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, string) (bool, error)); ok {
		return rf(ctx, businessID)
	}
	if rf, ok := ret.Get(0).(func(context.Context, string) bool); ok {
		r0 = rf(ctx, businessID)
	} else {
		r0 = ret.Get(0).(bool)
	}

	if rf, ok := ret.Get(1).(func(context.Context, string) error); ok {
		r1 = rf(ctx, businessID)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// ExistsByEmail provides a mock function with given fields: ctx, email
func (_m *UserRepository) ExistsByEmail(ctx context.Context, email string) (bool, error) {
	ret := _m.Called(ctx, email)

	if len(ret) == 0 {
		panic("no return value specified for ExistsByEmail")
	}

	var r0 bool
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, string) (bool, error)); ok {
		return rf(ctx, email)
	}
	if rf, ok := ret.Get(0).(func(context.Context, string) bool); ok {
		r0 = rf(ctx, email)
	} else {
		r0 = ret.Get(0).(bool)
	}

	if rf, ok := ret.Get(1).(func(context.Context, string) error); ok {
		r1 = rf(ctx, email)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// ExistsByPhone provides a mock function with given fields: ctx, phone
func (_m *UserRepository) ExistsByPhone(ctx context.Context, phone string) (bool, error) {
	ret := _m.Called(ctx, phone)

	if len(ret) == 0 {
		panic("no return value specified for ExistsByPhone")
	}

	var r0 bool
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, string) (bool, error)); ok {
		return rf(ctx, phone)
	}
	if rf, ok := ret.Get(0).(func(context.Context, string) bool); ok {
		r0 = rf(ctx, phone)
	} else {
		r0 = ret.Get(0).(bool)
	}

	if rf, ok := ret.Get(1).(func(context.Context, string) error); ok {
		r1 = rf(ctx, phone)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// FindByBusinessID provides a mock function with given fields: ctx, businessID
func (_m *UserRepository) FindByBusinessID(ctx context.Context, businessID string) (*entity.User, error) {
	ret := _m.Called(ctx, businessID)

	if len(ret) == 0 {
		panic("no return value specified for FindByBusinessID")
	}

	var r0 *entity.User
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, string) (*entity.User, error)); ok {
		return rf(ctx, businessID)
	}
	if rf, ok := ret.Get(0).(func(context.Context, string) *entity.User); ok {
		r0 = rf(ctx, businessID)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*entity.User)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, string) error); ok {
		r1 = rf(ctx, businessID)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// FindByBusinessIDs provides a mock function with given fields: ctx, businessIDs
func (_m *UserRepository) FindByBusinessIDs(ctx context.Context, businessIDs []string) ([]*entity.User, error) {
	ret := _m.Called(ctx, businessIDs)

	if len(ret) == 0 {
		panic("no return value specified for FindByBusinessIDs")
	}

	var r0 []*entity.User
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, []string) ([]*entity.User, error)); ok {
		return rf(ctx, businessIDs)
	}
	if rf, ok := ret.Get(0).(func(context.Context, []string) []*entity.User); ok {
		r0 = rf(ctx, businessIDs)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]*entity.User)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, []string) error); ok {
		r1 = rf(ctx, businessIDs)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// FindByTechID provides a mock function with given fields: ctx, techID
func (_m *UserRepository) FindByTechID(ctx context.Context, techID int64) (*entity.User, error) {
	ret := _m.Called(ctx, techID)

	if len(ret) == 0 {
		panic("no return value specified for FindByTechID")
	}

	var r0 *entity.User
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, int64) (*entity.User, error)); ok {
		return rf(ctx, techID)
	}
	if rf, ok := ret.Get(0).(func(context.Context, int64) *entity.User); ok {
		r0 = rf(ctx, techID)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*entity.User)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, int64) error); ok {
		r1 = rf(ctx, techID)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// FindByTenantID provides a mock function with given fields: ctx, tenantID, limit, offset
func (_m *UserRepository) FindByTenantID(ctx context.Context, tenantID string, limit int, offset int) ([]*entity.User, int64, error) {
	ret := _m.Called(ctx, tenantID, limit, offset)

	if len(ret) == 0 {
		panic("no return value specified for FindByTenantID")
	}

	var r0 []*entity.User
	var r1 int64
	var r2 error
	if rf, ok := ret.Get(0).(func(context.Context, string, int, int) ([]*entity.User, int64, error)); ok {
		return rf(ctx, tenantID, limit, offset)
	}
	if rf, ok := ret.Get(0).(func(context.Context, string, int, int) []*entity.User); ok {
		r0 = rf(ctx, tenantID, limit, offset)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]*entity.User)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, string, int, int) int64); ok {
		r1 = rf(ctx, tenantID, limit, offset)
	} else {
		r1 = ret.Get(1).(int64)
	}

	if rf, ok := ret.Get(2).(func(context.Context, string, int, int) error); ok {
		r2 = rf(ctx, tenantID, limit, offset)
	} else {
		r2 = ret.Error(2)
	}

	return r0, r1, r2
}

// FindUsersByRoleID provides a mock function with given fields: ctx, tenantID, roleBusinessID, limit, offset
func (_m *UserRepository) FindUsersByRoleID(ctx context.Context, tenantID string, roleBusinessID string, limit int, offset int) ([]*entity.User, int64, error) {
	ret := _m.Called(ctx, tenantID, roleBusinessID, limit, offset)

	if len(ret) == 0 {
		panic("no return value specified for FindUsersByRoleID")
	}

	var r0 []*entity.User
	var r1 int64
	var r2 error
	if rf, ok := ret.Get(0).(func(context.Context, string, string, int, int) ([]*entity.User, int64, error)); ok {
		return rf(ctx, tenantID, roleBusinessID, limit, offset)
	}
	if rf, ok := ret.Get(0).(func(context.Context, string, string, int, int) []*entity.User); ok {
		r0 = rf(ctx, tenantID, roleBusinessID, limit, offset)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]*entity.User)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, string, string, int, int) int64); ok {
		r1 = rf(ctx, tenantID, roleBusinessID, limit, offset)
	} else {
		r1 = ret.Get(1).(int64)
	}

	if rf, ok := ret.Get(2).(func(context.Context, string, string, int, int) error); ok {
		r2 = rf(ctx, tenantID, roleBusinessID, limit, offset)
	} else {
		r2 = ret.Error(2)
	}

	return r0, r1, r2
}

// Save provides a mock function with given fields: ctx, user
func (_m *UserRepository) Save(ctx context.Context, user *entity.User) error {
	ret := _m.Called(ctx, user)

	if len(ret) == 0 {
		panic("no return value specified for Save")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func(context.Context, *entity.User) error); ok {
		r0 = rf(ctx, user)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// SaveWithTx provides a mock function with given fields: ctx, tx, user
func (_m *UserRepository) SaveWithTx(ctx context.Context, tx *gorm.DB, user *entity.User) error {
	ret := _m.Called(ctx, tx, user)

	if len(ret) == 0 {
		panic("no return value specified for SaveWithTx")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func(context.Context, *gorm.DB, *entity.User) error); ok {
		r0 = rf(ctx, tx, user)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// SearchUsers provides a mock function with given fields: ctx, tenantID, keyword, limit, offset
func (_m *UserRepository) SearchUsers(ctx context.Context, tenantID string, keyword string, limit int, offset int) ([]*entity.User, int64, error) {
	ret := _m.Called(ctx, tenantID, keyword, limit, offset)

	if len(ret) == 0 {
		panic("no return value specified for SearchUsers")
	}

	var r0 []*entity.User
	var r1 int64
	var r2 error
	if rf, ok := ret.Get(0).(func(context.Context, string, string, int, int) ([]*entity.User, int64, error)); ok {
		return rf(ctx, tenantID, keyword, limit, offset)
	}
	if rf, ok := ret.Get(0).(func(context.Context, string, string, int, int) []*entity.User); ok {
		r0 = rf(ctx, tenantID, keyword, limit, offset)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]*entity.User)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, string, string, int, int) int64); ok {
		r1 = rf(ctx, tenantID, keyword, limit, offset)
	} else {
		r1 = ret.Get(1).(int64)
	}

	if rf, ok := ret.Get(2).(func(context.Context, string, string, int, int) error); ok {
		r2 = rf(ctx, tenantID, keyword, limit, offset)
	} else {
		r2 = ret.Error(2)
	}

	return r0, r1, r2
}

// Update provides a mock function with given fields: ctx, user
func (_m *UserRepository) Update(ctx context.Context, user *entity.User) error {
	ret := _m.Called(ctx, user)

	if len(ret) == 0 {
		panic("no return value specified for Update")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func(context.Context, *entity.User) error); ok {
		r0 = rf(ctx, user)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// FindByEmail provides a mock function with given fields: ctx, email
func (_m *UserRepository) FindByEmail(ctx context.Context, email string) (*entity.User, error) {
	ret := _m.Called(ctx, email)

	if len(ret) == 0 {
		panic("no return value specified for FindByEmail")
	}

	var r0 *entity.User
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, string) (*entity.User, error)); ok {
		return rf(ctx, email)
	}
	if rf, ok := ret.Get(0).(func(context.Context, string) *entity.User); ok {
		r0 = rf(ctx, email)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*entity.User)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, string) error); ok {
		r1 = rf(ctx, email)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// FindByPhone provides a mock function with given fields: ctx, phone
func (_m *UserRepository) FindByPhone(ctx context.Context, phone string) (*entity.User, error) {
	ret := _m.Called(ctx, phone)

	if len(ret) == 0 {
		panic("no return value specified for FindByPhone")
	}

	var r0 *entity.User
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, string) (*entity.User, error)); ok {
		return rf(ctx, phone)
	}
	if rf, ok := ret.Get(0).(func(context.Context, string) *entity.User); ok {
		r0 = rf(ctx, phone)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*entity.User)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, string) error); ok {
		r1 = rf(ctx, phone)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// NewUserRepository creates a new instance of UserRepository. It also registers a testing interface on the mock and a cleanup function to assert the mocks expectations.
// The first argument is typically a *testing.T value.
func NewUserRepository(t interface {
	mock.TestingT
	Cleanup(func())
}) *UserRepository {
	mock := &UserRepository{}
	mock.Mock.Test(t)

	t.Cleanup(func() { mock.AssertExpectations(t) })

	return mock
}
