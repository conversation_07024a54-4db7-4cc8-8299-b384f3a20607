package mock

import (
	"context"

	"backend/internal/domain/user/entity"

	"github.com/stretchr/testify/mock"
)

// UserTenantRepository is a mock type for the UserTenantRepository type
type UserTenantRepository struct {
	mock.Mock
}

// FindByUserBusinessID mocks the FindByUserBusinessID method
func (m *UserTenantRepository) FindByUserBusinessID(ctx context.Context, userBusinessID string) ([]*entity.UserTenant, error) {
	args := m.Called(ctx, userBusinessID)
	if args.Get(0) == nil {
		return nil, args.Error(1)
	}
	return args.Get(0).([]*entity.UserTenant), args.Error(1)
}

// FindByTenantBusinessID mocks the FindByTenantBusinessID method
func (m *UserTenantRepository) FindByTenantBusinessID(ctx context.Context, tenantBusinessID string) ([]*entity.UserTenant, error) {
	args := m.Called(ctx, tenantBusinessID)
	if args.Get(0) == nil {
		return nil, args.Error(1)
	}
	return args.Get(0).([]*entity.UserTenant), args.Error(1)
}

// FindByUserAndTenant mocks the FindByUserAndTenant method
func (m *UserTenantRepository) FindByUserAndTenant(ctx context.Context, userBusinessID, tenantBusinessID string) (*entity.UserTenant, error) {
	args := m.Called(ctx, userBusinessID, tenantBusinessID)
	if args.Get(0) == nil {
		return nil, args.Error(1)
	}
	return args.Get(0).(*entity.UserTenant), args.Error(1)
}

// Create mocks the Create method
func (m *UserTenantRepository) Create(ctx context.Context, userTenant *entity.UserTenant) error {
	args := m.Called(ctx, userTenant)
	return args.Error(0)
}

// Delete mocks the Delete method
func (m *UserTenantRepository) Delete(ctx context.Context, businessID string) error {
	args := m.Called(ctx, businessID)
	return args.Error(0)
}
