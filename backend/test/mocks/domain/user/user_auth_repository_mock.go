// Code generated by mockery v2.53.4. DO NOT EDIT.

package mock

import (
	entity "backend/internal/domain/auth/entity"
	context "context"

	gorm "gorm.io/gorm"

	mock "github.com/stretchr/testify/mock"
)

// UserAuthRepository is an autogenerated mock type for the UserAuthRepository type
type UserAuthRepository struct {
	mock.Mock
}

// BatchCreate provides a mock function with given fields: ctx, auths
func (_m *UserAuthRepository) BatchCreate(ctx context.Context, auths []*entity.UserAuth) error {
	ret := _m.Called(ctx, auths)

	if len(ret) == 0 {
		panic("no return value specified for BatchCreate")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func(context.Context, []*entity.UserAuth) error); ok {
		r0 = rf(ctx, auths)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// CountActiveByTenantID provides a mock function with given fields: ctx, tenantID
func (_m *UserAuthRepository) CountActiveByTenantID(ctx context.Context, tenantID string) (int64, error) {
	ret := _m.Called(ctx, tenantID)

	if len(ret) == 0 {
		panic("no return value specified for CountActiveByTenantID")
	}

	var r0 int64
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, string) (int64, error)); ok {
		return rf(ctx, tenantID)
	}
	if rf, ok := ret.Get(0).(func(context.Context, string) int64); ok {
		r0 = rf(ctx, tenantID)
	} else {
		r0 = ret.Get(0).(int64)
	}

	if rf, ok := ret.Get(1).(func(context.Context, string) error); ok {
		r1 = rf(ctx, tenantID)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// CountByTenantID provides a mock function with given fields: ctx, tenantID
func (_m *UserAuthRepository) CountByTenantID(ctx context.Context, tenantID string) (int64, error) {
	ret := _m.Called(ctx, tenantID)

	if len(ret) == 0 {
		panic("no return value specified for CountByTenantID")
	}

	var r0 int64
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, string) (int64, error)); ok {
		return rf(ctx, tenantID)
	}
	if rf, ok := ret.Get(0).(func(context.Context, string) int64); ok {
		r0 = rf(ctx, tenantID)
	} else {
		r0 = ret.Get(0).(int64)
	}

	if rf, ok := ret.Get(1).(func(context.Context, string) error); ok {
		r1 = rf(ctx, tenantID)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// Delete provides a mock function with given fields: ctx, businessID
func (_m *UserAuthRepository) Delete(ctx context.Context, businessID string) error {
	ret := _m.Called(ctx, businessID)

	if len(ret) == 0 {
		panic("no return value specified for Delete")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func(context.Context, string) error); ok {
		r0 = rf(ctx, businessID)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// ExistsByIdentifier provides a mock function with given fields: ctx, identityType, identifier
func (_m *UserAuthRepository) ExistsByIdentifier(ctx context.Context, identityType string, identifier string) (bool, error) {
	ret := _m.Called(ctx, identityType, identifier)

	if len(ret) == 0 {
		panic("no return value specified for ExistsByIdentifier")
	}

	var r0 bool
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, string, string) (bool, error)); ok {
		return rf(ctx, identityType, identifier)
	}
	if rf, ok := ret.Get(0).(func(context.Context, string, string) bool); ok {
		r0 = rf(ctx, identityType, identifier)
	} else {
		r0 = ret.Get(0).(bool)
	}

	if rf, ok := ret.Get(1).(func(context.Context, string, string) error); ok {
		r1 = rf(ctx, identityType, identifier)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// FindActiveByEmail provides a mock function with given fields: ctx, tenantID, email
func (_m *UserAuthRepository) FindActiveByEmail(ctx context.Context, tenantID string, email string) (*entity.UserAuth, error) {
	ret := _m.Called(ctx, tenantID, email)

	if len(ret) == 0 {
		panic("no return value specified for FindActiveByEmail")
	}

	var r0 *entity.UserAuth
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, string, string) (*entity.UserAuth, error)); ok {
		return rf(ctx, tenantID, email)
	}
	if rf, ok := ret.Get(0).(func(context.Context, string, string) *entity.UserAuth); ok {
		r0 = rf(ctx, tenantID, email)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*entity.UserAuth)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, string, string) error); ok {
		r1 = rf(ctx, tenantID, email)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// FindActiveByUsername provides a mock function with given fields: ctx, username
func (_m *UserAuthRepository) FindActiveByUsername(ctx context.Context, username string) (*entity.UserAuth, error) {
	ret := _m.Called(ctx, username)

	if len(ret) == 0 {
		panic("no return value specified for FindActiveByUsername")
	}

	var r0 *entity.UserAuth
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, string) (*entity.UserAuth, error)); ok {
		return rf(ctx, username)
	}
	if rf, ok := ret.Get(0).(func(context.Context, string) *entity.UserAuth); ok {
		r0 = rf(ctx, username)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*entity.UserAuth)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, string) error); ok {
		r1 = rf(ctx, username)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// FindActiveByUsernameInTenant provides a mock function with given fields: ctx, tenantID, username
func (_m *UserAuthRepository) FindActiveByUsernameInTenant(ctx context.Context, tenantID string, username string) (*entity.UserAuth, error) {
	ret := _m.Called(ctx, tenantID, username)

	if len(ret) == 0 {
		panic("no return value specified for FindActiveByUsernameInTenant")
	}

	var r0 *entity.UserAuth
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, string, string) (*entity.UserAuth, error)); ok {
		return rf(ctx, tenantID, username)
	}
	if rf, ok := ret.Get(0).(func(context.Context, string, string) *entity.UserAuth); ok {
		r0 = rf(ctx, tenantID, username)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*entity.UserAuth)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, string, string) error); ok {
		r1 = rf(ctx, tenantID, username)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// FindByBusinessID provides a mock function with given fields: ctx, businessID
func (_m *UserAuthRepository) FindByBusinessID(ctx context.Context, businessID string) (*entity.UserAuth, error) {
	ret := _m.Called(ctx, businessID)

	if len(ret) == 0 {
		panic("no return value specified for FindByBusinessID")
	}

	var r0 *entity.UserAuth
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, string) (*entity.UserAuth, error)); ok {
		return rf(ctx, businessID)
	}
	if rf, ok := ret.Get(0).(func(context.Context, string) *entity.UserAuth); ok {
		r0 = rf(ctx, businessID)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*entity.UserAuth)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, string) error); ok {
		r1 = rf(ctx, businessID)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// FindByEmail provides a mock function with given fields: ctx, tenantID, email
func (_m *UserAuthRepository) FindByEmail(ctx context.Context, tenantID string, email string) (*entity.UserAuth, error) {
	ret := _m.Called(ctx, tenantID, email)

	if len(ret) == 0 {
		panic("no return value specified for FindByEmail")
	}

	var r0 *entity.UserAuth
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, string, string) (*entity.UserAuth, error)); ok {
		return rf(ctx, tenantID, email)
	}
	if rf, ok := ret.Get(0).(func(context.Context, string, string) *entity.UserAuth); ok {
		r0 = rf(ctx, tenantID, email)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*entity.UserAuth)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, string, string) error); ok {
		r1 = rf(ctx, tenantID, email)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// FindByIdentifier provides a mock function with given fields: ctx, identityType, identifier
func (_m *UserAuthRepository) FindByIdentifier(ctx context.Context, identityType string, identifier string) (*entity.UserAuth, error) {
	ret := _m.Called(ctx, identityType, identifier)

	if len(ret) == 0 {
		panic("no return value specified for FindByIdentifier")
	}

	var r0 *entity.UserAuth
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, string, string) (*entity.UserAuth, error)); ok {
		return rf(ctx, identityType, identifier)
	}
	if rf, ok := ret.Get(0).(func(context.Context, string, string) *entity.UserAuth); ok {
		r0 = rf(ctx, identityType, identifier)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*entity.UserAuth)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, string, string) error); ok {
		r1 = rf(ctx, identityType, identifier)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// FindByTechID provides a mock function with given fields: ctx, techID
func (_m *UserAuthRepository) FindByTechID(ctx context.Context, techID int64) (*entity.UserAuth, error) {
	ret := _m.Called(ctx, techID)

	if len(ret) == 0 {
		panic("no return value specified for FindByTechID")
	}

	var r0 *entity.UserAuth
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, int64) (*entity.UserAuth, error)); ok {
		return rf(ctx, techID)
	}
	if rf, ok := ret.Get(0).(func(context.Context, int64) *entity.UserAuth); ok {
		r0 = rf(ctx, techID)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*entity.UserAuth)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, int64) error); ok {
		r1 = rf(ctx, techID)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// FindByTenantID provides a mock function with given fields: ctx, tenantID, limit, offset
func (_m *UserAuthRepository) FindByTenantID(ctx context.Context, tenantID string, limit int, offset int) ([]*entity.UserAuth, int64, error) {
	ret := _m.Called(ctx, tenantID, limit, offset)

	if len(ret) == 0 {
		panic("no return value specified for FindByTenantID")
	}

	var r0 []*entity.UserAuth
	var r1 int64
	var r2 error
	if rf, ok := ret.Get(0).(func(context.Context, string, int, int) ([]*entity.UserAuth, int64, error)); ok {
		return rf(ctx, tenantID, limit, offset)
	}
	if rf, ok := ret.Get(0).(func(context.Context, string, int, int) []*entity.UserAuth); ok {
		r0 = rf(ctx, tenantID, limit, offset)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]*entity.UserAuth)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, string, int, int) int64); ok {
		r1 = rf(ctx, tenantID, limit, offset)
	} else {
		r1 = ret.Get(1).(int64)
	}

	if rf, ok := ret.Get(2).(func(context.Context, string, int, int) error); ok {
		r2 = rf(ctx, tenantID, limit, offset)
	} else {
		r2 = ret.Error(2)
	}

	return r0, r1, r2
}

// FindByTenantIDs provides a mock function with given fields: ctx, tenantIDs
func (_m *UserAuthRepository) FindByTenantIDs(ctx context.Context, tenantIDs []string) ([]*entity.UserAuth, error) {
	ret := _m.Called(ctx, tenantIDs)

	if len(ret) == 0 {
		panic("no return value specified for FindByTenantIDs")
	}

	var r0 []*entity.UserAuth
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, []string) ([]*entity.UserAuth, error)); ok {
		return rf(ctx, tenantIDs)
	}
	if rf, ok := ret.Get(0).(func(context.Context, []string) []*entity.UserAuth); ok {
		r0 = rf(ctx, tenantIDs)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]*entity.UserAuth)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, []string) error); ok {
		r1 = rf(ctx, tenantIDs)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// FindByUserBusinessID provides a mock function with given fields: ctx, userBusinessID
func (_m *UserAuthRepository) FindByUserBusinessID(ctx context.Context, userBusinessID string) ([]*entity.UserAuth, error) {
	ret := _m.Called(ctx, userBusinessID)

	if len(ret) == 0 {
		panic("no return value specified for FindByUserBusinessID")
	}

	var r0 []*entity.UserAuth
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, string) ([]*entity.UserAuth, error)); ok {
		return rf(ctx, userBusinessID)
	}
	if rf, ok := ret.Get(0).(func(context.Context, string) []*entity.UserAuth); ok {
		r0 = rf(ctx, userBusinessID)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]*entity.UserAuth)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, string) error); ok {
		r1 = rf(ctx, userBusinessID)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// FindByUserID provides a mock function with given fields: ctx, userID
func (_m *UserAuthRepository) FindByUserID(ctx context.Context, userID string) (*entity.UserAuth, error) {
	ret := _m.Called(ctx, userID)

	if len(ret) == 0 {
		panic("no return value specified for FindByUserID")
	}

	var r0 *entity.UserAuth
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, string) (*entity.UserAuth, error)); ok {
		return rf(ctx, userID)
	}
	if rf, ok := ret.Get(0).(func(context.Context, string) *entity.UserAuth); ok {
		r0 = rf(ctx, userID)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*entity.UserAuth)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, string) error); ok {
		r1 = rf(ctx, userID)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// FindByUserIDs provides a mock function with given fields: ctx, userIDs
func (_m *UserAuthRepository) FindByUserIDs(ctx context.Context, userIDs []string) ([]*entity.UserAuth, error) {
	ret := _m.Called(ctx, userIDs)

	if len(ret) == 0 {
		panic("no return value specified for FindByUserIDs")
	}

	var r0 []*entity.UserAuth
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, []string) ([]*entity.UserAuth, error)); ok {
		return rf(ctx, userIDs)
	}
	if rf, ok := ret.Get(0).(func(context.Context, []string) []*entity.UserAuth); ok {
		r0 = rf(ctx, userIDs)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]*entity.UserAuth)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, []string) error); ok {
		r1 = rf(ctx, userIDs)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// FindByUsername provides a mock function with given fields: ctx, tenantID, username
func (_m *UserAuthRepository) FindByUsername(ctx context.Context, tenantID string, username string) (*entity.UserAuth, error) {
	ret := _m.Called(ctx, tenantID, username)

	if len(ret) == 0 {
		panic("no return value specified for FindByUsername")
	}

	var r0 *entity.UserAuth
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, string, string) (*entity.UserAuth, error)); ok {
		return rf(ctx, tenantID, username)
	}
	if rf, ok := ret.Get(0).(func(context.Context, string, string) *entity.UserAuth); ok {
		r0 = rf(ctx, tenantID, username)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*entity.UserAuth)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, string, string) error); ok {
		r1 = rf(ctx, tenantID, username)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// Save provides a mock function with given fields: ctx, auth
func (_m *UserAuthRepository) Save(ctx context.Context, auth *entity.UserAuth) error {
	ret := _m.Called(ctx, auth)

	if len(ret) == 0 {
		panic("no return value specified for Save")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func(context.Context, *entity.UserAuth) error); ok {
		r0 = rf(ctx, auth)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// SaveWithTx provides a mock function with given fields: ctx, tx, auth
func (_m *UserAuthRepository) SaveWithTx(ctx context.Context, tx *gorm.DB, auth *entity.UserAuth) error {
	ret := _m.Called(ctx, tx, auth)

	if len(ret) == 0 {
		panic("no return value specified for SaveWithTx")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func(context.Context, *gorm.DB, *entity.UserAuth) error); ok {
		r0 = rf(ctx, tx, auth)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// Update provides a mock function with given fields: ctx, auth
func (_m *UserAuthRepository) Update(ctx context.Context, auth *entity.UserAuth) error {
	ret := _m.Called(ctx, auth)

	if len(ret) == 0 {
		panic("no return value specified for Update")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func(context.Context, *entity.UserAuth) error); ok {
		r0 = rf(ctx, auth)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// NewUserAuthRepository creates a new instance of UserAuthRepository. It also registers a testing interface on the mock and a cleanup function to assert the mocks expectations.
// The first argument is typically a *testing.T value.
func NewUserAuthRepository(t interface {
	mock.TestingT
	Cleanup(func())
}) *UserAuthRepository {
	mock := &UserAuthRepository{}
	mock.Mock.Test(t)

	t.Cleanup(func() { mock.AssertExpectations(t) })

	return mock
}
