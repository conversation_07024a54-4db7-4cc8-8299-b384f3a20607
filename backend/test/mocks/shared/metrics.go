package shared_mocks

import (
	"time"

	"github.com/stretchr/testify/mock"
)

// MockMetricsCollector 模拟指标收集器
type MockMetricsCollector struct {
	mock.Mock
}

// RecordLatency 记录延迟指标
func (m *MockMetricsCollector) RecordLatency(operation string, duration time.Duration) {
	m.Called(operation, duration)
}

// RecordCounter 记录计数器指标
func (m *MockMetricsCollector) RecordCounter(name string, value int64, tags ...string) {
	args := []interface{}{name, value}
	for _, tag := range tags {
		args = append(args, tag)
	}
	m.Called(args...)
}

// RecordGauge 记录仪表盘指标
func (m *MockMetricsCollector) RecordGauge(name string, value float64, tags ...string) {
	args := []interface{}{name, value}
	for _, tag := range tags {
		args = append(args, tag)
	}
	m.Called(args...)
}

// RecordHistogram 记录直方图指标
func (m *MockMetricsCollector) RecordHistogram(name string, value float64, tags ...string) {
	args := []interface{}{name, value}
	for _, tag := range tags {
		args = append(args, tag)
	}
	m.Called(args...)
}

// RecordError 记录错误指标
func (m *MockMetricsCollector) RecordError(operation string, err error) {
	m.Called(operation, err)
}

// RecordHitRate 记录命中率指标
func (m *MockMetricsCollector) RecordHitRate(operation string, hit bool) {
	m.Called(operation, hit)
}

// Increment 增加计数器
func (m *MockMetricsCollector) Increment(name string, tags ...string) {
	args := []interface{}{name}
	for _, tag := range tags {
		args = append(args, tag)
	}
	m.Called(args...)
}

// Decrement 减少计数器
func (m *MockMetricsCollector) Decrement(name string, tags ...string) {
	args := []interface{}{name}
	for _, tag := range tags {
		args = append(args, tag)
	}
	m.Called(args...)
}

// Timer 创建计时器
func (m *MockMetricsCollector) Timer(name string, tags ...string) interface{} {
	args := []interface{}{name}
	for _, tag := range tags {
		args = append(args, tag)
	}
	result := m.Called(args...)
	return result.Get(0)
}

// Close 关闭指标收集器
func (m *MockMetricsCollector) Close() error {
	args := m.Called()
	return args.Error(0)
}
