package shared_mocks

import (
	"context"

	"github.com/stretchr/testify/mock"
)

// MockLogger 模拟日志记录器
type MockLogger struct {
	mock.Mock
}

// Debug 记录调试日志
func (m *<PERSON>ckLogger) Debug(ctx context.Context, msg string, keysAndValues ...interface{}) {
	args := []interface{}{ctx, msg}
	args = append(args, keysAndValues...)
	m.Called(args...)
}

// Info 记录信息日志
func (m *MockLogger) Info(ctx context.Context, msg string, keysAndValues ...interface{}) {
	args := []interface{}{ctx, msg}
	args = append(args, keysAndValues...)
	m.Called(args...)
}

// Warn 记录警告日志
func (m *MockLogger) Warn(ctx context.Context, msg string, keysAndValues ...interface{}) {
	args := []interface{}{ctx, msg}
	args = append(args, keysAndValues...)
	m.Called(args...)
}

// Error 记录错误日志
func (m *MockLogger) Error(ctx context.Context, msg string, keysAndValues ...interface{}) {
	args := []interface{}{ctx, msg}
	args = append(args, keysAndValues...)
	m.Called(args...)
}

// Fatal 记录致命错误日志
func (m *MockLogger) Fatal(ctx context.Context, msg string, keysAndValues ...interface{}) {
	args := []interface{}{ctx, msg}
	args = append(args, keysAndValues...)
	m.Called(args...)
}

// With 添加字段
func (m *MockLogger) With(keysAndValues ...interface{}) interface{} {
	args := m.Called(keysAndValues...)
	return args.Get(0)
}

// WithContext 添加上下文
func (m *MockLogger) WithContext(ctx context.Context) interface{} {
	args := m.Called(ctx)
	return args.Get(0)
}
