package mocks

//go:generate mockery --config=.mockery.yaml

// 这个文件用于生成所有的Mock对象
// 运行 go generate ./test/mocks 来生成所有Mock

// 领域层接口Mock生成
//go:generate mockery --name=UserRepository --dir=../../internal/domain/user/repository --output=./domain/user --outpkg=user_mocks
//go:generate mockery --name=UserService --dir=../../internal/domain/user/service --output=./domain/user --outpkg=user_mocks
//go:generate mockery --name=TenantRepository --dir=../../internal/domain/tenant/repository --output=./domain/tenant --outpkg=tenant_mocks
//go:generate mockery --name=TenantService --dir=../../internal/domain/tenant/service --output=./domain/tenant --outpkg=tenant_mocks
//go:generate mockery --name=ProductRepository --dir=../../internal/domain/product/repository --output=./domain/product --outpkg=product_mocks
//go:generate mockery --name=ProductService --dir=../../internal/domain/product/service --output=./domain/product --outpkg=product_mocks

// 应用层接口Mock生成
//go:generate mockery --name=UserRegistrationUseCase --dir=../../internal/application/usecase/user --output=./application/usecase --outpkg=usecase_mocks
//go:generate mockery --name=AuthenticationUseCase --dir=../../internal/application/usecase/auth --output=./application/usecase --outpkg=usecase_mocks
//go:generate mockery --name=TenantManagementUseCase --dir=../../internal/application/usecase/tenant --output=./application/usecase --outpkg=usecase_mocks
//go:generate mockery --name=ProductManagementUseCase --dir=../../internal/application/usecase/product --output=./application/usecase --outpkg=usecase_mocks

// 基础设施层接口Mock生成
//go:generate mockery --name=Database --dir=../../pkg/infrastructure/database --output=./infrastructure/database --outpkg=database_mocks
//go:generate mockery --name=Manager --dir=../../pkg/infrastructure/database --output=./infrastructure/database --outpkg=database_mocks
//go:generate mockery --name=Transaction --dir=../../pkg/infrastructure/database --output=./infrastructure/database --outpkg=database_mocks
//go:generate mockery --name=CacheClient --dir=../../pkg/infrastructure/cache --output=./infrastructure/cache --outpkg=cache_mocks
//go:generate mockery --name=RedisClient --dir=../../pkg/infrastructure/cache/redis --output=./infrastructure/cache --outpkg=cache_mocks
//go:generate mockery --name=MessageBroker --dir=../../pkg/infrastructure/messaging --output=./infrastructure/messaging --outpkg=messaging_mocks
//go:generate mockery --name=EventPublisher --dir=../../pkg/infrastructure/messaging --output=./infrastructure/messaging --outpkg=messaging_mocks

// 共享组件接口Mock生成
//go:generate mockery --name=Logger --dir=../../pkg/shared/logger --output=./shared --outpkg=shared_mocks
//go:generate mockery --name=MetricsCollector --dir=../../pkg/shared/metrics --output=./shared --outpkg=shared_mocks
//go:generate mockery --name=IDGenerator --dir=../../pkg/shared/id --output=./shared --outpkg=shared_mocks
//go:generate mockery --name=TimeProvider --dir=../../pkg/shared/time --output=./shared --outpkg=shared_mocks
//go:generate mockery --name=Validator --dir=../../pkg/shared/validator --output=./shared --outpkg=shared_mocks

// 从旧的test/mock/目录迁移的Mock生成指令
//go:generate mockery --srcpkg=../../pkg/infrastructure/auth --name=JWTManager --output=./domain/auth --filename=jwt_manager_mock.go --all
//go:generate mockery --srcpkg=../../pkg/infrastructure/auth --name=CasbinManager --output=./infrastructure/auth --filename=casbin_manager_mock.go --all
