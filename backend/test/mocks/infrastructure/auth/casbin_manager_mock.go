// Code generated by mockery v2.53.4. DO NOT EDIT.

package mock

import (
	context "context"

	mock "github.com/stretchr/testify/mock"
)

// CasbinManager is an autogenerated mock type for the CasbinManager type
type CasbinManager struct {
	mock.Mock
}

// CheckPermission provides a mock function with given fields: ctx, subject, domain, object, action
func (_m *CasbinManager) CheckPermission(ctx context.Context, subject string, domain string, object string, action string) (bool, error) {
	ret := _m.Called(ctx, subject, domain, object, action)
	
	var r0 bool
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, string, string, string, string) (bool, error)); ok {
		return rf(ctx, subject, domain, object, action)
	}
	if rf, ok := ret.Get(0).(func(context.Context, string, string, string, string) bool); ok {
		r0 = rf(ctx, subject, domain, object, action)
	} else {
		r0 = ret.Get(0).(bool)
	}
	
	if rf, ok := ret.Get(1).(func(context.Context, string, string, string, string) error); ok {
		r1 = rf(ctx, subject, domain, object, action)
	} else {
		r1 = ret.Error(1)
	}
	
	return r0, r1
}

// AddPolicy provides a mock function with given fields: ctx, subject, domain, object, action
func (_m *CasbinManager) AddPolicy(ctx context.Context, subject string, domain string, object string, action string) error {
	ret := _m.Called(ctx, subject, domain, object, action)
	
	var r0 error
	if rf, ok := ret.Get(0).(func(context.Context, string, string, string, string) error); ok {
		r0 = rf(ctx, subject, domain, object, action)
	} else {
		r0 = ret.Error(0)
	}
	
	return r0
}

// RemovePolicy provides a mock function with given fields: ctx, subject, domain, object, action
func (_m *CasbinManager) RemovePolicy(ctx context.Context, subject string, domain string, object string, action string) error {
	ret := _m.Called(ctx, subject, domain, object, action)
	
	var r0 error
	if rf, ok := ret.Get(0).(func(context.Context, string, string, string, string) error); ok {
		r0 = rf(ctx, subject, domain, object, action)
	} else {
		r0 = ret.Error(0)
	}
	
	return r0
}

// AddRoleForUser provides a mock function with given fields: ctx, user, role, domain
func (_m *CasbinManager) AddRoleForUser(ctx context.Context, user string, role string, domain string) error {
	ret := _m.Called(ctx, user, role, domain)
	
	var r0 error
	if rf, ok := ret.Get(0).(func(context.Context, string, string, string) error); ok {
		r0 = rf(ctx, user, role, domain)
	} else {
		r0 = ret.Error(0)
	}
	
	return r0
}

// DeleteRoleForUser provides a mock function with given fields: ctx, user, role, domain
func (_m *CasbinManager) DeleteRoleForUser(ctx context.Context, user string, role string, domain string) error {
	ret := _m.Called(ctx, user, role, domain)
	
	var r0 error
	if rf, ok := ret.Get(0).(func(context.Context, string, string, string) error); ok {
		r0 = rf(ctx, user, role, domain)
	} else {
		r0 = ret.Error(0)
	}
	
	return r0
}

// GetRolesForUser provides a mock function with given fields: ctx, user, domain
func (_m *CasbinManager) GetRolesForUser(ctx context.Context, user string, domain string) ([]string, error) {
	ret := _m.Called(ctx, user, domain)

	if len(ret) == 0 {
		panic("no return value specified for GetRolesForUser")
	}

	var r0 []string
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, string, string) ([]string, error)); ok {
		return rf(ctx, user, domain)
	}
	if rf, ok := ret.Get(0).(func(context.Context, string, string) []string); ok {
		r0 = rf(ctx, user, domain)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]string)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, string, string) error); ok {
		r1 = rf(ctx, user, domain)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// GetUsersForRole provides a mock function with given fields: ctx, role, domain
func (_m *CasbinManager) GetUsersForRole(ctx context.Context, role string, domain string) ([]string, error) {
	ret := _m.Called(ctx, role, domain)
	
	var r0 []string
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, string, string) ([]string, error)); ok {
		return rf(ctx, role, domain)
	}
	if rf, ok := ret.Get(0).(func(context.Context, string, string) []string); ok {
		r0 = rf(ctx, role, domain)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]string)
		}
	}
	
	if rf, ok := ret.Get(1).(func(context.Context, string, string) error); ok {
		r1 = rf(ctx, role, domain)
	} else {
		r1 = ret.Error(1)
	}
	
	return r0, r1
}

// GetPermissionsForUser provides a mock function with given fields: ctx, user, domain
func (_m *CasbinManager) GetPermissionsForUser(ctx context.Context, user string, domain string) ([][]string, error) {
	ret := _m.Called(ctx, user, domain)
	
	var r0 [][]string
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, string, string) ([][]string, error)); ok {
		return rf(ctx, user, domain)
	}
	if rf, ok := ret.Get(0).(func(context.Context, string, string) [][]string); ok {
		r0 = rf(ctx, user, domain)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([][]string)
		}
	}
	
	if rf, ok := ret.Get(1).(func(context.Context, string, string) error); ok {
		r1 = rf(ctx, user, domain)
	} else {
		r1 = ret.Error(1)
	}
	
	return r0, r1
}

// LoadPolicy provides a mock function with given fields:
func (_m *CasbinManager) LoadPolicy() error {
	ret := _m.Called()
	
	var r0 error
	if rf, ok := ret.Get(0).(func() error); ok {
		r0 = rf()
	} else {
		r0 = ret.Error(0)
	}
	
	return r0
}

// SavePolicy provides a mock function with given fields:
func (_m *CasbinManager) SavePolicy() error {
	ret := _m.Called()
	
	var r0 error
	if rf, ok := ret.Get(0).(func() error); ok {
		r0 = rf()
	} else {
		r0 = ret.Error(0)
	}
	
	return r0
}

// NewCasbinManager creates a new instance of CasbinManager. It also registers a testing interface on the mock and a cleanup function to assert the mocks expectations.
// The first argument is typically a *testing.T value.
func NewCasbinManager(t interface {
	mock.TestingT
	Cleanup(func())
}) *CasbinManager {
	mock := &CasbinManager{}
	mock.Mock.Test(t)

	t.Cleanup(func() { mock.AssertExpectations(t) })

	return mock
}
