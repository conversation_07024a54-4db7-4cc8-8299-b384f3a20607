package application

import (
	"context"
	"errors"
	"testing"

	"backend/internal/application/usecase/user"
	"backend/internal/domain/user/entity"
	"backend/internal/shared/transaction"
	pkgErrors "backend/pkg/common/errors"
	"backend/pkg/infrastructure/logger"
	"backend/pkg/infrastructure/snowflake"
	userMocks "backend/test/mocks/domain/user"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/mock"
	"github.com/stretchr/testify/require"
)

// MockTransactionManager 事务管理器的Mock实现
type MockTransactionManager struct {
	mock.Mock
}

// 确保实现了transaction.TransactionManager接口
var _ transaction.TransactionManager = (*MockTransactionManager)(nil)

func (m *MockTransactionManager) ExecuteInTransaction(ctx context.Context, fn func(context.Context) error) error {
	args := m.Called(ctx, fn)
	if fn != nil {
		// 如果测试中设置了Run函数，则会执行fn
		return args.Error(0)
	}
	return args.Error(0)
}

// setupUserRegistrationUseCaseTest 为每个测试创建测试环境和Mock对象
func setupUserRegistrationUseCaseTest(t *testing.T) (
	*user.UserRegistrationUseCase,
	*MockTransactionManager,
	*userMocks.UserRepository,
	*userMocks.UserAuthRepository,
	*userMocks.UserTenantRepository,
) {
	// 创建Mock对象
	mockTxManager := new(MockTransactionManager)
	mockUserRepo := new(userMocks.UserRepository)
	mockAuthRepo := new(userMocks.UserAuthRepository)
	mockUserTenantRepo := new(userMocks.UserTenantRepository)

	// 创建雪花算法生成器
	mockSnowflake := snowflake.NewGenerator(0)

	// 创建空日志记录器（测试环境）
	noopLogger, _ := logger.NewZapLogger(&logger.Config{Level: "error"})

	// 创建用例实例
	uc := user.NewUserRegistrationUseCase(
		mockTxManager,
		mockUserRepo,
		mockAuthRepo,
		mockUserTenantRepo,
		mockSnowflake,
		noopLogger,
	)

	// 清理函数，验证Mock对象的调用
	t.Cleanup(func() {
		mockTxManager.AssertExpectations(t)
		mockUserRepo.AssertExpectations(t)
		mockAuthRepo.AssertExpectations(t)
		mockUserTenantRepo.AssertExpectations(t)
	})

	return uc, mockTxManager, mockUserRepo, mockAuthRepo, mockUserTenantRepo
}

func TestUserRegistrationUseCase_Execute(t *testing.T) {
	ctx := context.Background()

	// 准备测试数据
	registerReq := &user.RegisterUserRequest{
		TenantID:  "tenant-123",
		Username:  "testuser",
		Email:     "<EMAIL>",
		Phone:     "1234567890",
		Password:  "password123",
		FirstName: "John",
		LastName:  "Doe",
		Language:  "zh-CN",
		Timezone:  "Asia/Shanghai",
	}

	t.Run("成功注册用户", func(t *testing.T) {
		uc, mockTxManager, mockUserRepo, mockAuthRepo, mockUserTenantRepo := setupUserRegistrationUseCaseTest(t)

		// Mock期望：用户不存在的检查
		mockUserRepo.On("ExistsByEmail", ctx, registerReq.Email).Return(false, nil).Once()
		mockUserRepo.On("ExistsByPhone", ctx, registerReq.Phone).Return(false, nil).Once()

		// 准备事务内部的Mock
		mockUserRepo.On("Save", mock.Anything, mock.AnythingOfType("*entity.User")).
			Return(nil).
			Run(func(args mock.Arguments) {
				// 模拟仓储层设置BusinessID
				user := args.Get(1).(*entity.User)
				user.BusinessID = "generated-user-id-123"
			}).Once()
		mockAuthRepo.On("Save", mock.Anything, mock.AnythingOfType("*entity.UserAuth")).Return(nil).Twice() // 手机和邮箱认证
		mockUserTenantRepo.On("Create", mock.Anything, mock.AnythingOfType("*entity.UserTenant")).Return(nil).Once()

		// Mock期望：事务执行
		mockTxManager.On("ExecuteInTransaction", ctx, mock.AnythingOfType("func(context.Context) error")).
			Return(nil).
			Run(func(args mock.Arguments) {
				// 执行传入的事务函数
				txFunc := args.Get(1).(func(context.Context) error)
				err := txFunc(ctx)
				assert.NoError(t, err)
			}).Once()

		// 执行测试
		result, err := uc.Execute(ctx, registerReq)

		// 验证结果
		require.NoError(t, err)
		require.NotNil(t, result)
		assert.Equal(t, registerReq.Username, result.Username)
		assert.Equal(t, registerReq.Email, result.Email)
		assert.Equal(t, registerReq.Phone, result.Phone)
		assert.Equal(t, registerReq.FirstName, result.FirstName)
		assert.Equal(t, registerReq.LastName, result.LastName)
		assert.NotEmpty(t, result.UserID) // 用户ID应该被生成
	})

	t.Run("邮箱已存在应返回错误", func(t *testing.T) {
		uc, _, mockUserRepo, _, _ := setupUserRegistrationUseCaseTest(t)

		// Mock期望：邮箱已存在
		mockUserRepo.On("ExistsByEmail", ctx, registerReq.Email).Return(true, nil).Once()

		// 执行测试
		result, err := uc.Execute(ctx, registerReq)

		// 验证结果
		assert.Error(t, err)
		assert.Nil(t, result)
		// 验证是否是业务错误类型
		appErr := pkgErrors.As(err)
		assert.NotNil(t, appErr)
		assert.Contains(t, err.Error(), "已存在")
	})

	t.Run("手机号已存在应返回错误", func(t *testing.T) {
		uc, _, mockUserRepo, _, _ := setupUserRegistrationUseCaseTest(t)

		// Mock期望：邮箱不存在，但手机号已存在
		mockUserRepo.On("ExistsByEmail", ctx, registerReq.Email).Return(false, nil).Once()
		mockUserRepo.On("ExistsByPhone", ctx, registerReq.Phone).Return(true, nil).Once()

		// 执行测试
		result, err := uc.Execute(ctx, registerReq)

		// 验证结果
		assert.Error(t, err)
		assert.Nil(t, result)
		// 验证是否是业务错误类型
		appErr := pkgErrors.As(err)
		assert.NotNil(t, appErr)
		assert.Contains(t, err.Error(), "已存在")
	})

	t.Run("检查邮箱存在时数据库错误", func(t *testing.T) {
		uc, _, mockUserRepo, _, _ := setupUserRegistrationUseCaseTest(t)

		// Mock期望：数据库错误
		dbError := errors.New("数据库连接失败")
		mockUserRepo.On("ExistsByEmail", ctx, registerReq.Email).Return(false, dbError).Once()

		// 执行测试
		result, err := uc.Execute(ctx, registerReq)

		// 验证结果
		assert.Error(t, err)
		assert.Nil(t, result)
		assert.Contains(t, err.Error(), "检查邮箱是否存在失败")
	})

	t.Run("事务执行失败", func(t *testing.T) {
		uc, mockTxManager, mockUserRepo, _, _ := setupUserRegistrationUseCaseTest(t)

		// Mock期望：用户不存在的检查通过
		mockUserRepo.On("ExistsByEmail", ctx, registerReq.Email).Return(false, nil).Once()
		mockUserRepo.On("ExistsByPhone", ctx, registerReq.Phone).Return(false, nil).Once()

		// Mock期望：事务执行失败
		txError := errors.New("事务执行失败")
		mockTxManager.On("ExecuteInTransaction", ctx, mock.AnythingOfType("func(context.Context) error")).
			Return(txError).Once()

		// 执行测试
		result, err := uc.Execute(ctx, registerReq)

		// 验证结果
		assert.Error(t, err)
		assert.Nil(t, result)
		assert.Equal(t, txError, err)
	})

}

func TestUserRegistrationUseCase_Execute_EdgeCases(t *testing.T) {
	ctx := context.Background()

	t.Run("邮箱和手机号相同时只创建一个认证记录", func(t *testing.T) {
		uc, mockTxManager, mockUserRepo, mockAuthRepo, mockUserTenantRepo := setupUserRegistrationUseCaseTest(t)

		// 准备测试数据：邮箱和手机号相同
		registerReq := &user.RegisterUserRequest{
			TenantID: "tenant-123",
			Username: "testuser",
			Email:    "<EMAIL>",
			Phone:    "<EMAIL>", // 与邮箱相同
			Password: "password123",
		}

		// Mock期望：用户不存在的检查
		mockUserRepo.On("ExistsByEmail", ctx, registerReq.Email).Return(false, nil).Once()
		mockUserRepo.On("ExistsByPhone", ctx, registerReq.Phone).Return(false, nil).Once()

		// 准备事务内部的Mock - 只应该创建一个认证记录
		mockUserRepo.On("Save", mock.Anything, mock.AnythingOfType("*entity.User")).Return(nil).Once()
		mockAuthRepo.On("Save", mock.Anything, mock.AnythingOfType("*entity.UserAuth")).Return(nil).Once() // 只有一次
		mockUserTenantRepo.On("Create", mock.Anything, mock.AnythingOfType("*entity.UserTenant")).Return(nil).Once()

		// Mock期望：事务执行
		mockTxManager.On("ExecuteInTransaction", ctx, mock.AnythingOfType("func(context.Context) error")).
			Return(nil).
			Run(func(args mock.Arguments) {
				txFunc := args.Get(1).(func(context.Context) error)
				err := txFunc(ctx)
				assert.NoError(t, err)
			}).Once()

		// 执行测试
		result, err := uc.Execute(ctx, registerReq)

		// 验证结果
		require.NoError(t, err)
		require.NotNil(t, result)
	})

	t.Run("最小化必填字段注册", func(t *testing.T) {
		uc, mockTxManager, mockUserRepo, mockAuthRepo, mockUserTenantRepo := setupUserRegistrationUseCaseTest(t)

		// 准备最小化测试数据
		registerReq := &user.RegisterUserRequest{
			TenantID: "tenant-123",
			Username: "testuser",
			Email:    "<EMAIL>",
			Phone:    "1234567890",
			Password: "password123",
			// 可选字段都不填
		}

		// Mock期望设置
		mockUserRepo.On("ExistsByEmail", ctx, registerReq.Email).Return(false, nil).Once()
		mockUserRepo.On("ExistsByPhone", ctx, registerReq.Phone).Return(false, nil).Once()

		// 准备事务内部的Mock
		mockUserRepo.On("Save", mock.Anything, mock.AnythingOfType("*entity.User")).Return(nil).Once()
		mockAuthRepo.On("Save", mock.Anything, mock.AnythingOfType("*entity.UserAuth")).Return(nil).Twice()
		mockUserTenantRepo.On("Create", mock.Anything, mock.AnythingOfType("*entity.UserTenant")).Return(nil).Once()

		mockTxManager.On("ExecuteInTransaction", ctx, mock.AnythingOfType("func(context.Context) error")).
			Return(nil).
			Run(func(args mock.Arguments) {
				txFunc := args.Get(1).(func(context.Context) error)
				err := txFunc(ctx)
				assert.NoError(t, err)
			}).Once()

		// 执行测试
		result, err := uc.Execute(ctx, registerReq)

		// 验证结果
		require.NoError(t, err)
		require.NotNil(t, result)
		assert.Equal(t, registerReq.Username, result.Username)
		assert.Equal(t, registerReq.Email, result.Email)
		assert.Equal(t, registerReq.Phone, result.Phone)
		assert.Empty(t, result.FirstName) // 可选字段应该为空
		assert.Empty(t, result.LastName)
	})
}
