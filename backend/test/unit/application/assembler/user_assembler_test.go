package assembler

import (
	"testing"
	"time"

	"backend/internal/application/assembler"
	"backend/internal/application/dto"
	"backend/internal/domain/user/entity"
	"backend/internal/domain/user/valueobject"
	"backend/internal/shared/types"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/suite"
)

// UserAssemblerTestSuite 用户Assembler测试套件
type UserAssemblerTestSuite struct {
	suite.Suite
	assembler assembler.UserAssembler
}

// SetupTest 设置测试
func (suite *UserAssemblerTestSuite) SetupTest() {
	suite.assembler = assembler.NewUserAssembler()
}

// TestToUserDTO 测试用户实体转DTO
func (suite *UserAssemblerTestSuite) TestToUserDTO() {
	// 准备测试数据
	now := time.Now()
	user := &entity.User{
		GlobalEntity: types.GlobalEntity{
			CoreEntity: types.CoreEntity{
				ID:         123456789,
				BusinessID: "user-123",
				CreatedAt:  now,
				UpdatedAt:  now,
				Version:    1,
			},
		},
		Username: "testuser",
		Email:    "<EMAIL>",
		Phone:    "+1234567890",
		Status:   entity.UserStatusActive,
		Profile: valueobject.UserProfile{
			Avatar:    "https://example.com/avatar.jpg",
			Nickname:  "Test User",
			FirstName: "Test",
			LastName:  "User",
			Language:  "zh-CN",
			Timezone:  "Asia/Shanghai",
		},
	}

	// 执行转换
	result := suite.assembler.ToUserDTO(user)

	// 验证结果
	assert.NotNil(suite.T(), result)
	assert.Equal(suite.T(), "user-123", result.ID)
	assert.Equal(suite.T(), "user-123", result.BusinessID)
	assert.Equal(suite.T(), "testuser", result.Username)
	assert.Equal(suite.T(), "<EMAIL>", result.Email)
	assert.Equal(suite.T(), "+1234567890", result.Phone)
	assert.Equal(suite.T(), "active", result.Status)
	assert.Equal(suite.T(), "https://example.com/avatar.jpg", result.Profile.Avatar)
	assert.Equal(suite.T(), "Test User", result.Profile.Nickname)
	assert.Equal(suite.T(), "Test", result.Profile.FirstName)
	assert.Equal(suite.T(), "User", result.Profile.LastName)
	assert.Equal(suite.T(), "zh-CN", result.Profile.Language)
	assert.Equal(suite.T(), "Asia/Shanghai", result.Profile.Timezone)
	assert.Equal(suite.T(), now, result.CreatedAt)
	assert.Equal(suite.T(), now, result.UpdatedAt)
	assert.Equal(suite.T(), 1, result.Version)
}

// TestToUserDTO_NilUser 测试空用户转换
func (suite *UserAssemblerTestSuite) TestToUserDTO_NilUser() {
	result := suite.assembler.ToUserDTO(nil)
	assert.Nil(suite.T(), result)
}

// TestToCreateUserCommand 测试创建用户命令转换
func (suite *UserAssemblerTestSuite) TestToCreateUserCommand() {
	// 准备测试数据
	dto := &dto.CreateUserRequestDTO{
		TenantID: "tenant-123",
		Username: "newuser",
		Email:    "<EMAIL>",
		Phone:    "+1987654321",
		Password: "password123",
		Profile: dto.UserProfileDTO{
			Avatar:    "https://example.com/new-avatar.jpg",
			Nickname:  "New User",
			FirstName: "New",
			LastName:  "User",
			Language:  "en-US",
			Timezone:  "America/New_York",
		},
		RoleID: "role-456",
	}

	// 执行转换
	result := suite.assembler.ToCreateUserCommand(dto)

	// 验证结果
	assert.NotNil(suite.T(), result)
	assert.Equal(suite.T(), "newuser", result.Username)
	assert.Equal(suite.T(), "<EMAIL>", result.Email)
	assert.Equal(suite.T(), "+1987654321", result.Phone)
	assert.Equal(suite.T(), "tenant-123", result.TenantID)
	assert.Equal(suite.T(), "role-456", result.RoleID)
	assert.Equal(suite.T(), "https://example.com/new-avatar.jpg", result.Profile.Avatar)
	assert.Equal(suite.T(), "New User", result.Profile.Nickname)
	assert.Equal(suite.T(), "New", result.Profile.FirstName)
	assert.Equal(suite.T(), "User", result.Profile.LastName)
	assert.Equal(suite.T(), "en-US", result.Profile.Language)
	assert.Equal(suite.T(), "America/New_York", result.Profile.Timezone)
}

// TestToUserListResponseDTO 测试用户列表响应转换
func (suite *UserAssemblerTestSuite) TestToUserListResponseDTO() {
	// 准备测试数据
	now := time.Now()
	users := []*entity.User{
		{
			GlobalEntity: types.GlobalEntity{
				CoreEntity: types.CoreEntity{
					ID:         1,
					BusinessID: "user-1",
					CreatedAt:  now,
					UpdatedAt:  now,
					Version:    1,
				},
			},
			Username: "user1",
			Email:    "<EMAIL>",
			Phone:    "+1111111111",
			Status:   entity.UserStatusActive,
		},
		{
			GlobalEntity: types.GlobalEntity{
				CoreEntity: types.CoreEntity{
					ID:         2,
					BusinessID: "user-2",
					CreatedAt:  now,
					UpdatedAt:  now,
					Version:    1,
				},
			},
			Username: "user2",
			Email:    "<EMAIL>",
			Phone:    "+2222222222",
			Status:   entity.UserStatusInactive,
		},
	}

	// 执行转换
	result := suite.assembler.ToUserListResponseDTO(users, 100, 1, 10)

	// 验证结果
	assert.NotNil(suite.T(), result)
	assert.Equal(suite.T(), int64(100), result.Total)
	assert.Equal(suite.T(), 1, result.Page)
	assert.Equal(suite.T(), 10, result.PageSize)
	assert.Equal(suite.T(), 10, result.TotalPages) // (100 + 10 - 1) / 10
	assert.Len(suite.T(), result.Users, 2)

	// 验证第一个用户
	user1 := result.Users[0]
	assert.Equal(suite.T(), "user-1", user1.ID)
	assert.Equal(suite.T(), "user-1", user1.BusinessID)
	assert.Equal(suite.T(), "user1", user1.Username)
	assert.Equal(suite.T(), "<EMAIL>", user1.Email)
	assert.Equal(suite.T(), "active", user1.Status)

	// 验证第二个用户
	user2 := result.Users[1]
	assert.Equal(suite.T(), "user-2", user2.ID)
	assert.Equal(suite.T(), "user-2", user2.BusinessID)
	assert.Equal(suite.T(), "user2", user2.Username)
	assert.Equal(suite.T(), "<EMAIL>", user2.Email)
	assert.Equal(suite.T(), "inactive", user2.Status)
}

// TestUserStatusConversion 测试用户状态转换（通过ToUserDTO间接测试）
func (suite *UserAssemblerTestSuite) TestUserStatusConversion() {
	testCases := []struct {
		input    entity.UserStatus
		expected string
	}{
		{entity.UserStatusPending, "pending"},
		{entity.UserStatusActive, "active"},
		{entity.UserStatusInactive, "inactive"},
		{entity.UserStatusSuspended, "suspended"},
		{entity.UserStatusBanned, "banned"},
	}

	for _, tc := range testCases {
		// 创建测试用户
		user := &entity.User{
			GlobalEntity: types.GlobalEntity{
				CoreEntity: types.CoreEntity{
					ID:         123,
					BusinessID: "test-user",
					CreatedAt:  time.Now(),
					UpdatedAt:  time.Now(),
					Version:    1,
				},
			},
			Username: "testuser",
			Email:    "<EMAIL>",
			Phone:    "+1234567890",
			Status:   tc.input,
		}

		// 转换并验证状态
		result := suite.assembler.ToUserDTO(user)
		assert.Equal(suite.T(), tc.expected, result.Status, "Status conversion failed for %v", tc.input)
	}
}

// TestToUserListQuery 测试用户列表查询转换
func (suite *UserAssemblerTestSuite) TestToUserListQuery() {
	// 准备测试数据
	dto := &dto.UserListRequestDTO{
		TenantID: "tenant-123",
		Keyword:  "test",
		Status:   "active",
		Page:     2,
		PageSize: 20,
		SortBy:   "username",
		SortDesc: true,
	}

	// 执行转换
	result := suite.assembler.ToUserListQuery(dto)

	// 验证结果
	assert.NotNil(suite.T(), result)
	assert.Equal(suite.T(), "tenant-123", result.TenantID)
	assert.Equal(suite.T(), "test", result.Keyword)
	assert.Equal(suite.T(), "active", result.Status)
	assert.Equal(suite.T(), 2, result.Page)
	assert.Equal(suite.T(), 20, result.PageSize)
	assert.Equal(suite.T(), "username", result.SortBy)
	assert.True(suite.T(), result.SortDesc)
}

// TestToUserSearchCriteria 测试用户搜索条件转换
func (suite *UserAssemblerTestSuite) TestToUserSearchCriteria() {
	// 准备测试数据
	startTime := time.Now().AddDate(0, 0, -30)
	endTime := time.Now()
	dto := &dto.UserSearchRequestDTO{
		TenantID:    "tenant-123",
		Keyword:     "search",
		Status:      "active",
		Email:       "<EMAIL>",
		Phone:       "+1234567890",
		CreatedFrom: &startTime,
		CreatedTo:   &endTime,
		Page:        1,
		PageSize:    10,
		SortBy:      "created_at",
		SortDesc:    false,
	}

	// 执行转换
	result := suite.assembler.ToUserSearchCriteria(dto)

	// 验证结果
	assert.NotNil(suite.T(), result)
	assert.Equal(suite.T(), "tenant-123", result.TenantID)
	assert.Equal(suite.T(), "search", result.Keyword)
	assert.Equal(suite.T(), "<EMAIL>", result.Email)
	assert.Equal(suite.T(), "+1234567890", result.Phone)
	assert.Equal(suite.T(), &startTime, result.CreatedFrom)
	assert.Equal(suite.T(), &endTime, result.CreatedTo)
	assert.Equal(suite.T(), 1, result.Page)
	assert.Equal(suite.T(), 10, result.PageSize)
	assert.Equal(suite.T(), "created_at", result.SortBy)
	assert.False(suite.T(), result.SortDesc)
}

// 运行测试套件
func TestUserAssemblerTestSuite(t *testing.T) {
	suite.Run(t, new(UserAssemblerTestSuite))
}
