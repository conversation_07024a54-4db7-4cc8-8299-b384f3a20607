package application_test

import (
	"testing"
	"time"

	commandModel "backend/internal/application/command/model"
	queryModel "backend/internal/application/query/model"

	"github.com/shopspring/decimal"
	"github.com/stretchr/testify/assert"
)

// TestTenantCQRSValidation 测试租户CQRS验证
func TestTenantCQRSValidation(t *testing.T) {
	t.Run("创建租户命令验证", func(t *testing.T) {
		cmd := &commandModel.CreateTenantCommand{
			Name:         "测试租户",
			Domain:       "test.example.com",
			DisplayName:  "测试租户显示名",
			Description:  "这是一个测试租户",
			Type:         "basic",
			Industry:     "科技",
			Country:      "中国",
			Province:     "北京",
			City:         "北京",
			Address:      "测试地址",
			ContactEmail: "<EMAIL>",
			ContactPhone: "13800138000",
		}

		err := cmd.Validate()
		assert.NoError(t, err)
	})

	t.Run("查询租户验证", func(t *testing.T) {
		query := &queryModel.GetTenantByIDQuery{
			TenantID: "tenant-123",
		}

		err := query.Validate()
		assert.NoError(t, err)
	})

	t.Run("更新配额命令验证", func(t *testing.T) {
		userLimit := 100
		storageLimit := int64(10240)
		cmd := &commandModel.UpdateQuotaCommand{
			TenantID:     "tenant-123",
			UserLimit:    &userLimit,
			StorageLimit: &storageLimit,
		}

		err := cmd.Validate()
		assert.NoError(t, err)
	})

	t.Run("查询配额验证", func(t *testing.T) {
		query := &queryModel.GetTenantQuotaQuery{
			TenantID: "tenant-123",
		}

		err := query.Validate()
		assert.NoError(t, err)
	})

	t.Run("创建订阅命令验证", func(t *testing.T) {
		startDate := time.Now()
		endDate := startDate.AddDate(1, 0, 0)
		cmd := &commandModel.CreateSubscriptionCommand{
			TenantID:     "tenant-123",
			PlanID:       "plan-basic",
			PlanName:     "基础版",
			StartDate:    startDate,
			EndDate:      endDate,
			Price:        decimal.NewFromFloat(99.99),
			Currency:     "CNY",
			BillingCycle: "monthly",
			UserLimit:    10,
			StorageLimit: 1024,
			APILimit:     10000,
			ProductLimit: 100,
			OrderLimit:   1000,
		}

		err := cmd.Validate()
		assert.NoError(t, err)
	})

	t.Run("查询订阅验证", func(t *testing.T) {
		query := &queryModel.GetSubscriptionQuery{
			SubscriptionID: "subscription-123",
		}

		err := query.Validate()
		assert.NoError(t, err)
	})

	t.Run("租户列表查询验证", func(t *testing.T) {
		query := &queryModel.ListTenantsQuery{
			Type:     "basic",
			Status:   "active",
			Page:     1,
			PageSize: 10,
		}

		err := query.Validate()
		assert.NoError(t, err)
		assert.Equal(t, "created_at", query.SortBy)
	})

	t.Run("订阅列表查询验证", func(t *testing.T) {
		query := &queryModel.ListSubscriptionsQuery{
			Status:       "active",
			Currency:     "CNY",
			BillingCycle: "monthly",
			Page:         1,
			PageSize:     20,
		}

		err := query.Validate()
		assert.NoError(t, err)
	})
}

// TestCQRSEdgeCases 测试CQRS边界情况
func TestCQRSEdgeCases(t *testing.T) {
	t.Run("无效命令验证", func(t *testing.T) {
		// 空的创建租户命令
		emptyCmd := &commandModel.CreateTenantCommand{}
		err := emptyCmd.Validate()
		assert.Error(t, err)

		// 无效类型
		invalidTypeCmd := &commandModel.CreateTenantCommand{
			Name:   "测试租户",
			Domain: "test.example.com",
			Type:   "invalid",
		}
		err = invalidTypeCmd.Validate()
		assert.Error(t, err)
	})

	t.Run("分页参数自动修正", func(t *testing.T) {
		query := &queryModel.ListTenantsQuery{
			Page:     0,   // 无效页码
			PageSize: 200, // 超过限制
		}

		err := query.Validate()
		assert.NoError(t, err)
		assert.Equal(t, 1, query.Page)
		assert.Equal(t, 100, query.PageSize)
	})

	t.Run("搜索查询验证", func(t *testing.T) {
		// 有效搜索
		validQuery := &queryModel.SearchTenantsQuery{
			Query:  "测试",
			Fields: []string{"name", "domain"},
			Page:   1,
		}
		err := validQuery.Validate()
		assert.NoError(t, err)

		// 空搜索关键词
		emptyQuery := &queryModel.SearchTenantsQuery{
			Query: "",
		}
		err = emptyQuery.Validate()
		assert.Error(t, err)

		// 无效搜索字段
		invalidFieldQuery := &queryModel.SearchTenantsQuery{
			Query:  "测试",
			Fields: []string{"invalid_field"},
		}
		err = invalidFieldQuery.Validate()
		assert.Error(t, err)
	})
}

// TestQuotaCQRSValidation 测试配额CQRS验证
func TestQuotaCQRSValidation(t *testing.T) {
	t.Run("重置配额命令验证", func(t *testing.T) {
		cmd := &commandModel.ResetQuotaCommand{
			TenantID:   "tenant-123",
			QuotaTypes: []string{"user", "storage", "api"},
		}

		err := cmd.Validate()
		assert.NoError(t, err)
	})

	t.Run("配额使用查询验证", func(t *testing.T) {
		query := &queryModel.GetQuotaUsageQuery{
			TenantID:   "tenant-123",
			QuotaTypes: []string{"user", "storage"},
		}

		err := query.Validate()
		assert.NoError(t, err)
	})

	t.Run("配额告警查询验证", func(t *testing.T) {
		query := &queryModel.GetQuotaAlertsQuery{
			TenantID: "tenant-123",
			Level:    "warning",
		}

		err := query.Validate()
		assert.NoError(t, err)
	})
}

// TestSubscriptionCQRSValidation 测试订阅CQRS验证
func TestSubscriptionCQRSValidation(t *testing.T) {
	t.Run("续费订阅命令验证", func(t *testing.T) {
		startDate := time.Now()
		endDate := startDate.AddDate(1, 0, 0)
		cmd := &commandModel.RenewSubscriptionCommand{
			SubscriptionID: "subscription-123",
			StartDate:      startDate,
			EndDate:        endDate,
			Price:          decimal.NewFromFloat(199.99),
			Currency:       "CNY",
			BillingCycle:   "yearly",
		}

		err := cmd.Validate()
		assert.NoError(t, err)
	})

	t.Run("取消订阅命令验证", func(t *testing.T) {
		cmd := &commandModel.CancelSubscriptionCommand{
			SubscriptionID: "subscription-123",
			Reason:         "用户主动取消",
		}

		err := cmd.Validate()
		assert.NoError(t, err)
	})

	t.Run("延长试用期命令验证", func(t *testing.T) {
		cmd := &commandModel.ExtendTrialCommand{
			SubscriptionID: "subscription-123",
			Days:           30,
			Reason:         "客户申请延长试用",
		}

		err := cmd.Validate()
		assert.NoError(t, err)
	})

	// 注意：订阅历史和统计查询模型需要在tenant_query.go中定义
	t.Log("订阅CQRS验证测试完成")
}

// TestSecurityCQRSValidation 测试安全模块CQRS验证
func TestSecurityCQRSValidation(t *testing.T) {
	t.Run("登录命令验证", func(t *testing.T) {
		cmd := &commandModel.LoginCommand{
			IdentityType: "email",
			Identifier:   "<EMAIL>",
			Credential:   "password123",
			DeviceID:     "device-001",
			DeviceInfo:   "Chrome Browser",
			RememberMe:   true,
		}

		err := cmd.Validate()
		assert.NoError(t, err)
	})

	t.Run("选择租户命令验证", func(t *testing.T) {
		cmd := &commandModel.SelectTenantCommand{
			UserID:   "user-123",
			TenantID: "tenant-456",
			DeviceID: "device-001",
		}

		err := cmd.Validate()
		assert.NoError(t, err)
	})

	t.Run("登出命令验证", func(t *testing.T) {
		cmd := &commandModel.LogoutCommand{
			UserID:     "user-123",
			TenantID:   "tenant-456",
			LogoutType: "current",
			DeviceID:   "device-001",
		}

		err := cmd.Validate()
		assert.NoError(t, err)
	})

	t.Run("修改密码命令验证", func(t *testing.T) {
		cmd := &commandModel.ChangePasswordCommand{
			UserID:      "user-123",
			OldPassword: "oldpassword123",
			NewPassword: "newpassword456",
		}

		err := cmd.Validate()
		assert.NoError(t, err)
	})

	t.Run("重置密码命令验证", func(t *testing.T) {
		cmd := &commandModel.ResetPasswordCommand{
			IdentityType: "email",
			Identifier:   "<EMAIL>",
			NewPassword:  "newpassword789",
			VerifyCode:   "123456",
		}

		err := cmd.Validate()
		assert.NoError(t, err)
	})

	t.Run("分配角色命令验证", func(t *testing.T) {
		cmd := &commandModel.AssignRoleCommand{
			UserID:   "user-123",
			TenantID: "tenant-456",
			RoleID:   "admin",
			Reason:   "升级为管理员",
		}

		err := cmd.Validate()
		assert.NoError(t, err)
	})

	t.Run("移除角色命令验证", func(t *testing.T) {
		cmd := &commandModel.RemoveRoleCommand{
			UserID:   "user-123",
			TenantID: "tenant-456",
			RoleID:   "admin",
			Reason:   "降级权限",
		}

		err := cmd.Validate()
		assert.NoError(t, err)
	})

	t.Run("更新权限命令验证", func(t *testing.T) {
		cmd := &commandModel.UpdatePermissionCommand{
			RoleID:    "admin",
			TenantID:  "tenant-456",
			Resource:  "user",
			Actions:   []string{"create", "read", "update", "delete"},
			Operation: "add",
		}

		err := cmd.Validate()
		assert.NoError(t, err)
	})
}

// TestSecurityQueryValidation 测试安全查询验证
func TestSecurityQueryValidation(t *testing.T) {
	t.Run("验证令牌查询验证", func(t *testing.T) {
		query := &queryModel.ValidateTokenQuery{
			Token: "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
		}

		err := query.Validate()
		assert.NoError(t, err)
	})

	t.Run("获取用户认证信息查询验证", func(t *testing.T) {
		query := &queryModel.GetUserAuthQuery{
			UserID: "user-123",
		}

		err := query.Validate()
		assert.NoError(t, err)
	})

	t.Run("检查权限查询验证", func(t *testing.T) {
		query := &queryModel.CheckPermissionQuery{
			UserID:   "user-123",
			TenantID: "tenant-456",
			Resource: "user",
			Action:   "create",
		}

		err := query.Validate()
		assert.NoError(t, err)
	})

	t.Run("获取用户角色查询验证", func(t *testing.T) {
		query := &queryModel.GetUserRolesQuery{
			UserID:   "user-123",
			TenantID: "tenant-456",
		}

		err := query.Validate()
		assert.NoError(t, err)
	})

	t.Run("获取角色权限查询验证", func(t *testing.T) {
		query := &queryModel.GetRolePermissionsQuery{
			RoleID:   "admin",
			TenantID: "tenant-456",
		}

		err := query.Validate()
		assert.NoError(t, err)
	})

	t.Run("角色列表查询验证", func(t *testing.T) {
		query := &queryModel.ListRolesQuery{
			TenantID: "tenant-456",
			Page:     1,
			PageSize: 20,
			SortBy:   "name",
			SortDesc: false,
		}

		err := query.Validate()
		assert.NoError(t, err)
		assert.Equal(t, "name", query.SortBy)
	})

	t.Run("获取会话查询验证", func(t *testing.T) {
		query := &queryModel.GetSessionQuery{
			SessionID: "session-123",
		}

		err := query.Validate()
		assert.NoError(t, err)
	})

	t.Run("用户会话列表查询验证", func(t *testing.T) {
		query := &queryModel.ListUserSessionsQuery{
			UserID:   "user-123",
			TenantID: "tenant-456",
			Status:   "active",
			Page:     1,
			PageSize: 10,
		}

		err := query.Validate()
		assert.NoError(t, err)
	})

	t.Run("安全事件查询验证", func(t *testing.T) {
		query := &queryModel.GetSecurityEventsQuery{
			UserID:    "user-123",
			TenantID:  "tenant-456",
			EventType: "login",
			StartTime: time.Now().Add(-24 * time.Hour),
			EndTime:   time.Now(),
			Page:      1,
			PageSize:  20,
		}

		err := query.Validate()
		assert.NoError(t, err)
	})

	t.Run("登录尝试查询验证", func(t *testing.T) {
		success := true
		query := &queryModel.GetLoginAttemptsQuery{
			UserID:    "user-123",
			IPAddress: "*************",
			StartTime: time.Now().Add(-1 * time.Hour),
			EndTime:   time.Now(),
			Success:   &success,
			Page:      1,
			PageSize:  10,
		}

		err := query.Validate()
		assert.NoError(t, err)
	})
}

// TestSecurityCQRSEdgeCases 测试安全CQRS边界情况
func TestSecurityCQRSEdgeCases(t *testing.T) {
	t.Run("无效登录命令验证", func(t *testing.T) {
		// 空的登录命令
		emptyCmd := &commandModel.LoginCommand{}
		err := emptyCmd.Validate()
		assert.Error(t, err)

		// 无效身份类型
		invalidTypeCmd := &commandModel.LoginCommand{
			IdentityType: "invalid",
			Identifier:   "<EMAIL>",
			Credential:   "password123",
		}
		err = invalidTypeCmd.Validate()
		assert.Error(t, err)

		// 密码太短
		shortPasswordCmd := &commandModel.LoginCommand{
			IdentityType: "email",
			Identifier:   "<EMAIL>",
			Credential:   "123",
		}
		err = shortPasswordCmd.Validate()
		assert.Error(t, err)
	})

	t.Run("密码验证边界情况", func(t *testing.T) {
		// 新旧密码相同
		samePasswordCmd := &commandModel.ChangePasswordCommand{
			UserID:      "user-123",
			OldPassword: "password123",
			NewPassword: "password123",
		}
		err := samePasswordCmd.Validate()
		assert.Error(t, err)

		// 新密码太短
		shortNewPasswordCmd := &commandModel.ChangePasswordCommand{
			UserID:      "user-123",
			OldPassword: "oldpassword123",
			NewPassword: "123",
		}
		err = shortNewPasswordCmd.Validate()
		assert.Error(t, err)
	})

	t.Run("权限命令验证边界情况", func(t *testing.T) {
		// 无效权限操作
		invalidActionCmd := &commandModel.UpdatePermissionCommand{
			RoleID:    "admin",
			TenantID:  "tenant-456",
			Resource:  "user",
			Actions:   []string{"invalid_action"},
			Operation: "add",
		}
		err := invalidActionCmd.Validate()
		assert.Error(t, err)

		// 无效操作类型
		invalidOperationCmd := &commandModel.UpdatePermissionCommand{
			RoleID:    "admin",
			TenantID:  "tenant-456",
			Resource:  "user",
			Actions:   []string{"read"},
			Operation: "invalid",
		}
		err = invalidOperationCmd.Validate()
		assert.Error(t, err)
	})

	t.Run("查询分页参数自动修正", func(t *testing.T) {
		query := &queryModel.ListRolesQuery{
			TenantID: "tenant-456",
			Page:     0,   // 无效页码
			PageSize: 200, // 超过限制
		}

		err := query.Validate()
		assert.NoError(t, err)
		assert.Equal(t, 1, query.Page)
		assert.Equal(t, 100, query.PageSize)
	})

	t.Run("时间范围验证", func(t *testing.T) {
		// 开始时间晚于结束时间
		invalidTimeQuery := &queryModel.GetSecurityEventsQuery{
			StartTime: time.Now(),
			EndTime:   time.Now().Add(-1 * time.Hour),
			Page:      1,
			PageSize:  10,
		}
		err := invalidTimeQuery.Validate()
		assert.Error(t, err)
	})
}
