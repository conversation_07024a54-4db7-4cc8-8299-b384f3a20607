package infrastructure_test

import (
	"errors"
	"testing"

	"backend/pkg/adapters/database"
	commonErrors "backend/pkg/common/errors"
	"backend/pkg/common/errors/codes"

	"github.com/jackc/pgx/v5/pgconn"
	"github.com/stretchr/testify/assert"
	"gorm.io/gorm"
)

// TestDatabaseErrorAdapterWithMockErrors 测试数据库错误适配器的模拟错误
func TestDatabaseErrorAdapterWithMockErrors(t *testing.T) {
	adapter := database.NewErrorAdapter()

	tests := []struct {
		name         string
		inputError   error
		expectedType commonErrors.ErrorType
		expectedCode string
	}{
		{
			name:         "GORM记录未找到",
			inputError:   gorm.ErrRecordNotFound,
			expectedType: commonErrors.TypeNotFound,
			expectedCode: codes.DatabaseNotFound,
		},
		{
			name:         "GORM无效事务",
			inputError:   gorm.ErrInvalidTransaction,
			expectedType: commonErrors.TypeInternal,
			expectedCode: codes.DatabaseTransaction,
		},
		{
			name:         "GORM缺少WHERE条件",
			inputError:   gorm.ErrMissingWhereClause,
			expectedType: commonErrors.TypeValidation,
			expectedCode: codes.ParamInvalid,
		},
		{
			name:         "GORM主键必需",
			inputError:   gorm.ErrPrimaryKeyRequired,
			expectedType: commonErrors.TypeValidation,
			expectedCode: codes.ParamRequired,
		},
		{
			name:         "GORM无效数据",
			inputError:   gorm.ErrInvalidData,
			expectedType: commonErrors.TypeValidation,
			expectedCode: codes.ParamInvalid,
		},
		{
			name:         "GORM无效数据库连接",
			inputError:   gorm.ErrInvalidDB,
			expectedType: commonErrors.TypeInternal,
			expectedCode: codes.DatabaseConnection,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			translatedErr := adapter.TranslateError(tt.inputError)
			assert.Error(t, translatedErr)

			appErr := commonErrors.As(translatedErr)
			if appErr != nil {
				assert.Equal(t, tt.expectedType, appErr.Type)
				assert.Equal(t, tt.expectedCode, appErr.Code)
			} else {
				t.Errorf("翻译后的错误不是AppError类型: %T", translatedErr)
			}
		})
	}
}

// TestDatabaseErrorAdapterNilError 测试nil错误处理
func TestDatabaseErrorAdapterNilError(t *testing.T) {
	adapter := database.NewErrorAdapter()

	// 测试nil错误
	result := adapter.TranslateError(nil)
	assert.NoError(t, result)

	// 测试WrapDBError的nil处理
	result = database.WrapDBError(nil)
	assert.NoError(t, result)
}

// TestDatabaseErrorAdapterPostgreSQLErrors 测试PostgreSQL特定错误
func TestDatabaseErrorAdapterPostgreSQLErrors(t *testing.T) {
	adapter := database.NewErrorAdapter()

	tests := []struct {
		name         string
		pgError      *pgconn.PgError
		expectedType commonErrors.ErrorType
		expectedCode string
	}{
		{
			name: "PostgreSQL唯一约束违反",
			pgError: &pgconn.PgError{
				Code:           "23505",
				Message:        "duplicate key value violates unique constraint",
				ConstraintName: "users_email_key",
			},
			expectedType: commonErrors.TypeConflict,
			expectedCode: codes.DatabaseDuplicateKey,
		},
		{
			name: "PostgreSQL外键约束违反",
			pgError: &pgconn.PgError{
				Code:           "23503",
				Message:        "insert or update on table violates foreign key constraint",
				ConstraintName: "fk_user_tenant",
			},
			expectedType: commonErrors.TypeConflict,
			expectedCode: codes.DatabaseConstraint,
		},
		{
			name: "PostgreSQL非空约束违反",
			pgError: &pgconn.PgError{
				Code:       "23502",
				Message:    "null value in column violates not-null constraint",
				ColumnName: "email",
			},
			expectedType: commonErrors.TypeValidation,
			expectedCode: codes.ParamRequired,
		},
		{
			name: "PostgreSQL连接失败",
			pgError: &pgconn.PgError{
				Code:    "08006",
				Message: "connection failure",
			},
			expectedType: commonErrors.TypeExternal,
			expectedCode: codes.DatabaseConnection,
		},
		{
			name: "PostgreSQL认证失败",
			pgError: &pgconn.PgError{
				Code:    "28000",
				Message: "invalid authorization specification",
			},
			expectedType: commonErrors.TypeUnauthorized,
			expectedCode: codes.DatabaseConnection,
		},
		{
			name: "PostgreSQL权限不足",
			pgError: &pgconn.PgError{
				Code:    "42501",
				Message: "insufficient privilege",
			},
			expectedType: commonErrors.TypePermission,
			expectedCode: codes.DatabaseConnection,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			translatedErr := adapter.TranslateError(tt.pgError)
			assert.Error(t, translatedErr)

			appErr := commonErrors.As(translatedErr)
			if appErr != nil {
				assert.Equal(t, tt.expectedType, appErr.Type)
				assert.Equal(t, tt.expectedCode, appErr.Code)

				// 验证PostgreSQL特定的详细信息
				assert.Equal(t, tt.pgError.Code, appErr.Details["pg_code"])
				assert.Equal(t, tt.pgError.Message, appErr.Details["pg_message"])

				if tt.pgError.ConstraintName != "" {
					assert.Equal(t, tt.pgError.ConstraintName, appErr.Details["constraint"])
				}
				if tt.pgError.ColumnName != "" {
					assert.Equal(t, tt.pgError.ColumnName, appErr.Details["column"])
				}
			} else {
				t.Errorf("翻译后的错误不是AppError类型: %T", translatedErr)
			}
		})
	}
}

// TestDatabaseErrorAdapterGenericErrors 测试通用错误模式匹配
func TestDatabaseErrorAdapterGenericErrors(t *testing.T) {
	adapter := database.NewErrorAdapter()

	tests := []struct {
		name         string
		inputError   error
		expectedType commonErrors.ErrorType
		expectedCode string
	}{
		{
			name:         "连接错误",
			inputError:   errors.New("connection refused"),
			expectedType: commonErrors.TypeExternal,
			expectedCode: codes.DatabaseConnection,
		},
		{
			name:         "超时错误",
			inputError:   errors.New("operation timeout"),
			expectedType: commonErrors.TypeExternal,
			expectedCode: codes.DatabaseTimeout,
		},
		{
			name:         "死锁错误",
			inputError:   errors.New("deadlock detected"),
			expectedType: commonErrors.TypeConflict,
			expectedCode: codes.DatabaseDeadlock,
		},
		{
			name:         "约束违反错误",
			inputError:   errors.New("constraint violation"),
			expectedType: commonErrors.TypeConflict,
			expectedCode: codes.DatabaseConstraint,
		},
		{
			name:         "重复键错误",
			inputError:   errors.New("duplicate key error"),
			expectedType: commonErrors.TypeConflict,
			expectedCode: codes.DatabaseDuplicateKey,
		},
		{
			name:         "语法错误",
			inputError:   errors.New("syntax error at position 10"),
			expectedType: commonErrors.TypeInternal,
			expectedCode: codes.DatabaseQuery,
		},
		{
			name:         "权限错误",
			inputError:   errors.New("permission denied for table users"),
			expectedType: commonErrors.TypePermission,
			expectedCode: codes.DatabaseConnection,
		},
		{
			name:         "未知错误",
			inputError:   errors.New("unknown database error"),
			expectedType: commonErrors.TypeInternal,
			expectedCode: codes.DatabaseQuery,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			translatedErr := adapter.TranslateError(tt.inputError)
			assert.Error(t, translatedErr)

			appErr := commonErrors.As(translatedErr)
			if appErr != nil {
				assert.Equal(t, tt.expectedType, appErr.Type)
				assert.Equal(t, tt.expectedCode, appErr.Code)
			} else {
				t.Errorf("翻译后的错误不是AppError类型: %T", translatedErr)
			}
		})
	}
}

// TestDatabaseErrorAdapterHelperFunctions 测试辅助函数
func TestDatabaseErrorAdapterHelperFunctions(t *testing.T) {
	t.Run("IsNotFoundError", func(t *testing.T) {
		assert.True(t, database.IsNotFoundError(gorm.ErrRecordNotFound))
		assert.False(t, database.IsNotFoundError(gorm.ErrInvalidTransaction))
		assert.False(t, database.IsNotFoundError(errors.New("other error")))
	})

	t.Run("IsConstraintError", func(t *testing.T) {
		// PostgreSQL约束错误
		pgErr := &pgconn.PgError{Code: "23505"}
		assert.True(t, database.IsConstraintError(pgErr))

		pgErr2 := &pgconn.PgError{Code: "08006"}
		assert.False(t, database.IsConstraintError(pgErr2))

		// 通用约束错误
		assert.True(t, database.IsConstraintError(errors.New("constraint violation")))
		assert.True(t, database.IsConstraintError(errors.New("duplicate key")))
		assert.True(t, database.IsConstraintError(errors.New("unique constraint")))
		assert.False(t, database.IsConstraintError(errors.New("other error")))
	})

	t.Run("IsConnectionError", func(t *testing.T) {
		// PostgreSQL连接错误
		pgErr := &pgconn.PgError{Code: "08006"}
		assert.True(t, database.IsConnectionError(pgErr))

		pgErr2 := &pgconn.PgError{Code: "23505"}
		assert.False(t, database.IsConnectionError(pgErr2))

		// 通用连接错误
		assert.True(t, database.IsConnectionError(errors.New("connection refused")))
		assert.True(t, database.IsConnectionError(errors.New("dial tcp failed")))
		assert.False(t, database.IsConnectionError(errors.New("other error")))
	})

	t.Run("TranslateDBError便捷函数", func(t *testing.T) {
		// 测试nil
		result := database.TranslateDBError(nil)
		assert.NoError(t, result)

		// 测试GORM错误
		result = database.TranslateDBError(gorm.ErrRecordNotFound)
		assert.Error(t, result)

		appErr := commonErrors.As(result)
		if appErr != nil {
			assert.Equal(t, commonErrors.TypeNotFound, appErr.Type)
		}
	})
}
