package service

import (
	"context"
	"testing"

	"backend/internal/shared/service/id"
	"backend/internal/shared/service/id/implementation"
	"backend/internal/shared/types"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
)

// TestEntityIDService_GenerateEntityIDs 测试实体ID生成
func TestEntityIDService_GenerateEntityIDs(t *testing.T) {
	service := createTestEntityIDService(t)
	ctx := context.Background()

	// 测试完整实体ID生成
	techID, businessID, err := service.GenerateEntityIDs(
		ctx,
		types.IDDomainUser,
		"tenant123",
		"user001",
		map[string]interface{}{
			"username": "testuser",
			"email":    "<EMAIL>",
		},
	)

	require.NoError(t, err)
	assert.Greater(t, techID, int64(0))
	assert.NotEmpty(t, businessID)
	assert.True(t, service.IsValidUUID(businessID))
	assert.True(t, service.IsValidSnowflake(techID))
}

// TestEntityIDService_GenerateEntityIDsSimple 测试简单实体ID生成
func TestEntityIDService_GenerateEntityIDsSimple(t *testing.T) {
	service := createTestEntityIDService(t)
	ctx := context.Background()

	// 测试简单实体ID生成
	techID, businessID, err := service.GenerateEntityIDsSimple(ctx, types.IDDomainProduct)

	require.NoError(t, err)
	assert.Greater(t, techID, int64(0))
	assert.NotEmpty(t, businessID)
	assert.True(t, service.IsValidUUID(businessID))
	assert.True(t, service.IsValidSnowflake(techID))
}

// TestEntityIDService_GenerateTechID 测试技术ID生成
func TestEntityIDService_GenerateTechID(t *testing.T) {
	service := createTestEntityIDService(t)
	ctx := context.Background()

	// 测试技术ID生成
	techID, err := service.GenerateTechID(ctx, types.IDDomainOrder)
	require.NoError(t, err)
	assert.Greater(t, techID, int64(0))

	// 验证技术ID
	err = service.ValidateTechID(techID)
	assert.NoError(t, err)
}

// TestEntityIDService_GenerateBusinessID 测试业务ID生成
func TestEntityIDService_GenerateBusinessID(t *testing.T) {
	service := createTestEntityIDService(t)
	ctx := context.Background()

	// 测试业务ID生成
	businessID, err := service.GenerateBusinessID(
		ctx,
		types.IDDomainOrder,
		"tenant456",
		"order001",
		map[string]interface{}{
			"customer_id": "customer123",
			"amount":      99.99,
		},
	)

	require.NoError(t, err)
	assert.NotEmpty(t, businessID)
	assert.True(t, service.IsValidUUID(businessID))

	// 验证业务ID
	err = service.ValidateBusinessID(businessID)
	assert.NoError(t, err)
}

// TestEntityIDService_GenerateSimpleBusinessID 测试简单业务ID生成
func TestEntityIDService_GenerateSimpleBusinessID(t *testing.T) {
	service := createTestEntityIDService(t)
	ctx := context.Background()

	// 测试简单业务ID生成
	businessID, err := service.GenerateSimpleBusinessID(ctx, types.IDDomainInventory)
	require.NoError(t, err)
	assert.NotEmpty(t, businessID)
	assert.True(t, service.IsValidUUID(businessID))

	// 验证业务ID
	err = service.ValidateBusinessID(businessID)
	assert.NoError(t, err)
}

// TestEntityIDService_ValidateIDs 测试ID验证
func TestEntityIDService_ValidateIDs(t *testing.T) {
	service := createTestEntityIDService(t)
	ctx := context.Background()

	// 生成有效ID进行验证
	techID, businessID, err := service.GenerateEntityIDsSimple(ctx, types.IDDomainGeneric)
	require.NoError(t, err)

	// 验证有效的技术ID
	err = service.ValidateTechID(techID)
	assert.NoError(t, err)

	// 验证有效的业务ID
	err = service.ValidateBusinessID(businessID)
	assert.NoError(t, err)

	// 验证无效的技术ID
	err = service.ValidateTechID(0)
	assert.Error(t, err)

	err = service.ValidateTechID(-1)
	assert.Error(t, err)

	// 验证无效的业务ID
	err = service.ValidateBusinessID("invalid-uuid")
	assert.Error(t, err)

	err = service.ValidateBusinessID("")
	assert.Error(t, err)
}

// TestEntityIDService_IsValidChecks 测试格式验证方法
func TestEntityIDService_IsValidChecks(t *testing.T) {
	service := createTestEntityIDService(t)
	ctx := context.Background()

	// 生成有效ID
	techID, businessID, err := service.GenerateEntityIDsSimple(ctx, types.IDDomainGeneric)
	require.NoError(t, err)

	// 测试有效性检查
	assert.True(t, service.IsValidSnowflake(techID))
	assert.True(t, service.IsValidUUID(businessID))

	// 测试无效值
	assert.False(t, service.IsValidSnowflake(0))
	assert.False(t, service.IsValidSnowflake(-1))
	assert.False(t, service.IsValidUUID("invalid-uuid"))
	assert.False(t, service.IsValidUUID(""))
}

// MockIDEntity 模拟ID实体，用于测试EntityIDHelper
type MockIDEntity struct {
	techID     int64
	businessID string
	tenantID   string
	entityKey  string
	domain     types.IDDomain
	metadata   map[string]interface{}
}

func (m *MockIDEntity) GetTechID() int64                      { return m.techID }
func (m *MockIDEntity) SetTechID(id int64)                    { m.techID = id }
func (m *MockIDEntity) GetBusinessID() string                 { return m.businessID }
func (m *MockIDEntity) SetBusinessID(id string)               { m.businessID = id }
func (m *MockIDEntity) GetTenantID() string                   { return m.tenantID }
func (m *MockIDEntity) GetEntityKey() string                  { return m.entityKey }
func (m *MockIDEntity) GetDomain() types.IDDomain             { return m.domain }
func (m *MockIDEntity) GetIDMetadata() map[string]interface{} { return m.metadata }

// TestEntityIDHelper_GenerateIDsForEntity 测试为实体生成ID
func TestEntityIDHelper_GenerateIDsForEntity(t *testing.T) {
	helper := createTestEntityIDHelper(t)
	ctx := context.Background()

	// 创建新实体（无ID）
	entity := &MockIDEntity{
		tenantID:  "tenant123",
		entityKey: "user001",
		domain:    types.IDDomainUser,
		metadata: map[string]interface{}{
			"username": "testuser",
			"email":    "<EMAIL>",
		},
	}

	// 为实体生成ID
	err := helper.GenerateIDsForEntity(ctx, entity)
	require.NoError(t, err)

	// 验证生成的ID
	assert.Greater(t, entity.GetTechID(), int64(0))
	assert.NotEmpty(t, entity.GetBusinessID())

	// 验证ID格式
	err = helper.ValidateEntity(entity)
	assert.NoError(t, err)
}

// TestEntityIDHelper_GenerateIDsForEntity_SkipExisting 测试跳过已有ID的实体
func TestEntityIDHelper_GenerateIDsForEntity_SkipExisting(t *testing.T) {
	helper := createTestEntityIDHelper(t)
	ctx := context.Background()

	// 创建已有ID的实体
	entity := &MockIDEntity{
		techID:     123456789,
		businessID: "existing-uuid-12345",
		tenantID:   "tenant123",
		entityKey:  "user001",
		domain:     types.IDDomainUser,
	}

	originalTechID := entity.GetTechID()
	originalBusinessID := entity.GetBusinessID()

	// 为实体生成ID（应该跳过）
	err := helper.GenerateIDsForEntity(ctx, entity)
	require.NoError(t, err)

	// 验证ID没有被修改
	assert.Equal(t, originalTechID, entity.GetTechID())
	assert.Equal(t, originalBusinessID, entity.GetBusinessID())
}

// TestEntityIDHelper_RegenerateBusinessID 测试重新生成业务ID
func TestEntityIDHelper_RegenerateBusinessID(t *testing.T) {
	helper := createTestEntityIDHelper(t)
	ctx := context.Background()

	// 创建实体
	entity := &MockIDEntity{
		techID:     123456789,
		businessID: "old-business-id",
		tenantID:   "tenant123",
		entityKey:  "user001",
		domain:     types.IDDomainUser,
		metadata: map[string]interface{}{
			"username": "testuser",
		},
	}

	originalTechID := entity.GetTechID()
	originalBusinessID := entity.GetBusinessID()

	// 重新生成业务ID
	err := helper.RegenerateBusinessID(ctx, entity)
	require.NoError(t, err)

	// 验证技术ID没有变化，业务ID已更新
	assert.Equal(t, originalTechID, entity.GetTechID())
	assert.NotEqual(t, originalBusinessID, entity.GetBusinessID())
	assert.NotEmpty(t, entity.GetBusinessID())
}

// TestEntityIDHelper_GenerateIDsForEntities 测试批量生成实体ID
func TestEntityIDHelper_GenerateIDsForEntities(t *testing.T) {
	helper := createTestEntityIDHelper(t)
	ctx := context.Background()

	// 创建实体列表
	entities := []id.IDEntity{
		&MockIDEntity{
			tenantID:  "tenant123",
			entityKey: "user001",
			domain:    types.IDDomainUser,
		},
		&MockIDEntity{
			tenantID:  "tenant123",
			entityKey: "user002",
			domain:    types.IDDomainUser,
		},
		&MockIDEntity{
			tenantID:  "tenant456",
			entityKey: "product001",
			domain:    types.IDDomainProduct,
		},
	}

	// 批量生成ID
	err := helper.GenerateIDsForEntities(ctx, entities)
	require.NoError(t, err)

	// 验证所有实体都有ID
	idMap := make(map[int64]bool)
	uuidMap := make(map[string]bool)

	for i, entity := range entities {
		assert.Greater(t, entity.GetTechID(), int64(0), "实体 %d 的技术ID无效", i)
		assert.NotEmpty(t, entity.GetBusinessID(), "实体 %d 的业务ID为空", i)

		// 验证ID唯一性
		assert.False(t, idMap[entity.GetTechID()], "发现重复的技术ID: %d", entity.GetTechID())
		assert.False(t, uuidMap[entity.GetBusinessID()], "发现重复的业务ID: %s", entity.GetBusinessID())

		idMap[entity.GetTechID()] = true
		uuidMap[entity.GetBusinessID()] = true

		// 验证实体ID格式
		err = helper.ValidateEntity(entity)
		assert.NoError(t, err, "实体 %d 的ID验证失败", i)
	}
}

// TestEntityIDHelper_ValidateEntityIDs 测试实体ID验证
func TestEntityIDHelper_ValidateEntityIDs(t *testing.T) {
	helper := createTestEntityIDHelper(t)
	ctx := context.Background()

	// 生成有效的ID
	service := createTestEntityIDService(t)
	techID, businessID, err := service.GenerateEntityIDsSimple(ctx, types.IDDomainUser)
	require.NoError(t, err)

	// 验证有效的ID组合
	err = helper.ValidateEntityIDs(techID, businessID, types.IDDomainUser)
	assert.NoError(t, err)

	// 验证无效的技术ID
	err = helper.ValidateEntityIDs(0, businessID, types.IDDomainUser)
	assert.Error(t, err)

	// 验证无效的业务ID
	err = helper.ValidateEntityIDs(techID, "invalid-uuid", types.IDDomainUser)
	assert.Error(t, err)
}

// createTestEntityIDService 创建用于测试的实体ID服务
func createTestEntityIDService(t *testing.T) id.EntityIDService {
	unifiedGenerator := createTestIDGenerator(t)
	return implementation.NewEntityIDService(unifiedGenerator)
}

// createTestEntityIDHelper 创建用于测试的实体ID辅助器
func createTestEntityIDHelper(t *testing.T) id.EntityIDHelper {
	entityIDService := createTestEntityIDService(t)
	return implementation.NewEntityIDHelper(entityIDService)
}
