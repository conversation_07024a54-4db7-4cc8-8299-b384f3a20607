package service

import (
	"context"
	"fmt"
	"testing"

	"backend/internal/shared/service/id"
	"backend/internal/shared/service/id/implementation"
	"backend/internal/shared/types"
	"backend/pkg/infrastructure/snowflake"
	"backend/pkg/infrastructure/uuid"
	uuidTypes "backend/pkg/types/uuid"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
)

// TestUnifiedIDGenerator_GenerateSnowflakeID 测试雪花ID生成
func TestUnifiedIDGenerator_GenerateSnowflakeID(t *testing.T) {
	generator := createTestIDGenerator(t)
	ctx := context.Background()

	// 测试单个雪花ID生成
	id, err := generator.GenerateSnowflakeID(ctx, types.IDDomainUser)
	require.NoError(t, err)
	assert.Greater(t, id, int64(0))

	// 测试批量雪花ID生成
	ids, err := generator.GenerateSnowflakeIDs(ctx, types.IDDomainUser, 5)
	require.NoError(t, err)
	assert.Len(t, ids, 5)

	// 验证生成的ID都是唯一的
	idMap := make(map[int64]bool)
	for _, id := range ids {
		assert.False(t, idMap[id], "发现重复的雪花ID: %d", id)
		idMap[id] = true
		assert.Greater(t, id, int64(0))
	}
}

// TestUnifiedIDGenerator_GenerateUUID 测试UUID生成
func TestUnifiedIDGenerator_GenerateUUID(t *testing.T) {
	generator := createTestIDGenerator(t)
	ctx := context.Background()

	// 测试业务UUID生成
	businessID, err := generator.GenerateBusinessUUID(ctx, types.IDDomainUser, "tenant123", "user001", map[string]interface{}{
		"username": "testuser",
		"email":    "<EMAIL>",
	})
	require.NoError(t, err)
	assert.NotEmpty(t, businessID)

	// 验证业务UUID格式
	err = generator.ValidateID(ctx, businessID, types.IDTypeSemanticUUID, types.IDDomainUser)
	assert.NoError(t, err)

	// 测试简单UUID生成
	simpleID, err := generator.GenerateSimpleUUID(ctx, types.IDDomainUser)
	require.NoError(t, err)
	assert.NotEmpty(t, simpleID)

	// 验证简单UUID格式
	err = generator.ValidateID(ctx, simpleID, types.IDTypeRandomUUID, types.IDDomainUser)
	assert.NoError(t, err)
}

// TestUnifiedIDGenerator_GenerateSequenceID 测试序列号生成
func TestUnifiedIDGenerator_GenerateSequenceID(t *testing.T) {
	generator := createTestIDGenerator(t)
	ctx := context.Background()

	// 测试序列号生成
	seqID, err := generator.GenerateSequenceID(ctx, types.IDDomainOrder, "ORD", 8)
	require.NoError(t, err)
	assert.NotEmpty(t, seqID)
	assert.Contains(t, seqID, "ORD")
	assert.True(t, len(seqID) >= 8)
}

// TestUnifiedIDGenerator_ValidateID 测试ID验证
func TestUnifiedIDGenerator_ValidateID(t *testing.T) {
	generator := createTestIDGenerator(t)
	ctx := context.Background()

	// 生成并验证雪花ID
	snowflakeID, err := generator.GenerateSnowflakeID(ctx, types.IDDomainUser)
	require.NoError(t, err)

	// 将int64转换为字符串进行验证
	snowflakeIDStr := fmt.Sprintf("%d", snowflakeID)
	err = generator.ValidateID(ctx, snowflakeIDStr, types.IDTypeSnowflake, types.IDDomainUser)
	assert.NoError(t, err)

	// 生成并验证UUID
	uuid, err := generator.GenerateSimpleUUID(ctx, types.IDDomainUser)
	require.NoError(t, err)

	err = generator.ValidateID(ctx, uuid, types.IDTypeRandomUUID, types.IDDomainUser)
	assert.NoError(t, err)

	// 测试无效ID验证
	err = generator.ValidateID(ctx, "invalid-uuid", types.IDTypeRandomUUID, types.IDDomainUser)
	assert.Error(t, err)
}

// TestUnifiedIDGenerator_BatchGeneration 测试批量ID生成
func TestUnifiedIDGenerator_BatchGeneration(t *testing.T) {
	generator := createTestIDGenerator(t)
	ctx := context.Background()

	// 创建批量生成请求
	request := &types.BatchIDGenerationRequest{
		BaseRequest: types.IDGenerationRequest{
			Type:     types.IDTypeRandomUUID,
			Purpose:  types.IDPurposeBusiness,
			Domain:   types.IDDomainUser,
			TenantID: "tenant123",
		},
		Items: []types.BatchIDItem{
			{EntityKey: "user001", Metadata: map[string]interface{}{"username": "user1"}},
			{EntityKey: "user002", Metadata: map[string]interface{}{"username": "user2"}},
			{EntityKey: "user003", Metadata: map[string]interface{}{"username": "user3"}},
		},
	}

	// 执行批量生成
	result, err := generator.GenerateBatchIDs(ctx, request)
	require.NoError(t, err)
	assert.Equal(t, 3, result.SuccessCount)
	assert.Equal(t, 0, result.FailureCount)
	assert.Len(t, result.Results, 3)

	// 验证生成的ID都是唯一的
	idMap := make(map[string]bool)
	for _, result := range result.Results {
		assert.False(t, idMap[result.UUID], "发现重复的UUID: %s", result.UUID)
		idMap[result.UUID] = true
		assert.Equal(t, types.IDTypeRandomUUID, result.Type)
		assert.Equal(t, types.IDDomainUser, result.Domain)
	}
}

// TestUnifiedIDGenerator_GetSupportedTypes 测试获取支持的类型
func TestUnifiedIDGenerator_GetSupportedTypes(t *testing.T) {
	generator := createTestIDGenerator(t)

	supportedTypes := generator.GetSupportedTypes()
	assert.Contains(t, supportedTypes, types.IDTypeSnowflake)
	assert.Contains(t, supportedTypes, types.IDTypeSemanticUUID)
	assert.Contains(t, supportedTypes, types.IDTypeRandomUUID)
	assert.Contains(t, supportedTypes, types.IDTypeSequence)

	supportedDomains := generator.GetSupportedDomains()
	assert.Contains(t, supportedDomains, types.IDDomainUser)
	assert.Contains(t, supportedDomains, types.IDDomainProduct)
	assert.Contains(t, supportedDomains, types.IDDomainOrder)
	assert.Contains(t, supportedDomains, types.IDDomainGeneric)
}

// TestUnifiedIDGenerator_GetGeneratorStats 测试获取生成器统计信息
func TestUnifiedIDGenerator_GetGeneratorStats(t *testing.T) {
	generator := createTestIDGenerator(t)
	ctx := context.Background()

	stats, err := generator.GetGeneratorStats(ctx)
	require.NoError(t, err)
	assert.NotNil(t, stats)
	assert.Contains(t, stats, "supported_types")
	assert.Contains(t, stats, "supported_domains")
}

// createTestIDGenerator 创建用于测试的ID生成器实例
func createTestIDGenerator(t *testing.T) id.UnifiedIDGenerator {
	// 创建雪花ID生成器
	snowflakeGen := snowflake.NewGenerator(1)

	// 创建UUID管理器
	uuidConfig := uuidTypes.GeneratorConfig{
		RootNamespace: "test.9wings.erp",
		FallbackToV4:  true,
		EnableCache:   false, // 测试时禁用缓存
		CacheSize:     0,
	}
	uuidManager, err := uuid.NewManager(uuidConfig)
	require.NoError(t, err)

	// 创建ID生成器配置
	config := &id.IDGeneratorConfig{
		SnowflakeConfig: &id.SnowflakeConfig{
			MachineID:  1,
			UseIPBased: false,
		},
		UUIDConfig: &id.UUIDConfig{
			RootNamespace:    "test.9wings.erp",
			EnableSemantic:   true,
			FallbackToRandom: true,
			DomainStrategies: make(map[types.IDDomain]*id.DomainStrategy),
		},
		SequenceConfig: &id.SequenceConfig{
			DefaultPrefix: "TEST",
			DefaultLength: 8,
			PadWithZeros:  true,
		},
		DefaultDomain:     types.IDDomainGeneric,
		EnableCache:       false, // 测试时禁用缓存
		ValidationEnabled: true,
		MetricsEnabled:    false,
	}

	return implementation.NewUnifiedIDGenerator(snowflakeGen, uuidManager, config)
}
