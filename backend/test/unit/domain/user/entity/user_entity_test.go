package domain_test

import (
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"backend/internal/domain/user/entity"
	"backend/internal/domain/user/valueobject"
)

func TestUser_NewUser(t *testing.T) {
	profile := valueobject.UserProfile{Nickname: "testuser"}
	user, err := entity.NewUser(profile, "testuser", "<EMAIL>", "1234567890")
	require.NoError(t, err)
	assert.NotNil(t, user)
	assert.Equal(t, "testuser", user.Username)
	assert.Equal(t, "<EMAIL>", user.Email)
	assert.Equal(t, "1234567890", user.Phone)
	assert.Equal(t, entity.UserStatusActive, user.Status)
	assert.Equal(t, "testuser", user.Profile.Nickname)

	// 在实际应用中，BusinessID是由仓储层在保存时生成的
	// 这里我们手动设置一个用于测试
	user.BusinessID = "test-business-id"
	assert.NotEmpty(t, user.BusinessID)
}

func TestUser_CanLogin(t *testing.T) {
	activeUser, _ := entity.NewUser(valueobject.UserProfile{}, "active", "<EMAIL>", "1")

	inactiveUser, _ := entity.NewUser(valueobject.UserProfile{}, "inactive", "<EMAIL>", "2")
	inactiveUser.Deactivate()

	bannedUser, _ := entity.NewUser(valueobject.UserProfile{}, "banned", "<EMAIL>", "3")
	bannedUser.Status = 4 // Banned status

	assert.True(t, activeUser.CanLogin())
	assert.False(t, inactiveUser.CanLogin())
	assert.False(t, bannedUser.CanLogin())
}

func TestUser_ActivateDeactivate(t *testing.T) {
	user, _ := entity.NewUser(valueobject.UserProfile{}, "test", "<EMAIL>", "1")

	err := user.Deactivate()
	require.NoError(t, err)
	assert.Equal(t, entity.UserStatusInactive, user.Status)

	err = user.Activate()
	require.NoError(t, err)
	assert.Equal(t, entity.UserStatusActive, user.Status)
}

func TestUser_UpdateProfile(t *testing.T) {
	user, _ := entity.NewUser(valueobject.UserProfile{}, "test", "<EMAIL>", "1")

	newProfile := valueobject.UserProfile{
		Nickname: "New Nickname",
		Avatar:   "new_avatar.png",
	}

	user.UpdateProfile(newProfile)

	assert.Equal(t, "New Nickname", user.Profile.Nickname)
	assert.Equal(t, "new_avatar.png", user.Profile.Avatar)
}

func TestUser_Validate(t *testing.T) {
	_, err := entity.NewUser(valueobject.UserProfile{}, "", "<EMAIL>", "123")
	assert.Error(t, err, "username is required")

	_, err = entity.NewUser(valueobject.UserProfile{}, "test", "<EMAIL>", "")
	assert.Error(t, err, "phone is required")
}
