package user_test

import (
	"context"
	"errors"
	"testing"
	"time"

	"backend/internal/application/usecase/user"
	"backend/internal/domain/user/entity"
	"backend/internal/domain/user/valueobject"
	"backend/internal/shared/types"
	"backend/pkg/infrastructure/logger"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/mock"
	"github.com/stretchr/testify/suite"
)

// MockUserRepository 模拟用户仓储
type MockUserRepository struct {
	mock.Mock
}

func (m *MockUserRepository) Save(ctx context.Context, user *entity.User) error {
	args := m.Called(ctx, user)
	return args.Error(0)
}

func (m *MockUserRepository) Update(ctx context.Context, user *entity.User) error {
	args := m.Called(ctx, user)
	return args.Error(0)
}

func (m *MockUserRepository) Delete(ctx context.Context, businessID string) error {
	args := m.Called(ctx, businessID)
	return args.Error(0)
}

func (m *MockUserRepository) FindByBusinessID(ctx context.Context, businessID string) (*entity.User, error) {
	args := m.Called(ctx, businessID)
	if args.Get(0) == nil {
		return nil, args.Error(1)
	}
	return args.Get(0).(*entity.User), args.Error(1)
}

func (m *MockUserRepository) FindByTechID(ctx context.Context, techID int64) (*entity.User, error) {
	args := m.Called(ctx, techID)
	if args.Get(0) == nil {
		return nil, args.Error(1)
	}
	return args.Get(0).(*entity.User), args.Error(1)
}

func (m *MockUserRepository) FindByBusinessIDs(ctx context.Context, businessIDs []string) ([]*entity.User, error) {
	args := m.Called(ctx, businessIDs)
	if args.Get(0) == nil {
		return nil, args.Error(1)
	}
	return args.Get(0).([]*entity.User), args.Error(1)
}

func (m *MockUserRepository) FindByTenantID(ctx context.Context, tenantID string, limit, offset int) ([]*entity.User, int64, error) {
	args := m.Called(ctx, tenantID, limit, offset)
	if args.Get(0) == nil {
		return nil, args.Get(1).(int64), args.Error(2)
	}
	return args.Get(0).([]*entity.User), args.Get(1).(int64), args.Error(2)
}

func (m *MockUserRepository) SearchUsers(ctx context.Context, tenantID, keyword string, limit, offset int) ([]*entity.User, int64, error) {
	args := m.Called(ctx, tenantID, keyword, limit, offset)
	if args.Get(0) == nil {
		return nil, args.Get(1).(int64), args.Error(2)
	}
	return args.Get(0).([]*entity.User), args.Get(1).(int64), args.Error(2)
}

func (m *MockUserRepository) ExistsByBusinessID(ctx context.Context, businessID string) (bool, error) {
	args := m.Called(ctx, businessID)
	return args.Bool(0), args.Error(1)
}

func (m *MockUserRepository) FindUsersByRoleID(ctx context.Context, tenantID, roleBusinessID string, limit, offset int) ([]*entity.User, int64, error) {
	args := m.Called(ctx, tenantID, roleBusinessID, limit, offset)
	if args.Get(0) == nil {
		return nil, args.Get(1).(int64), args.Error(2)
	}
	return args.Get(0).([]*entity.User), args.Get(1).(int64), args.Error(2)
}

func (m *MockUserRepository) BatchCreate(ctx context.Context, users []*entity.User) error {
	args := m.Called(ctx, users)
	return args.Error(0)
}

func (m *MockUserRepository) FindByEmail(ctx context.Context, email string) (*entity.User, error) {
	args := m.Called(ctx, email)
	if args.Get(0) == nil {
		return nil, args.Error(1)
	}
	return args.Get(0).(*entity.User), args.Error(1)
}

func (m *MockUserRepository) FindByPhone(ctx context.Context, phone string) (*entity.User, error) {
	args := m.Called(ctx, phone)
	if args.Get(0) == nil {
		return nil, args.Error(1)
	}
	return args.Get(0).(*entity.User), args.Error(1)
}

func (m *MockUserRepository) ExistsByEmail(ctx context.Context, email string) (bool, error) {
	args := m.Called(ctx, email)
	return args.Bool(0), args.Error(1)
}

func (m *MockUserRepository) ExistsByPhone(ctx context.Context, phone string) (bool, error) {
	args := m.Called(ctx, phone)
	return args.Bool(0), args.Error(1)
}

// MockUserTenantRepository 模拟用户租户仓储
type MockUserTenantRepository struct {
	mock.Mock
}

func (m *MockUserTenantRepository) FindByUserBusinessID(ctx context.Context, userBusinessID string) ([]*entity.UserTenant, error) {
	args := m.Called(ctx, userBusinessID)
	if args.Get(0) == nil {
		return nil, args.Error(1)
	}
	return args.Get(0).([]*entity.UserTenant), args.Error(1)
}

func (m *MockUserTenantRepository) FindByTenantBusinessID(ctx context.Context, tenantBusinessID string) ([]*entity.UserTenant, error) {
	args := m.Called(ctx, tenantBusinessID)
	if args.Get(0) == nil {
		return nil, args.Error(1)
	}
	return args.Get(0).([]*entity.UserTenant), args.Error(1)
}

func (m *MockUserTenantRepository) FindByUserAndTenant(ctx context.Context, userBusinessID, tenantBusinessID string) (*entity.UserTenant, error) {
	args := m.Called(ctx, userBusinessID, tenantBusinessID)
	if args.Get(0) == nil {
		return nil, args.Error(1)
	}
	return args.Get(0).(*entity.UserTenant), args.Error(1)
}

func (m *MockUserTenantRepository) Create(ctx context.Context, userTenant *entity.UserTenant) error {
	args := m.Called(ctx, userTenant)
	return args.Error(0)
}

func (m *MockUserTenantRepository) Delete(ctx context.Context, businessID string) error {
	args := m.Called(ctx, businessID)
	return args.Error(0)
}

// MockTransactionManager 模拟事务管理器
type MockTransactionManager struct {
	mock.Mock
}

func (m *MockTransactionManager) ExecuteInTransaction(ctx context.Context, fn func(context.Context) error) error {
	args := m.Called(ctx, fn)
	if args.Get(0) != nil {
		return fn(ctx)
	}
	return args.Error(0)
}

// MockLogger 模拟日志器
type MockLogger struct {
	mock.Mock
}

func (m *MockLogger) With(fields ...any) logger.Logger {
	args := m.Called(fields)
	return args.Get(0).(logger.Logger)
}

func (m *MockLogger) Debug(ctx context.Context, msg string, fields ...any) {
	m.Called(ctx, msg, fields)
}

func (m *MockLogger) Info(ctx context.Context, msg string, fields ...any) {
	m.Called(ctx, msg, fields)
}

func (m *MockLogger) Warn(ctx context.Context, msg string, fields ...any) {
	m.Called(ctx, msg, fields)
}

func (m *MockLogger) Error(ctx context.Context, msg string, fields ...any) {
	m.Called(ctx, msg, fields)
}

func (m *MockLogger) DebugNoCtx(msg string, fields ...any) {
	m.Called(msg, fields)
}

func (m *MockLogger) InfoNoCtx(msg string, fields ...any) {
	m.Called(msg, fields)
}

func (m *MockLogger) WarnNoCtx(msg string, fields ...any) {
	m.Called(msg, fields)
}

func (m *MockLogger) ErrorNoCtx(msg string, fields ...any) {
	m.Called(msg, fields)
}

// UserManagementUseCaseTestSuite 用户管理用例测试套件
type UserManagementUseCaseTestSuite struct {
	suite.Suite
	useCase            *user.UserManagementUseCase
	mockUserRepo       *MockUserRepository
	mockUserTenantRepo *MockUserTenantRepository
	mockTxManager      *MockTransactionManager
	mockLogger         *MockLogger
}

// SetupTest 每个测试前的设置
func (suite *UserManagementUseCaseTestSuite) SetupTest() {
	suite.mockUserRepo = new(MockUserRepository)
	suite.mockUserTenantRepo = new(MockUserTenantRepository)
	suite.mockTxManager = new(MockTransactionManager)
	suite.mockLogger = new(MockLogger)

	suite.useCase = user.NewUserManagementUseCase(
		suite.mockTxManager,
		suite.mockUserRepo,
		suite.mockUserTenantRepo,
		suite.mockLogger,
	)
}

// TearDownTest 每个测试后的清理
func (suite *UserManagementUseCaseTestSuite) TearDownTest() {
	suite.mockUserRepo.AssertExpectations(suite.T())
	suite.mockUserTenantRepo.AssertExpectations(suite.T())
	suite.mockTxManager.AssertExpectations(suite.T())
}

// createTestUser 创建测试用户
func (suite *UserManagementUseCaseTestSuite) createTestUser(businessID, username, email string, status entity.UserStatus) *entity.User {
	now := time.Now()
	return &entity.User{
		GlobalEntity: types.GlobalEntity{
			CoreEntity: types.CoreEntity{
				ID:         123,
				BusinessID: businessID,
				CreatedAt:  now,
				UpdatedAt:  now,
				Version:    1,
			},
		},
		Username: username,
		Email:    email,
		Phone:    "+1234567890",
		Status:   status,
		Profile: valueobject.UserProfile{
			FirstName: "Test",
			LastName:  "User",
			Nickname:  username,
		},
	}
}

// TestListUsers_Success 测试成功获取用户列表
func (suite *UserManagementUseCaseTestSuite) TestListUsers_Success() {
	// 准备测试数据
	tenantID := "tenant-123"
	testUsers := []*entity.User{
		suite.createTestUser("user-1", "alice", "<EMAIL>", entity.UserStatusActive),
		suite.createTestUser("user-2", "bob", "<EMAIL>", entity.UserStatusActive),
		suite.createTestUser("user-3", "charlie", "<EMAIL>", entity.UserStatusInactive),
	}

	// 设置Mock期望
	suite.mockUserRepo.On("FindByTenantID", mock.Anything, tenantID, 10, 0).
		Return(testUsers, int64(3), nil)
	suite.mockLogger.On("Info", mock.Anything, "用户列表查询成功", mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything).
		Return()

	// 执行测试
	req := &user.ListUsersRequest{
		TenantID: tenantID,
		Page:     1,
		PageSize: 10,
	}

	result, err := suite.useCase.ListUsers(context.Background(), req)

	// 验证结果
	assert.NoError(suite.T(), err)
	assert.NotNil(suite.T(), result)
	assert.Equal(suite.T(), int64(3), result.Total)
	assert.Equal(suite.T(), 1, result.Page)
	assert.Equal(suite.T(), 10, result.PageSize)
	assert.Equal(suite.T(), 1, result.TotalPages)
	assert.False(suite.T(), result.HasNext)
	assert.False(suite.T(), result.HasPrevious)
	assert.Len(suite.T(), result.Items, 3)

	// 验证用户数据
	assert.Equal(suite.T(), "user-1", result.Items[0].UserID)
	assert.Equal(suite.T(), "alice", result.Items[0].Username)
	assert.Equal(suite.T(), "<EMAIL>", result.Items[0].Email)
	assert.Equal(suite.T(), "active", result.Items[0].Status)
}

// TestListUsers_WithKeywordSearch 测试带关键词搜索的用户列表
func (suite *UserManagementUseCaseTestSuite) TestListUsers_WithKeywordSearch() {
	// 准备测试数据
	tenantID := "tenant-123"
	keyword := "alice"
	testUsers := []*entity.User{
		suite.createTestUser("user-1", "alice", "<EMAIL>", entity.UserStatusActive),
	}

	// 设置Mock期望
	suite.mockUserRepo.On("SearchUsers", mock.Anything, tenantID, keyword, 10, 0).
		Return(testUsers, int64(1), nil)
	suite.mockLogger.On("Info", mock.Anything, "用户列表查询成功", mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything).
		Return()

	// 执行测试
	req := &user.ListUsersRequest{
		TenantID: tenantID,
		Keyword:  keyword,
		Page:     1,
		PageSize: 10,
	}

	result, err := suite.useCase.ListUsers(context.Background(), req)

	// 验证结果
	assert.NoError(suite.T(), err)
	assert.NotNil(suite.T(), result)
	assert.Equal(suite.T(), int64(1), result.Total)
	assert.Len(suite.T(), result.Items, 1)
	assert.Equal(suite.T(), "alice", result.Items[0].Username)
}

// TestListUsers_WithStatusFilter 测试带状态筛选的用户列表
func (suite *UserManagementUseCaseTestSuite) TestListUsers_WithStatusFilter() {
	// 准备测试数据
	tenantID := "tenant-123"
	testUsers := []*entity.User{
		suite.createTestUser("user-1", "alice", "<EMAIL>", entity.UserStatusActive),
		suite.createTestUser("user-2", "bob", "<EMAIL>", entity.UserStatusActive),
		suite.createTestUser("user-3", "charlie", "<EMAIL>", entity.UserStatusInactive),
	}

	// 设置Mock期望
	suite.mockUserRepo.On("FindByTenantID", mock.Anything, tenantID, 10, 0).
		Return(testUsers, int64(3), nil)
	suite.mockLogger.On("Info", mock.Anything, "用户列表查询成功", mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything).
		Return()

	// 执行测试
	req := &user.ListUsersRequest{
		TenantID: tenantID,
		Status:   "active",
		Page:     1,
		PageSize: 10,
	}

	result, err := suite.useCase.ListUsers(context.Background(), req)

	// 验证结果
	assert.NoError(suite.T(), err)
	assert.NotNil(suite.T(), result)
	assert.Equal(suite.T(), int64(2), result.Total) // 过滤后只有2个active用户
	assert.Len(suite.T(), result.Items, 2)

	// 验证所有返回的用户都是active状态
	for _, item := range result.Items {
		assert.Equal(suite.T(), "active", item.Status)
	}
}

// TestListUsers_Pagination 测试分页功能
func (suite *UserManagementUseCaseTestSuite) TestListUsers_Pagination() {
	// 准备测试数据 - 第二页
	tenantID := "tenant-123"
	testUsers := []*entity.User{
		suite.createTestUser("user-6", "user6", "<EMAIL>", entity.UserStatusActive),
		suite.createTestUser("user-7", "user7", "<EMAIL>", entity.UserStatusActive),
	}

	// 设置Mock期望 - 第二页，每页5条，偏移量为5
	suite.mockUserRepo.On("FindByTenantID", mock.Anything, tenantID, 5, 5).
		Return(testUsers, int64(12), nil) // 总共12条记录
	suite.mockLogger.On("Info", mock.Anything, "用户列表查询成功", mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything).
		Return()

	// 执行测试
	req := &user.ListUsersRequest{
		TenantID: tenantID,
		Page:     2,
		PageSize: 5,
	}

	result, err := suite.useCase.ListUsers(context.Background(), req)

	// 验证结果
	assert.NoError(suite.T(), err)
	assert.NotNil(suite.T(), result)
	assert.Equal(suite.T(), int64(12), result.Total)
	assert.Equal(suite.T(), 2, result.Page)
	assert.Equal(suite.T(), 5, result.PageSize)
	assert.Equal(suite.T(), 3, result.TotalPages) // 12/5 = 2.4 -> 3页
	assert.True(suite.T(), result.HasNext)        // 还有第3页
	assert.True(suite.T(), result.HasPrevious)    // 有第1页
	assert.Len(suite.T(), result.Items, 2)
}

// TestListUsers_ValidationErrors 测试验证错误
func (suite *UserManagementUseCaseTestSuite) TestListUsers_ValidationErrors() {
	// 测试空租户ID
	req := &user.ListUsersRequest{
		TenantID: "",
		Page:     1,
		PageSize: 10,
	}

	result, err := suite.useCase.ListUsers(context.Background(), req)
	assert.Error(suite.T(), err)
	assert.Nil(suite.T(), result)
	assert.Contains(suite.T(), err.Error(), "租户ID不能为空")

	// 测试无效状态
	req = &user.ListUsersRequest{
		TenantID: "tenant-123",
		Status:   "invalid_status",
		Page:     1,
		PageSize: 10,
	}

	result, err = suite.useCase.ListUsers(context.Background(), req)
	assert.Error(suite.T(), err)
	assert.Nil(suite.T(), result)
	assert.Contains(suite.T(), err.Error(), "无效的用户状态")

	// 测试页面大小超限
	req = &user.ListUsersRequest{
		TenantID: "tenant-123",
		Page:     1,
		PageSize: 150, // 超过100的限制
	}

	result, err = suite.useCase.ListUsers(context.Background(), req)
	assert.Error(suite.T(), err)
	assert.Nil(suite.T(), result)
	assert.Contains(suite.T(), err.Error(), "页面大小必须在1-100之间")
}

// TestListUsers_RepositoryError 测试仓储错误
func (suite *UserManagementUseCaseTestSuite) TestListUsers_RepositoryError() {
	tenantID := "tenant-123"

	// 设置Mock期望 - 仓储返回错误
	suite.mockUserRepo.On("FindByTenantID", mock.Anything, tenantID, 10, 0).
		Return(nil, int64(0), errors.New("database connection failed"))
	suite.mockLogger.On("Error", mock.Anything, "查询用户列表失败", mock.Anything, mock.Anything, mock.Anything, mock.Anything).
		Return()

	// 执行测试
	req := &user.ListUsersRequest{
		TenantID: tenantID,
		Page:     1,
		PageSize: 10,
	}

	result, err := suite.useCase.ListUsers(context.Background(), req)

	// 验证结果
	assert.Error(suite.T(), err)
	assert.Nil(suite.T(), result)
	assert.Contains(suite.T(), err.Error(), "用户列表查询失败")
}

// TestListUsers_EmptyResult 测试空结果
func (suite *UserManagementUseCaseTestSuite) TestListUsers_EmptyResult() {
	tenantID := "tenant-123"

	// 设置Mock期望 - 返回空结果
	suite.mockUserRepo.On("FindByTenantID", mock.Anything, tenantID, 10, 0).
		Return([]*entity.User{}, int64(0), nil)
	suite.mockLogger.On("Info", mock.Anything, "用户列表查询成功", mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything).
		Return()

	// 执行测试
	req := &user.ListUsersRequest{
		TenantID: tenantID,
		Page:     1,
		PageSize: 10,
	}

	result, err := suite.useCase.ListUsers(context.Background(), req)

	// 验证结果
	assert.NoError(suite.T(), err)
	assert.NotNil(suite.T(), result)
	assert.Equal(suite.T(), int64(0), result.Total)
	assert.Equal(suite.T(), 0, result.TotalPages)
	assert.False(suite.T(), result.HasNext)
	assert.False(suite.T(), result.HasPrevious)
	assert.Len(suite.T(), result.Items, 0)
}

// TestListUsers_DefaultValues 测试默认值设置
func (suite *UserManagementUseCaseTestSuite) TestListUsers_DefaultValues() {
	tenantID := "tenant-123"
	testUsers := []*entity.User{
		suite.createTestUser("user-1", "alice", "<EMAIL>", entity.UserStatusActive),
	}

	// 设置Mock期望
	suite.mockUserRepo.On("FindByTenantID", mock.Anything, tenantID, 10, 0).
		Return(testUsers, int64(1), nil)
	suite.mockLogger.On("Info", mock.Anything, "用户列表查询成功", mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything).
		Return()

	// 执行测试 - 不设置Page和PageSize
	req := &user.ListUsersRequest{
		TenantID: tenantID,
		Page:     0, // 应该被设置为1
		PageSize: 0, // 应该被设置为10
	}

	result, err := suite.useCase.ListUsers(context.Background(), req)

	// 验证结果
	assert.NoError(suite.T(), err)
	assert.NotNil(suite.T(), result)
	assert.Equal(suite.T(), 1, result.Page)      // 默认值
	assert.Equal(suite.T(), 10, result.PageSize) // 默认值
}

// TestListUsers 运行测试套件
func TestUserManagementUseCase(t *testing.T) {
	suite.Run(t, new(UserManagementUseCaseTestSuite))
}
