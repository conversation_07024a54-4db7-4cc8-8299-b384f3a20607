package shared_test

import (
	"testing"

	"backend/pkg/common/utils"
)

func TestToString(t *testing.T) {
	tests := []struct {
		name     string
		input    interface{}
		expected string
	}{
		{"nil", nil, ""},
		{"string", "hello", "hello"},
		{"bytes", []byte("world"), "world"},
		{"int", 123, "123"},
		{"int64", int64(456), "456"},
		{"float64", 3.14, "3.14"},
		{"bool true", true, "true"},
		{"bool false", false, "false"},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := utils.ToString(tt.input)
			if result != tt.expected {
				t.Errorf("ToString(%v) = %s; want %s", tt.input, result, tt.expected)
			}
		})
	}
}

func TestToInt(t *testing.T) {
	tests := []struct {
		name        string
		input       string
		expected    int
		expectError bool
	}{
		{"valid number", "123", 123, false},
		{"with spaces", "  456  ", 456, false},
		{"negative", "-789", -789, false},
		{"invalid", "abc", 0, true},
		{"empty", "", 0, true},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result, err := utils.ToInt(tt.input)
			if tt.expectError {
				if err == nil {
					t.Errorf("ToInt(%s) expected error, got nil", tt.input)
				}
			} else {
				if err != nil {
					t.Errorf("ToInt(%s) unexpected error: %v", tt.input, err)
				}
				if result != tt.expected {
					t.Errorf("ToInt(%s) = %d; want %d", tt.input, result, tt.expected)
				}
			}
		})
	}
}

func TestToIntWithDefault(t *testing.T) {
	tests := []struct {
		name         string
		input        string
		defaultValue int
		expected     int
	}{
		{"valid", "123", 999, 123},
		{"invalid", "abc", 999, 999},
		{"empty", "", 42, 42},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := utils.ToIntWithDefault(tt.input, tt.defaultValue)
			if result != tt.expected {
				t.Errorf("ToIntWithDefault(%s, %d) = %d; want %d", tt.input, tt.defaultValue, result, tt.expected)
			}
		})
	}
}

func TestToInt64(t *testing.T) {
	tests := []struct {
		name        string
		input       string
		expected    int64
		expectError bool
	}{
		{"valid number", "123456789", 123456789, false},
		{"large number", "9223372036854775807", 9223372036854775807, false},
		{"negative", "-123", -123, false},
		{"invalid", "abc", 0, true},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result, err := utils.ToInt64(tt.input)
			if tt.expectError {
				if err == nil {
					t.Errorf("ToInt64(%s) expected error, got nil", tt.input)
				}
			} else {
				if err != nil {
					t.Errorf("ToInt64(%s) unexpected error: %v", tt.input, err)
				}
				if result != tt.expected {
					t.Errorf("ToInt64(%s) = %d; want %d", tt.input, result, tt.expected)
				}
			}
		})
	}
}

func TestToFloat64(t *testing.T) {
	tests := []struct {
		name        string
		input       string
		expected    float64
		expectError bool
	}{
		{"integer", "123", 123.0, false},
		{"float", "3.14", 3.14, false},
		{"with spaces", "  2.71  ", 2.71, false},
		{"scientific", "1.23e10", 1.23e10, false},
		{"invalid", "abc", 0, true},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result, err := utils.ToFloat64(tt.input)
			if tt.expectError {
				if err == nil {
					t.Errorf("ToFloat64(%s) expected error, got nil", tt.input)
				}
			} else {
				if err != nil {
					t.Errorf("ToFloat64(%s) unexpected error: %v", tt.input, err)
				}
				if result != tt.expected {
					t.Errorf("ToFloat64(%s) = %f; want %f", tt.input, result, tt.expected)
				}
			}
		})
	}
}

func TestToBool(t *testing.T) {
	tests := []struct {
		name        string
		input       string
		expected    bool
		expectError bool
	}{
		{"true", "true", true, false},
		{"True", "True", true, false},
		{"1", "1", true, false},
		{"yes", "yes", true, false},
		{"on", "on", true, false},
		{"y", "y", true, false},
		{"t", "t", true, false},
		{"false", "false", false, false},
		{"False", "False", false, false},
		{"0", "0", false, false},
		{"no", "no", false, false},
		{"off", "off", false, false},
		{"n", "n", false, false},
		{"f", "f", false, false},
		{"empty", "", false, false},
		{"with spaces", "  true  ", true, false},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result, err := utils.ToBool(tt.input)
			if tt.expectError {
				if err == nil {
					t.Errorf("ToBool(%s) expected error, got nil", tt.input)
				}
			} else {
				if err != nil {
					t.Errorf("ToBool(%s) unexpected error: %v", tt.input, err)
				}
				if result != tt.expected {
					t.Errorf("ToBool(%s) = %t; want %t", tt.input, result, tt.expected)
				}
			}
		})
	}
}

func TestToStringSlice(t *testing.T) {
	tests := []struct {
		name     string
		input    interface{}
		expected []string
	}{
		{"nil", nil, []string{}},
		{"string", "hello", []string{"hello"}},
		{"string slice", []string{"a", "b", "c"}, []string{"a", "b", "c"}},
		{"int slice", []int{1, 2, 3}, []string{"1", "2", "3"}},
		{"mixed interface slice", []interface{}{1, "hello", true}, []string{"1", "hello", "true"}},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := utils.ToStringSlice(tt.input)
			if len(result) != len(tt.expected) {
				t.Errorf("ToStringSlice(%v) length = %d; want %d", tt.input, len(result), len(tt.expected))
				return
			}
			for i, v := range result {
				if v != tt.expected[i] {
					t.Errorf("ToStringSlice(%v)[%d] = %s; want %s", tt.input, i, v, tt.expected[i])
				}
			}
		})
	}
}

func TestToIntSlice(t *testing.T) {
	tests := []struct {
		name        string
		input       []string
		expected    []int
		expectError bool
	}{
		{"valid", []string{"1", "2", "3"}, []int{1, 2, 3}, false},
		{"with spaces", []string{" 10 ", " 20 "}, []int{10, 20}, false},
		{"invalid", []string{"1", "abc", "3"}, nil, true},
		{"empty", []string{}, []int{}, false},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result, err := utils.ToIntSlice(tt.input)
			if tt.expectError {
				if err == nil {
					t.Errorf("ToIntSlice(%v) expected error, got nil", tt.input)
				}
			} else {
				if err != nil {
					t.Errorf("ToIntSlice(%v) unexpected error: %v", tt.input, err)
				}
				if len(result) != len(tt.expected) {
					t.Errorf("ToIntSlice(%v) length = %d; want %d", tt.input, len(result), len(tt.expected))
					return
				}
				for i, v := range result {
					if v != tt.expected[i] {
						t.Errorf("ToIntSlice(%v)[%d] = %d; want %d", tt.input, i, v, tt.expected[i])
					}
				}
			}
		})
	}
}

func TestStringPtr(t *testing.T) {
	str := "hello"
	result := utils.StringPtr(str)
	if result == nil {
		t.Error("StringPtr returned nil")
		return
	}
	if *result != str {
		t.Errorf("StringPtr(%s) = %s; want %s", str, *result, str)
	}
}

func TestStringPtrWithDefault(t *testing.T) {
	tests := []struct {
		name         string
		input        *string
		defaultValue string
		expected     string
	}{
		{"non-nil", utils.StringPtr("hello"), "default", "hello"},
		{"nil", nil, "default", "default"},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := utils.StringPtrWithDefault(tt.input, tt.defaultValue)
			if result != tt.expected {
				t.Errorf("StringPtrWithDefault() = %s; want %s", result, tt.expected)
			}
		})
	}
}

func TestIsEmpty(t *testing.T) {
	tests := []struct {
		name     string
		input    interface{}
		expected bool
	}{
		{"nil", nil, true},
		{"empty string", "", true},
		{"whitespace string", "   ", true},
		{"non-empty string", "hello", false},
		{"empty slice", []string{}, true},
		{"non-empty slice", []string{"a"}, false},
		{"empty map", map[string]string{}, true},
		{"non-empty map", map[string]string{"a": "b"}, false},
		{"zero int", 0, false},
		{"non-zero int", 42, false},
		{"nil pointer", (*string)(nil), true},
		{"non-nil pointer", utils.StringPtr("hello"), false},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := utils.IsEmpty(tt.input)
			if result != tt.expected {
				t.Errorf("IsEmpty(%v) = %t; want %t", tt.input, result, tt.expected)
			}
		})
	}
}

func TestIsNotEmpty(t *testing.T) {
	tests := []struct {
		name     string
		input    interface{}
		expected bool
	}{
		{"nil", nil, false},
		{"empty string", "", false},
		{"non-empty string", "hello", true},
		{"empty slice", []string{}, false},
		{"non-empty slice", []string{"a"}, true},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := utils.IsNotEmpty(tt.input)
			if result != tt.expected {
				t.Errorf("IsNotEmpty(%v) = %t; want %t", tt.input, result, tt.expected)
			}
		})
	}
}

func TestSafeFunctions(t *testing.T) {
	t.Run("SafeString", func(t *testing.T) {
		if utils.SafeString(nil) != "" {
			t.Error("SafeString(nil) should return empty string")
		}
		if utils.SafeString(utils.StringPtr("hello")) != "hello" {
			t.Error("SafeString with valid pointer failed")
		}
	})

	t.Run("SafeInt", func(t *testing.T) {
		if utils.SafeInt(nil) != 0 {
			t.Error("SafeInt(nil) should return 0")
		}
		if utils.SafeInt(utils.IntPtr(42)) != 42 {
			t.Error("SafeInt with valid pointer failed")
		}
	})

	t.Run("SafeBool", func(t *testing.T) {
		if utils.SafeBool(nil) != false {
			t.Error("SafeBool(nil) should return false")
		}
		if utils.SafeBool(utils.BoolPtr(true)) != true {
			t.Error("SafeBool with valid pointer failed")
		}
	})
}

func TestContains(t *testing.T) {
	tests := []struct {
		name     string
		slice    interface{}
		item     interface{}
		expected bool
	}{
		{"string slice contains", []string{"a", "b", "c"}, "b", true},
		{"string slice not contains", []string{"a", "b", "c"}, "d", false},
		{"int slice contains", []int{1, 2, 3}, 2, true},
		{"int slice not contains", []int{1, 2, 3}, 4, false},
		{"not a slice", "not a slice", "item", false},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := utils.Contains(tt.slice, tt.item)
			if result != tt.expected {
				t.Errorf("Contains(%v, %v) = %t; want %t", tt.slice, tt.item, result, tt.expected)
			}
		})
	}
}

func TestStringInSlice(t *testing.T) {
	slice := []string{"apple", "banana", "cherry"}

	if !utils.StringInSlice("apple", slice) {
		t.Error("StringInSlice should find 'apple'")
	}

	if utils.StringInSlice("grape", slice) {
		t.Error("StringInSlice should not find 'grape'")
	}
}

func TestIntInSlice(t *testing.T) {
	slice := []int{1, 2, 3, 4, 5}

	if !utils.IntInSlice(3, slice) {
		t.Error("IntInSlice should find 3")
	}

	if utils.IntInSlice(6, slice) {
		t.Error("IntInSlice should not find 6")
	}
}
