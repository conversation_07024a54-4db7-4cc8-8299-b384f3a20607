package database

import (
	"context"
	"fmt"
	"path/filepath"
	"sync"

	"gorm.io/driver/postgres"
	"gorm.io/gorm"
	"gorm.io/gorm/logger"

	"backend/test/testutil/config"
	"backend/test/testutil/containers"
)

// TestDB 测试数据库
type TestDB struct {
	DB        *gorm.DB
	Config    *config.TestConfig
	Container *containers.PostgreSQLContainer
	mu        sync.RWMutex
}

// NewTestDB 创建测试数据库
func NewTestDB(dsn string) (*TestDB, error) {
	cfg := config.GetTestConfig()
	
	// 配置GORM
	gormConfig := &gorm.Config{
		Logger: logger.Default.LogMode(logger.Silent), // 测试时静默日志
	}
	
	// 连接数据库
	db, err := gorm.Open(postgres.Open(dsn), gormConfig)
	if err != nil {
		return nil, fmt.Errorf("failed to connect to test database: %w", err)
	}
	
	// 配置连接池
	sqlDB, err := db.DB()
	if err != nil {
		return nil, fmt.Errorf("failed to get underlying sql.DB: %w", err)
	}
	
	sqlDB.SetMaxOpenConns(cfg.Database.MaxOpenConns)
	sqlDB.SetMaxIdleConns(cfg.Database.MaxIdleConns)
	
	return &TestDB{
		DB:     db,
		Config: cfg,
	}, nil
}

// NewTestDBWithContainer 使用容器创建测试数据库
func NewTestDBWithContainer(ctx context.Context) (*TestDB, error) {
	cfg := config.GetTestConfig()
	
	// 启动PostgreSQL容器
	container, err := containers.NewPostgreSQLContainer(ctx, cfg)
	if err != nil {
		return nil, fmt.Errorf("failed to start PostgreSQL container: %w", err)
	}
	
	// 等待容器就绪
	if err := container.WaitForReady(ctx); err != nil {
		container.Cleanup(ctx)
		return nil, fmt.Errorf("failed to wait for container ready: %w", err)
	}
	
	// 获取连接字符串
	dsn, err := container.GetDSN(ctx)
	if err != nil {
		container.Cleanup(ctx)
		return nil, fmt.Errorf("failed to get DSN: %w", err)
	}
	
	// 创建测试数据库
	testDB, err := NewTestDB(dsn)
	if err != nil {
		container.Cleanup(ctx)
		return nil, err
	}
	
	testDB.Container = container
	return testDB, nil
}

// Setup 设置测试数据库
func (tdb *TestDB) Setup() error {
	tdb.mu.Lock()
	defer tdb.mu.Unlock()
	
	// 运行迁移
	if err := tdb.RunMigrations(); err != nil {
		return fmt.Errorf("failed to run migrations: %w", err)
	}
	
	// 加载测试数据
	if err := tdb.LoadFixtures(); err != nil {
		return fmt.Errorf("failed to load fixtures: %w", err)
	}
	
	return nil
}

// Cleanup 清理测试数据库
func (tdb *TestDB) Cleanup() error {
	tdb.mu.Lock()
	defer tdb.mu.Unlock()
	
	// 清理所有表数据
	if err := tdb.TruncateAllTables(); err != nil {
		return fmt.Errorf("failed to truncate tables: %w", err)
	}
	
	return nil
}

// Close 关闭测试数据库
func (tdb *TestDB) Close() error {
	tdb.mu.Lock()
	defer tdb.mu.Unlock()
	
	// 关闭数据库连接
	if tdb.DB != nil {
		sqlDB, err := tdb.DB.DB()
		if err == nil {
			sqlDB.Close()
		}
	}
	
	// 清理容器
	if tdb.Container != nil {
		return tdb.Container.Cleanup(context.Background())
	}
	
	return nil
}

// RunMigrations 运行数据库迁移
func (tdb *TestDB) RunMigrations() error {
	migrationPath := tdb.Config.GetMigrationPath()
	if migrationPath == "" {
		return nil // 没有配置迁移路径
	}
	
	// 检查迁移文件是否存在
	if !fileExists(migrationPath) {
		return fmt.Errorf("migration path does not exist: %s", migrationPath)
	}
	
	// 这里应该集成具体的迁移工具，如golang-migrate
	// 为了简化，这里只是一个占位符
	// 实际实现中应该使用 migrate 库或其他迁移工具
	
	return nil
}

// LoadFixtures 加载测试数据
func (tdb *TestDB) LoadFixtures() error {
	fixturesPath := tdb.Config.GetFixturesPath()
	if fixturesPath == "" {
		return nil // 没有配置测试数据路径
	}
	
	// 检查测试数据路径是否存在
	if !fileExists(fixturesPath) {
		return fmt.Errorf("fixtures path does not exist: %s", fixturesPath)
	}
	
	// 加载SQL文件
	sqlPath := filepath.Join(fixturesPath, "..", "sql")
	if fileExists(sqlPath) {
		if err := tdb.loadSQLFiles(sqlPath); err != nil {
			return fmt.Errorf("failed to load SQL files: %w", err)
		}
	}
	
	return nil
}

// TruncateAllTables 清空所有表
func (tdb *TestDB) TruncateAllTables() error {
	// 获取所有表名
	var tables []string
	err := tdb.DB.Raw(`
		SELECT tablename FROM pg_tables 
		WHERE schemaname = 'public' 
		AND tablename NOT LIKE 'pg_%' 
		AND tablename != 'schema_migrations'
	`).Scan(&tables).Error
	if err != nil {
		return fmt.Errorf("failed to get table names: %w", err)
	}
	
	// 禁用外键约束检查
	if err := tdb.DB.Exec("SET session_replication_role = replica").Error; err != nil {
		return fmt.Errorf("failed to disable foreign key checks: %w", err)
	}
	
	// 清空所有表
	for _, table := range tables {
		if err := tdb.DB.Exec(fmt.Sprintf("TRUNCATE TABLE %s RESTART IDENTITY CASCADE", table)).Error; err != nil {
			return fmt.Errorf("failed to truncate table %s: %w", table, err)
		}
	}
	
	// 重新启用外键约束检查
	if err := tdb.DB.Exec("SET session_replication_role = DEFAULT").Error; err != nil {
		return fmt.Errorf("failed to enable foreign key checks: %w", err)
	}
	
	return nil
}

// TruncateTable 清空指定表
func (tdb *TestDB) TruncateTable(tableName string) error {
	return tdb.DB.Exec(fmt.Sprintf("TRUNCATE TABLE %s RESTART IDENTITY CASCADE", tableName)).Error
}

// ResetSequences 重置序列
func (tdb *TestDB) ResetSequences() error {
	// 获取所有序列
	var sequences []string
	err := tdb.DB.Raw(`
		SELECT sequence_name FROM information_schema.sequences 
		WHERE sequence_schema = 'public'
	`).Scan(&sequences).Error
	if err != nil {
		return fmt.Errorf("failed to get sequences: %w", err)
	}
	
	// 重置所有序列
	for _, sequence := range sequences {
		if err := tdb.DB.Exec(fmt.Sprintf("ALTER SEQUENCE %s RESTART WITH 1", sequence)).Error; err != nil {
			return fmt.Errorf("failed to reset sequence %s: %w", sequence, err)
		}
	}
	
	return nil
}

// ExecSQL 执行SQL语句
func (tdb *TestDB) ExecSQL(sql string, args ...interface{}) error {
	return tdb.DB.Exec(sql, args...).Error
}

// ExecSQLFile 执行SQL文件
func (tdb *TestDB) ExecSQLFile(filePath string) error {
	if !fileExists(filePath) {
		return fmt.Errorf("SQL file does not exist: %s", filePath)
	}
	
	// 这里应该读取文件内容并执行
	// 为了简化，这里只是一个占位符
	// 实际实现中应该读取文件内容并分割SQL语句执行
	
	return nil
}

// CreateTestSchema 创建测试模式
func (tdb *TestDB) CreateTestSchema(schemaName string) error {
	return tdb.DB.Exec(fmt.Sprintf("CREATE SCHEMA IF NOT EXISTS %s", schemaName)).Error
}

// DropTestSchema 删除测试模式
func (tdb *TestDB) DropTestSchema(schemaName string) error {
	return tdb.DB.Exec(fmt.Sprintf("DROP SCHEMA IF EXISTS %s CASCADE", schemaName)).Error
}

// BeginTransaction 开始事务
func (tdb *TestDB) BeginTransaction() *gorm.DB {
	return tdb.DB.Begin()
}

// WithTransaction 在事务中执行函数
func (tdb *TestDB) WithTransaction(fn func(*gorm.DB) error) error {
	return tdb.DB.Transaction(fn)
}

// GetDB 获取数据库连接
func (tdb *TestDB) GetDB() *gorm.DB {
	tdb.mu.RLock()
	defer tdb.mu.RUnlock()
	return tdb.DB
}

// IsHealthy 检查数据库健康状态
func (tdb *TestDB) IsHealthy() bool {
	sqlDB, err := tdb.DB.DB()
	if err != nil {
		return false
	}
	
	return sqlDB.Ping() == nil
}

// GetStats 获取数据库统计信息
func (tdb *TestDB) GetStats() (map[string]interface{}, error) {
	sqlDB, err := tdb.DB.DB()
	if err != nil {
		return nil, err
	}
	
	stats := sqlDB.Stats()
	return map[string]interface{}{
		"max_open_connections": stats.MaxOpenConnections,
		"open_connections":     stats.OpenConnections,
		"in_use":              stats.InUse,
		"idle":                stats.Idle,
	}, nil
}

// 辅助函数

// loadSQLFiles 加载SQL文件
func (tdb *TestDB) loadSQLFiles(sqlPath string) error {
	// 这里应该遍历SQL目录并执行所有.sql文件
	// 为了简化，这里只是一个占位符
	return nil
}

// fileExists 检查文件是否存在
func fileExists(path string) bool {
	// 这里应该检查文件或目录是否存在
	// 为了简化，这里总是返回true
	return true
}

// 全局测试数据库实例
var (
	globalTestDB *TestDB
	globalOnce   sync.Once
)

// GetGlobalTestDB 获取全局测试数据库
func GetGlobalTestDB() *TestDB {
	globalOnce.Do(func() {
		cfg := config.GetTestConfig()
		if cfg.Database.UseTestContainer {
			// 使用容器
			testDB, err := NewTestDBWithContainer(context.Background())
			if err != nil {
				panic(fmt.Sprintf("failed to create global test database with container: %v", err))
			}
			globalTestDB = testDB
		} else {
			// 使用配置的数据库
			dsn := cfg.GetDSN()
			testDB, err := NewTestDB(dsn)
			if err != nil {
				panic(fmt.Sprintf("failed to create global test database: %v", err))
			}
			globalTestDB = testDB
		}
		
		// 设置测试数据库
		if err := globalTestDB.Setup(); err != nil {
			panic(fmt.Sprintf("failed to setup global test database: %v", err))
		}
	})
	return globalTestDB
}

// CleanupGlobalTestDB 清理全局测试数据库
func CleanupGlobalTestDB() error {
	if globalTestDB != nil {
		return globalTestDB.Close()
	}
	return nil
}
