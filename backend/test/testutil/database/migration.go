package database

import (
	"fmt"
	"io/fs"
	"os"
	"path/filepath"
	"sort"
	"strings"

	"gorm.io/gorm"
)

// MigrationManager 迁移管理器
type MigrationManager struct {
	db            *gorm.DB
	migrationPath string
}

// NewMigrationManager 创建迁移管理器
func NewMigrationManager(db *gorm.DB, migrationPath string) *MigrationManager {
	return &MigrationManager{
		db:            db,
		migrationPath: migrationPath,
	}
}

// Migration 迁移信息
type Migration struct {
	Version   string
	Name      string
	UpSQL     string
	DownSQL   string
	FilePath  string
	Applied   bool
	AppliedAt *string
}

// MigrationRecord 迁移记录
type MigrationRecord struct {
	Version   string `gorm:"primaryKey;size:255"`
	Name      string `gorm:"size:255;not null"`
	AppliedAt string `gorm:"not null"`
}

// TableName 表名
func (MigrationRecord) TableName() string {
	return "schema_migrations"
}

// Up 执行向上迁移
func (mm *MigrationManager) Up() error {
	// 确保迁移表存在
	if err := mm.ensureMigrationTable(); err != nil {
		return fmt.Errorf("failed to ensure migration table: %w", err)
	}

	// 获取所有迁移文件
	migrations, err := mm.loadMigrations()
	if err != nil {
		return fmt.Errorf("failed to load migrations: %w", err)
	}

	// 获取已应用的迁移
	appliedMigrations, err := mm.getAppliedMigrations()
	if err != nil {
		return fmt.Errorf("failed to get applied migrations: %w", err)
	}

	// 标记已应用的迁移
	appliedMap := make(map[string]bool)
	for _, applied := range appliedMigrations {
		appliedMap[applied.Version] = true
	}

	// 执行未应用的迁移
	for _, migration := range migrations {
		if !appliedMap[migration.Version] {
			if err := mm.applyMigration(migration); err != nil {
				return fmt.Errorf("failed to apply migration %s: %w", migration.Version, err)
			}
		}
	}

	return nil
}

// Down 执行向下迁移
func (mm *MigrationManager) Down(steps int) error {
	// 确保迁移表存在
	if err := mm.ensureMigrationTable(); err != nil {
		return fmt.Errorf("failed to ensure migration table: %w", err)
	}

	// 获取已应用的迁移（按版本倒序）
	appliedMigrations, err := mm.getAppliedMigrations()
	if err != nil {
		return fmt.Errorf("failed to get applied migrations: %w", err)
	}

	// 按版本倒序排列
	sort.Slice(appliedMigrations, func(i, j int) bool {
		return appliedMigrations[i].Version > appliedMigrations[j].Version
	})

	// 限制回滚步数
	if steps > len(appliedMigrations) {
		steps = len(appliedMigrations)
	}

	// 加载所有迁移文件
	allMigrations, err := mm.loadMigrations()
	if err != nil {
		return fmt.Errorf("failed to load migrations: %w", err)
	}

	// 创建迁移映射
	migrationMap := make(map[string]*Migration)
	for _, migration := range allMigrations {
		migrationMap[migration.Version] = migration
	}

	// 执行回滚
	for i := 0; i < steps; i++ {
		appliedMigration := appliedMigrations[i]
		migration, exists := migrationMap[appliedMigration.Version]
		if !exists {
			return fmt.Errorf("migration file not found for version %s", appliedMigration.Version)
		}

		if err := mm.rollbackMigration(migration); err != nil {
			return fmt.Errorf("failed to rollback migration %s: %w", migration.Version, err)
		}
	}

	return nil
}

// Reset 重置所有迁移
func (mm *MigrationManager) Reset() error {
	// 获取已应用的迁移数量
	var count int64
	if err := mm.db.Model(&MigrationRecord{}).Count(&count).Error; err != nil {
		return fmt.Errorf("failed to count applied migrations: %w", err)
	}

	// 回滚所有迁移
	return mm.Down(int(count))
}

// Status 获取迁移状态
func (mm *MigrationManager) Status() ([]*Migration, error) {
	// 确保迁移表存在
	if err := mm.ensureMigrationTable(); err != nil {
		return nil, fmt.Errorf("failed to ensure migration table: %w", err)
	}

	// 获取所有迁移文件
	migrations, err := mm.loadMigrations()
	if err != nil {
		return nil, fmt.Errorf("failed to load migrations: %w", err)
	}

	// 获取已应用的迁移
	appliedMigrations, err := mm.getAppliedMigrations()
	if err != nil {
		return nil, fmt.Errorf("failed to get applied migrations: %w", err)
	}

	// 创建已应用迁移的映射
	appliedMap := make(map[string]*MigrationRecord)
	for _, applied := range appliedMigrations {
		appliedMap[applied.Version] = applied
	}

	// 标记迁移状态
	for _, migration := range migrations {
		if applied, exists := appliedMap[migration.Version]; exists {
			migration.Applied = true
			migration.AppliedAt = &applied.AppliedAt
		}
	}

	return migrations, nil
}

// ensureMigrationTable 确保迁移表存在
func (mm *MigrationManager) ensureMigrationTable() error {
	return mm.db.AutoMigrate(&MigrationRecord{})
}

// loadMigrations 加载所有迁移文件
func (mm *MigrationManager) loadMigrations() ([]*Migration, error) {
	var migrations []*Migration

	err := filepath.WalkDir(mm.migrationPath, func(path string, d fs.DirEntry, err error) error {
		if err != nil {
			return err
		}

		if d.IsDir() || !strings.HasSuffix(path, ".sql") {
			return nil
		}

		migration, err := mm.parseMigrationFile(path)
		if err != nil {
			return fmt.Errorf("failed to parse migration file %s: %w", path, err)
		}

		migrations = append(migrations, migration)
		return nil
	})

	if err != nil {
		return nil, err
	}

	// 按版本排序
	sort.Slice(migrations, func(i, j int) bool {
		return migrations[i].Version < migrations[j].Version
	})

	return migrations, nil
}

// parseMigrationFile 解析迁移文件
func (mm *MigrationManager) parseMigrationFile(filePath string) (*Migration, error) {
	// 从文件名提取版本和名称
	fileName := filepath.Base(filePath)
	parts := strings.SplitN(fileName, "_", 2)
	if len(parts) != 2 {
		return nil, fmt.Errorf("invalid migration file name format: %s", fileName)
	}

	version := parts[0]
	name := strings.TrimSuffix(parts[1], ".sql")

	// 读取文件内容
	content, err := os.ReadFile(filePath)
	if err != nil {
		return nil, fmt.Errorf("failed to read migration file: %w", err)
	}

	// 分割UP和DOWN部分
	contentStr := string(content)
	upSQL, downSQL := mm.splitMigrationContent(contentStr)

	return &Migration{
		Version:  version,
		Name:     name,
		UpSQL:    upSQL,
		DownSQL:  downSQL,
		FilePath: filePath,
	}, nil
}

// splitMigrationContent 分割迁移内容
func (mm *MigrationManager) splitMigrationContent(content string) (upSQL, downSQL string) {
	// 查找 -- +migrate Up 和 -- +migrate Down 标记
	lines := strings.Split(content, "\n")
	var upLines, downLines []string
	var currentSection string

	for _, line := range lines {
		trimmed := strings.TrimSpace(line)
		if strings.Contains(trimmed, "-- +migrate Up") {
			currentSection = "up"
			continue
		} else if strings.Contains(trimmed, "-- +migrate Down") {
			currentSection = "down"
			continue
		}

		switch currentSection {
		case "up":
			upLines = append(upLines, line)
		case "down":
			downLines = append(downLines, line)
		}
	}

	return strings.Join(upLines, "\n"), strings.Join(downLines, "\n")
}

// getAppliedMigrations 获取已应用的迁移
func (mm *MigrationManager) getAppliedMigrations() ([]*MigrationRecord, error) {
	var records []*MigrationRecord
	err := mm.db.Order("version").Find(&records).Error
	return records, err
}

// applyMigration 应用迁移
func (mm *MigrationManager) applyMigration(migration *Migration) error {
	// 在事务中执行迁移
	return mm.db.Transaction(func(tx *gorm.DB) error {
		// 执行UP SQL
		if migration.UpSQL != "" {
			if err := tx.Exec(migration.UpSQL).Error; err != nil {
				return fmt.Errorf("failed to execute up SQL: %w", err)
			}
		}

		// 记录迁移
		record := &MigrationRecord{
			Version:   migration.Version,
			Name:      migration.Name,
			AppliedAt: "NOW()",
		}

		if err := tx.Create(record).Error; err != nil {
			return fmt.Errorf("failed to record migration: %w", err)
		}

		return nil
	})
}

// rollbackMigration 回滚迁移
func (mm *MigrationManager) rollbackMigration(migration *Migration) error {
	// 在事务中执行回滚
	return mm.db.Transaction(func(tx *gorm.DB) error {
		// 执行DOWN SQL
		if migration.DownSQL != "" {
			if err := tx.Exec(migration.DownSQL).Error; err != nil {
				return fmt.Errorf("failed to execute down SQL: %w", err)
			}
		}

		// 删除迁移记录
		if err := tx.Where("version = ?", migration.Version).Delete(&MigrationRecord{}).Error; err != nil {
			return fmt.Errorf("failed to delete migration record: %w", err)
		}

		return nil
	})
}
