package testutil

import (
	"time"

	"backend/internal/domain/user/entity"
	"backend/internal/domain/user/valueobject"

	"github.com/google/uuid"
)

// UserBuilder 用户构建器，用于创建测试用户
type UserBuilder struct {
	user *entity.User
}

// NewUserBuilder 创建新的用户构建器
func NewUserBuilder() *UserBuilder {
	user, _ := entity.NewUser(
		valueobject.UserProfile{
			Nickname: "testuser",
		},
		"testuser",
		"<EMAIL>",
		"***********",
	)
	return &UserBuilder{user: user}
}

// WithBusinessID 设置业务ID
func (b *UserBuilder) WithBusinessID(id string) *UserBuilder {
	b.user.BusinessID = id
	return b
}

// WithUsername 设置用户名
func (b *UserBuilder) WithUsername(username string) *UserBuilder {
	b.user.Username = username
	return b
}

// WithEmail 设置邮箱
func (b *UserBuilder) WithEmail(email string) *UserBuilder {
	b.user.Email = email
	return b
}

// WithName 设置姓名
func (b *UserBuilder) WithName(firstName, lastName string) *UserBuilder {
	b.user.Profile.FirstName = firstName
	b.user.Profile.LastName = lastName
	return b
}

// WithStatus 设置状态
func (b *UserBuilder) WithStatus(status entity.UserStatus) *UserBuilder {
	b.user.Status = status
	return b
}

// WithPhone 设置电话
func (b *UserBuilder) WithPhone(phone string) *UserBuilder {
	b.user.Phone = phone
	return b
}

// WithVersion 设置版本号
func (b *UserBuilder) WithVersion(version int) *UserBuilder {
	b.user.Version = version
	return b
}

// Active 设置为激活状态
func (b *UserBuilder) Active() *UserBuilder {
	b.user.Status = entity.UserStatusActive
	return b
}

// Inactive 设置为未激活状态
func (b *UserBuilder) Inactive() *UserBuilder {
	b.user.Status = entity.UserStatusInactive
	return b
}

// Pending 设置为待激活状态
func (b *UserBuilder) Pending() *UserBuilder {
	b.user.Status = entity.UserStatusPending
	return b
}

// Suspended 设置为暂停状态
func (b *UserBuilder) Suspended() *UserBuilder {
	b.user.Status = entity.UserStatusSuspended
	return b
}

// Banned 设置为封禁状态
func (b *UserBuilder) Banned() *UserBuilder {
	b.user.Status = entity.UserStatusBanned
	return b
}

// Build 构建用户实体
func (b *UserBuilder) Build() *entity.User {
	return b.user
}

// 预定义的测试用户创建函数

// ValidUser 创建一个有效的测试用户
func ValidUser() *entity.User {
	return NewUserBuilder().Active().Build()
}

// PendingUser 创建一个待激活的测试用户
func PendingUser() *entity.User {
	return NewUserBuilder().Pending().Build()
}

// InactiveUser 创建一个未激活的测试用户
func InactiveUser() *entity.User {
	return NewUserBuilder().Inactive().Build()
}

// SuspendedUser 创建一个暂停的测试用户
func SuspendedUser() *entity.User {
	return NewUserBuilder().Suspended().Build()
}

// BannedUser 创建一个封禁的测试用户
func BannedUser() *entity.User {
	return NewUserBuilder().Banned().Build()
}

// UserWithUsername 创建指定用户名的测试用户
func UserWithUsername(username string) *entity.User {
	return NewUserBuilder().WithUsername(username).Active().Build()
}

// UserWithEmail 创建指定邮箱的测试用户
func UserWithEmail(email string) *entity.User {
	return NewUserBuilder().WithEmail(email).Active().Build()
}

// MultipleUsers 创建多个测试用户
func MultipleUsers(count int) []*entity.User {
	users := make([]*entity.User, count)
	for i := 0; i < count; i++ {
		users[i] = NewUserBuilder().
			WithUsername("user" + string(rune(i))).
			WithEmail("user" + string(rune(i)) + "@example.com").
			Active().
			Build()
	}
	return users
}

// TestConstants 测试常量
const (
	TestTenantID       = "test-tenant-123"
	TestUserID         = "test-user-123"
	TestUsername       = "testuser"
	TestEmail          = "<EMAIL>"
	TestPhone          = "***********"
	TestFirstName      = "Test"
	TestLastName       = "User"
	TestPassword       = "password123"
	TestHashedPassword = "$2a$10$example.hashed.password"
)

// TestErrors 测试错误
var (
	TestDatabaseError   = "database connection failed"
	TestValidationError = "validation failed"
	TestNotFoundError   = "record not found"
	TestConflictError   = "resource conflict"
	TestPermissionError = "permission denied"
)

// UserAssertions 用户断言辅助函数

// AssertUserEqual 断言两个用户相等（比较关键字段）
func AssertUserEqual(t interface{}, expected, actual *entity.User) {
	// 这里应该使用实际的测试框架断言
	// 示例实现，实际使用时需要适配具体的测试框架
	if expected.BusinessID != actual.BusinessID {
		panic("BusinessID not equal")
	}
	if expected.Username != actual.Username {
		panic("Username not equal")
	}
	if expected.Email != actual.Email {
		panic("Email not equal")
	}
	if expected.Status != actual.Status {
		panic("Status not equal")
	}
}

// AssertUserActive 断言用户是激活状态
func AssertUserActive(t interface{}, user *entity.User) {
	if user.Status != entity.UserStatusActive {
		panic("User should be active")
	}
}

// AssertUserInactive 断言用户是未激活状态
func AssertUserInactive(t interface{}, user *entity.User) {
	if user.Status == entity.UserStatusActive {
		panic("User should be inactive")
	}
}

// AssertUserDeleted 断言用户已删除
func AssertUserDeleted(t interface{}, user *entity.User) {
	if !user.IsDeleted() {
		panic("User should be deleted")
	}
}

// TestSetup 测试设置辅助函数

// SetupTestData 设置测试数据
func SetupTestData() map[string]*entity.User {
	return map[string]*entity.User{
		"active":    ValidUser(),
		"pending":   PendingUser(),
		"inactive":  InactiveUser(),
		"suspended": SuspendedUser(),
		"banned":    BannedUser(),
	}
}

// CleanupTestData 清理测试数据
func CleanupTestData() {
	// 实际实现中应该清理数据库或其他存储
	// 这里只是示例
}

// MockContext 创建模拟上下文
func MockContext() interface{} {
	// 返回模拟的上下文对象
	// 实际实现需要根据项目的上下文类型
	return nil
}

// TimeHelper 时间辅助函数

// FixedTime 返回固定的测试时间
func FixedTime() time.Time {
	return time.Date(2024, 1, 1, 12, 0, 0, 0, time.UTC)
}

// TimeAfter 返回指定时间后的时间
func TimeAfter(base time.Time, duration time.Duration) time.Time {
	return base.Add(duration)
}

// TimeBefore 返回指定时间前的时间
func TimeBefore(base time.Time, duration time.Duration) time.Time {
	return base.Add(-duration)
}

// RandomData 随机数据生成器

// RandomUsername 生成随机用户名
func RandomUsername() string {
	return "user_" + uuid.New().String()[:8]
}

// RandomEmail 生成随机邮箱
func RandomEmail() string {
	return uuid.New().String()[:8] + "@example.com"
}

// RandomTenantID 生成随机租户ID
func RandomTenantID() string {
	return "tenant_" + uuid.New().String()[:8]
}

// Validation 验证辅助函数

// IsValidUser 检查用户是否有效
func IsValidUser(user *entity.User) bool {
	if user == nil {
		return false
	}
	if user.BusinessID == "" {
		return false
	}
	if user.Username == "" || user.Email == "" {
		return false
	}
	return true
}

// IsValidEmail 检查邮箱格式是否有效
func IsValidEmail(email string) bool {
	// 简单的邮箱格式检查
	return len(email) > 5 &&
		len(email) <= 100 &&
		contains(email, "@") &&
		contains(email, ".")
}

// contains 检查字符串是否包含子字符串
func contains(s, substr string) bool {
	for i := 0; i <= len(s)-len(substr); i++ {
		if s[i:i+len(substr)] == substr {
			return true
		}
	}
	return false
}
