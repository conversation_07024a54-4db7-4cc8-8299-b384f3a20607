package builders

import (
	"time"

	"backend/internal/domain/tenant/entity"
	"backend/internal/domain/tenant/valueobject"
	"backend/internal/shared/types"
)

// TenantBuilder 租户构建器
type TenantBuilder struct {
	tenant *entity.Tenant
}

// NewTenantBuilder 创建租户构建器
func NewTenantBuilder() *TenantBuilder {
	tenant := &entity.Tenant{
		GlobalEntity: types.NewEmptyGlobalEntity(),
		Name:         UniqueString("tenant"),
		Domain:       UniqueString("domain") + ".example.com",
		DisplayName:  UniqueString("display"),
		Type:         valueobject.TenantTypeEnterprise,
		Status:       valueobject.TenantStatusActive,
		Settings: entity.TenantSettings{
			"max_users":        100,
			"max_storage":      1024 * 1024 * 1024, // 1GB
			"allowed_modules":  []string{"user", "product", "order"},
			"time_zone":        "Asia/Shanghai",
			"language":         "zh-CN",
			"currency":         "CNY",
			"date_format":      "YYYY-MM-DD",
			"time_format":      "HH:mm:ss",
			"number_format":    "#,##0.00",
			"enable_audit_log": true,
			"enable_backup":    true,
			"backup_frequency": "daily",
		},
		ContactEmail: RandomEmail(),
		ContactPhone: RandomPhone(),
	}

	// 设置默认ID
	tenant.ID = RandomInt64()
	tenant.BusinessID = UniqueBusinessID()
	tenant.CreatedAt = time.Now()
	tenant.UpdatedAt = time.Now()

	return &TenantBuilder{tenant: tenant}
}

// WithID 设置技术ID
func (b *TenantBuilder) WithID(id int64) *TenantBuilder {
	b.tenant.ID = id
	return b
}

// WithBusinessID 设置业务ID
func (b *TenantBuilder) WithBusinessID(businessID string) *TenantBuilder {
	b.tenant.BusinessID = businessID
	return b
}

// WithName 设置租户名称
func (b *TenantBuilder) WithName(name string) *TenantBuilder {
	b.tenant.Name = name
	return b
}

// WithDomain 设置租户域名
func (b *TenantBuilder) WithDomain(domain string) *TenantBuilder {
	b.tenant.Domain = domain
	return b
}

// WithType 设置租户类型
func (b *TenantBuilder) WithType(tenantType valueobject.TenantType) *TenantBuilder {
	b.tenant.Type = tenantType
	return b
}

// WithStatus 设置租户状态
func (b *TenantBuilder) WithStatus(status valueobject.TenantStatus) *TenantBuilder {
	b.tenant.Status = status
	return b
}

// WithSettings 设置租户配置
func (b *TenantBuilder) WithSettings(settings entity.TenantSettings) *TenantBuilder {
	b.tenant.Settings = settings
	return b
}

// WithContactEmail 设置联系邮箱
func (b *TenantBuilder) WithContactEmail(email string) *TenantBuilder {
	b.tenant.ContactEmail = email
	return b
}

// WithContactPhone 设置联系电话
func (b *TenantBuilder) WithContactPhone(phone string) *TenantBuilder {
	b.tenant.ContactPhone = phone
	return b
}

// WithMaxUsers 设置最大用户数
func (b *TenantBuilder) WithMaxUsers(maxUsers int) *TenantBuilder {
	if b.tenant.Settings == nil {
		b.tenant.Settings = make(entity.TenantSettings)
	}
	b.tenant.Settings["max_users"] = maxUsers
	return b
}

// WithMaxStorage 设置最大存储空间
func (b *TenantBuilder) WithMaxStorage(maxStorage int64) *TenantBuilder {
	if b.tenant.Settings == nil {
		b.tenant.Settings = make(entity.TenantSettings)
	}
	b.tenant.Settings["max_storage"] = maxStorage
	return b
}

// WithAllowedModules 设置允许的模块
func (b *TenantBuilder) WithAllowedModules(modules []string) *TenantBuilder {
	if b.tenant.Settings == nil {
		b.tenant.Settings = make(entity.TenantSettings)
	}
	b.tenant.Settings["allowed_modules"] = modules
	return b
}

// WithTimeZone 设置时区
func (b *TenantBuilder) WithTimeZone(timeZone string) *TenantBuilder {
	if b.tenant.Settings == nil {
		b.tenant.Settings = make(entity.TenantSettings)
	}
	b.tenant.Settings["time_zone"] = timeZone
	return b
}

// WithLanguage 设置语言
func (b *TenantBuilder) WithLanguage(language string) *TenantBuilder {
	if b.tenant.Settings == nil {
		b.tenant.Settings = make(entity.TenantSettings)
	}
	b.tenant.Settings["language"] = language
	return b
}

// WithCurrency 设置货币
func (b *TenantBuilder) WithCurrency(currency string) *TenantBuilder {
	if b.tenant.Settings == nil {
		b.tenant.Settings = make(entity.TenantSettings)
	}
	b.tenant.Settings["currency"] = currency
	return b
}

// WithEmail 设置邮箱
func (b *TenantBuilder) WithEmail(email string) *TenantBuilder {
	b.tenant.ContactEmail = email
	return b
}

// WithPhone 设置电话
func (b *TenantBuilder) WithPhone(phone string) *TenantBuilder {
	b.tenant.ContactPhone = phone
	return b
}

// WithAddress 设置地址
func (b *TenantBuilder) WithAddress(address string) *TenantBuilder {
	if b.tenant.Settings == nil {
		b.tenant.Settings = make(entity.TenantSettings)
	}
	b.tenant.Settings["address"] = address
	return b
}

// WithWebsite 设置网站
func (b *TenantBuilder) WithWebsite(website string) *TenantBuilder {
	if b.tenant.Settings == nil {
		b.tenant.Settings = make(entity.TenantSettings)
	}
	b.tenant.Settings["website"] = website
	return b
}

// WithCreatedAt 设置创建时间
func (b *TenantBuilder) WithCreatedAt(createdAt time.Time) *TenantBuilder {
	b.tenant.CreatedAt = createdAt
	return b
}

// WithUpdatedAt 设置更新时间
func (b *TenantBuilder) WithUpdatedAt(updatedAt time.Time) *TenantBuilder {
	b.tenant.UpdatedAt = updatedAt
	return b
}

// Enterprise 设置为企业版
func (b *TenantBuilder) Enterprise() *TenantBuilder {
	b.tenant.Type = valueobject.TenantTypeEnterprise
	return b
}

// Professional 设置为专业版
func (b *TenantBuilder) Professional() *TenantBuilder {
	b.tenant.Type = valueobject.TenantTypeProfessional
	return b
}

// Standard 设置为标准版
func (b *TenantBuilder) Standard() *TenantBuilder {
	b.tenant.Type = valueobject.TenantTypeBasic // 使用 Basic 替代 Standard
	return b
}

// Basic 设置为基础版
func (b *TenantBuilder) Basic() *TenantBuilder {
	b.tenant.Type = valueobject.TenantTypeBasic
	return b
}

// Active 设置为激活状态
func (b *TenantBuilder) Active() *TenantBuilder {
	b.tenant.Status = valueobject.TenantStatusActive
	return b
}

// Inactive 设置为未激活状态
func (b *TenantBuilder) Inactive() *TenantBuilder {
	b.tenant.Status = valueobject.TenantStatusInactive
	return b
}

// Suspended 设置为暂停状态
func (b *TenantBuilder) Suspended() *TenantBuilder {
	b.tenant.Status = valueobject.TenantStatusSuspended
	return b
}

// Expired 设置为过期状态
func (b *TenantBuilder) Expired() *TenantBuilder {
	b.tenant.Status = valueobject.TenantStatusExpired
	return b
}

// Deleted 设置为已删除状态
func (b *TenantBuilder) Deleted() *TenantBuilder {
	now := time.Now()
	b.tenant.DeletedAt.Time = now
	b.tenant.DeletedAt.Valid = true
	return b
}

// EnableAuditLog 启用审计日志
func (b *TenantBuilder) EnableAuditLog() *TenantBuilder {
	if b.tenant.Settings == nil {
		b.tenant.Settings = make(entity.TenantSettings)
	}
	b.tenant.Settings["enable_audit_log"] = true
	return b
}

// DisableAuditLog 禁用审计日志
func (b *TenantBuilder) DisableAuditLog() *TenantBuilder {
	if b.tenant.Settings == nil {
		b.tenant.Settings = make(entity.TenantSettings)
	}
	b.tenant.Settings["enable_audit_log"] = false
	return b
}

// EnableBackup 启用备份
func (b *TenantBuilder) EnableBackup() *TenantBuilder {
	if b.tenant.Settings == nil {
		b.tenant.Settings = make(entity.TenantSettings)
	}
	b.tenant.Settings["enable_backup"] = true
	return b
}

// DisableBackup 禁用备份
func (b *TenantBuilder) DisableBackup() *TenantBuilder {
	if b.tenant.Settings == nil {
		b.tenant.Settings = make(entity.TenantSettings)
	}
	b.tenant.Settings["enable_backup"] = false
	return b
}

// Build 构建租户对象
func (b *TenantBuilder) Build() *entity.Tenant {
	// 创建副本以避免修改原始对象
	tenant := &entity.Tenant{
		GlobalEntity: b.tenant.GlobalEntity,
		Name:         b.tenant.Name,
		Domain:       b.tenant.Domain,
		DisplayName:  b.tenant.DisplayName,
		Type:         b.tenant.Type,
		Status:       b.tenant.Status,
		Settings:     b.tenant.Settings,
		ContactEmail: b.tenant.ContactEmail,
		ContactPhone: b.tenant.ContactPhone,
	}
	return tenant
}

// BuildPtr 构建租户对象指针
func (b *TenantBuilder) BuildPtr() *entity.Tenant {
	return b.Build()
}

// BuildSlice 构建租户切片
func (b *TenantBuilder) BuildSlice(count int) []*entity.Tenant {
	tenants := make([]*entity.Tenant, count)
	for i := 0; i < count; i++ {
		// 为每个租户生成不同的数据
		builder := NewTenantBuilder()
		if b.tenant.Type != valueobject.TenantTypeEnterprise {
			builder.WithType(b.tenant.Type)
		}
		if b.tenant.Status != valueobject.TenantStatusActive {
			builder.WithStatus(b.tenant.Status)
		}
		tenants[i] = builder.Build()
	}
	return tenants
}

// Clone 克隆构建器
func (b *TenantBuilder) Clone() *TenantBuilder {
	return &TenantBuilder{
		tenant: b.Build(),
	}
}

// Reset 重置构建器
func (b *TenantBuilder) Reset() *TenantBuilder {
	return NewTenantBuilder()
}

// 预定义的租户构建器

// DefaultTenant 默认租户
func DefaultTenant() *TenantBuilder {
	return NewTenantBuilder().
		WithName("默认租户").
		WithDomain("default.example.com").
		Enterprise().
		Active()
}

// TestTenant 测试租户
func TestTenant() *TenantBuilder {
	return NewTenantBuilder().
		WithName("测试租户").
		WithDomain("test.example.com").
		Standard().
		Active()
}

// DemoTenant 演示租户
func DemoTenant() *TenantBuilder {
	return NewTenantBuilder().
		WithName("演示租户").
		WithDomain("demo.example.com").
		Basic().
		Active()
}

// ExpiredTenant 过期租户
func ExpiredTenant() *TenantBuilder {
	return NewTenantBuilder().
		WithName("过期租户").
		WithDomain("expired.example.com").
		Professional().
		Expired()
}

// SuspendedTenant 暂停租户
func SuspendedTenant() *TenantBuilder {
	return NewTenantBuilder().
		WithName("暂停租户").
		WithDomain("suspended.example.com").
		Enterprise().
		Suspended()
}
