package builders

import (
	"time"
)

// Builder 构建器接口
type Builder[T any] interface {
	Build() T
	BuildPtr() *T
	BuildSlice(count int) []T
	Clone() Builder[T]
	Reset() Builder[T]
}

// EntityBuilder 实体构建器接口
type EntityBuilder[T any] interface {
	Builder[T]
	WithID(id int64) EntityBuilder[T]
	WithBusinessID(businessID string) EntityBuilder[T]
	WithCreatedAt(createdAt time.Time) EntityBuilder[T]
	WithUpdatedAt(updatedAt time.Time) EntityBuilder[T]
}

// BaseBuilder 基础构建器
type BaseBuilder[T any] struct {
	entity T
}

// NewBaseBuilder 创建基础构建器
func NewBaseBuilder[T any](entity T) *BaseBuilder[T] {
	return &BaseBuilder[T]{entity: entity}
}

// Build 构建对象
func (b *BaseBuilder[T]) Build() T {
	return b.entity
}

// BuildPtr 构建对象指针
func (b *BaseBuilder[T]) BuildPtr() *T {
	entity := b.Build()
	return &entity
}

// BuildSlice 构建对象切片
func (b *BaseBuilder[T]) BuildSlice(count int) []T {
	entities := make([]T, count)
	for i := 0; i < count; i++ {
		entities[i] = b.Build()
	}
	return entities
}

// Clone 克隆构建器
func (b *BaseBuilder[T]) Clone() Builder[T] {
	return &BaseBuilder[T]{entity: b.entity}
}

// Reset 重置构建器
func (b *BaseBuilder[T]) Reset() Builder[T] {
	var zero T
	return &BaseBuilder[T]{entity: zero}
}

// BuilderFactory 构建器工厂
type BuilderFactory struct{}

// NewBuilderFactory 创建构建器工厂
func NewBuilderFactory() *BuilderFactory {
	return &BuilderFactory{}
}

// User 创建用户构建器
func (f *BuilderFactory) User() *UserBuilder {
	return NewUserBuilder()
}

// Tenant 创建租户构建器
func (f *BuilderFactory) Tenant() *TenantBuilder {
	return NewTenantBuilder()
}

// AdminUser 创建管理员用户构建器
func (f *BuilderFactory) AdminUser() *UserBuilder {
	return NewUserBuilder().WithUsername("admin").WithNickname("管理员用户")
}

// TestUser 创建测试用户构建器
func (f *BuilderFactory) TestUser() *UserBuilder {
	return NewUserBuilder().WithUsername("testuser").WithNickname("测试用户")
}

// DefaultTenant 创建默认租户构建器
func (f *BuilderFactory) DefaultTenant() *TenantBuilder {
	return NewTenantBuilder().
		WithName("默认租户").
		WithDomain("default.example.com").
		Enterprise().
		Active()
}

// TestTenant 创建测试租户构建器
func (f *BuilderFactory) TestTenant() *TenantBuilder {
	return NewTenantBuilder().
		WithName("测试租户").
		WithDomain("test.example.com").
		Basic().
		Active()
}

// 全局构建器工厂实例
var Factory = NewBuilderFactory()

// 便捷函数

// User 创建用户构建器
func User() *UserBuilder {
	return Factory.User()
}

// Tenant 创建租户构建器
func Tenant() *TenantBuilder {
	return Factory.Tenant()
}

// BuilderOptions 构建器选项
type BuilderOptions struct {
	Seed         int64
	DefaultCount int
	TimeZone     *time.Location
}

// DefaultBuilderOptions 默认构建器选项
var DefaultBuilderOptions = &BuilderOptions{
	Seed:         time.Now().UnixNano(),
	DefaultCount: 5,
	TimeZone:     time.UTC,
}

// SetBuilderOptions 设置构建器选项
func SetBuilderOptions(options *BuilderOptions) {
	if options.Seed != 0 {
		SetSeed(options.Seed)
	}
	if options.DefaultCount > 0 {
		DefaultBuilderOptions.DefaultCount = options.DefaultCount
	}
	if options.TimeZone != nil {
		DefaultBuilderOptions.TimeZone = options.TimeZone
	}
}

// ResetBuilderOptions 重置构建器选项
func ResetBuilderOptions() {
	DefaultBuilderOptions = &BuilderOptions{
		Seed:         time.Now().UnixNano(),
		DefaultCount: 5,
		TimeZone:     time.UTC,
	}
	ResetSeed()
}

// BuilderContext 构建器上下文
type BuilderContext struct {
	TenantID int64
	UserID   int64
	TimeZone *time.Location
	Language string
	Currency string
	Now      time.Time
	Seed     int64
}

// NewBuilderContext 创建构建器上下文
func NewBuilderContext() *BuilderContext {
	return &BuilderContext{
		TenantID: 1,
		UserID:   1,
		TimeZone: time.UTC,
		Language: "zh-CN",
		Currency: "CNY",
		Now:      time.Now(),
		Seed:     time.Now().UnixNano(),
	}
}

// WithTenant 设置租户ID
func (c *BuilderContext) WithTenant(tenantID int64) *BuilderContext {
	c.TenantID = tenantID
	return c
}

// WithUser 设置用户ID
func (c *BuilderContext) WithUser(userID int64) *BuilderContext {
	c.UserID = userID
	return c
}

// WithTimeZone 设置时区
func (c *BuilderContext) WithTimeZone(timeZone *time.Location) *BuilderContext {
	c.TimeZone = timeZone
	return c
}

// WithLanguage 设置语言
func (c *BuilderContext) WithLanguage(language string) *BuilderContext {
	c.Language = language
	return c
}

// WithCurrency 设置货币
func (c *BuilderContext) WithCurrency(currency string) *BuilderContext {
	c.Currency = currency
	return c
}

// WithNow 设置当前时间
func (c *BuilderContext) WithNow(now time.Time) *BuilderContext {
	c.Now = now
	return c
}

// WithSeed 设置随机种子
func (c *BuilderContext) WithSeed(seed int64) *BuilderContext {
	c.Seed = seed
	SetSeed(seed)
	return c
}

// Apply 应用上下文
func (c *BuilderContext) Apply() {
	SetSeed(c.Seed)
}

// 全局构建器上下文
var GlobalContext = NewBuilderContext()

// SetGlobalContext 设置全局构建器上下文
func SetGlobalContext(ctx *BuilderContext) {
	GlobalContext = ctx
	ctx.Apply()
}

// GetGlobalContext 获取全局构建器上下文
func GetGlobalContext() *BuilderContext {
	return GlobalContext
}
