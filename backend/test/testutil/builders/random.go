package builders

import (
	"fmt"
	"math/rand"
	"strings"
	"sync/atomic"
	"time"

	"github.com/google/uuid"
)

var (
	// 随机数生成器
	rng = rand.New(rand.NewSource(time.Now().UnixNano()))

	// 全局计数器，确保唯一性
	globalCounter int64

	// 字符集
	letterRunes = []rune("abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ")
	numberRunes = []rune("0123456789")

	// 常用名字
	firstNames = []string{
		"张", "王", "李", "赵", "刘", "陈", "杨", "黄", "周", "吴",
		"徐", "孙", "马", "朱", "胡", "林", "郭", "何", "高", "罗",
	}

	lastNames = []string{
		"伟", "芳", "娜", "敏", "静", "丽", "强", "磊", "军", "洋",
		"勇", "艳", "杰", "娟", "涛", "明", "超", "秀英", "霞", "平",
	}

	// 常用域名
	domains = []string{
		"example.com", "test.com", "demo.com", "sample.com",
		"gmail.com", "163.com", "qq.com", "hotmail.com",
	}

	// 手机号前缀
	phonePrefix = []string{
		"130", "131", "132", "133", "134", "135", "136", "137", "138", "139",
		"150", "151", "152", "153", "155", "156", "157", "158", "159",
		"180", "181", "182", "183", "184", "185", "186", "187", "188", "189",
	}
)

// UniqueString 生成唯一的字符串（带时间戳和计数器）
func UniqueString(prefix string) string {
	counter := atomic.AddInt64(&globalCounter, 1)
	timestamp := time.Now().UnixNano()
	return fmt.Sprintf("%s_%d_%d", prefix, timestamp, counter)
}

// UniqueBusinessID 生成唯一的业务ID
func UniqueBusinessID() string {
	return uuid.New().String()
}

// RandomString 生成随机字符串
func RandomString(length int) string {
	b := make([]rune, length)
	for i := range b {
		b[i] = letterRunes[rng.Intn(len(letterRunes))]
	}
	return string(b)
}

// RandomStringWithPrefix 生成带前缀的随机字符串
func RandomStringWithPrefix(prefix string, length int) string {
	return prefix + RandomString(length)
}

// RandomNumber 生成随机数字字符串
func RandomNumber(length int) string {
	b := make([]rune, length)
	for i := range b {
		b[i] = numberRunes[rng.Intn(len(numberRunes))]
	}
	return string(b)
}

// RandomInt 生成随机整数
func RandomInt(min, max int) int {
	return rng.Intn(max-min+1) + min
}

// RandomInt64 生成随机int64
func RandomInt64() int64 {
	return rng.Int63()
}

// RandomFloat64 生成随机浮点数
func RandomFloat64(min, max float64) float64 {
	return min + rng.Float64()*(max-min)
}

// RandomBool 生成随机布尔值
func RandomBool() bool {
	return rng.Intn(2) == 1
}

// RandomEmail 生成随机邮箱
func RandomEmail() string {
	username := strings.ToLower(RandomString(8))
	domain := domains[rng.Intn(len(domains))]
	return fmt.Sprintf("%s@%s", username, domain)
}

// RandomEmailWithDomain 生成指定域名的随机邮箱
func RandomEmailWithDomain(domain string) string {
	username := strings.ToLower(RandomString(8))
	return fmt.Sprintf("%s@%s", username, domain)
}

// RandomPhone 生成随机手机号
func RandomPhone() string {
	prefix := phonePrefix[rng.Intn(len(phonePrefix))]
	suffix := RandomNumber(8)
	return prefix + suffix
}

// RandomChineseName 生成随机中文姓名
func RandomChineseName() string {
	firstName := firstNames[rng.Intn(len(firstNames))]
	lastName := lastNames[rng.Intn(len(lastNames))]
	return firstName + lastName
}

// RandomUsername 生成随机用户名
func RandomUsername() string {
	prefixes := []string{"user", "test", "demo", "sample"}
	prefix := prefixes[rng.Intn(len(prefixes))]
	suffix := RandomNumber(4)
	return prefix + suffix
}

// RandomPassword 生成随机密码
func RandomPassword(length int) string {
	if length < 8 {
		length = 8
	}

	// 确保包含大小写字母、数字和特殊字符
	chars := "abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789!@#$%^&*"
	password := make([]byte, length)

	for i := range password {
		password[i] = chars[rng.Intn(len(chars))]
	}

	return string(password)
}

// RandomURL 生成随机URL
func RandomURL() string {
	protocols := []string{"http", "https"}
	protocol := protocols[rng.Intn(len(protocols))]
	domain := domains[rng.Intn(len(domains))]
	path := strings.ToLower(RandomString(6))

	return fmt.Sprintf("%s://%s/%s", protocol, domain, path)
}

// RandomDate 生成随机日期
func RandomDate(start, end time.Time) time.Time {
	delta := end.Unix() - start.Unix()
	sec := rng.Int63n(delta) + start.Unix()
	return time.Unix(sec, 0)
}

// RandomDateInPast 生成过去的随机日期
func RandomDateInPast(days int) time.Time {
	now := time.Now()
	past := now.AddDate(0, 0, -days)
	return RandomDate(past, now)
}

// RandomDateInFuture 生成未来的随机日期
func RandomDateInFuture(days int) time.Time {
	now := time.Now()
	future := now.AddDate(0, 0, days)
	return RandomDate(now, future)
}

// RandomChoice 从切片中随机选择一个元素
func RandomChoice[T any](choices []T) T {
	if len(choices) == 0 {
		var zero T
		return zero
	}
	return choices[rng.Intn(len(choices))]
}

// RandomChoices 从切片中随机选择多个元素
func RandomChoices[T any](choices []T, count int) []T {
	if count <= 0 || len(choices) == 0 {
		return []T{}
	}

	if count >= len(choices) {
		// 如果要选择的数量大于等于总数，返回所有元素的副本
		result := make([]T, len(choices))
		copy(result, choices)
		return result
	}

	// 随机选择不重复的元素
	indices := make([]int, len(choices))
	for i := range indices {
		indices[i] = i
	}

	// Fisher-Yates 洗牌算法
	for i := len(indices) - 1; i > 0; i-- {
		j := rng.Intn(i + 1)
		indices[i], indices[j] = indices[j], indices[i]
	}

	result := make([]T, count)
	for i := 0; i < count; i++ {
		result[i] = choices[indices[i]]
	}

	return result
}

// RandomShuffle 随机打乱切片
func RandomShuffle[T any](slice []T) []T {
	result := make([]T, len(slice))
	copy(result, slice)

	for i := len(result) - 1; i > 0; i-- {
		j := rng.Intn(i + 1)
		result[i], result[j] = result[j], result[i]
	}

	return result
}

// RandomUUID 生成随机UUID格式字符串（不是真正的UUID）
func RandomUUID() string {
	return fmt.Sprintf("%s-%s-%s-%s-%s",
		RandomString(8),
		RandomString(4),
		RandomString(4),
		RandomString(4),
		RandomString(12),
	)
}

// RandomBusinessID 生成随机业务ID
func RandomBusinessID(prefix string) string {
	if prefix == "" {
		prefix = "test"
	}
	return fmt.Sprintf("%s-%s", prefix, RandomUUID())
}

// RandomIPAddress 生成随机IP地址
func RandomIPAddress() string {
	return fmt.Sprintf("%d.%d.%d.%d",
		RandomInt(1, 255),
		RandomInt(0, 255),
		RandomInt(0, 255),
		RandomInt(1, 255),
	)
}

// RandomMACAddress 生成随机MAC地址
func RandomMACAddress() string {
	return fmt.Sprintf("%02x:%02x:%02x:%02x:%02x:%02x",
		RandomInt(0, 255),
		RandomInt(0, 255),
		RandomInt(0, 255),
		RandomInt(0, 255),
		RandomInt(0, 255),
		RandomInt(0, 255),
	)
}

// RandomJSON 生成随机JSON字符串
func RandomJSON() string {
	data := map[string]interface{}{
		"id":         RandomInt64(),
		"name":       RandomString(10),
		"active":     RandomBool(),
		"created_at": time.Now().Format(time.RFC3339),
	}

	// 简单的JSON序列化（仅用于测试）
	return fmt.Sprintf(`{"id":%d,"name":"%s","active":%t,"created_at":"%s"}`,
		data["id"], data["name"], data["active"], data["created_at"])
}

// SetSeed 设置随机数种子（用于可重现的测试）
func SetSeed(seed int64) {
	rng = rand.New(rand.NewSource(seed))
}

// ResetSeed 重置随机数种子
func ResetSeed() {
	rng = rand.New(rand.NewSource(time.Now().UnixNano()))
}
