package builders

import (
	"time"

	"backend/internal/domain/user/entity"
	"backend/internal/domain/user/valueobject"
	"backend/internal/shared/types"
)

// UserBuilder 用户构建器
type UserBuilder struct {
	user *entity.User
}

// NewUserBuilder 创建用户构建器
func NewUserBuilder() *UserBuilder {
	user := &entity.User{
		GlobalEntity: types.NewEmptyGlobalEntity(),
		Username:     UniqueString("user"),
		Email:        UniqueString("test") + "@example.com",
		Phone:        RandomPhone(),
		Status:       entity.UserStatusActive,
		Profile: valueobject.UserProfile{
			Nickname:  UniqueString("nick"),
			FirstName: RandomString(5),
			LastName:  RandomString(5),
			Language:  "zh-CN",
			Timezone:  "Asia/Shanghai",
		},
	}

	// 设置默认ID
	user.ID = RandomInt64()
	user.BusinessID = UniqueBusinessID()
	user.CreatedAt = time.Now()
	user.UpdatedAt = time.Now()

	return &UserBuilder{user: user}
}

// WithID 设置技术ID
func (b *UserBuilder) WithID(id int64) *UserBuilder {
	b.user.ID = id
	return b
}

// WithBusinessID 设置业务ID
func (b *UserBuilder) WithBusinessID(businessID string) *UserBuilder {
	b.user.BusinessID = businessID
	return b
}

// WithUsername 设置用户名
func (b *UserBuilder) WithUsername(username string) *UserBuilder {
	b.user.Username = username
	return b
}

// WithEmail 设置邮箱
func (b *UserBuilder) WithEmail(email string) *UserBuilder {
	b.user.Email = email
	return b
}

// WithPhone 设置手机号
func (b *UserBuilder) WithPhone(phone string) *UserBuilder {
	b.user.Phone = phone
	return b
}

// WithStatus 设置状态
func (b *UserBuilder) WithStatus(status entity.UserStatus) *UserBuilder {
	b.user.Status = status
	return b
}

// WithProfile 设置用户档案
func (b *UserBuilder) WithProfile(profile valueobject.UserProfile) *UserBuilder {
	b.user.Profile = profile
	return b
}

// WithNickname 设置昵称
func (b *UserBuilder) WithNickname(nickname string) *UserBuilder {
	b.user.Profile.Nickname = nickname
	return b
}

// WithFirstName 设置名字
func (b *UserBuilder) WithFirstName(firstName string) *UserBuilder {
	b.user.Profile.FirstName = firstName
	return b
}

// WithLastName 设置姓氏
func (b *UserBuilder) WithLastName(lastName string) *UserBuilder {
	b.user.Profile.LastName = lastName
	return b
}

// WithLanguage 设置语言
func (b *UserBuilder) WithLanguage(language string) *UserBuilder {
	b.user.Profile.Language = language
	return b
}

// WithTimezone 设置时区
func (b *UserBuilder) WithTimezone(timezone string) *UserBuilder {
	b.user.Profile.Timezone = timezone
	return b
}

// WithAvatar 设置头像
func (b *UserBuilder) WithAvatar(avatar string) *UserBuilder {
	b.user.Profile.Avatar = avatar
	return b
}

// WithCreatedAt 设置创建时间
func (b *UserBuilder) WithCreatedAt(createdAt time.Time) *UserBuilder {
	b.user.CreatedAt = createdAt
	return b
}

// WithUpdatedAt 设置更新时间
func (b *UserBuilder) WithUpdatedAt(updatedAt time.Time) *UserBuilder {
	b.user.UpdatedAt = updatedAt
	return b
}

// Active 设置为激活状态
func (b *UserBuilder) Active() *UserBuilder {
	b.user.Status = entity.UserStatusActive
	return b
}

// Inactive 设置为未激活状态
func (b *UserBuilder) Inactive() *UserBuilder {
	b.user.Status = entity.UserStatusInactive
	return b
}

// Suspended 设置为暂停状态
func (b *UserBuilder) Suspended() *UserBuilder {
	b.user.Status = entity.UserStatusSuspended
	return b
}

// Banned 设置为封禁状态
func (b *UserBuilder) Banned() *UserBuilder {
	b.user.Status = entity.UserStatusBanned
	return b
}

// Deleted 设置为已删除状态
func (b *UserBuilder) Deleted() *UserBuilder {
	now := time.Now()
	b.user.DeletedAt.Time = now
	b.user.DeletedAt.Valid = true
	return b
}

// Build 构建用户对象
func (b *UserBuilder) Build() *entity.User {
	// 创建副本以避免修改原始对象
	user := &entity.User{
		GlobalEntity: b.user.GlobalEntity,
		Username:     b.user.Username,
		Email:        b.user.Email,
		Phone:        b.user.Phone,
		Status:       b.user.Status,
		Profile:      b.user.Profile,
	}
	return user
}

// BuildPtr 构建用户对象指针
func (b *UserBuilder) BuildPtr() *entity.User {
	return b.Build()
}

// BuildSlice 构建用户切片
func (b *UserBuilder) BuildSlice(count int) []*entity.User {
	users := make([]*entity.User, count)
	for i := 0; i < count; i++ {
		// 为每个用户生成不同的数据
		builder := NewUserBuilder()
		if b.user.Status != entity.UserStatusActive {
			builder.WithStatus(b.user.Status)
		}
		users[i] = builder.Build()
	}
	return users
}

// Clone 克隆构建器
func (b *UserBuilder) Clone() *UserBuilder {
	return &UserBuilder{
		user: b.Build(),
	}
}

// Reset 重置构建器
func (b *UserBuilder) Reset() *UserBuilder {
	return NewUserBuilder()
}

// 预定义的用户构建器

// AdminUser 管理员用户
func AdminUser() *UserBuilder {
	return NewUserBuilder().
		WithUsername("admin").
		WithEmail("<EMAIL>").
		WithNickname("管理员").
		Active()
}

// TestUser 测试用户
func TestUser() *UserBuilder {
	return NewUserBuilder().
		WithUsername("testuser").
		WithEmail("<EMAIL>").
		WithNickname("测试用户").
		Active()
}

// InactiveUser 未激活用户
func InactiveUser() *UserBuilder {
	return NewUserBuilder().
		WithUsername("inactive").
		WithEmail("<EMAIL>").
		WithNickname("未激活用户").
		Inactive()
}

// BannedUser 被封禁用户
func BannedUser() *UserBuilder {
	return NewUserBuilder().
		WithUsername("banned").
		WithEmail("<EMAIL>").
		WithNickname("被封禁用户").
		Banned()
}

// DeletedUser 已删除用户
func DeletedUser() *UserBuilder {
	return NewUserBuilder().
		WithUsername("deleted").
		WithEmail("<EMAIL>").
		WithNickname("已删除用户").
		Deleted()
}

// UserWithProfile 带完整档案的用户
func UserWithProfile() *UserBuilder {
	return NewUserBuilder().
		WithProfile(valueobject.UserProfile{
			Nickname:  "张三",
			FirstName: "三",
			LastName:  "张",
			Avatar:    "https://example.com/avatar.jpg",
			Language:  "zh-CN",
			Timezone:  "Asia/Shanghai",
			Metadata: valueobject.MetadataMap{
				"gender":    "male",
				"birthday":  "1990-01-01",
				"bio":       "这是一个测试用户的简介",
				"website":   "https://example.com",
				"location":  "北京市",
				"company":   "测试公司",
				"job_title": "软件工程师",
			},
		})
}
