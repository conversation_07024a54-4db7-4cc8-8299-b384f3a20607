package http

import (
	"context"
	"fmt"
	"net"
	"net/http"
	"net/http/httptest"
	"sync"
	"time"

	"github.com/gin-gonic/gin"
)

// TestServer HTTP测试服务器
type TestServer struct {
	server   *httptest.Server
	handler  http.Handler
	client   *TestClient
	mu       sync.RWMutex
	started  bool
	baseURL  string
}

// NewTestServer 创建HTTP测试服务器
func NewTestServer(handler http.Handler) *TestServer {
	return &TestServer{
		handler: handler,
	}
}

// NewTestServerWithGin 使用Gin引擎创建HTTP测试服务器
func NewTestServerWithGin(engine *gin.Engine) *TestServer {
	return NewTestServer(engine)
}

// Start 启动测试服务器
func (ts *TestServer) Start() error {
	ts.mu.Lock()
	defer ts.mu.Unlock()

	if ts.started {
		return fmt.Errorf("test server already started")
	}

	ts.server = httptest.NewServer(ts.handler)
	ts.baseURL = ts.server.URL
	ts.client = NewTestClient(ts.handler)
	ts.started = true

	return nil
}

// StartTLS 启动TLS测试服务器
func (ts *TestServer) StartTLS() error {
	ts.mu.Lock()
	defer ts.mu.Unlock()

	if ts.started {
		return fmt.Errorf("test server already started")
	}

	ts.server = httptest.NewTLSServer(ts.handler)
	ts.baseURL = ts.server.URL
	ts.client = NewTestClient(ts.handler)
	ts.started = true

	return nil
}

// StartUnstarted 创建未启动的测试服务器
func (ts *TestServer) StartUnstarted() error {
	ts.mu.Lock()
	defer ts.mu.Unlock()

	if ts.started {
		return fmt.Errorf("test server already started")
	}

	ts.server = httptest.NewUnstartedServer(ts.handler)
	return nil
}

// StartOnPort 在指定端口启动测试服务器
func (ts *TestServer) StartOnPort(port int) error {
	ts.mu.Lock()
	defer ts.mu.Unlock()

	if ts.started {
		return fmt.Errorf("test server already started")
	}

	// 创建监听器
	listener, err := net.Listen("tcp", fmt.Sprintf(":%d", port))
	if err != nil {
		return fmt.Errorf("failed to listen on port %d: %w", port, err)
	}

	// 创建未启动的服务器
	ts.server = httptest.NewUnstartedServer(ts.handler)
	ts.server.Listener = listener

	// 启动服务器
	ts.server.Start()
	ts.baseURL = ts.server.URL
	ts.client = NewTestClient(ts.handler)
	ts.started = true

	return nil
}

// Stop 停止测试服务器
func (ts *TestServer) Stop() {
	ts.mu.Lock()
	defer ts.mu.Unlock()

	if ts.server != nil {
		ts.server.Close()
		ts.server = nil
	}

	if ts.client != nil {
		ts.client.Close()
		ts.client = nil
	}

	ts.started = false
}

// Close 关闭测试服务器（Stop的别名）
func (ts *TestServer) Close() {
	ts.Stop()
}

// GetURL 获取服务器URL
func (ts *TestServer) GetURL() string {
	ts.mu.RLock()
	defer ts.mu.RUnlock()
	return ts.baseURL
}

// GetClient 获取测试客户端
func (ts *TestServer) GetClient() *TestClient {
	ts.mu.RLock()
	defer ts.mu.RUnlock()
	return ts.client
}

// GetListener 获取监听器
func (ts *TestServer) GetListener() net.Listener {
	ts.mu.RLock()
	defer ts.mu.RUnlock()
	if ts.server != nil {
		return ts.server.Listener
	}
	return nil
}

// IsStarted 检查服务器是否已启动
func (ts *TestServer) IsStarted() bool {
	ts.mu.RLock()
	defer ts.mu.RUnlock()
	return ts.started
}

// WaitForReady 等待服务器就绪
func (ts *TestServer) WaitForReady(timeout time.Duration) error {
	if !ts.IsStarted() {
		return fmt.Errorf("server not started")
	}

	ctx, cancel := context.WithTimeout(context.Background(), timeout)
	defer cancel()

	ticker := time.NewTicker(100 * time.Millisecond)
	defer ticker.Stop()

	for {
		select {
		case <-ctx.Done():
			return fmt.Errorf("timeout waiting for server to be ready")
		case <-ticker.C:
			if ts.isReady() {
				return nil
			}
		}
	}
}

// isReady 检查服务器是否就绪
func (ts *TestServer) isReady() bool {
	client := &http.Client{Timeout: 1 * time.Second}
	resp, err := client.Get(ts.GetURL() + "/health")
	if err != nil {
		return false
	}
	defer resp.Body.Close()
	return resp.StatusCode == http.StatusOK
}

// Restart 重启测试服务器
func (ts *TestServer) Restart() error {
	ts.Stop()
	return ts.Start()
}

// UpdateHandler 更新处理器
func (ts *TestServer) UpdateHandler(handler http.Handler) error {
	ts.mu.Lock()
	defer ts.mu.Unlock()

	ts.handler = handler

	if ts.started {
		// 如果服务器已启动，需要重启
		ts.server.Close()
		ts.server = httptest.NewServer(handler)
		ts.baseURL = ts.server.URL
		ts.client = NewTestClient(handler)
	}

	return nil
}

// GetPort 获取服务器端口
func (ts *TestServer) GetPort() int {
	listener := ts.GetListener()
	if listener == nil {
		return 0
	}

	addr := listener.Addr().(*net.TCPAddr)
	return addr.Port
}

// GetHost 获取服务器主机
func (ts *TestServer) GetHost() string {
	listener := ts.GetListener()
	if listener == nil {
		return ""
	}

	addr := listener.Addr().(*net.TCPAddr)
	return addr.IP.String()
}

// GetAddress 获取服务器地址
func (ts *TestServer) GetAddress() string {
	listener := ts.GetListener()
	if listener == nil {
		return ""
	}

	return listener.Addr().String()
}

// TestServerManager 测试服务器管理器
type TestServerManager struct {
	servers map[string]*TestServer
	mu      sync.RWMutex
}

// NewTestServerManager 创建测试服务器管理器
func NewTestServerManager() *TestServerManager {
	return &TestServerManager{
		servers: make(map[string]*TestServer),
	}
}

// AddServer 添加测试服务器
func (tsm *TestServerManager) AddServer(name string, server *TestServer) {
	tsm.mu.Lock()
	defer tsm.mu.Unlock()
	tsm.servers[name] = server
}

// GetServer 获取测试服务器
func (tsm *TestServerManager) GetServer(name string) (*TestServer, bool) {
	tsm.mu.RLock()
	defer tsm.mu.RUnlock()
	server, exists := tsm.servers[name]
	return server, exists
}

// RemoveServer 移除测试服务器
func (tsm *TestServerManager) RemoveServer(name string) {
	tsm.mu.Lock()
	defer tsm.mu.Unlock()
	if server, exists := tsm.servers[name]; exists {
		server.Stop()
		delete(tsm.servers, name)
	}
}

// StartAll 启动所有服务器
func (tsm *TestServerManager) StartAll() error {
	tsm.mu.RLock()
	defer tsm.mu.RUnlock()

	for name, server := range tsm.servers {
		if !server.IsStarted() {
			if err := server.Start(); err != nil {
				return fmt.Errorf("failed to start server %s: %w", name, err)
			}
		}
	}

	return nil
}

// StopAll 停止所有服务器
func (tsm *TestServerManager) StopAll() {
	tsm.mu.RLock()
	defer tsm.mu.RUnlock()

	for _, server := range tsm.servers {
		server.Stop()
	}
}

// CleanupAll 清理所有服务器
func (tsm *TestServerManager) CleanupAll() {
	tsm.mu.Lock()
	defer tsm.mu.Unlock()

	for _, server := range tsm.servers {
		server.Stop()
	}

	tsm.servers = make(map[string]*TestServer)
}

// ListServers 列出所有服务器
func (tsm *TestServerManager) ListServers() []string {
	tsm.mu.RLock()
	defer tsm.mu.RUnlock()

	names := make([]string, 0, len(tsm.servers))
	for name := range tsm.servers {
		names = append(names, name)
	}

	return names
}

// GetServerCount 获取服务器数量
func (tsm *TestServerManager) GetServerCount() int {
	tsm.mu.RLock()
	defer tsm.mu.RUnlock()
	return len(tsm.servers)
}

// 全局服务器管理器
var globalServerManager = NewTestServerManager()

// GetGlobalServerManager 获取全局服务器管理器
func GetGlobalServerManager() *TestServerManager {
	return globalServerManager
}

// 便捷函数

// StartTestServer 启动测试服务器
func StartTestServer(handler http.Handler) (*TestServer, error) {
	server := NewTestServer(handler)
	err := server.Start()
	return server, err
}

// StartTestServerWithGin 使用Gin启动测试服务器
func StartTestServerWithGin(engine *gin.Engine) (*TestServer, error) {
	server := NewTestServerWithGin(engine)
	err := server.Start()
	return server, err
}
