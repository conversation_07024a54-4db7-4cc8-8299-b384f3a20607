package http

import (
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"strings"
)

// TestResponse HTTP测试响应
type TestResponse struct {
	*http.Response
	body []byte
}

// NewTestResponse 创建测试响应
func NewTestResponse(resp *http.Response) *TestResponse {
	// 读取响应体
	body, _ := io.ReadAll(resp.Body)
	resp.Body.Close()

	return &TestResponse{
		Response: resp,
		body:     body,
	}
}

// Body 获取响应体字节
func (tr *TestResponse) Body() []byte {
	return tr.body
}

// BodyString 获取响应体字符串
func (tr *TestResponse) BodyString() string {
	return string(tr.body)
}

// JSON 解析JSON响应体
func (tr *TestResponse) JSON(target interface{}) error {
	if len(tr.body) == 0 {
		return fmt.Errorf("response body is empty")
	}

	if err := json.Unmarshal(tr.body, target); err != nil {
		return fmt.Errorf("failed to unmarshal JSON response: %w", err)
	}

	return nil
}

// JSONMap 解析JSON响应体为map
func (tr *TestResponse) JSONMap() (map[string]interface{}, error) {
	var result map[string]interface{}
	err := tr.JSON(&result)
	return result, err
}

// JSONArray 解析JSON响应体为数组
func (tr *TestResponse) JSONArray() ([]interface{}, error) {
	var result []interface{}
	err := tr.JSON(&result)
	return result, err
}

// JSONPath 获取JSON路径的值
func (tr *TestResponse) JSONPath(path string) (interface{}, error) {
	var data interface{}
	if err := tr.JSON(&data); err != nil {
		return nil, err
	}

	return getJSONPathValue(data, path), nil
}

// JSONField 获取JSON字段的值
func (tr *TestResponse) JSONField(field string) (interface{}, error) {
	jsonMap, err := tr.JSONMap()
	if err != nil {
		return nil, err
	}

	value, exists := jsonMap[field]
	if !exists {
		return nil, fmt.Errorf("field %s not found in JSON response", field)
	}

	return value, nil
}

// IsJSON 检查响应是否为JSON格式
func (tr *TestResponse) IsJSON() bool {
	contentType := tr.Header.Get("Content-Type")
	return strings.Contains(contentType, "application/json")
}

// IsHTML 检查响应是否为HTML格式
func (tr *TestResponse) IsHTML() bool {
	contentType := tr.Header.Get("Content-Type")
	return strings.Contains(contentType, "text/html")
}

// IsXML 检查响应是否为XML格式
func (tr *TestResponse) IsXML() bool {
	contentType := tr.Header.Get("Content-Type")
	return strings.Contains(contentType, "application/xml") || strings.Contains(contentType, "text/xml")
}

// IsText 检查响应是否为纯文本格式
func (tr *TestResponse) IsText() bool {
	contentType := tr.Header.Get("Content-Type")
	return strings.Contains(contentType, "text/plain")
}

// GetHeader 获取响应头
func (tr *TestResponse) GetHeader(key string) string {
	return tr.Header.Get(key)
}

// GetHeaders 获取所有响应头
func (tr *TestResponse) GetHeaders() http.Header {
	return tr.Header
}

// GetCookie 获取Cookie
func (tr *TestResponse) GetCookie(name string) (*http.Cookie, error) {
	for _, cookie := range tr.Cookies() {
		if cookie.Name == name {
			return cookie, nil
		}
	}
	return nil, fmt.Errorf("cookie %s not found", name)
}

// GetCookies 获取所有Cookie
func (tr *TestResponse) GetCookies() []*http.Cookie {
	return tr.Cookies()
}

// HasHeader 检查响应头是否存在
func (tr *TestResponse) HasHeader(key string) bool {
	_, exists := tr.Header[key]
	return exists
}

// HasCookie 检查Cookie是否存在
func (tr *TestResponse) HasCookie(name string) bool {
	_, err := tr.GetCookie(name)
	return err == nil
}

// ContainsString 检查响应体是否包含指定字符串
func (tr *TestResponse) ContainsString(substring string) bool {
	return strings.Contains(tr.BodyString(), substring)
}

// ContainsBytes 检查响应体是否包含指定字节
func (tr *TestResponse) ContainsBytes(data []byte) bool {
	return strings.Contains(string(tr.body), string(data))
}

// IsEmpty 检查响应体是否为空
func (tr *TestResponse) IsEmpty() bool {
	return len(tr.body) == 0
}

// Size 获取响应体大小
func (tr *TestResponse) Size() int {
	return len(tr.body)
}

// IsSuccess 检查是否为成功响应（2xx）
func (tr *TestResponse) IsSuccess() bool {
	return tr.StatusCode >= 200 && tr.StatusCode < 300
}

// IsRedirect 检查是否为重定向响应（3xx）
func (tr *TestResponse) IsRedirect() bool {
	return tr.StatusCode >= 300 && tr.StatusCode < 400
}

// IsClientError 检查是否为客户端错误（4xx）
func (tr *TestResponse) IsClientError() bool {
	return tr.StatusCode >= 400 && tr.StatusCode < 500
}

// IsServerError 检查是否为服务器错误（5xx）
func (tr *TestResponse) IsServerError() bool {
	return tr.StatusCode >= 500 && tr.StatusCode < 600
}

// IsError 检查是否为错误响应（4xx或5xx）
func (tr *TestResponse) IsError() bool {
	return tr.IsClientError() || tr.IsServerError()
}

// GetLocation 获取重定向位置
func (tr *TestResponse) GetLocation() string {
	return tr.Header.Get("Location")
}

// GetContentType 获取内容类型
func (tr *TestResponse) GetContentType() string {
	return tr.Header.Get("Content-Type")
}

// GetContentLength 获取内容长度
func (tr *TestResponse) GetContentLength() int64 {
	return tr.ContentLength
}

// GetETag 获取ETag
func (tr *TestResponse) GetETag() string {
	return tr.Header.Get("ETag")
}

// GetLastModified 获取最后修改时间
func (tr *TestResponse) GetLastModified() string {
	return tr.Header.Get("Last-Modified")
}

// GetCacheControl 获取缓存控制
func (tr *TestResponse) GetCacheControl() string {
	return tr.Header.Get("Cache-Control")
}

// Print 打印响应信息（用于调试）
func (tr *TestResponse) Print() {
	fmt.Printf("Status: %d %s\n", tr.StatusCode, tr.Status)
	fmt.Printf("Headers:\n")
	for key, values := range tr.Header {
		for _, value := range values {
			fmt.Printf("  %s: %s\n", key, value)
		}
	}
	fmt.Printf("Body:\n%s\n", tr.BodyString())
}

// PrintHeaders 打印响应头（用于调试）
func (tr *TestResponse) PrintHeaders() {
	fmt.Printf("Response Headers:\n")
	for key, values := range tr.Header {
		for _, value := range values {
			fmt.Printf("  %s: %s\n", key, value)
		}
	}
}

// PrintBody 打印响应体（用于调试）
func (tr *TestResponse) PrintBody() {
	fmt.Printf("Response Body:\n%s\n", tr.BodyString())
}

// SaveToFile 保存响应体到文件
func (tr *TestResponse) SaveToFile(filename string) error {
	// 这里应该使用 os.WriteFile 保存文件
	// 为了简化，这里只是一个占位符
	return nil
}

// Clone 克隆响应
func (tr *TestResponse) Clone() *TestResponse {
	// 创建新的响应副本
	newResp := &http.Response{
		Status:           tr.Status,
		StatusCode:       tr.StatusCode,
		Proto:            tr.Proto,
		ProtoMajor:       tr.ProtoMajor,
		ProtoMinor:       tr.ProtoMinor,
		Header:           make(http.Header),
		ContentLength:    tr.ContentLength,
		TransferEncoding: tr.TransferEncoding,
		Close:            tr.Close,
		Uncompressed:     tr.Uncompressed,
		Trailer:          tr.Trailer,
		Request:          tr.Request,
		TLS:              tr.TLS,
	}

	// 复制响应头
	for key, values := range tr.Header {
		newResp.Header[key] = make([]string, len(values))
		copy(newResp.Header[key], values)
	}

	// 复制响应体
	newBody := make([]byte, len(tr.body))
	copy(newBody, tr.body)

	return &TestResponse{
		Response: newResp,
		body:     newBody,
	}
}

// 辅助函数

// getJSONPathValue 获取JSON路径的值
func getJSONPathValue(data interface{}, path string) interface{} {
	// 简单的JSON路径解析
	parts := strings.Split(path, ".")
	current := data

	for _, part := range parts {
		switch v := current.(type) {
		case map[string]interface{}:
			current = v[part]
		case []interface{}:
			// 处理数组索引（简化实现）
			if part == "0" && len(v) > 0 {
				current = v[0]
			} else {
				return nil
			}
		default:
			return nil
		}
	}

	return current
}
