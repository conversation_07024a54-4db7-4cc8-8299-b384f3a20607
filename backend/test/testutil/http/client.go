package http

import (
	"bytes"
	"context"
	"encoding/json"
	"fmt"
	"io"
	"mime/multipart"
	"net/http"
	"net/http/httptest"
	"net/url"
	"time"

	"github.com/gin-gonic/gin"
)

// TestClient HTTP测试客户端
type TestClient struct {
	server  *httptest.Server
	client  *http.Client
	baseURL string
	headers map[string]string
	cookies []*http.Cookie
}

// NewTestClient 创建HTTP测试客户端
func NewTestClient(handler http.Handler) *TestClient {
	server := httptest.NewServer(handler)

	return &TestClient{
		server: server,
		client: &http.Client{
			Timeout: 30 * time.Second,
		},
		baseURL: server.URL,
		headers: make(map[string]string),
	}
}

// NewTestClientWithGin 使用Gin引擎创建HTTP测试客户端
func NewTestClientWithGin(engine *gin.Engine) *TestClient {
	return NewTestClient(engine)
}

// Close 关闭测试客户端
func (tc *TestClient) Close() {
	if tc.server != nil {
		tc.server.Close()
	}
}

// SetHeader 设置请求头
func (tc *TestClient) SetHeader(key, value string) *TestClient {
	tc.headers[key] = value
	return tc
}

// SetHeaders 设置多个请求头
func (tc *TestClient) SetHeaders(headers map[string]string) *TestClient {
	for key, value := range headers {
		tc.headers[key] = value
	}
	return tc
}

// SetAuthToken 设置认证令牌
func (tc *TestClient) SetAuthToken(token string) *TestClient {
	return tc.SetHeader("Authorization", "Bearer "+token)
}

// SetBasicAuth 设置基础认证
func (tc *TestClient) SetBasicAuth(username, password string) *TestClient {
	return tc.SetHeader("Authorization", "Basic "+basicAuth(username, password))
}

// SetContentType 设置内容类型
func (tc *TestClient) SetContentType(contentType string) *TestClient {
	return tc.SetHeader("Content-Type", contentType)
}

// SetCookie 设置Cookie
func (tc *TestClient) SetCookie(cookie *http.Cookie) *TestClient {
	tc.cookies = append(tc.cookies, cookie)
	return tc
}

// SetCookies 设置多个Cookie
func (tc *TestClient) SetCookies(cookies []*http.Cookie) *TestClient {
	tc.cookies = append(tc.cookies, cookies...)
	return tc
}

// GET 发送GET请求
func (tc *TestClient) GET(path string, params ...map[string]string) (*TestResponse, error) {
	return tc.Request("GET", path, nil, params...)
}

// POST 发送POST请求
func (tc *TestClient) POST(path string, body interface{}, params ...map[string]string) (*TestResponse, error) {
	return tc.Request("POST", path, body, params...)
}

// PUT 发送PUT请求
func (tc *TestClient) PUT(path string, body interface{}, params ...map[string]string) (*TestResponse, error) {
	return tc.Request("PUT", path, body, params...)
}

// PATCH 发送PATCH请求
func (tc *TestClient) PATCH(path string, body interface{}, params ...map[string]string) (*TestResponse, error) {
	return tc.Request("PATCH", path, body, params...)
}

// DELETE 发送DELETE请求
func (tc *TestClient) DELETE(path string, params ...map[string]string) (*TestResponse, error) {
	return tc.Request("DELETE", path, nil, params...)
}

// Request 发送HTTP请求
func (tc *TestClient) Request(method, path string, body interface{}, params ...map[string]string) (*TestResponse, error) {
	// 构建URL
	fullURL := tc.baseURL + path
	if len(params) > 0 && params[0] != nil {
		fullURL += "?" + tc.buildQueryString(params[0])
	}

	// 准备请求体
	var bodyReader io.Reader
	if body != nil {
		bodyBytes, err := tc.marshalBody(body)
		if err != nil {
			return nil, fmt.Errorf("failed to marshal request body: %w", err)
		}
		bodyReader = bytes.NewReader(bodyBytes)
	}

	// 创建请求
	req, err := http.NewRequest(method, fullURL, bodyReader)
	if err != nil {
		return nil, fmt.Errorf("failed to create request: %w", err)
	}

	// 设置请求头
	for key, value := range tc.headers {
		req.Header.Set(key, value)
	}

	// 设置Cookie
	for _, cookie := range tc.cookies {
		req.AddCookie(cookie)
	}

	// 发送请求
	resp, err := tc.client.Do(req)
	if err != nil {
		return nil, fmt.Errorf("failed to send request: %w", err)
	}

	return NewTestResponse(resp), nil
}

// RequestWithContext 使用上下文发送HTTP请求
func (tc *TestClient) RequestWithContext(ctx context.Context, method, path string, body interface{}, params ...map[string]string) (*TestResponse, error) {
	// 构建URL
	fullURL := tc.baseURL + path
	if len(params) > 0 && params[0] != nil {
		fullURL += "?" + tc.buildQueryString(params[0])
	}

	// 准备请求体
	var bodyReader io.Reader
	if body != nil {
		bodyBytes, err := tc.marshalBody(body)
		if err != nil {
			return nil, fmt.Errorf("failed to marshal request body: %w", err)
		}
		bodyReader = bytes.NewReader(bodyBytes)
	}

	// 创建带上下文的请求
	req, err := http.NewRequestWithContext(ctx, method, fullURL, bodyReader)
	if err != nil {
		return nil, fmt.Errorf("failed to create request: %w", err)
	}

	// 设置请求头
	for key, value := range tc.headers {
		req.Header.Set(key, value)
	}

	// 设置Cookie
	for _, cookie := range tc.cookies {
		req.AddCookie(cookie)
	}

	// 发送请求
	resp, err := tc.client.Do(req)
	if err != nil {
		return nil, fmt.Errorf("failed to send request: %w", err)
	}

	return NewTestResponse(resp), nil
}

// Upload 上传文件
func (tc *TestClient) Upload(path, fieldName, fileName string, fileContent []byte, params ...map[string]string) (*TestResponse, error) {
	// 创建multipart表单
	body := &bytes.Buffer{}
	writer := createMultipartWriter(body)

	// 添加文件字段
	part, err := writer.CreateFormFile(fieldName, fileName)
	if err != nil {
		return nil, fmt.Errorf("failed to create form file: %w", err)
	}

	if _, err := part.Write(fileContent); err != nil {
		return nil, fmt.Errorf("failed to write file content: %w", err)
	}

	// 添加其他字段
	if len(params) > 0 && params[0] != nil {
		for key, value := range params[0] {
			if err := writer.WriteField(key, value); err != nil {
				return nil, fmt.Errorf("failed to write field %s: %w", key, err)
			}
		}
	}

	// 关闭writer
	if err := writer.Close(); err != nil {
		return nil, fmt.Errorf("failed to close multipart writer: %w", err)
	}

	// 创建请求
	req, err := http.NewRequest("POST", tc.baseURL+path, body)
	if err != nil {
		return nil, fmt.Errorf("failed to create upload request: %w", err)
	}

	// 设置Content-Type
	req.Header.Set("Content-Type", writer.FormDataContentType())

	// 设置其他请求头
	for key, value := range tc.headers {
		if key != "Content-Type" { // 不覆盖multipart的Content-Type
			req.Header.Set(key, value)
		}
	}

	// 设置Cookie
	for _, cookie := range tc.cookies {
		req.AddCookie(cookie)
	}

	// 发送请求
	resp, err := tc.client.Do(req)
	if err != nil {
		return nil, fmt.Errorf("failed to send upload request: %w", err)
	}

	return NewTestResponse(resp), nil
}

// GetBaseURL 获取基础URL
func (tc *TestClient) GetBaseURL() string {
	return tc.baseURL
}

// GetClient 获取HTTP客户端
func (tc *TestClient) GetClient() *http.Client {
	return tc.client
}

// Clone 克隆测试客户端
func (tc *TestClient) Clone() *TestClient {
	clone := &TestClient{
		server:  tc.server,
		client:  tc.client,
		baseURL: tc.baseURL,
		headers: make(map[string]string),
		cookies: make([]*http.Cookie, len(tc.cookies)),
	}

	// 复制请求头
	for key, value := range tc.headers {
		clone.headers[key] = value
	}

	// 复制Cookie
	copy(clone.cookies, tc.cookies)

	return clone
}

// 辅助方法

// marshalBody 序列化请求体
func (tc *TestClient) marshalBody(body interface{}) ([]byte, error) {
	switch v := body.(type) {
	case []byte:
		return v, nil
	case string:
		return []byte(v), nil
	case io.Reader:
		return io.ReadAll(v)
	default:
		// 默认使用JSON序列化
		tc.SetContentType("application/json")
		return json.Marshal(body)
	}
}

// buildQueryString 构建查询字符串
func (tc *TestClient) buildQueryString(params map[string]string) string {
	values := url.Values{}
	for key, value := range params {
		values.Add(key, value)
	}
	return values.Encode()
}

// 辅助函数

// basicAuth 生成基础认证字符串
func basicAuth(username, password string) string {
	auth := username + ":" + password
	return base64Encode([]byte(auth))
}

// base64Encode Base64编码
func base64Encode(data []byte) string {
	// 简单的Base64编码实现
	// 实际应该使用 encoding/base64 包
	return string(data) // 这里只是占位符
}

// createMultipartWriter 创建multipart writer
func createMultipartWriter(body *bytes.Buffer) *multipart.Writer {
	return multipart.NewWriter(body)
}
