package testutil

import (
	"fmt"
	"log"
	"os"
	"path/filepath"
	"runtime"
	"strings"

	"backend/pkg/infrastructure/config"
	"backend/pkg/infrastructure/database/postgres"

	gormpg "gorm.io/driver/postgres"
	"gorm.io/gorm"
)

// TruncateTables 清空所有表的数据
func TruncateTables(db *gorm.DB, tableNames ...string) error {
	if len(tableNames) == 0 {
		tables, err := db.Migrator().GetTables()
		if err != nil {
			return fmt.Errorf("无法获取表列表: %w", err)
		}
		tableNames = tables
	}

	// 关闭外键约束
	if err := db.Exec("SET session_replication_role = 'replica';").Error; err != nil {
		return fmt.Errorf("无法禁用外键约束: %w", err)
	}

	for _, tableName := range tableNames {
		// 我们跳过迁移历史表
		if tableName == "schema_migrations" {
			continue
		}
		if err := db.Exec(fmt.Sprintf("TRUNCATE TABLE %s RESTART IDENTITY CASCADE;", tableName)).Error; err != nil {
			log.Printf("无法清空表 %s: %v", tableName, err)
			// 尝试恢复外键约束，即使清空失败
			if err := db.Exec("SET session_replication_role = 'origin';").Error; err != nil {
				return fmt.Errorf("无法在清空表 %s 失败后恢复外键约束: %w", tableName, err)
			}
			return fmt.Errorf("无法清空表 %s: %w", tableName, err)
		}
	}

	// 重新开启外键约束
	if err := db.Exec("SET session_replication_role = 'origin';").Error; err != nil {
		return fmt.Errorf("无法恢复外键约束: %w", err)
	}

	return nil
}

// GetTestConfig 加载用于测试的配置
func GetTestConfig() (*config.Config, string, error) {
	// 将测试数据库名称设置为环境变量
	os.Setenv("DATABASE_DATABASE", "erp_test")
	os.Setenv("LOGGER_LEVEL", "error") // 在测试期间减少日志噪音

	// 找到项目根目录
	_, b, _, _ := runtime.Caller(0)
	basepath := filepath.Dir(b)
	root := filepath.Join(basepath, "..", "..") // 从 test/testutil 返回到项目根目录

	// 加载配置
	loader := config.NewLoader(root)
	cfg, err := loader.Load()
	if err != nil {
		return nil, "", fmt.Errorf("加载测试配置失败: %w", err)
	}

	return cfg, root, nil
}

// SetupTestDB 创建并连接到测试数据库
func SetupTestDB() (*gorm.DB, *config.Config, error) {
	cfg, _, err := GetTestConfig()
	if err != nil {
		return nil, nil, err
	}

	// 确保我们连接的是测试数据库
	if !strings.HasSuffix(cfg.Database.Database, "_test") {
		return nil, nil, fmt.Errorf("不安全的数据库名称: '%s'。测试数据库必须以 '_test' 结尾", cfg.Database.Database)
	}

	// 创建一个不带数据库名的 DSN 用于创建数据库
	dsnWithoutDB := fmt.Sprintf("host=%s user=%s password=%s port=%d sslmode=%s",
		cfg.Database.Host,
		cfg.Database.Username,
		cfg.Database.Password,
		cfg.Database.Port,
		cfg.Database.SSLMode,
	)

	// 连接到 postgres 数据库以创建测试数据库
	tempDB, err := gorm.Open(gormpg.Open(dsnWithoutDB), &gorm.Config{})
	if err != nil {
		return nil, nil, fmt.Errorf("无法连接到 'postgres' 数据库: %w", err)
	}
	sqlDB, _ := tempDB.DB()
	defer sqlDB.Close()

	// 检查并创建测试数据库
	var exists bool
	err = tempDB.Raw("SELECT EXISTS (SELECT 1 FROM pg_database WHERE datname = ?)", cfg.Database.Database).Scan(&exists).Error
	if err != nil {
		return nil, nil, fmt.Errorf("检查数据库是否存在时出错: %w", err)
	}

	if !exists {
		log.Printf("数据库 '%s' 不存在，正在创建...", cfg.Database.Database)
		err = tempDB.Exec("CREATE DATABASE " + cfg.Database.Database).Error
		if err != nil {
			return nil, nil, fmt.Errorf("创建数据库 '%s' 失败: %w", cfg.Database.Database, err)
		}
	}

	// 连接到新创建的测试数据库
	manager := postgres.NewDatabaseManager(&cfg.Database)

	// 初始化管理器
	if err := manager.Initialize(); err != nil {
		return nil, nil, fmt.Errorf("初始化数据库管理器失败: %w", err)
	}

	// 获取连接和迁移器
	conn := manager.GetConnection()
	migrator := manager.GetMigrator()

	// 运行数据库迁移
	if err := migrator.ApplyMigrations(); err != nil {
		return nil, nil, fmt.Errorf("执行数据库迁移失败: %w", err)
	}

	return conn.GetDB(), cfg, nil
}
