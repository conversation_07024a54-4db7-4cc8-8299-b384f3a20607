package config

import (
	"fmt"
	"os"
	"path/filepath"
	"strconv"
	"sync"

	"gopkg.in/yaml.v3"
)

// TestConfig 测试配置
type TestConfig struct {
	Database DatabaseConfig `yaml:"database"`
	Cache    CacheConfig    `yaml:"cache"`
	HTTP     HTTPConfig     `yaml:"http"`
	Auth     AuthConfig     `yaml:"auth"`
	External ExternalConfig `yaml:"external"`
	Logging  LoggingConfig  `yaml:"logging"`
}

// DatabaseConfig 数据库测试配置
type DatabaseConfig struct {
	Driver           string `yaml:"driver"`
	Host             string `yaml:"host"`
	Port             int    `yaml:"port"`
	Database         string `yaml:"database"`
	Username         string `yaml:"username"`
	Password         string `yaml:"password"`
	SSLMode          string `yaml:"ssl_mode"`
	MaxOpenConns     int    `yaml:"max_open_conns"`
	MaxIdleConns     int    `yaml:"max_idle_conns"`
	UseTestContainer bool   `yaml:"use_test_container"`
	MigrationPath    string `yaml:"migration_path"`
	FixturesPath     string `yaml:"fixtures_path"`
}

// CacheConfig 缓存测试配置
type CacheConfig struct {
	Redis RedisConfig `yaml:"redis"`
}

// RedisConfig Redis测试配置
type RedisConfig struct {
	Host             string `yaml:"host"`
	Port             int    `yaml:"port"`
	Password         string `yaml:"password"`
	Database         int    `yaml:"database"`
	UseTestContainer bool   `yaml:"use_test_container"`
}

// HTTPConfig HTTP测试配置
type HTTPConfig struct {
	Port    int    `yaml:"port"`
	Host    string `yaml:"host"`
	Timeout int    `yaml:"timeout"`
}

// AuthConfig 认证测试配置
type AuthConfig struct {
	JWT JWTConfig `yaml:"jwt"`
}

// JWTConfig JWT测试配置
type JWTConfig struct {
	SecretKey            string `yaml:"secret_key"`
	AccessTokenDuration  int    `yaml:"access_token_duration"`
	RefreshTokenDuration int    `yaml:"refresh_token_duration"`
	Issuer               string `yaml:"issuer"`
}

// ExternalConfig 外部服务测试配置
type ExternalConfig struct {
	PaymentService PaymentServiceConfig `yaml:"payment_service"`
}

// PaymentServiceConfig 支付服务测试配置
type PaymentServiceConfig struct {
	BaseURL string `yaml:"base_url"`
	APIKey  string `yaml:"api_key"`
	Timeout int    `yaml:"timeout"`
}

// LoggingConfig 日志测试配置
type LoggingConfig struct {
	Level  string `yaml:"level"`
	Format string `yaml:"format"`
	Output string `yaml:"output"`
}

var (
	testConfig *TestConfig
	configOnce sync.Once
)

// GetTestConfig 获取测试配置
func GetTestConfig() *TestConfig {
	configOnce.Do(func() {
		testConfig = loadTestConfig()
	})
	return testConfig
}

// loadTestConfig 加载测试配置
func loadTestConfig() *TestConfig {
	// 默认配置
	cfg := &TestConfig{
		Database: DatabaseConfig{
			Driver:           "postgres",
			Host:             "localhost",
			Port:             5432,
			Database:         "erp_test",
			Username:         "postgres",
			Password:         "postgres",
			SSLMode:          "disable",
			MaxOpenConns:     10,
			MaxIdleConns:     5,
			UseTestContainer: true,
			MigrationPath:    "../../migrations/postgres",
			FixturesPath:     "../fixtures/data",
		},
		Cache: CacheConfig{
			Redis: RedisConfig{
				Host:             "localhost",
				Port:             6379,
				Password:         "",
				Database:         1,
				UseTestContainer: true,
			},
		},
		HTTP: HTTPConfig{
			Port:    8081,
			Host:    "localhost",
			Timeout: 30,
		},
		Auth: AuthConfig{
			JWT: JWTConfig{
				SecretKey:            "test-jwt-secret-key",
				AccessTokenDuration:  3600,
				RefreshTokenDuration: 86400,
				Issuer:               "9-wings-erp",
			},
		},
		External: ExternalConfig{
			PaymentService: PaymentServiceConfig{
				BaseURL: "http://localhost:8082",
				APIKey:  "test-api-key",
				Timeout: 30,
			},
		},
		Logging: LoggingConfig{
			Level:  "debug",
			Format: "json",
			Output: "stdout",
		},
	}

	// 从环境变量覆盖配置
	overrideFromEnv(cfg)

	// 从配置文件覆盖配置
	if configFile := getConfigFile(); configFile != "" {
		if err := loadFromFile(cfg, configFile); err != nil {
			fmt.Printf("Warning: failed to load config file %s: %v\n", configFile, err)
		}
	}

	return cfg
}

// overrideFromEnv 从环境变量覆盖配置
func overrideFromEnv(cfg *TestConfig) {
	if host := os.Getenv("TEST_DB_HOST"); host != "" {
		cfg.Database.Host = host
	}
	if port := os.Getenv("TEST_DB_PORT"); port != "" {
		if p, err := strconv.Atoi(port); err == nil {
			cfg.Database.Port = p
		}
	}
	if database := os.Getenv("TEST_DB_NAME"); database != "" {
		cfg.Database.Database = database
	}
	if username := os.Getenv("TEST_DB_USER"); username != "" {
		cfg.Database.Username = username
	}
	if password := os.Getenv("TEST_DB_PASSWORD"); password != "" {
		cfg.Database.Password = password
	}

	if host := os.Getenv("TEST_REDIS_HOST"); host != "" {
		cfg.Cache.Redis.Host = host
	}
	if port := os.Getenv("TEST_REDIS_PORT"); port != "" {
		if p, err := strconv.Atoi(port); err == nil {
			cfg.Cache.Redis.Port = p
		}
	}

	if secret := os.Getenv("TEST_JWT_SECRET"); secret != "" {
		cfg.Auth.JWT.SecretKey = secret
	}
}

// getConfigFile 获取配置文件路径
func getConfigFile() string {
	// 优先级：环境变量 > 当前目录 > 默认路径
	if configFile := os.Getenv("TEST_CONFIG_FILE"); configFile != "" {
		return configFile
	}

	candidates := []string{
		"test.yaml",
		"test.yml",
		"../config/test.yaml",
		"../config/test.yml",
	}

	for _, candidate := range candidates {
		if _, err := os.Stat(candidate); err == nil {
			return candidate
		}
	}

	return ""
}

// loadFromFile 从文件加载配置
func loadFromFile(cfg *TestConfig, filename string) error {
	data, err := os.ReadFile(filename)
	if err != nil {
		return fmt.Errorf("failed to read config file: %w", err)
	}

	if err := yaml.Unmarshal(data, cfg); err != nil {
		return fmt.Errorf("failed to parse config file: %w", err)
	}

	return nil
}

// GetDSN 获取数据库连接字符串
func (cfg *TestConfig) GetDSN() string {
	return fmt.Sprintf("host=%s port=%d user=%s password=%s dbname=%s sslmode=%s",
		cfg.Database.Host,
		cfg.Database.Port,
		cfg.Database.Username,
		cfg.Database.Password,
		cfg.Database.Database,
		cfg.Database.SSLMode,
	)
}

// GetRedisAddr 获取Redis地址
func (cfg *TestConfig) GetRedisAddr() string {
	return fmt.Sprintf("%s:%d", cfg.Cache.Redis.Host, cfg.Cache.Redis.Port)
}

// GetHTTPAddr 获取HTTP地址
func (cfg *TestConfig) GetHTTPAddr() string {
	return fmt.Sprintf("%s:%d", cfg.HTTP.Host, cfg.HTTP.Port)
}

// GetMigrationPath 获取迁移文件路径
func (cfg *TestConfig) GetMigrationPath() string {
	if filepath.IsAbs(cfg.Database.MigrationPath) {
		return cfg.Database.MigrationPath
	}
	// 相对于测试目录的路径
	return filepath.Join("..", cfg.Database.MigrationPath)
}

// GetFixturesPath 获取测试数据路径
func (cfg *TestConfig) GetFixturesPath() string {
	if filepath.IsAbs(cfg.Database.FixturesPath) {
		return cfg.Database.FixturesPath
	}
	return filepath.Join("..", cfg.Database.FixturesPath)
}

// IsTestContainer 检查是否使用测试容器
func (cfg *TestConfig) IsTestContainer() bool {
	return cfg.Database.UseTestContainer || cfg.Cache.Redis.UseTestContainer
}

// Validate 验证配置
func (cfg *TestConfig) Validate() error {
	if cfg.Database.Driver == "" {
		return fmt.Errorf("database driver is required")
	}
	if cfg.Database.Database == "" {
		return fmt.Errorf("database name is required")
	}
	if cfg.Auth.JWT.SecretKey == "" {
		return fmt.Errorf("JWT secret is required")
	}
	return nil
}
