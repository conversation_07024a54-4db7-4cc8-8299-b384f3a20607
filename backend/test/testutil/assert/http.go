package assert

import (
	"encoding/json"
	"net/http"
	"net/http/httptest"
	"strings"
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
)

// HTTPAssert HTTP断言
type HTTPAssert struct {
	t        *testing.T
	response *httptest.ResponseRecorder
}

// NewHTTPAssert 创建HTTP断言
func NewHTTPAssert(t *testing.T, response *httptest.ResponseRecorder) *HTTPAssert {
	return &HTTPAssert{t: t, response: response}
}

// StatusCode 断言状态码
func (a *HTTPAssert) StatusCode(expectedCode int) *HTTPAssert {
	assert.Equal(a.t, expectedCode, a.response.Code, 
		"HTTP状态码应该是 %d，实际是 %d", expectedCode, a.response.Code)
	return a
}

// StatusOK 断言状态码为200
func (a *HTTPAssert) StatusOK() *HTTPAssert {
	return a.StatusCode(http.StatusOK)
}

// StatusCreated 断言状态码为201
func (a *HTTPAssert) StatusCreated() *HTTPAssert {
	return a.StatusCode(http.StatusCreated)
}

// StatusNoContent 断言状态码为204
func (a *HTTPAssert) StatusNoContent() *HTTPAssert {
	return a.StatusCode(http.StatusNoContent)
}

// StatusBadRequest 断言状态码为400
func (a *HTTPAssert) StatusBadRequest() *HTTPAssert {
	return a.StatusCode(http.StatusBadRequest)
}

// StatusUnauthorized 断言状态码为401
func (a *HTTPAssert) StatusUnauthorized() *HTTPAssert {
	return a.StatusCode(http.StatusUnauthorized)
}

// StatusForbidden 断言状态码为403
func (a *HTTPAssert) StatusForbidden() *HTTPAssert {
	return a.StatusCode(http.StatusForbidden)
}

// StatusNotFound 断言状态码为404
func (a *HTTPAssert) StatusNotFound() *HTTPAssert {
	return a.StatusCode(http.StatusNotFound)
}

// StatusConflict 断言状态码为409
func (a *HTTPAssert) StatusConflict() *HTTPAssert {
	return a.StatusCode(http.StatusConflict)
}

// StatusInternalServerError 断言状态码为500
func (a *HTTPAssert) StatusInternalServerError() *HTTPAssert {
	return a.StatusCode(http.StatusInternalServerError)
}

// Header 断言响应头
func (a *HTTPAssert) Header(key, expectedValue string) *HTTPAssert {
	actualValue := a.response.Header().Get(key)
	assert.Equal(a.t, expectedValue, actualValue, 
		"响应头 %s 应该是 %s，实际是 %s", key, expectedValue, actualValue)
	return a
}

// HeaderExists 断言响应头存在
func (a *HTTPAssert) HeaderExists(key string) *HTTPAssert {
	_, exists := a.response.Header()[key]
	assert.True(a.t, exists, "响应头 %s 应该存在", key)
	return a
}

// HeaderNotExists 断言响应头不存在
func (a *HTTPAssert) HeaderNotExists(key string) *HTTPAssert {
	_, exists := a.response.Header()[key]
	assert.False(a.t, exists, "响应头 %s 不应该存在", key)
	return a
}

// ContentType 断言内容类型
func (a *HTTPAssert) ContentType(expectedContentType string) *HTTPAssert {
	return a.Header("Content-Type", expectedContentType)
}

// ContentTypeJSON 断言内容类型为JSON
func (a *HTTPAssert) ContentTypeJSON() *HTTPAssert {
	contentType := a.response.Header().Get("Content-Type")
	assert.True(a.t, strings.Contains(contentType, "application/json"), 
		"Content-Type应该包含application/json，实际是 %s", contentType)
	return a
}

// Body 断言响应体
func (a *HTTPAssert) Body(expectedBody string) *HTTPAssert {
	actualBody := a.response.Body.String()
	assert.Equal(a.t, expectedBody, actualBody, 
		"响应体应该匹配")
	return a
}

// BodyContains 断言响应体包含指定内容
func (a *HTTPAssert) BodyContains(substring string) *HTTPAssert {
	actualBody := a.response.Body.String()
	assert.Contains(a.t, actualBody, substring, 
		"响应体应该包含 %s", substring)
	return a
}

// BodyNotContains 断言响应体不包含指定内容
func (a *HTTPAssert) BodyNotContains(substring string) *HTTPAssert {
	actualBody := a.response.Body.String()
	assert.NotContains(a.t, actualBody, substring, 
		"响应体不应该包含 %s", substring)
	return a
}

// BodyEmpty 断言响应体为空
func (a *HTTPAssert) BodyEmpty() *HTTPAssert {
	actualBody := a.response.Body.String()
	assert.Empty(a.t, actualBody, "响应体应该为空")
	return a
}

// BodyNotEmpty 断言响应体不为空
func (a *HTTPAssert) BodyNotEmpty() *HTTPAssert {
	actualBody := a.response.Body.String()
	assert.NotEmpty(a.t, actualBody, "响应体不应该为空")
	return a
}

// JSON 断言响应体为有效JSON并解析到目标对象
func (a *HTTPAssert) JSON(target interface{}) *HTTPAssert {
	actualBody := a.response.Body.String()
	err := json.Unmarshal([]byte(actualBody), target)
	require.NoError(a.t, err, "响应体应该是有效的JSON")
	return a
}

// JSONPath 断言JSON路径的值
func (a *HTTPAssert) JSONPath(path string, expectedValue interface{}) *HTTPAssert {
	actualBody := a.response.Body.String()
	
	var jsonData interface{}
	err := json.Unmarshal([]byte(actualBody), &jsonData)
	require.NoError(a.t, err, "响应体应该是有效的JSON")
	
	// 简单的JSON路径解析（仅支持基本路径）
	actualValue := getJSONValue(jsonData, path)
	assert.Equal(a.t, expectedValue, actualValue, 
		"JSON路径 %s 的值应该匹配", path)
	return a
}

// JSONField 断言JSON字段的值
func (a *HTTPAssert) JSONField(field string, expectedValue interface{}) *HTTPAssert {
	actualBody := a.response.Body.String()
	
	var jsonData map[string]interface{}
	err := json.Unmarshal([]byte(actualBody), &jsonData)
	require.NoError(a.t, err, "响应体应该是有效的JSON对象")
	
	actualValue, exists := jsonData[field]
	assert.True(a.t, exists, "JSON字段 %s 应该存在", field)
	assert.Equal(a.t, expectedValue, actualValue, 
		"JSON字段 %s 的值应该匹配", field)
	return a
}

// JSONFieldExists 断言JSON字段存在
func (a *HTTPAssert) JSONFieldExists(field string) *HTTPAssert {
	actualBody := a.response.Body.String()
	
	var jsonData map[string]interface{}
	err := json.Unmarshal([]byte(actualBody), &jsonData)
	require.NoError(a.t, err, "响应体应该是有效的JSON对象")
	
	_, exists := jsonData[field]
	assert.True(a.t, exists, "JSON字段 %s 应该存在", field)
	return a
}

// JSONFieldNotExists 断言JSON字段不存在
func (a *HTTPAssert) JSONFieldNotExists(field string) *HTTPAssert {
	actualBody := a.response.Body.String()
	
	var jsonData map[string]interface{}
	err := json.Unmarshal([]byte(actualBody), &jsonData)
	require.NoError(a.t, err, "响应体应该是有效的JSON对象")
	
	_, exists := jsonData[field]
	assert.False(a.t, exists, "JSON字段 %s 不应该存在", field)
	return a
}

// JSONArrayLength 断言JSON数组长度
func (a *HTTPAssert) JSONArrayLength(expectedLength int) *HTTPAssert {
	actualBody := a.response.Body.String()
	
	var jsonData []interface{}
	err := json.Unmarshal([]byte(actualBody), &jsonData)
	require.NoError(a.t, err, "响应体应该是有效的JSON数组")
	
	assert.Len(a.t, jsonData, expectedLength, 
		"JSON数组长度应该是 %d", expectedLength)
	return a
}

// JSONArrayNotEmpty 断言JSON数组不为空
func (a *HTTPAssert) JSONArrayNotEmpty() *HTTPAssert {
	actualBody := a.response.Body.String()
	
	var jsonData []interface{}
	err := json.Unmarshal([]byte(actualBody), &jsonData)
	require.NoError(a.t, err, "响应体应该是有效的JSON数组")
	
	assert.NotEmpty(a.t, jsonData, "JSON数组不应该为空")
	return a
}

// JSONArrayEmpty 断言JSON数组为空
func (a *HTTPAssert) JSONArrayEmpty() *HTTPAssert {
	actualBody := a.response.Body.String()
	
	var jsonData []interface{}
	err := json.Unmarshal([]byte(actualBody), &jsonData)
	require.NoError(a.t, err, "响应体应该是有效的JSON数组")
	
	assert.Empty(a.t, jsonData, "JSON数组应该为空")
	return a
}

// 辅助函数

// getJSONValue 获取JSON路径的值（简单实现）
func getJSONValue(data interface{}, path string) interface{} {
	// 简单的路径解析，仅支持点分隔的字段名
	parts := strings.Split(path, ".")
	current := data
	
	for _, part := range parts {
		if m, ok := current.(map[string]interface{}); ok {
			current = m[part]
		} else {
			return nil
		}
	}
	
	return current
}

// 便捷函数

// AssertHTTP 断言HTTP响应
func AssertHTTP(t *testing.T, response *httptest.ResponseRecorder) *HTTPAssert {
	return NewHTTPAssert(t, response)
}

// AssertResponse 断言HTTP响应（别名）
func AssertResponse(t *testing.T, response *httptest.ResponseRecorder) *HTTPAssert {
	return NewHTTPAssert(t, response)
}
