package assert

import (
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	tenantEntity "backend/internal/domain/tenant/entity"
	"backend/internal/domain/tenant/valueobject"
	"backend/internal/domain/user/entity"
	"backend/internal/shared/types"
)

// DomainAssert 领域断言
type DomainAssert struct {
	t *testing.T
}

// NewDomainAssert 创建领域断言
func NewDomainAssert(t *testing.T) *DomainAssert {
	return &DomainAssert{t: t}
}

// User 用户断言
func (a *DomainAssert) User(user *entity.User) *UserAssert {
	return &UserAssert{t: a.t, user: user}
}

// Tenant 租户断言
func (a *DomainAssert) Tenant(tenant *tenantEntity.Tenant) *TenantAssert {
	return &TenantAssert{t: a.t, tenant: tenant}
}

// UserAssert 用户断言
type UserAssert struct {
	t    *testing.T
	user *entity.User
}

// NotNil 断言用户不为空
func (a *UserAssert) NotNil() *UserAssert {
	require.NotNil(a.t, a.user, "用户不应该为空")
	return a
}

// IsNil 断言用户为空
func (a *UserAssert) IsNil() *UserAssert {
	assert.Nil(a.t, a.user, "用户应该为空")
	return a
}

// HasID 断言用户有ID
func (a *UserAssert) HasID() *UserAssert {
	a.NotNil()
	assert.NotZero(a.t, a.user.ID, "用户ID不应该为0")
	return a
}

// HasBusinessID 断言用户有业务ID
func (a *UserAssert) HasBusinessID() *UserAssert {
	a.NotNil()
	assert.NotEmpty(a.t, a.user.BusinessID, "用户业务ID不应该为空")
	return a
}

// HasUsername 断言用户有用户名
func (a *UserAssert) HasUsername(username string) *UserAssert {
	a.NotNil()
	assert.Equal(a.t, username, a.user.Username, "用户名应该匹配")
	return a
}

// HasEmail 断言用户有邮箱
func (a *UserAssert) HasEmail(email string) *UserAssert {
	a.NotNil()
	assert.Equal(a.t, email, a.user.Email, "邮箱应该匹配")
	return a
}

// HasPhone 断言用户有手机号
func (a *UserAssert) HasPhone(phone string) *UserAssert {
	a.NotNil()
	assert.Equal(a.t, phone, a.user.Phone, "手机号应该匹配")
	return a
}

// HasStatus 断言用户状态
func (a *UserAssert) HasStatus(status entity.UserStatus) *UserAssert {
	a.NotNil()
	assert.Equal(a.t, status, a.user.Status, "用户状态应该匹配")
	return a
}

// IsActive 断言用户是激活状态
func (a *UserAssert) IsActive() *UserAssert {
	return a.HasStatus(entity.UserStatusActive)
}

// IsInactive 断言用户是未激活状态
func (a *UserAssert) IsInactive() *UserAssert {
	return a.HasStatus(entity.UserStatusInactive)
}

// IsSuspended 断言用户是暂停状态
func (a *UserAssert) IsSuspended() *UserAssert {
	return a.HasStatus(entity.UserStatusSuspended)
}

// IsBanned 断言用户是封禁状态
func (a *UserAssert) IsBanned() *UserAssert {
	return a.HasStatus(entity.UserStatusBanned)
}

// IsDeleted 断言用户是已删除状态
func (a *UserAssert) IsDeleted() *UserAssert {
	a.NotNil()
	assert.True(a.t, a.user.DeletedAt.Valid, "用户应该被标记为已删除")
	assert.False(a.t, a.user.DeletedAt.Time.IsZero(), "删除时间不应该为零值")
	return a
}

// IsNotDeleted 断言用户未被删除
func (a *UserAssert) IsNotDeleted() *UserAssert {
	a.NotNil()
	assert.False(a.t, a.user.DeletedAt.Valid, "用户不应该被标记为已删除")
	return a
}

// HasProfile 断言用户有档案
func (a *UserAssert) HasProfile() *UserAssert {
	a.NotNil()
	assert.NotEmpty(a.t, a.user.Profile.Nickname, "用户昵称不应该为空")
	return a
}

// HasNickname 断言用户昵称
func (a *UserAssert) HasNickname(nickname string) *UserAssert {
	a.NotNil()
	assert.Equal(a.t, nickname, a.user.Profile.Nickname, "用户昵称应该匹配")
	return a
}

// CreatedAfter 断言创建时间在指定时间之后
func (a *UserAssert) CreatedAfter(t time.Time) *UserAssert {
	a.NotNil()
	assert.True(a.t, a.user.CreatedAt.After(t), "创建时间应该在指定时间之后")
	return a
}

// CreatedBefore 断言创建时间在指定时间之前
func (a *UserAssert) CreatedBefore(t time.Time) *UserAssert {
	a.NotNil()
	assert.True(a.t, a.user.CreatedAt.Before(t), "创建时间应该在指定时间之前")
	return a
}

// UpdatedAfter 断言更新时间在指定时间之后
func (a *UserAssert) UpdatedAfter(t time.Time) *UserAssert {
	a.NotNil()
	assert.True(a.t, a.user.UpdatedAt.After(t), "更新时间应该在指定时间之后")
	return a
}

// TenantAssert 租户断言
type TenantAssert struct {
	t      *testing.T
	tenant *tenantEntity.Tenant
}

// NotNil 断言租户不为空
func (a *TenantAssert) NotNil() *TenantAssert {
	require.NotNil(a.t, a.tenant, "租户不应该为空")
	return a
}

// IsNil 断言租户为空
func (a *TenantAssert) IsNil() *TenantAssert {
	assert.Nil(a.t, a.tenant, "租户应该为空")
	return a
}

// HasID 断言租户有ID
func (a *TenantAssert) HasID() *TenantAssert {
	a.NotNil()
	assert.NotZero(a.t, a.tenant.ID, "租户ID不应该为0")
	return a
}

// HasBusinessID 断言租户有业务ID
func (a *TenantAssert) HasBusinessID() *TenantAssert {
	a.NotNil()
	assert.NotEmpty(a.t, a.tenant.BusinessID, "租户业务ID不应该为空")
	return a
}

// HasName 断言租户名称
func (a *TenantAssert) HasName(name string) *TenantAssert {
	a.NotNil()
	assert.Equal(a.t, name, a.tenant.Name, "租户名称应该匹配")
	return a
}

// HasDomain 断言租户域名
func (a *TenantAssert) HasDomain(domain string) *TenantAssert {
	a.NotNil()
	assert.Equal(a.t, domain, a.tenant.Domain, "租户域名应该匹配")
	return a
}

// HasType 断言租户类型
func (a *TenantAssert) HasType(tenantType valueobject.TenantType) *TenantAssert {
	a.NotNil()
	assert.Equal(a.t, tenantType, a.tenant.Type, "租户类型应该匹配")
	return a
}

// IsEnterprise 断言是企业版租户
func (a *TenantAssert) IsEnterprise() *TenantAssert {
	return a.HasType(valueobject.TenantTypeEnterprise)
}

// IsProfessional 断言是专业版租户
func (a *TenantAssert) IsProfessional() *TenantAssert {
	return a.HasType(valueobject.TenantTypeProfessional)
}

// IsStandard 断言是标准版租户
func (a *TenantAssert) IsStandard() *TenantAssert {
	return a.HasType(valueobject.TenantTypeBasic) // 使用 Basic 替代 Standard
}

// IsBasic 断言是基础版租户
func (a *TenantAssert) IsBasic() *TenantAssert {
	return a.HasType(valueobject.TenantTypeBasic)
}

// HasStatus 断言租户状态
func (a *TenantAssert) HasStatus(status valueobject.TenantStatus) *TenantAssert {
	a.NotNil()
	assert.Equal(a.t, status, a.tenant.Status, "租户状态应该匹配")
	return a
}

// IsActive 断言租户是激活状态
func (a *TenantAssert) IsActive() *TenantAssert {
	return a.HasStatus(valueobject.TenantStatusActive)
}

// IsInactive 断言租户是未激活状态
func (a *TenantAssert) IsInactive() *TenantAssert {
	return a.HasStatus(valueobject.TenantStatusInactive)
}

// IsSuspended 断言租户是暂停状态
func (a *TenantAssert) IsSuspended() *TenantAssert {
	return a.HasStatus(valueobject.TenantStatusSuspended)
}

// IsExpired 断言租户是过期状态
func (a *TenantAssert) IsExpired() *TenantAssert {
	return a.HasStatus(valueobject.TenantStatusExpired)
}

// HasMaxUsers 断言最大用户数
func (a *TenantAssert) HasMaxUsers(maxUsers int) *TenantAssert {
	a.NotNil()
	if a.tenant.Settings != nil {
		if val, ok := a.tenant.Settings["max_users"]; ok {
			assert.Equal(a.t, maxUsers, val, "最大用户数应该匹配")
		}
	}
	return a
}

// HasContactEmail 断言联系邮箱
func (a *TenantAssert) HasContactEmail(email string) *TenantAssert {
	a.NotNil()
	assert.Equal(a.t, email, a.tenant.ContactEmail, "联系邮箱应该匹配")
	return a
}

// 便捷函数

// AssertUser 断言用户
func AssertUser(t *testing.T, user *entity.User) *UserAssert {
	return NewDomainAssert(t).User(user)
}

// AssertTenant 断言租户
func AssertTenant(t *testing.T, tenant *tenantEntity.Tenant) *TenantAssert {
	return NewDomainAssert(t).Tenant(tenant)
}

// AssertEntity 断言实体基础属性
func AssertEntity(t *testing.T, entity interface{}) {
	switch e := entity.(type) {
	case types.GlobalEntity:
		assert.NotZero(t, e.GetTechID(), "实体ID不应该为0")
		assert.NotEmpty(t, e.GetBusinessID(), "实体业务ID不应该为空")
		assert.False(t, e.CreatedAt.IsZero(), "创建时间不应该为零值")
		assert.False(t, e.UpdatedAt.IsZero(), "更新时间不应该为零值")
	case types.MultiTenantEntity:
		assert.NotZero(t, e.GetTechID(), "实体ID不应该为0")
		assert.NotEmpty(t, e.GetBusinessID(), "实体业务ID不应该为空")
		assert.False(t, e.CreatedAt.IsZero(), "创建时间不应该为零值")
		assert.False(t, e.UpdatedAt.IsZero(), "更新时间不应该为零值")
	default:
		t.Errorf("不支持的实体类型: %T", entity)
	}
}
