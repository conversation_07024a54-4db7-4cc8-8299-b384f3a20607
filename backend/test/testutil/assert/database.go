package assert

import (
	"database/sql"
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
	"gorm.io/gorm"
)

// DatabaseAssert 数据库断言
type DatabaseAssert struct {
	t  *testing.T
	db *gorm.DB
}

// NewDatabaseAssert 创建数据库断言
func NewDatabaseAssert(t *testing.T, db *gorm.DB) *DatabaseAssert {
	return &DatabaseAssert{t: t, db: db}
}

// TableExists 断言表存在
func (a *DatabaseAssert) TableExists(tableName string) *DatabaseAssert {
	var exists bool
	err := a.db.Raw("SELECT EXISTS (SELECT FROM information_schema.tables WHERE table_name = ?)", tableName).Scan(&exists).Error
	require.NoError(a.t, err, "检查表是否存在时出错")
	assert.True(a.t, exists, "表 %s 应该存在", tableName)
	return a
}

// TableNotExists 断言表不存在
func (a *DatabaseAssert) TableNotExists(tableName string) *DatabaseAssert {
	var exists bool
	err := a.db.Raw("SELECT EXISTS (SELECT FROM information_schema.tables WHERE table_name = ?)", tableName).Scan(&exists).Error
	require.NoError(a.t, err, "检查表是否存在时出错")
	assert.False(a.t, exists, "表 %s 不应该存在", tableName)
	return a
}

// ColumnExists 断言列存在
func (a *DatabaseAssert) ColumnExists(tableName, columnName string) *DatabaseAssert {
	var exists bool
	err := a.db.Raw("SELECT EXISTS (SELECT FROM information_schema.columns WHERE table_name = ? AND column_name = ?)", 
		tableName, columnName).Scan(&exists).Error
	require.NoError(a.t, err, "检查列是否存在时出错")
	assert.True(a.t, exists, "表 %s 的列 %s 应该存在", tableName, columnName)
	return a
}

// ColumnNotExists 断言列不存在
func (a *DatabaseAssert) ColumnNotExists(tableName, columnName string) *DatabaseAssert {
	var exists bool
	err := a.db.Raw("SELECT EXISTS (SELECT FROM information_schema.columns WHERE table_name = ? AND column_name = ?)", 
		tableName, columnName).Scan(&exists).Error
	require.NoError(a.t, err, "检查列是否存在时出错")
	assert.False(a.t, exists, "表 %s 的列 %s 不应该存在", tableName, columnName)
	return a
}

// IndexExists 断言索引存在
func (a *DatabaseAssert) IndexExists(indexName string) *DatabaseAssert {
	var exists bool
	err := a.db.Raw("SELECT EXISTS (SELECT FROM pg_indexes WHERE indexname = ?)", indexName).Scan(&exists).Error
	require.NoError(a.t, err, "检查索引是否存在时出错")
	assert.True(a.t, exists, "索引 %s 应该存在", indexName)
	return a
}

// IndexNotExists 断言索引不存在
func (a *DatabaseAssert) IndexNotExists(indexName string) *DatabaseAssert {
	var exists bool
	err := a.db.Raw("SELECT EXISTS (SELECT FROM pg_indexes WHERE indexname = ?)", indexName).Scan(&exists).Error
	require.NoError(a.t, err, "检查索引是否存在时出错")
	assert.False(a.t, exists, "索引 %s 不应该存在", indexName)
	return a
}

// RecordExists 断言记录存在
func (a *DatabaseAssert) RecordExists(model interface{}, conditions ...interface{}) *DatabaseAssert {
	var count int64
	err := a.db.Model(model).Where(conditions[0], conditions[1:]...).Count(&count).Error
	require.NoError(a.t, err, "检查记录是否存在时出错")
	assert.Greater(a.t, count, int64(0), "记录应该存在")
	return a
}

// RecordNotExists 断言记录不存在
func (a *DatabaseAssert) RecordNotExists(model interface{}, conditions ...interface{}) *DatabaseAssert {
	var count int64
	err := a.db.Model(model).Where(conditions[0], conditions[1:]...).Count(&count).Error
	require.NoError(a.t, err, "检查记录是否存在时出错")
	assert.Equal(a.t, int64(0), count, "记录不应该存在")
	return a
}

// RecordCount 断言记录数量
func (a *DatabaseAssert) RecordCount(model interface{}, expectedCount int64, conditions ...interface{}) *DatabaseAssert {
	var count int64
	query := a.db.Model(model)
	if len(conditions) > 0 {
		query = query.Where(conditions[0], conditions[1:]...)
	}
	err := query.Count(&count).Error
	require.NoError(a.t, err, "统计记录数量时出错")
	assert.Equal(a.t, expectedCount, count, "记录数量应该匹配")
	return a
}

// TableEmpty 断言表为空
func (a *DatabaseAssert) TableEmpty(model interface{}) *DatabaseAssert {
	return a.RecordCount(model, 0)
}

// TableNotEmpty 断言表不为空
func (a *DatabaseAssert) TableNotEmpty(model interface{}) *DatabaseAssert {
	var count int64
	err := a.db.Model(model).Count(&count).Error
	require.NoError(a.t, err, "统计记录数量时出错")
	assert.Greater(a.t, count, int64(0), "表不应该为空")
	return a
}

// FieldValue 断言字段值
func (a *DatabaseAssert) FieldValue(model interface{}, field string, expectedValue interface{}, conditions ...interface{}) *DatabaseAssert {
	var actualValue interface{}
	query := a.db.Model(model).Select(field)
	if len(conditions) > 0 {
		query = query.Where(conditions[0], conditions[1:]...)
	}
	err := query.First(&actualValue).Error
	require.NoError(a.t, err, "查询字段值时出错")
	assert.Equal(a.t, expectedValue, actualValue, "字段 %s 的值应该匹配", field)
	return a
}

// TransactionRollback 断言事务回滚
func (a *DatabaseAssert) TransactionRollback(fn func(*gorm.DB) error) *DatabaseAssert {
	// 记录事务前的记录数
	var beforeCount int64
	a.db.Raw("SELECT COUNT(*) FROM information_schema.tables WHERE table_schema = 'public'").Scan(&beforeCount)
	
	// 执行事务
	err := a.db.Transaction(fn)
	
	// 断言事务失败
	assert.Error(a.t, err, "事务应该失败并回滚")
	
	// 记录事务后的记录数
	var afterCount int64
	a.db.Raw("SELECT COUNT(*) FROM information_schema.tables WHERE table_schema = 'public'").Scan(&afterCount)
	
	// 断言记录数没有变化（事务已回滚）
	assert.Equal(a.t, beforeCount, afterCount, "事务回滚后记录数应该保持不变")
	
	return a
}

// TransactionCommit 断言事务提交
func (a *DatabaseAssert) TransactionCommit(fn func(*gorm.DB) error) *DatabaseAssert {
	// 执行事务
	err := a.db.Transaction(fn)
	
	// 断言事务成功
	assert.NoError(a.t, err, "事务应该成功提交")
	
	return a
}

// ForeignKeyExists 断言外键约束存在
func (a *DatabaseAssert) ForeignKeyExists(tableName, constraintName string) *DatabaseAssert {
	var exists bool
	err := a.db.Raw(`
		SELECT EXISTS (
			SELECT FROM information_schema.table_constraints 
			WHERE table_name = ? AND constraint_name = ? AND constraint_type = 'FOREIGN KEY'
		)`, tableName, constraintName).Scan(&exists).Error
	require.NoError(a.t, err, "检查外键约束是否存在时出错")
	assert.True(a.t, exists, "表 %s 的外键约束 %s 应该存在", tableName, constraintName)
	return a
}

// UniqueConstraintExists 断言唯一约束存在
func (a *DatabaseAssert) UniqueConstraintExists(tableName, constraintName string) *DatabaseAssert {
	var exists bool
	err := a.db.Raw(`
		SELECT EXISTS (
			SELECT FROM information_schema.table_constraints 
			WHERE table_name = ? AND constraint_name = ? AND constraint_type = 'UNIQUE'
		)`, tableName, constraintName).Scan(&exists).Error
	require.NoError(a.t, err, "检查唯一约束是否存在时出错")
	assert.True(a.t, exists, "表 %s 的唯一约束 %s 应该存在", tableName, constraintName)
	return a
}

// CheckConstraintExists 断言检查约束存在
func (a *DatabaseAssert) CheckConstraintExists(tableName, constraintName string) *DatabaseAssert {
	var exists bool
	err := a.db.Raw(`
		SELECT EXISTS (
			SELECT FROM information_schema.table_constraints 
			WHERE table_name = ? AND constraint_name = ? AND constraint_type = 'CHECK'
		)`, tableName, constraintName).Scan(&exists).Error
	require.NoError(a.t, err, "检查检查约束是否存在时出错")
	assert.True(a.t, exists, "表 %s 的检查约束 %s 应该存在", tableName, constraintName)
	return a
}

// ColumnType 断言列类型
func (a *DatabaseAssert) ColumnType(tableName, columnName, expectedType string) *DatabaseAssert {
	var actualType string
	err := a.db.Raw(`
		SELECT data_type FROM information_schema.columns 
		WHERE table_name = ? AND column_name = ?
	`, tableName, columnName).Scan(&actualType).Error
	require.NoError(a.t, err, "查询列类型时出错")
	assert.Equal(a.t, expectedType, actualType, "表 %s 列 %s 的类型应该是 %s", tableName, columnName, expectedType)
	return a
}

// ColumnNullable 断言列是否可为空
func (a *DatabaseAssert) ColumnNullable(tableName, columnName string, nullable bool) *DatabaseAssert {
	var isNullable string
	err := a.db.Raw(`
		SELECT is_nullable FROM information_schema.columns 
		WHERE table_name = ? AND column_name = ?
	`, tableName, columnName).Scan(&isNullable).Error
	require.NoError(a.t, err, "查询列是否可为空时出错")
	
	expectedNullable := "NO"
	if nullable {
		expectedNullable = "YES"
	}
	
	assert.Equal(a.t, expectedNullable, isNullable, "表 %s 列 %s 的可空性应该匹配", tableName, columnName)
	return a
}

// ColumnDefault 断言列默认值
func (a *DatabaseAssert) ColumnDefault(tableName, columnName string, expectedDefault sql.NullString) *DatabaseAssert {
	var actualDefault sql.NullString
	err := a.db.Raw(`
		SELECT column_default FROM information_schema.columns 
		WHERE table_name = ? AND column_name = ?
	`, tableName, columnName).Scan(&actualDefault).Error
	require.NoError(a.t, err, "查询列默认值时出错")
	assert.Equal(a.t, expectedDefault, actualDefault, "表 %s 列 %s 的默认值应该匹配", tableName, columnName)
	return a
}

// 便捷函数

// AssertDatabase 断言数据库
func AssertDatabase(t *testing.T, db *gorm.DB) *DatabaseAssert {
	return NewDatabaseAssert(t, db)
}

// AssertDB 断言数据库（别名）
func AssertDB(t *testing.T, db *gorm.DB) *DatabaseAssert {
	return NewDatabaseAssert(t, db)
}
