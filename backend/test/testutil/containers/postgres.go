package containers

import (
	"context"
	"fmt"
	"strconv"
	"time"

	"github.com/testcontainers/testcontainers-go"
	"github.com/testcontainers/testcontainers-go/wait"

	"backend/test/testutil/config"
)

// PostgreSQLContainer PostgreSQL测试容器
type PostgreSQLContainer struct {
	testcontainers.Container
	config *config.TestConfig
}

// NewPostgreSQLContainer 创建PostgreSQL容器
func NewPostgreSQLContainer(ctx context.Context, cfg *config.TestConfig) (*PostgreSQLContainer, error) {
	req := testcontainers.ContainerRequest{
		Image:        "postgres:15-alpine",
		ExposedPorts: []string{"5432/tcp"},
		Env: map[string]string{
			"POSTGRES_DB":       cfg.Database.Database,
			"POSTGRES_USER":     cfg.Database.Username,
			"POSTGRES_PASSWORD": cfg.Database.Password,
		},
		WaitingFor: wait.ForAll(
			wait.ForLog("database system is ready to accept connections").
				WithOccurrence(2).
				WithStartupTimeout(60*time.Second),
			wait.ForListeningPort("5432/tcp"),
		),
		AutoRemove: true,
	}

	container, err := testcontainers.GenericContainer(ctx, testcontainers.GenericContainerRequest{
		ContainerRequest: req,
		Started:          true,
	})
	if err != nil {
		return nil, fmt.Errorf("failed to start PostgreSQL container: %w", err)
	}

	return &PostgreSQLContainer{
		Container: container,
		config:    cfg,
	}, nil
}

// GetDSN 获取数据库连接字符串
func (c *PostgreSQLContainer) GetDSN(ctx context.Context) (string, error) {
	host, err := c.Host(ctx)
	if err != nil {
		return "", fmt.Errorf("failed to get container host: %w", err)
	}

	port, err := c.MappedPort(ctx, "5432")
	if err != nil {
		return "", fmt.Errorf("failed to get container port: %w", err)
	}

	return fmt.Sprintf("host=%s port=%s user=%s password=%s dbname=%s sslmode=disable",
		host,
		port.Port(),
		c.config.Database.Username,
		c.config.Database.Password,
		c.config.Database.Database,
	), nil
}

// GetHost 获取主机地址
func (c *PostgreSQLContainer) GetHost(ctx context.Context) (string, error) {
	return c.Host(ctx)
}

// GetPort 获取端口
func (c *PostgreSQLContainer) GetPort(ctx context.Context) (int, error) {
	port, err := c.MappedPort(ctx, "5432")
	if err != nil {
		return 0, fmt.Errorf("failed to get container port: %w", err)
	}

	portInt, err := strconv.Atoi(port.Port())
	if err != nil {
		return 0, fmt.Errorf("failed to parse port: %w", err)
	}

	return portInt, nil
}

// GetConnectionConfig 获取连接配置
func (c *PostgreSQLContainer) GetConnectionConfig(ctx context.Context) (*config.DatabaseConfig, error) {
	host, err := c.GetHost(ctx)
	if err != nil {
		return nil, err
	}

	port, err := c.GetPort(ctx)
	if err != nil {
		return nil, err
	}

	return &config.DatabaseConfig{
		Driver:   c.config.Database.Driver,
		Host:     host,
		Port:     port,
		Database: c.config.Database.Database,
		Username: c.config.Database.Username,
		Password: c.config.Database.Password,
		SSLMode:  "disable",
	}, nil
}

// WaitForReady 等待容器就绪
func (c *PostgreSQLContainer) WaitForReady(ctx context.Context) error {
	// 等待容器启动
	if err := c.Start(ctx); err != nil {
		return fmt.Errorf("failed to start container: %w", err)
	}

	// 额外等待确保数据库完全就绪
	time.Sleep(2 * time.Second)

	return nil
}

// Exec 在容器中执行命令
func (c *PostgreSQLContainer) Exec(ctx context.Context, cmd []string) (int, error) {
	exitCode, _, err := c.Container.Exec(ctx, cmd)
	if err != nil {
		return 0, fmt.Errorf("failed to execute command: %w", err)
	}
	return exitCode, nil
}

// ExecSQL 执行SQL命令
func (c *PostgreSQLContainer) ExecSQL(ctx context.Context, sql string) error {
	cmd := []string{
		"psql",
		"-U", c.config.Database.Username,
		"-d", c.config.Database.Database,
		"-c", sql,
	}

	exitCode, err := c.Exec(ctx, cmd)
	if err != nil {
		return fmt.Errorf("failed to execute SQL: %w", err)
	}

	if exitCode != 0 {
		return fmt.Errorf("SQL execution failed with exit code %d", exitCode)
	}

	return nil
}

// LoadSQLFile 加载SQL文件
func (c *PostgreSQLContainer) LoadSQLFile(ctx context.Context, filePath string) error {
	cmd := []string{
		"psql",
		"-U", c.config.Database.Username,
		"-d", c.config.Database.Database,
		"-f", filePath,
	}

	exitCode, err := c.Exec(ctx, cmd)
	if err != nil {
		return fmt.Errorf("failed to load SQL file: %w", err)
	}

	if exitCode != 0 {
		return fmt.Errorf("SQL file loading failed with exit code %d", exitCode)
	}

	return nil
}

// CreateDatabase 创建数据库
func (c *PostgreSQLContainer) CreateDatabase(ctx context.Context, dbName string) error {
	sql := fmt.Sprintf("CREATE DATABASE %s;", dbName)
	return c.ExecSQL(ctx, sql)
}

// DropDatabase 删除数据库
func (c *PostgreSQLContainer) DropDatabase(ctx context.Context, dbName string) error {
	sql := fmt.Sprintf("DROP DATABASE IF EXISTS %s;", dbName)
	return c.ExecSQL(ctx, sql)
}

// Cleanup 清理容器
func (c *PostgreSQLContainer) Cleanup(ctx context.Context) error {
	if err := c.Terminate(ctx); err != nil {
		return fmt.Errorf("failed to terminate PostgreSQL container: %w", err)
	}
	return nil
}

// IsHealthy 检查容器健康状态
func (c *PostgreSQLContainer) IsHealthy(ctx context.Context) (bool, error) {
	state, err := c.State(ctx)
	if err != nil {
		return false, fmt.Errorf("failed to get container state: %w", err)
	}

	return state.Running && state.Health.Status == "healthy", nil
}

// GetLogs 获取容器日志
func (c *PostgreSQLContainer) GetLogs(ctx context.Context) (string, error) {
	logs, err := c.Logs(ctx)
	if err != nil {
		return "", fmt.Errorf("failed to get container logs: %w", err)
	}
	defer logs.Close()

	// 读取日志内容
	buf := make([]byte, 1024)
	n, err := logs.Read(buf)
	if err != nil {
		return "", fmt.Errorf("failed to read logs: %w", err)
	}

	return string(buf[:n]), nil
}
