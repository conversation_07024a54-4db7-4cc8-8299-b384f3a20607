package containers

import (
	"context"
	"fmt"
	"sync"
	"time"

	"github.com/testcontainers/testcontainers-go"
	"github.com/testcontainers/testcontainers-go/wait"

	"backend/test/testutil/config"
)

// ContainerManager 容器管理器
type ContainerManager struct {
	containers map[string]testcontainers.Container
	mu         sync.RWMutex
	config     *config.TestConfig
}

// NewContainerManager 创建容器管理器
func NewContainerManager(cfg *config.TestConfig) *ContainerManager {
	return &ContainerManager{
		containers: make(map[string]testcontainers.Container),
		config:     cfg,
	}
}

// StartPostgreSQL 启动PostgreSQL容器
func (cm *ContainerManager) StartPostgreSQL(ctx context.Context) (*PostgreSQLContainer, error) {
	cm.mu.Lock()
	defer cm.mu.Unlock()

	// 检查是否已经启动
	if container, exists := cm.containers["postgresql"]; exists {
		return &PostgreSQLContainer{
			Container: container,
			config:    cm.config,
		}, nil
	}

	// 创建PostgreSQL容器请求
	req := testcontainers.ContainerRequest{
		Image:        "postgres:15-alpine",
		ExposedPorts: []string{"5432/tcp"},
		Env: map[string]string{
			"POSTGRES_DB":       cm.config.Database.Database,
			"POSTGRES_USER":     cm.config.Database.Username,
			"POSTGRES_PASSWORD": cm.config.Database.Password,
		},
		WaitingFor: wait.ForAll(
			wait.ForLog("database system is ready to accept connections").
				WithOccurrence(2).
				WithStartupTimeout(60*time.Second),
			wait.ForListeningPort("5432/tcp"),
		),
		AutoRemove: true,
	}

	// 启动容器
	container, err := testcontainers.GenericContainer(ctx, testcontainers.GenericContainerRequest{
		ContainerRequest: req,
		Started:          true,
	})
	if err != nil {
		return nil, fmt.Errorf("failed to start PostgreSQL container: %w", err)
	}

	// 保存容器引用
	cm.containers["postgresql"] = container

	return &PostgreSQLContainer{
		Container: container,
		config:    cm.config,
	}, nil
}

// StartRedis 启动Redis容器
func (cm *ContainerManager) StartRedis(ctx context.Context) (*RedisContainer, error) {
	cm.mu.Lock()
	defer cm.mu.Unlock()

	// 检查是否已经启动
	if container, exists := cm.containers["redis"]; exists {
		return &RedisContainer{
			Container: container,
			config:    cm.config,
		}, nil
	}

	// 创建Redis容器请求
	req := testcontainers.ContainerRequest{
		Image:        "redis:7-alpine",
		ExposedPorts: []string{"6379/tcp"},
		WaitingFor: wait.ForAll(
			wait.ForLog("Ready to accept connections"),
			wait.ForListeningPort("6379/tcp"),
		),
		AutoRemove: true,
	}

	// 如果配置了密码，添加认证
	if cm.config.Cache.Redis.Password != "" {
		req.Cmd = []string{"redis-server", "--requirepass", cm.config.Cache.Redis.Password}
	}

	// 启动容器
	container, err := testcontainers.GenericContainer(ctx, testcontainers.GenericContainerRequest{
		ContainerRequest: req,
		Started:          true,
	})
	if err != nil {
		return nil, fmt.Errorf("failed to start Redis container: %w", err)
	}

	// 保存容器引用
	cm.containers["redis"] = container

	return &RedisContainer{
		Container: container,
		config:    cm.config,
	}, nil
}

// StartAll 启动所有需要的容器
func (cm *ContainerManager) StartAll(ctx context.Context) (*TestContainers, error) {
	containers := &TestContainers{}

	// 启动PostgreSQL
	if cm.config.Database.UseTestContainer {
		pg, err := cm.StartPostgreSQL(ctx)
		if err != nil {
			return nil, fmt.Errorf("failed to start PostgreSQL: %w", err)
		}
		containers.PostgreSQL = pg
	}

	// 启动Redis
	if cm.config.Cache.Redis.UseTestContainer {
		redis, err := cm.StartRedis(ctx)
		if err != nil {
			return nil, fmt.Errorf("failed to start Redis: %w", err)
		}
		containers.Redis = redis
	}

	return containers, nil
}

// Stop 停止指定容器
func (cm *ContainerManager) Stop(ctx context.Context, name string) error {
	cm.mu.Lock()
	defer cm.mu.Unlock()

	container, exists := cm.containers[name]
	if !exists {
		return fmt.Errorf("container %s not found", name)
	}

	if err := container.Terminate(ctx); err != nil {
		return fmt.Errorf("failed to stop container %s: %w", name, err)
	}

	delete(cm.containers, name)
	return nil
}

// StopAll 停止所有容器
func (cm *ContainerManager) StopAll(ctx context.Context) error {
	cm.mu.Lock()
	defer cm.mu.Unlock()

	var errors []error

	for name, container := range cm.containers {
		if err := container.Terminate(ctx); err != nil {
			errors = append(errors, fmt.Errorf("failed to stop container %s: %w", name, err))
		}
	}

	// 清空容器映射
	cm.containers = make(map[string]testcontainers.Container)

	if len(errors) > 0 {
		return fmt.Errorf("failed to stop some containers: %v", errors)
	}

	return nil
}

// GetContainer 获取指定容器
func (cm *ContainerManager) GetContainer(name string) (testcontainers.Container, bool) {
	cm.mu.RLock()
	defer cm.mu.RUnlock()

	container, exists := cm.containers[name]
	return container, exists
}

// ListContainers 列出所有容器
func (cm *ContainerManager) ListContainers() []string {
	cm.mu.RLock()
	defer cm.mu.RUnlock()

	names := make([]string, 0, len(cm.containers))
	for name := range cm.containers {
		names = append(names, name)
	}
	return names
}

// IsRunning 检查容器是否运行
func (cm *ContainerManager) IsRunning(ctx context.Context, name string) (bool, error) {
	cm.mu.RLock()
	container, exists := cm.containers[name]
	cm.mu.RUnlock()

	if !exists {
		return false, nil
	}

	state, err := container.State(ctx)
	if err != nil {
		return false, fmt.Errorf("failed to get container state: %w", err)
	}

	return state.Running, nil
}

// WaitForReady 等待容器就绪
func (cm *ContainerManager) WaitForReady(ctx context.Context, name string, timeout time.Duration) error {
	cm.mu.RLock()
	container, exists := cm.containers[name]
	cm.mu.RUnlock()

	if !exists {
		return fmt.Errorf("container %s not found", name)
	}

	// 创建带超时的上下文
	timeoutCtx, cancel := context.WithTimeout(ctx, timeout)
	defer cancel()

	// 等待容器就绪
	return container.Start(timeoutCtx)
}

// TestContainers 测试容器集合
type TestContainers struct {
	PostgreSQL *PostgreSQLContainer
	Redis      *RedisContainer
}

// Cleanup 清理所有容器
func (tc *TestContainers) Cleanup(ctx context.Context) error {
	var errors []error

	if tc.PostgreSQL != nil {
		if err := tc.PostgreSQL.Terminate(ctx); err != nil {
			errors = append(errors, fmt.Errorf("failed to cleanup PostgreSQL: %w", err))
		}
	}

	if tc.Redis != nil {
		if err := tc.Redis.Terminate(ctx); err != nil {
			errors = append(errors, fmt.Errorf("failed to cleanup Redis: %w", err))
		}
	}

	if len(errors) > 0 {
		return fmt.Errorf("cleanup errors: %v", errors)
	}

	return nil
}

// GetDatabaseDSN 获取数据库连接字符串
func (tc *TestContainers) GetDatabaseDSN(ctx context.Context) (string, error) {
	if tc.PostgreSQL == nil {
		return "", fmt.Errorf("PostgreSQL container not available")
	}
	return tc.PostgreSQL.GetDSN(ctx)
}

// GetRedisAddr 获取Redis地址
func (tc *TestContainers) GetRedisAddr(ctx context.Context) (string, error) {
	if tc.Redis == nil {
		return "", fmt.Errorf("Redis container not available")
	}
	return tc.Redis.GetAddr(ctx)
}

// 全局容器管理器实例
var (
	globalManager *ContainerManager
	managerOnce   sync.Once
)

// GetGlobalManager 获取全局容器管理器
func GetGlobalManager() *ContainerManager {
	managerOnce.Do(func() {
		globalManager = NewContainerManager(config.GetTestConfig())
	})
	return globalManager
}

// StartGlobalContainers 启动全局容器
func StartGlobalContainers(ctx context.Context) (*TestContainers, error) {
	return GetGlobalManager().StartAll(ctx)
}

// StopGlobalContainers 停止全局容器
func StopGlobalContainers(ctx context.Context) error {
	return GetGlobalManager().StopAll(ctx)
}
