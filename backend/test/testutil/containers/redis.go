package containers

import (
	"context"
	"fmt"
	"strconv"
	"time"

	"github.com/testcontainers/testcontainers-go"
	"github.com/testcontainers/testcontainers-go/wait"

	"backend/test/testutil/config"
)

// RedisContainer Redis测试容器
type RedisContainer struct {
	testcontainers.Container
	config *config.TestConfig
}

// NewRedisContainer 创建Redis容器
func NewRedisContainer(ctx context.Context, cfg *config.TestConfig) (*RedisContainer, error) {
	req := testcontainers.ContainerRequest{
		Image:        "redis:7-alpine",
		ExposedPorts: []string{"6379/tcp"},
		WaitingFor: wait.ForAll(
			wait.ForLog("Ready to accept connections"),
			wait.ForListeningPort("6379/tcp"),
		),
		AutoRemove: true,
	}

	// 如果配置了密码，添加认证
	if cfg.Cache.Redis.Password != "" {
		req.Cmd = []string{"redis-server", "--requirepass", cfg.Cache.Redis.Password}
	}

	container, err := testcontainers.GenericContainer(ctx, testcontainers.GenericContainerRequest{
		ContainerRequest: req,
		Started:          true,
	})
	if err != nil {
		return nil, fmt.Errorf("failed to start Redis container: %w", err)
	}

	return &RedisContainer{
		Container: container,
		config:    cfg,
	}, nil
}

// GetAddr 获取Redis地址
func (c *RedisContainer) GetAddr(ctx context.Context) (string, error) {
	host, err := c.Host(ctx)
	if err != nil {
		return "", fmt.Errorf("failed to get container host: %w", err)
	}

	port, err := c.MappedPort(ctx, "6379")
	if err != nil {
		return "", fmt.Errorf("failed to get container port: %w", err)
	}

	return fmt.Sprintf("%s:%s", host, port.Port()), nil
}

// GetHost 获取主机地址
func (c *RedisContainer) GetHost(ctx context.Context) (string, error) {
	return c.Host(ctx)
}

// GetPort 获取端口
func (c *RedisContainer) GetPort(ctx context.Context) (int, error) {
	port, err := c.MappedPort(ctx, "6379")
	if err != nil {
		return 0, fmt.Errorf("failed to get container port: %w", err)
	}

	portInt, err := strconv.Atoi(port.Port())
	if err != nil {
		return 0, fmt.Errorf("failed to parse port: %w", err)
	}

	return portInt, nil
}

// GetConnectionConfig 获取连接配置
func (c *RedisContainer) GetConnectionConfig(ctx context.Context) (*config.RedisConfig, error) {
	host, err := c.GetHost(ctx)
	if err != nil {
		return nil, err
	}

	port, err := c.GetPort(ctx)
	if err != nil {
		return nil, err
	}

	return &config.RedisConfig{
		Host:     host,
		Port:     port,
		Password: c.config.Cache.Redis.Password,
		Database: c.config.Cache.Redis.Database,
	}, nil
}

// WaitForReady 等待容器就绪
func (c *RedisContainer) WaitForReady(ctx context.Context) error {
	// 等待容器启动
	if err := c.Start(ctx); err != nil {
		return fmt.Errorf("failed to start container: %w", err)
	}

	// 额外等待确保Redis完全就绪
	time.Sleep(1 * time.Second)

	return nil
}

// Exec 在容器中执行命令
func (c *RedisContainer) Exec(ctx context.Context, cmd []string) (int, error) {
	exitCode, _, err := c.Container.Exec(ctx, cmd)
	if err != nil {
		return 0, fmt.Errorf("failed to execute command: %w", err)
	}
	return exitCode, nil
}

// ExecRedisCmd 执行Redis命令
func (c *RedisContainer) ExecRedisCmd(ctx context.Context, args ...string) error {
	cmd := []string{"redis-cli"}

	// 如果有密码，添加认证参数
	if c.config.Cache.Redis.Password != "" {
		cmd = append(cmd, "-a", c.config.Cache.Redis.Password)
	}

	// 添加数据库选择
	if c.config.Cache.Redis.Database != 0 {
		cmd = append(cmd, "-n", strconv.Itoa(c.config.Cache.Redis.Database))
	}

	// 添加命令参数
	cmd = append(cmd, args...)

	exitCode, err := c.Exec(ctx, cmd)
	if err != nil {
		return fmt.Errorf("failed to execute Redis command: %w", err)
	}

	if exitCode != 0 {
		return fmt.Errorf("Redis command execution failed with exit code %d", exitCode)
	}

	return nil
}

// Ping 测试Redis连接
func (c *RedisContainer) Ping(ctx context.Context) error {
	return c.ExecRedisCmd(ctx, "PING")
}

// FlushDB 清空当前数据库
func (c *RedisContainer) FlushDB(ctx context.Context) error {
	return c.ExecRedisCmd(ctx, "FLUSHDB")
}

// FlushAll 清空所有数据库
func (c *RedisContainer) FlushAll(ctx context.Context) error {
	return c.ExecRedisCmd(ctx, "FLUSHALL")
}

// Set 设置键值
func (c *RedisContainer) Set(ctx context.Context, key, value string) error {
	return c.ExecRedisCmd(ctx, "SET", key, value)
}

// Get 获取键值
func (c *RedisContainer) Get(ctx context.Context, key string) error {
	return c.ExecRedisCmd(ctx, "GET", key)
}

// Del 删除键
func (c *RedisContainer) Del(ctx context.Context, keys ...string) error {
	args := append([]string{"DEL"}, keys...)
	return c.ExecRedisCmd(ctx, args...)
}

// Exists 检查键是否存在
func (c *RedisContainer) Exists(ctx context.Context, key string) error {
	return c.ExecRedisCmd(ctx, "EXISTS", key)
}

// Keys 获取匹配的键列表
func (c *RedisContainer) Keys(ctx context.Context, pattern string) error {
	return c.ExecRedisCmd(ctx, "KEYS", pattern)
}

// Info 获取Redis信息
func (c *RedisContainer) Info(ctx context.Context, section ...string) error {
	args := []string{"INFO"}
	args = append(args, section...)
	return c.ExecRedisCmd(ctx, args...)
}

// Cleanup 清理容器
func (c *RedisContainer) Cleanup(ctx context.Context) error {
	// 清空数据
	if err := c.FlushAll(ctx); err != nil {
		// 忽略清空错误，继续终止容器
	}

	if err := c.Terminate(ctx); err != nil {
		return fmt.Errorf("failed to terminate Redis container: %w", err)
	}
	return nil
}

// IsHealthy 检查容器健康状态
func (c *RedisContainer) IsHealthy(ctx context.Context) (bool, error) {
	state, err := c.State(ctx)
	if err != nil {
		return false, fmt.Errorf("failed to get container state: %w", err)
	}

	if !state.Running {
		return false, nil
	}

	// 尝试ping测试连接
	if err := c.Ping(ctx); err != nil {
		return false, nil
	}

	return true, nil
}

// GetLogs 获取容器日志
func (c *RedisContainer) GetLogs(ctx context.Context) (string, error) {
	logs, err := c.Logs(ctx)
	if err != nil {
		return "", fmt.Errorf("failed to get container logs: %w", err)
	}
	defer logs.Close()

	// 读取日志内容
	buf := make([]byte, 1024)
	n, err := logs.Read(buf)
	if err != nil {
		return "", fmt.Errorf("failed to read logs: %w", err)
	}

	return string(buf[:n]), nil
}

// GetStats 获取容器统计信息
func (c *RedisContainer) GetStats(ctx context.Context) error {
	return c.Info(ctx, "stats")
}

// GetMemoryUsage 获取内存使用情况
func (c *RedisContainer) GetMemoryUsage(ctx context.Context) error {
	return c.Info(ctx, "memory")
}
