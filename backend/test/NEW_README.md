# 测试模块重构文档

## 📋 新的目录结构

```
test/
├── README.md                    # 本文档
├── Makefile                     # 测试执行脚本
├── config/                      # 测试配置
│   ├── test.yaml               # 测试环境配置
│   ├── docker-compose.test.yaml # 测试依赖服务
│   └── testcontainers.yaml    # 容器测试配置
├── fixtures/                    # 测试数据和夹具
│   ├── data/                   # 测试数据文件
│   ├── sql/                    # SQL测试数据
│   └── builders/               # 测试数据构建器
├── mocks/                       # 统一Mock管理
│   ├── domain/                 # 领域层Mock
│   ├── application/            # 应用层Mock
│   ├── infrastructure/         # 基础设施Mock
│   └── shared/                 # 共享Mock
├── testutil/                    # 测试工具库
│   ├── assert/                 # 自定义断言
│   ├── builders/               # 测试对象构建器
│   ├── containers/             # 测试容器管理
│   ├── database/               # 数据库测试工具
│   ├── http/                   # HTTP测试工具
│   ├── auth/                   # 认证测试工具
│   └── common/                 # 通用工具
├── unit/                        # 单元测试
│   ├── domain/                 # 领域层测试
│   ├── application/            # 应用层测试
│   ├── infrastructure/         # 基础设施层测试
│   └── shared/                 # 共享组件测试
├── integration/                 # 集成测试
│   ├── api/                    # API集成测试
│   ├── database/               # 数据库集成测试
│   ├── cache/                  # 缓存集成测试
│   ├── messaging/              # 消息集成测试
│   └── external/               # 外部服务集成测试
├── e2e/                        # 端到端测试
│   ├── scenarios/              # 测试场景
│   ├── pages/                  # 页面对象模式
│   └── data/                   # E2E测试数据
├── performance/                 # 性能测试
│   ├── load/                   # 负载测试
│   ├── stress/                 # 压力测试
│   └── benchmark/              # 基准测试
└── contract/                    # 契约测试
    ├── consumer/               # 消费者契约
    └── provider/               # 提供者契约
```

## 🎯 测试分层

### 1. 单元测试 (Unit Tests)
- **目标**: 测试单个组件的功能
- **范围**: 函数、方法、类
- **依赖**: 使用Mock对象
- **执行速度**: 快速 (< 1秒)
- **覆盖率要求**: > 80%

### 2. 集成测试 (Integration Tests)
- **目标**: 测试组件间的交互
- **范围**: 数据库、缓存、外部服务
- **依赖**: 真实的外部依赖或测试容器
- **执行速度**: 中等 (1-10秒)
- **覆盖率要求**: > 60%

### 3. 端到端测试 (E2E Tests)
- **目标**: 测试完整的用户场景
- **范围**: 整个应用程序
- **依赖**: 完整的测试环境
- **执行速度**: 慢 (10秒+)
- **覆盖率要求**: 关键路径100%

## 🔧 重构计划

### 阶段1：基础设施重构 (1-2天)
1. **创建新的测试目录结构**
   - 按照新的设计创建完整的目录结构
   - 包括config、fixtures、mocks、testutil等目录

2. **重构测试配置管理**
   - 创建统一的测试配置文件和配置管理工具
   - 支持不同环境的测试配置

3. **建立测试容器基础设施**
   - 使用testcontainers创建PostgreSQL、Redis等测试容器的管理工具

4. **重构Mock生成和管理**
   - 统一管理所有Mock对象
   - 按照架构分层组织Mock文件

### 阶段2：测试工具库开发 (2-3天)
1. **开发测试数据构建器**
   - 创建灵活的测试数据构建器
   - 支持链式调用和随机数据生成

2. **开发自定义断言工具**
   - 创建针对领域对象、HTTP响应、数据库等的专用断言工具

3. **开发数据库测试工具**
   - 创建数据库设置、迁移管理、数据清理等工具

4. **开发HTTP测试工具**
   - 创建HTTP测试客户端、测试服务器和响应记录器

### 阶段3：单元测试重构 (3-4天)
1. **重构领域层测试**
   - 按照新的结构重构所有领域实体、值对象和领域服务的测试

2. **重构应用层测试**
   - 重构所有UseCase、应用服务和组装器的测试
   - 使用新的Mock管理和测试工具

3. **重构基础设施层测试**
   - 重构所有Repository、缓存、消息等基础设施的测试

4. **重构共享组件测试**
   - 重构共享服务、类型、工具等组件的测试

### 阶段4：集成测试重构 (2-3天)
1. **重构API集成测试**
   - 重构所有HTTP API的集成测试
   - 使用新的HTTP测试工具和测试容器

2. **重构数据库集成测试**
   - 重构数据库迁移、事务、Repository等的集成测试

3. **重构缓存集成测试**
   - 重构Redis缓存、缓存策略等的集成测试

4. **重构消息和外部服务集成测试**
   - 重构事件消息、外部API等的集成测试

### 阶段5：高级测试功能 (2-3天)
1. **建立端到端测试框架**
   - 创建完整的E2E测试框架
   - 包括测试场景和页面对象模式

2. **建立性能测试框架**
   - 创建负载测试、压力测试和基准测试的框架

3. **建立契约测试框架**
   - 创建消费者和提供者契约测试的框架

4. **完善测试文档和规范**
   - 编写完整的测试文档、编写规范和最佳实践指南

## 🚀 快速开始

### 1. 环境准备

```bash
# 安装依赖
go mod download

# 安装测试工具
go install github.com/vektra/mockery/v2@latest

# 启动测试依赖
make test-deps-up
```

### 2. 运行测试

```bash
# 运行所有测试
make test

# 运行单元测试
make test-unit

# 运行集成测试
make test-integration

# 运行特定包的测试
go test ./test/unit/domain/user/...

# 运行带覆盖率的测试
make test-coverage
```

## 📝 编写测试

### 单元测试示例

```go
package user_test

import (
    "context"
    "testing"

    "github.com/stretchr/testify/assert"
    "github.com/stretchr/testify/require"

    "backend/internal/domain/user/entity"
    "backend/test/testutil/builders"
)

func TestUser_Activate(t *testing.T) {
    // Arrange
    user := builders.NewUserBuilder().
        Inactive().
        Build()

    // Act
    err := user.Activate()

    // Assert
    require.NoError(t, err)
    assert.Equal(t, entity.UserStatusActive, user.Status)
}
```

### 集成测试示例

```go
package integration_test

import (
    "context"
    "testing"

    "github.com/stretchr/testify/suite"

    "backend/test/testutil/containers"
    "backend/test/testutil/database"
)

type UserRepositoryTestSuite struct {
    suite.Suite
    containers *containers.TestContainers
    db         *database.TestDB
}

func (s *UserRepositoryTestSuite) SetupSuite() {
    ctx := context.Background()
    
    // 启动测试容器
    containers, err := containers.StartGlobalContainers(ctx)
    s.Require().NoError(err)
    s.containers = containers
    
    // 设置测试数据库
    dsn, err := containers.GetDatabaseDSN(ctx)
    s.Require().NoError(err)
    
    s.db, err = database.NewTestDB(dsn)
    s.Require().NoError(err)
}

func TestUserRepositoryTestSuite(t *testing.T) {
    suite.Run(t, new(UserRepositoryTestSuite))
}
```

## 🎨 最佳实践

### 1. 测试命名
- **文件命名**: `*_test.go`
- **函数命名**: `TestFunction_Scenario`
- **子测试**: `t.Run("scenario description", func(t *testing.T) {...})`

### 2. 测试结构
使用 **AAA 模式** (Arrange, Act, Assert):

```go
func TestFunction_Scenario(t *testing.T) {
    // Arrange - 准备测试数据和环境
    user := builders.NewUserBuilder().Build()
    
    // Act - 执行被测试的操作
    result, err := service.ProcessUser(user)
    
    // Assert - 验证结果
    require.NoError(t, err)
    assert.Equal(t, expected, result)
}
```

### 3. Mock使用
- **原则**: 只Mock外部依赖
- **范围**: Repository、外部服务、基础设施
- **避免**: Mock值对象、实体、简单函数

## 🛠️ 常用命令

```bash
# 测试相关
make test                    # 运行所有测试
make test-unit              # 运行单元测试
make test-integration       # 运行集成测试
make test-e2e              # 运行E2E测试
make test-coverage         # 生成覆盖率报告

# Mock相关
make generate-mocks        # 生成所有Mock
make clean-mocks          # 清理Mock文件

# 环境相关
make test-deps-up         # 启动测试依赖
make test-deps-down       # 停止测试依赖
make test-db-reset        # 重置测试数据库
```
