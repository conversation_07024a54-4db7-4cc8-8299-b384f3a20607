package integration

import (
	"net/http"
	"net/http/httptest"
	"testing"
	"time"

	"backend/internal/adapters/http/middleware"
	"backend/pkg/infrastructure/logger"
	"backend/pkg/infrastructure/monitoring"

	"github.com/gin-gonic/gin"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
)

func TestRequestIDMiddleware(t *testing.T) {
	// 创建测试日志器
	testLogger, err := logger.NewZapLogger(&logger.Config{
		Level:  "debug",
		Format: "json",
	})
	require.NoError(t, err)

	// 创建测试路由器
	gin.SetMode(gin.TestMode)
	router := gin.New()

	// 添加请求ID中间件
	router.Use(middleware.RequestIDMiddleware(testLogger))

	// 添加测试端点
	router.GET("/test", func(c *gin.Context) {
		requestID := middleware.GetRequestID(c)
		correlationID := middleware.GetCorrelationID(c)

		c.<PERSON>(200, gin.H{
			"request_id":     requestID,
			"correlation_id": correlationID,
			"message":        "test",
		})
	})

	// 测试1: 不带请求ID的请求
	req, err := http.NewRequest("GET", "/test", nil)
	require.NoError(t, err)

	w := httptest.NewRecorder()
	router.ServeHTTP(w, req)

	assert.Equal(t, http.StatusOK, w.Code)

	// 验证响应头包含请求ID
	requestID := w.Header().Get("X-Request-ID")
	correlationID := w.Header().Get("X-Correlation-ID")

	assert.NotEmpty(t, requestID)
	assert.NotEmpty(t, correlationID)
	assert.Len(t, requestID, 36)     // UUID长度
	assert.Len(t, correlationID, 36) // UUID长度

	// 测试2: 带请求ID的请求
	customRequestID := "custom-request-id-123"
	customCorrelationID := "custom-correlation-id-456"

	req, err = http.NewRequest("GET", "/test", nil)
	require.NoError(t, err)
	req.Header.Set("X-Request-ID", customRequestID)
	req.Header.Set("X-Correlation-ID", customCorrelationID)

	w = httptest.NewRecorder()
	router.ServeHTTP(w, req)

	assert.Equal(t, http.StatusOK, w.Code)

	// 验证使用了自定义的请求ID
	assert.Equal(t, customRequestID, w.Header().Get("X-Request-ID"))
	assert.Equal(t, customCorrelationID, w.Header().Get("X-Correlation-ID"))
}

func TestOperationTracker(t *testing.T) {
	// 创建测试组件
	testLogger, err := logger.NewZapLogger(&logger.Config{
		Level:  "debug",
		Format: "json",
	})
	require.NoError(t, err)

	metricsManager := monitoring.NewMetricsManager()
	operationTracker := monitoring.NewOperationTracker(testLogger, metricsManager)

	// 创建测试路由器
	gin.SetMode(gin.TestMode)
	router := gin.New()

	// 添加中间件
	router.Use(middleware.RequestIDMiddleware(testLogger))

	// 添加测试端点
	router.POST("/test/business", func(c *gin.Context) {
		ctx := c.Request.Context()

		// 模拟业务操作追踪
		operationTracker.TrackBusinessOperation(ctx, "create_order",
			time.Now().Add(-100*time.Millisecond), true, map[string]interface{}{
				"order_id": "order-123",
				"amount":   99.99,
			})

		c.JSON(200, gin.H{"message": "business operation tracked"})
	})

	router.GET("/test/database", func(c *gin.Context) {
		ctx := c.Request.Context()

		// 模拟数据库操作追踪
		operationTracker.TrackDatabaseOperation(ctx, "SELECT", "orders",
			time.Now().Add(-50*time.Millisecond), 1, nil)

		c.JSON(200, gin.H{"message": "database operation tracked"})
	})

	router.GET("/test/cache", func(c *gin.Context) {
		ctx := c.Request.Context()

		// 模拟缓存操作追踪
		operationTracker.TrackCacheOperation(ctx, "get", "user:123",
			time.Now().Add(-10*time.Millisecond), true, 256, nil)

		c.JSON(200, gin.H{"message": "cache operation tracked"})
	})

	// 测试业务操作追踪
	req, err := http.NewRequest("POST", "/test/business", nil)
	require.NoError(t, err)

	w := httptest.NewRecorder()
	router.ServeHTTP(w, req)

	assert.Equal(t, http.StatusOK, w.Code)
	assert.NotEmpty(t, w.Header().Get("X-Request-ID"))

	// 测试数据库操作追踪
	req, err = http.NewRequest("GET", "/test/database", nil)
	require.NoError(t, err)

	w = httptest.NewRecorder()
	router.ServeHTTP(w, req)

	assert.Equal(t, http.StatusOK, w.Code)

	// 测试缓存操作追踪
	req, err = http.NewRequest("GET", "/test/cache", nil)
	require.NoError(t, err)

	w = httptest.NewRecorder()
	router.ServeHTTP(w, req)

	assert.Equal(t, http.StatusOK, w.Code)

	// 验证操作追踪器不为空
	assert.NotNil(t, operationTracker)
}

func TestStructuredLogging(t *testing.T) {
	// 创建测试日志器
	testLogger, err := logger.NewZapLogger(&logger.Config{
		Level:  "debug",
		Format: "json",
	})
	require.NoError(t, err)

	structuredLogger := logger.NewStructuredLogger(testLogger)

	// 创建测试路由器
	gin.SetMode(gin.TestMode)
	router := gin.New()

	// 添加中间件
	router.Use(middleware.RequestIDMiddleware(testLogger))

	// 添加测试端点
	router.POST("/test/structured-logging", func(c *gin.Context) {
		ctx := c.Request.Context()

		// 测试业务操作日志
		structuredLogger.LogBusinessOperation(ctx, logger.BusinessOperation{
			Operation: "user_registration",
			TenantID:  "tenant-123",
			UserID:    "user-456",
			Action:    "register",
			Status:    "success",
			Duration:  time.Duration(200 * time.Millisecond),
			Details: map[string]interface{}{
				"email": "<EMAIL>",
				"role":  "customer",
			},
		})

		// 测试数据库操作日志
		structuredLogger.LogDatabaseOperation(ctx, logger.DatabaseOperation{
			Operation:    "INSERT",
			Table:        "users",
			Duration:     time.Duration(50 * time.Millisecond),
			RowsAffected: 1,
		})

		// 测试缓存操作日志
		structuredLogger.LogCacheOperation(ctx, logger.CacheOperation{
			Operation: "set",
			Key:       "user:456",
			Hit:       false,
			Duration:  time.Duration(5 * time.Millisecond),
			Size:      512,
		})

		// 测试外部调用日志
		structuredLogger.LogExternalCall(ctx, logger.ExternalCall{
			Service:      "payment-service",
			Endpoint:     "/api/v1/payments",
			Method:       "POST",
			Duration:     time.Duration(300 * time.Millisecond),
			StatusCode:   200,
			RequestSize:  1024,
			ResponseSize: 256,
		})

		// 测试安全事件日志
		structuredLogger.LogSecurityEvent(ctx, logger.SecurityEvent{
			Event:     "login_attempt",
			UserID:    "user-456",
			TenantID:  "tenant-123",
			IPAddress: "*************",
			UserAgent: "Mozilla/5.0",
			Result:    "success",
			Severity:  "low",
		})

		c.JSON(200, gin.H{"message": "structured logging completed"})
	})

	// 发送测试请求
	req, err := http.NewRequest("POST", "/test/structured-logging", nil)
	require.NoError(t, err)

	w := httptest.NewRecorder()
	router.ServeHTTP(w, req)

	assert.Equal(t, http.StatusOK, w.Code)
	assert.NotEmpty(t, w.Header().Get("X-Request-ID"))

	// 验证结构化日志器不为空
	assert.NotNil(t, structuredLogger)
}

func TestRequestContextPropagation(t *testing.T) {
	// 创建测试日志器
	testLogger, err := logger.NewZapLogger(&logger.Config{
		Level:  "debug",
		Format: "json",
	})
	require.NoError(t, err)

	// 创建测试路由器
	gin.SetMode(gin.TestMode)
	router := gin.New()

	// 添加中间件
	router.Use(middleware.RequestIDMiddleware(testLogger))

	// 添加测试端点
	router.GET("/test/context", func(c *gin.Context) {
		// 验证上下文中的请求ID
		requestID := middleware.GetRequestIDFromContext(c.Request.Context())
		correlationID := middleware.GetCorrelationIDFromContext(c.Request.Context())

		// 验证Gin上下文中的请求ID
		ginRequestID := middleware.GetRequestID(c)
		ginCorrelationID := middleware.GetCorrelationID(c)

		c.JSON(200, gin.H{
			"context_request_id":     requestID,
			"context_correlation_id": correlationID,
			"gin_request_id":         ginRequestID,
			"gin_correlation_id":     ginCorrelationID,
		})
	})

	// 发送测试请求
	req, err := http.NewRequest("GET", "/test/context", nil)
	require.NoError(t, err)

	w := httptest.NewRecorder()
	router.ServeHTTP(w, req)

	assert.Equal(t, http.StatusOK, w.Code)

	// 验证响应头包含请求ID
	headerRequestID := w.Header().Get("X-Request-ID")
	headerCorrelationID := w.Header().Get("X-Correlation-ID")

	assert.NotEmpty(t, headerRequestID)
	assert.NotEmpty(t, headerCorrelationID)

	// 解析响应体
	responseBody := w.Body.String()
	assert.Contains(t, responseBody, headerRequestID)
	assert.Contains(t, responseBody, headerCorrelationID)
}

func TestLoggingMiddlewareWithRequestID(t *testing.T) {
	// 创建测试日志器
	testLogger, err := logger.NewZapLogger(&logger.Config{
		Level:  "debug",
		Format: "json",
	})
	require.NoError(t, err)

	// 创建测试路由器
	gin.SetMode(gin.TestMode)
	router := gin.New()

	// 添加中间件（顺序很重要）
	router.Use(middleware.RequestIDMiddleware(testLogger))
	router.Use(middleware.LoggingMiddleware(testLogger))

	// 添加测试端点
	router.GET("/test/logging", func(c *gin.Context) {
		c.JSON(200, gin.H{"message": "test logging with request ID"})
	})

	// 发送测试请求
	req, err := http.NewRequest("GET", "/test/logging", nil)
	require.NoError(t, err)

	w := httptest.NewRecorder()
	router.ServeHTTP(w, req)

	assert.Equal(t, http.StatusOK, w.Code)

	// 验证响应头包含请求ID
	requestID := w.Header().Get("X-Request-ID")
	correlationID := w.Header().Get("X-Correlation-ID")

	assert.NotEmpty(t, requestID)
	assert.NotEmpty(t, correlationID)
	assert.Len(t, requestID, 36)     // UUID长度
	assert.Len(t, correlationID, 36) // UUID长度
}
