package integration

import (
	"bytes"
	"encoding/json"
	"net/http"
	"net/http/httptest"
	"testing"
	"time"

	"backend/internal/adapters/http/handler"
	"backend/pkg/infrastructure/logger"
	"backend/pkg/infrastructure/monitoring"

	"github.com/gin-gonic/gin"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
)

func TestAlertManager(t *testing.T) {
	// 创建测试日志器
	testLogger, err := logger.NewZapLogger(&logger.Config{
		Level:  "debug",
		Format: "json",
	})
	require.NoError(t, err)

	// 创建告警管理器
	alertManager := monitoring.NewAlertManager(testLogger)

	// 添加日志告警通道
	logChannel := monitoring.NewLogAlertChannel(testLogger)
	alertManager.AddChannel(logChannel)

	// 测试触发告警
	alertManager.TriggerAlert(nil, "test_alert", "warning", map[string]interface{}{
		"test_data": "test_value",
		"count":     1,
	})

	// 获取活跃告警
	activeAlerts := alertManager.GetActiveAlerts()
	assert.Len(t, activeAlerts, 1)
	assert.Equal(t, "test_alert", activeAlerts[0].Type)
	assert.Equal(t, "warning", activeAlerts[0].Severity)
	assert.Equal(t, "active", activeAlerts[0].Status)

	// 测试重复告警（应该增加计数而不是创建新告警）
	alertManager.TriggerAlert(nil, "test_alert", "warning", map[string]interface{}{
		"test_data": "test_value",
		"count":     2,
	})

	activeAlerts = alertManager.GetActiveAlerts()
	assert.Len(t, activeAlerts, 1)
	assert.Equal(t, 2, activeAlerts[0].Count)

	// 测试解决告警
	alertID := activeAlerts[0].ID
	alertManager.ResolveAlert(nil, alertID)

	activeAlerts = alertManager.GetActiveAlerts()
	assert.Len(t, activeAlerts, 0)

	// 获取告警历史
	alertHistory := alertManager.GetAlertHistory(10)
	assert.Len(t, alertHistory, 1)
	assert.Equal(t, "resolved", alertHistory[0].Status)
	assert.NotNil(t, alertHistory[0].ResolvedAt)
}

func TestPerformanceMonitor(t *testing.T) {
	// 创建测试组件
	testLogger, err := logger.NewZapLogger(&logger.Config{
		Level:  "debug",
		Format: "json",
	})
	require.NoError(t, err)

	metricsManager := monitoring.NewMetricsManager()
	alertManager := monitoring.NewAlertManager(testLogger)
	performanceMonitor := monitoring.NewPerformanceMonitor(testLogger, metricsManager, alertManager)

	// 获取当前指标
	metrics := performanceMonitor.GetCurrentMetrics()
	assert.NotNil(t, metrics)
	assert.True(t, metrics.Timestamp.After(time.Now().Add(-time.Minute)))
	assert.Greater(t, metrics.Goroutines, 0)
	assert.Greater(t, metrics.MemoryUsage.Alloc, uint64(0))

	// 验证性能监控器不为空
	assert.NotNil(t, performanceMonitor)
}

func TestMonitoringHandler(t *testing.T) {
	// 创建测试组件
	testLogger, err := logger.NewZapLogger(&logger.Config{
		Level:  "debug",
		Format: "json",
	})
	require.NoError(t, err)

	metricsManager := monitoring.NewMetricsManager()
	alertManager := monitoring.NewAlertManager(testLogger)
	performanceMonitor := monitoring.NewPerformanceMonitor(testLogger, metricsManager, alertManager)

	// 创建监控处理器
	monitoringHandler := handler.NewMonitoringHandler(performanceMonitor, alertManager, metricsManager)

	// 创建测试路由器
	gin.SetMode(gin.TestMode)
	router := gin.New()

	// 添加监控路由
	monitoringGroup := router.Group("/monitoring")
	{
		monitoringGroup.GET("/status", monitoringHandler.GetMonitoringStatus)
		monitoringGroup.GET("/health", monitoringHandler.GetSystemHealth)
		monitoringGroup.GET("/performance", monitoringHandler.GetPerformanceMetrics)
		monitoringGroup.GET("/config", monitoringHandler.GetMonitoringConfig)

		alertsGroup := monitoringGroup.Group("/alerts")
		{
			alertsGroup.GET("/active", monitoringHandler.GetActiveAlerts)
			alertsGroup.GET("/history", monitoringHandler.GetAlertHistory)
			alertsGroup.POST("/test", monitoringHandler.TriggerTestAlert)
		}
	}

	// 测试监控状态端点
	req, err := http.NewRequest("GET", "/monitoring/status", nil)
	require.NoError(t, err)

	w := httptest.NewRecorder()
	router.ServeHTTP(w, req)

	assert.Equal(t, http.StatusOK, w.Code)

	var response map[string]interface{}
	err = json.Unmarshal(w.Body.Bytes(), &response)
	require.NoError(t, err)

	assert.Equal(t, "success", response["status"])
	assert.Contains(t, response, "data")

	// 测试系统健康端点
	req, err = http.NewRequest("GET", "/monitoring/health", nil)
	require.NoError(t, err)

	w = httptest.NewRecorder()
	router.ServeHTTP(w, req)

	assert.Equal(t, http.StatusOK, w.Code)

	err = json.Unmarshal(w.Body.Bytes(), &response)
	require.NoError(t, err)

	data := response["data"].(map[string]interface{})
	assert.Contains(t, data, "health_score")
	assert.Contains(t, data, "status")
	assert.Contains(t, data, "components")

	// 测试性能指标端点
	req, err = http.NewRequest("GET", "/monitoring/performance", nil)
	require.NoError(t, err)

	w = httptest.NewRecorder()
	router.ServeHTTP(w, req)

	assert.Equal(t, http.StatusOK, w.Code)

	// 测试活跃告警端点
	req, err = http.NewRequest("GET", "/monitoring/alerts/active", nil)
	require.NoError(t, err)

	w = httptest.NewRecorder()
	router.ServeHTTP(w, req)

	assert.Equal(t, http.StatusOK, w.Code)

	// 测试告警历史端点
	req, err = http.NewRequest("GET", "/monitoring/alerts/history?limit=50", nil)
	require.NoError(t, err)

	w = httptest.NewRecorder()
	router.ServeHTTP(w, req)

	assert.Equal(t, http.StatusOK, w.Code)

	// 测试监控配置端点
	req, err = http.NewRequest("GET", "/monitoring/config", nil)
	require.NoError(t, err)

	w = httptest.NewRecorder()
	router.ServeHTTP(w, req)

	assert.Equal(t, http.StatusOK, w.Code)
}

func TestTriggerTestAlert(t *testing.T) {
	// 创建测试组件
	testLogger, err := logger.NewZapLogger(&logger.Config{
		Level:  "debug",
		Format: "json",
	})
	require.NoError(t, err)

	metricsManager := monitoring.NewMetricsManager()
	alertManager := monitoring.NewAlertManager(testLogger)
	performanceMonitor := monitoring.NewPerformanceMonitor(testLogger, metricsManager, alertManager)

	// 添加日志告警通道
	logChannel := monitoring.NewLogAlertChannel(testLogger)
	alertManager.AddChannel(logChannel)

	// 创建监控处理器
	monitoringHandler := handler.NewMonitoringHandler(performanceMonitor, alertManager, metricsManager)

	// 创建测试路由器
	gin.SetMode(gin.TestMode)
	router := gin.New()
	router.POST("/monitoring/alerts/test", monitoringHandler.TriggerTestAlert)

	// 测试触发测试告警
	alertData := map[string]interface{}{
		"type":     "test_performance",
		"severity": "warning",
		"data": map[string]interface{}{
			"cpu_usage":    85.5,
			"memory_usage": 78.2,
		},
	}

	jsonData, err := json.Marshal(alertData)
	require.NoError(t, err)

	req, err := http.NewRequest("POST", "/monitoring/alerts/test", bytes.NewBuffer(jsonData))
	require.NoError(t, err)
	req.Header.Set("Content-Type", "application/json")

	w := httptest.NewRecorder()
	router.ServeHTTP(w, req)

	assert.Equal(t, http.StatusOK, w.Code)

	var response map[string]interface{}
	err = json.Unmarshal(w.Body.Bytes(), &response)
	require.NoError(t, err)

	assert.Equal(t, "success", response["status"])
	assert.Contains(t, response, "data")

	// 验证告警已被触发
	activeAlerts := alertManager.GetActiveAlerts()
	assert.Len(t, activeAlerts, 1)
	assert.Equal(t, "test_performance", activeAlerts[0].Type)
	assert.Equal(t, "warning", activeAlerts[0].Severity)

	// 测试无效的告警级别
	invalidAlertData := map[string]interface{}{
		"type":     "test_invalid",
		"severity": "invalid_severity",
		"data":     map[string]interface{}{},
	}

	jsonData, err = json.Marshal(invalidAlertData)
	require.NoError(t, err)

	req, err = http.NewRequest("POST", "/monitoring/alerts/test", bytes.NewBuffer(jsonData))
	require.NoError(t, err)
	req.Header.Set("Content-Type", "application/json")

	w = httptest.NewRecorder()
	router.ServeHTTP(w, req)

	assert.Equal(t, http.StatusBadRequest, w.Code)

	err = json.Unmarshal(w.Body.Bytes(), &response)
	require.NoError(t, err)

	assert.Equal(t, "error", response["status"])
	assert.Contains(t, response["message"], "Invalid severity level")
}

func TestAlertChannels(t *testing.T) {
	// 创建测试日志器
	testLogger, err := logger.NewZapLogger(&logger.Config{
		Level:  "debug",
		Format: "json",
	})
	require.NoError(t, err)

	// 测试日志告警通道
	logChannel := monitoring.NewLogAlertChannel(testLogger)
	assert.Equal(t, "log", logChannel.Name())
	assert.True(t, logChannel.IsEnabled())

	// 创建测试告警
	alert := &monitoring.Alert{
		ID:          "test-alert-123",
		Type:        "test_alert",
		Severity:    "warning",
		Title:       "Test Alert",
		Description: "This is a test alert",
		Data: map[string]interface{}{
			"test_field": "test_value",
		},
		CreatedAt: time.Now(),
		Status:    "active",
		Count:     1,
	}

	// 发送告警
	err = logChannel.Send(nil, alert)
	assert.NoError(t, err)

	// 测试邮件告警通道（无配置）
	emailChannel := monitoring.NewEmailAlertChannel(testLogger, nil)
	assert.Equal(t, "email", emailChannel.Name())
	assert.False(t, emailChannel.IsEnabled())

	// 测试Webhook告警通道
	webhookChannel := monitoring.NewWebhookAlertChannel(testLogger, "")
	assert.Equal(t, "webhook", webhookChannel.Name())
	assert.False(t, webhookChannel.IsEnabled())

	webhookChannel = monitoring.NewWebhookAlertChannel(testLogger, "http://example.com/webhook")
	assert.True(t, webhookChannel.IsEnabled())

	// 测试Slack告警通道
	slackChannel := monitoring.NewSlackAlertChannel(testLogger, "", "")
	assert.Equal(t, "slack", slackChannel.Name())
	assert.False(t, slackChannel.IsEnabled())

	slackChannel = monitoring.NewSlackAlertChannel(testLogger, "http://hooks.slack.com/test", "#alerts")
	assert.True(t, slackChannel.IsEnabled())
}
