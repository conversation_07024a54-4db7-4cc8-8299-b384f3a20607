package integration

import (
	"context"
	"fmt"
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"backend/internal/application/event/handler"
	"backend/internal/domain/event"
	"backend/pkg/event/bus"
	"backend/pkg/infrastructure/logger"
	"backend/pkg/infrastructure/mq"
)

// MockMQManager 模拟MQ管理器用于测试
type MockMQManager struct {
	producer     *MockProducer
	consumer     *MockConsumer
	publisher    *MockPublisher
	subscriber   *MockSubscriber
	delayedQueue *MockDelayedQueue
}

func NewMockMQManager() *MockMQManager {
	return &MockMQManager{
		producer:     NewMockProducer(),
		consumer:     NewMockConsumer(),
		publisher:    NewMockPublisher(),
		subscriber:   NewMockSubscriber(),
		delayedQueue: NewMockDelayedQueue(),
	}
}

func (m *MockMQManager) Producer() mq.Producer         { return m.producer }
func (m *MockMQManager) Consumer() mq.Consumer         { return m.consumer }
func (m *MockMQManager) Publisher() mq.Publisher       { return m.publisher }
func (m *MockMQManager) Subscriber() mq.Subscriber     { return m.subscriber }
func (m *MockMQManager) DelayedQueue() mq.DelayedQueue { return m.delayedQueue }
func (m *MockMQManager) Health() error                 { return nil }
func (m *MockMQManager) Close() error                  { return nil }

// MockProducer 模拟生产者
type MockProducer struct {
	sentMessages []*mq.Message
}

func NewMockProducer() *MockProducer {
	return &MockProducer{
		sentMessages: make([]*mq.Message, 0),
	}
}

func (p *MockProducer) Send(ctx context.Context, topic string, payload []byte, headers ...map[string]string) error {
	message := &mq.Message{
		Topic:     topic,
		Payload:   payload,
		Timestamp: time.Now(),
	}
	if len(headers) > 0 {
		message.Headers = headers[0]
	}
	p.sentMessages = append(p.sentMessages, message)
	return nil
}

func (p *MockProducer) SendBatch(ctx context.Context, messages []*mq.Message) error {
	p.sentMessages = append(p.sentMessages, messages...)
	return nil
}

func (p *MockProducer) SendDelayed(ctx context.Context, topic string, payload []byte, delay time.Duration, headers ...map[string]string) error {
	return p.Send(ctx, topic, payload, headers...)
}

func (p *MockProducer) Close() error {
	return nil
}

// MockConsumer 模拟消费者
type MockConsumer struct {
	subscriptions map[string]mq.MessageHandler
}

func NewMockConsumer() *MockConsumer {
	return &MockConsumer{
		subscriptions: make(map[string]mq.MessageHandler),
	}
}

func (c *MockConsumer) Subscribe(ctx context.Context, topic string, handler mq.MessageHandler) error {
	c.subscriptions[topic] = handler
	return nil
}

func (c *MockConsumer) SubscribeMultiple(ctx context.Context, topics []string, handler mq.MessageHandler) error {
	for _, topic := range topics {
		c.subscriptions[topic] = handler
	}
	return nil
}

func (c *MockConsumer) Unsubscribe(topic string) error {
	delete(c.subscriptions, topic)
	return nil
}

func (c *MockConsumer) Close() error {
	return nil
}

// SimulateMessage 模拟接收消息
func (c *MockConsumer) SimulateMessage(ctx context.Context, topic string, message *mq.Message) error {
	if handler, exists := c.subscriptions[topic]; exists {
		return handler.Handle(ctx, message)
	}
	return nil
}

// MockPublisher 模拟发布者
type MockPublisher struct{}

func NewMockPublisher() *MockPublisher {
	return &MockPublisher{}
}

func (p *MockPublisher) Publish(ctx context.Context, channel string, payload []byte) error {
	return nil
}

func (p *MockPublisher) PublishMultiple(ctx context.Context, channels []string, payload []byte) error {
	return nil
}

// MockSubscriber 模拟订阅者
type MockSubscriber struct{}

func NewMockSubscriber() *MockSubscriber {
	return &MockSubscriber{}
}

func (s *MockSubscriber) Subscribe(ctx context.Context, channels []string, handler mq.MessageHandler) error {
	return nil
}

func (s *MockSubscriber) Unsubscribe(channels ...string) error {
	return nil
}

func (s *MockSubscriber) Close() error {
	return nil
}

// MockDelayedQueue 模拟延时队列
type MockDelayedQueue struct{}

func NewMockDelayedQueue() *MockDelayedQueue {
	return &MockDelayedQueue{}
}

func (q *MockDelayedQueue) AddDelayedTask(ctx context.Context, topic string, payload []byte, delay time.Duration) error {
	return nil
}

func (q *MockDelayedQueue) StartProcessor(ctx context.Context) {
	// 空实现
}

func (q *MockDelayedQueue) StopProcessor() {
	// 空实现
}

// TestRedisEventBus 测试Redis事件总线
func TestRedisEventBus(t *testing.T) {
	// 创建测试依赖
	config := &logger.Config{
		Level:  "debug",
		Format: "console",
		Output: "stdout",
	}
	mockLogger, err := logger.NewZapLogger(config)
	require.NoError(t, err)

	mockMQManager := NewMockMQManager()

	t.Run("Redis事件总线基本功能", func(t *testing.T) {
		// 创建Redis事件总线
		busConfig := bus.DefaultEventBusConfig()
		eventBus := bus.NewRedisEventBus(mockMQManager, mockLogger, busConfig, event.GlobalEventRegistry)

		// 创建事件处理器
		userHandler := handler.NewUserEventHandler(mockLogger)

		// 订阅事件
		err := eventBus.Subscribe(event.UserCreatedEventType, userHandler)
		require.NoError(t, err)

		// 启动事件总线
		ctx := context.Background()
		err = eventBus.Start(ctx)
		require.NoError(t, err)
		defer eventBus.Stop(ctx)

		// 验证运行状态
		assert.True(t, eventBus.IsRunning())

		// 创建并发布事件
		eventData := &event.UserCreatedEventData{
			UserID:    "user-123",
			TenantID:  "tenant-456",
			Username:  "testuser",
			Email:     "<EMAIL>",
			Phone:     "1234567890",
			FirstName: "Test",
			LastName:  "User",
			Status:    "active",
			CreatedAt: time.Now(),
		}

		userCreatedEvent := event.NewUserCreatedEvent("user-123", eventData)

		// 发布事件
		err = eventBus.PublishEvent(ctx, userCreatedEvent)
		require.NoError(t, err)

		// 验证消息已发送到Mock Producer
		assert.Len(t, mockMQManager.producer.sentMessages, 1)
		sentMessage := mockMQManager.producer.sentMessages[0]
		assert.Equal(t, event.UserCreatedEventType, sentMessage.Topic)
		assert.NotEmpty(t, sentMessage.Payload)

		// 验证指标
		metrics := eventBus.GetMetrics()
		assert.Equal(t, int64(1), metrics.PublishedEvents)
	})

	t.Run("批量事件发布", func(t *testing.T) {
		// 创建Redis事件总线
		busConfig := bus.DefaultEventBusConfig()
		eventBus := bus.NewRedisEventBus(mockMQManager, mockLogger, busConfig, event.GlobalEventRegistry)

		// 启动事件总线
		ctx := context.Background()
		err = eventBus.Start(ctx)
		require.NoError(t, err)
		defer eventBus.Stop(ctx)

		// 创建多个事件
		events := make([]event.DomainEvent, 3)
		for i := 0; i < 3; i++ {
			eventData := &event.UserCreatedEventData{
				UserID:    fmt.Sprintf("user-%d", i),
				TenantID:  "tenant-456",
				Username:  fmt.Sprintf("testuser%d", i),
				Email:     fmt.Sprintf("<EMAIL>", i),
				Phone:     "1234567890",
				FirstName: "Test",
				LastName:  "User",
				Status:    "active",
				CreatedAt: time.Now(),
			}
			events[i] = event.NewUserCreatedEvent(fmt.Sprintf("user-%d", i), eventData)
		}

		// 重置Mock Producer
		mockMQManager.producer.sentMessages = make([]*mq.Message, 0)

		// 批量发布事件
		err = eventBus.PublishEvents(ctx, events)
		require.NoError(t, err)

		// 验证批量消息已发送
		assert.Len(t, mockMQManager.producer.sentMessages, 3)

		// 验证指标
		metrics := eventBus.GetMetrics()
		assert.Equal(t, int64(3), metrics.PublishedEvents)
	})

	t.Run("事件订阅和取消订阅", func(t *testing.T) {
		// 创建Redis事件总线
		busConfig := bus.DefaultEventBusConfig()
		eventBus := bus.NewRedisEventBus(mockMQManager, mockLogger, busConfig, event.GlobalEventRegistry)

		// 创建事件处理器
		userHandler := handler.NewUserEventHandler(mockLogger)
		projectionHandler := handler.NewUserProjectionHandler(mockLogger)

		// 订阅事件
		err := eventBus.Subscribe(event.UserCreatedEventType, userHandler)
		require.NoError(t, err)

		err = eventBus.Subscribe(event.UserCreatedEventType, projectionHandler)
		require.NoError(t, err)

		// 启动事件总线
		ctx := context.Background()
		err = eventBus.Start(ctx)
		require.NoError(t, err)
		defer eventBus.Stop(ctx)

		// 验证订阅
		metrics := eventBus.GetMetrics()
		assert.Equal(t, 1, metrics.ActiveSubscriptions) // 一个事件类型

		// 取消订阅
		err = eventBus.Unsubscribe(event.UserCreatedEventType, userHandler)
		require.NoError(t, err)

		// 验证取消订阅后的状态
		metrics = eventBus.GetMetrics()
		assert.Equal(t, 1, metrics.ActiveSubscriptions) // 还有一个处理器

		// 取消所有订阅
		err = eventBus.Unsubscribe(event.UserCreatedEventType, projectionHandler)
		require.NoError(t, err)

		metrics = eventBus.GetMetrics()
		assert.Equal(t, 0, metrics.ActiveSubscriptions) // 没有订阅了
	})
}
