package http

import (
	"testing"

	"github.com/stretchr/testify/assert"

	commandModel "backend/internal/application/command/model"
	"backend/pkg/common/response"
)

// TestSecurityHandler_Creation 测试Security Handler创建
func TestSecurityHandler_Creation(t *testing.T) {
	// 这个测试验证SecurityHandler可以被正确创建
	// 由于需要真实的依赖注入，我们只测试基本的结构验证

	// 验证LoginCommand结构
	loginCmd := commandModel.LoginCommand{
		IdentityType: "username",
		Identifier:   "testuser",
		Credential:   "password123",
		DeviceID:     "test-device",
	}

	// 验证命令验证功能
	err := loginCmd.Validate()
	assert.NoError(t, err)

	// 验证无效命令
	invalidCmd := commandModel.LoginCommand{
		IdentityType: "invalid",
		Identifier:   "",
		Credential:   "123", // 太短
	}
	err = invalidCmd.Validate()
	assert.Error(t, err)

	// 验证SelectTenantCommand结构
	selectCmd := commandModel.SelectTenantCommand{
		UserID:   "user-123",
		TenantID: "tenant-123",
		DeviceID: "device-123",
	}
	err = selectCmd.Validate()
	assert.NoError(t, err)

	// 验证API响应结构
	apiResp := response.APIResponse{
		Code:    0,
		Message: "success",
		Data:    map[string]string{"test": "data"},
	}
	assert.Equal(t, 0, apiResp.Code)
	assert.Equal(t, "success", apiResp.Message)
	assert.NotNil(t, apiResp.Data)
}
