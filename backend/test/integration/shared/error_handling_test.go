package integration

import (
	"encoding/json"
	"net/http"
	"net/http/httptest"
	"testing"

	"backend/internal/adapters/http/middleware"
	sharedErrors "backend/internal/shared/errors"
	commonErrors "backend/pkg/common/errors"
	"backend/pkg/common/errors/codes"
	"backend/pkg/infrastructure/logger"

	"github.com/gin-gonic/gin"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
)

// ErrorResponse 错误响应结构体
type ErrorResponse struct {
	Code      string         `json:"code"`
	Message   string         `json:"message"`
	Details   map[string]any `json:"details,omitempty"`
	TraceID   string         `json:"trace_id,omitempty"`
	Timestamp int64          `json:"timestamp"`
}

// TestErrorMiddleware 测试错误中间件基本功能
func TestErrorMiddleware(t *testing.T) {
	gin.SetMode(gin.TestMode)

	log, err := logger.NewZapLogger(&logger.Config{
		Level:  "info",
		Format: "json",
	})
	require.NoError(t, err)

	// 测试shared errors包的错误处理
	t.Run("SharedErrors - TenantNotFound", func(t *testing.T) {
		router := gin.New()
		router.Use(middleware.ErrorMiddleware(log))

		router.GET("/test", func(c *gin.Context) {
			err := sharedErrors.TenantNotFound("test-123")
			c.Error(err)
		})

		req := httptest.NewRequest(http.MethodGet, "/test", nil)
		req.Header.Set("X-Trace-ID", "test-trace-123")
		w := httptest.NewRecorder()

		router.ServeHTTP(w, req)

		assert.Equal(t, http.StatusNotFound, w.Code)

		var response ErrorResponse
		err = json.Unmarshal(w.Body.Bytes(), &response)
		require.NoError(t, err)

		assert.Equal(t, codes.TenantNotFound, response.Code)
		assert.Equal(t, "租户不存在", response.Message)
		assert.Equal(t, "test-trace-123", response.TraceID)
		assert.Equal(t, "test-123", response.Details["tenantID"])
	})

	// 测试common errors包的错误处理
	t.Run("CommonErrors - Validation", func(t *testing.T) {
		router := gin.New()
		router.Use(middleware.ErrorMiddleware(log))

		router.GET("/test", func(c *gin.Context) {
			err := commonErrors.NewValidation(codes.ParamRequired, "参数缺失").
				WithDetail("field", "email").Build()
			c.Error(err)
		})

		req := httptest.NewRequest(http.MethodGet, "/test", nil)
		w := httptest.NewRecorder()

		router.ServeHTTP(w, req)

		assert.Equal(t, http.StatusBadRequest, w.Code)

		var response ErrorResponse
		err = json.Unmarshal(w.Body.Bytes(), &response)
		require.NoError(t, err)

		assert.Equal(t, codes.ParamRequired, response.Code)
		assert.Equal(t, "参数缺失", response.Message)
		assert.Equal(t, "email", response.Details["field"])
	})

	// 测试普通Go error的包装
	t.Run("Regular Error Wrapping", func(t *testing.T) {
		router := gin.New()
		router.Use(middleware.ErrorMiddleware(log))

		router.GET("/test", func(c *gin.Context) {
			err := assert.AnError // 普通的Go error
			c.Error(err)
		})

		req := httptest.NewRequest(http.MethodGet, "/test", nil)
		w := httptest.NewRecorder()

		router.ServeHTTP(w, req)

		assert.Equal(t, http.StatusInternalServerError, w.Code)

		var response ErrorResponse
		err = json.Unmarshal(w.Body.Bytes(), &response)
		require.NoError(t, err)

		assert.Equal(t, codes.SystemError, response.Code)
		assert.Equal(t, "发生未知系统错误", response.Message)
	})
}

// TestSharedErrorsHandleError 测试shared errors包的HandleError函数
func TestSharedErrorsHandleError(t *testing.T) {
	gin.SetMode(gin.TestMode)

	log, err := logger.NewZapLogger(&logger.Config{
		Level:  "info",
		Format: "json",
	})
	require.NoError(t, err)

	router := gin.New()
	router.Use(middleware.ErrorMiddleware(log))

	router.GET("/handle-error", func(c *gin.Context) {
		err := sharedErrors.TenantNotFound("test-tenant-123")
		sharedErrors.HandleError(c, err)
	})

	req := httptest.NewRequest(http.MethodGet, "/handle-error", nil)
	w := httptest.NewRecorder()

	router.ServeHTTP(w, req)

	assert.Equal(t, http.StatusNotFound, w.Code)

	var response ErrorResponse
	err = json.Unmarshal(w.Body.Bytes(), &response)
	require.NoError(t, err)

	assert.Equal(t, codes.TenantNotFound, response.Code)
	assert.Equal(t, "租户不存在", response.Message)
}

// TestErrorMiddlewarePanic 测试panic处理
func TestErrorMiddlewarePanic(t *testing.T) {
	gin.SetMode(gin.TestMode)

	log, err := logger.NewZapLogger(&logger.Config{
		Level:  "info",
		Format: "json",
	})
	require.NoError(t, err)

	router := gin.New()
	router.Use(middleware.ErrorMiddleware(log))

	router.GET("/panic", func(c *gin.Context) {
		panic("test panic")
	})

	req := httptest.NewRequest(http.MethodGet, "/panic", nil)
	req.Header.Set("X-Trace-ID", "panic-trace-123")
	w := httptest.NewRecorder()

	router.ServeHTTP(w, req)

	assert.Equal(t, http.StatusInternalServerError, w.Code)

	var response ErrorResponse
	err = json.Unmarshal(w.Body.Bytes(), &response)
	require.NoError(t, err)

	assert.Equal(t, codes.Panic, response.Code)
	assert.Contains(t, response.Message, "系统发生Panic")
	assert.Equal(t, "panic-trace-123", response.TraceID)
}
