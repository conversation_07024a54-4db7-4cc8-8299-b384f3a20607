package integration

import (
	"context"
	"fmt"
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"backend/pkg/infrastructure/cache"
	"backend/pkg/infrastructure/config"
	"backend/pkg/infrastructure/logger"
)

// TestCacheIntegration 测试缓存系统集成
func TestCacheIntegration(t *testing.T) {
	// 检查是否在集成测试环境中运行
	if testing.Short() {
		t.Skip("跳过集成测试")
	}

	// 创建测试配置
	cfg := createTestConfig()

	// 创建logger
	testLogger, err := createTestLogger()
	require.NoError(t, err)

	// 创建缓存管理器
	manager := cache.NewManager(&cfg.Business.Cache, testLogger)

	ctx := context.Background()

	// 模拟缓存适配器（在真实集成测试中，这里会创建真实的Redis连接）
	mockAdapter := &MockCacheAdapter{}

	// 注册缓存适配器
	err = manager.RegisterCache("test", mockAdapter)
	require.NoError(t, err)

	t.Run("基础缓存操作", func(t *testing.T) {
		testCache, err := manager.GetCache("test")
		require.NoError(t, err)

		testKey := "integration:test:key"
		testValue := []byte("integration test value")
		testTTL := 5 * time.Minute

		// 测试Set操作
		err = testCache.Set(ctx, testKey, testValue, testTTL)
		assert.NoError(t, err)

		// 测试Get操作
		retrievedValue, err := testCache.Get(ctx, testKey)
		assert.NoError(t, err)
		assert.Equal(t, testValue, retrievedValue)

		// 测试Exists操作
		exists, err := testCache.Exists(ctx, testKey)
		assert.NoError(t, err)
		assert.True(t, exists)

		// 测试TTL操作
		ttl, err := testCache.TTL(ctx, testKey)
		assert.NoError(t, err)
		assert.True(t, ttl > 0)

		// 测试Del操作
		err = testCache.Del(ctx, testKey)
		assert.NoError(t, err)

		// 验证删除后不存在
		exists, err = testCache.Exists(ctx, testKey)
		assert.NoError(t, err)
		assert.False(t, exists)
	})

	t.Run("批量操作", func(t *testing.T) {
		testCache, err := manager.GetCache("test")
		require.NoError(t, err)

		// 准备测试数据
		items := map[string]cache.CacheItem{
			"batch:key1": {Key: "batch:key1", Value: []byte("value1"), TTL: 10 * time.Minute},
			"batch:key2": {Key: "batch:key2", Value: []byte("value2"), TTL: 15 * time.Minute},
			"batch:key3": {Key: "batch:key3", Value: []byte("value3"), TTL: 20 * time.Minute},
		}

		// 测试BatchSet
		err = testCache.BatchSet(ctx, items)
		assert.NoError(t, err)

		// 测试BatchGet
		keys := []string{"batch:key1", "batch:key2", "batch:key3", "batch:nonexistent"}
		results, err := testCache.BatchGet(ctx, keys)
		assert.NoError(t, err)

		// 验证结果
		assert.Equal(t, []byte("value1"), results["batch:key1"])
		assert.Equal(t, []byte("value2"), results["batch:key2"])
		assert.Equal(t, []byte("value3"), results["batch:key3"])
		assert.NotContains(t, results, "batch:nonexistent")

		// 清理
		err = testCache.BatchDel(ctx, keys[:3])
		assert.NoError(t, err)
	})

	t.Run("缓存策略测试", func(t *testing.T) {
		// 测试获取缓存策略
		strategy, exists := manager.GetStrategy("user_profile")
		assert.True(t, exists)
		assert.Equal(t, "user_profile", strategy.Name)
		assert.Equal(t, 2*time.Hour, strategy.TTL)
		assert.True(t, strategy.Enabled)

		// 测试设置自定义策略
		customStrategy := &cache.CacheStrategy{
			Name:     "custom_test",
			TTL:      30 * time.Minute,
			MaxSize:  500,
			Enabled:  true,
			Compress: true,
		}
		manager.SetStrategy("custom_test", customStrategy)

		retrievedStrategy, exists := manager.GetStrategy("custom_test")
		assert.True(t, exists)
		assert.Equal(t, customStrategy, retrievedStrategy)
	})

	t.Run("缓存管理器统计", func(t *testing.T) {
		// 测试列出缓存
		caches := manager.ListCaches()
		assert.Contains(t, caches, "test")

		// 测试获取统计信息
		stats, err := manager.GetStats(ctx)
		assert.NoError(t, err)
		assert.Contains(t, stats, "test")
	})

	// 清理资源
	t.Cleanup(func() {
		err := manager.Close()
		if err != nil {
			t.Logf("清理缓存管理器失败: %v", err)
		}
	})
}

// MockCacheAdapter 用于集成测试的模拟缓存适配器
type MockCacheAdapter struct {
	data map[string][]byte
	ttls map[string]time.Time
	sets map[string]map[string]struct{} // key -> members set
}

func (m *MockCacheAdapter) Get(ctx context.Context, key string) ([]byte, error) {
	if m.data == nil {
		return nil, cache.ErrKeyNotFound
	}

	// 检查TTL
	if ttl, exists := m.ttls[key]; exists {
		if time.Now().After(ttl) {
			delete(m.data, key)
			delete(m.ttls, key)
			return nil, cache.ErrKeyNotFound
		}
	}

	value, exists := m.data[key]
	if !exists {
		return nil, cache.ErrKeyNotFound
	}
	return value, nil
}

func (m *MockCacheAdapter) Set(ctx context.Context, key string, value []byte, ttl time.Duration) error {
	if m.data == nil {
		m.data = make(map[string][]byte)
		m.ttls = make(map[string]time.Time)
		m.sets = make(map[string]map[string]struct{})
	}

	m.data[key] = value
	if ttl > 0 {
		m.ttls[key] = time.Now().Add(ttl)
	}
	return nil
}

func (m *MockCacheAdapter) Del(ctx context.Context, keys ...string) error {
	if m.data == nil {
		return nil
	}

	for _, key := range keys {
		delete(m.data, key)
		delete(m.ttls, key)
		delete(m.sets, key)
	}
	return nil
}

func (m *MockCacheAdapter) Exists(ctx context.Context, key string) (bool, error) {
	_, err := m.Get(ctx, key)
	if err == cache.ErrKeyNotFound {
		return false, nil
	}
	return err == nil, err
}

func (m *MockCacheAdapter) TTL(ctx context.Context, key string) (time.Duration, error) {
	if m.ttls == nil {
		return -1, nil
	}

	ttl, exists := m.ttls[key]
	if !exists {
		return -1, nil
	}

	remaining := time.Until(ttl)
	if remaining <= 0 {
		return -1, nil
	}
	return remaining, nil
}

func (m *MockCacheAdapter) SetTTL(ctx context.Context, key string, ttl time.Duration) error {
	if m.ttls == nil {
		m.ttls = make(map[string]time.Time)
	}

	if _, exists := m.data[key]; !exists {
		return cache.ErrKeyNotFound
	}

	m.ttls[key] = time.Now().Add(ttl)
	return nil
}

func (m *MockCacheAdapter) Close() error {
	return nil
}

func (m *MockCacheAdapter) BatchGet(ctx context.Context, keys []string) (map[string][]byte, error) {
	results := make(map[string][]byte)

	for _, key := range keys {
		if value, err := m.Get(ctx, key); err == nil {
			results[key] = value
		}
	}

	return results, nil
}

func (m *MockCacheAdapter) BatchSet(ctx context.Context, items map[string]cache.CacheItem) error {
	for _, item := range items {
		if err := m.Set(ctx, item.Key, item.Value, item.TTL); err != nil {
			return err
		}
	}
	return nil
}

func (m *MockCacheAdapter) BatchDel(ctx context.Context, keys []string) error {
	return m.Del(ctx, keys...)
}

func (m *MockCacheAdapter) Keys(ctx context.Context, pattern string) ([]string, error) {
	if m.data == nil {
		return []string{}, nil
	}

	var keys []string
	for key := range m.data {
		// 简单的模式匹配（这里只是为了测试）
		if pattern == "*" || key == pattern {
			keys = append(keys, key)
		}
	}
	return keys, nil
}

func (m *MockCacheAdapter) Scan(ctx context.Context, cursor uint64, pattern string, count int64) ([]string, uint64, error) {
	keys, err := m.Keys(ctx, pattern)
	if err != nil {
		return nil, 0, err
	}

	// 简单的分页实现
	start := int(cursor)
	end := start + int(count)

	if start >= len(keys) {
		return []string{}, 0, nil
	}

	if end > len(keys) {
		end = len(keys)
	}

	nextCursor := uint64(0)
	if end < len(keys) {
		nextCursor = uint64(end)
	}

	return keys[start:end], nextCursor, nil
}

func (m *MockCacheAdapter) SAdd(ctx context.Context, key string, members ...string) error {
	if m.sets == nil {
		m.sets = make(map[string]map[string]struct{})
	}

	if m.sets[key] == nil {
		m.sets[key] = make(map[string]struct{})
	}

	for _, member := range members {
		m.sets[key][member] = struct{}{}
	}
	return nil
}

func (m *MockCacheAdapter) SRem(ctx context.Context, key string, members ...string) error {
	if m.sets == nil {
		return nil
	}

	if m.sets[key] == nil {
		return nil
	}

	for _, member := range members {
		delete(m.sets[key], member)
	}
	return nil
}

func (m *MockCacheAdapter) SMembers(ctx context.Context, key string) ([]string, error) {
	if m.sets == nil {
		return []string{}, nil
	}

	setMembers, exists := m.sets[key]
	if !exists {
		return []string{}, nil
	}

	members := make([]string, 0, len(setMembers))
	for member := range setMembers {
		members = append(members, member)
	}

	return members, nil
}

func (m *MockCacheAdapter) SCard(ctx context.Context, key string) (int64, error) {
	if m.sets == nil {
		return 0, nil
	}

	setMembers, exists := m.sets[key]
	if !exists {
		return 0, nil
	}

	return int64(len(setMembers)), nil
}

func (m *MockCacheAdapter) FlushDB(ctx context.Context) error {
	m.data = make(map[string][]byte)
	m.ttls = make(map[string]time.Time)
	m.sets = make(map[string]map[string]struct{})
	return nil
}

func (m *MockCacheAdapter) Info(ctx context.Context) (*cache.CacheInfo, error) {
	totalKeys := int64(0)
	if m.data != nil {
		totalKeys = int64(len(m.data))
	}

	return &cache.CacheInfo{
		ConnectedClients: 1,
		UsedMemory:       1024 * 1024,
		MaxMemory:        10 * 1024 * 1024,
		TotalKeys:        totalKeys,
		HitRate:          0.95,
		Version:          "mock-1.0.0",
		RedisMode:        "standalone",
		Role:             "master",
	}, nil
}

func (m *MockCacheAdapter) Ping(ctx context.Context) error {
	return nil
}

// createTestConfig 创建测试配置
func createTestConfig() *config.Config {
	return &config.Config{
		Redis: config.RedisConfig{
			Host:     "localhost",
			Port:     6379,
			Password: "",
			DB:       1, // 使用测试数据库
			PoolSize: 5,
		},
		Business: config.BusinessConfig{
			Cache: config.CacheConfig{
				DefaultTTL: 1 * time.Hour,
				MaxKeys:    1000,
				Strategies: map[string]config.CacheStrategy{
					"user_profile": {
						TTL:      2 * time.Hour,
						MaxSize:  500,
						Enabled:  true,
						Compress: false,
					},
					"product_info": {
						TTL:      30 * time.Minute,
						MaxSize:  1000,
						Enabled:  true,
						Compress: true,
					},
				},
				Redis: config.CacheRedisConfig{
					Serialization: "json",
					Compression:   false,
					KeyPrefix:     "test",
					KeySeparator:  ":",
					BatchSize:     100,
					BatchTimeout:  5 * time.Second,
				},
				Metrics: config.CacheMetricsConfig{
					Enabled:            true,
					CollectInterval:    30 * time.Second,
					HitRateWindow:      5 * time.Minute,
					SlowQueryThreshold: 100 * time.Millisecond,
				},
			},
		},
	}
}

// createTestLogger 创建测试日志器
func createTestLogger() (logger.Logger, error) {
	loggerConfig := &logger.Config{
		Level:  "debug",
		Format: "text",
		Output: "stdout",
	}
	return logger.NewZapLogger(loggerConfig)
}

// TestCacheConfigValidation 测试缓存配置验证
func TestCacheConfigValidation(t *testing.T) {
	t.Run("有效配置", func(t *testing.T) {
		cfg := createTestConfig()
		assert.NotNil(t, cfg)
		assert.Equal(t, "localhost", cfg.Redis.Host)
		assert.Equal(t, 6379, cfg.Redis.Port)
		assert.Equal(t, "json", cfg.Business.Cache.Redis.Serialization)
	})

	t.Run("缓存策略配置", func(t *testing.T) {
		cfg := createTestConfig()

		userStrategy, exists := cfg.Business.Cache.Strategies["user_profile"]
		assert.True(t, exists)
		assert.Equal(t, 2*time.Hour, userStrategy.TTL)
		assert.True(t, userStrategy.Enabled)

		productStrategy, exists := cfg.Business.Cache.Strategies["product_info"]
		assert.True(t, exists)
		assert.Equal(t, 30*time.Minute, productStrategy.TTL)
		assert.True(t, productStrategy.Compress)
	})
}

// BenchmarkCacheOperations 缓存操作性能基准测试
func BenchmarkCacheOperations(b *testing.B) {
	mockAdapter := &MockCacheAdapter{}
	ctx := context.Background()

	b.Run("Set操作", func(b *testing.B) {
		b.ResetTimer()
		for i := 0; i < b.N; i++ {
			key := fmt.Sprintf("bench:set:%d", i)
			value := []byte(fmt.Sprintf("value-%d", i))
			_ = mockAdapter.Set(ctx, key, value, time.Hour)
		}
	})

	b.Run("Get操作", func(b *testing.B) {
		// 预设数据
		for i := 0; i < 1000; i++ {
			key := fmt.Sprintf("bench:get:%d", i)
			value := []byte(fmt.Sprintf("value-%d", i))
			_ = mockAdapter.Set(ctx, key, value, time.Hour)
		}

		b.ResetTimer()
		for i := 0; i < b.N; i++ {
			key := fmt.Sprintf("bench:get:%d", i%1000)
			_, _ = mockAdapter.Get(ctx, key)
		}
	})

	b.Run("BatchSet操作", func(b *testing.B) {
		items := make(map[string]cache.CacheItem)
		for i := 0; i < 100; i++ {
			key := fmt.Sprintf("bench:batch:%d", i)
			items[key] = cache.CacheItem{
				Key:   key,
				Value: []byte(fmt.Sprintf("value-%d", i)),
				TTL:   time.Hour,
			}
		}

		b.ResetTimer()
		for i := 0; i < b.N; i++ {
			_ = mockAdapter.BatchSet(ctx, items)
		}
	})
}
