package integration

import (
	"context"
	"path/filepath"
	"runtime"
	"testing"

	"backend/internal/adapters/persistence/repository/relational/auth"
	"backend/internal/domain/auth/entity"
	"backend/pkg/adapters/database"
	commonErrors "backend/pkg/common/errors"
	"backend/pkg/common/errors/codes"
	"backend/pkg/infrastructure/config"
	"backend/pkg/infrastructure/database/postgres"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
	"gorm.io/gorm"
)

// getConfigPath 智能获取配置文件路径
func getConfigPath() string {
	// 获取当前文件路径
	_, filename, _, _ := runtime.Caller(0)
	// 从当前测试文件向上查找到项目根目录的configs目录
	return filepath.Join(filepath.Dir(filename), "..", "..", "..", "configs")
}

// TestDatabaseErrorAdapter 测试数据库错误适配器
func TestDatabaseErrorAdapter(t *testing.T) {
	// 跳过集成测试，如果没有数据库连接
	if testing.Short() {
		t.Skip("跳过集成测试")
	}

	// 加载配置 - 使用智能路径解析
	configPath := getConfigPath()
	loader := config.NewLoader(configPath)
	cfg, err := loader.Load()
	require.NoError(t, err)

	// 创建数据库管理器并初始化
	dbManager := postgres.NewDatabaseManager(&cfg.Database)
	err = dbManager.Initialize()
	require.NoError(t, err)

	// 创建仓储
	sessionRepo := auth.NewSessionRepository(dbManager)

	ctx := context.Background()

	t.Run("测试记录未找到错误翻译", func(t *testing.T) {
		// 查找不存在的会话
		session, err := sessionRepo.FindBySessionID(ctx, "non-existent-session")

		// 验证返回nil和特定错误
		assert.Nil(t, session)
		assert.Error(t, err)

		// 验证错误是shared errors包的SessionNotFound
		assert.Contains(t, err.Error(), "会话不存在")
	})

	t.Run("测试约束违反错误翻译", func(t *testing.T) {
		// 创建一个会话
		session1 := &entity.UserSession{
			SessionID: "test-session-duplicate",
			UserID:    "test-user-1",
			DeviceID:  "test-device-1",
			Status:    entity.SessionStatusActive,
		}

		// 第一次保存应该成功
		err := sessionRepo.Save(ctx, session1)
		if err != nil {
			// 如果失败，可能是因为表不存在或其他原因，跳过这个测试
			t.Skipf("无法创建测试会话: %v", err)
		}

		// 清理
		defer func() {
			sessionRepo.Delete(ctx, "test-session-duplicate")
		}()

		// 创建重复的会话（相同SessionID）
		session2 := &entity.UserSession{
			SessionID: "test-session-duplicate",
			UserID:    "test-user-2",
			DeviceID:  "test-device-2",
			Status:    entity.SessionStatusActive,
		}

		// 第二次保存应该失败（唯一约束违反）
		err = sessionRepo.Save(ctx, session2)
		assert.Error(t, err)

		// 验证错误被正确翻译
		appErr := commonErrors.As(err)
		if appErr != nil {
			// 应该是冲突类型的错误
			assert.Equal(t, commonErrors.TypeConflict, appErr.Type)
		}
	})

	t.Run("测试数据库错误适配器直接调用", func(t *testing.T) {
		// 测试记录未找到错误
		notFoundErr := database.TranslateDBError(gorm.ErrRecordNotFound)
		assert.Error(t, notFoundErr)

		appErr := commonErrors.As(notFoundErr)
		if appErr != nil {
			assert.Equal(t, commonErrors.TypeNotFound, appErr.Type)
			assert.Equal(t, codes.DatabaseNotFound, appErr.Code)
		}

		// 测试无效事务错误
		invalidTxErr := database.TranslateDBError(gorm.ErrInvalidTransaction)
		assert.Error(t, invalidTxErr)

		appErr = commonErrors.As(invalidTxErr)
		if appErr != nil {
			assert.Equal(t, commonErrors.TypeInternal, appErr.Type)
			assert.Equal(t, codes.DatabaseTransaction, appErr.Code)
		}

		// 测试缺少WHERE条件错误
		missingWhereErr := database.TranslateDBError(gorm.ErrMissingWhereClause)
		assert.Error(t, missingWhereErr)

		appErr = commonErrors.As(missingWhereErr)
		if appErr != nil {
			assert.Equal(t, commonErrors.TypeValidation, appErr.Type)
			assert.Equal(t, codes.ParamInvalid, appErr.Code)
		}
	})

	t.Run("测试错误检查函数", func(t *testing.T) {
		// 测试IsNotFoundError
		assert.True(t, database.IsNotFoundError(gorm.ErrRecordNotFound))
		assert.False(t, database.IsNotFoundError(gorm.ErrInvalidTransaction))

		// 测试WrapDBError
		nilErr := database.WrapDBError(nil)
		assert.NoError(t, nilErr)

		wrappedErr := database.WrapDBError(gorm.ErrRecordNotFound)
		assert.Error(t, wrappedErr)
	})
}

// TestDatabaseErrorAdapterWithMockErrors 测试数据库错误适配器的模拟错误
func TestDatabaseErrorAdapterWithMockErrors(t *testing.T) {
	adapter := database.NewErrorAdapter()

	tests := []struct {
		name         string
		inputError   error
		expectedType commonErrors.ErrorType
		expectedCode string
	}{
		{
			name:         "GORM记录未找到",
			inputError:   gorm.ErrRecordNotFound,
			expectedType: commonErrors.TypeNotFound,
			expectedCode: codes.DatabaseNotFound,
		},
		{
			name:         "GORM无效事务",
			inputError:   gorm.ErrInvalidTransaction,
			expectedType: commonErrors.TypeInternal,
			expectedCode: codes.DatabaseTransaction,
		},
		{
			name:         "GORM缺少WHERE条件",
			inputError:   gorm.ErrMissingWhereClause,
			expectedType: commonErrors.TypeValidation,
			expectedCode: codes.ParamInvalid,
		},
		{
			name:         "GORM主键必需",
			inputError:   gorm.ErrPrimaryKeyRequired,
			expectedType: commonErrors.TypeValidation,
			expectedCode: codes.ParamRequired,
		},
		{
			name:         "GORM无效数据",
			inputError:   gorm.ErrInvalidData,
			expectedType: commonErrors.TypeValidation,
			expectedCode: codes.ParamInvalid,
		},
		{
			name:         "GORM无效数据库连接",
			inputError:   gorm.ErrInvalidDB,
			expectedType: commonErrors.TypeInternal,
			expectedCode: codes.DatabaseConnection,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			translatedErr := adapter.TranslateError(tt.inputError)
			assert.Error(t, translatedErr)

			appErr := commonErrors.As(translatedErr)
			if appErr != nil {
				assert.Equal(t, tt.expectedType, appErr.Type)
				assert.Equal(t, tt.expectedCode, appErr.Code)
			} else {
				t.Errorf("翻译后的错误不是AppError类型: %T", translatedErr)
			}
		})
	}
}

// TestDatabaseErrorAdapterNilError 测试nil错误处理
func TestDatabaseErrorAdapterNilError(t *testing.T) {
	adapter := database.NewErrorAdapter()

	// 测试nil错误
	result := adapter.TranslateError(nil)
	assert.NoError(t, result)

	// 测试WrapDBError的nil处理
	result = database.WrapDBError(nil)
	assert.NoError(t, result)
}
