package integration

import (
	"context"
	"fmt"
	"testing"

	uuidInfra "backend/pkg/infrastructure/uuid"
	uuidTypes "backend/pkg/types/uuid"

	"github.com/google/uuid"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
)

func TestNamespaceManager(t *testing.T) {
	t.Run("创建命名空间管理器", func(t *testing.T) {
		nsManager, err := uuidInfra.NewNamespaceManager("com.9wings.erp")
		require.NoError(t, err)
		assert.NotNil(t, nsManager)
	})

	t.<PERSON>("生成和缓存命名空间", func(t *testing.T) {
		nsManager, err := uuidInfra.NewNamespaceManager("com.9wings.erp")
		require.NoError(t, err)

		// 首次获取
		ns1, err := nsManager.GetOrCreate("user", "entity")
		require.NoError(t, err)
		assert.NotEqual(t, uuid.Nil, ns1)

		// 再次获取应该返回相同的UUID（来自缓存）
		ns2, err := nsManager.GetOrCreate("user", "entity")
		require.NoError(t, err)
		assert.Equal(t, ns1, ns2)

		// 不同域应该生成不同的命名空间
		ns3, err := nsManager.GetOrCreate("product", "entity")
		require.NoError(t, err)
		assert.NotEqual(t, ns1, ns3)
	})

	t.Run("验证命名空间存在性", func(t *testing.T) {
		nsManager, err := uuidInfra.NewNamespaceManager("com.9wings.erp")
		require.NoError(t, err)

		// 初始不存在
		assert.False(t, nsManager.Exists("user", "entity"))

		// 创建后存在
		_, err = nsManager.GetOrCreate("user", "entity")
		require.NoError(t, err)
		assert.True(t, nsManager.Exists("user", "entity"))
	})
}

func TestDomainStrategies(t *testing.T) {
	t.Run("用户策略测试", func(t *testing.T) {
		strategy := uuidInfra.NewUserStrategy()

		attrs := uuidTypes.DomainAttributes{
			TenantID:   "tenant001",
			EntityType: "entity",
			Key:        "user123",
			Metadata: map[string]interface{}{
				"username": "zhangsan",
				"email":    "<EMAIL>",
			},
		}

		// 验证属性
		err := strategy.ValidateAttributes(attrs)
		assert.NoError(t, err)

		// 生成语义化名称
		name, err := strategy.GenerateSemanticName(attrs)
		require.NoError(t, err)
		assert.Contains(t, name, "zhangsan")
		assert.Contains(t, name, "tenant001")
		assert.Contains(t, name, "user123")
	})

	t.Run("商品策略测试", func(t *testing.T) {
		strategy := uuidInfra.NewProductStrategy()

		attrs := uuidTypes.DomainAttributes{
			TenantID:   "tenant001",
			EntityType: "entity",
			Key:        "product123",
			Metadata: map[string]interface{}{
				"sku":      "PHONE001",
				"category": "electronics",
				"brand":    "apple",
			},
		}

		// 验证属性
		err := strategy.ValidateAttributes(attrs)
		assert.NoError(t, err)

		// 生成语义化名称
		name, err := strategy.GenerateSemanticName(attrs)
		require.NoError(t, err)
		assert.Contains(t, name, "PHONE001")
		assert.Contains(t, name, "electronics")
		assert.Contains(t, name, "apple")
	})

	t.Run("缺少必需属性应该失败", func(t *testing.T) {
		strategy := uuidInfra.NewUserStrategy()

		attrs := uuidTypes.DomainAttributes{
			TenantID:   "", // 缺少必需属性
			EntityType: "entity",
			Key:        "user123",
		}

		err := strategy.ValidateAttributes(attrs)
		assert.Error(t, err)
		assert.Contains(t, err.Error(), "tenant_id")
	})
}

func TestGenerator(t *testing.T) {
	config := uuidTypes.GeneratorConfig{
		RootNamespace: "com.9wings.erp",
		FallbackToV4:  true,
		EnableCache:   true,
		CacheSize:     1000,
		Strategies: map[string]uuidTypes.StrategyConfig{
			"test": {
				RequiredAttrs:  []string{"tenant_id", "key"},
				OptionalAttrs:  []string{"name"},
				AttributeOrder: []string{"name", "tenant_id", "key"},
			},
		},
	}

	t.Run("创建生成器", func(t *testing.T) {
		generator, err := uuidInfra.NewGenerator(config)
		require.NoError(t, err)
		assert.NotNil(t, generator)
	})

	t.Run("生成UUID的确定性", func(t *testing.T) {
		generator, err := uuidInfra.NewGenerator(config)
		require.NoError(t, err)

		attrs := uuidTypes.DomainAttributes{
			TenantID:   "tenant001",
			EntityType: "entity",
			Key:        "test123",
			Metadata: map[string]interface{}{
				"username": "zhangsan",
			},
		}

		// 生成第一个UUID
		uuid1, err := generator.Generate(context.Background(), "user", attrs)
		require.NoError(t, err)
		assert.NotEqual(t, uuid.Nil, uuid1)

		// 相同输入应该生成相同的UUID
		uuid2, err := generator.Generate(context.Background(), "user", attrs)
		require.NoError(t, err)
		assert.Equal(t, uuid1, uuid2)

		// 不同输入应该生成不同的UUID
		attrs.Key = "test456"
		uuid3, err := generator.Generate(context.Background(), "user", attrs)
		require.NoError(t, err)
		assert.NotEqual(t, uuid1, uuid3)
	})

	t.Run("降级到UUID v4", func(t *testing.T) {
		generator, err := uuidInfra.NewGenerator(config)
		require.NoError(t, err)

		// 使用无效属性触发错误
		attrs := uuidTypes.DomainAttributes{
			TenantID:   "", // 缺少必需属性
			EntityType: "entity",
			Key:        "test123",
		}

		// 正常生成应该失败
		_, err = generator.Generate(context.Background(), "user", attrs)
		assert.Error(t, err)

		// 带降级的生成应该成功
		fallbackUUID, err := generator.GenerateWithFallback(context.Background(), "user", attrs)
		require.NoError(t, err)
		assert.NotEqual(t, uuid.Nil, fallbackUUID)

		// 降级生成的UUID应该是随机的（v4）
		fallbackUUID2, err := generator.GenerateWithFallback(context.Background(), "user", attrs)
		require.NoError(t, err)
		assert.NotEqual(t, fallbackUUID, fallbackUUID2) // 每次生成的v4 UUID应该不同
	})
}

func TestManager(t *testing.T) {
	t.Run("创建管理器", func(t *testing.T) {
		manager, err := uuidInfra.NewManagerWithDefaults()
		require.NoError(t, err)
		assert.NotNil(t, manager)
		defer manager.Close()
	})

	t.Run("生成用户UUID", func(t *testing.T) {
		manager, err := uuidInfra.NewManagerWithDefaults()
		require.NoError(t, err)
		defer manager.Close()

		metadata := map[string]interface{}{
			"username": "zhangsan",
			"email":    "<EMAIL>",
		}

		userUUID, err := manager.GenerateForUser(context.Background(), "tenant001", "user123", metadata)
		require.NoError(t, err)
		assert.NotEqual(t, uuid.Nil, userUUID)
	})

	t.Run("生成商品UUID", func(t *testing.T) {
		manager, err := uuidInfra.NewManagerWithDefaults()
		require.NoError(t, err)
		defer manager.Close()

		metadata := map[string]interface{}{
			"sku":      "PHONE001",
			"category": "electronics",
			"brand":    "apple",
		}

		productUUID, err := manager.GenerateForProduct(context.Background(), "tenant001", "product123", metadata)
		require.NoError(t, err)
		assert.NotEqual(t, uuid.Nil, productUUID)
	})

	t.Run("获取支持的域", func(t *testing.T) {
		manager, err := uuidInfra.NewManagerWithDefaults()
		require.NoError(t, err)
		defer manager.Close()

		domains := manager.GetSupportedDomains()
		assert.Contains(t, domains, "user")
		assert.Contains(t, domains, "product")
		assert.Contains(t, domains, "order")
	})

	t.Run("获取统计信息", func(t *testing.T) {
		manager, err := uuidInfra.NewManagerWithDefaults()
		require.NoError(t, err)
		defer manager.Close()

		stats := manager.GetStats()
		assert.NotEmpty(t, stats)
		assert.Contains(t, stats, "registered_domains")
		assert.Contains(t, stats, "cache_enabled")
		assert.Contains(t, stats, "fallback_enabled")
	})
}

func TestGlobalManager(t *testing.T) {
	t.Run("全局管理器单例", func(t *testing.T) {
		manager1, err := uuidInfra.GetGlobalManager()
		require.NoError(t, err)

		manager2, err := uuidInfra.GetGlobalManager()
		require.NoError(t, err)

		// 应该是同一个实例
		assert.Same(t, manager1, manager2)
	})

	t.Run("便捷函数", func(t *testing.T) {
		metadata := map[string]interface{}{
			"username": "testuser",
		}

		userUUID, err := uuidInfra.GenerateUserUUID(context.Background(), "tenant001", "user123", metadata)
		require.NoError(t, err)
		assert.NotEqual(t, uuid.Nil, userUUID)

		// 相同输入应该生成相同的UUID
		userUUID2, err := uuidInfra.GenerateUserUUID(context.Background(), "tenant001", "user123", metadata)
		require.NoError(t, err)
		assert.Equal(t, userUUID, userUUID2)
	})
}

// func TestConfigValidation(t *testing.T) {
// 	t.Run("有效配置", func(t *testing.T) {
// 		config := uuidTypes.GeneratorConfig{
// 			RootNamespace: "com.test",
// 			FallbackToV4:  true,
// 			EnableCache:   true,
// 			CacheSize:     1000,
// 			Strategies: map[string]uuidTypes.StrategyConfig{
// 				"test": {
// 					RequiredAttrs: []string{"tenant_id"},
// 				},
// 			},
// 		}

// 		err := uuidInfra.validateConfig(config)
// 		assert.NoError(t, err)
// 	})

// 	t.Run("缺少根命名空间", func(t *testing.T) {
// 		config := uuidTypes.GeneratorConfig{
// 			RootNamespace: "", // 无效
// 		}

// 		err := uuidInfra.validateConfig(config)
// 		assert.Error(t, err)
// 		assert.Contains(t, err.Error(), "root_namespace is required")
// 	})

// 	t.Run("缓存大小无效", func(t *testing.T) {
// 		config := uuidTypes.GeneratorConfig{
// 			RootNamespace: "com.test",
// 			EnableCache:   true,
// 			CacheSize:     0, // 无效
// 		}

// 		err := uuidInfra.validateConfig(config)
// 		assert.Error(t, err)
// 		assert.Contains(t, err.Error(), "cache_size must be positive")
// 	})
// }

// 基准测试
func BenchmarkUUIDGeneration(b *testing.B) {
	manager, err := uuidInfra.NewManagerWithDefaults()
	if err != nil {
		b.Fatal(err)
	}
	defer manager.Close()

	metadata := map[string]interface{}{
		"username": "benchuser",
	}

	b.ResetTimer()
	b.RunParallel(func(pb *testing.PB) {
		i := 0
		for pb.Next() {
			_, err := manager.GenerateForUser(
				context.Background(),
				"tenant001",
				fmt.Sprintf("user%d", i),
				metadata,
			)
			if err != nil {
				b.Fatal(err)
			}
			i++
		}
	})
}

func BenchmarkNamespaceCache(b *testing.B) {
	nsManager, err := uuidInfra.NewNamespaceManager("com.9wings.erp")
	if err != nil {
		b.Fatal(err)
	}

	// 预填充一些命名空间
	for i := 0; i < 100; i++ {
		nsManager.GetOrCreate(fmt.Sprintf("domain%d", i), "entity")
	}

	b.ResetTimer()
	b.RunParallel(func(pb *testing.PB) {
		i := 0
		for pb.Next() {
			nsManager.GetOrCreate(fmt.Sprintf("domain%d", i%100), "entity")
			i++
		}
	})
}
