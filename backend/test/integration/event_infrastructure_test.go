package integration

import (
	"context"
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"backend/internal/application/event/handler"
	"backend/internal/domain/event"
	"backend/pkg/event/bus"
	"backend/pkg/infrastructure/logger"
)

// TestEventInfrastructure 测试事件基础设施
func TestEventInfrastructure(t *testing.T) {
	// 创建测试依赖
	config := &logger.Config{
		Level:  "debug",
		Format: "console",
		Output: "stdout",
	}
	mockLogger, err := logger.NewZapLogger(config)
	require.NoError(t, err)

	t.Run("领域事件创建和序列化", func(t *testing.T) {
		// 创建用户创建事件
		eventData := &event.UserCreatedEventData{
			UserID:    "user-123",
			TenantID:  "tenant-456",
			Username:  "testuser",
			Email:     "<EMAIL>",
			Phone:     "1234567890",
			FirstName: "Test",
			LastName:  "User",
			Status:    "active",
			CreatedAt: time.Now(),
		}

		userCreatedEvent := event.NewUserCreatedEvent("user-123", eventData)

		// 验证事件属性
		assert.Equal(t, "user-123", userCreatedEvent.AggregateID())
		assert.Equal(t, event.UserAggregateType, userCreatedEvent.AggregateType())
		assert.Equal(t, event.UserCreatedEventType, userCreatedEvent.EventType())
		assert.Equal(t, 1, userCreatedEvent.EventVersion())
		assert.NotEmpty(t, userCreatedEvent.EventID())
		assert.NotZero(t, userCreatedEvent.OccurredAt())

		// 验证事件数据
		retrievedData, ok := userCreatedEvent.EventData().(*event.UserCreatedEventData)
		require.True(t, ok)
		assert.Equal(t, eventData.UserID, retrievedData.UserID)
		assert.Equal(t, eventData.Username, retrievedData.Username)
		assert.Equal(t, eventData.Email, retrievedData.Email)
	})

	t.Run("事件注册表功能", func(t *testing.T) {
		registry := event.NewEventRegistry()

		// 注册事件类型
		registry.Register("test.event", func() event.DomainEvent {
			return &event.UserCreatedEvent{}
		})

		// 验证注册
		registeredTypes := registry.GetRegisteredTypes()
		assert.Contains(t, registeredTypes, "test.event")

		// 创建事件实例
		evt, exists := registry.Create("test.event")
		assert.True(t, exists)
		assert.NotNil(t, evt)

		// 测试不存在的事件类型
		_, exists = registry.Create("non.existent")
		assert.False(t, exists)
	})

	t.Run("事件处理器基本功能", func(t *testing.T) {
		// 创建事件处理器
		userHandler := handler.NewUserEventHandler(mockLogger)

		// 验证处理器属性
		assert.Equal(t, "UserEventHandler", userHandler.GetHandlerName())
		assert.True(t, userHandler.CanHandle(event.UserCreatedEventType))
		assert.True(t, userHandler.CanHandle(event.UserActivatedEventType))
		assert.False(t, userHandler.CanHandle("unknown.event"))

		// 创建测试事件
		eventData := &event.UserCreatedEventData{
			UserID:    "user-123",
			TenantID:  "tenant-456",
			Username:  "testuser",
			Email:     "<EMAIL>",
			Phone:     "1234567890",
			FirstName: "Test",
			LastName:  "User",
			Status:    "active",
			CreatedAt: time.Now(),
		}

		userCreatedEvent := event.NewUserCreatedEvent("user-123", eventData)

		// 处理事件
		ctx := context.Background()
		err := userHandler.Handle(ctx, userCreatedEvent)
		assert.NoError(t, err)
	})

	t.Run("投影处理器基本功能", func(t *testing.T) {
		// 创建投影处理器
		projectionHandler := handler.NewUserProjectionHandler(mockLogger)

		// 验证处理器属性
		assert.Equal(t, "UserProjectionHandler", projectionHandler.GetHandlerName())
		assert.True(t, projectionHandler.CanHandle(event.UserCreatedEventType))
		assert.True(t, projectionHandler.CanHandle(event.UserActivatedEventType))
		assert.False(t, projectionHandler.CanHandle("unknown.event"))

		// 创建事件
		eventData := &event.UserCreatedEventData{
			UserID:    "user-123",
			TenantID:  "tenant-456",
			Username:  "testuser",
			Email:     "<EMAIL>",
			Phone:     "1234567890",
			FirstName: "Test",
			LastName:  "User",
			Status:    "active",
			CreatedAt: time.Now(),
		}

		userCreatedEvent := event.NewUserCreatedEvent("user-123", eventData)

		// 处理事件
		ctx := context.Background()
		err := projectionHandler.Handle(ctx, userCreatedEvent)
		assert.NoError(t, err)
	})

}

// TestEventBusObserver 测试事件总线观察者
type TestEventBusObserver struct {
	EventPublishedCount      int
	EventProcessedCount      int
	EventFailedCount         int
	SubscriptionAddedCount   int
	SubscriptionRemovedCount int
}

func (o *TestEventBusObserver) OnEventPublished(ctx context.Context, event event.DomainEvent) {
	o.EventPublishedCount++
}

func (o *TestEventBusObserver) OnEventProcessed(ctx context.Context, result *bus.EventProcessingResult) {
	o.EventProcessedCount++
}

func (o *TestEventBusObserver) OnEventFailed(ctx context.Context, event event.DomainEvent, err error) {
	o.EventFailedCount++
}

func (o *TestEventBusObserver) OnSubscriptionAdded(eventType string, handler bus.EventHandler) {
	o.SubscriptionAddedCount++
}

func (o *TestEventBusObserver) OnSubscriptionRemoved(eventType string, handler bus.EventHandler) {
	o.SubscriptionRemovedCount++
}
