package integration

import (
	"bytes"
	"encoding/json"
	"fmt"
	"log"
	"net/http"
	"net/http/httptest"
	"os"
	"path/filepath"
	"runtime"
	"testing"
	"time"

	authEntity "backend/internal/domain/auth/entity"
	tenantEntity "backend/internal/domain/tenant/entity"
	userEntity "backend/internal/domain/user/entity"
	"backend/internal/shared/di/container"
	"backend/internal/shared/di/injector"
	"backend/internal/shared/types"
	"backend/pkg/common/response"
	"backend/test/testutil"

	"github.com/gin-gonic/gin"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
	"github.com/stretchr/testify/suite"
	"gorm.io/gorm"
)

// AuthIntegrationTestSuite 认证集成测试套件
type AuthIntegrationTestSuite struct {
	suite.Suite
	router   *gin.Engine
	db       *gorm.DB
	app      *container.App
	cleanup  func()
	testData *TestDataTracker // 🔥 新增：跟踪测试数据
}

// TestDataTracker 跟踪测试过程中创建的数据，便于精确清理
type TestDataTracker struct {
	UserIDs       []string
	TenantIDs     []string
	UserAuthIDs   []string
	RoleIDs       []string
	UserTenantIDs []string
}

// NewTestDataTracker 创建新的测试数据跟踪器
func NewTestDataTracker() *TestDataTracker {
	return &TestDataTracker{
		UserIDs:       make([]string, 0),
		TenantIDs:     make([]string, 0),
		UserAuthIDs:   make([]string, 0),
		RoleIDs:       make([]string, 0),
		UserTenantIDs: make([]string, 0),
	}
}

// TrackUser 跟踪创建的用户
func (t *TestDataTracker) TrackUser(userID string) {
	t.UserIDs = append(t.UserIDs, userID)
}

// TrackTenant 跟踪创建的租户
func (t *TestDataTracker) TrackTenant(tenantID string) {
	t.TenantIDs = append(t.TenantIDs, tenantID)
}

// TrackUserAuth 跟踪创建的用户认证
func (t *TestDataTracker) TrackUserAuth(userAuthID string) {
	t.UserAuthIDs = append(t.UserAuthIDs, userAuthID)
}

// TrackRole 跟踪创建的角色
func (t *TestDataTracker) TrackRole(roleID string) {
	t.RoleIDs = append(t.RoleIDs, roleID)
}

// TrackUserTenant 跟踪创建的用户-租户关联
func (t *TestDataTracker) TrackUserTenant(userTenantID string) {
	t.UserTenantIDs = append(t.UserTenantIDs, userTenantID)
}

// CleanupTestData 清理跟踪的测试数据
func (t *TestDataTracker) CleanupTestData(db *gorm.DB) error {
	// 按照依赖关系的逆序删除数据

	// 1. 删除用户-租户关联（依赖用户和租户）
	if len(t.UserTenantIDs) > 0 {
		if err := db.Where("business_id IN ?", t.UserTenantIDs).Delete(&userEntity.UserTenant{}).Error; err != nil {
			return fmt.Errorf("删除用户租户关联失败: %w", err)
		}
	}

	// 2. 删除用户认证（依赖用户）
	if len(t.UserAuthIDs) > 0 {
		if err := db.Where("business_id IN ?", t.UserAuthIDs).Delete(&authEntity.UserAuth{}).Error; err != nil {
			return fmt.Errorf("删除用户认证失败: %w", err)
		}
	}

	// 3. 删除角色（依赖租户）
	if len(t.RoleIDs) > 0 {
		if err := db.Where("business_id IN ?", t.RoleIDs).Delete(&userEntity.Role{}).Error; err != nil {
			return fmt.Errorf("删除角色失败: %w", err)
		}
	}

	// 4. 删除用户
	if len(t.UserIDs) > 0 {
		if err := db.Where("business_id IN ?", t.UserIDs).Delete(&userEntity.User{}).Error; err != nil {
			return fmt.Errorf("删除用户失败: %w", err)
		}
	}

	// 5. 删除租户
	if len(t.TenantIDs) > 0 {
		if err := db.Where("business_id IN ?", t.TenantIDs).Delete(&tenantEntity.Tenant{}).Error; err != nil {
			return fmt.Errorf("删除租户失败: %w", err)
		}
	}

	return nil
}

// SetupSuite will be executed once before the testing suite is executed
func (suite *AuthIntegrationTestSuite) SetupSuite() {
	gin.SetMode(gin.TestMode)

	// 设置测试环境变量
	os.Setenv("DATABASE_DATABASE", "erp_test")
	os.Setenv("LOGGER_LEVEL", "error")

	// 找到项目根目录
	_, b, _, _ := runtime.Caller(0)
	basepath := filepath.Dir(b)
	// 从 test/integration 返回到项目根目录
	root := filepath.Join(basepath, "..", "..")

	// 为了解决 Casbin model 路径问题，我们需要先加载配置，修改它，然后再初始化应用
	// 这不是一个理想的模式，但对于测试是有效的
	os.Setenv("SECURITY_CASBIN_MODEL_PATH", filepath.Join(root, "configs/casbin_model.conf"))

	// 切换到项目根目录，确保相对路径正确
	originalDir, _ := os.Getwd()
	os.Chdir(root)
	defer os.Chdir(originalDir)

	// 初始化应用
	var err error
	configPath := filepath.Join(root, "configs")
	suite.app, suite.cleanup, err = injector.InitializeApp(configPath)
	require.NoError(suite.T(), err, "应用初始化失败")

	// 设置路由
	suite.app.Router.SetupRoutes()

	suite.router = suite.app.Router.GetEngine()
	suite.db = suite.app.DB

	// 跳过数据库迁移 - 假设迁移已经被应用
	// 在集成测试环境中，我们假设数据库结构已经是最新的
	// migrationsDir := filepath.Join(root, "migrations", "postgres")
	// dbFactory := postgres.NewFactory(&suite.app.Config.Database, migrationsDir)
	// conn, err := dbFactory.CreateConnection()
	// require.NoError(suite.T(), err, "无法创建数据库连接")
	//
	// migrator, err := dbFactory.CreateMigrator(conn)
	// require.NoError(suite.T(), err, "无法创建迁移器")
	//
	// err = migrator.ApplyMigrations()
	// require.NoError(suite.T(), err, "数据库迁移失败")
}

// TearDownSuite will be executed after all tests in the suite have been run
func (suite *AuthIntegrationTestSuite) TearDownSuite() {
	if suite.cleanup != nil {
		suite.cleanup()
	}
}

// SetupTest will be executed before each test in the suite
func (suite *AuthIntegrationTestSuite) SetupTest() {
	// 🔥 修改：为每个测试创建新的数据跟踪器
	suite.testData = NewTestDataTracker()
}

// TearDownTest will be executed after each test in the suite
func (suite *AuthIntegrationTestSuite) TearDownTest() {
	// 🔥 修改：只清理当前测试创建的数据
	if suite.testData != nil {
		err := suite.testData.CleanupTestData(suite.db)
		if err != nil {
			suite.T().Logf("清理测试数据时出现警告: %v", err)
		}
	}
}

func (suite *AuthIntegrationTestSuite) TestRegistration() {
	suite.Run("Successful Registration", func() {
		// 🔥 使用时间戳确保唯一性
		timestamp := time.Now().UnixNano()

		// 准备租户
		tenant, err := tenantEntity.NewTenant(fmt.Sprintf("Test Tenant %d", timestamp), fmt.Sprintf("test%d-domain.com", timestamp), "test", "test")
		require.NoError(suite.T(), err)
		require.NoError(suite.T(), suite.db.Create(tenant).Error)
		suite.testData.TrackTenant(tenant.BusinessID) // 🔥 跟踪创建的租户

		body, _ := json.Marshal(map[string]interface{}{
			"tenant_id": tenant.BusinessID,
			"username":  fmt.Sprintf("newuser_%d", timestamp),
			"email":     fmt.Sprintf("<EMAIL>", timestamp),
			"phone":     fmt.Sprintf("139%08d", timestamp%100000000),
			"password":  "password123",
		})
		req := httptest.NewRequest(http.MethodPost, "/auth/register", bytes.NewBuffer(body))
		req.Header.Set("Content-Type", "application/json")
		w := httptest.NewRecorder()
		suite.router.ServeHTTP(w, req)

		// 检查响应
		require.Equal(suite.T(), http.StatusCreated, w.Code, "Expected status code 201 for successful registration")
		var resp response.APIResponse
		err = json.Unmarshal(w.Body.Bytes(), &resp)
		require.NoError(suite.T(), err)
		assert.Equal(suite.T(), 0, resp.Code)
		data, ok := resp.Data.(map[string]interface{})
		require.True(suite.T(), ok)
		assert.NotEmpty(suite.T(), data["user_id"])
		assert.Equal(suite.T(), fmt.Sprintf("<EMAIL>", timestamp), data["username"]) // Register use case uses email as username

		// 🔥 跟踪注册过程中创建的用户（从响应中获取用户ID）
		if userID, ok := data["user_id"].(string); ok {
			suite.testData.TrackUser(userID)
		}
	})

	suite.Run("Email Already Exists", func() {
		// 🔥 使用时间戳确保唯一性
		timestamp := time.Now().UnixNano()
		existingEmail := fmt.Sprintf("<EMAIL>", timestamp)

		// 准备一个已存在的用户
		existingUser := testutil.UserWithEmail(existingEmail)
		// 🔥 修复：使用不同的手机号避免冲突
		existingUser.Phone = fmt.Sprintf("137%08d", timestamp%100000000)
		require.NoError(suite.T(), suite.db.Create(existingUser).Error)
		suite.testData.TrackUser(existingUser.BusinessID) // 🔥 跟踪创建的用户

		// 准备租户
		tenant, err := tenantEntity.NewTenant(fmt.Sprintf("Another Test Tenant %d", timestamp), fmt.Sprintf("another%d.test.domain", timestamp), "test", "test")
		require.NoError(suite.T(), err)
		require.NoError(suite.T(), suite.db.Create(tenant).Error)
		suite.testData.TrackTenant(tenant.BusinessID) // 🔥 跟踪创建的租户

		body, _ := json.Marshal(map[string]interface{}{
			"tenant_id": tenant.BusinessID,
			"username":  fmt.Sprintf("anotheruser_%d", timestamp),
			"email":     existingEmail,                               // Existing email
			"phone":     fmt.Sprintf("136%08d", timestamp%100000000), // 🔥 使用不同的手机号
			"password":  "password123",
		})
		req := httptest.NewRequest(http.MethodPost, "/auth/register", bytes.NewBuffer(body))
		req.Header.Set("Content-Type", "application/json")
		w := httptest.NewRecorder()
		suite.router.ServeHTTP(w, req)

		assert.Equal(suite.T(), http.StatusConflict, w.Code)
		var resp response.APIResponse
		err = json.Unmarshal(w.Body.Bytes(), &resp)
		require.NoError(suite.T(), err)
		assert.Equal(suite.T(), 3002, resp.Code) // CodeUserExists - 用户已存在
	})
}

func (suite *AuthIntegrationTestSuite) TestLoginFlow() {
	// --- 在测试流程开始前，准备通用数据 ---
	// 🔥 使用时间戳确保唯一性
	timestamp := time.Now().UnixNano()

	// 1. 创建用户
	testUser := testutil.NewUserBuilder().
		WithUsername(fmt.Sprintf("testuser_%d", timestamp)).
		WithEmail(fmt.Sprintf("<EMAIL>", timestamp)).
		WithPhone(fmt.Sprintf("138%08d", timestamp%100000000)).
		Build()
	// 🔥 手动生成雪花ID
	testUser.ID = timestamp + 1
	require.NoError(suite.T(), suite.db.Create(testUser).Error, "创建测试用户失败")
	suite.testData.TrackUser(testUser.BusinessID) // 🔥 跟踪创建的用户

	// 2. 为用户创建认证信息（密码）
	password := "password123"
	// 直接传递原始密码，NewUserAuth会在内部进行哈希
	userAuth, err := authEntity.NewUserAuth(testUser.BusinessID, "username", testUser.Username, password)
	require.NoError(suite.T(), err)
	// 🔥 手动生成雪花ID
	userAuth.ID = timestamp + 2
	require.NoError(suite.T(), suite.db.Create(userAuth).Error, "创建用户认证失败")
	suite.testData.TrackUserAuth(userAuth.BusinessID) // 🔥 跟踪创建的用户认证

	// 3. 创建租户和角色
	testTenant, err := tenantEntity.NewTenant(fmt.Sprintf("Login Test Tenant %d", timestamp), fmt.Sprintf("login%d.test.com", timestamp), "test", "test")
	require.NoError(suite.T(), err)
	// 🔥 手动生成雪花ID
	testTenant.ID = timestamp + 3
	require.NoError(suite.T(), suite.db.Create(testTenant).Error, "创建测试租户失败")
	suite.testData.TrackTenant(testTenant.BusinessID) // 🔥 跟踪创建的租户

	testRole := &userEntity.Role{
		TenantScopedEntity: types.TenantScopedEntity{
			TenantID: testTenant.BusinessID,
		},
		Name:        "admin",
		DisplayName: "Administrator",
	}
	// 🔥 手动生成雪花ID，避免主键冲突
	testRole.ID = timestamp + 4
	require.NoError(suite.T(), suite.db.Create(testRole).Error, "创建角色失败")
	suite.testData.TrackRole(testRole.BusinessID) // 🔥 跟踪创建的角色

	// 4. 将用户关联到租户
	userTenant := userEntity.NewUserTenant(testUser.BusinessID, testTenant.BusinessID, testRole.BusinessID)
	// 🔥 手动生成雪花ID
	userTenant.ID = timestamp + 5
	require.NoError(suite.T(), suite.db.Create(userTenant).Error, "关联用户与租户失败")
	suite.testData.TrackUserTenant(userTenant.BusinessID) // 🔥 跟踪创建的用户租户关联

	// --- 测试流程开始 ---
	var loginResponse map[string]interface{}
	suite.Run("Stage 1: Successful Login", func() {
		body, _ := json.Marshal(map[string]interface{}{
			"identity_type": "username",
			"identifier":    testUser.Username, // 🔥 使用动态生成的用户名
			"credential":    "password123",     // 🔥 修复：使用正确的密码
		})
		req := httptest.NewRequest(http.MethodPost, "/auth/login", bytes.NewBuffer(body))
		req.Header.Set("Content-Type", "application/json")
		w := httptest.NewRecorder()
		suite.router.ServeHTTP(w, req)

		log.Printf("Login Response: %s", w.Body.String()) // 调试日志

		require.Equal(suite.T(), http.StatusOK, w.Code)
		var resp response.APIResponse
		err := json.Unmarshal(w.Body.Bytes(), &resp)
		require.NoError(suite.T(), err)
		require.Equal(suite.T(), 0, resp.Code)

		// 将响应数据解析到 LoginResponse 结构体中
		respDataBytes, _ := json.Marshal(resp.Data)
		err = json.Unmarshal(respDataBytes, &loginResponse)
		require.NoError(suite.T(), err)

		assert.Equal(suite.T(), testUser.BusinessID, loginResponse["user_id"])
		tenants, ok := loginResponse["tenants"].([]interface{})
		require.True(suite.T(), ok)
		require.Len(suite.T(), tenants, 1)
		firstTenant, ok := tenants[0].(map[string]interface{})
		require.True(suite.T(), ok)
		assert.Equal(suite.T(), testTenant.BusinessID, firstTenant["tenant_id"])
		assert.NotEmpty(suite.T(), loginResponse["pre_auth_token"])
	})

	suite.Run("Stage 2: Successful Tenant Selection", func() {
		require.NotEmpty(suite.T(), loginResponse["pre_auth_token"], "PreAuthToken should not be empty after login")
		body, _ := json.Marshal(map[string]interface{}{"tenant_id": testTenant.BusinessID})
		req := httptest.NewRequest(http.MethodPost, "/auth/select-tenant", bytes.NewBuffer(body))
		req.Header.Set("Content-Type", "application/json")
		req.Header.Set("Authorization", "Bearer "+loginResponse["pre_auth_token"].(string))

		w := httptest.NewRecorder()
		suite.router.ServeHTTP(w, req)

		log.Printf("Select Tenant Response: %s", w.Body.String()) // 调试日志
		require.Equal(suite.T(), http.StatusOK, w.Code)
		var resp response.APIResponse
		err := json.Unmarshal(w.Body.Bytes(), &resp)
		require.NoError(suite.T(), err)
		require.Equal(suite.T(), 0, resp.Code)
		data, ok := resp.Data.(map[string]interface{})
		require.True(suite.T(), ok)
		assert.NotEmpty(suite.T(), data["access_token"])
	})

	suite.Run("Stage 1: Login Failure", func() {
		body, _ := json.Marshal(map[string]interface{}{
			"identity_type": "username",
			"identifier":    testUser.Username, // 🔥 使用动态生成的用户名
			"credential":    "wrongpassword",
		})
		req := httptest.NewRequest(http.MethodPost, "/auth/login", bytes.NewBuffer(body))
		req.Header.Set("Content-Type", "application/json")
		w := httptest.NewRecorder()
		suite.router.ServeHTTP(w, req)

		assert.Equal(suite.T(), http.StatusUnauthorized, w.Code)
	})
}

// TestAuthIntegrationTestSuite runs the entire test suite.
func TestAuthIntegrationTestSuite(t *testing.T) {
	suite.Run(t, new(AuthIntegrationTestSuite))
}
