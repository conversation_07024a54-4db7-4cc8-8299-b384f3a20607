package integration

import (
	"bytes"
	"encoding/json"
	"flag"
	"net/http"
	"net/http/httptest"
	"testing"

	"backend/internal/application/dto"
	"backend/internal/shared/di/injector"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
)

// TestTenantIntegration 租户管理集成测试
func TestTenantIntegration(t *testing.T) {
	// 跳过集成测试（需要数据库连接）
	if testing.Short() {
		t.Skip("跳过集成测试")
	}

	// 初始化应用
	configPath := flag.String("config", "../../configs", "path to config directory")
	flag.Parse()
	app, cleanup, err := injector.InitializeApp(*configPath)
	require.NoError(t, err)
	defer cleanup()

	// 设置路由
	router := app.Router
	router.SetupRoutes()

	t.Run("租户API路由可访问性测试", func(t *testing.T) {
		// 测试未认证访问租户列表API
		req := httptest.NewRequest(http.MethodGet, "/tenants", nil)
		w := httptest.NewRecorder()

		router.GetEngine().ServeHTTP(w, req)

		// 应该返回401未认证错误
		assert.Equal(t, http.StatusUnauthorized, w.Code)

		var response map[string]interface{}
		err := json.Unmarshal(w.Body.Bytes(), &response)
		require.NoError(t, err)

		assert.Equal(t, float64(401), response["code"])
		assert.Contains(t, response["message"], "认证")
	})

	t.Run("租户创建API路由测试", func(t *testing.T) {
		// 准备创建租户请求
		createReq := dto.CreateTenantRequestDTO{
			Name:         "测试租户",
			Domain:       "test-tenant",
			DisplayName:  "测试租户显示名称",
			Description:  "这是一个测试租户",
			Type:         "enterprise", // 企业版
			Industry:     "电商",
			Country:      "中国",
			Province:     "广东省",
			City:         "深圳市",
			ContactEmail: "<EMAIL>",
			ContactPhone: "13800138000",
		}

		reqBody, err := json.Marshal(createReq)
		require.NoError(t, err)

		req := httptest.NewRequest(http.MethodPost, "/tenants", bytes.NewBuffer(reqBody))
		req.Header.Set("Content-Type", "application/json")
		w := httptest.NewRecorder()

		router.GetEngine().ServeHTTP(w, req)

		// 应该返回401未认证错误（因为没有认证token）
		assert.Equal(t, http.StatusUnauthorized, w.Code)
	})

	t.Run("健康检查API测试", func(t *testing.T) {
		req := httptest.NewRequest(http.MethodGet, "/health", nil)
		w := httptest.NewRecorder()

		router.GetEngine().ServeHTTP(w, req)

		assert.Equal(t, http.StatusOK, w.Code)

		var response map[string]interface{}
		err := json.Unmarshal(w.Body.Bytes(), &response)
		require.NoError(t, err)

		assert.Equal(t, "ok", response["status"])
		assert.Equal(t, "nine-wings-erp", response["service"])
	})

	t.Run("订阅管理API路由测试", func(t *testing.T) {
		// 测试未认证访问订阅列表API
		req := httptest.NewRequest(http.MethodGet, "/subscriptions", nil)
		w := httptest.NewRecorder()

		router.GetEngine().ServeHTTP(w, req)

		// 应该返回401未认证错误
		assert.Equal(t, http.StatusUnauthorized, w.Code)
	})

	t.Run("Swagger文档可访问性测试", func(t *testing.T) {
		req := httptest.NewRequest(http.MethodGet, "/swagger/index.html", nil)
		w := httptest.NewRecorder()

		router.GetEngine().ServeHTTP(w, req)

		// Swagger UI应该可以访问
		assert.Equal(t, http.StatusOK, w.Code)
		assert.Contains(t, w.Body.String(), "Swagger UI")
	})
}

// TestTenantDatabaseIntegration 租户数据库集成测试
func TestTenantDatabaseIntegration(t *testing.T) {
	// 跳过需要数据库的测试
	if testing.Short() {
		t.Skip("跳过数据库集成测试")
	}

	// 这里可以添加实际的数据库操作测试
	// 需要配置测试数据库连接
	t.Log("数据库集成测试待实现")
}
