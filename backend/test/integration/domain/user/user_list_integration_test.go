package integration

import (
	"context"
	"os"
	"path/filepath"
	"runtime"
	"testing"
	"time"

	userRepoImpl "backend/internal/adapters/persistence/repository/relational/user"
	userUseCase "backend/internal/application/usecase/user"
	userEntity "backend/internal/domain/user/entity"
	userRepoInterface "backend/internal/domain/user/repository"
	"backend/internal/domain/user/valueobject"
	"backend/internal/shared/transaction"
	"backend/internal/shared/types"
	"backend/pkg/infrastructure/config"
	"backend/pkg/infrastructure/database/adapter"
	"backend/pkg/infrastructure/database/postgres"
	"backend/pkg/infrastructure/logger"
	"backend/pkg/infrastructure/snowflake"
	"backend/pkg/infrastructure/uuid"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
	"github.com/stretchr/testify/suite"
	"gorm.io/gorm"
)

// UserListIntegrationTestSuite 用户列表集成测试套件
type UserListIntegrationTestSuite struct {
	suite.Suite
	db                    *gorm.DB
	userRepo              userRepoInterface.UserRepository
	userTenantRepo        userRepoInterface.UserTenantRepository
	userManagementUseCase *userUseCase.UserManagementUseCase
	txManager             transaction.TransactionManager
	logger                logger.Logger
	testTenantBusinessID  string
	testUsers             []*userEntity.User
}

// SetupSuite 测试套件初始化
func (suite *UserListIntegrationTestSuite) SetupSuite() {
	// 获取项目根目录
	_, filename, _, _ := runtime.Caller(0)
	rootDir := filepath.Join(filepath.Dir(filename), "../../../..")
	configPath := filepath.Join(rootDir, "configs")

	// 设置环境变量
	os.Setenv("APP_ENV", "development")
	os.Setenv("ERP_DB_HOST", "localhost")

	// 加载配置
	loader := config.NewLoader(configPath)
	cfg, err := loader.Load()
	require.NoError(suite.T(), err)

	// 初始化数据库管理器
	dbManager := postgres.NewDatabaseManager(&cfg.Database)
	err = dbManager.Initialize()
	require.NoError(suite.T(), err)

	// 获取数据库连接
	suite.db = dbManager.GetConnection().GetDB()

	// 初始化日志器
	loggerConfig := &logger.Config{
		Level:      cfg.Logger.Level,
		Format:     cfg.Logger.Format,
		Output:     cfg.Logger.Output,
		File:       cfg.Logger.File,
		MaxSize:    cfg.Logger.MaxSize,
		MaxAge:     cfg.Logger.MaxAge,
		MaxBackups: cfg.Logger.MaxBackups,
		Compress:   cfg.Logger.Compress,
	}
	suite.logger, err = logger.NewZapLogger(loggerConfig)
	require.NoError(suite.T(), err)

	// 初始化事务管理器
	suite.txManager = adapter.NewTransactionManagerAdapter(dbManager.GetTransactionManager())

	// 初始化仓储
	suite.userRepo = userRepoImpl.NewRepository(dbManager)
	suite.userTenantRepo = userRepoImpl.NewUserTenantRepository(dbManager)

	// 初始化用例
	suite.userManagementUseCase = userUseCase.NewUserManagementUseCase(
		suite.txManager,
		suite.userRepo,
		suite.userTenantRepo,
		suite.logger,
	)

	// 设置测试租户ID
	suite.testTenantBusinessID = "test-tenant-" + time.Now().Format("20060102150405")
}

// SetupTest 每个测试前的设置
func (suite *UserListIntegrationTestSuite) SetupTest() {
	// 清理测试数据
	suite.cleanupTestData()

	// 创建测试用户
	suite.createTestUsers()
}

// TearDownTest 每个测试后的清理
func (suite *UserListIntegrationTestSuite) TearDownTest() {
	suite.cleanupTestData()
}

// TearDownSuite 测试套件清理
func (suite *UserListIntegrationTestSuite) TearDownSuite() {
	if suite.db != nil {
		sqlDB, _ := suite.db.DB()
		sqlDB.Close()
	}
}

// cleanupTestData 清理测试数据
func (suite *UserListIntegrationTestSuite) cleanupTestData() {
	// 删除测试用户租户关联
	suite.db.Where("tenant_business_id = ?", suite.testTenantBusinessID).Delete(&userEntity.UserTenant{})

	// 删除测试用户
	for _, user := range suite.testUsers {
		if user != nil {
			suite.db.Where("business_id = ?", user.BusinessID).Delete(&userEntity.User{})
		}
	}
	suite.testUsers = nil
}

// createTestUsers 创建测试用户
func (suite *UserListIntegrationTestSuite) createTestUsers() {
	// 初始化ID生成器
	snowflakeGen := snowflake.NewGenerator(1)

	uuidGen, err := uuid.NewManagerWithDefaults()
	require.NoError(suite.T(), err)

	// 创建测试用户数据
	testUserData := []struct {
		username string
		email    string
		status   userEntity.UserStatus
	}{
		{"alice", "<EMAIL>", userEntity.UserStatusActive},
		{"bob", "<EMAIL>", userEntity.UserStatusActive},
		{"charlie", "<EMAIL>", userEntity.UserStatusInactive},
		{"david", "<EMAIL>", userEntity.UserStatusActive},
		{"eve", "<EMAIL>", userEntity.UserStatusSuspended},
	}

	suite.testUsers = make([]*userEntity.User, len(testUserData))

	for i, userData := range testUserData {
		// 生成ID
		techID := snowflakeGen.Generate()

		businessUUID, err := uuidGen.GenerateForUser(context.Background(), suite.testTenantBusinessID, userData.username, map[string]interface{}{
			"username": userData.username,
			"email":    userData.email,
		})
		require.NoError(suite.T(), err)

		// 创建用户
		user := &userEntity.User{
			GlobalEntity: types.GlobalEntity{
				CoreEntity: types.CoreEntity{
					ID:         techID,
					BusinessID: businessUUID.String(),
					CreatedAt:  time.Now(),
					UpdatedAt:  time.Now(),
					Version:    1,
				},
			},
			Username: userData.username,
			Email:    userData.email,
			Phone:    "+1234567890" + string(rune(i)),
			Status:   userData.status,
			Profile: valueobject.UserProfile{
				FirstName: "Test",
				LastName:  "User" + string(rune(i)),
				Nickname:  userData.username,
			},
		}

		// 保存用户
		err = suite.userRepo.Save(context.Background(), user)
		require.NoError(suite.T(), err)

		suite.testUsers[i] = user

		// 创建用户租户关联
		userTenant := &userEntity.UserTenant{
			TenantScopedEntity: types.TenantScopedEntity{
				CoreEntity: types.CoreEntity{
					ID:         techID + int64(i+1000),
					BusinessID: businessUUID.String() + "-tenant",
					CreatedAt:  time.Now(),
					UpdatedAt:  time.Now(),
					Version:    1,
				},
				TenantID: suite.testTenantBusinessID,
			},
			UserBusinessID:   user.BusinessID,
			TenantBusinessID: suite.testTenantBusinessID,
			RoleBusinessID:   "role-user",
		}

		err = suite.userTenantRepo.Create(context.Background(), userTenant)
		require.NoError(suite.T(), err)
	}
}

// TestListUsers_BasicPagination 测试基本分页功能
func (suite *UserListIntegrationTestSuite) TestListUsers_BasicPagination() {
	req := &userUseCase.ListUsersRequest{
		TenantID: suite.testTenantBusinessID,
		Page:     1,
		PageSize: 3,
	}

	result, err := suite.userManagementUseCase.ListUsers(context.Background(), req)

	assert.NoError(suite.T(), err)
	assert.NotNil(suite.T(), result)
	assert.Equal(suite.T(), int64(5), result.Total)
	assert.Equal(suite.T(), 1, result.Page)
	assert.Equal(suite.T(), 3, result.PageSize)
	assert.Equal(suite.T(), 2, result.TotalPages) // 5/3 = 1.67 -> 2页
	assert.True(suite.T(), result.HasNext)
	assert.False(suite.T(), result.HasPrevious)
	assert.Len(suite.T(), result.Items, 3)
}

// TestListUsers_WithStatusFilter 测试状态筛选
func (suite *UserListIntegrationTestSuite) TestListUsers_WithStatusFilter() {
	req := &userUseCase.ListUsersRequest{
		TenantID: suite.testTenantBusinessID,
		Status:   "active",
		Page:     1,
		PageSize: 10,
	}

	result, err := suite.userManagementUseCase.ListUsers(context.Background(), req)

	assert.NoError(suite.T(), err)
	assert.NotNil(suite.T(), result)
	assert.Equal(suite.T(), int64(3), result.Total) // 3个active用户
	assert.Len(suite.T(), result.Items, 3)

	// 验证所有返回的用户都是active状态
	for _, item := range result.Items {
		assert.Equal(suite.T(), "active", item.Status)
	}
}

// TestListUsers_WithKeywordSearch 测试关键词搜索
func (suite *UserListIntegrationTestSuite) TestListUsers_WithKeywordSearch() {
	req := &userUseCase.ListUsersRequest{
		TenantID: suite.testTenantBusinessID,
		Keyword:  "alice",
		Page:     1,
		PageSize: 10,
	}

	result, err := suite.userManagementUseCase.ListUsers(context.Background(), req)

	assert.NoError(suite.T(), err)
	assert.NotNil(suite.T(), result)
	assert.Equal(suite.T(), int64(1), result.Total)
	assert.Len(suite.T(), result.Items, 1)
	assert.Equal(suite.T(), "alice", result.Items[0].Username)
}

// TestUserListIntegration 运行集成测试套件
func TestUserListIntegration(t *testing.T) {
	suite.Run(t, new(UserListIntegrationTestSuite))
}
