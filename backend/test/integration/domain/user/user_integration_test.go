package integration

import (
	"context"
	"fmt"
	"os"
	"path/filepath"
	"runtime"
	"testing"

	userRepoImpl "backend/internal/adapters/persistence/repository/relational/user"
	authEntity "backend/internal/domain/auth/entity"
	userEntity "backend/internal/domain/user/entity"
	"backend/internal/domain/user/valueobject"
	"backend/internal/shared/service/id"
	"backend/internal/shared/service/id/implementation"
	"backend/internal/shared/types"
	"backend/pkg/infrastructure/config"
	"backend/pkg/infrastructure/database/postgres"
	"backend/pkg/infrastructure/snowflake"
	"backend/pkg/infrastructure/uuid"
	uuidTypes "backend/pkg/types/uuid"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
	"github.com/stretchr/testify/suite"
	"gorm.io/gorm"
)

// UserIntegrationTestSuite 用户领域集成测试套件
type UserIntegrationTestSuite struct {
	suite.Suite
	db                   *gorm.DB
	userRepo             *userRepoImpl.Repository
	userTenantRepo       *userRepoImpl.UserTenantRepository
	idGenerator          id.UnifiedIDGenerator
	testTenantBusinessID string
	testRoleBusinessID   string
}

// SetupSuite 测试套件初始化
func (suite *UserIntegrationTestSuite) SetupSuite() {
	// 获取项目根目录
	_, filename, _, _ := runtime.Caller(0)
	rootDir := filepath.Join(filepath.Dir(filename), "../../../..")
	configPath := filepath.Join(rootDir, "configs")

	// 设置环境变量
	os.Setenv("APP_ENV", "development")
	os.Setenv("ERP_DB_HOST", "localhost")

	// 加载配置
	loader := config.NewLoader(configPath)
	cfg, err := loader.Load()
	require.NoError(suite.T(), err)

	validator := config.NewValidator()
	err = validator.ValidateConfig(cfg)
	require.NoError(suite.T(), err)

	// 初始化数据库连接
	dbManager := postgres.NewDatabaseManager(&cfg.Database)
	err = dbManager.Initialize()
	require.NoError(suite.T(), err)

	suite.db = dbManager.GetConnection().GetDB()

	// 创建仓储实例 - 使用类型断言
	suite.userRepo = userRepoImpl.NewRepository(dbManager).(*userRepoImpl.Repository)
	suite.userTenantRepo = userRepoImpl.NewUserTenantRepository(dbManager).(*userRepoImpl.UserTenantRepository)

	// 初始化ID生成器组件
	snowflakeGen := snowflake.NewGeneratorWithIP()
	uuidConfig := uuidTypes.GeneratorConfig{
		RootNamespace: "test-namespace",
		FallbackToV4:  true,
		EnableCache:   false,
	}
	uuidManager, err := uuid.NewManager(uuidConfig)
	require.NoError(suite.T(), err)

	idGeneratorConfig := &id.IDGeneratorConfig{
		SnowflakeConfig: &id.SnowflakeConfig{
			MachineID:  1,
			UseIPBased: false,
		},
		UUIDConfig: &id.UUIDConfig{
			RootNamespace:    "test-namespace",
			EnableSemantic:   true,
			FallbackToRandom: true,
		},
		SequenceConfig: &id.SequenceConfig{
			DefaultPrefix: "TEST",
			DefaultLength: 8,
			PadWithZeros:  true,
		},
		DefaultDomain: types.IDDomainGeneric,
		EnableCache:   false,
	}

	suite.idGenerator = implementation.NewUnifiedIDGenerator(snowflakeGen, uuidManager, idGeneratorConfig)

	// 生成测试用租户信息
	suite.testTenantBusinessID = "550e8400-e29b-41d4-a716-************" // 固定测试租户ID
	suite.testRoleBusinessID = "550e8400-e29b-41d4-a716-************"   // 固定测试角色ID
}

// SetupTest 每个测试方法前的清理
func (suite *UserIntegrationTestSuite) SetupTest() {
	suite.cleanupTestData()
}

// TearDownTest 每个测试方法后的清理
func (suite *UserIntegrationTestSuite) TearDownTest() {
	suite.cleanupTestData()
}

// cleanupTestData 清理测试数据
func (suite *UserIntegrationTestSuite) cleanupTestData() {
	ctx := context.Background()

	// 清理所有测试相关的用户-租户关联关系（包括测试租户的所有关联）
	suite.db.WithContext(ctx).Unscoped().Where("tenant_business_id = ?", suite.testTenantBusinessID).Delete(&userEntity.UserTenant{})

	// 查询所有测试相关的用户
	var testUsers []userEntity.User
	suite.db.WithContext(ctx).Unscoped().Where("username LIKE ? OR email LIKE ?", "%test%", "%test%").Find(&testUsers)

	// 硬删除所有测试相关的用户
	for _, user := range testUsers {
		// 硬删除所有该用户的关联关系
		suite.db.WithContext(ctx).Unscoped().Where("user_business_id = ?", user.BusinessID).Delete(&userEntity.UserTenant{})

		// 硬删除用户
		suite.db.WithContext(ctx).Unscoped().Where("business_id = ?", user.BusinessID).Delete(&userEntity.User{})
	}

	// 额外清理：删除所有测试相关的用户认证记录
	suite.db.WithContext(ctx).Unscoped().Where("identifier LIKE ?", "%test%").Delete(&authEntity.UserAuth{})

	// 清理用户-租户关联表中的测试数据（通过模式匹配）
	suite.db.WithContext(ctx).Unscoped().Where("user_business_id LIKE ? OR tenant_business_id LIKE ?", "%test%", "%550e8400%").Delete(&userEntity.UserTenant{})

	// 清理包含特定关键词的用户
	suite.db.WithContext(ctx).Unscoped().Where("username LIKE ? OR username LIKE ? OR username LIKE ?", "%relation%", "%multitenant%", "%domain%").Delete(&userEntity.User{})
}

// TestUserCreationWithIDGeneration 测试用户创建和ID生成集成
func (suite *UserIntegrationTestSuite) TestUserCreationWithIDGeneration() {
	suite.Run("创建用户成功-完整信息", func() {
		ctx := context.Background()

		// 生成用户的tech ID和business ID
		techID, err := suite.idGenerator.GenerateSnowflakeID(ctx, types.IDDomainUser)
		require.NoError(suite.T(), err)
		assert.Greater(suite.T(), techID, int64(0), "技术ID应该大于0")

		businessID, err := suite.idGenerator.GenerateBusinessUUID(ctx, types.IDDomainUser, suite.testTenantBusinessID,
			"testuser1", map[string]interface{}{
				"email": "<EMAIL>",
			})
		require.NoError(suite.T(), err)
		assert.NotEmpty(suite.T(), businessID, "业务ID不应为空")

		// 创建用户
		profile := valueobject.NewUserProfile("testuser1", "", "")
		user, err := userEntity.NewUser(*profile, "testuser1", "<EMAIL>", "***********")
		require.NoError(suite.T(), err)

		// 设置ID
		user.ID = techID
		user.BusinessID = businessID

		// 保存用户
		err = suite.userRepo.Save(ctx, user)
		require.NoError(suite.T(), err, "保存用户失败")

		// 验证用户已保存
		savedUser, err := suite.userRepo.FindByBusinessID(ctx, businessID)
		require.NoError(suite.T(), err)
		assert.Equal(suite.T(), techID, savedUser.ID)
		assert.Equal(suite.T(), businessID, savedUser.BusinessID)
		assert.Equal(suite.T(), "testuser1", savedUser.Username)
		assert.Equal(suite.T(), "<EMAIL>", savedUser.Email)
	})

	suite.Run("创建用户成功-最小信息", func() {
		ctx := context.Background()

		// 生成用户的tech ID和business ID
		techID, err := suite.idGenerator.GenerateSnowflakeID(ctx, types.IDDomainUser)
		require.NoError(suite.T(), err)

		businessID, err := suite.idGenerator.GenerateBusinessUUID(ctx, types.IDDomainUser, suite.testTenantBusinessID,
			"testuser2", map[string]interface{}{
				"email": "<EMAIL>",
			})
		require.NoError(suite.T(), err)

		// 创建用户
		profile := valueobject.NewUserProfile("testuser2", "", "")
		user, err := userEntity.NewUser(*profile, "testuser2", "<EMAIL>", "***********")
		require.NoError(suite.T(), err)

		// 设置ID
		user.ID = techID
		user.BusinessID = businessID

		// 保存用户
		err = suite.userRepo.Save(ctx, user)
		require.NoError(suite.T(), err, "保存用户失败")

		// 验证用户已保存
		savedUser, err := suite.userRepo.FindByBusinessID(ctx, businessID)
		require.NoError(suite.T(), err)
		assert.Equal(suite.T(), userEntity.UserStatusActive, savedUser.Status)
	})
}

// TestUserTenantRelationship 测试用户和租户的多对多关联关系
func (suite *UserIntegrationTestSuite) TestUserTenantRelationship() {
	suite.Run("创建用户-租户关联关系", func() {
		ctx := context.Background()

		// 1. 先创建一个用户
		techID, err := suite.idGenerator.GenerateSnowflakeID(ctx, types.IDDomainUser)
		require.NoError(suite.T(), err)

		userBusinessID, err := suite.idGenerator.GenerateBusinessUUID(ctx, types.IDDomainUser, suite.testTenantBusinessID,
			"relationuser-single", map[string]interface{}{
				"test": "relationship-single",
			})
		require.NoError(suite.T(), err)

		profile := valueobject.NewUserProfile("relationuser-single", "", "")
		user, err := userEntity.NewUser(*profile, "relationuser-single", "<EMAIL>", "***********")
		require.NoError(suite.T(), err)

		user.ID = techID
		user.BusinessID = userBusinessID

		err = suite.userRepo.Save(ctx, user)
		require.NoError(suite.T(), err)

		// 2. 创建用户-租户关联关系
		relationTechID, err := suite.idGenerator.GenerateSnowflakeID(ctx, types.IDDomainGeneric)
		require.NoError(suite.T(), err)

		relationBusinessID, err := suite.idGenerator.GenerateBusinessUUID(ctx, types.IDDomainGeneric, suite.testTenantBusinessID,
			"user-tenant-relation", map[string]interface{}{
				"user":   userBusinessID,
				"tenant": suite.testTenantBusinessID,
			})
		require.NoError(suite.T(), err)

		userTenant := userEntity.NewUserTenant(userBusinessID, suite.testTenantBusinessID, suite.testRoleBusinessID)
		userTenant.ID = relationTechID
		userTenant.BusinessID = relationBusinessID

		err = suite.userTenantRepo.Create(ctx, userTenant)
		require.NoError(suite.T(), err, "创建用户-租户关联失败")

		// 3. 验证关联关系
		// 通过用户BusinessID查找关联关系
		relations, err := suite.userTenantRepo.FindByUserBusinessID(ctx, userBusinessID)
		require.NoError(suite.T(), err)
		assert.Len(suite.T(), relations, 1)
		assert.Equal(suite.T(), suite.testTenantBusinessID, relations[0].TenantBusinessID)
		assert.Equal(suite.T(), suite.testRoleBusinessID, relations[0].RoleBusinessID)

		// 通过租户BusinessID查找关联关系
		tenantRelations, err := suite.userTenantRepo.FindByTenantBusinessID(ctx, suite.testTenantBusinessID)
		require.NoError(suite.T(), err)
		assert.Len(suite.T(), tenantRelations, 1)
		assert.Equal(suite.T(), userBusinessID, tenantRelations[0].UserBusinessID)

		// 通过用户和租户BusinessID查找特定关联关系
		specificRelation, err := suite.userTenantRepo.FindByUserAndTenant(ctx, userBusinessID, suite.testTenantBusinessID)
		require.NoError(suite.T(), err)
		assert.Equal(suite.T(), relationBusinessID, specificRelation.BusinessID)
	})

	suite.Run("一个用户关联多个租户", func() {
		ctx := context.Background()

		// 清理测试数据，确保干净的环境
		suite.cleanupTestData()

		// 1. 创建用户
		techID, err := suite.idGenerator.GenerateSnowflakeID(ctx, types.IDDomainUser)
		require.NoError(suite.T(), err)

		userBusinessID, err := suite.idGenerator.GenerateBusinessUUID(ctx, types.IDDomainUser, suite.testTenantBusinessID,
			"multitenantuser-unique", map[string]interface{}{
				"test": "multitenant",
			})
		require.NoError(suite.T(), err)

		profile := valueobject.NewUserProfile("multitenantuser-unique", "", "")
		user, err := userEntity.NewUser(*profile, "multitenantuser-unique", "<EMAIL>", "***********")
		require.NoError(suite.T(), err)

		user.ID = techID
		user.BusinessID = userBusinessID

		err = suite.userRepo.Save(ctx, user)
		require.NoError(suite.T(), err)

		// 2. 创建多个租户关联
		tenant2BusinessID := "550e8400-e29b-41d4-a716-************"
		tenant3BusinessID := "550e8400-e29b-41d4-a716-************"

		tenantIDs := []string{suite.testTenantBusinessID, tenant2BusinessID, tenant3BusinessID}

		for i, tenantID := range tenantIDs {
			relationTechID, err := suite.idGenerator.GenerateSnowflakeID(ctx, types.IDDomainGeneric)
			require.NoError(suite.T(), err)

			relationBusinessID, err := suite.idGenerator.GenerateBusinessUUID(ctx, types.IDDomainGeneric, tenantID,
				fmt.Sprintf("user-tenant-relation-%d", i), nil)
			require.NoError(suite.T(), err)

			userTenant := userEntity.NewUserTenant(userBusinessID, tenantID, suite.testRoleBusinessID)
			userTenant.ID = relationTechID
			userTenant.BusinessID = relationBusinessID

			err = suite.userTenantRepo.Create(ctx, userTenant)
			require.NoError(suite.T(), err)
		}

		// 3. 验证用户关联了3个租户
		userRelations, err := suite.userTenantRepo.FindByUserBusinessID(ctx, userBusinessID)
		require.NoError(suite.T(), err)
		assert.Len(suite.T(), userRelations, 3)

		// 验证每个租户都能找到该用户
		for _, tenantID := range tenantIDs {
			tenantRelations, err := suite.userTenantRepo.FindByTenantBusinessID(ctx, tenantID)
			require.NoError(suite.T(), err)
			assert.Len(suite.T(), tenantRelations, 1)
			assert.Equal(suite.T(), userBusinessID, tenantRelations[0].UserBusinessID)
		}
	})
}

// TestIDGeneratorConsistency 测试ID生成器的一致性
func (suite *UserIntegrationTestSuite) TestIDGeneratorConsistency() {
	ctx := context.Background()

	suite.Run("雪花ID唯一性测试", func() {
		idSet := make(map[int64]bool)
		for i := 0; i < 10; i++ {
			id, err := suite.idGenerator.GenerateSnowflakeID(ctx, types.IDDomainUser)
			require.NoError(suite.T(), err)
			assert.False(suite.T(), idSet[id], "雪花ID应该是唯一的")
			idSet[id] = true
		}
	})

	suite.Run("业务UUID唯一性测试", func() {
		uuidSet := make(map[string]bool)
		for i := 0; i < 10; i++ {
			uuid, err := suite.idGenerator.GenerateBusinessUUID(ctx, types.IDDomainUser, suite.testTenantBusinessID,
				fmt.Sprintf("testuser%d", i), map[string]interface{}{
					"index": i,
				})
			require.NoError(suite.T(), err)
			assert.False(suite.T(), uuidSet[uuid], "业务UUID应该是唯一的")
			uuidSet[uuid] = true
		}
	})

	suite.Run("ID验证功能测试", func() {
		// 生成有效的雪花ID并验证
		validSnowflakeID, err := suite.idGenerator.GenerateSnowflakeID(ctx, types.IDDomainUser)
		require.NoError(suite.T(), err)

		err = suite.idGenerator.ValidateID(ctx, fmt.Sprintf("%d", validSnowflakeID), types.IDTypeSnowflake, types.IDDomainUser)
		assert.NoError(suite.T(), err, "有效的雪花ID应该通过验证")

		// 生成有效的UUID并验证
		validUUID, err := suite.idGenerator.GenerateBusinessUUID(ctx, types.IDDomainUser, suite.testTenantBusinessID,
			"validatetest", nil)
		require.NoError(suite.T(), err)

		err = suite.idGenerator.ValidateID(ctx, validUUID, types.IDTypeSemanticUUID, types.IDDomainUser)
		assert.NoError(suite.T(), err, "有效的UUID应该通过验证")
	})
}

// TestUserDomainImplementation 测试用户领域的业务逻辑实现
func (suite *UserIntegrationTestSuite) TestUserDomainImplementation() {
	ctx := context.Background()

	// 创建测试用户
	techID, err := suite.idGenerator.GenerateSnowflakeID(ctx, types.IDDomainUser)
	require.NoError(suite.T(), err)

	businessID, err := suite.idGenerator.GenerateBusinessUUID(ctx, types.IDDomainUser, suite.testTenantBusinessID,
		"domainuser-unique", map[string]interface{}{
			"test": "domain-implementation",
		})
	require.NoError(suite.T(), err)

	profile := valueobject.NewUserProfile("Domain User", "John", "Doe")
	user, err := userEntity.NewUser(*profile, "domainuser-unique", "<EMAIL>", "***********")
	require.NoError(suite.T(), err)

	user.ID = techID
	user.BusinessID = businessID

	err = suite.userRepo.Save(ctx, user)
	require.NoError(suite.T(), err)

	// 测试用户状态变更
	err = user.Deactivate()
	assert.NoError(suite.T(), err)
	assert.Equal(suite.T(), userEntity.UserStatusInactive, user.Status)

	err = user.Activate()
	assert.NoError(suite.T(), err)
	assert.Equal(suite.T(), userEntity.UserStatusActive, user.Status)

	// 测试登录能力检查
	assert.True(suite.T(), user.CanLogin(), "激活状态的用户应该可以登录")

	// 测试资料更新
	newProfile := valueobject.NewUserProfile("Updated User", "Jane", "Smith")
	user.UpdateProfile(*newProfile)
	assert.Equal(suite.T(), "Updated User", user.Profile.Nickname)
	assert.Equal(suite.T(), "Jane", user.Profile.FirstName)
	assert.Equal(suite.T(), "Smith", user.Profile.LastName)

	// 更新到数据库
	err = suite.userRepo.Update(ctx, user)
	assert.NoError(suite.T(), err)

	// 验证更新
	updatedUser, err := suite.userRepo.FindByBusinessID(ctx, businessID)
	require.NoError(suite.T(), err)
	assert.Equal(suite.T(), "Updated User", updatedUser.Profile.Nickname)
	assert.Equal(suite.T(), userEntity.UserStatusActive, updatedUser.Status)
}

// 运行测试套件
func TestUserIntegrationTestSuite(t *testing.T) {
	suite.Run(t, new(UserIntegrationTestSuite))
}
