package integration

import (
	"net/http"
	"net/http/httptest"
	"strings"
	"testing"
	"time"

	"backend/pkg/infrastructure/monitoring"

	"github.com/gin-gonic/gin"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
)

func TestMetricsCollection(t *testing.T) {
	// 创建指标管理器
	metricsManager := monitoring.NewMetricsManager()

	// 设置应用信息
	metricsManager.SetAppInfo("test-version", "test", "test-service")

	// 记录一些测试指标
	metricsManager.RecordHTTPRequest("GET", "/api/test", "200", "test-tenant", 100*time.Millisecond, 1024, 2048)
	metricsManager.RecordDatabaseQuery("SELECT", "users", "success", "test-tenant", 50*time.Millisecond)
	metricsManager.RecordCacheOperation("user-cache", "get", "test-tenant", true, 5*time.Millisecond)
	metricsManager.RecordBusinessOperation("user_login", "success", "test-tenant", "test-user", 200*time.Millisecond)
	metricsManager.RecordError("validation", "invalid_input", "test-tenant")

	// 创建HTTP服务器来测试指标端点
	gin.SetMode(gin.TestMode)
	router := gin.New()
	router.GET("/metrics", func(c *gin.Context) {
		handler := metricsManager.GetHandler()
		handler.ServeHTTP(c.Writer, c.Request)
	})

	// 发送请求获取指标
	req, err := http.NewRequest("GET", "/metrics", nil)
	require.NoError(t, err)

	w := httptest.NewRecorder()
	router.ServeHTTP(w, req)

	// 验证响应
	assert.Equal(t, http.StatusOK, w.Code)

	body := w.Body.String()

	// 验证包含预期的指标
	assert.Contains(t, body, "http_requests_total")
	assert.Contains(t, body, "database_queries_total")
	assert.Contains(t, body, "cache_hits_total")
	assert.Contains(t, body, "business_operations_total")
	assert.Contains(t, body, "errors_total")
	assert.Contains(t, body, "app_info")

	// 验证指标值
	assert.Contains(t, body, `http_requests_total{endpoint="/api/test",method="GET",status_code="200",tenant_id="test-tenant"} 1`)
	assert.Contains(t, body, `database_queries_total{operation="SELECT",status="success",table="users",tenant_id="test-tenant"} 1`)
	assert.Contains(t, body, `cache_hits_total{cache_name="user-cache",operation="get",tenant_id="test-tenant"} 1`)
	assert.Contains(t, body, `business_operations_total{operation="user_login",status="success",tenant_id="test-tenant",user_id="test-user"} 1`)
	assert.Contains(t, body, `errors_total{error_code="invalid_input",error_type="validation",tenant_id="test-tenant"} 1`)
}

func TestMetricsMiddleware(t *testing.T) {
	// 创建指标管理器
	metricsManager := monitoring.NewMetricsManager()

	// 创建测试路由器
	gin.SetMode(gin.TestMode)
	router := gin.New()

	// 添加指标中间件
	router.Use(func(c *gin.Context) {
		start := time.Now()
		c.Next()
		duration := time.Since(start)

		metricsManager.RecordHTTPRequest(
			c.Request.Method,
			c.FullPath(),
			"200",
			"test-tenant",
			duration,
			c.Request.ContentLength,
			int64(c.Writer.Size()),
		)
	})

	// 添加测试端点
	router.GET("/test", func(c *gin.Context) {
		c.JSON(200, gin.H{"message": "test"})
	})

	router.GET("/metrics", func(c *gin.Context) {
		handler := metricsManager.GetHandler()
		handler.ServeHTTP(c.Writer, c.Request)
	})

	// 发送测试请求
	req, err := http.NewRequest("GET", "/test", nil)
	require.NoError(t, err)

	w := httptest.NewRecorder()
	router.ServeHTTP(w, req)

	assert.Equal(t, http.StatusOK, w.Code)

	// 获取指标
	req, err = http.NewRequest("GET", "/metrics", nil)
	require.NoError(t, err)

	w = httptest.NewRecorder()
	router.ServeHTTP(w, req)

	assert.Equal(t, http.StatusOK, w.Code)

	body := w.Body.String()

	// 验证HTTP请求指标被记录
	assert.Contains(t, body, "http_requests_total")
	assert.Contains(t, body, "http_request_duration_seconds")
}

func TestSystemMetricsCollector(t *testing.T) {
	// 创建指标管理器
	metricsManager := monitoring.NewMetricsManager()

	// 创建系统指标收集器
	systemCollector := monitoring.NewSystemMetricsCollector(metricsManager, nil)

	// 更新系统指标
	metricsManager.UpdateSystemMetrics(100, 1024*1024*100, 25.5) // 100 goroutines, 100MB memory, 25.5% CPU

	// 创建HTTP服务器来测试指标端点
	gin.SetMode(gin.TestMode)
	router := gin.New()
	router.GET("/metrics", func(c *gin.Context) {
		handler := metricsManager.GetHandler()
		handler.ServeHTTP(c.Writer, c.Request)
	})

	// 获取指标
	req, err := http.NewRequest("GET", "/metrics", nil)
	require.NoError(t, err)

	w := httptest.NewRecorder()
	router.ServeHTTP(w, req)

	assert.Equal(t, http.StatusOK, w.Code)

	body := w.Body.String()

	// 验证系统指标
	assert.Contains(t, body, "go_goroutines")
	assert.Contains(t, body, "memory_usage_bytes")
	assert.Contains(t, body, "cpu_usage_percent")

	// 验证指标值
	assert.Contains(t, body, "go_goroutines 100")
	assert.Contains(t, body, "memory_usage_bytes 1.048576e+08") // 100MB in scientific notation
	assert.Contains(t, body, "cpu_usage_percent 25.5")

	// 测试收集器不为空
	assert.NotNil(t, systemCollector)
}

func TestDatabaseMetricsCollector(t *testing.T) {
	// 创建指标管理器
	metricsManager := monitoring.NewMetricsManager()

	// 创建数据库指标收集器
	dbCollector := monitoring.NewDatabaseMetricsCollector(metricsManager, nil)

	// 记录数据库连接池指标
	dbCollector.CollectConnectionPoolMetrics(5, 3, 10) // 5 active, 3 idle, 10 total

	// 记录数据库查询
	dbCollector.RecordQuery("INSERT", "orders", "test-tenant", 100*time.Millisecond, nil)

	// 创建HTTP服务器来测试指标端点
	gin.SetMode(gin.TestMode)
	router := gin.New()
	router.GET("/metrics", func(c *gin.Context) {
		handler := metricsManager.GetHandler()
		handler.ServeHTTP(c.Writer, c.Request)
	})

	// 获取指标
	req, err := http.NewRequest("GET", "/metrics", nil)
	require.NoError(t, err)

	w := httptest.NewRecorder()
	router.ServeHTTP(w, req)

	assert.Equal(t, http.StatusOK, w.Code)

	body := w.Body.String()

	// 验证数据库指标
	assert.Contains(t, body, "database_connections_active 5")
	assert.Contains(t, body, "database_connections_idle 3")
	assert.Contains(t, body, "database_connections_total 10")
	assert.Contains(t, body, `database_queries_total{operation="INSERT",status="success",table="orders",tenant_id="test-tenant"} 1`)

	// 测试收集器不为空
	assert.NotNil(t, dbCollector)
}

func TestBusinessMetricsCollector(t *testing.T) {
	// 创建指标管理器
	metricsManager := monitoring.NewMetricsManager()

	// 创建业务指标收集器
	businessCollector := monitoring.NewBusinessMetricsCollector(metricsManager, nil)

	// 记录业务操作
	businessCollector.RecordUserLogin("test-tenant", "test-user", true, 150*time.Millisecond)
	businessCollector.RecordOrderCreation("test-tenant", "test-user", true, 300*time.Millisecond)

	// 创建HTTP服务器来测试指标端点
	gin.SetMode(gin.TestMode)
	router := gin.New()
	router.GET("/metrics", func(c *gin.Context) {
		handler := metricsManager.GetHandler()
		handler.ServeHTTP(c.Writer, c.Request)
	})

	// 获取指标
	req, err := http.NewRequest("GET", "/metrics", nil)
	require.NoError(t, err)

	w := httptest.NewRecorder()
	router.ServeHTTP(w, req)

	assert.Equal(t, http.StatusOK, w.Code)

	body := w.Body.String()

	// 验证业务指标
	assert.Contains(t, body, `business_operations_total{operation="user_login",status="success",tenant_id="test-tenant",user_id="test-user"} 1`)
	assert.Contains(t, body, `business_operations_total{operation="order_creation",status="success",tenant_id="test-tenant",user_id="test-user"} 1`)

	// 测试收集器不为空
	assert.NotNil(t, businessCollector)
}

func TestPrometheusMetricsFormat(t *testing.T) {
	// 创建指标管理器
	metricsManager := monitoring.NewMetricsManager()

	// 记录一些指标
	metricsManager.RecordHTTPRequest("POST", "/api/users", "201", "tenant-123", 250*time.Millisecond, 512, 1024)

	// 创建HTTP服务器
	gin.SetMode(gin.TestMode)
	router := gin.New()
	router.GET("/metrics", func(c *gin.Context) {
		handler := metricsManager.GetHandler()
		handler.ServeHTTP(c.Writer, c.Request)
	})

	// 获取指标
	req, err := http.NewRequest("GET", "/metrics", nil)
	require.NoError(t, err)

	w := httptest.NewRecorder()
	router.ServeHTTP(w, req)

	assert.Equal(t, http.StatusOK, w.Code)
	assert.Contains(t, w.Header().Get("Content-Type"), "text/plain")

	body := w.Body.String()

	// 验证Prometheus格式
	lines := strings.Split(body, "\n")

	// 应该包含HELP和TYPE注释
	helpFound := false
	typeFound := false
	metricFound := false

	for _, line := range lines {
		if strings.HasPrefix(line, "# HELP http_requests_total") {
			helpFound = true
		}
		if strings.HasPrefix(line, "# TYPE http_requests_total") {
			typeFound = true
		}
		if strings.Contains(line, `http_requests_total{endpoint="/api/users",method="POST",status_code="201",tenant_id="tenant-123"} 1`) {
			metricFound = true
		}
	}

	assert.True(t, helpFound, "Should contain HELP comment")
	assert.True(t, typeFound, "Should contain TYPE comment")
	assert.True(t, metricFound, "Should contain metric value")
}
