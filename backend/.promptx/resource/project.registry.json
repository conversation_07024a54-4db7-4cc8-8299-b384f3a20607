{"version": "2.0.0", "source": "project", "metadata": {"version": "2.0.0", "description": "project 级资源注册表", "createdAt": "2025-06-25T15:39:06.179Z", "updatedAt": "2025-06-25T15:39:06.181Z", "resourceCount": 4}, "resources": [{"id": "golang-backend-developer", "source": "project", "protocol": "role", "name": "Golang Backend Developer 角色", "description": "专业角色，提供特定领域的专业能力", "reference": "@project://.promptx/resource/domain/golang-backend-developer/golang-backend-developer.role.md", "metadata": {"createdAt": "2025-06-25T15:39:06.180Z", "updatedAt": "2025-06-25T15:39:06.180Z", "scannedAt": "2025-06-25T15:39:06.180Z"}}, {"id": "golang-backend-thinking", "source": "project", "protocol": "thought", "name": "Golang Backend Thinking 思维模式", "description": "思维模式，指导AI的思考方式", "reference": "@project://.promptx/resource/domain/golang-backend-developer/thought/golang-backend-thinking.thought.md", "metadata": {"createdAt": "2025-06-25T15:39:06.181Z", "updatedAt": "2025-06-25T15:39:06.181Z", "scannedAt": "2025-06-25T15:39:06.181Z"}}, {"id": "golang-development-process", "source": "project", "protocol": "execution", "name": "Golang Development Process 执行模式", "description": "执行模式，定义具体的行为模式", "reference": "@project://.promptx/resource/domain/golang-backend-developer/execution/golang-development-process.execution.md", "metadata": {"createdAt": "2025-06-25T15:39:06.181Z", "updatedAt": "2025-06-25T15:39:06.181Z", "scannedAt": "2025-06-25T15:39:06.181Z"}}, {"id": "golang-backend-expertise", "source": "project", "protocol": "knowledge", "name": "Golang Backend Expertise 知识库", "description": "知识库，提供专业知识和信息", "reference": "@project://.promptx/resource/domain/golang-backend-developer/knowledge/golang-backend-expertise.knowledge.md", "metadata": {"createdAt": "2025-06-25T15:39:06.181Z", "updatedAt": "2025-06-25T15:39:06.181Z", "scannedAt": "2025-06-25T15:39:06.181Z"}}], "stats": {"totalResources": 4, "byProtocol": {"role": 1, "thought": 1, "execution": 1, "knowledge": 1}, "bySource": {"project": 4}}}