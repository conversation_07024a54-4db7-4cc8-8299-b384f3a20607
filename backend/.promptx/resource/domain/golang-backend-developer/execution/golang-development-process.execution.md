<execution>
  <constraint>
    ## Go语言技术约束
    - **Go版本兼容性**：必须考虑Go版本差异和向后兼容性问题
    - **内存管理限制**：受GC机制影响，需要合理管理内存分配和回收
    - **并发模型约束**：Goroutine和Channel的使用必须避免Race Condition
    - **静态编译约束**：CGO使用会影响跨平台编译和部署灵活性
    - **标准库依赖**：尽量减少第三方依赖，优先使用Go标准库
    - **错误处理约束**：必须显式处理所有可能的错误情况
  </constraint>

  <rule>
    ## Go开发强制规则
    - **代码规范强制性**：严格遵循`gofmt`、`golint`、`go vet`工具检查要求
    - **错误处理强制性**：所有返回error的函数调用必须检查error
    - **并发安全强制性**：共享资源访问必须使用互斥锁或Channel保护
    - **测试覆盖强制性**：核心业务逻辑必须有对应的单元测试
    - **接口优先原则**：依赖抽象而非具体实现，广泛使用接口
    - **资源释放强制性**：使用defer确保资源正确释放
    - **上下文传递强制性**：长时间运行的操作必须支持context.Context取消
  </rule>

  <guideline>
    ## Go开发指导原则
    - **简洁性原则**：保持代码简单直观，避免过度抽象
    - **可读性优先**：代码应该是自文档化的，变量和函数命名清晰
    - **性能合理性**：在保证可读性的前提下考虑性能优化
    - **测试友好性**：设计时考虑测试的便利性，依赖注入有利于测试
    - **渐进式优化**：先实现功能，再进行性能和架构优化
    - **标准库优先**：优先使用Go标准库，减少外部依赖
    - **文档完整性**：重要的包和函数应该有清晰的文档注释
  </guideline>

  <process>
    ## Go后端开发流程

    ### Phase 1: 项目初始化
    ```bash
    # 1. 创建Go模块
    go mod init project-name
    
    # 2. 设置项目结构
    mkdir -p {cmd,internal,pkg,api,configs,scripts,docs}
    
    # 3. 配置开发工具
    # - 配置IDE/编辑器Go插件
    # - 设置pre-commit hooks
    # - 配置Makefile自动化脚本
    ```

    ### Phase 2: 架构设计
    1. **领域建模**
       - 识别核心业务实体和值对象
       - 定义领域服务和仓储接口
       - 设计聚合根和边界上下文

    2. **分层架构设计**
       ```
       cmd/           # 应用程序入口
       internal/      # 私有代码
         domain/      # 领域层
         application/ # 应用层
         adapters/    # 适配器层
       pkg/           # 公共库
       ```

    3. **接口设计**
       - 定义Repository接口
       - 设计Service接口
       - 规划HTTP API接口

    ### Phase 3: 核心开发
    ```go
    // 1. 实体定义示例
    type User struct {
        ID       int64     `json:"id" db:"id"`
        Username string    `json:"username" db:"username"`
        Email    string    `json:"email" db:"email"`
        CreateAt time.Time `json:"created_at" db:"created_at"`
    }

    // 2. 仓储接口定义
    type UserRepository interface {
        Create(ctx context.Context, user *User) error
        GetByID(ctx context.Context, id int64) (*User, error)
        Update(ctx context.Context, user *User) error
        Delete(ctx context.Context, id int64) error
    }

    // 3. 服务实现
    type UserService struct {
        repo UserRepository
    }

    func (s *UserService) CreateUser(ctx context.Context, req *CreateUserRequest) (*User, error) {
        // 业务逻辑处理
        user := &User{
            Username: req.Username,
            Email:    req.Email,
            CreateAt: time.Now(),
        }
        
        if err := s.repo.Create(ctx, user); err != nil {
            return nil, fmt.Errorf("failed to create user: %w", err)
        }
        
        return user, nil
    }
    ```

    ### Phase 4: 测试实现
    ```go
    // 单元测试示例
    func TestUserService_CreateUser(t *testing.T) {
        // 准备测试数据
        repo := &mockUserRepository{}
        service := &UserService{repo: repo}
        
        req := &CreateUserRequest{
            Username: "testuser",
            Email:    "<EMAIL>",
        }
        
        // 执行测试
        user, err := service.CreateUser(context.Background(), req)
        
        // 验证结果
        assert.NoError(t, err)
        assert.Equal(t, "testuser", user.Username)
        assert.Equal(t, "<EMAIL>", user.Email)
    }
    ```

    ### Phase 5: 集成与部署
    1. **Docker容器化**
       ```dockerfile
       FROM golang:1.21-alpine AS builder
       WORKDIR /app
       COPY go.mod go.sum ./
       RUN go mod download
       COPY . .
       RUN go build -o main cmd/api/main.go

       FROM alpine:latest
       RUN apk --no-cache add ca-certificates
       WORKDIR /root/
       COPY --from=builder /app/main .
       CMD ["./main"]
       ```

    2. **CI/CD配置**
       - 自动化测试
       - 代码质量检查
       - 构建和部署流水线

    ### Phase 6: 监控与维护
    ```go
    // 健康检查端点
    func healthCheck(w http.ResponseWriter, r *http.Request) {
        health := map[string]string{
            "status":    "ok",
            "timestamp": time.Now().UTC().Format(time.RFC3339),
        }
        
        w.Header().Set("Content-Type", "application/json")
        json.NewEncoder(w).Encode(health)
    }

    // 指标收集
    var (
        requestsTotal = prometheus.NewCounterVec(
            prometheus.CounterOpts{
                Name: "http_requests_total",
                Help: "Total HTTP requests",
            },
            []string{"method", "endpoint", "status"},
        )
    )
    ```
  </process>

  <criteria>
    ## Go开发质量标准

    ### 代码质量标准
    - ✅ 通过`go fmt`、`go vet`、`golangci-lint`检查
    - ✅ 函数复杂度控制在合理范围内（圈复杂度 < 10）
    - ✅ 包和函数有清晰的文档注释
    - ✅ 变量和函数命名符合Go惯用法
    - ✅ 正确使用Go的接口和组合

    ### 测试质量标准
    - ✅ 核心业务逻辑单元测试覆盖率 > 80%
    - ✅ 集成测试覆盖主要业务流程
    - ✅ 基准测试覆盖性能关键路径
    - ✅ 测试用例包含边界条件和异常情况
    - ✅ Mock测试合理使用，不过度Mock

    ### 性能标准
    - ✅ API响应时间 < 100ms（95分位）
    - ✅ 内存使用稳定，无明显内存泄漏
    - ✅ Goroutine数量控制在合理范围
    - ✅ 数据库连接池配置合理
    - ✅ 并发安全，无Race Condition

    ### 架构质量标准
    - ✅ 分层架构清晰，依赖方向正确
    - ✅ 接口设计合理，抽象层次适当
    - ✅ 错误处理一致且完整
    - ✅ 配置管理统一且安全
    - ✅ 日志记录结构化且完整

    ### 部署运维标准
    - ✅ Docker镜像大小合理（< 100MB）
    - ✅ 应用启动时间 < 30秒
    - ✅ 健康检查和就绪检查正确实现
    - ✅ 监控指标完整且有用
    - ✅ 日志可查询且包含关键信息
  </criteria>
</execution> 