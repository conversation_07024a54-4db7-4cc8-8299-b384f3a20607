package utils

import (
	"fmt"
	"reflect"
	"strconv"
	"strings"

	apperrors "backend/pkg/common/errors"
	"backend/pkg/common/errors/codes"
)

// ToString 将任意类型转换为字符串
func ToString(value interface{}) string {
	if value == nil {
		return ""
	}

	switch v := value.(type) {
	case string:
		return v
	case []byte:
		return string(v)
	case int, int8, int16, int32, int64:
		return fmt.Sprintf("%d", v)
	case uint, uint8, uint16, uint32, uint64:
		return fmt.Sprintf("%d", v)
	case float32, float64:
		return fmt.Sprintf("%g", v)
	case bool:
		return strconv.FormatBool(v)
	case fmt.Stringer:
		return v.String()
	default:
		return fmt.Sprintf("%v", v)
	}
}

// ToInt 将字符串转换为int
func ToInt(str string) (int, error) {
	return strconv.Atoi(strings.TrimSpace(str))
}

// ToIntWithDefault 将字符串转换为int，失败时返回默认值
func ToIntWithDefault(str string, defaultValue int) int {
	if value, err := ToInt(str); err == nil {
		return value
	}
	return defaultValue
}

// ToInt64 将字符串转换为int64
func ToInt64(str string) (int64, error) {
	return strconv.ParseInt(strings.TrimSpace(str), 10, 64)
}

// ToInt64WithDefault 将字符串转换为int64，失败时返回默认值
func ToInt64WithDefault(str string, defaultValue int64) int64 {
	if value, err := ToInt64(str); err == nil {
		return value
	}
	return defaultValue
}

// ToFloat64 将字符串转换为float64
func ToFloat64(str string) (float64, error) {
	return strconv.ParseFloat(strings.TrimSpace(str), 64)
}

// ToFloat64WithDefault 将字符串转换为float64，失败时返回默认值
func ToFloat64WithDefault(str string, defaultValue float64) float64 {
	if value, err := ToFloat64(str); err == nil {
		return value
	}
	return defaultValue
}

// ToBool 将字符串转换为bool
func ToBool(str string) (bool, error) {
	str = strings.TrimSpace(strings.ToLower(str))
	switch str {
	case "true", "1", "yes", "on", "y", "t":
		return true, nil
	case "false", "0", "no", "off", "n", "f", "":
		return false, nil
	default:
		return strconv.ParseBool(str)
	}
}

// ToBoolWithDefault 将字符串转换为bool，失败时返回默认值
func ToBoolWithDefault(str string, defaultValue bool) bool {
	if value, err := ToBool(str); err == nil {
		return value
	}
	return defaultValue
}

// ToStringSlice 将任意类型转换为[]string
func ToStringSlice(value interface{}) []string {
	if value == nil {
		return []string{}
	}

	v := reflect.ValueOf(value)
	switch v.Kind() {
	case reflect.String:
		return []string{v.String()}
	case reflect.Slice, reflect.Array:
		result := make([]string, v.Len())
		for i := 0; i < v.Len(); i++ {
			result[i] = ToString(v.Index(i).Interface())
		}
		return result
	default:
		return []string{ToString(value)}
	}
}

// ToIntSlice 将字符串切片转换为int切片
func ToIntSlice(strSlice []string) ([]int, error) {
	result := make([]int, len(strSlice))
	for i, str := range strSlice {
		value, err := ToInt(str)
		if err != nil {
			return nil, apperrors.NewInternal(codes.Serialization, "转换数组元素").Wrap(err).Build()
		}
		result[i] = value
	}
	return result, nil
}

// ToInt64Slice 将字符串切片转换为int64切片
func ToInt64Slice(strSlice []string) ([]int64, error) {
	result := make([]int64, len(strSlice))
	for i, str := range strSlice {
		value, err := ToInt64(str)
		if err != nil {
			return nil, apperrors.NewInternal(codes.Serialization, "转换数组元素").Wrap(err).Build()
		}
		result[i] = value
	}
	return result, nil
}

// StringPtr 将字符串转换为字符串指针
func StringPtr(s string) *string {
	return &s
}

// StringPtrWithDefault 将字符串指针转换为字符串，nil时返回默认值
func StringPtrWithDefault(s *string, defaultValue string) string {
	if s == nil {
		return defaultValue
	}
	return *s
}

// IntPtr 将int转换为int指针
func IntPtr(i int) *int {
	return &i
}

// IntPtrWithDefault 将int指针转换为int，nil时返回默认值
func IntPtrWithDefault(i *int, defaultValue int) int {
	if i == nil {
		return defaultValue
	}
	return *i
}

// Int64Ptr 将int64转换为int64指针
func Int64Ptr(i int64) *int64 {
	return &i
}

// Int64PtrWithDefault 将int64指针转换为int64，nil时返回默认值
func Int64PtrWithDefault(i *int64, defaultValue int64) int64 {
	if i == nil {
		return defaultValue
	}
	return *i
}

// Float64Ptr 将float64转换为float64指针
func Float64Ptr(f float64) *float64 {
	return &f
}

// Float64PtrWithDefault 将float64指针转换为float64，nil时返回默认值
func Float64PtrWithDefault(f *float64, defaultValue float64) float64 {
	if f == nil {
		return defaultValue
	}
	return *f
}

// BoolPtr 将bool转换为bool指针
func BoolPtr(b bool) *bool {
	return &b
}

// BoolPtrWithDefault 将bool指针转换为bool，nil时返回默认值
func BoolPtrWithDefault(b *bool, defaultValue bool) bool {
	if b == nil {
		return defaultValue
	}
	return *b
}

// IsEmpty 检查值是否为空
func IsEmpty(value interface{}) bool {
	if value == nil {
		return true
	}

	v := reflect.ValueOf(value)
	switch v.Kind() {
	case reflect.String:
		return strings.TrimSpace(v.String()) == ""
	case reflect.Slice, reflect.Array, reflect.Map, reflect.Chan:
		return v.Len() == 0
	case reflect.Ptr, reflect.Interface:
		return v.IsNil()
	default:
		return false
	}
}

// IsNotEmpty 检查值是否不为空
func IsNotEmpty(value interface{}) bool {
	return !IsEmpty(value)
}

// SafeString 安全地获取字符串值，处理nil指针
func SafeString(s *string) string {
	if s == nil {
		return ""
	}
	return *s
}

// SafeInt 安全地获取int值，处理nil指针
func SafeInt(i *int) int {
	if i == nil {
		return 0
	}
	return *i
}

// SafeInt64 安全地获取int64值，处理nil指针
func SafeInt64(i *int64) int64 {
	if i == nil {
		return 0
	}
	return *i
}

// SafeFloat64 安全地获取float64值，处理nil指针
func SafeFloat64(f *float64) float64 {
	if f == nil {
		return 0.0
	}
	return *f
}

// SafeBool 安全地获取bool值，处理nil指针
func SafeBool(b *bool) bool {
	if b == nil {
		return false
	}
	return *b
}

// Contains 检查切片是否包含指定元素
func Contains(slice interface{}, item interface{}) bool {
	s := reflect.ValueOf(slice)
	if s.Kind() != reflect.Slice {
		return false
	}

	for i := 0; i < s.Len(); i++ {
		if reflect.DeepEqual(s.Index(i).Interface(), item) {
			return true
		}
	}
	return false
}

// StringInSlice 检查字符串是否在切片中
func StringInSlice(str string, slice []string) bool {
	for _, s := range slice {
		if s == str {
			return true
		}
	}
	return false
}

// IntInSlice 检查整数是否在切片中
func IntInSlice(num int, slice []int) bool {
	for _, n := range slice {
		if n == num {
			return true
		}
	}
	return false
}

// Must 将任意类型转换为任意类型，失败时panic
func Must(value interface{}, err error) interface{} {
	if err != nil {
		panic(err)
	}
	return value
}
