package utils

import "time"

// Now 获取当前时间
func Now() time.Time {
	return time.Now()
}

// NowPtr 获取当前时间指针
func NowPtr() *time.Time {
	now := time.Now()
	return &now
}

// FormatTime 格式化时间
func FormatTime(t time.Time) string {
	return t.Format("2006-01-02 15:04:05")
}

// ParseTime 解析时间字符串
func ParseTime(timeStr string) (time.Time, error) {
	return time.Parse("2006-01-02 15:04:05", timeStr)
}

// IsToday 检查是否是今天
func IsToday(t time.Time) bool {
	now := time.Now()
	return t.Year() == now.Year() && t.Month() == now.Month() && t.Day() == now.Day()
}

// DaysAgo 计算几天前的时间
func DaysAgo(days int) time.Time {
	return time.Now().AddDate(0, 0, -days)
}

// DaysLater 计算几天后的时间
func DaysLater(days int) time.Time {
	return time.Now().AddDate(0, 0, days)
}
