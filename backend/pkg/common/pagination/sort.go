package pagination

import (
	"errors"
	"fmt"
	"strings"
)

// Validate 验证排序字段
func (s *SortField) Validate() error {
	if s.Field == "" {
		return errors.New("sort field cannot be empty")
	}
	
	// 验证字段名格式（只允许字母、数字、下划线、点号）
	if !isValidFieldName(s.Field) {
		return fmt.Errorf("invalid field name: %s", s.Field)
	}
	
	if s.Direction != "" && s.Direction != SortAsc && s.Direction != SortDesc {
		return fmt.Errorf("invalid sort direction: %s", s.Direction)
	}
	
	return nil
}

// ToSQL 转换为SQL排序语句
func (s *SortField) ToSQL() string {
	direction := string(s.Direction)
	if direction == "" {
		direction = string(SortAsc)
	}
	
	sql := s.Field + " " + strings.ToUpper(direction)
	
	if s.NullsLast {
		sql += " NULLS LAST"
	}
	
	return sql
}

// GetDirection 获取排序方向，如果未设置则返回默认值
func (s *SortField) GetDirection() SortDirection {
	if s.Direction == "" {
		return SortAsc
	}
	return s.Direction
}

// IsAscending 是否为升序
func (s *SortField) IsAscending() bool {
	return s.GetDirection() == SortAsc
}

// IsDescending 是否为降序
func (s *SortField) IsDescending() bool {
	return s.GetDirection() == SortDesc
}

// SortFields 排序字段集合
type SortFields []SortField

// Validate 验证所有排序字段
func (sf SortFields) Validate() error {
	for i, field := range sf {
		if err := field.Validate(); err != nil {
			return fmt.Errorf("sort field %d: %w", i, err)
		}
	}
	
	// 检查重复字段
	fieldMap := make(map[string]bool)
	for _, field := range sf {
		if fieldMap[field.Field] {
			return fmt.Errorf("duplicate sort field: %s", field.Field)
		}
		fieldMap[field.Field] = true
	}
	
	return nil
}

// ToSQL 转换为SQL ORDER BY子句
func (sf SortFields) ToSQL() string {
	if len(sf) == 0 {
		return ""
	}
	
	var parts []string
	for _, field := range sf {
		parts = append(parts, field.ToSQL())
	}
	
	return strings.Join(parts, ", ")
}

// AddField 添加排序字段
func (sf *SortFields) AddField(field string, direction SortDirection) {
	*sf = append(*sf, SortField{
		Field:     field,
		Direction: direction,
	})
}

// AddAscending 添加升序字段
func (sf *SortFields) AddAscending(field string) {
	sf.AddField(field, SortAsc)
}

// AddDescending 添加降序字段
func (sf *SortFields) AddDescending(field string) {
	sf.AddField(field, SortDesc)
}

// HasField 检查是否包含指定字段
func (sf SortFields) HasField(field string) bool {
	for _, f := range sf {
		if f.Field == field {
			return true
		}
	}
	return false
}

// GetField 获取指定字段的排序信息
func (sf SortFields) GetField(field string) (*SortField, bool) {
	for _, f := range sf {
		if f.Field == field {
			return &f, true
		}
	}
	return nil, false
}

// RemoveField 移除指定字段
func (sf *SortFields) RemoveField(field string) {
	for i, f := range *sf {
		if f.Field == field {
			*sf = append((*sf)[:i], (*sf)[i+1:]...)
			return
		}
	}
}

// Clear 清空所有排序字段
func (sf *SortFields) Clear() {
	*sf = (*sf)[:0]
}

// IsEmpty 检查是否为空
func (sf SortFields) IsEmpty() bool {
	return len(sf) == 0
}

// Count 获取字段数量
func (sf SortFields) Count() int {
	return len(sf)
}

// ParseSortString 解析排序字符串
// 格式: "field1:asc,field2:desc,field3"
func ParseSortString(sortStr string) (SortFields, error) {
	if sortStr == "" {
		return nil, nil
	}
	
	var fields SortFields
	parts := strings.Split(sortStr, ",")
	
	for _, part := range parts {
		part = strings.TrimSpace(part)
		if part == "" {
			continue
		}
		
		var field SortField
		if strings.Contains(part, ":") {
			fieldParts := strings.Split(part, ":")
			if len(fieldParts) != 2 {
				return nil, fmt.Errorf("invalid sort format: %s", part)
			}
			
			field.Field = strings.TrimSpace(fieldParts[0])
			dirStr := strings.ToLower(strings.TrimSpace(fieldParts[1]))
			
			switch dirStr {
			case "asc", "ascending":
				field.Direction = SortAsc
			case "desc", "descending":
				field.Direction = SortDesc
			default:
				return nil, fmt.Errorf("invalid sort direction: %s", dirStr)
			}
		} else {
			field.Field = part
			field.Direction = SortAsc // 默认升序
		}
		
		if err := field.Validate(); err != nil {
			return nil, fmt.Errorf("invalid sort field %s: %w", field.Field, err)
		}
		
		fields = append(fields, field)
	}
	
	return fields, nil
}

// isValidFieldName 验证字段名是否有效
func isValidFieldName(field string) bool {
	if field == "" {
		return false
	}
	
	// 允许字母、数字、下划线、点号（用于关联查询）
	for _, r := range field {
		if !((r >= 'a' && r <= 'z') || 
			 (r >= 'A' && r <= 'Z') || 
			 (r >= '0' && r <= '9') || 
			 r == '_' || r == '.') {
			return false
		}
	}
	
	// 不能以数字开头
	if field[0] >= '0' && field[0] <= '9' {
		return false
	}
	
	return true
}
