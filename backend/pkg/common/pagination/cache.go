package pagination

import (
	"context"
	"errors"
	"sync"
	"time"
)

// MemoryCache 内存缓存实现（用于测试和简单场景）
type MemoryCache struct {
	data map[string]*cacheItem
	mu   sync.RWMutex
}

type cacheItem struct {
	value     []byte
	expiresAt time.Time
}

// NewMemoryCache 创建内存缓存
func NewMemoryCache() *MemoryCache {
	cache := &MemoryCache{
		data: make(map[string]*cacheItem),
	}
	
	// 启动清理协程
	go cache.cleanup()
	
	return cache
}

// Get 获取缓存值
func (c *MemoryCache) Get(ctx context.Context, key string) ([]byte, error) {
	c.mu.RLock()
	defer c.mu.RUnlock()
	
	item, exists := c.data[key]
	if !exists {
		return nil, errors.New("key not found")
	}
	
	if time.Now().After(item.expiresAt) {
		// 过期了，删除并返回错误
		delete(c.data, key)
		return nil, errors.New("key expired")
	}
	
	return item.value, nil
}

// Set 设置缓存值
func (c *MemoryCache) Set(ctx context.Context, key string, value []byte, ttl time.Duration) error {
	c.mu.Lock()
	defer c.mu.Unlock()
	
	expiresAt := time.Now().Add(ttl)
	c.data[key] = &cacheItem{
		value:     value,
		expiresAt: expiresAt,
	}
	
	return nil
}

// Delete 删除缓存值
func (c *MemoryCache) Delete(ctx context.Context, key string) error {
	c.mu.Lock()
	defer c.mu.Unlock()
	
	delete(c.data, key)
	return nil
}

// Clear 清空所有缓存
func (c *MemoryCache) Clear() {
	c.mu.Lock()
	defer c.mu.Unlock()
	
	c.data = make(map[string]*cacheItem)
}

// Size 获取缓存大小
func (c *MemoryCache) Size() int {
	c.mu.RLock()
	defer c.mu.RUnlock()
	
	return len(c.data)
}

// cleanup 清理过期缓存
func (c *MemoryCache) cleanup() {
	ticker := time.NewTicker(5 * time.Minute)
	defer ticker.Stop()
	
	for range ticker.C {
		c.mu.Lock()
		now := time.Now()
		for key, item := range c.data {
			if now.After(item.expiresAt) {
				delete(c.data, key)
			}
		}
		c.mu.Unlock()
	}
}

// NullCache 空缓存实现（不缓存任何内容）
type NullCache struct{}

// NewNullCache 创建空缓存
func NewNullCache() *NullCache {
	return &NullCache{}
}

// Get 获取缓存值（总是返回未找到）
func (c *NullCache) Get(ctx context.Context, key string) ([]byte, error) {
	return nil, errors.New("key not found")
}

// Set 设置缓存值（什么都不做）
func (c *NullCache) Set(ctx context.Context, key string, value []byte, ttl time.Duration) error {
	return nil
}

// Delete 删除缓存值（什么都不做）
func (c *NullCache) Delete(ctx context.Context, key string) error {
	return nil
}
