package pagination

import (
	"errors"
	"fmt"
	"time"
)

// Request 通用分页请求
type Request struct {
	// 基础分页参数
	Page     int `json:"page" validate:"min=1" form:"page"`
	PageSize int `json:"page_size" validate:"min=1,max=1000" form:"page_size"`
	
	// 排序参数
	Sort []SortField `json:"sort,omitempty" form:"sort"`
	
	// 过滤参数
	Filter Filter `json:"filter,omitempty" form:"filter"`
	
	// 搜索参数
	Search *SearchCondition `json:"search,omitempty" form:"search"`
	
	// 分页模式
	Mode PaginationMode `json:"mode,omitempty" form:"mode"`
	
	// 性能优化选项
	Options *RequestOptions `json:"options,omitempty"`
}

// CursorRequest 游标分页请求
type CursorRequest struct {
	// 游标值
	Cursor string `json:"cursor,omitempty" form:"cursor"`
	
	// 每页数量
	Limit int `json:"limit" validate:"min=1,max=1000" form:"limit"`
	
	// 方向（向前/向后）
	Direction CursorDirection `json:"direction,omitempty" form:"direction"`
	
	// 排序字段（游标分页必须指定）
	SortBy string `json:"sort_by" validate:"required" form:"sort_by"`
	
	// 排序方向
	SortDesc bool `json:"sort_desc,omitempty" form:"sort_desc"`
	
	// 过滤条件
	Filter Filter `json:"filter,omitempty" form:"filter"`
}

// StreamRequest 流式分页请求
type StreamRequest struct {
	// 起始位置
	Offset int64 `json:"offset,omitempty" form:"offset"`
	
	// 批次大小
	BatchSize int `json:"batch_size" validate:"min=1,max=10000" form:"batch_size"`
	
	// 时间范围
	TimeRange *TimeRange `json:"time_range,omitempty" form:"time_range"`
	
	// 过滤条件
	Filter Filter `json:"filter,omitempty" form:"filter"`
}

// Validate 验证分页请求
func (r *Request) Validate() error {
	if r.Page < 1 {
		return errors.New("page must be greater than 0")
	}
	
	if r.PageSize < 1 || r.PageSize > 1000 {
		return errors.New("page_size must be between 1 and 1000")
	}
	
	// 验证排序字段
	for _, sort := range r.Sort {
		if err := sort.Validate(); err != nil {
			return fmt.Errorf("invalid sort field: %w", err)
		}
	}
	
	// 验证过滤条件
	if r.Filter != nil {
		if err := r.Filter.Validate(); err != nil {
			return fmt.Errorf("invalid filter: %w", err)
		}
	}
	
	return nil
}

// GetOffset 计算偏移量
func (r *Request) GetOffset() int {
	return (r.Page - 1) * r.PageSize
}

// GetLimit 获取限制数量
func (r *Request) GetLimit() int {
	return r.PageSize
}

// SetDefaults 设置默认值
func (r *Request) SetDefaults() {
	if r.Page <= 0 {
		r.Page = 1
	}
	
	if r.PageSize <= 0 {
		r.PageSize = 10
	}
	
	if r.PageSize > 1000 {
		r.PageSize = 1000
	}
	
	if r.Mode == "" {
		r.Mode = ModeOffset
	}
	
	if r.Options == nil {
		r.Options = &RequestOptions{
			CountTotal: true,
			UseCache:   false,
			CacheTTL:   5 * time.Minute,
		}
	}
}

// Validate 验证游标分页请求
func (r *CursorRequest) Validate() error {
	if r.SortBy == "" {
		return errors.New("sort_by is required for cursor pagination")
	}
	
	if r.Limit < 1 || r.Limit > 1000 {
		return errors.New("limit must be between 1 and 1000")
	}
	
	if r.Direction != "" && r.Direction != CursorNext && r.Direction != CursorPrev {
		return errors.New("invalid cursor direction")
	}
	
	// 验证过滤条件
	if r.Filter != nil {
		if err := r.Filter.Validate(); err != nil {
			return fmt.Errorf("invalid filter: %w", err)
		}
	}
	
	return nil
}

// SetDefaults 设置默认值
func (r *CursorRequest) SetDefaults() {
	if r.Limit <= 0 {
		r.Limit = 10
	}
	
	if r.Limit > 1000 {
		r.Limit = 1000
	}
	
	if r.Direction == "" {
		r.Direction = CursorNext
	}
}

// Validate 验证流式分页请求
func (r *StreamRequest) Validate() error {
	if r.BatchSize < 1 || r.BatchSize > 10000 {
		return errors.New("batch_size must be between 1 and 10000")
	}
	
	if r.Offset < 0 {
		return errors.New("offset must be non-negative")
	}
	
	// 验证时间范围
	if r.TimeRange != nil {
		if r.TimeRange.Start != nil && r.TimeRange.End != nil {
			if r.TimeRange.Start.After(*r.TimeRange.End) {
				return errors.New("start time must be before end time")
			}
		}
	}
	
	// 验证过滤条件
	if r.Filter != nil {
		if err := r.Filter.Validate(); err != nil {
			return fmt.Errorf("invalid filter: %w", err)
		}
	}
	
	return nil
}

// SetDefaults 设置默认值
func (r *StreamRequest) SetDefaults() {
	if r.BatchSize <= 0 {
		r.BatchSize = 100
	}
	
	if r.BatchSize > 10000 {
		r.BatchSize = 10000
	}
	
	if r.Offset < 0 {
		r.Offset = 0
	}
}
