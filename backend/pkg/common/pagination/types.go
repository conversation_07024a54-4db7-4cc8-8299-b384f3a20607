package pagination

import (
	"context"
	"time"
)

// PaginationMode 分页模式
type PaginationMode string

const (
	ModeOffset PaginationMode = "offset" // 传统偏移分页
	ModeCursor PaginationMode = "cursor" // 游标分页
	ModeStream PaginationMode = "stream" // 流式分页
)

// SortDirection 排序方向
type SortDirection string

const (
	SortAsc  SortDirection = "asc"
	SortDesc SortDirection = "desc"
)

// CursorDirection 游标方向
type CursorDirection string

const (
	CursorNext CursorDirection = "next"
	CursorPrev CursorDirection = "prev"
)

// FilterOperator 过滤操作符
type FilterOperator string

const (
	OpEqual        FilterOperator = "eq"      // 等于
	OpNotEqual     FilterOperator = "ne"      // 不等于
	OpGreater      FilterOperator = "gt"      // 大于
	OpGreaterEqual FilterOperator = "gte"     // 大于等于
	OpLess         FilterOperator = "lt"      // 小于
	OpLessEqual    FilterOperator = "lte"     // 小于等于
	OpLike         FilterOperator = "like"    // 模糊匹配
	OpNotLike      FilterOperator = "nlike"   // 不匹配
	OpIn           FilterOperator = "in"      // 包含
	OpNotIn        FilterOperator = "nin"     // 不包含
	OpIsNull       FilterOperator = "null"    // 为空
	OpIsNotNull    FilterOperator = "nnull"   // 不为空
	OpBetween      FilterOperator = "between" // 范围
	OpRegex        FilterOperator = "regex"   // 正则表达式
)

// LogicOperator 逻辑操作符
type LogicOperator string

const (
	LogicAnd LogicOperator = "and"
	LogicOr  LogicOperator = "or"
)

// SearchMode 搜索模式
type SearchMode string

const (
	SearchExact  SearchMode = "exact"  // 精确匹配
	SearchFuzzy  SearchMode = "fuzzy"  // 模糊匹配
	SearchPrefix SearchMode = "prefix" // 前缀匹配
	SearchFull   SearchMode = "full"   // 全文搜索
)

// Paginator 分页器接口
type Paginator[T any] interface {
	// 传统分页
	Paginate(ctx context.Context, req *Request) (*Response[T], error)
	
	// 游标分页
	CursorPaginate(ctx context.Context, req *CursorRequest) (*CursorResponse[T], error)
}

// Repository 仓储分页接口
type Repository[T any] interface {
	// 查询总数
	Count(ctx context.Context, filter Filter) (int64, error)
	
	// 分页查询
	FindWithPagination(ctx context.Context, req *Request, filter Filter) ([]T, error)
	
	// 游标查询
	FindWithCursor(ctx context.Context, req *CursorRequest, filter Filter) ([]T, error)
}

// Filter 过滤条件接口
type Filter interface {
	// 验证过滤条件
	Validate() error
	
	// 获取过滤条件
	GetConditions() []FilterCondition
}

// CacheProvider 缓存提供者接口
type CacheProvider interface {
	Get(ctx context.Context, key string) ([]byte, error)
	Set(ctx context.Context, key string, value []byte, ttl time.Duration) error
	Delete(ctx context.Context, key string) error
}

// SortField 排序字段
type SortField struct {
	// 字段名
	Field string `json:"field" validate:"required"`
	
	// 排序方向
	Direction SortDirection `json:"direction,omitempty"`
	
	// 排序优先级
	Priority int `json:"priority,omitempty"`
	
	// 空值处理
	NullsLast bool `json:"nulls_last,omitempty"`
}

// FilterCondition 基础过滤条件
type FilterCondition struct {
	// 字段名
	Field string `json:"field" validate:"required"`
	
	// 操作符
	Operator FilterOperator `json:"operator" validate:"required"`
	
	// 值
	Value interface{} `json:"value"`
	
	// 多个值（用于IN操作）
	Values []interface{} `json:"values,omitempty"`
	
	// 逻辑关系
	Logic LogicOperator `json:"logic,omitempty"`
}

// SearchCondition 搜索条件
type SearchCondition struct {
	// 搜索关键词
	Query string `json:"query" validate:"required"`
	
	// 搜索字段
	Fields []string `json:"fields,omitempty"`
	
	// 搜索模式
	Mode SearchMode `json:"mode,omitempty"`
	
	// 高亮设置
	Highlight *HighlightConfig `json:"highlight,omitempty"`
}

// HighlightConfig 高亮配置
type HighlightConfig struct {
	PreTag  string `json:"pre_tag,omitempty"`
	PostTag string `json:"post_tag,omitempty"`
}

// TimeRange 时间范围
type TimeRange struct {
	Start *time.Time `json:"start,omitempty"`
	End   *time.Time `json:"end,omitempty"`
}

// RequestOptions 请求选项
type RequestOptions struct {
	// 是否计算总数（大数据量时可关闭以提升性能）
	CountTotal bool `json:"count_total"`
	
	// 是否使用缓存
	UseCache bool `json:"use_cache"`
	
	// 缓存TTL
	CacheTTL time.Duration `json:"cache_ttl"`
	
	// 是否预加载关联数据
	Preload []string `json:"preload,omitempty"`
}

// Performance 性能信息
type Performance struct {
	// 查询耗时
	QueryTime time.Duration `json:"query_time"`
	
	// 总耗时
	TotalTime time.Duration `json:"total_time"`
	
	// 是否使用了缓存
	FromCache bool `json:"from_cache"`
}

// Links HATEOAS链接信息
type Links struct {
	Self     string `json:"self,omitempty"`
	First    string `json:"first,omitempty"`
	Previous string `json:"previous,omitempty"`
	Next     string `json:"next,omitempty"`
	Last     string `json:"last,omitempty"`
}
