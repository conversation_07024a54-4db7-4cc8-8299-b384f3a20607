package pagination

import (
	"errors"
	"fmt"
	"regexp"
	"strings"
)

// Validator 分页验证器
type Validator struct {
	// 允许的排序字段
	AllowedSortFields []string
	
	// 允许的过滤字段
	AllowedFilterFields []string
	
	// 最大页大小
	MaxPageSize int
	
	// 最小页大小
	MinPageSize int
	
	// 是否允许空排序
	AllowEmptySort bool
	
	// 是否允许空过滤
	AllowEmptyFilter bool
	
	// 字段名验证正则
	FieldNamePattern *regexp.Regexp
}

// NewValidator 创建验证器
func NewValidator() *Validator {
	return &Validator{
		AllowedSortFields:   []string{},
		AllowedFilterFields: []string{},
		MaxPageSize:         1000,
		MinPageSize:         1,
		AllowEmptySort:      true,
		AllowEmptyFilter:    true,
		FieldNamePattern:    regexp.MustCompile(`^[a-zA-Z][a-zA-Z0-9_\.]*$`),
	}
}

// WithAllowedSortFields 设置允许的排序字段
func (v *Validator) WithAllowedSortFields(fields ...string) *Validator {
	v.AllowedSortFields = fields
	return v
}

// WithAllowedFilterFields 设置允许的过滤字段
func (v *Validator) WithAllowedFilterFields(fields ...string) *Validator {
	v.AllowedFilterFields = fields
	return v
}

// WithPageSizeRange 设置页大小范围
func (v *Validator) WithPageSizeRange(min, max int) *Validator {
	v.MinPageSize = min
	v.MaxPageSize = max
	return v
}

// WithFieldNamePattern 设置字段名验证模式
func (v *Validator) WithFieldNamePattern(pattern string) *Validator {
	if regex, err := regexp.Compile(pattern); err == nil {
		v.FieldNamePattern = regex
	}
	return v
}

// ValidateRequest 验证分页请求
func (v *Validator) ValidateRequest(req *Request) error {
	if req == nil {
		return errors.New("request cannot be nil")
	}
	
	// 验证页码
	if req.Page < 1 {
		return errors.New("page must be greater than 0")
	}
	
	// 验证页大小
	if req.PageSize < v.MinPageSize {
		return fmt.Errorf("page_size must be at least %d", v.MinPageSize)
	}
	
	if req.PageSize > v.MaxPageSize {
		return fmt.Errorf("page_size must not exceed %d", v.MaxPageSize)
	}
	
	// 验证排序字段
	if err := v.validateSortFields(req.Sort); err != nil {
		return fmt.Errorf("invalid sort fields: %w", err)
	}
	
	// 验证过滤条件
	if req.Filter != nil {
		if err := v.validateFilter(req.Filter); err != nil {
			return fmt.Errorf("invalid filter: %w", err)
		}
	}
	
	// 验证搜索条件
	if req.Search != nil {
		if err := v.validateSearchCondition(req.Search); err != nil {
			return fmt.Errorf("invalid search condition: %w", err)
		}
	}
	
	return nil
}

// ValidateCursorRequest 验证游标分页请求
func (v *Validator) ValidateCursorRequest(req *CursorRequest) error {
	if req == nil {
		return errors.New("request cannot be nil")
	}
	
	// 验证限制数量
	if req.Limit < v.MinPageSize {
		return fmt.Errorf("limit must be at least %d", v.MinPageSize)
	}
	
	if req.Limit > v.MaxPageSize {
		return fmt.Errorf("limit must not exceed %d", v.MaxPageSize)
	}
	
	// 验证排序字段
	if req.SortBy == "" {
		return errors.New("sort_by is required for cursor pagination")
	}
	
	if err := v.validateFieldName(req.SortBy); err != nil {
		return fmt.Errorf("invalid sort_by field: %w", err)
	}
	
	if len(v.AllowedSortFields) > 0 && !v.isFieldAllowed(req.SortBy, v.AllowedSortFields) {
		return fmt.Errorf("sort field '%s' is not allowed", req.SortBy)
	}
	
	// 验证游标方向
	if req.Direction != "" && req.Direction != CursorNext && req.Direction != CursorPrev {
		return fmt.Errorf("invalid cursor direction: %s", req.Direction)
	}
	
	// 验证过滤条件
	if req.Filter != nil {
		if err := v.validateFilter(req.Filter); err != nil {
			return fmt.Errorf("invalid filter: %w", err)
		}
	}
	
	return nil
}

// validateSortFields 验证排序字段
func (v *Validator) validateSortFields(fields []SortField) error {
	if len(fields) == 0 {
		if !v.AllowEmptySort {
			return errors.New("sort fields cannot be empty")
		}
		return nil
	}
	
	fieldMap := make(map[string]bool)
	
	for i, field := range fields {
		// 验证字段名格式
		if err := v.validateFieldName(field.Field); err != nil {
			return fmt.Errorf("sort field %d: %w", i, err)
		}
		
		// 检查重复字段
		if fieldMap[field.Field] {
			return fmt.Errorf("duplicate sort field: %s", field.Field)
		}
		fieldMap[field.Field] = true
		
		// 检查字段是否允许
		if len(v.AllowedSortFields) > 0 && !v.isFieldAllowed(field.Field, v.AllowedSortFields) {
			return fmt.Errorf("sort field '%s' is not allowed", field.Field)
		}
		
		// 验证排序方向
		if field.Direction != "" && field.Direction != SortAsc && field.Direction != SortDesc {
			return fmt.Errorf("invalid sort direction for field '%s': %s", field.Field, field.Direction)
		}
	}
	
	return nil
}

// validateFilter 验证过滤条件
func (v *Validator) validateFilter(filter Filter) error {
	if filter == nil {
		if !v.AllowEmptyFilter {
			return errors.New("filter cannot be empty")
		}
		return nil
	}
	
	conditions := filter.GetConditions()
	if len(conditions) == 0 {
		if !v.AllowEmptyFilter {
			return errors.New("filter conditions cannot be empty")
		}
		return nil
	}
	
	for i, condition := range conditions {
		// 验证字段名格式
		if err := v.validateFieldName(condition.Field); err != nil {
			return fmt.Errorf("filter condition %d: %w", i, err)
		}
		
		// 检查字段是否允许
		if len(v.AllowedFilterFields) > 0 && !v.isFieldAllowed(condition.Field, v.AllowedFilterFields) {
			return fmt.Errorf("filter field '%s' is not allowed", condition.Field)
		}
		
		// 验证条件
		if err := condition.Validate(); err != nil {
			return fmt.Errorf("filter condition %d: %w", i, err)
		}
	}
	
	return nil
}

// validateSearchCondition 验证搜索条件
func (v *Validator) validateSearchCondition(search *SearchCondition) error {
	if search == nil {
		return nil
	}
	
	// 验证搜索关键词
	if strings.TrimSpace(search.Query) == "" {
		return errors.New("search query cannot be empty")
	}
	
	// 验证搜索字段
	for i, field := range search.Fields {
		if err := v.validateFieldName(field); err != nil {
			return fmt.Errorf("search field %d: %w", i, err)
		}
		
		if len(v.AllowedFilterFields) > 0 && !v.isFieldAllowed(field, v.AllowedFilterFields) {
			return fmt.Errorf("search field '%s' is not allowed", field)
		}
	}
	
	// 验证搜索模式
	if search.Mode != "" {
		validModes := []SearchMode{SearchExact, SearchFuzzy, SearchPrefix, SearchFull}
		isValid := false
		for _, mode := range validModes {
			if search.Mode == mode {
				isValid = true
				break
			}
		}
		if !isValid {
			return fmt.Errorf("invalid search mode: %s", search.Mode)
		}
	}
	
	return nil
}

// validateFieldName 验证字段名
func (v *Validator) validateFieldName(field string) error {
	if field == "" {
		return errors.New("field name cannot be empty")
	}
	
	if v.FieldNamePattern != nil && !v.FieldNamePattern.MatchString(field) {
		return fmt.Errorf("invalid field name format: %s", field)
	}
	
	return nil
}

// isFieldAllowed 检查字段是否在允许列表中
func (v *Validator) isFieldAllowed(field string, allowedFields []string) bool {
	for _, allowed := range allowedFields {
		if field == allowed {
			return true
		}
		// 支持通配符匹配（简单的前缀匹配）
		if strings.HasSuffix(allowed, "*") {
			prefix := strings.TrimSuffix(allowed, "*")
			if strings.HasPrefix(field, prefix) {
				return true
			}
		}
	}
	return false
}
