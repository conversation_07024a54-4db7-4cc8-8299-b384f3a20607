package pagination

import (
	"context"
	"testing"
	"time"
)

func TestRequest_Validate(t *testing.T) {
	tests := []struct {
		name    string
		req     *Request
		wantErr bool
	}{
		{
			name: "valid request",
			req: &Request{
				Page:     1,
				PageSize: 10,
			},
			wantErr: false,
		},
		{
			name: "invalid page",
			req: &Request{
				Page:     0,
				PageSize: 10,
			},
			wantErr: true,
		},
		{
			name: "invalid page size",
			req: &Request{
				Page:     1,
				PageSize: 0,
			},
			wantErr: true,
		},
		{
			name: "page size too large",
			req: &Request{
				Page:     1,
				PageSize: 1001,
			},
			wantErr: true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			err := tt.req.Validate()
			if (err != nil) != tt.wantErr {
				t.Errorf("Request.Validate() error = %v, wantErr %v", err, tt.wantErr)
			}
		})
	}
}

func TestRequest_GetOffset(t *testing.T) {
	tests := []struct {
		name     string
		page     int
		pageSize int
		want     int
	}{
		{
			name:     "first page",
			page:     1,
			pageSize: 10,
			want:     0,
		},
		{
			name:     "second page",
			page:     2,
			pageSize: 10,
			want:     10,
		},
		{
			name:     "third page with different size",
			page:     3,
			pageSize: 20,
			want:     40,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			req := &Request{
				Page:     tt.page,
				PageSize: tt.pageSize,
			}
			if got := req.GetOffset(); got != tt.want {
				t.Errorf("Request.GetOffset() = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestRequest_SetDefaults(t *testing.T) {
	req := &Request{}
	req.SetDefaults()

	if req.Page != 1 {
		t.Errorf("Expected default page to be 1, got %d", req.Page)
	}

	if req.PageSize != 10 {
		t.Errorf("Expected default page size to be 10, got %d", req.PageSize)
	}

	if req.Mode != ModeOffset {
		t.Errorf("Expected default mode to be %s, got %s", ModeOffset, req.Mode)
	}

	if req.Options == nil {
		t.Error("Expected options to be initialized")
	}
}

func TestSortField_Validate(t *testing.T) {
	tests := []struct {
		name    string
		field   SortField
		wantErr bool
	}{
		{
			name: "valid field",
			field: SortField{
				Field:     "name",
				Direction: SortAsc,
			},
			wantErr: false,
		},
		{
			name: "empty field",
			field: SortField{
				Field: "",
			},
			wantErr: true,
		},
		{
			name: "invalid direction",
			field: SortField{
				Field:     "name",
				Direction: "invalid",
			},
			wantErr: true,
		},
		{
			name: "field with dot notation",
			field: SortField{
				Field:     "user.name",
				Direction: SortDesc,
			},
			wantErr: false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			err := tt.field.Validate()
			if (err != nil) != tt.wantErr {
				t.Errorf("SortField.Validate() error = %v, wantErr %v", err, tt.wantErr)
			}
		})
	}
}

func TestSortField_ToSQL(t *testing.T) {
	tests := []struct {
		name  string
		field SortField
		want  string
	}{
		{
			name: "ascending",
			field: SortField{
				Field:     "name",
				Direction: SortAsc,
			},
			want: "name ASC",
		},
		{
			name: "descending",
			field: SortField{
				Field:     "created_at",
				Direction: SortDesc,
			},
			want: "created_at DESC",
		},
		{
			name: "default direction",
			field: SortField{
				Field: "id",
			},
			want: "id ASC",
		},
		{
			name: "with nulls last",
			field: SortField{
				Field:     "updated_at",
				Direction: SortDesc,
				NullsLast: true,
			},
			want: "updated_at DESC NULLS LAST",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if got := tt.field.ToSQL(); got != tt.want {
				t.Errorf("SortField.ToSQL() = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestParseSortString(t *testing.T) {
	tests := []struct {
		name    string
		sortStr string
		want    int // 期望的字段数量
		wantErr bool
	}{
		{
			name:    "empty string",
			sortStr: "",
			want:    0,
			wantErr: false,
		},
		{
			name:    "single field",
			sortStr: "name",
			want:    1,
			wantErr: false,
		},
		{
			name:    "single field with direction",
			sortStr: "name:desc",
			want:    1,
			wantErr: false,
		},
		{
			name:    "multiple fields",
			sortStr: "name:asc,created_at:desc",
			want:    2,
			wantErr: false,
		},
		{
			name:    "invalid format",
			sortStr: "name:invalid:direction",
			want:    0,
			wantErr: true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got, err := ParseSortString(tt.sortStr)
			if (err != nil) != tt.wantErr {
				t.Errorf("ParseSortString() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if len(got) != tt.want {
				t.Errorf("ParseSortString() got %d fields, want %d", len(got), tt.want)
			}
		})
	}
}

func TestBasicFilter_AddCondition(t *testing.T) {
	filter := NewBasicFilter()

	filter.AddCondition("name", OpEqual, "test")
	filter.AddCondition("age", OpGreater, 18)

	conditions := filter.GetConditions()
	if len(conditions) != 2 {
		t.Errorf("Expected 2 conditions, got %d", len(conditions))
	}

	if conditions[0].Field != "name" {
		t.Errorf("Expected first condition field to be 'name', got '%s'", conditions[0].Field)
	}

	if conditions[0].Operator != OpEqual {
		t.Errorf("Expected first condition operator to be %s, got %s", OpEqual, conditions[0].Operator)
	}
}

func TestNewResponse(t *testing.T) {
	items := []string{"item1", "item2", "item3"}
	total := int64(100)
	req := &Request{
		Page:     2,
		PageSize: 10,
	}

	response := NewResponse(items, total, req)

	if len(response.Items) != 3 {
		t.Errorf("Expected 3 items, got %d", len(response.Items))
	}

	if response.Meta.Total != total {
		t.Errorf("Expected total %d, got %d", total, response.Meta.Total)
	}

	if response.Meta.Page != 2 {
		t.Errorf("Expected page 2, got %d", response.Meta.Page)
	}

	if response.Meta.TotalPages != 10 {
		t.Errorf("Expected total pages 10, got %d", response.Meta.TotalPages)
	}

	if !response.Meta.HasNext {
		t.Error("Expected HasNext to be true")
	}

	if !response.Meta.HasPrevious {
		t.Error("Expected HasPrevious to be true")
	}
}

func TestMemoryCache(t *testing.T) {
	cache := NewMemoryCache()
	ctx := context.Background()

	// 测试设置和获取
	key := "test_key"
	value := []byte("test_value")
	ttl := time.Minute

	err := cache.Set(ctx, key, value, ttl)
	if err != nil {
		t.Errorf("Failed to set cache: %v", err)
	}

	retrieved, err := cache.Get(ctx, key)
	if err != nil {
		t.Errorf("Failed to get cache: %v", err)
	}

	if string(retrieved) != string(value) {
		t.Errorf("Expected %s, got %s", string(value), string(retrieved))
	}

	// 测试删除
	err = cache.Delete(ctx, key)
	if err != nil {
		t.Errorf("Failed to delete cache: %v", err)
	}

	_, err = cache.Get(ctx, key)
	if err == nil {
		t.Error("Expected error when getting deleted key")
	}
}
