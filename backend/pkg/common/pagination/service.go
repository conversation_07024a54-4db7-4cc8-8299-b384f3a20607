package pagination

import (
	"context"
	"crypto/md5"
	"encoding/json"
	"fmt"
	"time"
)

// Config 分页配置
type Config struct {
	// 默认页大小
	DefaultPageSize int `json:"default_page_size" yaml:"default_page_size"`
	
	// 最大页大小
	MaxPageSize int `json:"max_page_size" yaml:"max_page_size"`
	
	// 是否启用缓存
	EnableCache bool `json:"enable_cache" yaml:"enable_cache"`
	
	// 缓存TTL
	CacheTTL time.Duration `json:"cache_ttl" yaml:"cache_ttl"`
	
	// 是否启用性能监控
	EnablePerformance bool `json:"enable_performance" yaml:"enable_performance"`
	
	// 缓存键前缀
	CacheKeyPrefix string `json:"cache_key_prefix" yaml:"cache_key_prefix"`
}

// DefaultConfig 默认配置
func DefaultConfig() *Config {
	return &Config{
		DefaultPageSize:   10,
		MaxPageSize:       1000,
		EnableCache:       true,
		CacheTTL:          5 * time.Minute,
		EnablePerformance: true,
		CacheKeyPrefix:    "pagination:",
	}
}

// Service 分页服务
type Service[T any] struct {
	repository Repository[T]
	cache      CacheProvider
	config     *Config
}

// NewService 创建分页服务
func NewService[T any](repo Repository[T], cache CacheProvider, config *Config) *Service[T] {
	if config == nil {
		config = DefaultConfig()
	}
	
	return &Service[T]{
		repository: repo,
		cache:      cache,
		config:     config,
	}
}

// Paginate 执行分页查询
func (s *Service[T]) Paginate(ctx context.Context, req *Request) (*Response[T], error) {
	startTime := time.Now()
	
	// 验证请求
	if err := req.Validate(); err != nil {
		return nil, fmt.Errorf("invalid pagination request: %w", err)
	}
	
	// 设置默认值
	s.setDefaults(req)
	
	// 尝试从缓存获取
	if s.shouldUseCache(req) {
		if cached := s.getCachedResult(ctx, req); cached != nil {
			return cached, nil
		}
	}
	
	// 查询总数（如果需要）
	var total int64
	var err error
	if req.Options == nil || req.Options.CountTotal {
		total, err = s.repository.Count(ctx, req.Filter)
		if err != nil {
			return nil, fmt.Errorf("failed to count records: %w", err)
		}
	}
	
	// 查询数据
	items, err := s.repository.FindWithPagination(ctx, req, req.Filter)
	if err != nil {
		return nil, fmt.Errorf("failed to find records: %w", err)
	}
	
	// 构建响应
	response := NewResponse(items, total, req)
	
	// 添加性能信息
	if s.config.EnablePerformance {
		response = response.WithPerformance(&Performance{
			TotalTime: time.Since(startTime),
			FromCache: false,
		})
	}
	
	// 缓存结果
	if s.shouldUseCache(req) {
		s.cacheResult(ctx, req, response)
	}
	
	return response, nil
}

// CursorPaginate 执行游标分页
func (s *Service[T]) CursorPaginate(ctx context.Context, req *CursorRequest) (*CursorResponse[T], error) {
	// 验证请求
	if err := req.Validate(); err != nil {
		return nil, fmt.Errorf("invalid cursor pagination request: %w", err)
	}
	
	// 设置默认值
	req.SetDefaults()
	
	// 查询数据
	items, err := s.repository.FindWithCursor(ctx, req, req.Filter)
	if err != nil {
		return nil, fmt.Errorf("failed to find records with cursor: %w", err)
	}
	
	// 构建游标
	nextCursor, prevCursor := s.buildCursors(items, req)
	
	return NewCursorResponse(items, nextCursor, prevCursor, req.Limit), nil
}

// setDefaults 设置默认值
func (s *Service[T]) setDefaults(req *Request) {
	if req.PageSize <= 0 {
		req.PageSize = s.config.DefaultPageSize
	}
	
	if req.PageSize > s.config.MaxPageSize {
		req.PageSize = s.config.MaxPageSize
	}
	
	if req.Page <= 0 {
		req.Page = 1
	}
	
	if req.Mode == "" {
		req.Mode = ModeOffset
	}
	
	if req.Options == nil {
		req.Options = &RequestOptions{
			CountTotal: true,
			UseCache:   s.config.EnableCache,
			CacheTTL:   s.config.CacheTTL,
		}
	}
}

// shouldUseCache 是否应该使用缓存
func (s *Service[T]) shouldUseCache(req *Request) bool {
	return s.config.EnableCache && 
		   s.cache != nil && 
		   req.Options != nil && 
		   req.Options.UseCache
}

// getCachedResult 从缓存获取结果
func (s *Service[T]) getCachedResult(ctx context.Context, req *Request) *Response[T] {
	if s.cache == nil {
		return nil
	}
	
	key := s.buildCacheKey(req)
	data, err := s.cache.Get(ctx, key)
	if err != nil {
		return nil
	}
	
	var response Response[T]
	if err := json.Unmarshal(data, &response); err != nil {
		return nil
	}
	
	// 标记为来自缓存
	if response.Meta != nil && response.Meta.Performance != nil {
		response.Meta.Performance.FromCache = true
	}
	
	return &response
}

// cacheResult 缓存结果
func (s *Service[T]) cacheResult(ctx context.Context, req *Request, response *Response[T]) {
	if s.cache == nil {
		return
	}
	
	key := s.buildCacheKey(req)
	data, err := json.Marshal(response)
	if err != nil {
		return
	}
	
	ttl := s.config.CacheTTL
	if req.Options != nil && req.Options.CacheTTL > 0 {
		ttl = req.Options.CacheTTL
	}
	
	s.cache.Set(ctx, key, data, ttl)
}

// buildCacheKey 构建缓存键
func (s *Service[T]) buildCacheKey(req *Request) string {
	// 创建请求的哈希值作为缓存键
	data, _ := json.Marshal(req)
	hash := fmt.Sprintf("%x", md5.Sum(data))
	return s.config.CacheKeyPrefix + hash
}

// buildCursors 构建游标
func (s *Service[T]) buildCursors(items []T, req *CursorRequest) (string, string) {
	if len(items) == 0 {
		return "", ""
	}
	
	// 这里需要根据具体的实体类型来提取游标值
	// 简化实现，实际需要反射或接口来获取排序字段的值
	// TODO: 实现具体的游标提取逻辑
	
	var nextCursor, prevCursor string
	
	// 如果有数据且数量等于限制，说明可能有下一页
	if len(items) == req.Limit {
		nextCursor = "next_cursor_placeholder"
	}
	
	// 如果不是第一页，说明有上一页
	if req.Cursor != "" {
		prevCursor = "prev_cursor_placeholder"
	}
	
	return nextCursor, prevCursor
}

// GetConfig 获取配置
func (s *Service[T]) GetConfig() *Config {
	return s.config
}

// SetConfig 设置配置
func (s *Service[T]) SetConfig(config *Config) {
	if config != nil {
		s.config = config
	}
}

// ClearCache 清除缓存
func (s *Service[T]) ClearCache(ctx context.Context, req *Request) error {
	if s.cache == nil {
		return nil
	}
	
	key := s.buildCacheKey(req)
	return s.cache.Delete(ctx, key)
}
