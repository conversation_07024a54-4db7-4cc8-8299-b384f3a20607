# Pagination Package

这是一个功能完整的Go分页包，支持多种分页模式，包括传统偏移分页、游标分页和流式分页。

## 特性

- 🚀 **多种分页模式**：支持传统分页、游标分页、流式分页
- 🔍 **灵活的排序和过滤**：支持多字段排序、复杂过滤条件
- 🎯 **类型安全**：使用Go泛型确保类型安全
- 💾 **缓存支持**：内置缓存机制提升性能
- 📊 **性能监控**：内置性能指标收集
- 🔧 **高度可配置**：支持自定义配置和验证规则
- 🌐 **HATEOAS支持**：自动生成分页链接

## 快速开始

### 基础使用

```go
package main

import (
    "context"
    "fmt"
    "backend/pkg/common/pagination"
)

// 定义你的实体类型
type User struct {
    ID   int    `json:"id"`
    Name string `json:"name"`
    Age  int    `json:"age"`
}

// 实现Repository接口
type UserRepository struct {
    // 你的数据访问逻辑
}

func (r *UserRepository) Count(ctx context.Context, filter pagination.Filter) (int64, error) {
    // 实现计数逻辑
    return 100, nil
}

func (r *UserRepository) FindWithPagination(ctx context.Context, req *pagination.Request, filter pagination.Filter) ([]User, error) {
    // 实现分页查询逻辑
    return []User{
        {ID: 1, Name: "Alice", Age: 25},
        {ID: 2, Name: "Bob", Age: 30},
    }, nil
}

func (r *UserRepository) FindWithCursor(ctx context.Context, req *pagination.CursorRequest, filter pagination.Filter) ([]User, error) {
    // 实现游标分页逻辑
    return []User{}, nil
}

func main() {
    // 创建仓储
    repo := &UserRepository{}
    
    // 创建缓存（可选）
    cache := pagination.NewMemoryCache()
    
    // 创建分页服务
    service := pagination.NewService[User](repo, cache, nil)
    
    // 创建分页请求
    req := &pagination.Request{
        Page:     1,
        PageSize: 10,
        Sort: []pagination.SortField{
            {Field: "name", Direction: pagination.SortAsc},
        },
    }
    
    // 执行分页查询
    ctx := context.Background()
    response, err := service.Paginate(ctx, req)
    if err != nil {
        panic(err)
    }
    
    fmt.Printf("Total: %d, Page: %d, Items: %d\n", 
        response.Meta.Total, 
        response.Meta.Page, 
        len(response.Items))
}
```

### HTTP处理器中使用

```go
package handler

import (
    "net/http"
    "github.com/gin-gonic/gin"
    "backend/pkg/common/pagination"
)

func (h *UserHandler) ListUsers(c *gin.Context) {
    // 解析分页参数
    req, err := pagination.ParseQueryParams(c.Request.URL.Query())
    if err != nil {
        c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
        return
    }
    
    // 执行分页查询
    response, err := h.userService.Paginate(c.Request.Context(), req)
    if err != nil {
        c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
        return
    }
    
    // 添加HATEOAS链接
    baseURL := "http://localhost:8080/api/users"
    links := pagination.BuildLinks(req, response.Meta, baseURL)
    response = response.WithLinks(links)
    
    c.JSON(http.StatusOK, response)
}
```

### 过滤和搜索

```go
// 创建过滤器
filter := pagination.NewBasicFilter()
filter.AddCondition("age", pagination.OpGreater, 18)
filter.AddCondition("status", pagination.OpEqual, "active")
filter.AddInCondition("role", []interface{}{"admin", "user"})

// 创建搜索条件
search := &pagination.SearchCondition{
    Query:  "john",
    Fields: []string{"name", "email"},
    Mode:   pagination.SearchFuzzy,
}

// 创建请求
req := &pagination.Request{
    Page:     1,
    PageSize: 20,
    Filter:   filter,
    Search:   search,
    Sort: []pagination.SortField{
        {Field: "created_at", Direction: pagination.SortDesc},
    },
}
```

### 游标分页

```go
// 创建游标分页请求
cursorReq := &pagination.CursorRequest{
    Cursor:    "eyJpZCI6MTAwfQ==", // base64编码的游标
    Limit:     20,
    SortBy:    "id",
    SortDesc:  false,
    Direction: pagination.CursorNext,
}

// 执行游标分页
response, err := service.CursorPaginate(ctx, cursorReq)
if err != nil {
    panic(err)
}

fmt.Printf("Items: %d, HasMore: %t, NextCursor: %s\n",
    len(response.Items),
    response.HasMore,
    response.Cursors.Next)
```

### 自定义配置

```go
config := &pagination.Config{
    DefaultPageSize:   20,
    MaxPageSize:       500,
    EnableCache:       true,
    CacheTTL:          10 * time.Minute,
    EnablePerformance: true,
    CacheKeyPrefix:    "myapp:pagination:",
}

service := pagination.NewService[User](repo, cache, config)
```

### 验证器使用

```go
validator := pagination.NewValidator().
    WithAllowedSortFields("id", "name", "created_at", "updated_at").
    WithAllowedFilterFields("status", "role", "age", "name").
    WithPageSizeRange(1, 100)

// 验证请求
if err := validator.ValidateRequest(req); err != nil {
    return fmt.Errorf("invalid request: %w", err)
}
```

## API参考

### 核心类型

- `Request`: 通用分页请求
- `Response[T]`: 分页响应
- `CursorRequest`: 游标分页请求
- `CursorResponse[T]`: 游标分页响应
- `Service[T]`: 分页服务
- `Filter`: 过滤条件接口
- `SortField`: 排序字段

### 接口

- `Paginator[T]`: 分页器接口
- `Repository[T]`: 仓储接口
- `CacheProvider`: 缓存提供者接口

### 工具函数

- `ParseQueryParams()`: 解析URL查询参数
- `BuildQueryParams()`: 构建URL查询参数
- `BuildLinks()`: 构建HATEOAS链接
- `ParseSortString()`: 解析排序字符串

## 最佳实践

1. **使用验证器**：始终验证分页请求参数
2. **合理设置页大小限制**：防止大页面查询影响性能
3. **使用缓存**：对于频繁查询的数据启用缓存
4. **索引优化**：确保排序字段有适当的数据库索引
5. **游标分页**：对于大数据集使用游标分页而非偏移分页
6. **性能监控**：启用性能监控了解查询性能

## 注意事项

- 游标分页需要实现具体的游标编码/解码逻辑
- 缓存键的生成基于请求参数的哈希值
- 过滤条件的SQL转换需要根据具体ORM实现
- 大数据量时建议关闭总数计算以提升性能
