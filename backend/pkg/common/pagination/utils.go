package pagination

import (
	"fmt"
	"net/url"
	"strconv"
	"strings"
)

// ParseQueryParams 从URL查询参数解析分页请求
func ParseQueryParams(values url.Values) (*Request, error) {
	req := &Request{}
	
	// 解析页码
	if pageStr := values.Get("page"); pageStr != "" {
		page, err := strconv.Atoi(pageStr)
		if err != nil {
			return nil, fmt.Errorf("invalid page parameter: %s", pageStr)
		}
		req.Page = page
	}
	
	// 解析页大小
	if pageSizeStr := values.Get("page_size"); pageSizeStr != "" {
		pageSize, err := strconv.Atoi(pageSizeStr)
		if err != nil {
			return nil, fmt.Errorf("invalid page_size parameter: %s", pageSizeStr)
		}
		req.PageSize = pageSize
	}
	
	// 解析排序
	if sortStr := values.Get("sort"); sortStr != "" {
		sortFields, err := ParseSortString(sortStr)
		if err != nil {
			return nil, fmt.Errorf("invalid sort parameter: %w", err)
		}
		req.Sort = sortFields
	}
	
	// 解析搜索
	if query := values.Get("q"); query != "" {
		req.Search = &SearchCondition{
			Query: query,
		}
		
		// 解析搜索字段
		if fields := values.Get("search_fields"); fields != "" {
			req.Search.Fields = strings.Split(fields, ",")
		}
		
		// 解析搜索模式
		if mode := values.Get("search_mode"); mode != "" {
			req.Search.Mode = SearchMode(mode)
		}
	}
	
	// 设置默认值
	req.SetDefaults()
	
	return req, nil
}

// BuildQueryParams 将分页请求转换为URL查询参数
func BuildQueryParams(req *Request) url.Values {
	values := url.Values{}
	
	if req.Page > 0 {
		values.Set("page", strconv.Itoa(req.Page))
	}
	
	if req.PageSize > 0 {
		values.Set("page_size", strconv.Itoa(req.PageSize))
	}
	
	if len(req.Sort) > 0 {
		sortParts := make([]string, len(req.Sort))
		for i, field := range req.Sort {
			if field.Direction == SortDesc {
				sortParts[i] = field.Field + ":desc"
			} else {
				sortParts[i] = field.Field + ":asc"
			}
		}
		values.Set("sort", strings.Join(sortParts, ","))
	}
	
	if req.Search != nil && req.Search.Query != "" {
		values.Set("q", req.Search.Query)
		
		if len(req.Search.Fields) > 0 {
			values.Set("search_fields", strings.Join(req.Search.Fields, ","))
		}
		
		if req.Search.Mode != "" {
			values.Set("search_mode", string(req.Search.Mode))
		}
	}
	
	return values
}

// BuildLinks 构建HATEOAS链接
func BuildLinks(req *Request, meta *Meta, baseURL string) *Links {
	if meta == nil {
		return nil
	}
	
	links := &Links{}
	
	// 构建基础URL和查询参数
	values := BuildQueryParams(req)
	
	// 当前页链接
	values.Set("page", strconv.Itoa(req.Page))
	links.Self = fmt.Sprintf("%s?%s", baseURL, values.Encode())
	
	// 第一页链接
	values.Set("page", "1")
	links.First = fmt.Sprintf("%s?%s", baseURL, values.Encode())
	
	// 上一页链接
	if meta.HasPrevious {
		values.Set("page", strconv.Itoa(req.Page-1))
		links.Previous = fmt.Sprintf("%s?%s", baseURL, values.Encode())
	}
	
	// 下一页链接
	if meta.HasNext {
		values.Set("page", strconv.Itoa(req.Page+1))
		links.Next = fmt.Sprintf("%s?%s", baseURL, values.Encode())
	}
	
	// 最后一页链接
	if meta.TotalPages > 0 {
		values.Set("page", strconv.Itoa(meta.TotalPages))
		links.Last = fmt.Sprintf("%s?%s", baseURL, values.Encode())
	}
	
	return links
}

// CalculateOffset 计算偏移量
func CalculateOffset(page, pageSize int) int {
	if page < 1 {
		page = 1
	}
	if pageSize < 1 {
		pageSize = 10
	}
	return (page - 1) * pageSize
}

// CalculateTotalPages 计算总页数
func CalculateTotalPages(total int64, pageSize int) int {
	if pageSize <= 0 {
		return 0
	}
	return int((total + int64(pageSize) - 1) / int64(pageSize))
}

// ValidatePageRange 验证页码范围
func ValidatePageRange(page, totalPages int) error {
	if page < 1 {
		return fmt.Errorf("page must be greater than 0")
	}
	
	if totalPages > 0 && page > totalPages {
		return fmt.Errorf("page %d exceeds total pages %d", page, totalPages)
	}
	
	return nil
}

// NormalizeSortFields 标准化排序字段
func NormalizeSortFields(fields []SortField) []SortField {
	if len(fields) == 0 {
		return fields
	}
	
	normalized := make([]SortField, len(fields))
	for i, field := range fields {
		normalized[i] = SortField{
			Field:     strings.TrimSpace(field.Field),
			Direction: field.GetDirection(),
			Priority:  field.Priority,
			NullsLast: field.NullsLast,
		}
	}
	
	return normalized
}

// MergeSortFields 合并排序字段（去重）
func MergeSortFields(fields1, fields2 []SortField) []SortField {
	fieldMap := make(map[string]SortField)
	
	// 添加第一组字段
	for _, field := range fields1 {
		fieldMap[field.Field] = field
	}
	
	// 添加第二组字段（覆盖重复的）
	for _, field := range fields2 {
		fieldMap[field.Field] = field
	}
	
	// 转换回切片
	result := make([]SortField, 0, len(fieldMap))
	for _, field := range fieldMap {
		result = append(result, field)
	}
	
	return result
}

// ExtractUniqueFields 提取唯一字段名
func ExtractUniqueFields(conditions []FilterCondition) []string {
	fieldMap := make(map[string]bool)
	
	for _, condition := range conditions {
		fieldMap[condition.Field] = true
	}
	
	fields := make([]string, 0, len(fieldMap))
	for field := range fieldMap {
		fields = append(fields, field)
	}
	
	return fields
}

// IsValidPageSize 验证页大小是否有效
func IsValidPageSize(pageSize, min, max int) bool {
	return pageSize >= min && pageSize <= max
}

// ClampPageSize 限制页大小在有效范围内
func ClampPageSize(pageSize, min, max int) int {
	if pageSize < min {
		return min
	}
	if pageSize > max {
		return max
	}
	return pageSize
}

// ClampPage 限制页码在有效范围内
func ClampPage(page, maxPage int) int {
	if page < 1 {
		return 1
	}
	if maxPage > 0 && page > maxPage {
		return maxPage
	}
	return page
}

// FormatSortString 格式化排序字符串
func FormatSortString(fields []SortField) string {
	if len(fields) == 0 {
		return ""
	}
	
	parts := make([]string, len(fields))
	for i, field := range fields {
		if field.Direction == SortDesc {
			parts[i] = field.Field + ":desc"
		} else {
			parts[i] = field.Field + ":asc"
		}
	}
	
	return strings.Join(parts, ",")
}

// ParseCursorValue 解析游标值（简单实现）
func ParseCursorValue(cursor string) (map[string]interface{}, error) {
	if cursor == "" {
		return nil, nil
	}
	
	// 这里应该实现具体的游标解析逻辑
	// 可以是base64编码的JSON，或者其他格式
	// 简化实现，返回空map
	return make(map[string]interface{}), nil
}

// BuildCursorValue 构建游标值（简单实现）
func BuildCursorValue(values map[string]interface{}) (string, error) {
	if len(values) == 0 {
		return "", nil
	}
	
	// 这里应该实现具体的游标构建逻辑
	// 可以是base64编码的JSON，或者其他格式
	// 简化实现，返回固定字符串
	return "cursor_placeholder", nil
}
