package pagination

import (
	"time"
)

// Response 通用分页响应
type Response[T any] struct {
	// 数据列表
	Items []T `json:"items"`
	
	// 分页元信息
	Meta *Meta `json:"meta"`
	
	// 链接信息（HATEOAS）
	Links *Links `json:"links,omitempty"`
	
	// 响应时间戳
	Timestamp time.Time `json:"timestamp"`
}

// CursorResponse 游标分页响应
type CursorResponse[T any] struct {
	// 数据列表
	Items []T `json:"items"`
	
	// 游标信息
	Cursors *CursorMeta `json:"cursors"`
	
	// 是否有更多数据
	HasMore bool `json:"has_more"`
	
	// 响应时间戳
	Timestamp time.Time `json:"timestamp"`
}

// StreamResponse 流式分页响应
type StreamResponse[T any] struct {
	// 数据列表
	Items []T `json:"items"`
	
	// 流信息
	Stream *StreamMeta `json:"stream"`
	
	// 响应时间戳
	Timestamp time.Time `json:"timestamp"`
}

// Meta 分页元信息
type Meta struct {
	// 当前页码
	Page int `json:"page"`
	
	// 每页数量
	PageSize int `json:"page_size"`
	
	// 总记录数
	Total int64 `json:"total"`
	
	// 总页数
	TotalPages int `json:"total_pages"`
	
	// 是否有下一页
	HasNext bool `json:"has_next"`
	
	// 是否有上一页
	HasPrevious bool `json:"has_previous"`
	
	// 当前页记录数
	Count int `json:"count"`
	
	// 性能信息
	Performance *Performance `json:"performance,omitempty"`
}

// CursorMeta 游标元信息
type CursorMeta struct {
	// 当前游标
	Current string `json:"current,omitempty"`
	
	// 下一页游标
	Next string `json:"next,omitempty"`
	
	// 上一页游标
	Previous string `json:"previous,omitempty"`
}

// StreamMeta 流元信息
type StreamMeta struct {
	// 当前偏移量
	Offset int64 `json:"offset"`
	
	// 批次大小
	BatchSize int `json:"batch_size"`
	
	// 下一批次偏移量
	NextOffset int64 `json:"next_offset"`
	
	// 是否结束
	IsEnd bool `json:"is_end"`
}

// NewResponse 创建分页响应
func NewResponse[T any](items []T, total int64, request *Request) *Response[T] {
	meta := buildMeta(request, total, len(items))
	
	return &Response[T]{
		Items:     items,
		Meta:      meta,
		Timestamp: time.Now(),
	}
}

// NewCursorResponse 创建游标分页响应
func NewCursorResponse[T any](items []T, nextCursor, prevCursor string, limit int) *CursorResponse[T] {
	cursors := &CursorMeta{
		Next:     nextCursor,
		Previous: prevCursor,
	}
	
	hasMore := len(items) == limit
	
	return &CursorResponse[T]{
		Items:     items,
		Cursors:   cursors,
		HasMore:   hasMore,
		Timestamp: time.Now(),
	}
}

// NewStreamResponse 创建流式分页响应
func NewStreamResponse[T any](items []T, offset int64, batchSize int) *StreamResponse[T] {
	nextOffset := offset + int64(len(items))
	isEnd := len(items) < batchSize
	
	stream := &StreamMeta{
		Offset:     offset,
		BatchSize:  batchSize,
		NextOffset: nextOffset,
		IsEnd:      isEnd,
	}
	
	return &StreamResponse[T]{
		Items:     items,
		Stream:    stream,
		Timestamp: time.Now(),
	}
}

// buildMeta 构建分页元信息
func buildMeta(req *Request, total int64, count int) *Meta {
	totalPages := 0
	if req.PageSize > 0 && total > 0 {
		totalPages = int((total + int64(req.PageSize) - 1) / int64(req.PageSize))
	}
	
	return &Meta{
		Page:        req.Page,
		PageSize:    req.PageSize,
		Total:       total,
		TotalPages:  totalPages,
		HasNext:     req.Page < totalPages,
		HasPrevious: req.Page > 1,
		Count:       count,
	}
}

// WithPerformance 添加性能信息
func (r *Response[T]) WithPerformance(perf *Performance) *Response[T] {
	if r.Meta != nil {
		r.Meta.Performance = perf
	}
	return r
}

// WithLinks 添加链接信息
func (r *Response[T]) WithLinks(links *Links) *Response[T] {
	r.Links = links
	return r
}

// IsEmpty 检查是否为空结果
func (r *Response[T]) IsEmpty() bool {
	return len(r.Items) == 0
}

// GetTotalPages 获取总页数
func (r *Response[T]) GetTotalPages() int {
	if r.Meta != nil {
		return r.Meta.TotalPages
	}
	return 0
}

// GetTotal 获取总记录数
func (r *Response[T]) GetTotal() int64 {
	if r.Meta != nil {
		return r.Meta.Total
	}
	return 0
}

// HasNextPage 是否有下一页
func (r *Response[T]) HasNextPage() bool {
	if r.Meta != nil {
		return r.Meta.HasNext
	}
	return false
}

// HasPreviousPage 是否有上一页
func (r *Response[T]) HasPreviousPage() bool {
	if r.Meta != nil {
		return r.Meta.HasPrevious
	}
	return false
}

// GetCurrentPage 获取当前页码
func (r *Response[T]) GetCurrentPage() int {
	if r.Meta != nil {
		return r.Meta.Page
	}
	return 1
}

// GetPageSize 获取页大小
func (r *Response[T]) GetPageSize() int {
	if r.Meta != nil {
		return r.Meta.PageSize
	}
	return 10
}
