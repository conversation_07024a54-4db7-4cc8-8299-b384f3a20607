package pagination

import (
	"errors"
	"fmt"
	"reflect"
	"strings"
)

// BasicFilter 基础过滤器实现
type BasicFilter struct {
	Conditions []FilterCondition `json:"conditions"`
}

// NewBasicFilter 创建基础过滤器
func NewBasicFilter() *BasicFilter {
	return &BasicFilter{
		Conditions: make([]FilterCondition, 0),
	}
}

// AddCondition 添加过滤条件
func (f *BasicFilter) AddCondition(field string, operator FilterOperator, value interface{}) *BasicFilter {
	condition := FilterCondition{
		Field:    field,
		Operator: operator,
		Value:    value,
		Logic:    LogicAnd, // 默认AND逻辑
	}
	f.Conditions = append(f.Conditions, condition)
	return f
}

// AddOrCondition 添加OR逻辑的过滤条件
func (f *BasicFilter) AddOrCondition(field string, operator FilterOperator, value interface{}) *BasicFilter {
	condition := FilterCondition{
		Field:    field,
		Operator: operator,
		Value:    value,
		Logic:    LogicOr,
	}
	f.Conditions = append(f.Conditions, condition)
	return f
}

// AddInCondition 添加IN条件
func (f *BasicFilter) AddInCondition(field string, values []interface{}) *BasicFilter {
	condition := FilterCondition{
		Field:    field,
		Operator: OpIn,
		Values:   values,
		Logic:    LogicAnd,
	}
	f.Conditions = append(f.Conditions, condition)
	return f
}

// AddBetweenCondition 添加BETWEEN条件
func (f *BasicFilter) AddBetweenCondition(field string, start, end interface{}) *BasicFilter {
	condition := FilterCondition{
		Field:    field,
		Operator: OpBetween,
		Values:   []interface{}{start, end},
		Logic:    LogicAnd,
	}
	f.Conditions = append(f.Conditions, condition)
	return f
}

// AddLikeCondition 添加LIKE条件
func (f *BasicFilter) AddLikeCondition(field string, pattern string) *BasicFilter {
	return f.AddCondition(field, OpLike, pattern)
}

// AddEqualCondition 添加等于条件
func (f *BasicFilter) AddEqualCondition(field string, value interface{}) *BasicFilter {
	return f.AddCondition(field, OpEqual, value)
}

// AddNotEqualCondition 添加不等于条件
func (f *BasicFilter) AddNotEqualCondition(field string, value interface{}) *BasicFilter {
	return f.AddCondition(field, OpNotEqual, value)
}

// AddGreaterThanCondition 添加大于条件
func (f *BasicFilter) AddGreaterThanCondition(field string, value interface{}) *BasicFilter {
	return f.AddCondition(field, OpGreater, value)
}

// AddLessThanCondition 添加小于条件
func (f *BasicFilter) AddLessThanCondition(field string, value interface{}) *BasicFilter {
	return f.AddCondition(field, OpLess, value)
}

// AddIsNullCondition 添加IS NULL条件
func (f *BasicFilter) AddIsNullCondition(field string) *BasicFilter {
	return f.AddCondition(field, OpIsNull, nil)
}

// AddIsNotNullCondition 添加IS NOT NULL条件
func (f *BasicFilter) AddIsNotNullCondition(field string) *BasicFilter {
	return f.AddCondition(field, OpIsNotNull, nil)
}

// Validate 验证过滤条件
func (f *BasicFilter) Validate() error {
	if f == nil {
		return nil
	}
	
	for i, condition := range f.Conditions {
		if err := condition.Validate(); err != nil {
			return fmt.Errorf("condition %d: %w", i, err)
		}
	}
	
	return nil
}

// GetConditions 获取过滤条件
func (f *BasicFilter) GetConditions() []FilterCondition {
	if f == nil {
		return nil
	}
	return f.Conditions
}

// IsEmpty 检查是否为空
func (f *BasicFilter) IsEmpty() bool {
	return f == nil || len(f.Conditions) == 0
}

// Count 获取条件数量
func (f *BasicFilter) Count() int {
	if f == nil {
		return 0
	}
	return len(f.Conditions)
}

// Clear 清空所有条件
func (f *BasicFilter) Clear() {
	if f != nil {
		f.Conditions = f.Conditions[:0]
	}
}

// Validate 验证过滤条件
func (c *FilterCondition) Validate() error {
	if c.Field == "" {
		return errors.New("field cannot be empty")
	}
	
	// 验证字段名格式
	if !isValidFieldName(c.Field) {
		return fmt.Errorf("invalid field name: %s", c.Field)
	}
	
	// 验证操作符
	if !isValidOperator(c.Operator) {
		return fmt.Errorf("invalid operator: %s", c.Operator)
	}
	
	// 验证逻辑操作符
	if c.Logic != "" && c.Logic != LogicAnd && c.Logic != LogicOr {
		return fmt.Errorf("invalid logic operator: %s", c.Logic)
	}
	
	// 验证值
	if err := c.validateValue(); err != nil {
		return err
	}
	
	return nil
}

// validateValue 验证值
func (c *FilterCondition) validateValue() error {
	switch c.Operator {
	case OpIsNull, OpIsNotNull:
		// NULL操作不需要值
		return nil
		
	case OpIn, OpNotIn:
		if len(c.Values) == 0 {
			return fmt.Errorf("IN/NOT IN operator requires at least one value")
		}
		
	case OpBetween:
		if len(c.Values) != 2 {
			return fmt.Errorf("BETWEEN operator requires exactly two values")
		}
		
	default:
		if c.Value == nil {
			return fmt.Errorf("operator %s requires a value", c.Operator)
		}
		
		// 检查值是否为空
		if isEmptyValue(c.Value) {
			return fmt.Errorf("value cannot be empty for operator %s", c.Operator)
		}
	}
	
	return nil
}

// GetLogic 获取逻辑操作符，如果未设置则返回默认值
func (c *FilterCondition) GetLogic() LogicOperator {
	if c.Logic == "" {
		return LogicAnd
	}
	return c.Logic
}

// IsAndLogic 是否为AND逻辑
func (c *FilterCondition) IsAndLogic() bool {
	return c.GetLogic() == LogicAnd
}

// IsOrLogic 是否为OR逻辑
func (c *FilterCondition) IsOrLogic() bool {
	return c.GetLogic() == LogicOr
}

// isValidOperator 验证操作符是否有效
func isValidOperator(op FilterOperator) bool {
	validOps := []FilterOperator{
		OpEqual, OpNotEqual, OpGreater, OpGreaterEqual,
		OpLess, OpLessEqual, OpLike, OpNotLike,
		OpIn, OpNotIn, OpIsNull, OpIsNotNull,
		OpBetween, OpRegex,
	}
	
	for _, validOp := range validOps {
		if op == validOp {
			return true
		}
	}
	
	return false
}

// isEmptyValue 检查值是否为空
func isEmptyValue(value interface{}) bool {
	if value == nil {
		return true
	}
	
	v := reflect.ValueOf(value)
	switch v.Kind() {
	case reflect.String:
		return strings.TrimSpace(v.String()) == ""
	case reflect.Slice, reflect.Array, reflect.Map:
		return v.Len() == 0
	case reflect.Ptr, reflect.Interface:
		return v.IsNil()
	default:
		return false
	}
}
