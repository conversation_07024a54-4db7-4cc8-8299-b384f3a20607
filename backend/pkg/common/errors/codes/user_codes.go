package codes

// ============ 用户与认证错误 (user.) ============
const (
	// 业务错误 (user.biz)
	UserNotFound      = "user.biz.notFound"         // 用户不存在
	UserAlreadyExists = "user.biz.alreadyExists"    // 用户已存在
	PasswordMismatch  = "user.biz.passwordMismatch" // 密码错误
	AccountLocked     = "user.biz.locked"           // 账户被锁定
	AccountDisabled   = "user.biz.disabled"         // 账户被禁用
	AccountExpired    = "user.biz.expired"          // 账户已过期

	// 认证与会话 (user.auth)
	Unauthorized        = "user.auth.unauthorized"        // 未认证
	Forbidden           = "user.auth.forbidden"           // 权限不足
	TokenInvalid        = "user.auth.tokenInvalid"        // Token无效
	TokenExpired        = "user.auth.tokenExpired"        // Token过期
	RefreshTokenInvalid = "user.auth.refreshTokenInvalid" // 刷新Token无效
	PermissionDenied    = "user.auth.permissionDenied"    // 权限拒绝
	RoleNotFound        = "user.auth.roleNotFound"        // 角色不存在
	SessionExpired      = "user.auth.sessionExpired"      // 会话过期
	LoginRequired       = "user.auth.loginRequired"       // 需要登录

	// Token存储管理 (user.token.storage)
	TokenStorageNotFound        = "user.token.storage.notFound"       // Token不存在
	TokenStorageAlreadyExists   = "user.token.storage.alreadyExists"  // Token已存在
	TokenStorageBlacklisted     = "user.token.storage.blacklisted"    // Token已被拉黑
	TokenStorageError           = "user.token.storage.error"          // Token存储错误
	SessionManagerNotFound      = "user.session.notFound"             // 会话不存在
	SessionManagerConflict      = "user.session.conflict"             // 会话冲突
	SessionManagerLimitExceeded = "user.session.limitExceeded"        // 会话数量超限
	DeviceNotAuthorized         = "user.session.deviceNotAuthorized"  // 设备未授权
	ConcurrentLoginLimit        = "user.session.concurrentLoginLimit" // 并发登录限制

	// 前置认证 (user.auth.pre)
	PreAuthRequired      = "user.auth.pre.required"             // 需要前置认证
	PreAuthExpired       = "user.auth.pre.expired"              // 前置认证已过期
	PreAuthInvalid       = "user.auth.pre.invalid"              // 前置认证无效
	PreAuthStepInvalid   = "user.auth.pre.stepInvalid"          // 认证步骤无效
	PreAuthCompleted     = "user.auth.pre.completed"            // 前置认证已完成
	VerificationRequired = "user.auth.pre.verificationRequired" // 需要验证
	VerificationFailed   = "user.auth.pre.verificationFailed"   // 验证失败
	VerificationExpired  = "user.auth.pre.verificationExpired"  // 验证码已过期
	TooManyAttempts      = "user.auth.pre.tooManyAttempts"      // 尝试次数过多
)
