package codes

// ============ 租户与资源错误 (tenant.) ============
const (
	// 租户业务错误 (tenant.biz)
	TenantNotFound = "tenant.biz.notFound" // 租户不存在
	TenantExpired  = "tenant.biz.expired"  // 租户已过期

	// 资源管理 (tenant.resource)
	ResourceNotFound      = "tenant.resource.notFound"        // 资源未找到
	ResourceAlreadyExists = "tenant.resource.alreadyExists"   // 资源已存在
	ResourceConflict      = "tenant.resource.conflict"        // 资源冲突
	ResourceLocked        = "tenant.resource.locked"          // 资源被锁定
	ResourceExpired       = "tenant.resource.expired"         // 资源已过期
	ResourceQuotaExceeded = "tenant.resource.quotaExceeded"   // 资源配额超限
	DuplicateEntry        = "tenant.resource.duplicateEntry"  // 重复条目
	VersionConflict       = "tenant.resource.versionConflict" // 版本冲突
	ResourceInUse         = "tenant.resource.inUse"           // 资源正在使用中
	ResourceUnavailable   = "tenant.resource.unavailable"     // 资源不可用
)
