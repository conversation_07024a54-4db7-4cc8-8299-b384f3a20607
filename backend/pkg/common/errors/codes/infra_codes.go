package codes

// ============ 基础设施与通用错误 (infra.) ============
const (
	// 系统相关 (infra.system)
	SystemError        = "infra.system.internal"      // 系统内部错误
	ConfigError        = "infra.system.config"        // 配置错误
	ServiceUnavailable = "infra.system.unavailable"   // 服务不可用
	Timeout            = "infra.system.timeout"       // 请求超时
	Panic              = "infra.system.panic"         // 系统panic错误
	Serialization      = "infra.system.serialization" // 序列化/反序列化错误
	FileSystem         = "infra.system.filesystem"    // 文件系统操作错误
	Network            = "infra.system.network"       // 网络连接错误
	Security           = "infra.system.security"      // 安全相关错误
	Encryption         = "infra.system.encryption"    // 加密/解密错误
	Concurrency        = "infra.system.concurrency"   // 并发控制错误

	// 数据库相关 (infra.database)
	DatabaseConnection   = "infra.database.connection"   // 数据库连接错误
	DatabaseQuery        = "infra.database.query"        // 数据库查询错误
	DatabaseTransaction  = "infra.database.transaction"  // 数据库事务错误
	DatabaseConstraint   = "infra.database.constraint"   // 数据库约束违反
	DatabaseNotFound     = "infra.database.notFound"     // 记录未找到
	DatabaseDuplicateKey = "infra.database.duplicateKey" // 重复键错误
	DatabaseDeadlock     = "infra.database.deadlock"     // 数据库死锁
	DatabaseTimeout      = "infra.database.timeout"      // 数据库操作超时
	DatabaseMigration    = "infra.database.migration"    // 数据库迁移错误

	// 缓存相关 (infra.cache)
	CacheConnection = "infra.cache.connection" // 缓存连接错误
	CacheOperation  = "infra.cache.operation"  // 缓存操作错误
	CacheNotFound   = "infra.cache.notFound"   // 缓存未找到
	CacheExpired    = "infra.cache.expired"    // 缓存过期
	CacheInvalid    = "infra.cache.invalid"    // 缓存无效
	CacheFull       = "infra.cache.full"       // 缓存已满
	CacheEmpty      = "infra.cache.empty"      // 缓存为空
	CacheInvalidArg = "infra.cache.invalidArg" // 缓存无效参数
	CacheInvalidKey = "infra.cache.invalidKey" // 缓存无效键

	// 消息队列相关 (infra.mq)
	MessageQueueConnection = "infra.mq.connection" // 消息队列连接错误
	MessageQueueOperation  = "infra.mq.operation"  // 消息队列操作错误
	MessageQueueNotFound   = "infra.mq.notFound"   // 消息队列未找到

	// ID生成器相关 (infra.generator)
	GeneratorConfig     = "infra.generator.config"     // ID生成器配置无效
	GeneratorInvalidArg = "infra.generator.invalidArg" // 无效的参数

	// 追踪相关 (infra.tracing)
	TracingConfigInvalid  = "infra.tracing.configInvalid"  // 追踪配置无效
	TracingResourceError  = "infra.tracing.resourceError"  // 追踪资源错误
	TracingExporterError  = "infra.tracing.exporterError"  // 追踪导出器错误
	TracingProviderError  = "infra.tracing.providerError"  // 追踪提供者错误
	TracingSpanError      = "infra.tracing.spanError"      // 追踪span错误
	TracingSpanStartError = "infra.tracing.spanStartError" // 追踪span启动错误

	// 验证相关 (infra.validation)
	Validation          = "infra.validation.general"      // 通用验证错误
	ParamRequired       = "infra.validation.required"     // 必需参数缺失
	ParamInvalid        = "infra.validation.invalid"      // 参数格式错误
	ParamOutOfRange     = "infra.validation.outOfRange"   // 参数值超出范围
	ParamTooLong        = "infra.validation.tooLong"      // 参数过长
	ParamTooShort       = "infra.validation.tooShort"     // 参数过短
	EmailInvalid        = "infra.validation.email"        // 邮箱格式错误
	PhoneInvalid        = "infra.validation.phone"        // 手机号格式错误
	PasswordWeak        = "infra.validation.passwordWeak" // 密码强度不足
	JSONInvalid         = "infra.validation.json"         // JSON格式错误
	FileFormatInvalid   = "infra.validation.fileFormat"   // 文件格式错误
	FileSizeExceeded    = "infra.validation.fileSize"     // 文件大小超限
	URLInvalid          = "infra.validation.url"          // URL格式错误
	DateFormatInvalid   = "infra.validation.dateFormat"   // 日期格式错误
	NumberFormatInvalid = "infra.validation.numberFormat" // 数字格式错误
	RegexMatchFailed    = "infra.validation.regex"        // 正则表达式匹配失败

	// 外部服务相关 (infra.external)
	ExternalService       = "infra.external.general"      // 外部服务错误
	ExternalAPICallFailed = "infra.external.apiCall"      // 第三方API调用失败
	ExternalWebhook       = "infra.external.webhook"      // Webhook处理错误
	ExternalNotification  = "infra.external.notification" // 通知服务错误
	ExternalPayment       = "infra.external.payment"      // 支付服务错误
	ExternalSMS           = "infra.external.sms"          // 短信服务错误
	ExternalEmail         = "infra.external.email"        // 邮件服务错误
	ExternalStorage       = "infra.external.storage"      // 存储服务错误
	ExternalMonitoring    = "infra.external.monitoring"   // 监控服务错误
	ExternalLogging       = "infra.external.logging"      // 日志服务错误
	ExternalAnalytics     = "infra.external.analytics"    // 分析服务错误
)
