package codes

// ============ 产品与订单领域错误 ============

const (
	// 商品与库存 (product.)
	ProductNotFound       = "product.biz.notFound"                // 商品不存在
	CategoryNotFound      = "product.category.notFound"           // 分类不存在
	SKUAlreadyExists      = "product.sku.alreadyExists"           // SKU已存在
	ProductOutOfStock     = "product.inventory.outOfStock"        // 商品库存不足
	InventoryInsufficient = "product.inventory.insufficient"      // 库存不足
	WarehouseNotFound     = "product.inventory.warehouseNotFound" // 仓库不存在

	// 订单与采购 (order.)
	OrderNotFound    = "order.biz.notFound"      // 订单不存在
	OrderCancelled   = "order.biz.cancelled"     // 订单已取消
	PurchaseNotFound = "order.purchase.notFound" // 采购单不存在
	SupplierNotFound = "order.supplier.notFound" // 供应商不存在
	CustomerNotFound = "order.customer.notFound" // 客户不存在

	// 支付与财务 (finance.)
	InsufficientBalance = "finance.balance.insufficient" // 余额不足
	PaymentFailed       = "finance.payment.failed"       // 支付失败
	RefundFailed        = "finance.refund.failed"        // 退款失败
)
