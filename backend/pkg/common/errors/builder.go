package errors

// ErrorBuilder 是一个流式的错误构建器
type ErrorBuilder struct {
	err AppError
}

// New 创建一个新的错误构建器实例
// code: 唯一的、领域化的错误码 (e.g., "user.biz.notFound")
// message: 人类可读的错误信息
func New(code, message string) *ErrorBuilder {
	return &ErrorBuilder{
		err: AppError{
			Code:    code,
			Message: message,
			stack:   newStack(), // 捕获堆栈信息
		},
	}
}

// WithType 设置错误的业务类型
func (b *ErrorBuilder) WithType(t ErrorType) *ErrorBuilder {
	b.err.Type = t
	return b
}

// WithDetail 添加一个详细的上下文信息
func (b *ErrorBuilder) WithDetail(key string, value any) *ErrorBuilder {
	if b.err.Details == nil {
		b.err.Details = make(map[string]any)
	}
	b.err.Details[key] = value
	return b
}

// WithDetails 批量添加详细的上下文信息
func (b *ErrorBuilder) WithDetails(details map[string]any) *ErrorBuilder {
	if b.err.Details == nil {
		b.err.Details = make(map[string]any)
	}
	for key, value := range details {
		b.err.Details[key] = value
	}
	return b
}

// Wrap 包装一个底层的 Go error
func (b *ErrorBuilder) Wrap(err error) *ErrorBuilder {
	b.err.cause = err
	return b
}

// Build 返回最终构建的 error
// 返回的是一个 error 接口，但其底层类型是 *AppError
func (b *ErrorBuilder) Build() error {
	return &b.err
}

// 便捷的顶层函数，用于快速创建特定类型的错误

// NewInternal 创建一个内部服务器错误 (HTTP 500)
func NewInternal(code, message string) *ErrorBuilder {
	return New(code, message).WithType(TypeInternal)
}

// NewValidation 创建一个验证错误 (HTTP 400)
func NewValidation(code, message string) *ErrorBuilder {
	return New(code, message).WithType(TypeValidation)
}

// NewNotFound 创建一个资源未找到错误 (HTTP 404)
func NewNotFound(code, message string) *ErrorBuilder {
	return New(code, message).WithType(TypeNotFound)
}

// NewConflict 创建一个资源冲突错误 (HTTP 409)
func NewConflict(code, message string) *ErrorBuilder {
	return New(code, message).WithType(TypeConflict)
}

// NewPermission 创建一个权限不足错误 (HTTP 403)
func NewPermission(code, message string) *ErrorBuilder {
	return New(code, message).WithType(TypePermission)
}

// NewUnauthorized 创建一个未认证错误 (HTTP 401)
func NewUnauthorized(code, message string) *ErrorBuilder {
	return New(code, message).WithType(TypeUnauthorized)
}

// NewExternal 创建一个外部服务错误 (HTTP 503)
func NewExternal(code, message string) *ErrorBuilder {
	return New(code, message).WithType(TypeExternal)
}

// NewTooManyRequests 创建一个请求过于频繁的错误 (HTTP 429)
func NewTooManyRequests(code, message string) *ErrorBuilder {
	return New(code, message).WithType(TypeTooManyRequests)
}
