package errors

import (
	"errors"
)

// Is 检查 err 是否是指定的 AppError 类型，并匹配 Code
// 类似于 errors.Is，但更具体
func Is(err error, code string) bool {
	var appErr *AppError
	if errors.As(err, &appErr) {
		return appErr.Code == code
	}
	return false
}

// As 将 err 转换为 *AppError 类型
// 如果转换失败，返回 nil
func As(err error) *AppError {
	var appErr *AppError
	if errors.As(err, &appErr) {
		return appErr
	}
	return nil
}

// GetType 返回错误的业务类型
// 如果 err 不是 *AppError，返回 TypeInternal
func GetType(err error) ErrorType {
	var appErr *AppError
	if errors.As(err, &appErr) {
		return appErr.Type
	}
	return TypeInternal
}

// GetCode 返回错误的唯一码
// 如果 err 不是 *AppError，返回空字符串
func GetCode(err error) string {
	var appErr *AppError
	if errors.As(err, &appErr) {
		return appErr.Code
	}
	return ""
}

// Wrap 包装一个已存在的错误，如果它不是 *AppError，则将其转换为内部错误
// code: 包装时使用的新错误码
// message: 包装时使用的新消息
func Wrap(err error, code, message string) error {
	if err == nil {
		return nil
	}
	return New(code, message).WithType(TypeInternal).Wrap(err).Build()
}
