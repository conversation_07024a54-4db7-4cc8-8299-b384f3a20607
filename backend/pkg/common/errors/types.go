package errors

import (
	"encoding/json"
	"fmt"
	"runtime"
)

// ErrorType 定义错误的业务分类，用于中间件判断HTTP状态码
type ErrorType int

const (
	TypeInternal        ErrorType = iota + 1 // 500: 服务器内部错误
	TypeValidation                           // 400: 请求参数验证失败
	TypeNotFound                             // 404: 资源未找到
	TypeConflict                             // 409: 资源冲突或重复
	TypePermission                           // 403: 权限不足
	TypeUnauthorized                         // 401: 未认证
	TypeExternal                             // 503: 外部服务错误
	TypeTooManyRequests                      // 429: 请求过于频繁
)

// AppError 是应用中所有错误的唯一标准实现
type AppError struct {
	Type    ErrorType      `json:"-"`
	Code    string         `json:"code"`
	Message string         `json:"message"`
	Details map[string]any `json:"details,omitempty"`
	cause   error          `json:"-"`
	stack   *stack         `json:"-"`
}

// stack 保存错误发生时的调用堆栈
type stack struct {
	pcs []uintptr
}

// newStack 创建并填充堆栈信息
func newStack() *stack {
	pcs := make([]uintptr, 32)
	// 我们跳过前3个调用者: runtime.Callers, newStack, 和调用newStack的函数(通常是New)
	n := runtime.Callers(3, pcs)
	return &stack{pcs: pcs[:n]}
}

// Frames 返回堆栈帧
func (s *stack) Frames() *runtime.Frames {
	return runtime.CallersFrames(s.pcs)
}

// 实现 error 接口
func (e *AppError) Error() string {
	if e.cause != nil {
		return fmt.Sprintf("%s: %s -> %v", e.Code, e.Message, e.cause)
	}
	return fmt.Sprintf("%s: %s", e.Code, e.Message)
}

// Unwrap 实现 Go 1.13+ 的错误包装接口, 支持 errors.Is 和 errors.As
func (e *AppError) Unwrap() error {
	return e.cause
}

// MarshalJSON 自定义JSON序列化，避免暴露敏感信息如 cause 和 stack
func (e *AppError) MarshalJSON() ([]byte, error) {
	type Alias AppError
	return json.Marshal(&struct {
		Code    string         `json:"code"`
		Message string         `json:"message"`
		Details map[string]any `json:"details,omitempty"`
	}{
		Code:    e.Code,
		Message: e.Message,
		Details: e.Details,
	})
}
