package middleware

import (
	"errors"
	"fmt"
	"net/http"
	"runtime/debug"
	"time"

	apperrors "backend/pkg/common/errors"
	"backend/pkg/common/errors/codes"
	"backend/pkg/infrastructure/logger"

	"github.com/gin-gonic/gin"
)

// ErrorResponse 错误响应结构体
type ErrorResponse struct {
	Code      string         `json:"code"`
	Message   string         `json:"message"`
	Details   map[string]any `json:"details,omitempty"`
	TraceID   string         `json:"trace_id,omitempty"`
	Timestamp int64          `json:"timestamp"`
}

// ErrorHandler 返回一个用于集中处理错误和恢复的 gin.HandlerFunc
// 它应该是中间件链中的第一个
func ErrorHandler(log logger.Logger) gin.HandlerFunc {
	return func(c *gin.Context) {
		defer func() {
			// 处理 panic
			if r := recover(); r != nil {
				handlePanic(c, r, log)
				return
			}

			// 处理常规错误
			// Gin 会将 handler 中通过 c.Error(err) 附加的错误收集到 c.Errors
			if len(c.Errors) > 0 {
				// 我们只处理最后一个错误，以避免重复响应
				handleError(c, c.Errors.Last().Err, log)
			}
		}()

		c.Next()
	}
}

// handlePanic 处理 panic
func handlePanic(c *gin.Context, recovered any, log logger.Logger) {
	// 将 panic 包装成一个标准的 AppError
	var err error
	if e, ok := recovered.(error); ok {
		err = apperrors.NewInternal(codes.Panic, "系统发生Panic").Wrap(e).Build()
	} else {
		err = apperrors.NewInternal(codes.Panic, fmt.Sprintf("系统发生Panic: %v", recovered)).Build()
	}

	appErr := apperrors.As(err) // 此时必定转换成功

	traceID := getTraceID(c)
	logErrorWithStack(c, appErr, traceID, log)
	respondWithError(c, appErr, traceID)
}

// handleError 处理常规错误
func handleError(c *gin.Context, err error, log logger.Logger) {
	var appErr *apperrors.AppError
	if !errors.As(err, &appErr) {
		// 如果错误不是 *AppError，则将其包装为通用的内部系统错误
		err = apperrors.NewInternal(codes.SystemError, "发生未知系统错误").Wrap(err).Build()
		appErr = apperrors.As(err)
	}

	traceID := getTraceID(c)
	logError(c, appErr, traceID, log)
	respondWithError(c, appErr, traceID)
}

// respondWithError 向客户端发送错误响应
func respondWithError(c *gin.Context, err *apperrors.AppError, traceID string) {
	if c.Writer.Written() {
		return
	}

	response := ErrorResponse{
		Code:      err.Code,
		Message:   err.Message,
		Details:   err.Details,
		TraceID:   traceID,
		Timestamp: time.Now().Unix(),
	}

	c.JSON(errorTypeToHTTPStatus(err.Type), response)
	c.Abort()
}

// logError 记录结构化错误日志
func logError(c *gin.Context, err *apperrors.AppError, traceID string, log logger.Logger) {
	fields := map[string]any{
		"error_code":    err.Code,
		"error_message": err.Message,
		"details":       err.Details,
		"http_method":   c.Request.Method,
		"http_url":      c.Request.URL.String(),
		"client_ip":     c.ClientIP(),
		"trace_id":      traceID,
	}

	if cause := errors.Unwrap(err); cause != nil {
		fields["root_cause"] = cause.Error()
	}

	switch err.Type {
	case apperrors.TypeInternal, apperrors.TypeExternal:
		log.Error(c, err.Message, fields)
	case apperrors.TypeValidation, apperrors.TypePermission, apperrors.TypeUnauthorized, apperrors.TypeTooManyRequests:
		log.Warn(c, err.Message, fields)
	default: // TypeNotFound, TypeConflict 等
		log.Info(c, err.Message, fields)
	}
}

// logErrorWithStack 记录带堆栈信息的 panic 日志
func logErrorWithStack(c *gin.Context, err *apperrors.AppError, traceID string, log logger.Logger) {
	fields := map[string]any{
		"error_code":    err.Code,
		"error_message": err.Message,
		"details":       err.Details,
		"http_method":   c.Request.Method,
		"http_url":      c.Request.URL.String(),
		"client_ip":     c.ClientIP(),
		"trace_id":      traceID,
		"stack_trace":   string(debug.Stack()),
	}

	if cause := errors.Unwrap(err); cause != nil {
		fields["root_cause"] = cause.Error()
	}

	log.Error(c, err.Message, fields)
}

// errorTypeToHTTPStatus 将错误类型映射到 HTTP 状态码
func errorTypeToHTTPStatus(t apperrors.ErrorType) int {
	switch t {
	case apperrors.TypeInternal:
		return http.StatusInternalServerError
	case apperrors.TypeValidation:
		return http.StatusBadRequest
	case apperrors.TypeNotFound:
		return http.StatusNotFound
	case apperrors.TypeConflict:
		return http.StatusConflict
	case apperrors.TypePermission:
		return http.StatusForbidden
	case apperrors.TypeUnauthorized:
		return http.StatusUnauthorized
	case apperrors.TypeExternal:
		return http.StatusServiceUnavailable
	case apperrors.TypeTooManyRequests:
		return http.StatusTooManyRequests
	default:
		return http.StatusInternalServerError
	}
}

// getTraceID 从请求中获取追踪ID
func getTraceID(c *gin.Context) string {
	if traceID := c.GetHeader("X-Trace-ID"); traceID != "" {
		return traceID
	}
	if traceID := c.GetHeader("X-Request-ID"); traceID != "" {
		return traceID
	}
	if traceID, exists := c.Get("trace_id"); exists {
		if id, ok := traceID.(string); ok {
			return id
		}
	}
	return ""
}
