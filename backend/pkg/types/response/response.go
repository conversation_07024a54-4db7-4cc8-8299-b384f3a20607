package response

// APIResponse 定义了所有HTTP API的统一响应体
type APIResponse struct {
	Success bool        `json:"success"`            // 业务是否成功
	Code    int         `json:"code"`               // 业务状态码
	Message string      `json:"message"`            // 提示信息
	Data    interface{} `json:"data,omitempty"`     // 响应数据, omitempty so it's hidden if nil
	TraceID string      `json:"trace_id,omitempty"` // 用于链路追踪的ID
}
