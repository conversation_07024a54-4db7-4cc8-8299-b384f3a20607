package uuid

import (
	"context"

	"github.com/google/uuid"
)

// DomainAttributes 领域属性结构
type DomainAttributes struct {
	// TenantID 租户ID
	TenantID string `json:"tenant_id" validate:"required"`

	// EntityType 实体类型
	EntityType string `json:"entity_type" validate:"required"`

	// Key 主键值
	Key string `json:"key" validate:"required"`

	// Metadata 元数据
	Metadata map[string]interface{} `json:"metadata,omitempty"`
}

// GeneratorConfig 生成器配置
type GeneratorConfig struct {
	// RootNamespace 根命名空间
	RootNamespace string `yaml:"root_namespace" json:"root_namespace"`

	// FallbackToV4 失败时是否降级到UUID V4
	FallbackToV4 bool `yaml:"fallback_to_v4" json:"fallback_to_v4"`

	// EnableCache 是否启用缓存
	EnableCache bool `yaml:"enable_cache" json:"enable_cache"`

	// CacheSize 缓存大小
	CacheSize int `yaml:"cache_size" json:"cache_size"`

	// Strategies 领域策略配置
	Strategies map[string]StrategyConfig `yaml:"strategies" json:"strategies"`
}

// StrategyConfig 策略配置
type StrategyConfig struct {
	// RequiredAttrs 必需属性
	RequiredAttrs []string `yaml:"required_attrs" json:"required_attrs"`

	// OptionalAttrs 可选属性
	OptionalAttrs []string `yaml:"optional_attrs" json:"optional_attrs"`

	// AttributeOrder 属性排序（用于生成语义化名称）
	AttributeOrder []string `yaml:"attribute_order" json:"attribute_order"`
}

// Generator UUID生成器接口
type Generator interface {
	// Generate 生成语义化UUID
	Generate(ctx context.Context, domain string, attrs DomainAttributes) (uuid.UUID, error)

	// GenerateWithFallback 生成UUID，失败时可降级
	GenerateWithFallback(ctx context.Context, domain string, attrs DomainAttributes) (uuid.UUID, error)

	// GetNamespace 获取命名空间UUID
	GetNamespace(domain string, entityType string) (uuid.UUID, error)

	// ValidateAttributes 验证属性
	ValidateAttributes(domain string, attrs DomainAttributes) error
}

// DomainStrategy 领域策略接口
type DomainStrategy interface {
	// GenerateSemanticName 生成语义化名称
	GenerateSemanticName(attrs DomainAttributes) (string, error)

	// ValidateAttributes 验证属性
	ValidateAttributes(attrs DomainAttributes) error

	// GetRequiredAttributes 获取必需属性列表
	GetRequiredAttributes() []string

	// GetOptionalAttributes 获取可选属性列表
	GetOptionalAttributes() []string
}

// NamespaceManager 命名空间管理器接口
type NamespaceManager interface {
	// GetOrCreate 获取或创建命名空间
	GetOrCreate(domain string, entityType string) (uuid.UUID, error)

	// Exists 检查命名空间是否存在
	Exists(domain string, entityType string) bool

	// Clear 清空缓存
	Clear()
}
