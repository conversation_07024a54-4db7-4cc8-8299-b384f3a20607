package database

import (
	"errors"
	"strings"

	commonErrors "backend/pkg/common/errors"
	"backend/pkg/common/errors/codes"

	"github.com/jackc/pgx/v5/pgconn"
	"gorm.io/gorm"
)

// 全局错误适配器实例
var defaultAdapter = NewErrorAdapter()

// TranslateDBError 便捷函数，翻译数据库错误
func TranslateDBError(err error) error {
	return defaultAdapter.TranslateError(err)
}

// WrapDBError 包装数据库错误，如果是nil则返回nil
func WrapDBError(err error) error {
	if err == nil {
		return nil
	}
	return TranslateDBError(err)
}

// IsNotFoundError 检查是否为记录未找到错误
func IsNotFoundError(err error) bool {
	return errors.Is(err, gorm.ErrRecordNotFound)
}

// IsConstraintError 检查是否为约束违反错误
func IsConstraintError(err error) bool {
	var pgErr *pgconn.PgError
	if errors.As(err, &pgErr) {
		return strings.HasPrefix(pgErr.Code, "23")
	}

	errMsg := strings.ToLower(err.Error())
	return strings.Contains(errMsg, "constraint") ||
		strings.Contains(errMsg, "violates") ||
		strings.Contains(errMsg, "duplicate") ||
		strings.Contains(errMsg, "unique")
}

// IsConnectionError 检查是否为连接错误
func IsConnectionError(err error) bool {
	var pgErr *pgconn.PgError
	if errors.As(err, &pgErr) {
		return strings.HasPrefix(pgErr.Code, "08")
	}

	errMsg := strings.ToLower(err.Error())
	return strings.Contains(errMsg, "connection") ||
		strings.Contains(errMsg, "connect") ||
		strings.Contains(errMsg, "dial")
}

// ErrorAdapter 数据库错误适配器
type ErrorAdapter struct{}

// NewErrorAdapter 创建新的数据库错误适配器
func NewErrorAdapter() *ErrorAdapter {
	return &ErrorAdapter{}
}

// TranslateError 将数据库错误翻译为标准的AppError
func (a *ErrorAdapter) TranslateError(err error) error {
	if err == nil {
		return nil
	}

	// GORM特定错误
	if errors.Is(err, gorm.ErrRecordNotFound) {
		return commonErrors.NewNotFound(codes.DatabaseNotFound, "记录未找到").
			Wrap(err).Build()
	}

	if errors.Is(err, gorm.ErrInvalidTransaction) {
		return commonErrors.NewInternal(codes.DatabaseTransaction, "无效的数据库事务").
			Wrap(err).Build()
	}

	if errors.Is(err, gorm.ErrNotImplemented) {
		return commonErrors.NewInternal(codes.SystemError, "数据库操作未实现").
			Wrap(err).Build()
	}

	if errors.Is(err, gorm.ErrMissingWhereClause) {
		return commonErrors.NewValidation(codes.ParamInvalid, "缺少WHERE条件").
			Wrap(err).Build()
	}

	if errors.Is(err, gorm.ErrUnsupportedRelation) {
		return commonErrors.NewInternal(codes.DatabaseQuery, "不支持的关联关系").
			Wrap(err).Build()
	}

	if errors.Is(err, gorm.ErrPrimaryKeyRequired) {
		return commonErrors.NewValidation(codes.ParamRequired, "缺少主键").
			Wrap(err).Build()
	}

	if errors.Is(err, gorm.ErrModelValueRequired) {
		return commonErrors.NewValidation(codes.ParamRequired, "缺少模型值").
			Wrap(err).Build()
	}

	if errors.Is(err, gorm.ErrInvalidData) {
		return commonErrors.NewValidation(codes.ParamInvalid, "无效的数据").
			Wrap(err).Build()
	}

	if errors.Is(err, gorm.ErrUnsupportedDriver) {
		return commonErrors.NewInternal(codes.DatabaseConnection, "不支持的数据库驱动").
			Wrap(err).Build()
	}

	if errors.Is(err, gorm.ErrRegistered) {
		return commonErrors.NewInternal(codes.SystemError, "重复注册").
			Wrap(err).Build()
	}

	if errors.Is(err, gorm.ErrInvalidField) {
		return commonErrors.NewValidation(codes.ParamInvalid, "无效的字段").
			Wrap(err).Build()
	}

	if errors.Is(err, gorm.ErrEmptySlice) {
		return commonErrors.NewValidation(codes.ParamInvalid, "空切片").
			Wrap(err).Build()
	}

	if errors.Is(err, gorm.ErrDryRunModeUnsupported) {
		return commonErrors.NewInternal(codes.SystemError, "不支持DryRun模式").
			Wrap(err).Build()
	}

	if errors.Is(err, gorm.ErrInvalidDB) {
		return commonErrors.NewInternal(codes.DatabaseConnection, "无效的数据库连接").
			Wrap(err).Build()
	}

	if errors.Is(err, gorm.ErrInvalidValue) {
		return commonErrors.NewValidation(codes.ParamInvalid, "无效的值").
			Wrap(err).Build()
	}

	if errors.Is(err, gorm.ErrInvalidValueOfLength) {
		return commonErrors.NewValidation(codes.ParamOutOfRange, "值长度无效").
			Wrap(err).Build()
	}

	// PostgreSQL特定错误
	var pgErr *pgconn.PgError
	if errors.As(err, &pgErr) {
		return a.translatePostgreSQLError(pgErr)
	}

	// 通用数据库错误模式匹配
	errMsg := strings.ToLower(err.Error())

	// 连接错误
	if strings.Contains(errMsg, "connection") ||
		strings.Contains(errMsg, "connect") ||
		strings.Contains(errMsg, "dial") {
		return commonErrors.NewExternal(codes.DatabaseConnection, "数据库连接失败").
			Wrap(err).Build()
	}

	// 超时错误
	if strings.Contains(errMsg, "timeout") ||
		strings.Contains(errMsg, "deadline") {
		return commonErrors.NewExternal(codes.DatabaseTimeout, "数据库操作超时").
			Wrap(err).Build()
	}

	// 死锁错误
	if strings.Contains(errMsg, "deadlock") {
		return commonErrors.NewConflict(codes.DatabaseDeadlock, "数据库死锁").
			Wrap(err).Build()
	}

	// 约束违反
	if strings.Contains(errMsg, "constraint") ||
		strings.Contains(errMsg, "violates") {
		return commonErrors.NewConflict(codes.DatabaseConstraint, "数据库约束违反").
			Wrap(err).Build()
	}

	// 重复键错误
	if strings.Contains(errMsg, "duplicate") ||
		strings.Contains(errMsg, "unique") {
		return commonErrors.NewConflict(codes.DatabaseDuplicateKey, "重复键错误").
			Wrap(err).Build()
	}

	// 语法错误
	if strings.Contains(errMsg, "syntax") ||
		strings.Contains(errMsg, "invalid sql") {
		return commonErrors.NewInternal(codes.DatabaseQuery, "SQL语法错误").
			Wrap(err).Build()
	}

	// 权限错误
	if strings.Contains(errMsg, "permission") ||
		strings.Contains(errMsg, "access denied") {
		return commonErrors.NewPermission(codes.DatabaseConnection, "数据库访问权限不足").
			Wrap(err).Build()
	}

	// 默认包装为内部数据库错误
	return commonErrors.NewInternal(codes.DatabaseQuery, "数据库操作失败").
		Wrap(err).Build()
}

// translatePostgreSQLError 翻译PostgreSQL特定错误
func (a *ErrorAdapter) translatePostgreSQLError(pgErr *pgconn.PgError) error {
	switch pgErr.Code {
	// 连接错误类 (08xxx)
	case "08000": // connection_exception
		return commonErrors.NewExternal(codes.DatabaseConnection, "数据库连接异常").
			WithDetail("pg_code", pgErr.Code).
			WithDetail("pg_message", pgErr.Message).
			Wrap(pgErr).Build()
	case "08003": // connection_does_not_exist
		return commonErrors.NewExternal(codes.DatabaseConnection, "数据库连接不存在").
			WithDetail("pg_code", pgErr.Code).
			WithDetail("pg_message", pgErr.Message).
			Wrap(pgErr).Build()
	case "08006": // connection_failure
		return commonErrors.NewExternal(codes.DatabaseConnection, "数据库连接失败").
			WithDetail("pg_code", pgErr.Code).
			WithDetail("pg_message", pgErr.Message).
			Wrap(pgErr).Build()

	// 数据异常类 (22xxx)
	case "22001": // string_data_right_truncation
		return commonErrors.NewValidation(codes.ParamTooLong, "字符串数据过长").
			WithDetail("pg_code", pgErr.Code).
			WithDetail("pg_message", pgErr.Message).
			Wrap(pgErr).Build()
	case "22003": // numeric_value_out_of_range
		return commonErrors.NewValidation(codes.ParamOutOfRange, "数值超出范围").
			WithDetail("pg_code", pgErr.Code).
			WithDetail("pg_message", pgErr.Message).
			Wrap(pgErr).Build()
	case "22007": // invalid_datetime_format
		return commonErrors.NewValidation(codes.DateFormatInvalid, "无效的日期时间格式").
			WithDetail("pg_code", pgErr.Code).
			WithDetail("pg_message", pgErr.Message).
			Wrap(pgErr).Build()
	case "22012": // division_by_zero
		return commonErrors.NewValidation(codes.ParamInvalid, "除零错误").
			WithDetail("pg_code", pgErr.Code).
			WithDetail("pg_message", pgErr.Message).
			Wrap(pgErr).Build()

	// 完整性约束违反类 (23xxx)
	case "23000": // integrity_constraint_violation
		return commonErrors.NewConflict(codes.DatabaseConstraint, "完整性约束违反").
			WithDetail("pg_code", pgErr.Code).
			WithDetail("pg_message", pgErr.Message).
			WithDetail("constraint", pgErr.ConstraintName).
			Wrap(pgErr).Build()
	case "23001": // restrict_violation
		return commonErrors.NewConflict(codes.DatabaseConstraint, "限制约束违反").
			WithDetail("pg_code", pgErr.Code).
			WithDetail("pg_message", pgErr.Message).
			WithDetail("constraint", pgErr.ConstraintName).
			Wrap(pgErr).Build()
	case "23502": // not_null_violation
		return commonErrors.NewValidation(codes.ParamRequired, "非空约束违反").
			WithDetail("pg_code", pgErr.Code).
			WithDetail("pg_message", pgErr.Message).
			WithDetail("column", pgErr.ColumnName).
			Wrap(pgErr).Build()
	case "23503": // foreign_key_violation
		return commonErrors.NewConflict(codes.DatabaseConstraint, "外键约束违反").
			WithDetail("pg_code", pgErr.Code).
			WithDetail("pg_message", pgErr.Message).
			WithDetail("constraint", pgErr.ConstraintName).
			Wrap(pgErr).Build()
	case "23505": // unique_violation
		return commonErrors.NewConflict(codes.DatabaseDuplicateKey, "唯一约束违反").
			WithDetail("pg_code", pgErr.Code).
			WithDetail("pg_message", pgErr.Message).
			WithDetail("constraint", pgErr.ConstraintName).
			Wrap(pgErr).Build()
	case "23514": // check_violation
		return commonErrors.NewValidation(codes.ParamInvalid, "检查约束违反").
			WithDetail("pg_code", pgErr.Code).
			WithDetail("pg_message", pgErr.Message).
			WithDetail("constraint", pgErr.ConstraintName).
			Wrap(pgErr).Build()

	// 无效授权规范类 (28xxx)
	case "28000": // invalid_authorization_specification
		return commonErrors.NewUnauthorized(codes.DatabaseConnection, "数据库认证失败").
			WithDetail("pg_code", pgErr.Code).
			WithDetail("pg_message", pgErr.Message).
			Wrap(pgErr).Build()
	case "28P01": // invalid_password
		return commonErrors.NewUnauthorized(codes.DatabaseConnection, "数据库密码错误").
			WithDetail("pg_code", pgErr.Code).
			WithDetail("pg_message", pgErr.Message).
			Wrap(pgErr).Build()

	// 无效事务状态类 (25xxx)
	case "25001": // active_sql_transaction
		return commonErrors.NewConflict(codes.DatabaseTransaction, "活跃的SQL事务").
			WithDetail("pg_code", pgErr.Code).
			WithDetail("pg_message", pgErr.Message).
			Wrap(pgErr).Build()
	case "25P02": // in_failed_sql_transaction
		return commonErrors.NewInternal(codes.DatabaseTransaction, "事务失败状态").
			WithDetail("pg_code", pgErr.Code).
			WithDetail("pg_message", pgErr.Message).
			Wrap(pgErr).Build()

	// 语法错误或访问规则违反类 (42xxx)
	case "42601": // syntax_error
		return commonErrors.NewInternal(codes.DatabaseQuery, "SQL语法错误").
			WithDetail("pg_code", pgErr.Code).
			WithDetail("pg_message", pgErr.Message).
			Wrap(pgErr).Build()
	case "42501": // insufficient_privilege
		return commonErrors.NewPermission(codes.DatabaseConnection, "数据库权限不足").
			WithDetail("pg_code", pgErr.Code).
			WithDetail("pg_message", pgErr.Message).
			Wrap(pgErr).Build()
	case "42P01": // undefined_table
		return commonErrors.NewNotFound(codes.DatabaseQuery, "表不存在").
			WithDetail("pg_code", pgErr.Code).
			WithDetail("pg_message", pgErr.Message).
			Wrap(pgErr).Build()
	case "42703": // undefined_column
		return commonErrors.NewNotFound(codes.DatabaseQuery, "列不存在").
			WithDetail("pg_code", pgErr.Code).
			WithDetail("pg_message", pgErr.Message).
			Wrap(pgErr).Build()

	// 系统错误类 (XX xxx)
	case "XX000": // internal_error
		return commonErrors.NewInternal(codes.SystemError, "数据库内部错误").
			WithDetail("pg_code", pgErr.Code).
			WithDetail("pg_message", pgErr.Message).
			Wrap(pgErr).Build()

	// 默认处理
	default:
		return commonErrors.NewInternal(codes.DatabaseQuery, "PostgreSQL数据库错误").
			WithDetail("pg_code", pgErr.Code).
			WithDetail("pg_message", pgErr.Message).
			WithDetail("pg_severity", pgErr.Severity).
			Wrap(pgErr).Build()
	}
}
