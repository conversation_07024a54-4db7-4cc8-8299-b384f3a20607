package cache

import (
	"context"
	"fmt"
	"time"

	"backend/pkg/infrastructure/logger"
	"backend/pkg/infrastructure/monitoring"

	"go.opentelemetry.io/otel/attribute"
	"go.opentelemetry.io/otel/trace"
)

// CacheTracer 缓存操作追踪器
type CacheTracer struct {
	logger         logger.Logger
	contextManager *logger.ContextManager
	tracingManager *monitoring.TracingManager
}

// NewCacheTracer 创建缓存追踪器
func NewCacheTracer(
	log logger.Logger,
	tracingManager *monitoring.TracingManager,
) *CacheTracer {
	return &CacheTracer{
		logger:         log,
		contextManager: logger.NewContextManager(log),
		tracingManager: tracingManager,
	}
}

// TraceGet 追踪缓存获取操作
func (ct *CacheTracer) TraceGet(ctx context.Context, key string, fn func() (interface{}, bool, error)) (interface{}, bool, error) {
	start := time.Now()

	// 开始缓存span
	spanCtx, span := ct.tracingManager.StartCacheSpan(ctx, "get", key)
	defer span.End()

	// 执行获取操作
	value, hit, err := fn()
	duration := time.Since(start)

	// 计算值大小
	var size int64
	if value != nil {
		size = ct.calculateSize(value)
	}

	// 记录结果到span
	span.SetAttributes(
		attribute.Bool("cache.hit", hit),
		attribute.String("cache.duration", duration.String()),
		attribute.Int64("cache.size", size),
	)

	// 记录日志
	cacheOp := logger.CacheOperationLog{
		Operation: "get",
		Key:       key,
		Hit:       hit,
		Size:      size,
		Duration:  duration,
		StartTime: start,
		EndTime:   time.Now(),
	}

	if err != nil {
		cacheOp.Error = err.Error()
		ct.tracingManager.RecordError(span, err)
	} else {
		ct.tracingManager.SetSpanSuccess(span)
	}

	ct.contextManager.LogCacheOperation(spanCtx, cacheOp)

	return value, hit, err
}

// TraceSet 追踪缓存设置操作
func (ct *CacheTracer) TraceSet(ctx context.Context, key string, value interface{}, ttl time.Duration, fn func() error) error {
	start := time.Now()

	// 开始缓存span
	spanCtx, span := ct.tracingManager.StartCacheSpan(ctx, "set", key)
	defer span.End()

	// 计算值大小
	size := ct.calculateSize(value)

	// 添加设置信息到span
	span.SetAttributes(
		attribute.Int64("cache.size", size),
		attribute.String("cache.ttl", ttl.String()),
	)

	// 执行设置操作
	err := fn()
	duration := time.Since(start)

	// 记录结果
	span.SetAttributes(
		attribute.String("cache.duration", duration.String()),
	)

	// 记录日志
	cacheOp := logger.CacheOperationLog{
		Operation: "set",
		Key:       key,
		Hit:       false, // set操作不涉及hit
		Size:      size,
		TTL:       ttl,
		Duration:  duration,
		StartTime: start,
		EndTime:   time.Now(),
	}

	if err != nil {
		cacheOp.Error = err.Error()
		ct.tracingManager.RecordError(span, err)
	} else {
		ct.tracingManager.SetSpanSuccess(span)
	}

	ct.contextManager.LogCacheOperation(spanCtx, cacheOp)

	return err
}

// TraceDelete 追踪缓存删除操作
func (ct *CacheTracer) TraceDelete(ctx context.Context, key string, fn func() error) error {
	start := time.Now()

	// 开始缓存span
	spanCtx, span := ct.tracingManager.StartCacheSpan(ctx, "delete", key)
	defer span.End()

	// 执行删除操作
	err := fn()
	duration := time.Since(start)

	// 记录结果
	span.SetAttributes(
		attribute.String("cache.duration", duration.String()),
	)

	// 记录日志
	cacheOp := logger.CacheOperationLog{
		Operation: "delete",
		Key:       key,
		Hit:       false, // delete操作不涉及hit
		Duration:  duration,
		StartTime: start,
		EndTime:   time.Now(),
	}

	if err != nil {
		cacheOp.Error = err.Error()
		ct.tracingManager.RecordError(span, err)
	} else {
		ct.tracingManager.SetSpanSuccess(span)
	}

	ct.contextManager.LogCacheOperation(spanCtx, cacheOp)

	return err
}

// TraceBatchOperation 追踪批量缓存操作
func (ct *CacheTracer) TraceBatchOperation(ctx context.Context, operation string, keys []string, fn func() (map[string]interface{}, error)) (map[string]interface{}, error) {
	start := time.Now()

	// 开始批量操作span
	spanCtx, span := ct.tracingManager.StartSpan(ctx, fmt.Sprintf("cache.batch_%s", operation),
		trace.WithAttributes(
			attribute.String("operation.type", "cache"),
			attribute.String("cache.operation", fmt.Sprintf("batch_%s", operation)),
			attribute.Int("batch.size", len(keys)),
		),
	)
	defer span.End()

	// 执行批量操作
	results, err := fn()
	duration := time.Since(start)

	// 计算命中率
	var hitCount int
	var totalSize int64
	if results != nil {
		hitCount = len(results)
		for _, value := range results {
			totalSize += ct.calculateSize(value)
		}
	}

	hitRate := float64(hitCount) / float64(len(keys))

	// 记录结果
	span.SetAttributes(
		attribute.String("cache.duration", duration.String()),
		attribute.Int("cache.hit_count", hitCount),
		attribute.Float64("cache.hit_rate", hitRate),
		attribute.Int64("cache.total_size", totalSize),
	)

	if err != nil {
		ct.tracingManager.RecordError(span, err)
	} else {
		ct.tracingManager.SetSpanSuccess(span)
	}

	// 记录详细日志
	ct.contextManager.LogWithStandardContext(spanCtx, logger.LevelInfo,
		fmt.Sprintf("Batch cache operation: %s", operation),
		"operation", operation,
		"batch_size", len(keys),
		"hit_count", hitCount,
		"hit_rate", fmt.Sprintf("%.2f%%", hitRate*100),
		"total_size", totalSize,
		"duration", duration,
	)

	return results, err
}

// TraceEviction 追踪缓存淘汰
func (ct *CacheTracer) TraceEviction(ctx context.Context, reason string, keys []string) {
	spanCtx, span := ct.tracingManager.StartSpan(ctx, "cache.eviction",
		trace.WithAttributes(
			attribute.String("operation.type", "cache"),
			attribute.String("cache.operation", "eviction"),
			attribute.String("eviction.reason", reason),
			attribute.Int("eviction.count", len(keys)),
		),
	)
	defer span.End()

	ct.contextManager.LogWithStandardContext(spanCtx, logger.LevelInfo,
		"Cache eviction occurred",
		"reason", reason,
		"evicted_count", len(keys),
		"evicted_keys", keys,
	)

	ct.tracingManager.SetSpanSuccess(span)
}

// TraceStats 追踪缓存统计信息
func (ct *CacheTracer) TraceStats(ctx context.Context, stats map[string]interface{}) {
	ct.contextManager.LogWithStandardContext(ctx, logger.LevelDebug,
		"Cache statistics",
		"stats", stats,
	)

	// 如果命中率过低，记录警告
	if hitRate, ok := stats["hit_rate"].(float64); ok && hitRate < 0.5 {
		ct.contextManager.LogWithStandardContext(ctx, logger.LevelWarn,
			"Low cache hit rate detected",
			"hit_rate", fmt.Sprintf("%.2f%%", hitRate*100),
			"stats", stats,
		)
	}
}

// calculateSize 计算值的大小（简化实现）
func (ct *CacheTracer) calculateSize(value interface{}) int64 {
	if value == nil {
		return 0
	}

	switch v := value.(type) {
	case string:
		return int64(len(v))
	case []byte:
		return int64(len(v))
	case int, int32, int64, float32, float64:
		return 8 // 简化处理
	default:
		// 对于复杂类型，返回估算值
		return 64
	}
}

// TraceConnectionPool 追踪缓存连接池状态
func (ct *CacheTracer) TraceConnectionPool(ctx context.Context, stats interface{}) {
	ct.contextManager.LogWithStandardContext(ctx, logger.LevelDebug,
		"Cache connection pool stats",
		"stats", stats,
	)
}

// TraceHealthCheck 追踪缓存健康检查
func (ct *CacheTracer) TraceHealthCheck(ctx context.Context, fn func() error) error {
	start := time.Now()

	spanCtx, span := ct.tracingManager.StartSpan(ctx, "cache.health_check",
		trace.WithAttributes(
			attribute.String("operation.type", "cache"),
			attribute.String("cache.operation", "health_check"),
		),
	)
	defer span.End()

	err := fn()
	duration := time.Since(start)

	span.SetAttributes(
		attribute.String("health_check.duration", duration.String()),
	)

	if err != nil {
		ct.tracingManager.RecordError(span, err)
		ct.contextManager.LogWithStandardContext(spanCtx, logger.LevelError,
			"Cache health check failed",
			"error", err.Error(),
			"duration", duration,
		)
	} else {
		ct.tracingManager.SetSpanSuccess(span)
		ct.contextManager.LogWithStandardContext(spanCtx, logger.LevelDebug,
			"Cache health check passed",
			"duration", duration,
		)
	}

	return err
}
