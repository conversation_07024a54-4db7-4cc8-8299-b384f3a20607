package cache

import (
	"context"
	"errors"
	"time"
)

// 定义常用错误
var (
	ErrKeyNotFound       = errors.New("缓存键不存在")
	ErrCacheUnavailable  = errors.New("缓存服务不可用")
	ErrInvalidKey        = errors.New("无效的缓存键")
	ErrSerializeFailed   = errors.New("序列化失败")
	ErrDeserializeFailed = errors.New("反序列化失败")
)

// Cache 基础缓存接口
// 提供最基本的缓存操作，所有缓存实现都应该支持这些操作
type Cache interface {
	// Get 获取缓存值
	Get(ctx context.Context, key string) ([]byte, error)

	// Set 设置缓存值
	Set(ctx context.Context, key string, value []byte, ttl time.Duration) error

	// Del 删除缓存
	Del(ctx context.Context, keys ...string) error

	// Exists 检查键是否存在
	Exists(ctx context.Context, key string) (bool, error)

	// TTL 获取键的剩余生存时间
	TTL(ctx context.Context, key string) (time.Duration, error)

	// SetTTL 设置键的生存时间
	SetTTL(ctx context.Context, key string, ttl time.Duration) error

	// Close 关闭缓存连接
	Close() error
}

// AdvancedCache 高级缓存接口
// 提供批量操作、模式匹配等高级功能
type AdvancedCache interface {
	Cache

	// BatchGet 批量获取缓存值
	BatchGet(ctx context.Context, keys []string) (map[string][]byte, error)

	// BatchSet 批量设置缓存值
	BatchSet(ctx context.Context, items map[string]CacheItem) error

	// BatchDel 批量删除缓存
	BatchDel(ctx context.Context, keys []string) error

	// Keys 根据模式获取键列表
	Keys(ctx context.Context, pattern string) ([]string, error)

	// Scan 扫描键，支持游标和分页
	Scan(ctx context.Context, cursor uint64, pattern string, count int64) ([]string, uint64, error)

	// SAdd 添加元素到集合
	SAdd(ctx context.Context, key string, members ...string) error

	// SRem 从集合中移除元素
	SRem(ctx context.Context, key string, members ...string) error

	// SMembers 获取集合中的所有元素
	SMembers(ctx context.Context, key string) ([]string, error)

	// SCard 获取集合中的元素数量
	SCard(ctx context.Context, key string) (int64, error)

	// FlushDB 清空当前数据库
	FlushDB(ctx context.Context) error

	// Info 获取缓存统计信息
	Info(ctx context.Context) (*CacheInfo, error)

	// Ping 测试连接
	Ping(ctx context.Context) error
}

// GenericCache 泛型缓存接口
// 提供类型安全的缓存操作，自动处理序列化和反序列化
type GenericCache interface {
	// GetObject 获取对象并反序列化到目标类型
	GetObject(ctx context.Context, key string, dest interface{}) error

	// SetObject 序列化对象并设置到缓存
	SetObject(ctx context.Context, key string, obj interface{}, ttl time.Duration) error
}

// DistributedCache 分布式缓存接口
// 提供分布式环境下的缓存操作
type DistributedCache interface {
	AdvancedCache

	// Lock 分布式锁
	Lock(ctx context.Context, key string, ttl time.Duration) (*Lock, error)

	// TryLock 尝试获取分布式锁
	TryLock(ctx context.Context, key string, ttl time.Duration) (*Lock, error)

	// Publish 发布消息
	Publish(ctx context.Context, channel string, message []byte) error

	// Subscribe 订阅消息
	Subscribe(ctx context.Context, channel string) (<-chan *Message, error)
}

// CacheManager 缓存管理器接口
// 管理多个缓存实例和策略
type CacheManager interface {
	// GetCache 根据名称获取缓存实例
	GetCache(name string) (AdvancedCache, error)

	// GetGenericCache 获取泛型缓存实例
	GetGenericCache(name string) (GenericCache, error)

	// GetDistributedCache 获取分布式缓存实例
	GetDistributedCache(name string) (DistributedCache, error)

	// RegisterCache 注册缓存实例
	RegisterCache(name string, cache AdvancedCache) error

	// ListCaches 列出所有缓存实例名称
	ListCaches() []string

	// GetStats 获取所有缓存的统计信息
	GetStats(ctx context.Context) (map[string]*CacheInfo, error)

	// Close 关闭所有缓存连接
	Close() error
}

// Serializer 序列化器接口
type Serializer interface {
	// Serialize 序列化对象
	Serialize(obj interface{}) ([]byte, error)

	// Deserialize 反序列化对象
	Deserialize(data []byte, obj interface{}) error

	// ContentType 获取内容类型
	ContentType() string
}

// Compressor 压缩器接口
type Compressor interface {
	// Compress 压缩数据
	Compress(data []byte) ([]byte, error)

	// Decompress 解压数据
	Decompress(data []byte) ([]byte, error)
}

// CacheItem 缓存项
type CacheItem struct {
	Key   string        `json:"key"`
	Value []byte        `json:"value"`
	TTL   time.Duration `json:"ttl"`
}

// CacheInfo 缓存信息
type CacheInfo struct {
	// 连接信息
	ConnectedClients int   `json:"connected_clients"`
	UsedMemory       int64 `json:"used_memory"`
	MaxMemory        int64 `json:"max_memory"`

	// 统计信息
	TotalKeys   int64 `json:"total_keys"`
	ExpiredKeys int64 `json:"expired_keys"`
	EvictedKeys int64 `json:"evicted_keys"`

	// 性能指标
	HitRate       float64 `json:"hit_rate"`
	TotalHits     int64   `json:"total_hits"`
	TotalMisses   int64   `json:"total_misses"`
	TotalCommands int64   `json:"total_commands"`

	// 网络统计
	TotalConnections int64 `json:"total_connections"`
	InstantaneousOps int64 `json:"instantaneous_ops"`

	// 服务器信息
	Version         string `json:"version"`
	RedisMode       string `json:"redis_mode"`
	Role            string `json:"role"`
	UptimeInSeconds int64  `json:"uptime_in_seconds"`
}

// Lock 分布式锁
type Lock struct {
	Key   string        `json:"key"`
	Value string        `json:"value"`
	TTL   time.Duration `json:"ttl"`
	cache DistributedCache
}

// Unlock 释放锁
func (l *Lock) Unlock(ctx context.Context) error {
	// 实现将在具体的缓存适配器中提供
	return nil
}

// Extend 延长锁的生存时间
func (l *Lock) Extend(ctx context.Context, ttl time.Duration) error {
	// 实现将在具体的缓存适配器中提供
	return nil
}

// Message 消息
type Message struct {
	Channel string `json:"channel"`
	Pattern string `json:"pattern"`
	Payload []byte `json:"payload"`
}

// CacheStrategy 缓存策略
type CacheStrategy struct {
	Name     string        `json:"name"`
	TTL      time.Duration `json:"ttl"`
	MaxSize  int           `json:"max_size"`
	Enabled  bool          `json:"enabled"`
	Compress bool          `json:"compress"`
}

// KeyBuilder 缓存键构建器接口
type KeyBuilder interface {
	// Build 构建缓存键
	Build(parts ...string) string

	// BuildWithTenant 构建包含租户信息的缓存键
	BuildWithTenant(tenantID string, parts ...string) string

	// BuildWithPrefix 构建包含前缀的缓存键
	BuildWithPrefix(prefix string, parts ...string) string

	// Parse 解析缓存键
	Parse(key string) ([]string, error)
}

// MetricsCollector 缓存指标收集器接口
type MetricsCollector interface {
	// RecordHit 记录缓存命中
	RecordHit(key string)

	// RecordMiss 记录缓存未命中
	RecordMiss(key string)

	// RecordLatency 记录操作延迟
	RecordLatency(operation string, duration time.Duration)

	// RecordError 记录错误
	RecordError(operation string, err error)

	// GetStats 获取统计信息
	GetStats() map[string]interface{}
}

// CacheEventType 缓存事件类型
type CacheEventType string

const (
	CacheEventTypeSet    CacheEventType = "set"
	CacheEventTypeGet    CacheEventType = "get"
	CacheEventTypeDel    CacheEventType = "del"
	CacheEventTypeExpire CacheEventType = "expire"
	CacheEventTypeEvict  CacheEventType = "evict"
)

// CacheEvent 缓存事件
type CacheEvent struct {
	Type      CacheEventType `json:"type"`
	Key       string         `json:"key"`
	Value     []byte         `json:"value,omitempty"`
	Timestamp time.Time      `json:"timestamp"`
	TTL       time.Duration  `json:"ttl,omitempty"`
}

// CacheEventListener 缓存事件监听器接口
type CacheEventListener interface {
	// OnEvent 处理缓存事件
	OnEvent(ctx context.Context, event *CacheEvent) error
}
