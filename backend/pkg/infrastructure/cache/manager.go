package cache

import (
	"context"
	"fmt"
	"strings"
	"sync"
	"time"

	apperrors "backend/pkg/common/errors"
	"backend/pkg/common/errors/codes"
	"backend/pkg/infrastructure/config"
	"backend/pkg/infrastructure/logger"
)

// Manager 缓存管理器实现
type Manager struct {
	caches     map[string]AdvancedCache
	gCaches    map[string]GenericCache
	dCaches    map[string]DistributedCache
	strategies map[string]*CacheStrategy
	config     *config.CacheConfig
	logger     logger.Logger
	mu         sync.RWMutex
}

// NewManager 创建缓存管理器
func NewManager(cfg *config.CacheConfig, logger logger.Logger) *Manager {
	return &Manager{
		caches:     make(map[string]AdvancedCache),
		gCaches:    make(map[string]GenericCache),
		dCaches:    make(map[string]DistributedCache),
		strategies: make(map[string]*CacheStrategy),
		config:     cfg,
		logger:     logger,
	}
}

// GetCache 根据名称获取缓存实例
func (m *Manager) GetCache(name string) (AdvancedCache, error) {
	m.mu.RLock()
	defer m.mu.RUnlock()

	cache, exists := m.caches[name]
	if !exists {
		return nil, apperrors.NewInternal(codes.CacheConnection, "缓存实例不存在").Build()
	}

	return cache, nil
}

// GetGenericCache 获取泛型缓存实例
func (m *Manager) GetGenericCache(name string) (GenericCache, error) {
	m.mu.RLock()
	defer m.mu.RUnlock()

	cache, exists := m.gCaches[name]
	if !exists {
		return nil, apperrors.NewInternal(codes.CacheConnection, "泛型缓存实例不存在").Build()
	}

	return cache, nil
}

// GetDistributedCache 获取分布式缓存实例
func (m *Manager) GetDistributedCache(name string) (DistributedCache, error) {
	m.mu.RLock()
	defer m.mu.RUnlock()

	cache, exists := m.dCaches[name]
	if !exists {
		return nil, apperrors.NewInternal(codes.CacheConnection, "分布式缓存实例不存在").Build()
	}

	return cache, nil
}

// RegisterCache 注册缓存实例
func (m *Manager) RegisterCache(name string, cache AdvancedCache) error {
	if name == "" {
		return apperrors.NewInternal(codes.CacheConnection, "缓存名称不能为空").Build()
	}

	if cache == nil {
		return apperrors.NewInternal(codes.CacheConnection, "缓存实例不能为空").Build()
	}

	m.mu.Lock()
	defer m.mu.Unlock()

	if _, exists := m.caches[name]; exists {
		return apperrors.NewInternal(codes.CacheConnection, "缓存实例已存在").Build()
	}

	m.caches[name] = cache

	// 如果实现了GenericCache接口，也注册到泛型缓存中
	if gCache, ok := cache.(GenericCache); ok {
		m.gCaches[name] = gCache
	}

	// 如果实现了DistributedCache接口，也注册到分布式缓存中
	if dCache, ok := cache.(DistributedCache); ok {
		m.dCaches[name] = dCache
	}

	m.logger.InfoNoCtx("缓存实例注册成功", "name", name)
	return nil
}

// ListCaches 列出所有缓存实例名称
func (m *Manager) ListCaches() []string {
	m.mu.RLock()
	defer m.mu.RUnlock()

	names := make([]string, 0, len(m.caches))
	for name := range m.caches {
		names = append(names, name)
	}

	return names
}

// GetStats 获取所有缓存的统计信息
func (m *Manager) GetStats(ctx context.Context) (map[string]*CacheInfo, error) {
	m.mu.RLock()
	defer m.mu.RUnlock()

	stats := make(map[string]*CacheInfo)

	for name, cache := range m.caches {
		info, err := cache.Info(ctx)
		if err != nil {
			m.logger.Error(ctx, "获取缓存统计信息失败", "name", name, "error", err)
			continue
		}
		stats[name] = info
	}

	return stats, nil
}

// GetStrategy 获取缓存策略
func (m *Manager) GetStrategy(name string) (*CacheStrategy, bool) {
	m.mu.RLock()
	defer m.mu.RUnlock()

	// 首先检查是否有专门的策略配置
	if strategy, exists := m.strategies[name]; exists {
		return strategy, true
	}

	// 检查配置文件中的策略
	if m.config != nil && m.config.Strategies != nil {
		if cfgStrategy, exists := m.config.Strategies[name]; exists {
			strategy := &CacheStrategy{
				Name:     name,
				TTL:      cfgStrategy.TTL,
				MaxSize:  cfgStrategy.MaxSize,
				Enabled:  cfgStrategy.Enabled,
				Compress: cfgStrategy.Compress,
			}
			return strategy, true
		}
	}

	// 返回默认策略
	return &CacheStrategy{
		Name:     name,
		TTL:      m.config.DefaultTTL,
		MaxSize:  m.config.MaxKeys,
		Enabled:  true,
		Compress: false,
	}, false
}

// SetStrategy 设置缓存策略
func (m *Manager) SetStrategy(name string, strategy *CacheStrategy) {
	m.mu.Lock()
	defer m.mu.Unlock()

	m.strategies[name] = strategy
	m.logger.InfoNoCtx("缓存策略设置成功", "name", name, "ttl", strategy.TTL, "max_size", strategy.MaxSize)
}

// Close 关闭所有缓存连接
func (m *Manager) Close() error {
	m.mu.Lock()
	defer m.mu.Unlock()

	var errors []string

	for name, cache := range m.caches {
		if err := cache.Close(); err != nil {
			errorMsg := fmt.Sprintf("关闭缓存[%s]失败: %v", name, err)
			errors = append(errors, errorMsg)
			m.logger.ErrorNoCtx("关闭缓存失败", "name", name, "error", err)
		} else {
			m.logger.InfoNoCtx("缓存已关闭", "name", name)
		}
	}

	if len(errors) > 0 {
		return apperrors.NewInternal(codes.CacheConnection, "关闭缓存时发生错误").Wrap(fmt.Errorf("%s", strings.Join(errors, "; "))).Build()
	}

	return nil
}

// DefaultKeyBuilder 默认键构建器实现
type DefaultKeyBuilder struct {
	prefix    string
	separator string
}

// NewDefaultKeyBuilder 创建默认键构建器
func NewDefaultKeyBuilder(prefix, separator string) KeyBuilder {
	if separator == "" {
		separator = ":"
	}
	return &DefaultKeyBuilder{
		prefix:    prefix,
		separator: separator,
	}
}

// Build 构建缓存键
func (kb *DefaultKeyBuilder) Build(parts ...string) string {
	if len(parts) == 0 {
		return kb.prefix
	}

	// 过滤空字符串
	validParts := make([]string, 0, len(parts)+1)
	if kb.prefix != "" {
		validParts = append(validParts, kb.prefix)
	}

	for _, part := range parts {
		if part != "" {
			validParts = append(validParts, part)
		}
	}

	return strings.Join(validParts, kb.separator)
}

// BuildWithTenant 构建包含租户信息的缓存键
func (kb *DefaultKeyBuilder) BuildWithTenant(tenantID string, parts ...string) string {
	if tenantID == "" {
		return kb.Build(parts...)
	}

	allParts := make([]string, 0, len(parts)+1)
	allParts = append(allParts, tenantID)
	allParts = append(allParts, parts...)

	return kb.Build(allParts...)
}

// BuildWithPrefix 构建包含前缀的缓存键
func (kb *DefaultKeyBuilder) BuildWithPrefix(prefix string, parts ...string) string {
	if prefix == "" {
		return kb.Build(parts...)
	}

	allParts := make([]string, 0, len(parts)+1)
	allParts = append(allParts, prefix)
	allParts = append(allParts, parts...)

	return kb.Build(allParts...)
}

// Parse 解析缓存键
func (kb *DefaultKeyBuilder) Parse(key string) ([]string, error) {
	if key == "" {
		return nil, apperrors.NewInternal(codes.CacheOperation, "缓存键不能为空").Build()
	}

	parts := strings.Split(key, kb.separator)

	// 移除前缀（如果存在）
	if kb.prefix != "" && len(parts) > 0 && parts[0] == kb.prefix {
		parts = parts[1:]
	}

	return parts, nil
}

// DefaultMetricsCollector 默认指标收集器实现
type DefaultMetricsCollector struct {
	hits    int64
	misses  int64
	errors  int64
	latency map[string][]time.Duration
	mutex   sync.RWMutex
	maxSize int // 延迟数据最大保存数量
}

// NewDefaultMetricsCollector 创建默认指标收集器
func NewDefaultMetricsCollector() MetricsCollector {
	return &DefaultMetricsCollector{
		latency: make(map[string][]time.Duration),
		maxSize: 1000, // 默认保存最近1000条延迟数据
	}
}

// RecordHit 记录缓存命中
func (mc *DefaultMetricsCollector) RecordHit(key string) {
	mc.mutex.Lock()
	defer mc.mutex.Unlock()
	mc.hits++
}

// RecordMiss 记录缓存未命中
func (mc *DefaultMetricsCollector) RecordMiss(key string) {
	mc.mutex.Lock()
	defer mc.mutex.Unlock()
	mc.misses++
}

// RecordLatency 记录操作延迟
func (mc *DefaultMetricsCollector) RecordLatency(operation string, duration time.Duration) {
	mc.mutex.Lock()
	defer mc.mutex.Unlock()

	if mc.latency[operation] == nil {
		mc.latency[operation] = make([]time.Duration, 0, mc.maxSize)
	}

	// 如果超过最大数量，移除最旧的数据
	if len(mc.latency[operation]) >= mc.maxSize {
		mc.latency[operation] = mc.latency[operation][1:]
	}

	mc.latency[operation] = append(mc.latency[operation], duration)
}

// RecordError 记录错误
func (mc *DefaultMetricsCollector) RecordError(operation string, err error) {
	mc.mutex.Lock()
	defer mc.mutex.Unlock()
	mc.errors++
}

// GetStats 获取统计信息
func (mc *DefaultMetricsCollector) GetStats() map[string]interface{} {
	mc.mutex.RLock()
	defer mc.mutex.RUnlock()

	total := mc.hits + mc.misses
	hitRate := float64(0)
	if total > 0 {
		hitRate = float64(mc.hits) / float64(total)
	}

	// 计算平均延迟
	avgLatency := make(map[string]time.Duration)
	for operation, durations := range mc.latency {
		if len(durations) > 0 {
			var total time.Duration
			for _, d := range durations {
				total += d
			}
			avgLatency[operation] = total / time.Duration(len(durations))
		}
	}

	return map[string]interface{}{
		"hits":        mc.hits,
		"misses":      mc.misses,
		"total":       total,
		"hit_rate":    hitRate,
		"errors":      mc.errors,
		"avg_latency": avgLatency,
		"operations":  len(mc.latency),
	}
}

// CacheFactory 缓存工厂
type CacheFactory struct {
	config *config.Config
	logger logger.Logger
}

// NewCacheFactory 创建缓存工厂
func NewCacheFactory(cfg *config.Config, logger logger.Logger) *CacheFactory {
	return &CacheFactory{
		config: cfg,
		logger: logger,
	}
}

// CreateKeyBuilder 创建键构建器
func (cf *CacheFactory) CreateKeyBuilder() KeyBuilder {
	prefix := "default"
	separator := ":"

	if cf.config.Business.Cache.Redis.KeyPrefix != "" {
		prefix = cf.config.Business.Cache.Redis.KeyPrefix
	}

	if cf.config.Business.Cache.Redis.KeySeparator != "" {
		separator = cf.config.Business.Cache.Redis.KeySeparator
	}

	return NewDefaultKeyBuilder(prefix, separator)
}

// CreateMetricsCollector 创建指标收集器
func (cf *CacheFactory) CreateMetricsCollector() MetricsCollector {
	return NewDefaultMetricsCollector()
}
