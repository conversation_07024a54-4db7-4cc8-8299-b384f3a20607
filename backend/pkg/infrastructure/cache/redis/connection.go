package redis

import (
	"context"
	"fmt"
	"time"

	"github.com/redis/go-redis/v9"

	"backend/pkg/infrastructure/config"
	"backend/pkg/infrastructure/logger"
)

// ConnectionManager Redis连接管理器
type ConnectionManager struct {
	config *config.RedisConfig
	logger logger.Logger
	client *redis.Client
}

// NewConnectionManager 创建新的Redis连接管理器
func NewConnectionManager(cfg *config.RedisConfig, log logger.Logger) *ConnectionManager {
	return &ConnectionManager{
		config: cfg,
		logger: log,
	}
}

// Connect 建立Redis连接
func (cm *ConnectionManager) Connect(ctx context.Context) error {
	// 创建Redis客户端配置
	opts := cm.buildRedisOptions()

	// 创建Redis客户端
	cm.client = redis.NewClient(opts)

	// 测试连接
	if err := cm.client.Ping(ctx).Err(); err != nil {
		cm.logger.Error(ctx, "redis连接失败", "error", err, "addr", opts.Addr)
		return fmt.Errorf("redis连接失败: %w", err)
	}

	cm.logger.Info(ctx, "redis连接成功", "addr", opts.Addr, "db", opts.DB)
	return nil
}

// GetClient 获取Redis客户端
func (cm *ConnectionManager) GetClient() *redis.Client {
	return cm.client
}

// Close 关闭Redis连接
func (cm *ConnectionManager) Close(ctx context.Context) error {
	if cm.client == nil {
		return nil
	}

	cm.logger.Info(ctx, "正在关闭redis连接")
	err := cm.client.Close()
	if err != nil {
		cm.logger.Error(ctx, "关闭redis连接失败", "error", err)
		return fmt.Errorf("关闭Redis连接失败: %w", err)
	}

	cm.logger.Info(ctx, "redis连接已关闭")
	return nil
}

// HealthCheck 健康检查
func (cm *ConnectionManager) HealthCheck(ctx context.Context) error {
	if cm.client == nil {
		return fmt.Errorf("redis客户端未初始化")
	}

	// 执行PING命令测试连接
	result := cm.client.Ping(ctx)
	if err := result.Err(); err != nil {
		cm.logger.Error(ctx, "redis健康检查失败", "error", err)
		return fmt.Errorf("redis健康检查失败: %w", err)
	}

	// 检查响应
	pong, err := result.Result()
	if err != nil {
		return fmt.Errorf("获取redis响应失败: %w", err)
	}

	if pong != "PONG" {
		return fmt.Errorf("redis响应异常: %s", pong)
	}

	return nil
}

// GetStats 获取Redis连接统计信息
func (cm *ConnectionManager) GetStats(ctx context.Context) (*ConnectionStats, error) {
	if cm.client == nil {
		return nil, fmt.Errorf("redis客户端未初始化")
	}

	// 获取连接池统计信息
	poolStats := cm.client.PoolStats()

	// 获取Redis服务器信息
	info, err := cm.client.Info(ctx, "server", "memory", "stats").Result()
	if err != nil {
		cm.logger.Error(ctx, "获取redis信息失败", "error", err)
		return nil, fmt.Errorf("获取redis信息失败: %w", err)
	}

	return &ConnectionStats{
		PoolStats:  poolStats,
		ServerInfo: info,
		Timestamp:  time.Now(),
	}, nil
}

// Reconnect 重新连接
func (cm *ConnectionManager) Reconnect(ctx context.Context) error {
	cm.logger.Info(ctx, "正在尝试重新连接redis")

	// 关闭现有连接
	if cm.client != nil {
		_ = cm.client.Close()
	}

	// 重新建立连接
	return cm.Connect(ctx)
}

// buildRedisOptions 构建Redis客户端配置
func (cm *ConnectionManager) buildRedisOptions() *redis.Options {
	opts := &redis.Options{
		// 基础连接配置
		Addr:     cm.config.GetAddr(),
		Password: cm.config.Password,
		DB:       cm.config.DB,

		// 连接池配置
		PoolSize:     cm.config.PoolSize,
		MinIdleConns: cm.config.MinIdleConns,
		PoolTimeout:  cm.config.PoolTimeout,

		// 操作超时配置
		DialTimeout:  cm.config.DialTimeout,
		ReadTimeout:  cm.config.ReadTimeout,
		WriteTimeout: cm.config.WriteTimeout,

		// 重试配置
		MaxRetries:      cm.config.MaxRetries,
		MinRetryBackoff: cm.config.MinRetryBackoff,
		MaxRetryBackoff: cm.config.MaxRetryBackoff,
	}

	// 设置连接钩子函数
	opts.OnConnect = func(ctx context.Context, cn *redis.Conn) error {
		cm.logger.Debug(ctx, "redis连接建立", "addr", cn.String())
		return nil
	}

	return opts
}

// ConnectionStats Redis连接统计信息
type ConnectionStats struct {
	PoolStats  *redis.PoolStats `json:"pool_stats"`
	ServerInfo string           `json:"server_info"`
	Timestamp  time.Time        `json:"timestamp"`
}

// ConnectionFactory Redis连接工厂
type ConnectionFactory struct {
	logger logger.Logger
}

// NewConnectionFactory 创建Redis连接工厂
func NewConnectionFactory(log logger.Logger) *ConnectionFactory {
	return &ConnectionFactory{
		logger: log,
	}
}

// CreateConnection 创建Redis连接
func (cf *ConnectionFactory) CreateConnection(cfg *config.RedisConfig) (*ConnectionManager, error) {
	if cfg == nil {
		return nil, fmt.Errorf("redis配置不能为空")
	}

	// 验证配置
	if err := cf.validateConfig(cfg); err != nil {
		return nil, fmt.Errorf("redis配置验证失败: %w", err)
	}

	// 创建连接管理器
	manager := NewConnectionManager(cfg, cf.logger)

	cf.logger.InfoNoCtx("创建redis连接管理器",
		"addr", cfg.GetAddr(),
		"db", cfg.DB,
		"pool_size", cfg.PoolSize)

	return manager, nil
}

// CreateClusterConnection 创建Redis集群连接（预留接口）
func (cf *ConnectionFactory) CreateClusterConnection(cfg *config.RedisConfig, addrs []string) (*redis.ClusterClient, error) {
	// TODO: 实现Redis集群连接
	cf.logger.InfoNoCtx("创建redis集群连接", "addrs", addrs)
	return nil, fmt.Errorf("redis集群连接暂未实现")
}

// validateConfig 验证Redis配置
func (cf *ConnectionFactory) validateConfig(cfg *config.RedisConfig) error {
	if cfg.Host == "" {
		return fmt.Errorf("redis主机地址不能为空")
	}

	if cfg.Port <= 0 || cfg.Port > 65535 {
		return fmt.Errorf("redis端口号无效: %d", cfg.Port)
	}

	if cfg.DB < 0 || cfg.DB > 15 {
		return fmt.Errorf("redis数据库编号无效: %d", cfg.DB)
	}

	if cfg.PoolSize <= 0 {
		cfg.PoolSize = 10 // 设置默认值
	}

	if cfg.MinIdleConns < 0 {
		cfg.MinIdleConns = 0
	}

	// 设置默认超时值
	if cfg.DialTimeout <= 0 {
		cfg.DialTimeout = 5 * time.Second
	}

	if cfg.ReadTimeout <= 0 {
		cfg.ReadTimeout = 3 * time.Second
	}

	if cfg.WriteTimeout <= 0 {
		cfg.WriteTimeout = 3 * time.Second
	}

	if cfg.PoolTimeout <= 0 {
		cfg.PoolTimeout = 4 * time.Second
	}

	if cfg.IdleTimeout <= 0 {
		cfg.IdleTimeout = 5 * time.Minute
	}

	if cfg.IdleCheckFreq <= 0 {
		cfg.IdleCheckFreq = 1 * time.Minute
	}

	if cfg.MaxConnAge <= 0 {
		cfg.MaxConnAge = 30 * time.Minute
	}

	// 设置默认重试配置
	if cfg.MaxRetries < 0 {
		cfg.MaxRetries = 3
	}

	if cfg.MinRetryBackoff <= 0 {
		cfg.MinRetryBackoff = 8 * time.Millisecond
	}

	if cfg.MaxRetryBackoff <= 0 {
		cfg.MaxRetryBackoff = 512 * time.Millisecond
	}

	return nil
}
