package redis

import (
	"context"
	"fmt"
	"strconv"
	"strings"
	"time"

	"github.com/redis/go-redis/v9"

	"backend/pkg/infrastructure/cache"
	"backend/pkg/infrastructure/logger"
)

// Client Redis客户端封装
type Client struct {
	client  *redis.Client
	logger  logger.Logger
	metrics cache.MetricsCollector
}

// NewClient 创建Redis客户端封装
func NewClient(redisClient *redis.Client, logger logger.Logger, metrics cache.MetricsCollector) *Client {
	return &Client{
		client:  redisClient,
		logger:  logger,
		metrics: metrics,
	}
}

// Get 获取缓存值
func (c *Client) Get(ctx context.Context, key string) ([]byte, error) {
	start := time.Now()
	defer func() {
		if c.metrics != nil {
			c.metrics.RecordLatency("get", time.Since(start))
		}
	}()

	result, err := c.client.Get(ctx, key).Result()
	if err != nil {
		if err == redis.Nil {
			if c.metrics != nil {
				c.metrics.RecordMiss(key)
			}
			return nil, cache.ErrKeyNotFound
		}

		if c.metrics != nil {
			c.metrics.RecordError("get", err)
		}
		c.logger.Error(ctx, "redis GET操作失败", "key", key, "error", err)
		return nil, fmt.Errorf("redis GET操作失败: %w", err)
	}

	if c.metrics != nil {
		c.metrics.RecordHit(key)
	}

	return []byte(result), nil
}

// Set 设置缓存值
func (c *Client) Set(ctx context.Context, key string, value []byte, ttl time.Duration) error {
	start := time.Now()
	defer func() {
		if c.metrics != nil {
			c.metrics.RecordLatency("set", time.Since(start))
		}
	}()

	err := c.client.Set(ctx, key, value, ttl).Err()
	if err != nil {
		if c.metrics != nil {
			c.metrics.RecordError("set", err)
		}
		c.logger.Error(ctx, "redis SET操作失败", "key", key, "error", err)
		return fmt.Errorf("redis SET操作失败: %w", err)
	}

	return nil
}

// Del 删除缓存
func (c *Client) Del(ctx context.Context, keys ...string) error {
	if len(keys) == 0 {
		return nil
	}

	start := time.Now()
	defer func() {
		if c.metrics != nil {
			c.metrics.RecordLatency("del", time.Since(start))
		}
	}()

	err := c.client.Del(ctx, keys...).Err()
	if err != nil {
		if c.metrics != nil {
			c.metrics.RecordError("del", err)
		}
		c.logger.Error(ctx, "redis DEL操作失败", "keys", keys, "error", err)
		return fmt.Errorf("redis DEL操作失败: %w", err)
	}

	return nil
}

// Exists 检查键是否存在
func (c *Client) Exists(ctx context.Context, key string) (bool, error) {
	start := time.Now()
	defer func() {
		if c.metrics != nil {
			c.metrics.RecordLatency("exists", time.Since(start))
		}
	}()

	result, err := c.client.Exists(ctx, key).Result()
	if err != nil {
		if c.metrics != nil {
			c.metrics.RecordError("exists", err)
		}
		c.logger.Error(ctx, "redis EXISTS操作失败", "key", key, "error", err)
		return false, fmt.Errorf("redis EXISTS操作失败: %w", err)
	}

	return result > 0, nil
}

// TTL 获取键的剩余生存时间
func (c *Client) TTL(ctx context.Context, key string) (time.Duration, error) {
	start := time.Now()
	defer func() {
		if c.metrics != nil {
			c.metrics.RecordLatency("ttl", time.Since(start))
		}
	}()

	result, err := c.client.TTL(ctx, key).Result()
	if err != nil {
		if c.metrics != nil {
			c.metrics.RecordError("ttl", err)
		}
		c.logger.Error(ctx, "redis TTL操作失败", "key", key, "error", err)
		return 0, fmt.Errorf("redis TTL操作失败: %w", err)
	}

	return result, nil
}

// SetTTL 设置键的生存时间
func (c *Client) SetTTL(ctx context.Context, key string, ttl time.Duration) error {
	start := time.Now()
	defer func() {
		if c.metrics != nil {
			c.metrics.RecordLatency("expire", time.Since(start))
		}
	}()

	err := c.client.Expire(ctx, key, ttl).Err()
	if err != nil {
		if c.metrics != nil {
			c.metrics.RecordError("expire", err)
		}
		c.logger.Error(ctx, "redis EXPIRE操作失败", "key", key, "ttl", ttl, "error", err)
		return fmt.Errorf("redis EXPIRE操作失败: %w", err)
	}

	return nil
}

// BatchGet 批量获取缓存值
func (c *Client) BatchGet(ctx context.Context, keys []string) (map[string][]byte, error) {
	if len(keys) == 0 {
		return make(map[string][]byte), nil
	}

	start := time.Now()
	defer func() {
		if c.metrics != nil {
			c.metrics.RecordLatency("mget", time.Since(start))
		}
	}()

	result, err := c.client.MGet(ctx, keys...).Result()
	if err != nil {
		if c.metrics != nil {
			c.metrics.RecordError("mget", err)
		}
		c.logger.Error(ctx, "redis MGET操作失败", "keys", keys, "error", err)
		return nil, fmt.Errorf("redis MGET操作失败: %w", err)
	}

	values := make(map[string][]byte)
	for i, val := range result {
		if val != nil {
			if str, ok := val.(string); ok {
				values[keys[i]] = []byte(str)
				if c.metrics != nil {
					c.metrics.RecordHit(keys[i])
				}
			}
		} else {
			if c.metrics != nil {
				c.metrics.RecordMiss(keys[i])
			}
		}
	}

	return values, nil
}

// BatchSet 批量设置缓存值
func (c *Client) BatchSet(ctx context.Context, items map[string]cache.CacheItem) error {
	if len(items) == 0 {
		return nil
	}

	start := time.Now()
	defer func() {
		if c.metrics != nil {
			c.metrics.RecordLatency("mset", time.Since(start))
		}
	}()

	// 使用Pipeline进行批量操作
	pipe := c.client.Pipeline()

	for key, item := range items {
		pipe.Set(ctx, key, item.Value, item.TTL)
	}

	_, err := pipe.Exec(ctx)
	if err != nil {
		if c.metrics != nil {
			c.metrics.RecordError("mset", err)
		}
		c.logger.Error(ctx, "redis批量SET操作失败", "count", len(items), "error", err)
		return fmt.Errorf("redis批量SET操作失败: %w", err)
	}

	return nil
}

// BatchDel 批量删除缓存
func (c *Client) BatchDel(ctx context.Context, keys []string) error {
	if len(keys) == 0 {
		return nil
	}

	return c.Del(ctx, keys...)
}

// Keys 根据模式获取键列表
func (c *Client) Keys(ctx context.Context, pattern string) ([]string, error) {
	start := time.Now()
	defer func() {
		if c.metrics != nil {
			c.metrics.RecordLatency("keys", time.Since(start))
		}
	}()

	result, err := c.client.Keys(ctx, pattern).Result()
	if err != nil {
		if c.metrics != nil {
			c.metrics.RecordError("keys", err)
		}
		c.logger.Error(ctx, "redis KEYS操作失败", "pattern", pattern, "error", err)
		return nil, fmt.Errorf("redis KEYS操作失败: %w", err)
	}

	return result, nil
}

// Scan 扫描键，支持游标和分页
func (c *Client) Scan(ctx context.Context, cursor uint64, pattern string, count int64) ([]string, uint64, error) {
	start := time.Now()
	defer func() {
		if c.metrics != nil {
			c.metrics.RecordLatency("scan", time.Since(start))
		}
	}()

	result, newCursor, err := c.client.Scan(ctx, cursor, pattern, count).Result()
	if err != nil {
		if c.metrics != nil {
			c.metrics.RecordError("scan", err)
		}
		c.logger.Error(ctx, "redis SCAN操作失败", "cursor", cursor, "pattern", pattern, "count", count, "error", err)
		return nil, 0, fmt.Errorf("redis SCAN操作失败: %w", err)
	}

	return result, newCursor, nil
}

// SAdd 添加元素到集合
func (c *Client) SAdd(ctx context.Context, key string, members ...string) error {
	start := time.Now()
	defer func() {
		if c.metrics != nil {
			c.metrics.RecordLatency("sadd", time.Since(start))
		}
	}()
	err := c.client.SAdd(ctx, key, members).Err()
	if err != nil {
		if c.metrics != nil {
			c.metrics.RecordError("sadd", err)
		}
		c.logger.Error(ctx, "redis SADD操作失败", "key", key, "members", members, "error", err)
		return fmt.Errorf("redis SADD操作失败: %w", err)
	}
	return nil
}

// SRem 从集合中移除元素
func (c *Client) SRem(ctx context.Context, key string, members ...string) error {
	start := time.Now()
	defer func() {
		if c.metrics != nil {
			c.metrics.RecordLatency("srem", time.Since(start))
		}
	}()
	err := c.client.SRem(ctx, key, members).Err()
	if err != nil {
		if c.metrics != nil {
			c.metrics.RecordError("srem", err)
		}
		c.logger.Error(ctx, "redis SREM操作失败", "key", key, "members", members, "error", err)
		return fmt.Errorf("redis SREM操作失败: %w", err)
	}
	return nil
}

// SMembers 获取集合中的所有元素
func (c *Client) SMembers(ctx context.Context, key string) ([]string, error) {
	start := time.Now()
	defer func() {
		if c.metrics != nil {
			c.metrics.RecordLatency("smembers", time.Since(start))
		}
	}()
	result, err := c.client.SMembers(ctx, key).Result()
	if err != nil {
		if c.metrics != nil {
			c.metrics.RecordError("smembers", err)
		}
		c.logger.Error(ctx, "redis SMEMBERS操作失败", "key", key, "error", err)
		return nil, fmt.Errorf("redis SMEMBERS操作失败: %w", err)
	}
	return result, nil
}

// SCard 获取集合中的元素数量
func (c *Client) SCard(ctx context.Context, key string) (int64, error) {
	start := time.Now()
	defer func() {
		if c.metrics != nil {
			c.metrics.RecordLatency("scard", time.Since(start))
		}
	}()
	result, err := c.client.SCard(ctx, key).Result()
	if err != nil {
		if c.metrics != nil {
			c.metrics.RecordError("scard", err)
		}
		c.logger.Error(ctx, "redis SCARD操作失败", "key", key, "error", err)
		return 0, fmt.Errorf("redis SCARD操作失败: %w", err)
	}
	return result, nil
}

// FlushDB 清空当前数据库
func (c *Client) FlushDB(ctx context.Context) error {
	start := time.Now()
	defer func() {
		if c.metrics != nil {
			c.metrics.RecordLatency("flushdb", time.Since(start))
		}
	}()

	err := c.client.FlushDB(ctx).Err()
	if err != nil {
		if c.metrics != nil {
			c.metrics.RecordError("flushdb", err)
		}
		c.logger.Error(ctx, "redis FLUSHDB操作失败", "error", err)
		return fmt.Errorf("redis FLUSHDB操作失败: %w", err)
	}

	c.logger.Warn(ctx, "redis数据库已清空")
	return nil
}

// Info 获取缓存统计信息
func (c *Client) Info(ctx context.Context) (*cache.CacheInfo, error) {
	start := time.Now()
	defer func() {
		if c.metrics != nil {
			c.metrics.RecordLatency("info", time.Since(start))
		}
	}()

	// 获取Redis服务器信息
	info, err := c.client.Info(ctx, "server", "memory", "stats", "clients", "keyspace").Result()
	if err != nil {
		if c.metrics != nil {
			c.metrics.RecordError("info", err)
		}
		c.logger.Error(ctx, "redis INFO操作失败", "error", err)
		return nil, fmt.Errorf("redis INFO操作失败: %w", err)
	}

	// 解析INFO信息
	cacheInfo := c.parseInfo(info)

	// 获取连接池统计
	poolStats := c.client.PoolStats()
	cacheInfo.ConnectedClients = int(poolStats.TotalConns - poolStats.IdleConns)

	return cacheInfo, nil
}

// Ping 测试连接
func (c *Client) Ping(ctx context.Context) error {
	start := time.Now()
	defer func() {
		if c.metrics != nil {
			c.metrics.RecordLatency("ping", time.Since(start))
		}
	}()

	err := c.client.Ping(ctx).Err()
	if err != nil {
		if c.metrics != nil {
			c.metrics.RecordError("ping", err)
		}
		c.logger.Error(ctx, "redis PING操作失败", "error", err)
		return fmt.Errorf("redis PING操作失败: %w", err)
	}

	return nil
}

// Close 关闭客户端
func (c *Client) Close() error {
	if c.client != nil {
		return c.client.Close()
	}
	return nil
}

// parseInfo 解析Redis INFO命令的输出
func (c *Client) parseInfo(info string) *cache.CacheInfo {
	cacheInfo := &cache.CacheInfo{}

	lines := strings.Split(info, "\r\n")
	for _, line := range lines {
		if strings.Contains(line, ":") {
			parts := strings.SplitN(line, ":", 2)
			if len(parts) != 2 {
				continue
			}

			key := strings.TrimSpace(parts[0])
			value := strings.TrimSpace(parts[1])

			switch key {
			case "redis_version":
				cacheInfo.Version = value
			case "redis_mode":
				cacheInfo.RedisMode = value
			case "role":
				cacheInfo.Role = value
			case "uptime_in_seconds":
				if v, err := strconv.ParseInt(value, 10, 64); err == nil {
					cacheInfo.UptimeInSeconds = v
				}
			case "connected_clients":
				if v, err := strconv.Atoi(value); err == nil {
					cacheInfo.ConnectedClients = v
				}
			case "used_memory":
				if v, err := strconv.ParseInt(value, 10, 64); err == nil {
					cacheInfo.UsedMemory = v
				}
			case "maxmemory":
				if v, err := strconv.ParseInt(value, 10, 64); err == nil {
					cacheInfo.MaxMemory = v
				}
			case "total_commands_processed":
				if v, err := strconv.ParseInt(value, 10, 64); err == nil {
					cacheInfo.TotalCommands = v
				}
			case "total_connections_received":
				if v, err := strconv.ParseInt(value, 10, 64); err == nil {
					cacheInfo.TotalConnections = v
				}
			case "instantaneous_ops_per_sec":
				if v, err := strconv.ParseInt(value, 10, 64); err == nil {
					cacheInfo.InstantaneousOps = v
				}
			case "expired_keys":
				if v, err := strconv.ParseInt(value, 10, 64); err == nil {
					cacheInfo.ExpiredKeys = v
				}
			case "evicted_keys":
				if v, err := strconv.ParseInt(value, 10, 64); err == nil {
					cacheInfo.EvictedKeys = v
				}
			}
		}
	}

	return cacheInfo
}

// GetRedisClient 获取底层Redis客户端（谨慎使用）
func (c *Client) GetRedisClient() *redis.Client {
	return c.client
}
