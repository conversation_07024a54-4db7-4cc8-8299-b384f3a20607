package config

import (
	"fmt"
	"time"
)

// Config 应用程序主配置
type Config struct {
	// 环境配置
	Env string `yaml:"env" mapstructure:"env" validate:"required,oneof=development testing production"`

	// 服务器配置
	Server ServerConfig `yaml:"server" mapstructure:"server"`

	// 数据库配置
	Database DatabaseConfig `yaml:"database" mapstructure:"database"`

	// Redis配置
	Redis RedisConfig `yaml:"redis" mapstructure:"redis"`

	// 消息队列配置
	MessageQueue MQConfig `yaml:"message_queue" mapstructure:"message_queue"`

	// 日志配置
	Logger LoggerConfig `yaml:"logger" mapstructure:"logger"`

	// 认证配置
	Auth AuthConfig `yaml:"auth" mapstructure:"auth"`

	// 监控配置
	Monitoring MonitoringConfig `yaml:"monitoring" mapstructure:"monitoring"`

	// 业务配置
	Business BusinessConfig `yaml:"business" mapstructure:"business"`
}

// ServerConfig 服务器配置
type ServerConfig struct {
	Host         string        `yaml:"host" mapstructure:"host"`
	Port         int           `yaml:"port" mapstructure:"port"`
	ReadTimeout  time.Duration `yaml:"read_timeout" mapstructure:"read_timeout"`
	WriteTimeout time.Duration `yaml:"write_timeout" mapstructure:"write_timeout"`
	IdleTimeout  time.Duration `yaml:"idle_timeout" mapstructure:"idle_timeout"`
	GRPC         GRPCConfig    `yaml:"grpc" mapstructure:"grpc"`
}

// GRPCConfig gRPC服务器配置
type GRPCConfig struct {
	Enable bool `yaml:"enable" mapstructure:"enable"`
	Port   int  `yaml:"port" mapstructure:"port"`
}

// RedisConfig Redis配置
type RedisConfig struct {
	Host     string `yaml:"host" mapstructure:"host" validate:"required"`
	Port     int    `yaml:"port" mapstructure:"port" validate:"required,min=1,max=65535"`
	Password string `yaml:"password" mapstructure:"password"`
	DB       int    `yaml:"db" mapstructure:"db" validate:"min=0,max=15"`
	PoolSize int    `yaml:"pool_size" mapstructure:"pool_size"`

	// 连接池高级配置
	MinIdleConns  int           `yaml:"min_idle_conns" mapstructure:"min_idle_conns"`
	MaxConnAge    time.Duration `yaml:"max_conn_age" mapstructure:"max_conn_age"`
	PoolTimeout   time.Duration `yaml:"pool_timeout" mapstructure:"pool_timeout"`
	IdleTimeout   time.Duration `yaml:"idle_timeout" mapstructure:"idle_timeout"`
	IdleCheckFreq time.Duration `yaml:"idle_check_freq" mapstructure:"idle_check_freq"`

	// 操作超时配置
	DialTimeout  time.Duration `yaml:"dial_timeout" mapstructure:"dial_timeout"`
	ReadTimeout  time.Duration `yaml:"read_timeout" mapstructure:"read_timeout"`
	WriteTimeout time.Duration `yaml:"write_timeout" mapstructure:"write_timeout"`

	// 重试配置
	MaxRetries      int           `yaml:"max_retries" mapstructure:"max_retries"`
	MinRetryBackoff time.Duration `yaml:"min_retry_backoff" mapstructure:"min_retry_backoff"`
	MaxRetryBackoff time.Duration `yaml:"max_retry_backoff" mapstructure:"max_retry_backoff"`
}

// LoggerConfig 日志配置
type LoggerConfig struct {
	Level      string `yaml:"level" mapstructure:"level" validate:"required,oneof=debug info warn error"`
	Format     string `yaml:"format" mapstructure:"format" validate:"required,oneof=json text"`
	Output     string `yaml:"output" mapstructure:"output" validate:"required,oneof=stdout stderr file"`
	File       string `yaml:"file" mapstructure:"file"`
	MaxSize    int    `yaml:"max_size" mapstructure:"max_size"`
	MaxAge     int    `yaml:"max_age" mapstructure:"max_age"`
	MaxBackups int    `yaml:"max_backups" mapstructure:"max_backups"`
	Compress   bool   `yaml:"compress" mapstructure:"compress"`
}

// AuthConfig 认证配置
type AuthConfig struct {
	JWT       JWTConfig       `yaml:"jwt" mapstructure:"jwt"`
	Casbin    CasbinConfig    `yaml:"casbin" mapstructure:"casbin"`
	CORS      CORSConfig      `yaml:"cors" mapstructure:"cors"`
	RateLimit RateLimitConfig `yaml:"rate_limit" mapstructure:"rate_limit"`
}

// JWTConfig JWT配置
type JWTConfig struct {
	SecretKey            string        `yaml:"secret_key" mapstructure:"secret_key" validate:"required,min=32"`
	AccessTokenDuration  time.Duration `yaml:"access_token_duration" mapstructure:"access_token_duration"`
	RefreshTokenDuration time.Duration `yaml:"refresh_token_duration" mapstructure:"refresh_token_duration"`
	Issuer               string        `yaml:"issuer" mapstructure:"issuer" validate:"required"`
}

// CasbinConfig Casbin配置
type CasbinConfig struct {
	ModelPath string `yaml:"model_path" mapstructure:"model_path" validate:"required"`
	TableName string `yaml:"table_name" mapstructure:"table_name" validate:"required"`
}

// CORSConfig CORS配置
type CORSConfig struct {
	AllowedOrigins []string `yaml:"allowed_origins" mapstructure:"allowed_origins"`
	AllowedMethods []string `yaml:"allowed_methods" mapstructure:"allowed_methods"`
	AllowedHeaders []string `yaml:"allowed_headers" mapstructure:"allowed_headers"`
}

// RateLimitConfig 限流配置
type RateLimitConfig struct {
	Enable bool `yaml:"enable" mapstructure:"enable"`
	RPS    int  `yaml:"rps" mapstructure:"rps"`
	Burst  int  `yaml:"burst" mapstructure:"burst"`
}

// MonitoringConfig 监控配置
type MonitoringConfig struct {
	Metrics MetricsConfig `yaml:"metrics" mapstructure:"metrics"`
	Tracing TracingConfig `yaml:"tracing" mapstructure:"tracing"`
	Health  HealthConfig  `yaml:"health" mapstructure:"health"`
}

// MetricsConfig 指标配置
type MetricsConfig struct {
	Enable bool   `yaml:"enable" mapstructure:"enable"`
	Path   string `yaml:"path" mapstructure:"path"`
	Port   int    `yaml:"port" mapstructure:"port"`
}

// TracingConfig 链路追踪配置
type TracingConfig struct {
	Enable      bool   `yaml:"enable" mapstructure:"enable"`
	Endpoint    string `yaml:"endpoint" mapstructure:"endpoint"`
	Service     string `yaml:"service" mapstructure:"service"`
	Environment string `yaml:"environment" mapstructure:"environment"`
	Version     string `yaml:"version" mapstructure:"version"`
}

// HealthConfig 健康检查配置
type HealthConfig struct {
	Enable bool   `yaml:"enable" mapstructure:"enable"`
	Path   string `yaml:"path" mapstructure:"path"`
}

// BusinessConfig 业务配置
type BusinessConfig struct {
	// 雪花ID配置
	Snowflake SnowflakeConfig `yaml:"snowflake" mapstructure:"snowflake"`

	// UUID配置
	UUID UUIDConfig `yaml:"uuid" mapstructure:"uuid"`

	// ID生成器配置
	IDGenerator IDGeneratorConfig `yaml:"id_generator" mapstructure:"id_generator"`

	// 文件上传配置
	Upload UploadConfig `yaml:"upload" mapstructure:"upload"`

	// 缓存配置
	Cache CacheConfig `yaml:"cache" mapstructure:"cache"`
}

// SnowflakeConfig 雪花ID配置
type SnowflakeConfig struct {
	MachineID  int64 `yaml:"machine_id" mapstructure:"machine_id" validate:"min=0,max=1023"`
	UseIPBased bool  `yaml:"use_ip_based" mapstructure:"use_ip_based"`
}

// UUIDConfig UUID配置
type UUIDConfig struct {
	RootNamespace    string `yaml:"root_namespace" mapstructure:"root_namespace"`
	EnableSemantic   bool   `yaml:"enable_semantic" mapstructure:"enable_semantic"`
	FallbackToRandom bool   `yaml:"fallback_to_random" mapstructure:"fallback_to_random"`
}

// IDGeneratorConfig ID生成器配置
type IDGeneratorConfig struct {
	DefaultDomain          string `yaml:"default_domain" mapstructure:"default_domain"`
	DefaultPrefix          string `yaml:"default_prefix" mapstructure:"default_prefix"`
	DefaultLength          int    `yaml:"default_length" mapstructure:"default_length"`
	PadWithZeros           bool   `yaml:"pad_with_zeros" mapstructure:"pad_with_zeros"`
	EnableCache            bool   `yaml:"enable_cache" mapstructure:"enable_cache"`
	ValidationEnabled      bool   `yaml:"validation_enabled" mapstructure:"validation_enabled"`
	MetricsEnabled         bool   `yaml:"metrics_enabled" mapstructure:"metrics_enabled"`
	CacheSize              int    `yaml:"cache_size" mapstructure:"cache_size"`
	CacheTTL               int    `yaml:"cache_ttl" mapstructure:"cache_ttl"`
	EnableDistributedCache bool   `yaml:"enable_distributed_cache" mapstructure:"enable_distributed_cache"`
}

// UploadConfig 文件上传配置
type UploadConfig struct {
	MaxSize      int64    `yaml:"max_size" mapstructure:"max_size"`
	AllowedTypes []string `yaml:"allowed_types" mapstructure:"allowed_types"`
	Path         string   `yaml:"path" mapstructure:"path"`
}

// CacheConfig 缓存配置
type CacheConfig struct {
	DefaultTTL time.Duration `yaml:"default_ttl" mapstructure:"default_ttl"`
	MaxKeys    int           `yaml:"max_keys" mapstructure:"max_keys"`

	// 缓存策略配置
	Strategies map[string]CacheStrategy `yaml:"strategies" mapstructure:"strategies"`

	// Redis缓存配置
	Redis CacheRedisConfig `yaml:"redis" mapstructure:"redis"`

	// 监控配置
	Metrics CacheMetricsConfig `yaml:"metrics" mapstructure:"metrics"`
}

// CacheStrategy 缓存策略配置
type CacheStrategy struct {
	TTL      time.Duration `yaml:"ttl" mapstructure:"ttl"`
	MaxSize  int           `yaml:"max_size" mapstructure:"max_size"`
	Enabled  bool          `yaml:"enabled" mapstructure:"enabled"`
	Compress bool          `yaml:"compress" mapstructure:"compress"`
}

// CacheRedisConfig Redis缓存专用配置
type CacheRedisConfig struct {
	Serialization string `yaml:"serialization" mapstructure:"serialization" validate:"oneof=json msgpack protobuf"`
	Compression   bool   `yaml:"compression" mapstructure:"compression"`
	KeyPrefix     string `yaml:"key_prefix" mapstructure:"key_prefix"`
	KeySeparator  string `yaml:"key_separator" mapstructure:"key_separator"`

	// 批量操作配置
	BatchSize    int           `yaml:"batch_size" mapstructure:"batch_size"`
	BatchTimeout time.Duration `yaml:"batch_timeout" mapstructure:"batch_timeout"`

	// 故障转移配置
	FailoverEnabled       bool          `yaml:"failover_enabled" mapstructure:"failover_enabled"`
	FailoverTimeout       time.Duration `yaml:"failover_timeout" mapstructure:"failover_timeout"`
	CircuitBreakerEnabled bool          `yaml:"circuit_breaker_enabled" mapstructure:"circuit_breaker_enabled"`
}

// CacheMetricsConfig 缓存监控配置
type CacheMetricsConfig struct {
	Enabled            bool          `yaml:"enabled" mapstructure:"enabled"`
	CollectInterval    time.Duration `yaml:"collect_interval" mapstructure:"collect_interval"`
	HitRateWindow      time.Duration `yaml:"hit_rate_window" mapstructure:"hit_rate_window"`
	SlowQueryThreshold time.Duration `yaml:"slow_query_threshold" mapstructure:"slow_query_threshold"`
}

// GetDSN 生成数据库连接字符串
func (d *DatabaseConfig) GetDSN() string {
	switch d.Driver {
	case "postgres":
		return fmt.Sprintf("host=%s port=%d user=%s password=%s dbname=%s sslmode=%s",
			d.Host, d.Port, d.Username, d.Password, d.Database, d.SSLMode)
	case "mysql":
		return fmt.Sprintf("%s:%s@tcp(%s:%d)/%s?charset=utf8mb4&parseTime=True&loc=Local",
			d.Username, d.Password, d.Host, d.Port, d.Database)
	default:
		return ""
	}
}

// GetAddr 生成Redis连接地址
func (r *RedisConfig) GetAddr() string {
	return fmt.Sprintf("%s:%d", r.Host, r.Port)
}
