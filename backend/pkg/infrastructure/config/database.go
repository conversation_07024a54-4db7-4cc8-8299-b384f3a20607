package config

import (
	"time"
)

// DatabaseConfig 数据库配置
type DatabaseConfig struct {
	Driver   string `yaml:"driver" mapstructure:"driver" validate:"required,oneof=postgres mysql"`
	Host     string `yaml:"host" mapstructure:"host" validate:"required"`
	Port     int    `yaml:"port" mapstructure:"port" validate:"required,min=1,max=65535"`
	Database string `yaml:"database" mapstructure:"database" validate:"required"`
	Username string `yaml:"username" mapstructure:"username" validate:"required"`
	Password string `yaml:"password" mapstructure:"password"`
	SSLMode  string `yaml:"ssl_mode" mapstructure:"ssl_mode"`

	// 连接池配置
	MaxOpenConns    int           `yaml:"max_open_conns" mapstructure:"max_open_conns"`
	MaxIdleConns    int           `yaml:"max_idle_conns" mapstructure:"max_idle_conns"`
	ConnMaxLifetime time.Duration `yaml:"conn_max_lifetime" mapstructure:"conn_max_lifetime"`
	ConnMaxIdleTime time.Duration `yaml:"conn_max_idle_time" mapstructure:"conn_max_idle_time"`

	// 迁移配置
	Migration MigrationConfig `yaml:"migration" mapstructure:"migration"`
}

// MigrationConfig 数据库迁移配置
type MigrationConfig struct {
	AutoMigrate   bool   `yaml:"auto_migrate" mapstructure:"auto_migrate"`     // 是否自动执行迁移
	MigrationsDir string `yaml:"migrations_dir" mapstructure:"migrations_dir"` // 迁移文件目录
	FailOnError   bool   `yaml:"fail_on_error" mapstructure:"fail_on_error"`   // 迁移失败时是否停止应用启动
	LogMigrations bool   `yaml:"log_migrations" mapstructure:"log_migrations"` // 是否记录迁移日志
}

// GetDriver 获取数据库驱动名称
func (d *DatabaseConfig) GetDriver() string {
	return d.Driver
}

// GetMaxOpenConns 获取最大打开连接数
func (d *DatabaseConfig) GetMaxOpenConns() int {
	return d.MaxOpenConns
}

// GetMaxIdleConns 获取最大空闲连接数
func (d *DatabaseConfig) GetMaxIdleConns() int {
	return d.MaxIdleConns
}

// GetConnMaxLifetime 获取连接最大生存时间
func (d *DatabaseConfig) GetConnMaxLifetime() time.Duration {
	return d.ConnMaxLifetime
}

// GetConnMaxIdleTime 获取连接最大空闲时间
func (d *DatabaseConfig) GetConnMaxIdleTime() time.Duration {
	return d.ConnMaxIdleTime
}
