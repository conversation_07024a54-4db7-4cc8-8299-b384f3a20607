package config

import (
	"fmt"
	"os"
	"path/filepath"
	"strings"

	apperrors "backend/pkg/common/errors"
	"backend/pkg/common/errors/codes"

	"github.com/joho/godotenv"
	"github.com/spf13/viper"
)

// Loader 配置加载器
type Loader struct {
	configPath string
	env        string
}

// NewLoader 创建配置加载器
func NewLoader(configPath string) *Loader {
	env := getEnv()
	return &Loader{
		configPath: configPath,
		env:        env,
	}
}

// Load 加载配置
func (l *Loader) Load() (*Config, error) {
	// 首先加载环境变量文件
	if err := l.loadEnvFiles(); err != nil {
		return nil, apperrors.NewInternal(codes.ConfigError, "加载环境变量文件失败").Wrap(err).Build()
	}

	// 创建viper实例
	v := viper.New()
	v.SetConfigType("yaml")

	// 加载配置文件
	configFile := filepath.Join(l.configPath, "config.yaml")
	if err := l.loadConfigFile(v, configFile); err != nil {
		return nil, apperrors.NewInternal(codes.ConfigError, "加载配置文件失败").Wrap(err).Build()
	}

	// 绑定环境变量
	l.bindEnvVars(v)

	// 反序列化配置
	var config Config
	if err := v.Unmarshal(&config); err != nil {
		return nil, apperrors.NewInternal(codes.ConfigError, "配置反序列化失败").Wrap(err).Build()
	}

	// 设置环境
	config.Env = l.env

	// 验证配置
	validator := NewValidator()
	if err := validator.ValidateConfig(&config); err != nil {
		return nil, apperrors.NewInternal(codes.ConfigError, "配置验证失败").Wrap(err).Build()
	}

	return &config, nil
}

// loadConfigFile 加载配置文件
func (l *Loader) loadConfigFile(v *viper.Viper, configFile string) error {
	// 检查配置文件是否存在
	if _, err := os.Stat(configFile); os.IsNotExist(err) {
		return apperrors.NewInternal(codes.FileSystem, "配置文件不存在").Wrap(err).Build()
	}

	// 设置配置文件路径
	v.SetConfigFile(configFile)

	// 读取配置文件
	return v.ReadInConfig()
}

// loadEnvFiles 使用godotenv加载环境变量文件（按正确优先级）
func (l *Loader) loadEnvFiles() error {
	// 获取项目根目录
	projectRoot := l.getProjectRoot()

	// 按优先级顺序加载.env文件（从低到高优先级）
	// 注意：后加载的文件优先级更高，不会覆盖已设置的环境变量
	envFiles := []string{
		filepath.Join(projectRoot, ".env"),                        // 基础配置（最低优先级）
		filepath.Join(projectRoot, fmt.Sprintf(".env.%s", l.env)), // 环境特定配置
		filepath.Join(projectRoot, ".env.local"),                  // 本地覆盖（最高优先级，不提交到git）
	}

	for _, file := range envFiles {
		if err := l.loadEnvFile(file); err != nil {
			// 继续加载其他文件，不因为某个文件缺失而失败
			continue
		}
	}

	return nil
}

// loadEnvFile 使用godotenv读取并安全设置环境变量
func (l *Loader) loadEnvFile(filePath string) error {
	if _, err := os.Stat(filePath); os.IsNotExist(err) {
		// 文件不存在，不是错误
		return nil
	}

	// 使用godotenv.Read读取文件内容，而不是直接Load
	envMap, err := godotenv.Read(filePath)
	if err != nil {
		return apperrors.NewInternal(codes.FileSystem, "读取环境变量文件失败").Wrap(err).Build()
	}

	// 手动设置环境变量，只在未设置时才设置（保持优先级）
	for key, value := range envMap {
		if os.Getenv(key) == "" {
			os.Setenv(key, value)
		}
	}

	return nil
}

// bindEnvVars 绑定环境变量到viper
func (l *Loader) bindEnvVars(v *viper.Viper) {
	// 设置环境变量前缀
	v.SetEnvPrefix("ERP")

	// 自动绑定环境变量
	v.AutomaticEnv()

	// 设置环境变量键名转换
	v.SetEnvKeyReplacer(strings.NewReplacer(".", "_"))

	// 手动绑定关键配置项，确保正确映射
	envBindings := l.getEnvBindings()
	for key, envVar := range envBindings {
		v.BindEnv(key, envVar)
	}
}

// getEnvBindings 获取环境变量绑定映射
func (l *Loader) getEnvBindings() map[string]string {
	return map[string]string{
		// 环境配置
		"env": "APP_ENV",

		// 服务器配置
		"server.host":          "ERP_SERVER_HOST",
		"server.port":          "ERP_SERVER_PORT",
		"server.read_timeout":  "ERP_SERVER_READ_TIMEOUT",
		"server.write_timeout": "ERP_SERVER_WRITE_TIMEOUT",
		"server.idle_timeout":  "ERP_SERVER_IDLE_TIMEOUT",
		"server.grpc.enable":   "ERP_GRPC_ENABLE",
		"server.grpc.port":     "ERP_GRPC_PORT",

		// 数据库配置
		"database.driver":             "ERP_DB_DRIVER",
		"database.host":               "ERP_DB_HOST",
		"database.port":               "ERP_DB_PORT",
		"database.database":           "ERP_DB_NAME",
		"database.username":           "ERP_DB_USER",
		"database.password":           "ERP_DB_PASSWORD",
		"database.ssl_mode":           "ERP_DB_SSL_MODE",
		"database.max_open_conns":     "ERP_DB_MAX_OPEN_CONNS",
		"database.max_idle_conns":     "ERP_DB_MAX_IDLE_CONNS",
		"database.conn_max_lifetime":  "ERP_DB_CONN_MAX_LIFETIME",
		"database.conn_max_idle_time": "ERP_DB_CONN_MAX_IDLE_TIME",

		// 数据库迁移配置
		"database.migration.auto_migrate":   "ERP_DB_MIGRATION_AUTO_MIGRATE",
		"database.migration.migrations_dir": "ERP_DB_MIGRATION_MIGRATIONS_DIR",
		"database.migration.fail_on_error":  "ERP_DB_MIGRATION_FAIL_ON_ERROR",
		"database.migration.log_migrations": "ERP_DB_MIGRATION_LOG_MIGRATIONS",

		// Redis配置
		"redis.host":      "ERP_REDIS_HOST",
		"redis.port":      "ERP_REDIS_PORT",
		"redis.password":  "ERP_REDIS_PASSWORD",
		"redis.db":        "ERP_REDIS_DB",
		"redis.pool_size": "ERP_REDIS_POOL_SIZE",

		// 消息队列配置
		"message_queue.provider":                          "ERP_MQ_PROVIDER",
		"message_queue.redis.host":                        "ERP_MQ_REDIS_HOST",
		"message_queue.redis.port":                        "ERP_MQ_REDIS_PORT",
		"message_queue.redis.password":                    "ERP_MQ_REDIS_PASSWORD",
		"message_queue.redis.db":                          "ERP_MQ_REDIS_DB",
		"message_queue.redis.pool_size":                   "ERP_MQ_REDIS_POOL_SIZE",
		"message_queue.redis.consumer_group":              "ERP_MQ_CONSUMER_GROUP",
		"message_queue.redis.delayed_queue.enable":        "ERP_MQ_DELAYED_QUEUE_ENABLE",
		"message_queue.redis.delayed_queue.scan_interval": "ERP_MQ_DELAYED_QUEUE_SCAN_INTERVAL",
		"message_queue.redis.retention.default_ttl":       "ERP_MQ_RETENTION_TTL",
		"message_queue.redis.retention.max_len":           "ERP_MQ_RETENTION_MAX_LEN",
		"message_queue.redis.retention.approximate":       "ERP_MQ_RETENTION_APPROXIMATE",
		"message_queue.kafka.enable":                      "ERP_MQ_KAFKA_ENABLE",
		"message_queue.kafka.brokers":                     "ERP_MQ_KAFKA_BROKERS",
		"message_queue.kafka.consumer_group":              "ERP_MQ_KAFKA_CONSUMER_GROUP",
		"message_queue.kafka.batch_size":                  "ERP_MQ_KAFKA_BATCH_SIZE",
		"message_queue.kafka.timeout":                     "ERP_MQ_KAFKA_TIMEOUT",
		"message_queue.processing.max_retries":            "ERP_MQ_MAX_RETRIES",
		"message_queue.processing.retry_delay":            "ERP_MQ_RETRY_DELAY",
		"message_queue.processing.dead_letter_topic":      "ERP_MQ_DEAD_LETTER_TOPIC",

		// 日志配置
		"logger.level":       "ERP_LOG_LEVEL",
		"logger.format":      "ERP_LOG_FORMAT",
		"logger.output":      "ERP_LOG_OUTPUT",
		"logger.file":        "ERP_LOG_FILE",
		"logger.max_size":    "ERP_LOG_MAX_SIZE",
		"logger.max_age":     "ERP_LOG_MAX_AGE",
		"logger.max_backups": "ERP_LOG_MAX_BACKUPS",
		"logger.compress":    "ERP_LOG_COMPRESS",

		// 安全配置
		"security.jwt.secret":           "ERP_JWT_SECRET",
		"security.jwt.issuer":           "ERP_JWT_ISSUER",
		"security.jwt.audience":         "ERP_JWT_AUDIENCE",
		"security.jwt.expiration":       "ERP_JWT_EXPIRATION",
		"security.cors.allowed_origins": "ERP_CORS_ALLOWED_ORIGINS",
		"security.cors.allowed_methods": "ERP_CORS_ALLOWED_METHODS",
		"security.cors.allowed_headers": "ERP_CORS_ALLOWED_HEADERS",
		"security.rate_limit.enable":    "ERP_RATE_LIMIT_ENABLE",
		"security.rate_limit.rps":       "ERP_RATE_LIMIT_RPS",
		"security.rate_limit.burst":     "ERP_RATE_LIMIT_BURST",

		// 监控配置
		"monitoring.metrics.enable":   "ERP_METRICS_ENABLE",
		"monitoring.metrics.path":     "ERP_METRICS_PATH",
		"monitoring.metrics.port":     "ERP_METRICS_PORT",
		"monitoring.tracing.enable":   "ERP_TRACING_ENABLE",
		"monitoring.tracing.endpoint": "ERP_TRACING_ENDPOINT",
		"monitoring.tracing.service":  "ERP_TRACING_SERVICE",
		"monitoring.health.enable":    "ERP_HEALTH_ENABLE",
		"monitoring.health.path":      "ERP_HEALTH_PATH",

		// 业务配置
		"business.snowflake.machine_id": "ERP_SNOWFLAKE_MACHINE_ID",
		"business.upload.max_size":      "ERP_UPLOAD_MAX_SIZE",
		"business.upload.allowed_types": "ERP_UPLOAD_ALLOWED_TYPES",
		"business.upload.path":          "ERP_UPLOAD_PATH",
		"business.cache.default_ttl":    "ERP_CACHE_DEFAULT_TTL",
		"business.cache.max_keys":       "ERP_CACHE_MAX_KEYS",
	}
}

// getProjectRoot 获取项目根目录
func (l *Loader) getProjectRoot() string {
	// 从配置路径推断项目根目录
	if filepath.IsAbs(l.configPath) {
		return filepath.Dir(l.configPath)
	}

	// 相对路径，从当前工作目录计算
	wd, err := os.Getwd()
	if err != nil {
		return "."
	}

	return filepath.Dir(filepath.Join(wd, l.configPath))
}

// getEnv 获取当前环境
func getEnv() string {
	env := os.Getenv("APP_ENV")
	if env == "" {
		env = os.Getenv("ERP_ENV")
	}
	if env == "" {
		env = "development"
	}
	return env
}

// LoadFromPath 从指定路径加载配置
func LoadFromPath(configPath string) (*Config, error) {
	loader := NewLoader(configPath)
	return loader.Load()
}

// LoadDefault 加载默认配置
func LoadDefault() (*Config, error) {
	// 默认配置路径
	configPath := os.Getenv("ERP_CONFIG_PATH")
	if configPath == "" {
		configPath = "configs"
	}

	return LoadFromPath(configPath)
}

// MustLoad 必须成功加载配置，否则panic
func MustLoad() *Config {
	config, err := LoadDefault()
	if err != nil {
		panic(fmt.Sprintf("加载配置失败: %v", err))
	}
	return config
}

// MustLoadFromPath 必须从指定路径加载配置，否则panic
func MustLoadFromPath(configPath string) *Config {
	config, err := LoadFromPath(configPath)
	if err != nil {
		panic(fmt.Sprintf("加载配置失败: %v", err))
	}
	return config
}
