package config

import (
	"fmt"
	"time"
)

// MQConfig 消息队列配置
type MQConfig struct {
	Provider   string           `yaml:"provider" mapstructure:"provider"`
	Redis      RedisMQConfig    `yaml:"redis" mapstructure:"redis"`
	Kafka      KafkaMQConfig    `yaml:"kafka" mapstructure:"kafka"`
	Processing ProcessingConfig `yaml:"processing" mapstructure:"processing"`
}

// RedisMQConfig Redis消息队列配置
type RedisMQConfig struct {
	Host          string             `yaml:"host" mapstructure:"host"`
	Port          int                `yaml:"port" mapstructure:"port"`
	Password      string             `yaml:"password" mapstructure:"password"`
	DB            int                `yaml:"db" mapstructure:"db"`
	PoolSize      int                `yaml:"pool_size" mapstructure:"pool_size"`
	ConsumerGroup string             `yaml:"consumer_group" mapstructure:"consumer_group"`
	DelayedQueue  DelayedQueueConfig `yaml:"delayed_queue" mapstructure:"delayed_queue"`
	Retention     RetentionConfig    `yaml:"retention" mapstructure:"retention"`
}

// GetAddr 生成Redis连接地址（兼容方法）
func (r *RedisMQConfig) GetAddr() string {
	return fmt.Sprintf("%s:%d", r.Host, r.Port)
}

// DelayedQueueConfig 延时队列配置
type DelayedQueueConfig struct {
	Enable       bool          `yaml:"enable" mapstructure:"enable"`
	ScanInterval time.Duration `yaml:"scan_interval" mapstructure:"scan_interval"`
}

// RetentionConfig 消息保留配置
type RetentionConfig struct {
	DefaultTTL  time.Duration `yaml:"default_ttl" mapstructure:"default_ttl"`
	MaxLen      int64         `yaml:"max_len" mapstructure:"max_len"`
	Approximate bool          `yaml:"approximate" mapstructure:"approximate"`
}

// KafkaMQConfig Kafka消息队列配置（未来使用）
type KafkaMQConfig struct {
	Enable        bool          `yaml:"enable" mapstructure:"enable"`
	Brokers       []string      `yaml:"brokers" mapstructure:"brokers"`
	ConsumerGroup string        `yaml:"consumer_group" mapstructure:"consumer_group"`
	BatchSize     int           `yaml:"batch_size" mapstructure:"batch_size"`
	Timeout       time.Duration `yaml:"timeout" mapstructure:"timeout"`
}

// ProcessingConfig 消息处理配置
type ProcessingConfig struct {
	MaxRetries      int           `yaml:"max_retries" mapstructure:"max_retries"`
	RetryDelay      time.Duration `yaml:"retry_delay" mapstructure:"retry_delay"`
	DeadLetterTopic string        `yaml:"dead_letter_topic" mapstructure:"dead_letter_topic"`
}

// DefaultMQConfig 返回默认的消息队列配置
func DefaultMQConfig() MQConfig {
	return MQConfig{
		Provider: "redis",
		Redis: RedisMQConfig{
			Host:          "localhost",
			Port:          6379,
			Password:      "",
			DB:            1,
			PoolSize:      10,
			ConsumerGroup: "erp-consumer-group",
			DelayedQueue: DelayedQueueConfig{
				Enable:       true,
				ScanInterval: time.Second,
			},
			Retention: RetentionConfig{
				DefaultTTL:  24 * time.Hour,
				MaxLen:      10000,
				Approximate: true,
			},
		},
		Kafka: KafkaMQConfig{
			Enable:        false,
			Brokers:       []string{"localhost:9092"},
			ConsumerGroup: "erp-consumer-group",
			BatchSize:     100,
			Timeout:       30 * time.Second,
		},
		Processing: ProcessingConfig{
			MaxRetries:      3,
			RetryDelay:      5 * time.Second,
			DeadLetterTopic: "dlq",
		},
	}
}
