package config

import (
	"fmt"
	"strings"

	"github.com/go-playground/validator/v10"
)

// Validator 配置验证器
type Validator struct {
	validator *validator.Validate
}

// NewValidator 创建配置验证器
func NewValidator() *Validator {
	return &Validator{
		validator: validator.New(),
	}
}

// ValidateConfig 验证配置
func (v *Validator) ValidateConfig(config *Config) error {
	// 使用结构体标签验证
	if err := v.validator.Struct(config); err != nil {
		return fmt.Errorf("配置验证失败: %w", err)
	}

	// 业务逻辑验证
	if err := v.validateBusinessRules(config); err != nil {
		return fmt.Errorf("业务规则验证失败: %w", err)
	}

	return nil
}

// validateBusinessRules 验证业务规则
func (v *Validator) validateBusinessRules(config *Config) error {
	// 验证JWT密钥长度
	if len(config.Auth.JWT.SecretKey) < 32 {
		return fmt.Errorf("JWT密钥长度不能少于32位")
	}

	// 验证雪花ID机器ID范围
	if config.Business.Snowflake.MachineID < 0 || config.Business.Snowflake.MachineID > 1023 {
		return fmt.Errorf("雪花ID机器ID必须在0-1023范围内")
	}

	// 验证环境特定的规则
	if err := v.validateEnvironmentSpecificRules(config); err != nil {
		return err
	}

	return nil
}

// validateEnvironmentSpecificRules 验证环境特定的规则
func (v *Validator) validateEnvironmentSpecificRules(config *Config) error {
	switch config.Env {
	case "production":
		// 生产环境特定验证
		if config.Auth.JWT.SecretKey == "development_jwt_secret_key_change_in_production_very_long_secret" {
			return fmt.Errorf("生产环境不能使用默认的JWT密钥")
		}

		if config.Database.SSLMode == "disable" {
			return fmt.Errorf("生产环境数据库必须启用SSL")
		}

	case "development":
		// 开发环境可以使用默认配置
		break

	case "testing":
		// 测试环境特定验证
		if !strings.Contains(config.Database.Database, "test") {
			return fmt.Errorf("测试环境数据库名称应包含'test'")
		}
	}

	return nil
}
