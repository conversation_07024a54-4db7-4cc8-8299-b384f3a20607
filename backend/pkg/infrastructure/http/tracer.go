package http

import (
	"context"
	"fmt"
	"net/http"
	"time"

	"backend/pkg/infrastructure/logger"
	"backend/pkg/infrastructure/monitoring"

	"go.opentelemetry.io/otel"
	"go.opentelemetry.io/otel/attribute"
	"go.opentelemetry.io/otel/propagation"
	"go.opentelemetry.io/otel/trace"
)

// HTTPTracer HTTP调用追踪器
type HTTPTracer struct {
	logger         logger.Logger
	contextManager *logger.ContextManager
	tracingManager *monitoring.TracingManager
}

// NewHTTPTracer 创建HTTP追踪器
func NewHTTPTracer(
	log logger.Logger,
	tracingManager *monitoring.TracingManager,
) *HTTPTracer {
	return &HTTPTracer{
		logger:         log,
		contextManager: logger.NewContextManager(log),
		tracingManager: tracingManager,
	}
}

// TraceRequest 追踪HTTP请求
func (ht *HTTPTracer) TraceRequest(ctx context.Context, service, method, url string, fn func(*http.Request) (*http.Response, error)) (*http.Response, error) {
	start := time.Now()

	// 开始外部调用span
	spanCtx, span := ht.tracingManager.StartExternalSpan(ctx, service, method, url)
	defer span.End()

	// 创建HTTP请求
	req, err := http.NewRequestWithContext(spanCtx, method, url, nil)
	if err != nil {
		ht.tracingManager.RecordError(span, err)
		return nil, err
	}

	// 注入追踪上下文到HTTP头
	propagator := otel.GetTextMapPropagator()
	propagator.Inject(spanCtx, propagation.HeaderCarrier(req.Header))

	// 执行请求
	resp, err := fn(req)
	duration := time.Since(start)

	// 记录请求和响应信息
	var statusCode int
	var responseSize int64
	var requestSize int64

	if req.Body != nil {
		// 这里需要实际计算请求大小
		requestSize = 0 // 简化处理
	}

	if resp != nil {
		statusCode = resp.StatusCode
		if resp.ContentLength > 0 {
			responseSize = resp.ContentLength
		}
	}

	// 更新span属性
	span.SetAttributes(
		attribute.Int("http.status_code", statusCode),
		attribute.Int64("http.request_size", requestSize),
		attribute.Int64("http.response_size", responseSize),
		attribute.String("http.duration", duration.String()),
	)

	// 记录日志
	extCall := logger.ExternalCallLog{
		Service:      service,
		URL:          url,
		Method:       method,
		StatusCode:   statusCode,
		Duration:     duration,
		RequestSize:  requestSize,
		ResponseSize: responseSize,
		StartTime:    start,
		EndTime:      time.Now(),
	}

	if err != nil {
		extCall.Error = err.Error()
		ht.tracingManager.RecordError(span, err)
	} else if statusCode >= 400 {
		ht.tracingManager.RecordError(span, fmt.Errorf("HTTP %d", statusCode))
	} else {
		ht.tracingManager.SetSpanSuccess(span)
	}

	ht.contextManager.LogExternalCall(spanCtx, extCall)

	return resp, err
}

// TraceWebhook 追踪Webhook调用
func (ht *HTTPTracer) TraceWebhook(ctx context.Context, webhookType, url string, payload interface{}, fn func() (*http.Response, error)) (*http.Response, error) {
	start := time.Now()

	// 开始webhook span
	spanCtx, span := ht.tracingManager.StartSpan(ctx, fmt.Sprintf("webhook.%s", webhookType),
		trace.WithAttributes(
			attribute.String("operation.type", "webhook"),
			attribute.String("webhook.type", webhookType),
			attribute.String("webhook.url", url),
		),
	)
	defer span.End()

	// 执行webhook调用
	resp, err := fn()
	duration := time.Since(start)

	// 记录结果
	var statusCode int
	if resp != nil {
		statusCode = resp.StatusCode
	}

	span.SetAttributes(
		attribute.Int("http.status_code", statusCode),
		attribute.String("webhook.duration", duration.String()),
	)

	// 记录日志
	if err != nil {
		ht.tracingManager.RecordError(span, err)
		ht.contextManager.LogWithStandardContext(spanCtx, logger.LevelError,
			fmt.Sprintf("Webhook call failed: %s", webhookType),
			"webhook_type", webhookType,
			"url", url,
			"error", err.Error(),
			"duration", duration,
		)
	} else {
		ht.tracingManager.SetSpanSuccess(span)
		ht.contextManager.LogWithStandardContext(spanCtx, logger.LevelInfo,
			fmt.Sprintf("Webhook call completed: %s", webhookType),
			"webhook_type", webhookType,
			"url", url,
			"status_code", statusCode,
			"duration", duration,
		)
	}

	return resp, err
}

// TraceAPICall 追踪第三方API调用
func (ht *HTTPTracer) TraceAPICall(ctx context.Context, apiName, endpoint string, fn func() (interface{}, error)) (interface{}, error) {
	start := time.Now()

	// 开始API调用span
	spanCtx, span := ht.tracingManager.StartSpan(ctx, fmt.Sprintf("api.%s", apiName),
		trace.WithAttributes(
			attribute.String("operation.type", "api_call"),
			attribute.String("api.name", apiName),
			attribute.String("api.endpoint", endpoint),
		),
	)
	defer span.End()

	// 执行API调用
	result, err := fn()
	duration := time.Since(start)

	// 记录结果
	span.SetAttributes(
		attribute.String("api.duration", duration.String()),
	)

	if err != nil {
		ht.tracingManager.RecordError(span, err)
		ht.contextManager.LogWithStandardContext(spanCtx, logger.LevelError,
			fmt.Sprintf("API call failed: %s", apiName),
			"api_name", apiName,
			"endpoint", endpoint,
			"error", err.Error(),
			"duration", duration,
		)
	} else {
		ht.tracingManager.SetSpanSuccess(span)
		ht.contextManager.LogWithStandardContext(spanCtx, logger.LevelInfo,
			fmt.Sprintf("API call completed: %s", apiName),
			"api_name", apiName,
			"endpoint", endpoint,
			"duration", duration,
		)
	}

	return result, err
}

// TraceRetryableCall 追踪可重试的调用
func (ht *HTTPTracer) TraceRetryableCall(ctx context.Context, operation string, maxRetries int, fn func(attempt int) error) error {
	start := time.Now()

	// 开始重试操作span
	spanCtx, span := ht.tracingManager.StartSpan(ctx, fmt.Sprintf("retry.%s", operation),
		trace.WithAttributes(
			attribute.String("operation.type", "retry"),
			attribute.String("retry.operation", operation),
			attribute.Int("retry.max_attempts", maxRetries),
		),
	)
	defer span.End()

	var lastErr error
	for attempt := 1; attempt <= maxRetries; attempt++ {
		attemptStart := time.Now()

		// 记录尝试开始
		ht.contextManager.LogWithStandardContext(spanCtx, logger.LevelDebug,
			fmt.Sprintf("Retry attempt %d/%d for %s", attempt, maxRetries, operation),
			"operation", operation,
			"attempt", attempt,
			"max_attempts", maxRetries,
		)

		err := fn(attempt)
		attemptDuration := time.Since(attemptStart)

		if err == nil {
			// 成功
			totalDuration := time.Since(start)
			span.SetAttributes(
				attribute.Int("retry.successful_attempt", attempt),
				attribute.String("retry.total_duration", totalDuration.String()),
			)

			ht.tracingManager.SetSpanSuccess(span)
			ht.contextManager.LogWithStandardContext(spanCtx, logger.LevelInfo,
				fmt.Sprintf("Retry operation succeeded: %s", operation),
				"operation", operation,
				"successful_attempt", attempt,
				"total_duration", totalDuration,
				"attempt_duration", attemptDuration,
			)

			return nil
		}

		lastErr = err

		// 记录失败的尝试
		ht.contextManager.LogWithStandardContext(spanCtx, logger.LevelWarn,
			fmt.Sprintf("Retry attempt %d/%d failed for %s", attempt, maxRetries, operation),
			"operation", operation,
			"attempt", attempt,
			"max_attempts", maxRetries,
			"error", err.Error(),
			"attempt_duration", attemptDuration,
		)

		// 如果不是最后一次尝试，等待一段时间
		if attempt < maxRetries {
			backoff := time.Duration(attempt) * time.Second
			time.Sleep(backoff)
		}
	}

	// 所有尝试都失败了
	totalDuration := time.Since(start)
	span.SetAttributes(
		attribute.Int("retry.failed_attempts", maxRetries),
		attribute.String("retry.total_duration", totalDuration.String()),
	)

	ht.tracingManager.RecordError(span, lastErr)
	ht.contextManager.LogWithStandardContext(spanCtx, logger.LevelError,
		fmt.Sprintf("Retry operation failed after %d attempts: %s", maxRetries, operation),
		"operation", operation,
		"max_attempts", maxRetries,
		"total_duration", totalDuration,
		"final_error", lastErr.Error(),
	)

	return lastErr
}

// TraceCircuitBreaker 追踪熔断器状态
func (ht *HTTPTracer) TraceCircuitBreaker(ctx context.Context, name, state string, metrics map[string]interface{}) {
	ht.contextManager.LogWithStandardContext(ctx, logger.LevelInfo,
		fmt.Sprintf("Circuit breaker state: %s", name),
		"circuit_breaker", name,
		"state", state,
		"metrics", metrics,
	)

	// 如果熔断器打开，记录警告
	if state == "open" {
		ht.contextManager.LogWithStandardContext(ctx, logger.LevelWarn,
			fmt.Sprintf("Circuit breaker opened: %s", name),
			"circuit_breaker", name,
			"state", state,
			"metrics", metrics,
		)
	}
}
