package mq

import (
	"context"
	"time"
)

// Message 消息结构
type Message struct {
	ID        string            `json:"id"`
	Topic     string            `json:"topic"`
	Payload   []byte            `json:"payload"`
	Headers   map[string]string `json:"headers"`
	Timestamp time.Time         `json:"timestamp"`
	Attempts  int               `json:"attempts"`
}

// Producer 消息生产者接口
type Producer interface {
	// 发送消息
	Send(ctx context.Context, topic string, payload []byte, headers ...map[string]string) error

	// 批量发送消息
	SendBatch(ctx context.Context, messages []*Message) error

	// 延时发送消息
	SendDelayed(ctx context.Context, topic string, payload []byte, delay time.Duration, headers ...map[string]string) error

	// 关闭生产者
	Close() error
}

// Consumer 消息消费者接口
type Consumer interface {
	// 订阅主题
	Subscribe(ctx context.Context, topic string, handler MessageHandler) error

	// 批量订阅主题
	SubscribeMultiple(ctx context.Context, topics []string, handler MessageHandler) error

	// 取消订阅
	Unsubscribe(topic string) error

	// 关闭消费者
	Close() error
}

// MessageHandler 消息处理器
type MessageHandler interface {
	Handle(ctx context.Context, message *Message) error
}

// MessageHandlerFunc 消息处理函数
type MessageHandlerFunc func(ctx context.Context, message *Message) error

func (f MessageHandlerFunc) Handle(ctx context.Context, message *Message) error {
	return f(ctx, message)
}

// Publisher 发布订阅接口
type Publisher interface {
	// 发布消息
	Publish(ctx context.Context, channel string, payload []byte) error

	// 发布到多个频道
	PublishMultiple(ctx context.Context, channels []string, payload []byte) error
}

// Subscriber 订阅者接口
type Subscriber interface {
	// 订阅频道
	Subscribe(ctx context.Context, channels []string, handler MessageHandler) error

	// 取消订阅
	Unsubscribe(channels ...string) error

	// 关闭订阅者
	Close() error
}

// DelayedQueue 延时队列接口
type DelayedQueue interface {
	// 添加延时任务
	AddDelayedTask(ctx context.Context, topic string, payload []byte, delay time.Duration) error

	// 启动延时任务处理器
	StartProcessor(ctx context.Context)

	// 停止处理器
	StopProcessor()
}

// MQManager 消息队列管理器
type MQManager interface {
	Producer() Producer
	Consumer() Consumer
	Publisher() Publisher
	Subscriber() Subscriber
	DelayedQueue() DelayedQueue

	// 健康检查
	Health() error

	// 关闭所有连接
	Close() error
}
