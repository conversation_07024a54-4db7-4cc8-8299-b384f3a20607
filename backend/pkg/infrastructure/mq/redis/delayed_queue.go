package redis

import (
	"context"
	"encoding/json"
	"fmt"
	"sync"
	"time"

	"github.com/redis/go-redis/v9"
)

// DelayedQueueManager 延时队列管理器
type DelayedQueueManager struct {
	client    *redis.Client
	producer  *RedisProducer
	stopChan  chan struct{}
	isRunning bool
	mu        sync.RWMutex
}

func NewDelayedQueueManager(client *redis.Client) *DelayedQueueManager {
	return &DelayedQueueManager{
		client:   client,
		producer: NewRedisProducer(client),
		stopChan: make(chan struct{}),
	}
}

// AddDelayedTask 添加延时任务
func (d *DelayedQueueManager) AddDelayedTask(ctx context.Context, topic string, payload []byte, delay time.Duration) error {
	executeTime := time.Now().Add(delay)
	score := float64(executeTime.Unix())

	task := DelayedTask{
		Topic:     topic,
		Payload:   payload,
		ExecuteAt: executeTime,
	}

	data, err := json.<PERSON>(task)
	if err != nil {
		return err
	}

	return d.client.ZAdd(ctx, fmt.Sprintf("delayed:%s", topic), redis.Z{
		Score:  score,
		Member: data,
	}).Err()
}

// StartProcessor 启动延时任务处理器
func (d *DelayedQueueManager) StartProcessor(ctx context.Context) {
	d.mu.Lock()
	defer d.mu.Unlock()

	if d.isRunning {
		return
	}

	d.isRunning = true
	go d.processLoop(ctx)
}

// StopProcessor 停止处理器
func (d *DelayedQueueManager) StopProcessor() {
	d.mu.Lock()
	defer d.mu.Unlock()

	if !d.isRunning {
		return
	}

	close(d.stopChan)
	d.isRunning = false

	// 重新创建停止通道，为下次启动准备
	d.stopChan = make(chan struct{})
}

func (d *DelayedQueueManager) processLoop(ctx context.Context) {
	ticker := time.NewTicker(time.Second)
	defer ticker.Stop()

	for {
		select {
		case <-d.stopChan:
			return
		case <-ctx.Done():
			return
		case <-ticker.C:
			d.processDelayedTasks(ctx)
		}
	}
}

func (d *DelayedQueueManager) processDelayedTasks(ctx context.Context) {
	now := float64(time.Now().Unix())

	// 获取所有延时队列
	keys, err := d.client.Keys(ctx, "delayed:*").Result()
	if err != nil {
		return
	}

	for _, key := range keys {
		d.processQueueTasks(ctx, key, now)
	}

	// 同时处理重试队列
	retryKeys, err := d.client.Keys(ctx, "retry:*").Result()
	if err != nil {
		return
	}

	for _, key := range retryKeys {
		d.processQueueTasks(ctx, key, now)
	}
}

func (d *DelayedQueueManager) processQueueTasks(ctx context.Context, key string, now float64) {
	// 获取到期的任务
	tasks, err := d.client.ZRangeByScoreWithScores(ctx, key, &redis.ZRangeBy{
		Min: "0",
		Max: fmt.Sprintf("%f", now),
	}).Result()

	if err != nil {
		return
	}

	for _, task := range tasks {
		var delayedTask DelayedTask
		if err := json.Unmarshal([]byte(task.Member.(string)), &delayedTask); err != nil {
			// 移除无效的任务
			d.client.ZRem(ctx, key, task.Member)
			continue
		}

		// 发送任务到正常队列
		if err := d.producer.Send(ctx, delayedTask.Topic, delayedTask.Payload); err != nil {
			continue // 发送失败，下次再试
		}

		// 从延时队列中移除
		d.client.ZRem(ctx, key, task.Member)
	}
}

// DelayedTask 延时任务结构
type DelayedTask struct {
	Topic     string    `json:"topic"`
	Payload   []byte    `json:"payload"`
	ExecuteAt time.Time `json:"execute_at"`
}
