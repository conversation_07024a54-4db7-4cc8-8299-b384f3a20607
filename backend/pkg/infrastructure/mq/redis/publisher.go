package redis

import (
	"context"
	"sync"

	"github.com/google/uuid"
	"github.com/redis/go-redis/v9"

	"backend/pkg/infrastructure/mq"
)

// RedisPublisher Redis发布者实现（Pub/Sub）
type RedisPublisher struct {
	client *redis.Client
}

func NewRedisPublisher(client *redis.Client) *RedisPublisher {
	return &RedisPublisher{client: client}
}

func (p *RedisPublisher) Publish(ctx context.Context, channel string, payload []byte) error {
	return p.client.Publish(ctx, channel, payload).Err()
}

func (p *RedisPublisher) PublishMultiple(ctx context.Context, channels []string, payload []byte) error {
	pipe := p.client.Pipeline()
	for _, channel := range channels {
		pipe.Publish(ctx, channel, payload)
	}
	_, err := pipe.Exec(ctx)
	return err
}

func (p *RedisPublisher) Close() error {
	return nil
}

// RedisSubscriber Redis订阅者实现
type RedisSubscriber struct {
	client   *redis.Client
	pubsub   *redis.PubSub
	handlers map[string]mq.MessageHandler
	stopChan chan struct{}
	mu       sync.RWMutex
}

func NewRedisSubscriber(client *redis.Client) *RedisSubscriber {
	return &RedisSubscriber{
		client:   client,
		handlers: make(map[string]mq.MessageHandler),
		stopChan: make(chan struct{}),
	}
}

func (s *RedisSubscriber) Subscribe(ctx context.Context, channels []string, handler mq.MessageHandler) error {
	s.mu.Lock()
	defer s.mu.Unlock()

	if s.pubsub == nil {
		s.pubsub = s.client.Subscribe(ctx, channels...)
	} else {
		s.pubsub.Subscribe(ctx, channels...)
	}

	for _, channel := range channels {
		s.handlers[channel] = handler
	}

	// 启动消息接收循环
	go s.receiveLoop(ctx)

	return nil
}

func (s *RedisSubscriber) receiveLoop(ctx context.Context) {
	if s.pubsub == nil {
		return
	}

	ch := s.pubsub.Channel()

	for {
		select {
		case msg := <-ch:
			if msg == nil {
				continue
			}

			s.mu.RLock()
			handler, exists := s.handlers[msg.Channel]
			s.mu.RUnlock()

			if !exists {
				continue
			}

			message := &mq.Message{
				ID:      uuid.New().String(),
				Topic:   msg.Channel,
				Payload: []byte(msg.Payload),
				Headers: make(map[string]string),
			}

			handler.Handle(ctx, message)

		case <-s.stopChan:
			return
		case <-ctx.Done():
			return
		}
	}
}

func (s *RedisSubscriber) Unsubscribe(channels ...string) error {
	s.mu.Lock()
	defer s.mu.Unlock()

	if s.pubsub != nil {
		err := s.pubsub.Unsubscribe(context.Background(), channels...)
		if err != nil {
			return err
		}
	}

	for _, channel := range channels {
		delete(s.handlers, channel)
	}

	return nil
}

func (s *RedisSubscriber) Close() error {
	s.mu.Lock()
	defer s.mu.Unlock()

	close(s.stopChan)

	if s.pubsub != nil {
		err := s.pubsub.Close()
		s.pubsub = nil
		return err
	}

	return nil
}
