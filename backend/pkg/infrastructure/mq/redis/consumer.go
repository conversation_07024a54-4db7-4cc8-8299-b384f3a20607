package redis

import (
	"context"
	"encoding/json"
	"fmt"
	"sync"
	"time"

	"github.com/google/uuid"
	"github.com/redis/go-redis/v9"

	"backend/pkg/infrastructure/mq"
)

// RedisConsumer Redis消费者实现
type RedisConsumer struct {
	client        *redis.Client
	handlers      map[string]mq.MessageHandler
	stopChannels  map[string]chan struct{}
	consumerGroup string
	mu            sync.RWMutex
}

func NewRedisConsumer(client *redis.Client) *RedisConsumer {
	return &RedisConsumer{
		client:        client,
		handlers:      make(map[string]mq.MessageHandler),
		stopChannels:  make(map[string]chan struct{}),
		consumerGroup: "erp-consumer-group",
	}
}

func (c *RedisConsumer) Subscribe(ctx context.Context, topic string, handler mq.MessageHandler) error {
	c.mu.Lock()
	defer c.mu.Unlock()

	c.handlers[topic] = handler

	// 创建消费组
	streamName := fmt.Sprintf("topic:%s", topic)
	err := c.client.XGroupCreate(ctx, streamName, c.consumerGroup, "0").Err()
	if err != nil && err.Error() != "BUSYGROUP Consumer Group name already exists" {
		return err
	}

	// 创建停止信号通道
	stopChan := make(chan struct{})
	c.stopChannels[topic] = stopChan

	// 启动消费循环
	go c.consumeLoop(ctx, topic, stopChan)

	return nil
}

func (c *RedisConsumer) SubscribeMultiple(ctx context.Context, topics []string, handler mq.MessageHandler) error {
	for _, topic := range topics {
		if err := c.Subscribe(ctx, topic, handler); err != nil {
			return err
		}
	}
	return nil
}

func (c *RedisConsumer) consumeLoop(ctx context.Context, topic string, stopChan chan struct{}) {
	streamName := fmt.Sprintf("topic:%s", topic)
	consumerName := fmt.Sprintf("consumer-%s", uuid.New().String())

	for {
		select {
		case <-stopChan:
			return
		case <-ctx.Done():
			return
		default:
			// 从消费组读取消息
			streams, err := c.client.XReadGroup(ctx, &redis.XReadGroupArgs{
				Group:    c.consumerGroup,
				Consumer: consumerName,
				Streams:  []string{streamName, ">"},
				Count:    10,
				Block:    time.Second,
			}).Result()

			if err != nil {
				time.Sleep(time.Second)
				continue
			}

			for _, stream := range streams {
				for _, message := range stream.Messages {
					c.processMessage(ctx, topic, message)
				}
			}
		}
	}
}

func (c *RedisConsumer) processMessage(ctx context.Context, topic string, redisMsg redis.XMessage) {
	c.mu.RLock()
	handler, exists := c.handlers[topic]
	c.mu.RUnlock()

	if !exists {
		return
	}

	data, ok := redisMsg.Values["data"].(string)
	if !ok {
		return
	}

	var message mq.Message
	if err := json.Unmarshal([]byte(data), &message); err != nil {
		return
	}

	// 处理消息
	if err := handler.Handle(ctx, &message); err != nil {
		// 消息处理失败，可以实现重试逻辑
		message.Attempts++
		if message.Attempts < 3 { // 最多重试3次
			c.retryMessage(ctx, &message, time.Second*time.Duration(message.Attempts))
		}
		return
	}

	// 确认消息已处理
	streamName := fmt.Sprintf("topic:%s", topic)
	c.client.XAck(ctx, streamName, c.consumerGroup, redisMsg.ID)
}

func (c *RedisConsumer) retryMessage(ctx context.Context, message *mq.Message, delay time.Duration) {
	// 将失败的消息放入延时队列重新处理
	data, err := json.Marshal(message)
	if err != nil {
		return
	}

	executeTime := time.Now().Add(delay)
	score := float64(executeTime.Unix())

	c.client.ZAdd(ctx, fmt.Sprintf("retry:%s", message.Topic), redis.Z{
		Score:  score,
		Member: data,
	})
}

func (c *RedisConsumer) Unsubscribe(topic string) error {
	c.mu.Lock()
	defer c.mu.Unlock()

	// 发送停止信号
	if stopChan, exists := c.stopChannels[topic]; exists {
		close(stopChan)
		delete(c.stopChannels, topic)
	}

	delete(c.handlers, topic)
	return nil
}

func (c *RedisConsumer) Close() error {
	c.mu.Lock()
	defer c.mu.Unlock()

	// 关闭所有消费循环
	for topic, stopChan := range c.stopChannels {
		close(stopChan)
		delete(c.stopChannels, topic)
	}

	// 清空处理器
	c.handlers = make(map[string]mq.MessageHandler)

	return nil
}
