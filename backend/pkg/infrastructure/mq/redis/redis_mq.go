package redis

import (
	"context"
	"encoding/json"
	"fmt"
	"time"

	"github.com/google/uuid"
	"github.com/redis/go-redis/v9"

	"backend/pkg/infrastructure/mq"
)

// RedisMQManager Redis消息队列管理器
type RedisMQManager struct {
	client       *redis.Client
	producer     *RedisProducer
	consumer     *RedisConsumer
	publisher    *RedisPublisher
	subscriber   *RedisSubscriber
	delayedQueue *DelayedQueueManager
}

func NewRedisMQManager(client *redis.Client) mq.MQManager {
	return &RedisMQManager{
		client:       client,
		producer:     NewRedisProducer(client),
		consumer:     NewRedisConsumer(client),
		publisher:    NewRedisPublisher(client),
		subscriber:   NewRedisSubscriber(client),
		delayedQueue: NewDelayedQueueManager(client),
	}
}

func (r *RedisMQManager) Producer() mq.Producer {
	return r.producer
}

func (r *RedisMQManager) Consumer() mq.Consumer {
	return r.consumer
}

func (r *RedisMQManager) Publisher() mq.Publisher {
	return r.publisher
}

func (r *RedisMQManager) Subscriber() mq.Subscriber {
	return r.subscriber
}

func (r *RedisMQManager) DelayedQueue() mq.DelayedQueue {
	return r.delayedQueue
}

func (r *RedisMQManager) Health() error {
	return r.client.Ping(context.Background()).Err()
}

func (r *RedisMQManager) Close() error {
	var errs []error

	if err := r.producer.Close(); err != nil {
		errs = append(errs, err)
	}
	if err := r.consumer.Close(); err != nil {
		errs = append(errs, err)
	}
	if err := r.publisher.Close(); err != nil {
		errs = append(errs, err)
	}
	if err := r.subscriber.Close(); err != nil {
		errs = append(errs, err)
	}

	r.delayedQueue.StopProcessor()

	if len(errs) > 0 {
		return errs[0] // 返回第一个错误
	}
	return nil
}

// RedisProducer Redis生产者实现
type RedisProducer struct {
	client *redis.Client
}

func NewRedisProducer(client *redis.Client) *RedisProducer {
	return &RedisProducer{client: client}
}

func (p *RedisProducer) Send(ctx context.Context, topic string, payload []byte, headers ...map[string]string) error {
	message := &mq.Message{
		ID:        uuid.New().String(),
		Topic:     topic,
		Payload:   payload,
		Headers:   make(map[string]string),
		Timestamp: time.Now(),
		Attempts:  0,
	}

	if len(headers) > 0 {
		message.Headers = headers[0]
	}

	data, err := json.Marshal(message)
	if err != nil {
		return err
	}

	// 使用Redis Streams存储消息
	return p.client.XAdd(ctx, &redis.XAddArgs{
		Stream: fmt.Sprintf("topic:%s", topic),
		Values: map[string]interface{}{
			"data": data,
		},
	}).Err()
}

func (p *RedisProducer) SendBatch(ctx context.Context, messages []*mq.Message) error {
	pipe := p.client.Pipeline()

	for _, msg := range messages {
		data, err := json.Marshal(msg)
		if err != nil {
			return err
		}

		pipe.XAdd(ctx, &redis.XAddArgs{
			Stream: fmt.Sprintf("topic:%s", msg.Topic),
			Values: map[string]interface{}{
				"data": data,
			},
		})
	}

	_, err := pipe.Exec(ctx)
	return err
}

func (p *RedisProducer) SendDelayed(ctx context.Context, topic string, payload []byte, delay time.Duration, headers ...map[string]string) error {
	message := &mq.Message{
		ID:        uuid.New().String(),
		Topic:     topic,
		Payload:   payload,
		Headers:   make(map[string]string),
		Timestamp: time.Now().Add(delay),
		Attempts:  0,
	}

	if len(headers) > 0 {
		message.Headers = headers[0]
	}

	data, err := json.Marshal(message)
	if err != nil {
		return err
	}

	// 使用Redis Sorted Sets实现延时队列
	score := float64(message.Timestamp.Unix())
	return p.client.ZAdd(ctx, fmt.Sprintf("delayed:%s", topic), redis.Z{
		Score:  score,
		Member: data,
	}).Err()
}

func (p *RedisProducer) Close() error {
	return nil // Redis连接由管理器统一管理
}
