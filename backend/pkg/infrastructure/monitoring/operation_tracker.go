package monitoring

import (
	"context"
	"time"

	"backend/pkg/infrastructure/logger"
)

// OperationTracker 操作追踪器
type OperationTracker struct {
	logger         logger.Logger
	structuredLogger *logger.StructuredLogger
	metricsManager *MetricsManager
}

// NewOperationTracker 创建操作追踪器
func NewOperationTracker(log logger.Logger, metricsManager *MetricsManager) *OperationTracker {
	return &OperationTracker{
		logger:           log,
		structuredLogger: logger.NewStructuredLogger(log),
		metricsManager:   metricsManager,
	}
}

// TrackBusinessOperation 追踪业务操作
func (ot *OperationTracker) TrackBusinessOperation(ctx context.Context, operation string, start time.Time, success bool, details map[string]interface{}) {
	duration := time.Since(start)
	status := "success"
	if !success {
		status = "failure"
	}

	// 从上下文获取信息
	requestID := getRequestIDFromContext(ctx)
	tenantID := getTenantIDFromContext(ctx)
	userID := getUserIDFromContext(ctx)
	traceID := getTraceIDFromContext(ctx)

	// 记录结构化日志
	businessOp := logger.BusinessOperation{
		Operation: operation,
		TenantID:  tenantID,
		UserID:    userID,
		Action:    operation,
		Status:    status,
		Duration:  duration,
		Details:   details,
		RequestID: requestID,
		TraceID:   traceID,
	}

	if !success && details != nil {
		if err, ok := details["error"]; ok {
			businessOp.Error = err.(string)
		}
	}

	ot.structuredLogger.LogBusinessOperation(ctx, businessOp)

	// 记录指标
	if ot.metricsManager != nil {
		ot.metricsManager.RecordBusinessOperation(operation, status, tenantID, userID, duration)
	}
}

// TrackDatabaseOperation 追踪数据库操作
func (ot *OperationTracker) TrackDatabaseOperation(ctx context.Context, operation, table string, start time.Time, rowsAffected int64, err error) {
	duration := time.Since(start)
	status := "success"
	errorMsg := ""
	
	if err != nil {
		status = "error"
		errorMsg = err.Error()
	}

	// 从上下文获取信息
	requestID := getRequestIDFromContext(ctx)
	tenantID := getTenantIDFromContext(ctx)

	// 记录结构化日志
	dbOp := logger.DatabaseOperation{
		Operation:    operation,
		Table:        table,
		Duration:     duration,
		RowsAffected: rowsAffected,
		Error:        errorMsg,
		RequestID:    requestID,
		TenantID:     tenantID,
	}

	ot.structuredLogger.LogDatabaseOperation(ctx, dbOp)

	// 记录指标
	if ot.metricsManager != nil {
		ot.metricsManager.RecordDatabaseQuery(operation, table, status, tenantID, duration)
	}
}

// TrackCacheOperation 追踪缓存操作
func (ot *OperationTracker) TrackCacheOperation(ctx context.Context, operation, key string, start time.Time, hit bool, size int, err error) {
	duration := time.Since(start)
	errorMsg := ""
	
	if err != nil {
		errorMsg = err.Error()
	}

	// 从上下文获取信息
	requestID := getRequestIDFromContext(ctx)
	tenantID := getTenantIDFromContext(ctx)

	// 记录结构化日志
	cacheOp := logger.CacheOperation{
		Operation: operation,
		Key:       key,
		Hit:       hit,
		Duration:  duration,
		Size:      size,
		Error:     errorMsg,
		RequestID: requestID,
	}

	ot.structuredLogger.LogCacheOperation(ctx, cacheOp)

	// 记录指标
	if ot.metricsManager != nil {
		ot.metricsManager.RecordCacheOperation(key, operation, tenantID, hit, duration)
	}
}

// TrackExternalCall 追踪外部调用
func (ot *OperationTracker) TrackExternalCall(ctx context.Context, service, endpoint, method string, start time.Time, statusCode int, requestSize, responseSize int64, retryCount int, err error) {
	duration := time.Since(start)
	errorMsg := ""
	
	if err != nil {
		errorMsg = err.Error()
	}

	// 从上下文获取信息
	requestID := getRequestIDFromContext(ctx)

	// 记录结构化日志
	externalCall := logger.ExternalCall{
		Service:      service,
		Endpoint:     endpoint,
		Method:       method,
		Duration:     duration,
		StatusCode:   statusCode,
		RequestSize:  requestSize,
		ResponseSize: responseSize,
		Error:        errorMsg,
		RequestID:    requestID,
		RetryCount:   retryCount,
	}

	ot.structuredLogger.LogExternalCall(ctx, externalCall)
}

// TrackSecurityEvent 追踪安全事件
func (ot *OperationTracker) TrackSecurityEvent(ctx context.Context, event, result, severity string, details map[string]interface{}) {
	// 从上下文获取信息
	requestID := getRequestIDFromContext(ctx)
	tenantID := getTenantIDFromContext(ctx)
	userID := getUserIDFromContext(ctx)
	ipAddress := getIPAddressFromContext(ctx)
	userAgent := getUserAgentFromContext(ctx)

	// 记录结构化日志
	securityEvent := logger.SecurityEvent{
		Event:     event,
		UserID:    userID,
		TenantID:  tenantID,
		IPAddress: ipAddress,
		UserAgent: userAgent,
		Result:    result,
		Details:   details,
		RequestID: requestID,
		Severity:  severity,
	}

	if details != nil {
		if resource, ok := details["resource"]; ok {
			securityEvent.Resource = resource.(string)
		}
		if action, ok := details["action"]; ok {
			securityEvent.Action = action.(string)
		}
		if reason, ok := details["reason"]; ok {
			securityEvent.Reason = reason.(string)
		}
	}

	ot.structuredLogger.LogSecurityEvent(ctx, securityEvent)

	// 记录错误指标（如果是失败的安全事件）
	if ot.metricsManager != nil && result != "success" {
		ot.metricsManager.RecordError("security", event, tenantID)
	}
}

// TrackUserAction 追踪用户行为
func (ot *OperationTracker) TrackUserAction(ctx context.Context, action string, start time.Time, success bool, details map[string]interface{}) {
	duration := time.Since(start)
	
	// 从上下文获取信息
	requestID := getRequestIDFromContext(ctx)
	tenantID := getTenantIDFromContext(ctx)
	userID := getUserIDFromContext(ctx)
	traceID := getTraceIDFromContext(ctx)

	status := "success"
	if !success {
		status = "failure"
	}

	// 记录结构化日志
	businessOp := logger.BusinessOperation{
		Operation: "user_action",
		TenantID:  tenantID,
		UserID:    userID,
		Action:    action,
		Status:    status,
		Duration:  duration,
		Details:   details,
		RequestID: requestID,
		TraceID:   traceID,
	}

	if !success && details != nil {
		if err, ok := details["error"]; ok {
			businessOp.Error = err.(string)
		}
	}

	ot.structuredLogger.LogBusinessOperation(ctx, businessOp)

	// 记录指标
	if ot.metricsManager != nil {
		ot.metricsManager.RecordBusinessOperation("user_action_"+action, status, tenantID, userID, duration)
	}
}

// TrackPerformanceMetric 追踪性能指标
func (ot *OperationTracker) TrackPerformanceMetric(ctx context.Context, metric string, value float64, unit string, details map[string]interface{}) {
	requestID := getRequestIDFromContext(ctx)
	tenantID := getTenantIDFromContext(ctx)

	fields := map[string]interface{}{
		"metric":     metric,
		"value":      value,
		"unit":       unit,
		"request_id": requestID,
		"tenant_id":  tenantID,
	}

	// 合并详细信息
	for key, val := range details {
		fields[key] = val
	}

	ot.structuredLogger.LogWithFields(ctx, logger.LevelInfo, "Performance metric recorded", fields)
}

// 辅助函数：从上下文获取各种信息
func getRequestIDFromContext(ctx context.Context) string {
	if requestID := ctx.Value("request_id"); requestID != nil {
		if id, ok := requestID.(string); ok {
			return id
		}
	}
	return ""
}

func getTenantIDFromContext(ctx context.Context) string {
	if tenantID := ctx.Value("tenant_id"); tenantID != nil {
		if id, ok := tenantID.(string); ok {
			return id
		}
	}
	return "unknown"
}

func getUserIDFromContext(ctx context.Context) string {
	if userID := ctx.Value("user_id"); userID != nil {
		if id, ok := userID.(string); ok {
			return id
		}
	}
	return "unknown"
}

func getTraceIDFromContext(ctx context.Context) string {
	if traceID := ctx.Value("trace_id"); traceID != nil {
		if id, ok := traceID.(string); ok {
			return id
		}
	}
	return ""
}

func getIPAddressFromContext(ctx context.Context) string {
	if ip := ctx.Value("client_ip"); ip != nil {
		if addr, ok := ip.(string); ok {
			return addr
		}
	}
	return ""
}

func getUserAgentFromContext(ctx context.Context) string {
	if ua := ctx.Value("user_agent"); ua != nil {
		if agent, ok := ua.(string); ok {
			return agent
		}
	}
	return ""
}
