package monitoring

import (
	"net/http"
	"time"

	"github.com/prometheus/client_golang/prometheus"
	"github.com/prometheus/client_golang/prometheus/promhttp"
)

// MetricsManager 指标管理器
type MetricsManager struct {
	// HTTP 指标
	httpRequestsTotal   *prometheus.CounterVec
	httpRequestDuration *prometheus.HistogramVec
	httpRequestSize     *prometheus.HistogramVec
	httpResponseSize    *prometheus.HistogramVec

	// 数据库指标
	dbConnectionsActive prometheus.Gauge
	dbConnectionsIdle   prometheus.Gauge
	dbConnectionsTotal  prometheus.Gauge
	dbQueryDuration     *prometheus.HistogramVec
	dbQueriesTotal      *prometheus.CounterVec

	// 缓存指标
	cacheHitsTotal         *prometheus.CounterVec
	cacheMissesTotal       *prometheus.CounterVec
	cacheOperationDuration *prometheus.HistogramVec

	// 业务指标
	businessOperationsTotal   *prometheus.CounterVec
	businessOperationDuration *prometheus.HistogramVec

	// 应用指标
	appInfo     *prometheus.GaugeVec
	goRoutines  prometheus.Gauge
	memoryUsage prometheus.Gauge
	cpuUsage    prometheus.Gauge

	// 错误指标
	errorsTotal *prometheus.CounterVec
	panicTotal  prometheus.Counter

	registry *prometheus.Registry
}

// NewMetricsManager 创建新的指标管理器
func NewMetricsManager() *MetricsManager {
	registry := prometheus.NewRegistry()

	m := &MetricsManager{
		// HTTP 指标
		httpRequestsTotal: prometheus.NewCounterVec(
			prometheus.CounterOpts{
				Name: "http_requests_total",
				Help: "Total number of HTTP requests",
			},
			[]string{"method", "endpoint", "status_code", "tenant_id"},
		),
		httpRequestDuration: prometheus.NewHistogramVec(
			prometheus.HistogramOpts{
				Name:    "http_request_duration_seconds",
				Help:    "HTTP request duration in seconds",
				Buckets: prometheus.DefBuckets,
			},
			[]string{"method", "endpoint", "status_code"},
		),
		httpRequestSize: prometheus.NewHistogramVec(
			prometheus.HistogramOpts{
				Name:    "http_request_size_bytes",
				Help:    "HTTP request size in bytes",
				Buckets: prometheus.ExponentialBuckets(100, 10, 8),
			},
			[]string{"method", "endpoint"},
		),
		httpResponseSize: prometheus.NewHistogramVec(
			prometheus.HistogramOpts{
				Name:    "http_response_size_bytes",
				Help:    "HTTP response size in bytes",
				Buckets: prometheus.ExponentialBuckets(100, 10, 8),
			},
			[]string{"method", "endpoint", "status_code"},
		),

		// 数据库指标
		dbConnectionsActive: prometheus.NewGauge(
			prometheus.GaugeOpts{
				Name: "database_connections_active",
				Help: "Number of active database connections",
			},
		),
		dbConnectionsIdle: prometheus.NewGauge(
			prometheus.GaugeOpts{
				Name: "database_connections_idle",
				Help: "Number of idle database connections",
			},
		),
		dbConnectionsTotal: prometheus.NewGauge(
			prometheus.GaugeOpts{
				Name: "database_connections_total",
				Help: "Total number of database connections",
			},
		),
		dbQueryDuration: prometheus.NewHistogramVec(
			prometheus.HistogramOpts{
				Name:    "database_query_duration_seconds",
				Help:    "Database query duration in seconds",
				Buckets: []float64{0.001, 0.005, 0.01, 0.05, 0.1, 0.5, 1, 5},
			},
			[]string{"operation", "table", "tenant_id"},
		),
		dbQueriesTotal: prometheus.NewCounterVec(
			prometheus.CounterOpts{
				Name: "database_queries_total",
				Help: "Total number of database queries",
			},
			[]string{"operation", "table", "status", "tenant_id"},
		),

		// 缓存指标
		cacheHitsTotal: prometheus.NewCounterVec(
			prometheus.CounterOpts{
				Name: "cache_hits_total",
				Help: "Total number of cache hits",
			},
			[]string{"cache_name", "operation", "tenant_id"},
		),
		cacheMissesTotal: prometheus.NewCounterVec(
			prometheus.CounterOpts{
				Name: "cache_misses_total",
				Help: "Total number of cache misses",
			},
			[]string{"cache_name", "operation", "tenant_id"},
		),
		cacheOperationDuration: prometheus.NewHistogramVec(
			prometheus.HistogramOpts{
				Name:    "cache_operation_duration_seconds",
				Help:    "Cache operation duration in seconds",
				Buckets: []float64{0.0001, 0.0005, 0.001, 0.005, 0.01, 0.05, 0.1},
			},
			[]string{"cache_name", "operation"},
		),

		// 业务指标
		businessOperationsTotal: prometheus.NewCounterVec(
			prometheus.CounterOpts{
				Name: "business_operations_total",
				Help: "Total number of business operations",
			},
			[]string{"operation", "status", "tenant_id", "user_id"},
		),
		businessOperationDuration: prometheus.NewHistogramVec(
			prometheus.HistogramOpts{
				Name:    "business_operation_duration_seconds",
				Help:    "Business operation duration in seconds",
				Buckets: []float64{0.01, 0.05, 0.1, 0.5, 1, 5, 10, 30},
			},
			[]string{"operation", "tenant_id"},
		),

		// 应用指标
		appInfo: prometheus.NewGaugeVec(
			prometheus.GaugeOpts{
				Name: "app_info",
				Help: "Application information",
			},
			[]string{"version", "environment", "service"},
		),
		goRoutines: prometheus.NewGauge(
			prometheus.GaugeOpts{
				Name: "go_goroutines",
				Help: "Number of goroutines",
			},
		),
		memoryUsage: prometheus.NewGauge(
			prometheus.GaugeOpts{
				Name: "memory_usage_bytes",
				Help: "Memory usage in bytes",
			},
		),
		cpuUsage: prometheus.NewGauge(
			prometheus.GaugeOpts{
				Name: "cpu_usage_percent",
				Help: "CPU usage percentage",
			},
		),

		// 错误指标
		errorsTotal: prometheus.NewCounterVec(
			prometheus.CounterOpts{
				Name: "errors_total",
				Help: "Total number of errors",
			},
			[]string{"error_type", "error_code", "tenant_id"},
		),
		panicTotal: prometheus.NewCounter(
			prometheus.CounterOpts{
				Name: "panic_total",
				Help: "Total number of panics",
			},
		),

		registry: registry,
	}

	// 注册所有指标
	m.registerMetrics()

	return m
}

// registerMetrics 注册所有指标
func (m *MetricsManager) registerMetrics() {
	// HTTP 指标
	m.registry.MustRegister(m.httpRequestsTotal)
	m.registry.MustRegister(m.httpRequestDuration)
	m.registry.MustRegister(m.httpRequestSize)
	m.registry.MustRegister(m.httpResponseSize)

	// 数据库指标
	m.registry.MustRegister(m.dbConnectionsActive)
	m.registry.MustRegister(m.dbConnectionsIdle)
	m.registry.MustRegister(m.dbConnectionsTotal)
	m.registry.MustRegister(m.dbQueryDuration)
	m.registry.MustRegister(m.dbQueriesTotal)

	// 缓存指标
	m.registry.MustRegister(m.cacheHitsTotal)
	m.registry.MustRegister(m.cacheMissesTotal)
	m.registry.MustRegister(m.cacheOperationDuration)

	// 业务指标
	m.registry.MustRegister(m.businessOperationsTotal)
	m.registry.MustRegister(m.businessOperationDuration)

	// 应用指标
	m.registry.MustRegister(m.appInfo)
	m.registry.MustRegister(m.goRoutines)
	m.registry.MustRegister(m.memoryUsage)
	m.registry.MustRegister(m.cpuUsage)

	// 错误指标
	m.registry.MustRegister(m.errorsTotal)
	m.registry.MustRegister(m.panicTotal)
}

// GetHandler 获取 Prometheus HTTP 处理器
func (m *MetricsManager) GetHandler() http.Handler {
	return promhttp.HandlerFor(m.registry, promhttp.HandlerOpts{})
}

// RecordHTTPRequest 记录 HTTP 请求指标
func (m *MetricsManager) RecordHTTPRequest(method, endpoint, statusCode, tenantID string, duration time.Duration, requestSize, responseSize int64) {
	m.httpRequestsTotal.WithLabelValues(method, endpoint, statusCode, tenantID).Inc()
	m.httpRequestDuration.WithLabelValues(method, endpoint, statusCode).Observe(duration.Seconds())

	if requestSize > 0 {
		m.httpRequestSize.WithLabelValues(method, endpoint).Observe(float64(requestSize))
	}
	if responseSize > 0 {
		m.httpResponseSize.WithLabelValues(method, endpoint, statusCode).Observe(float64(responseSize))
	}
}

// RecordDatabaseQuery 记录数据库查询指标
func (m *MetricsManager) RecordDatabaseQuery(operation, table, status, tenantID string, duration time.Duration) {
	m.dbQueriesTotal.WithLabelValues(operation, table, status, tenantID).Inc()
	m.dbQueryDuration.WithLabelValues(operation, table, tenantID).Observe(duration.Seconds())
}

// UpdateDatabaseConnections 更新数据库连接指标
func (m *MetricsManager) UpdateDatabaseConnections(active, idle, total int) {
	m.dbConnectionsActive.Set(float64(active))
	m.dbConnectionsIdle.Set(float64(idle))
	m.dbConnectionsTotal.Set(float64(total))
}

// RecordCacheOperation 记录缓存操作指标
func (m *MetricsManager) RecordCacheOperation(cacheName, operation, tenantID string, hit bool, duration time.Duration) {
	if hit {
		m.cacheHitsTotal.WithLabelValues(cacheName, operation, tenantID).Inc()
	} else {
		m.cacheMissesTotal.WithLabelValues(cacheName, operation, tenantID).Inc()
	}
	m.cacheOperationDuration.WithLabelValues(cacheName, operation).Observe(duration.Seconds())
}

// RecordBusinessOperation 记录业务操作指标
func (m *MetricsManager) RecordBusinessOperation(operation, status, tenantID, userID string, duration time.Duration) {
	m.businessOperationsTotal.WithLabelValues(operation, status, tenantID, userID).Inc()
	m.businessOperationDuration.WithLabelValues(operation, tenantID).Observe(duration.Seconds())
}

// RecordError 记录错误指标
func (m *MetricsManager) RecordError(errorType, errorCode, tenantID string) {
	m.errorsTotal.WithLabelValues(errorType, errorCode, tenantID).Inc()
}

// RecordPanic 记录 Panic 指标
func (m *MetricsManager) RecordPanic() {
	m.panicTotal.Inc()
}

// SetAppInfo 设置应用信息
func (m *MetricsManager) SetAppInfo(version, environment, service string) {
	m.appInfo.WithLabelValues(version, environment, service).Set(1)
}

// UpdateSystemMetrics 更新系统指标
func (m *MetricsManager) UpdateSystemMetrics(goroutines int, memoryBytes uint64, cpuPercent float64) {
	m.goRoutines.Set(float64(goroutines))
	m.memoryUsage.Set(float64(memoryBytes))
	m.cpuUsage.Set(cpuPercent)
}
