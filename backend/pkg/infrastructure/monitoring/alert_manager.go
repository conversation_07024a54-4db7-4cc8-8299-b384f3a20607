package monitoring

import (
	"context"
	"fmt"
	"sync"
	"time"

	"backend/pkg/infrastructure/logger"
)

// AlertManager 告警管理器
type AlertManager struct {
	logger logger.Logger

	// 告警配置
	config *AlertConfig

	// 告警状态
	mu           sync.RWMutex
	activeAlerts map[string]*Alert
	alertHistory []*Alert

	// 告警通道
	channels []AlertChannel

	// 抑制规则
	suppressionRules map[string]*SuppressionRule
}

// AlertConfig 告警配置
type AlertConfig struct {
	// 是否启用告警
	Enabled bool `json:"enabled"`

	// 告警去重时间窗口
	DeduplicationWindow time.Duration `json:"deduplication_window"`

	// 告警历史保留时间
	HistoryRetention time.Duration `json:"history_retention"`

	// 默认告警通道
	DefaultChannels []string `json:"default_channels"`

	// 告警级别配置
	SeverityConfig map[string]*SeverityConfig `json:"severity_config"`
}

// SeverityConfig 告警级别配置
type SeverityConfig struct {
	// 是否启用
	Enabled bool `json:"enabled"`

	// 通知通道
	Channels []string `json:"channels"`

	// 重复通知间隔
	RepeatInterval time.Duration `json:"repeat_interval"`

	// 自动恢复时间
	AutoResolveAfter time.Duration `json:"auto_resolve_after"`
}

// Alert 告警
type Alert struct {
	ID          string                 `json:"id"`
	Type        string                 `json:"type"`
	Severity    string                 `json:"severity"`
	Title       string                 `json:"title"`
	Description string                 `json:"description"`
	Data        map[string]interface{} `json:"data"`

	// 时间信息
	CreatedAt  time.Time  `json:"created_at"`
	UpdatedAt  time.Time  `json:"updated_at"`
	ResolvedAt *time.Time `json:"resolved_at,omitempty"`

	// 状态
	Status string `json:"status"` // active, resolved, suppressed

	// 计数器
	Count int `json:"count"`

	// 标签
	Labels map[string]string `json:"labels"`

	// 关联信息
	RequestID string `json:"request_id,omitempty"`
	TenantID  string `json:"tenant_id,omitempty"`
}

// AlertChannel 告警通道接口
type AlertChannel interface {
	Name() string
	Send(ctx context.Context, alert *Alert) error
	IsEnabled() bool
}

// SuppressionRule 抑制规则
type SuppressionRule struct {
	Name        string            `json:"name"`
	Matchers    map[string]string `json:"matchers"`
	Duration    time.Duration     `json:"duration"`
	Description string            `json:"description"`
	CreatedAt   time.Time         `json:"created_at"`
}

// NewAlertManager 创建告警管理器
func NewAlertManager(logger logger.Logger) *AlertManager {
	return &AlertManager{
		logger:           logger,
		config:           getDefaultAlertConfig(),
		activeAlerts:     make(map[string]*Alert),
		alertHistory:     make([]*Alert, 0),
		channels:         make([]AlertChannel, 0),
		suppressionRules: make(map[string]*SuppressionRule),
	}
}

// getDefaultAlertConfig 获取默认告警配置
func getDefaultAlertConfig() *AlertConfig {
	return &AlertConfig{
		Enabled:             true,
		DeduplicationWindow: 5 * time.Minute,
		HistoryRetention:    7 * 24 * time.Hour, // 7天
		DefaultChannels:     []string{"log"},
		SeverityConfig: map[string]*SeverityConfig{
			"critical": {
				Enabled:          true,
				Channels:         []string{"log", "email"},
				RepeatInterval:   15 * time.Minute,
				AutoResolveAfter: 1 * time.Hour,
			},
			"warning": {
				Enabled:          true,
				Channels:         []string{"log"},
				RepeatInterval:   30 * time.Minute,
				AutoResolveAfter: 2 * time.Hour,
			},
			"info": {
				Enabled:          true,
				Channels:         []string{"log"},
				RepeatInterval:   1 * time.Hour,
				AutoResolveAfter: 4 * time.Hour,
			},
		},
	}
}

// TriggerAlert 触发告警
func (am *AlertManager) TriggerAlert(ctx context.Context, alertType, severity string, data map[string]interface{}) {
	if !am.config.Enabled {
		return
	}

	// 检查告警级别是否启用
	severityConfig, exists := am.config.SeverityConfig[severity]
	if !exists || !severityConfig.Enabled {
		return
	}

	// 生成告警ID
	alertID := am.generateAlertID(alertType, severity, data)

	am.mu.Lock()
	defer am.mu.Unlock()

	// 检查是否存在活跃的相同告警
	if existingAlert, exists := am.activeAlerts[alertID]; exists {
		// 更新现有告警
		existingAlert.Count++
		existingAlert.UpdatedAt = time.Now()
		existingAlert.Data = data

		am.logger.Debug(ctx, "Alert updated",
			"alert_id", alertID,
			"count", existingAlert.Count)
		return
	}

	// 创建新告警
	alert := &Alert{
		ID:          alertID,
		Type:        alertType,
		Severity:    severity,
		Title:       am.generateAlertTitle(alertType, severity),
		Description: am.generateAlertDescription(alertType, severity, data),
		Data:        data,
		CreatedAt:   time.Now(),
		UpdatedAt:   time.Now(),
		Status:      "active",
		Count:       1,
		Labels:      am.generateAlertLabels(alertType, severity, data),
		RequestID:   am.getRequestIDFromContext(ctx),
		TenantID:    am.getTenantIDFromContext(ctx),
	}

	// 检查抑制规则
	if am.isAlertSuppressed(alert) {
		alert.Status = "suppressed"
		am.logger.Debug(ctx, "Alert suppressed",
			"alert_id", alertID,
			"type", alertType,
			"severity", severity)
		return
	}

	// 添加到活跃告警
	am.activeAlerts[alertID] = alert
	am.alertHistory = append(am.alertHistory, alert)

	am.logger.Info(ctx, "Alert triggered",
		"alert_id", alertID,
		"type", alertType,
		"severity", severity,
		"title", alert.Title)

	// 发送告警通知
	go am.sendAlertNotification(ctx, alert)
}

// ResolveAlert 解决告警
func (am *AlertManager) ResolveAlert(ctx context.Context, alertID string) {
	am.mu.Lock()
	defer am.mu.Unlock()

	alert, exists := am.activeAlerts[alertID]
	if !exists {
		return
	}

	now := time.Now()
	alert.Status = "resolved"
	alert.ResolvedAt = &now
	alert.UpdatedAt = now

	// 从活跃告警中移除
	delete(am.activeAlerts, alertID)

	am.logger.Info(ctx, "Alert resolved",
		"alert_id", alertID,
		"type", alert.Type,
		"severity", alert.Severity,
		"duration", now.Sub(alert.CreatedAt))
}

// sendAlertNotification 发送告警通知
func (am *AlertManager) sendAlertNotification(ctx context.Context, alert *Alert) {
	severityConfig := am.config.SeverityConfig[alert.Severity]
	channels := severityConfig.Channels
	if len(channels) == 0 {
		channels = am.config.DefaultChannels
	}

	for _, channelName := range channels {
		channel := am.getChannel(channelName)
		if channel == nil || !channel.IsEnabled() {
			continue
		}

		if err := channel.Send(ctx, alert); err != nil {
			am.logger.Error(ctx, "Failed to send alert notification",
				"alert_id", alert.ID,
				"channel", channelName,
				"error", err)
		} else {
			am.logger.Debug(ctx, "Alert notification sent",
				"alert_id", alert.ID,
				"channel", channelName)
		}
	}
}

// AddChannel 添加告警通道
func (am *AlertManager) AddChannel(channel AlertChannel) {
	am.channels = append(am.channels, channel)
	am.logger.Info(context.Background(), "Alert channel added",
		"channel", channel.Name(),
		"enabled", channel.IsEnabled())
}

// getChannel 获取告警通道
func (am *AlertManager) getChannel(name string) AlertChannel {
	for _, channel := range am.channels {
		if channel.Name() == name {
			return channel
		}
	}
	return nil
}

// AddSuppressionRule 添加抑制规则
func (am *AlertManager) AddSuppressionRule(rule *SuppressionRule) {
	am.mu.Lock()
	defer am.mu.Unlock()

	rule.CreatedAt = time.Now()
	am.suppressionRules[rule.Name] = rule

	am.logger.Info(context.Background(), "Suppression rule added",
		"rule_name", rule.Name,
		"duration", rule.Duration)
}

// isAlertSuppressed 检查告警是否被抑制
func (am *AlertManager) isAlertSuppressed(alert *Alert) bool {
	for _, rule := range am.suppressionRules {
		if am.matchesSuppressionRule(alert, rule) {
			return true
		}
	}
	return false
}

// matchesSuppressionRule 检查告警是否匹配抑制规则
func (am *AlertManager) matchesSuppressionRule(alert *Alert, rule *SuppressionRule) bool {
	for key, value := range rule.Matchers {
		switch key {
		case "type":
			if alert.Type != value {
				return false
			}
		case "severity":
			if alert.Severity != value {
				return false
			}
		default:
			if labelValue, exists := alert.Labels[key]; !exists || labelValue != value {
				return false
			}
		}
	}
	return true
}

// GetActiveAlerts 获取活跃告警
func (am *AlertManager) GetActiveAlerts() []*Alert {
	am.mu.RLock()
	defer am.mu.RUnlock()

	alerts := make([]*Alert, 0, len(am.activeAlerts))
	for _, alert := range am.activeAlerts {
		alerts = append(alerts, alert)
	}
	return alerts
}

// GetAlertHistory 获取告警历史
func (am *AlertManager) GetAlertHistory(limit int) []*Alert {
	am.mu.RLock()
	defer am.mu.RUnlock()

	if limit <= 0 || limit > len(am.alertHistory) {
		limit = len(am.alertHistory)
	}

	// 返回最近的告警
	start := len(am.alertHistory) - limit
	return am.alertHistory[start:]
}

// 辅助方法
func (am *AlertManager) generateAlertID(alertType, severity string, data map[string]interface{}) string {
	// 简单的ID生成策略，实际应用中可能需要更复杂的逻辑
	return fmt.Sprintf("%s_%s_%d", alertType, severity, time.Now().Unix())
}

func (am *AlertManager) generateAlertTitle(alertType, severity string) string {
	return fmt.Sprintf("[%s] %s Alert", severity, alertType)
}

func (am *AlertManager) generateAlertDescription(alertType, severity string, data map[string]interface{}) string {
	return fmt.Sprintf("Alert of type '%s' with severity '%s' has been triggered. Data: %v", alertType, severity, data)
}

func (am *AlertManager) generateAlertLabels(alertType, severity string, data map[string]interface{}) map[string]string {
	labels := map[string]string{
		"alert_type": alertType,
		"severity":   severity,
	}

	// 从数据中提取一些标签
	if tenant, ok := data["tenant_id"].(string); ok {
		labels["tenant_id"] = tenant
	}

	return labels
}

func (am *AlertManager) getRequestIDFromContext(ctx context.Context) string {
	if ctx == nil {
		return ""
	}
	if requestID := ctx.Value("request_id"); requestID != nil {
		if id, ok := requestID.(string); ok {
			return id
		}
	}
	return ""
}

func (am *AlertManager) getTenantIDFromContext(ctx context.Context) string {
	if ctx == nil {
		return ""
	}
	if tenantID := ctx.Value("tenant_id"); tenantID != nil {
		if id, ok := tenantID.(string); ok {
			return id
		}
	}
	return ""
}
