package monitoring

import (
	"context"
	"runtime"
	"time"

	"backend/pkg/infrastructure/logger"
)

// SystemMetricsCollector 系统指标收集器
type SystemMetricsCollector struct {
	metricsManager *MetricsManager
	logger         logger.Logger
	stopChan       chan struct{}
	interval       time.Duration
}

// NewSystemMetricsCollector 创建系统指标收集器
func NewSystemMetricsCollector(metricsManager *MetricsManager, logger logger.Logger) *SystemMetricsCollector {
	return &SystemMetricsCollector{
		metricsManager: metricsManager,
		logger:         logger,
		stopChan:       make(chan struct{}),
		interval:       30 * time.Second, // 默认30秒收集一次
	}
}

// Start 开始收集系统指标
func (c *SystemMetricsCollector) Start(ctx context.Context) {
	c.logger.Info(ctx, "Starting system metrics collection", "interval", c.interval)

	ticker := time.NewTicker(c.interval)
	defer ticker.Stop()

	// 立即收集一次
	c.collectMetrics(ctx)

	for {
		select {
		case <-ticker.C:
			c.collectMetrics(ctx)
		case <-c.stopChan:
			c.logger.Info(ctx, "System metrics collection stopped")
			return
		case <-ctx.Done():
			c.logger.Info(ctx, "System metrics collection stopped due to context cancellation")
			return
		}
	}
}

// Stop 停止收集系统指标
func (c *SystemMetricsCollector) Stop() {
	close(c.stopChan)
}

// SetInterval 设置收集间隔
func (c *SystemMetricsCollector) SetInterval(interval time.Duration) {
	c.interval = interval
}

// collectMetrics 收集系统指标
func (c *SystemMetricsCollector) collectMetrics(ctx context.Context) {
	// 收集 Go 运行时指标
	c.collectGoMetrics()

	// 收集内存指标
	c.collectMemoryMetrics()

	// 收集 Goroutine 指标
	c.collectGoroutineMetrics()

	c.logger.Debug(ctx, "System metrics collected successfully")
}

// collectGoMetrics 收集 Go 运行时指标
func (c *SystemMetricsCollector) collectGoMetrics() {
	var m runtime.MemStats
	runtime.ReadMemStats(&m)

	// 更新内存使用指标
	c.metricsManager.UpdateSystemMetrics(
		runtime.NumGoroutine(),
		m.Alloc,
		0, // CPU 使用率需要额外计算
	)
}

// collectMemoryMetrics 收集内存指标
func (c *SystemMetricsCollector) collectMemoryMetrics() {
	var m runtime.MemStats
	runtime.ReadMemStats(&m)

	// 可以添加更详细的内存指标
	// 例如：堆内存、栈内存、GC 次数等
}

// collectGoroutineMetrics 收集 Goroutine 指标
func (c *SystemMetricsCollector) collectGoroutineMetrics() {
	numGoroutines := runtime.NumGoroutine()

	// 如果 Goroutine 数量过多，记录警告
	if numGoroutines > 1000 {
		c.logger.Warn(context.Background(), "High number of goroutines detected",
			"count", numGoroutines)
	}
}

// DatabaseMetricsCollector 数据库指标收集器
type DatabaseMetricsCollector struct {
	metricsManager *MetricsManager
	logger         logger.Logger
}

// NewDatabaseMetricsCollector 创建数据库指标收集器
func NewDatabaseMetricsCollector(metricsManager *MetricsManager, logger logger.Logger) *DatabaseMetricsCollector {
	return &DatabaseMetricsCollector{
		metricsManager: metricsManager,
		logger:         logger,
	}
}

// CollectConnectionPoolMetrics 收集连接池指标
func (c *DatabaseMetricsCollector) CollectConnectionPoolMetrics(active, idle, total int) {
	c.metricsManager.UpdateDatabaseConnections(active, idle, total)
}

// RecordQuery 记录数据库查询
func (c *DatabaseMetricsCollector) RecordQuery(operation, table, tenantID string, duration time.Duration, err error) {
	status := "success"
	if err != nil {
		status = "error"
		c.logger.Warn(context.Background(), "Database query failed",
			"operation", operation,
			"table", table,
			"tenant_id", tenantID,
			"duration", duration,
			"error", err)
	}

	c.metricsManager.RecordDatabaseQuery(operation, table, status, tenantID, duration)
}

// CacheMetricsCollector 缓存指标收集器
type CacheMetricsCollector struct {
	metricsManager *MetricsManager
	logger         logger.Logger
}

// NewCacheMetricsCollector 创建缓存指标收集器
func NewCacheMetricsCollector(metricsManager *MetricsManager, logger logger.Logger) *CacheMetricsCollector {
	return &CacheMetricsCollector{
		metricsManager: metricsManager,
		logger:         logger,
	}
}

// RecordOperation 记录缓存操作
func (c *CacheMetricsCollector) RecordOperation(cacheName, operation, tenantID string, duration time.Duration, hit bool, err error) {
	if err != nil {
		c.logger.Warn(context.Background(), "Cache operation failed",
			"cache_name", cacheName,
			"operation", operation,
			"tenant_id", tenantID,
			"duration", duration,
			"error", err)
	}

	c.metricsManager.RecordCacheOperation(cacheName, operation, tenantID, hit, duration)
}

// BusinessMetricsCollector 业务指标收集器
type BusinessMetricsCollector struct {
	metricsManager *MetricsManager
	logger         logger.Logger
}

// NewBusinessMetricsCollector 创建业务指标收集器
func NewBusinessMetricsCollector(metricsManager *MetricsManager, logger logger.Logger) *BusinessMetricsCollector {
	return &BusinessMetricsCollector{
		metricsManager: metricsManager,
		logger:         logger,
	}
}

// RecordUserLogin 记录用户登录
func (c *BusinessMetricsCollector) RecordUserLogin(tenantID, userID string, success bool, duration time.Duration) {
	status := "success"
	if !success {
		status = "failure"
	}

	c.metricsManager.RecordBusinessOperation("user_login", status, tenantID, userID, duration)

	if c.logger != nil {
		c.logger.Info(context.Background(), "User login recorded",
			"tenant_id", tenantID,
			"user_id", userID,
			"success", success,
			"duration", duration)
	}
}

// RecordOrderCreation 记录订单创建
func (c *BusinessMetricsCollector) RecordOrderCreation(tenantID, userID string, success bool, duration time.Duration) {
	status := "success"
	if !success {
		status = "failure"
	}

	c.metricsManager.RecordBusinessOperation("order_creation", status, tenantID, userID, duration)
}

// RecordProductView 记录产品查看
func (c *BusinessMetricsCollector) RecordProductView(tenantID, userID string, duration time.Duration) {
	c.metricsManager.RecordBusinessOperation("product_view", "success", tenantID, userID, duration)
}

// RecordInventoryUpdate 记录库存更新
func (c *BusinessMetricsCollector) RecordInventoryUpdate(tenantID, userID string, success bool, duration time.Duration) {
	status := "success"
	if !success {
		status = "failure"
	}

	c.metricsManager.RecordBusinessOperation("inventory_update", status, tenantID, userID, duration)
}

// ErrorMetricsCollector 错误指标收集器
type ErrorMetricsCollector struct {
	metricsManager *MetricsManager
	logger         logger.Logger
}

// NewErrorMetricsCollector 创建错误指标收集器
func NewErrorMetricsCollector(metricsManager *MetricsManager, logger logger.Logger) *ErrorMetricsCollector {
	return &ErrorMetricsCollector{
		metricsManager: metricsManager,
		logger:         logger,
	}
}

// RecordError 记录错误
func (c *ErrorMetricsCollector) RecordError(errorType, errorCode, tenantID string, err error) {
	c.metricsManager.RecordError(errorType, errorCode, tenantID)

	c.logger.Error(context.Background(), "Error recorded in metrics",
		"error_type", errorType,
		"error_code", errorCode,
		"tenant_id", tenantID,
		"error", err)
}

// RecordPanic 记录 Panic
func (c *ErrorMetricsCollector) RecordPanic(err interface{}) {
	c.metricsManager.RecordPanic()

	c.logger.Error(context.Background(), "Panic recorded in metrics",
		"panic", err)
}
