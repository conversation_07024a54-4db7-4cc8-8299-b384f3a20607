package monitoring

import (
	"context"
	"fmt"
	"time"

	apperrors "backend/pkg/common/errors"
	errorCodes "backend/pkg/common/errors/codes"

	"go.opentelemetry.io/otel"
	"go.opentelemetry.io/otel/attribute"
	"go.opentelemetry.io/otel/codes"
	"go.opentelemetry.io/otel/exporters/otlp/otlptrace/otlptracegrpc"
	"go.opentelemetry.io/otel/propagation"
	"go.opentelemetry.io/otel/sdk/resource"
	sdktrace "go.opentelemetry.io/otel/sdk/trace"
	semconv "go.opentelemetry.io/otel/semconv/v1.21.0"
	"go.opentelemetry.io/otel/trace"
	"google.golang.org/grpc"
	"google.golang.org/grpc/credentials/insecure"
)

// TracingConfig 追踪配置
type TracingConfig struct {
	Enable      bool   `mapstructure:"enable"`
	Endpoint    string `mapstructure:"endpoint"`
	ServiceName string `mapstructure:"service"`
	Environment string `mapstructure:"environment"`
	Version     string `mapstructure:"version"`
}

// TracingManager 追踪管理器
type TracingManager struct {
	provider trace.TracerProvider
	tracer   trace.Tracer
	config   *TracingConfig
}

// NewTracingManager 创建新的追踪管理器
func NewTracingManager(config *TracingConfig) (*TracingManager, error) {
	if !config.Enable {
		// 如果追踪未启用，使用NoOp provider
		provider := trace.NewNoopTracerProvider()
		tracer := provider.Tracer(config.ServiceName)

		return &TracingManager{
			provider: provider,
			tracer:   tracer,
			config:   config,
		}, nil
	}

	// 创建资源
	res, err := resource.New(context.Background(),
		resource.WithAttributes(
			semconv.ServiceName(config.ServiceName),
			semconv.ServiceVersion(config.Version),
			semconv.DeploymentEnvironment(config.Environment),
		),
	)
	if err != nil {
		return nil, apperrors.NewInternal(errorCodes.SystemError, "创建追踪资源失败").Wrap(err).Build()
	}

	// 创建OTLP exporter
	ctx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
	defer cancel()

	conn, err := grpc.DialContext(ctx, config.Endpoint,
		grpc.WithTransportCredentials(insecure.NewCredentials()),
		grpc.WithBlock(),
	)
	if err != nil {
		return nil, apperrors.NewInternal(errorCodes.SystemError, "创建追踪导出器失败").Wrap(err).Build()
	}

	exporter, err := otlptracegrpc.New(ctx, otlptracegrpc.WithGRPCConn(conn))
	if err != nil {
		return nil, apperrors.NewInternal(errorCodes.SystemError, "创建追踪导出器失败").Wrap(err).Build()
	}

	// 创建trace provider
	provider := sdktrace.NewTracerProvider(
		sdktrace.WithBatcher(exporter),
		sdktrace.WithResource(res),
		sdktrace.WithSampler(sdktrace.AlwaysSample()),
	)

	// 设置全局provider
	otel.SetTracerProvider(provider)

	// 设置全局propagator
	otel.SetTextMapPropagator(propagation.NewCompositeTextMapPropagator(
		propagation.TraceContext{},
		propagation.Baggage{},
	))

	// 创建tracer
	tracer := provider.Tracer(config.ServiceName)

	return &TracingManager{
		provider: provider,
		tracer:   tracer,
		config:   config,
	}, nil
}

// GetTracer 获取tracer实例
func (tm *TracingManager) GetTracer() trace.Tracer {
	return tm.tracer
}

// StartSpan 开始一个新的span
func (tm *TracingManager) StartSpan(ctx context.Context, name string, opts ...trace.SpanStartOption) (context.Context, trace.Span) {
	return tm.tracer.Start(ctx, name, opts...)
}

// StartBusinessSpan 开始业务操作span
func (tm *TracingManager) StartBusinessSpan(ctx context.Context, operation, action string, attrs ...attribute.KeyValue) (context.Context, trace.Span) {
	spanName := fmt.Sprintf("business.%s.%s", operation, action)

	// 添加标准属性
	standardAttrs := []attribute.KeyValue{
		attribute.String("operation.type", "business"),
		attribute.String("operation.name", operation),
		attribute.String("operation.action", action),
	}

	// 从context中提取业务信息
	if userID := ctx.Value("user_id"); userID != nil {
		if str, ok := userID.(string); ok && str != "" {
			standardAttrs = append(standardAttrs, attribute.String("user.id", str))
		}
	}

	if tenantID := ctx.Value("tenant_id"); tenantID != nil {
		if str, ok := tenantID.(string); ok && str != "" {
			standardAttrs = append(standardAttrs, attribute.String("tenant.id", str))
		}
	}

	if sessionID := ctx.Value("session_id"); sessionID != nil {
		if str, ok := sessionID.(string); ok && str != "" {
			standardAttrs = append(standardAttrs, attribute.String("session.id", str))
		}
	}

	// 合并自定义属性
	allAttrs := append(standardAttrs, attrs...)

	return tm.tracer.Start(ctx, spanName, trace.WithAttributes(allAttrs...))
}

// StartDatabaseSpan 开始数据库操作span
func (tm *TracingManager) StartDatabaseSpan(ctx context.Context, operation, table string) (context.Context, trace.Span) {
	spanName := fmt.Sprintf("db.%s.%s", operation, table)

	attrs := []attribute.KeyValue{
		attribute.String("operation.type", "database"),
		attribute.String("db.operation", operation),
		attribute.String("db.table", table),
		attribute.String("db.system", "postgresql"),
	}

	return tm.tracer.Start(ctx, spanName, trace.WithAttributes(attrs...))
}

// StartCacheSpan 开始缓存操作span
func (tm *TracingManager) StartCacheSpan(ctx context.Context, operation, key string) (context.Context, trace.Span) {
	spanName := fmt.Sprintf("cache.%s", operation)

	attrs := []attribute.KeyValue{
		attribute.String("operation.type", "cache"),
		attribute.String("cache.operation", operation),
		attribute.String("cache.key", key),
		attribute.String("cache.system", "redis"),
	}

	return tm.tracer.Start(ctx, spanName, trace.WithAttributes(attrs...))
}

// StartExternalSpan 开始外部调用span
func (tm *TracingManager) StartExternalSpan(ctx context.Context, service, method, url string) (context.Context, trace.Span) {
	spanName := fmt.Sprintf("external.%s", service)

	attrs := []attribute.KeyValue{
		attribute.String("operation.type", "external"),
		attribute.String("external.service", service),
		attribute.String("http.method", method),
		attribute.String("http.url", url),
	}

	return tm.tracer.Start(ctx, spanName, trace.WithAttributes(attrs...))
}

// RecordError 记录span错误
func (tm *TracingManager) RecordError(span trace.Span, err error, attrs ...attribute.KeyValue) {
	if err == nil {
		return
	}

	span.RecordError(err)
	span.SetStatus(codes.Error, err.Error())

	if len(attrs) > 0 {
		span.SetAttributes(attrs...)
	}
}

// SetSpanSuccess 设置span成功状态
func (tm *TracingManager) SetSpanSuccess(span trace.Span, attrs ...attribute.KeyValue) {
	span.SetStatus(codes.Ok, "")

	if len(attrs) > 0 {
		span.SetAttributes(attrs...)
	}
}

// Shutdown 关闭追踪管理器
func (tm *TracingManager) Shutdown(ctx context.Context) error {
	if provider, ok := tm.provider.(*sdktrace.TracerProvider); ok {
		return provider.Shutdown(ctx)
	}
	return nil
}

// IsEnabled 检查追踪是否启用
func (tm *TracingManager) IsEnabled() bool {
	return tm.config.Enable
}
