package monitoring

import (
	"context"
	"runtime"
	"sync"
	"time"

	"backend/pkg/infrastructure/logger"
)

// PerformanceMonitor 性能监控器
type PerformanceMonitor struct {
	logger         logger.Logger
	metricsManager *MetricsManager
	alertManager   *AlertManager
	
	// 监控配置
	config *PerformanceConfig
	
	// 监控状态
	mu       sync.RWMutex
	running  bool
	stopChan chan struct{}
	
	// 性能数据
	lastMetrics *PerformanceMetrics
}

// PerformanceConfig 性能监控配置
type PerformanceConfig struct {
	// 监控间隔
	Interval time.Duration `json:"interval"`
	
	// 阈值配置
	Thresholds *PerformanceThresholds `json:"thresholds"`
	
	// 是否启用告警
	AlertEnabled bool `json:"alert_enabled"`
	
	// 历史数据保留时间
	RetentionPeriod time.Duration `json:"retention_period"`
}

// PerformanceThresholds 性能阈值
type PerformanceThresholds struct {
	// CPU 使用率阈值 (%)
	CPUUsageWarning  float64 `json:"cpu_usage_warning"`
	CPUUsageCritical float64 `json:"cpu_usage_critical"`
	
	// 内存使用率阈值 (%)
	MemoryUsageWarning  float64 `json:"memory_usage_warning"`
	MemoryUsageCritical float64 `json:"memory_usage_critical"`
	
	// Goroutine 数量阈值
	GoroutineWarning  int `json:"goroutine_warning"`
	GoroutineCritical int `json:"goroutine_critical"`
	
	// 响应时间阈值 (ms)
	ResponseTimeWarning  float64 `json:"response_time_warning"`
	ResponseTimeCritical float64 `json:"response_time_critical"`
	
	// 错误率阈值 (%)
	ErrorRateWarning  float64 `json:"error_rate_warning"`
	ErrorRateCritical float64 `json:"error_rate_critical"`
	
	// 数据库连接池阈值
	DBConnectionWarning  int `json:"db_connection_warning"`
	DBConnectionCritical int `json:"db_connection_critical"`
}

// PerformanceMetrics 性能指标
type PerformanceMetrics struct {
	Timestamp time.Time `json:"timestamp"`
	
	// 系统指标
	CPUUsage    float64 `json:"cpu_usage"`
	MemoryUsage struct {
		Alloc      uint64  `json:"alloc"`
		TotalAlloc uint64  `json:"total_alloc"`
		Sys        uint64  `json:"sys"`
		UsageRate  float64 `json:"usage_rate"`
	} `json:"memory_usage"`
	
	// Go 运行时指标
	Goroutines   int `json:"goroutines"`
	GCRuns       uint32 `json:"gc_runs"`
	GCPauseTotal time.Duration `json:"gc_pause_total"`
	
	// 应用指标
	HTTPRequests struct {
		Total       int64   `json:"total"`
		ErrorRate   float64 `json:"error_rate"`
		AvgResponse float64 `json:"avg_response"`
		P95Response float64 `json:"p95_response"`
		P99Response float64 `json:"p99_response"`
	} `json:"http_requests"`
	
	// 数据库指标
	Database struct {
		ActiveConnections int     `json:"active_connections"`
		IdleConnections   int     `json:"idle_connections"`
		TotalConnections  int     `json:"total_connections"`
		AvgQueryTime      float64 `json:"avg_query_time"`
		SlowQueries       int64   `json:"slow_queries"`
	} `json:"database"`
	
	// 缓存指标
	Cache struct {
		HitRate     float64 `json:"hit_rate"`
		MissRate    float64 `json:"miss_rate"`
		Operations  int64   `json:"operations"`
		AvgLatency  float64 `json:"avg_latency"`
	} `json:"cache"`
}

// NewPerformanceMonitor 创建性能监控器
func NewPerformanceMonitor(logger logger.Logger, metricsManager *MetricsManager, alertManager *AlertManager) *PerformanceMonitor {
	return &PerformanceMonitor{
		logger:         logger,
		metricsManager: metricsManager,
		alertManager:   alertManager,
		config:         getDefaultPerformanceConfig(),
		stopChan:       make(chan struct{}),
	}
}

// getDefaultPerformanceConfig 获取默认性能监控配置
func getDefaultPerformanceConfig() *PerformanceConfig {
	return &PerformanceConfig{
		Interval:        30 * time.Second,
		AlertEnabled:    true,
		RetentionPeriod: 24 * time.Hour,
		Thresholds: &PerformanceThresholds{
			CPUUsageWarning:      70.0,
			CPUUsageCritical:     90.0,
			MemoryUsageWarning:   80.0,
			MemoryUsageCritical:  95.0,
			GoroutineWarning:     1000,
			GoroutineCritical:    5000,
			ResponseTimeWarning:  1000.0, // 1s
			ResponseTimeCritical: 5000.0, // 5s
			ErrorRateWarning:     5.0,    // 5%
			ErrorRateCritical:    10.0,   // 10%
			DBConnectionWarning:  80,     // 80% of pool
			DBConnectionCritical: 95,     // 95% of pool
		},
	}
}

// Start 开始性能监控
func (pm *PerformanceMonitor) Start(ctx context.Context) error {
	pm.mu.Lock()
	defer pm.mu.Unlock()
	
	if pm.running {
		return nil
	}
	
	pm.running = true
	pm.logger.Info(ctx, "Performance monitor started", 
		"interval", pm.config.Interval,
		"alert_enabled", pm.config.AlertEnabled)
	
	go pm.monitorLoop(ctx)
	
	return nil
}

// Stop 停止性能监控
func (pm *PerformanceMonitor) Stop() {
	pm.mu.Lock()
	defer pm.mu.Unlock()
	
	if !pm.running {
		return
	}
	
	pm.running = false
	close(pm.stopChan)
	pm.logger.Info(context.Background(), "Performance monitor stopped")
}

// monitorLoop 监控循环
func (pm *PerformanceMonitor) monitorLoop(ctx context.Context) {
	ticker := time.NewTicker(pm.config.Interval)
	defer ticker.Stop()
	
	for {
		select {
		case <-ticker.C:
			pm.collectAndAnalyzeMetrics(ctx)
		case <-pm.stopChan:
			return
		case <-ctx.Done():
			return
		}
	}
}

// collectAndAnalyzeMetrics 收集和分析指标
func (pm *PerformanceMonitor) collectAndAnalyzeMetrics(ctx context.Context) {
	metrics := pm.collectMetrics()
	
	// 更新 Prometheus 指标
	pm.updatePrometheusMetrics(metrics)
	
	// 检查阈值并触发告警
	if pm.config.AlertEnabled {
		pm.checkThresholds(ctx, metrics)
	}
	
	// 保存历史数据
	pm.mu.Lock()
	pm.lastMetrics = metrics
	pm.mu.Unlock()
	
	pm.logger.Debug(ctx, "Performance metrics collected",
		"cpu_usage", metrics.CPUUsage,
		"memory_usage_rate", metrics.MemoryUsage.UsageRate,
		"goroutines", metrics.Goroutines,
		"db_active_connections", metrics.Database.ActiveConnections)
}

// collectMetrics 收集性能指标
func (pm *PerformanceMonitor) collectMetrics() *PerformanceMetrics {
	var m runtime.MemStats
	runtime.ReadMemStats(&m)
	
	metrics := &PerformanceMetrics{
		Timestamp:  time.Now(),
		Goroutines: runtime.NumGoroutine(),
		GCRuns:     m.NumGC,
	}
	
	// 内存指标
	metrics.MemoryUsage.Alloc = m.Alloc
	metrics.MemoryUsage.TotalAlloc = m.TotalAlloc
	metrics.MemoryUsage.Sys = m.Sys
	metrics.MemoryUsage.UsageRate = float64(m.Alloc) / float64(m.Sys) * 100
	
	// GC 暂停时间
	if m.NumGC > 0 {
		metrics.GCPauseTotal = time.Duration(m.PauseTotalNs)
	}
	
	// TODO: 实现 CPU 使用率计算
	// TODO: 实现 HTTP 请求指标收集
	// TODO: 实现数据库指标收集
	// TODO: 实现缓存指标收集
	
	return metrics
}

// updatePrometheusMetrics 更新 Prometheus 指标
func (pm *PerformanceMonitor) updatePrometheusMetrics(metrics *PerformanceMetrics) {
	if pm.metricsManager == nil {
		return
	}
	
	pm.metricsManager.UpdateSystemMetrics(
		metrics.Goroutines,
		metrics.MemoryUsage.Alloc,
		metrics.CPUUsage,
	)
}

// checkThresholds 检查阈值
func (pm *PerformanceMonitor) checkThresholds(ctx context.Context, metrics *PerformanceMetrics) {
	thresholds := pm.config.Thresholds
	
	// 检查 CPU 使用率
	if metrics.CPUUsage >= thresholds.CPUUsageCritical {
		pm.triggerAlert(ctx, "cpu_usage", "critical", 
			map[string]interface{}{
				"current": metrics.CPUUsage,
				"threshold": thresholds.CPUUsageCritical,
			})
	} else if metrics.CPUUsage >= thresholds.CPUUsageWarning {
		pm.triggerAlert(ctx, "cpu_usage", "warning",
			map[string]interface{}{
				"current": metrics.CPUUsage,
				"threshold": thresholds.CPUUsageWarning,
			})
	}
	
	// 检查内存使用率
	if metrics.MemoryUsage.UsageRate >= thresholds.MemoryUsageCritical {
		pm.triggerAlert(ctx, "memory_usage", "critical",
			map[string]interface{}{
				"current": metrics.MemoryUsage.UsageRate,
				"threshold": thresholds.MemoryUsageCritical,
				"alloc_bytes": metrics.MemoryUsage.Alloc,
			})
	} else if metrics.MemoryUsage.UsageRate >= thresholds.MemoryUsageWarning {
		pm.triggerAlert(ctx, "memory_usage", "warning",
			map[string]interface{}{
				"current": metrics.MemoryUsage.UsageRate,
				"threshold": thresholds.MemoryUsageWarning,
				"alloc_bytes": metrics.MemoryUsage.Alloc,
			})
	}
	
	// 检查 Goroutine 数量
	if metrics.Goroutines >= thresholds.GoroutineCritical {
		pm.triggerAlert(ctx, "goroutine_count", "critical",
			map[string]interface{}{
				"current": metrics.Goroutines,
				"threshold": thresholds.GoroutineCritical,
			})
	} else if metrics.Goroutines >= thresholds.GoroutineWarning {
		pm.triggerAlert(ctx, "goroutine_count", "warning",
			map[string]interface{}{
				"current": metrics.Goroutines,
				"threshold": thresholds.GoroutineWarning,
			})
	}
	
	// 检查数据库连接
	if metrics.Database.ActiveConnections >= thresholds.DBConnectionCritical {
		pm.triggerAlert(ctx, "db_connections", "critical",
			map[string]interface{}{
				"active": metrics.Database.ActiveConnections,
				"total": metrics.Database.TotalConnections,
				"threshold": thresholds.DBConnectionCritical,
			})
	} else if metrics.Database.ActiveConnections >= thresholds.DBConnectionWarning {
		pm.triggerAlert(ctx, "db_connections", "warning",
			map[string]interface{}{
				"active": metrics.Database.ActiveConnections,
				"total": metrics.Database.TotalConnections,
				"threshold": thresholds.DBConnectionWarning,
			})
	}
}

// triggerAlert 触发告警
func (pm *PerformanceMonitor) triggerAlert(ctx context.Context, alertType, severity string, data map[string]interface{}) {
	if pm.alertManager != nil {
		pm.alertManager.TriggerAlert(ctx, alertType, severity, data)
	}
	
	pm.logger.Warn(ctx, "Performance threshold exceeded",
		"alert_type", alertType,
		"severity", severity,
		"data", data)
}

// GetCurrentMetrics 获取当前性能指标
func (pm *PerformanceMonitor) GetCurrentMetrics() *PerformanceMetrics {
	pm.mu.RLock()
	defer pm.mu.RUnlock()
	
	if pm.lastMetrics == nil {
		return pm.collectMetrics()
	}
	
	return pm.lastMetrics
}

// UpdateConfig 更新配置
func (pm *PerformanceMonitor) UpdateConfig(config *PerformanceConfig) {
	pm.mu.Lock()
	defer pm.mu.Unlock()
	
	pm.config = config
	pm.logger.Info(context.Background(), "Performance monitor configuration updated",
		"interval", config.Interval,
		"alert_enabled", config.AlertEnabled)
}
