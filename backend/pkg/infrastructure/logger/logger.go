package logger

import "context"

// Logger 定义统一的日志接口
type Logger interface {
	// With 创建一个带有额外字段的新logger实例
	With(fields ...any) Logger

	// 上下文日志方法 - 从context中提取链路信息
	Debug(ctx context.Context, msg string, fields ...any)
	Info(ctx context.Context, msg string, fields ...any)
	Warn(ctx context.Context, msg string, fields ...any)
	Error(ctx context.Context, msg string, fields ...any)

	// 非上下文日志方法 - 用于系统启动等场景
	DebugNoCtx(msg string, fields ...any)
	InfoNoCtx(msg string, fields ...any)
	WarnNoCtx(msg string, fields ...any)
	ErrorNoCtx(msg string, fields ...any)
}

// Field 表示日志字段
type Field struct {
	Key   string
	Value any
}

// String 创建字符串字段
func String(key, value string) Field {
	return Field{Key: key, Value: value}
}

// Int 创建整数字段
func Int(key string, value int) Field {
	return Field{Key: key, Value: value}
}

// Int64 创建64位整数字段
func Int64(key string, value int64) Field {
	return Field{Key: key, Value: value}
}

// Error 创建错误字段
func Error(err error) Field {
	return Field{Key: "error", Value: err}
}

// Any 创建任意类型字段
func Any(key string, value any) Field {
	return Field{Key: key, Value: value}
}
