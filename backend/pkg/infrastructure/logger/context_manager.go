package logger

import (
	"context"
	"fmt"
	"runtime"
	"strings"
	"time"
)

// ContextManager 上下文管理器
type ContextManager struct {
	logger Logger
}

// NewContextManager 创建上下文管理器
func NewContextManager(logger Logger) *ContextManager {
	return &ContextManager{
		logger: logger,
	}
}

// WithBusinessContext 添加业务上下文
func (cm *ContextManager) WithBusinessContext(ctx context.Context, userID, tenantID, sessionID string) context.Context {
	ctx = context.WithValue(ctx, contextKeyUserID, userID)
	ctx = context.WithValue(ctx, contextKeyTenantID, tenantID)
	ctx = context.WithValue(ctx, contextKeySessionID, sessionID)
	return ctx
}

// WithComponent 添加组件信息
func (cm *ContextManager) WithComponent(ctx context.Context, component, layer string) context.Context {
	ctx = context.WithValue(ctx, contextKeyComponent, component)
	ctx = context.WithValue(ctx, contextKeyLayer, layer)
	return ctx
}

// WithOperation 添加操作信息
func (cm *ContextManager) WithOperation(ctx context.Context, operation, action string) context.Context {
	ctx = context.WithValue(ctx, contextKeyOperation, operation)
	ctx = context.WithValue(ctx, contextKeyAction, action)
	return ctx
}

// LogWithStandardContext 使用标准上下文记录日志
func (cm *ContextManager) LogWithStandardContext(ctx context.Context, level LogLevel, message string, additionalFields ...interface{}) {
	// 提取标准上下文
	sc := ExtractStandardContext(ctx)

	// 添加调用者信息
	caller := cm.getCaller(2)

	// 合并字段
	fields := sc.ToFields()
	fields = append(fields, "caller", caller)
	fields = append(fields, additionalFields...)

	// 记录日志
	switch level {
	case LevelDebug:
		cm.logger.Debug(ctx, message, fields...)
	case LevelInfo:
		cm.logger.Info(ctx, message, fields...)
	case LevelWarn:
		cm.logger.Warn(ctx, message, fields...)
	case LevelError:
		cm.logger.Error(ctx, message, fields...)
	}
}

// LogBusinessOperation 记录业务操作
func (cm *ContextManager) LogBusinessOperation(ctx context.Context, op BusinessOperationLog) {
	sc := ExtractStandardContext(ctx)
	fields := sc.ToFields()

	fields = append(fields,
		FieldOperation, op.Operation,
		FieldAction, op.Action,
		FieldStatus, op.Status,
		FieldDuration, op.Duration.String(),
		"start_time", op.StartTime.Format(time.RFC3339),
		"end_time", op.EndTime.Format(time.RFC3339),
	)

	if op.EntityType != "" {
		fields = append(fields, FieldEntityType, op.EntityType)
	}
	if op.EntityID != "" {
		fields = append(fields, FieldEntityID, op.EntityID)
	}
	if op.Details != nil {
		fields = append(fields, "details", op.Details)
	}
	if op.Error != "" {
		fields = append(fields, FieldError, op.Error)
	}
	if op.ErrorCode != "" {
		fields = append(fields, FieldErrorCode, op.ErrorCode)
	}

	message := fmt.Sprintf("Business operation: %s.%s [%s]", op.Operation, op.Action, op.Status)

	if op.Error != "" {
		cm.logger.Error(ctx, message, fields...)
	} else {
		cm.logger.Info(ctx, message, fields...)
	}
}

// LogDatabaseOperation 记录数据库操作
func (cm *ContextManager) LogDatabaseOperation(ctx context.Context, op DatabaseOperationLog) {
	sc := ExtractStandardContext(ctx)
	fields := sc.ToFields()

	fields = append(fields,
		FieldDBOperation, op.Operation,
		FieldDBTable, op.Table,
		FieldDBDuration, op.Duration.String(),
		FieldDBRowsAffected, op.RowsAffected,
		"start_time", op.StartTime.Format(time.RFC3339),
		"end_time", op.EndTime.Format(time.RFC3339),
	)

	if op.Query != "" {
		// 截断过长的查询语句
		query := op.Query
		if len(query) > 500 {
			query = query[:500] + "..."
		}
		fields = append(fields, FieldDBQuery, query)
	}

	if op.Error != "" {
		fields = append(fields, FieldError, op.Error)
	}

	message := fmt.Sprintf("Database operation: %s on %s (%d rows, %v)",
		op.Operation, op.Table, op.RowsAffected, op.Duration)

	if op.Error != "" {
		cm.logger.Error(ctx, message, fields...)
	} else if op.Duration > 100*time.Millisecond {
		// 慢查询警告
		cm.logger.Warn(ctx, "Slow database operation: "+message, fields...)
	} else {
		cm.logger.Debug(ctx, message, fields...)
	}
}

// LogCacheOperation 记录缓存操作
func (cm *ContextManager) LogCacheOperation(ctx context.Context, op CacheOperationLog) {
	sc := ExtractStandardContext(ctx)
	fields := sc.ToFields()

	fields = append(fields,
		FieldCacheOperation, op.Operation,
		FieldCacheKey, op.Key,
		FieldCacheHit, op.Hit,
		FieldDuration, op.Duration.String(),
		"start_time", op.StartTime.Format(time.RFC3339),
		"end_time", op.EndTime.Format(time.RFC3339),
	)

	if op.Size > 0 {
		fields = append(fields, FieldCacheSize, op.Size)
	}
	if op.TTL > 0 {
		fields = append(fields, FieldCacheTTL, op.TTL.String())
	}
	if op.Error != "" {
		fields = append(fields, FieldError, op.Error)
	}

	hitStatus := "miss"
	if op.Hit {
		hitStatus = "hit"
	}

	message := fmt.Sprintf("Cache operation: %s %s [%s]", op.Operation, op.Key, hitStatus)

	if op.Error != "" {
		cm.logger.Error(ctx, message, fields...)
	} else {
		cm.logger.Debug(ctx, message, fields...)
	}
}

// LogExternalCall 记录外部调用
func (cm *ContextManager) LogExternalCall(ctx context.Context, op ExternalCallLog) {
	sc := ExtractStandardContext(ctx)
	fields := sc.ToFields()

	fields = append(fields,
		FieldExternalService, op.Service,
		FieldExternalURL, op.URL,
		FieldExternalMethod, op.Method,
		FieldExternalStatus, op.StatusCode,
		FieldExternalDuration, op.Duration.String(),
		"start_time", op.StartTime.Format(time.RFC3339),
		"end_time", op.EndTime.Format(time.RFC3339),
	)

	if op.RequestSize > 0 {
		fields = append(fields, "request_size", op.RequestSize)
	}
	if op.ResponseSize > 0 {
		fields = append(fields, "response_size", op.ResponseSize)
	}
	if op.Error != "" {
		fields = append(fields, FieldError, op.Error)
	}

	message := fmt.Sprintf("External call: %s %s %s [%d] (%v)",
		op.Method, op.Service, op.URL, op.StatusCode, op.Duration)

	if op.Error != "" || op.StatusCode >= 400 {
		cm.logger.Error(ctx, message, fields...)
	} else if op.Duration > 5*time.Second {
		// 慢调用警告
		cm.logger.Warn(ctx, "Slow external call: "+message, fields...)
	} else {
		cm.logger.Info(ctx, message, fields...)
	}
}

// LogSecurityEvent 记录安全事件
func (cm *ContextManager) LogSecurityEvent(ctx context.Context, op SecurityEventLog) {
	sc := ExtractStandardContext(ctx)
	fields := sc.ToFields()

	fields = append(fields,
		"event_type", op.EventType,
		FieldAction, op.Action,
		"result", op.Result,
		"risk_level", op.RiskLevel,
		"timestamp", op.Timestamp.Format(time.RFC3339),
	)

	if op.ClientIP != "" {
		fields = append(fields, FieldClientIP, op.ClientIP)
	}
	if op.UserAgent != "" {
		fields = append(fields, FieldUserAgent, op.UserAgent)
	}
	if op.Details != nil {
		fields = append(fields, "details", op.Details)
	}

	message := fmt.Sprintf("Security event: %s.%s [%s] - %s",
		op.EventType, op.Action, op.Result, op.RiskLevel)

	switch op.RiskLevel {
	case "high", "critical":
		cm.logger.Error(ctx, message, fields...)
	case "medium":
		cm.logger.Warn(ctx, message, fields...)
	default:
		cm.logger.Info(ctx, message, fields...)
	}
}

// getCaller 获取调用者信息
func (cm *ContextManager) getCaller(skip int) string {
	_, file, line, ok := runtime.Caller(skip)
	if !ok {
		return "unknown"
	}

	// 简化文件路径
	if idx := strings.LastIndex(file, "/"); idx >= 0 {
		file = file[idx+1:]
	}

	return fmt.Sprintf("%s:%d", file, line)
}

// TrackOperation 追踪操作执行时间
func (cm *ContextManager) TrackOperation(ctx context.Context, operation, action string, fn func() error) error {
	start := time.Now()

	// 添加操作上下文
	ctx = cm.WithOperation(ctx, operation, action)

	// 记录开始
	cm.LogWithStandardContext(ctx, LevelDebug,
		fmt.Sprintf("Starting operation: %s.%s", operation, action))

	// 执行操作
	err := fn()

	// 记录结果
	duration := time.Since(start)
	status := "success"
	if err != nil {
		status = "failure"
	}

	op := BusinessOperationLog{
		Operation: operation,
		Action:    action,
		Status:    status,
		Duration:  duration,
		StartTime: start,
		EndTime:   time.Now(),
	}

	if err != nil {
		op.Error = err.Error()
	}

	cm.LogBusinessOperation(ctx, op)

	return err
}
