package logger

import (
	"context"
	"fmt"
	"runtime"
	"strings"
	"time"
)

// StructuredLogger 结构化日志器
type StructuredLogger struct {
	logger         Logger
	contextManager *ContextManager
}

// NewStructuredLogger 创建结构化日志器
func NewStructuredLogger(logger Logger) *StructuredLogger {
	return &StructuredLogger{
		logger:         logger,
		contextManager: NewContextManager(logger),
	}
}

// GetContextManager 获取上下文管理器
func (sl *StructuredLogger) GetContextManager() *ContextManager {
	return sl.contextManager
}

// LogLevel 日志级别
type LogLevel string

const (
	LevelDebug LogLevel = "debug"
	LevelInfo  LogLevel = "info"
	LevelWarn  LogLevel = "warn"
	LevelError LogLevel = "error"
)

// LogEntry 日志条目
type LogEntry struct {
	Level      LogLevel               `json:"level"`
	Message    string                 `json:"message"`
	Timestamp  time.Time              `json:"timestamp"`
	RequestID  string                 `json:"request_id,omitempty"`
	TraceID    string                 `json:"trace_id,omitempty"`
	SpanID     string                 `json:"span_id,omitempty"`
	TenantID   string                 `json:"tenant_id,omitempty"`
	UserID     string                 `json:"user_id,omitempty"`
	Operation  string                 `json:"operation,omitempty"`
	Component  string                 `json:"component,omitempty"`
	Duration   *time.Duration         `json:"duration,omitempty"`
	Error      string                 `json:"error,omitempty"`
	StackTrace string                 `json:"stack_trace,omitempty"`
	Fields     map[string]interface{} `json:"fields,omitempty"`
	Caller     string                 `json:"caller,omitempty"`
}

// BusinessOperation 业务操作日志
type BusinessOperation struct {
	Operation  string                 `json:"operation"`
	TenantID   string                 `json:"tenant_id"`
	UserID     string                 `json:"user_id"`
	EntityType string                 `json:"entity_type,omitempty"`
	EntityID   string                 `json:"entity_id,omitempty"`
	Action     string                 `json:"action"`
	Status     string                 `json:"status"`
	Duration   time.Duration          `json:"duration"`
	Details    map[string]interface{} `json:"details,omitempty"`
	Error      string                 `json:"error,omitempty"`
	RequestID  string                 `json:"request_id,omitempty"`
	TraceID    string                 `json:"trace_id,omitempty"`
}

// DatabaseOperation 数据库操作日志
type DatabaseOperation struct {
	Operation    string        `json:"operation"`
	Table        string        `json:"table"`
	Query        string        `json:"query,omitempty"`
	Duration     time.Duration `json:"duration"`
	RowsAffected int64         `json:"rows_affected,omitempty"`
	Error        string        `json:"error,omitempty"`
	RequestID    string        `json:"request_id,omitempty"`
	TenantID     string        `json:"tenant_id,omitempty"`
}

// CacheOperation 缓存操作日志
type CacheOperation struct {
	Operation string        `json:"operation"`
	Key       string        `json:"key"`
	Hit       bool          `json:"hit"`
	Duration  time.Duration `json:"duration"`
	Size      int           `json:"size,omitempty"`
	TTL       time.Duration `json:"ttl,omitempty"`
	Error     string        `json:"error,omitempty"`
	RequestID string        `json:"request_id,omitempty"`
}

// ExternalCall 外部调用日志
type ExternalCall struct {
	Service      string        `json:"service"`
	Endpoint     string        `json:"endpoint"`
	Method       string        `json:"method"`
	Duration     time.Duration `json:"duration"`
	StatusCode   int           `json:"status_code"`
	RequestSize  int64         `json:"request_size,omitempty"`
	ResponseSize int64         `json:"response_size,omitempty"`
	Error        string        `json:"error,omitempty"`
	RequestID    string        `json:"request_id,omitempty"`
	RetryCount   int           `json:"retry_count,omitempty"`
}

// SecurityEvent 安全事件日志
type SecurityEvent struct {
	Event     string                 `json:"event"`
	UserID    string                 `json:"user_id,omitempty"`
	TenantID  string                 `json:"tenant_id,omitempty"`
	IPAddress string                 `json:"ip_address"`
	UserAgent string                 `json:"user_agent,omitempty"`
	Resource  string                 `json:"resource,omitempty"`
	Action    string                 `json:"action,omitempty"`
	Result    string                 `json:"result"`
	Reason    string                 `json:"reason,omitempty"`
	Details   map[string]interface{} `json:"details,omitempty"`
	RequestID string                 `json:"request_id,omitempty"`
	Severity  string                 `json:"severity"`
}

// LogBusinessOperation 记录业务操作
func (sl *StructuredLogger) LogBusinessOperation(ctx context.Context, op BusinessOperation) {
	fields := []interface{}{
		"operation_type", "business",
		"operation", op.Operation,
		"tenant_id", op.TenantID,
		"user_id", op.UserID,
		"action", op.Action,
		"status", op.Status,
		"duration", op.Duration.String(),
	}

	if op.EntityType != "" {
		fields = append(fields, "entity_type", op.EntityType)
	}
	if op.EntityID != "" {
		fields = append(fields, "entity_id", op.EntityID)
	}
	if op.Details != nil {
		fields = append(fields, "details", op.Details)
	}
	if op.RequestID != "" {
		fields = append(fields, "request_id", op.RequestID)
	}
	if op.TraceID != "" {
		fields = append(fields, "trace_id", op.TraceID)
	}

	message := fmt.Sprintf("Business operation: %s %s", op.Operation, op.Status)

	if op.Error != "" {
		fields = append(fields, "error", op.Error)
		sl.logger.Error(ctx, message, fields...)
	} else {
		sl.logger.Info(ctx, message, fields...)
	}
}

// LogDatabaseOperation 记录数据库操作
func (sl *StructuredLogger) LogDatabaseOperation(ctx context.Context, op DatabaseOperation) {
	fields := []interface{}{
		"operation_type", "database",
		"db_operation", op.Operation,
		"table", op.Table,
		"duration", op.Duration.String(),
	}

	if op.Query != "" {
		fields = append(fields, "query", op.Query)
	}
	if op.RowsAffected > 0 {
		fields = append(fields, "rows_affected", op.RowsAffected)
	}
	if op.RequestID != "" {
		fields = append(fields, "request_id", op.RequestID)
	}
	if op.TenantID != "" {
		fields = append(fields, "tenant_id", op.TenantID)
	}

	message := fmt.Sprintf("Database operation: %s on %s", op.Operation, op.Table)

	if op.Error != "" {
		fields = append(fields, "error", op.Error)
		sl.logger.Error(ctx, message, fields...)
	} else {
		sl.logger.Debug(ctx, message, fields...)
	}
}

// LogCacheOperation 记录缓存操作
func (sl *StructuredLogger) LogCacheOperation(ctx context.Context, op CacheOperation) {
	fields := []interface{}{
		"operation_type", "cache",
		"cache_operation", op.Operation,
		"cache_key", op.Key,
		"cache_hit", op.Hit,
		"duration", op.Duration.String(),
	}

	if op.Size > 0 {
		fields = append(fields, "size", op.Size)
	}
	if op.TTL > 0 {
		fields = append(fields, "ttl", op.TTL.String())
	}
	if op.RequestID != "" {
		fields = append(fields, "request_id", op.RequestID)
	}

	message := fmt.Sprintf("Cache operation: %s %s", op.Operation, op.Key)

	if op.Error != "" {
		fields = append(fields, "error", op.Error)
		sl.logger.Warn(ctx, message, fields...)
	} else {
		sl.logger.Debug(ctx, message, fields...)
	}
}

// LogExternalCall 记录外部调用
func (sl *StructuredLogger) LogExternalCall(ctx context.Context, call ExternalCall) {
	fields := []interface{}{
		"operation_type", "external_call",
		"service", call.Service,
		"endpoint", call.Endpoint,
		"method", call.Method,
		"duration", call.Duration.String(),
		"status_code", call.StatusCode,
	}

	if call.RequestSize > 0 {
		fields = append(fields, "request_size", call.RequestSize)
	}
	if call.ResponseSize > 0 {
		fields = append(fields, "response_size", call.ResponseSize)
	}
	if call.RequestID != "" {
		fields = append(fields, "request_id", call.RequestID)
	}
	if call.RetryCount > 0 {
		fields = append(fields, "retry_count", call.RetryCount)
	}

	message := fmt.Sprintf("External call: %s %s %s", call.Method, call.Service, call.Endpoint)

	if call.Error != "" {
		fields = append(fields, "error", call.Error)
		sl.logger.Error(ctx, message, fields...)
	} else {
		sl.logger.Info(ctx, message, fields...)
	}
}

// LogSecurityEvent 记录安全事件
func (sl *StructuredLogger) LogSecurityEvent(ctx context.Context, event SecurityEvent) {
	fields := []interface{}{
		"operation_type", "security",
		"security_event", event.Event,
		"ip_address", event.IPAddress,
		"result", event.Result,
		"severity", event.Severity,
	}

	if event.UserID != "" {
		fields = append(fields, "user_id", event.UserID)
	}
	if event.TenantID != "" {
		fields = append(fields, "tenant_id", event.TenantID)
	}
	if event.UserAgent != "" {
		fields = append(fields, "user_agent", event.UserAgent)
	}
	if event.Resource != "" {
		fields = append(fields, "resource", event.Resource)
	}
	if event.Action != "" {
		fields = append(fields, "action", event.Action)
	}
	if event.Reason != "" {
		fields = append(fields, "reason", event.Reason)
	}
	if event.Details != nil {
		fields = append(fields, "details", event.Details)
	}
	if event.RequestID != "" {
		fields = append(fields, "request_id", event.RequestID)
	}

	message := fmt.Sprintf("Security event: %s - %s", event.Event, event.Result)

	switch event.Severity {
	case "critical", "high":
		sl.logger.Error(ctx, message, fields...)
	case "medium":
		sl.logger.Warn(ctx, message, fields...)
	default:
		sl.logger.Info(ctx, message, fields...)
	}
}

// WithCaller 添加调用者信息
func (sl *StructuredLogger) WithCaller(skip int) *StructuredLogger {
	_, file, line, ok := runtime.Caller(skip + 1)
	if !ok {
		return sl
	}

	// 简化文件路径
	if idx := strings.LastIndex(file, "/"); idx >= 0 {
		file = file[idx+1:]
	}

	caller := fmt.Sprintf("%s:%d", file, line)

	// 创建带有调用者信息的新logger
	newLogger := sl.logger.With("caller", caller)
	return &StructuredLogger{logger: newLogger}
}

// LogWithFields 使用字段记录日志
func (sl *StructuredLogger) LogWithFields(ctx context.Context, level LogLevel, message string, fields map[string]interface{}) {
	logFields := make([]interface{}, 0, len(fields)*2)
	for key, value := range fields {
		logFields = append(logFields, key, value)
	}

	switch level {
	case LevelDebug:
		sl.logger.Debug(ctx, message, logFields...)
	case LevelInfo:
		sl.logger.Info(ctx, message, logFields...)
	case LevelWarn:
		sl.logger.Warn(ctx, message, logFields...)
	case LevelError:
		sl.logger.Error(ctx, message, logFields...)
	}
}
