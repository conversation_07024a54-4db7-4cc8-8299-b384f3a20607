package logger

import (
	"context"
	"fmt"
	"os"

	"go.opentelemetry.io/otel/trace"
	"go.uber.org/zap"
	"go.uber.org/zap/zapcore"
	"gopkg.in/natefinch/lumberjack.v2"
)

// ZapLogger 基于zap的Logger实现
type ZapLogger struct {
	logger *zap.Logger
}

// GetZapLogger 获取底层的zap.Logger实例
func (l *ZapLogger) GetZapLogger() *zap.Logger {
	return l.logger
}

// Config 日志配置
type Config struct {
	Level      string `mapstructure:"level"`
	Format     string `mapstructure:"format"`
	Output     string `mapstructure:"output"`
	File       string `mapstructure:"file"`
	MaxSize    int    `mapstructure:"max_size"`
	MaxAge     int    `mapstructure:"max_age"`
	MaxBackups int    `mapstructure:"max_backups"`
	Compress   bool   `mapstructure:"compress"`
}

// NewZapLogger 创建新的ZapLogger实例
func NewZapLogger(config *Config) (Logger, error) {
	// 解析日志级别
	level, err := zapcore.ParseLevel(config.Level)
	if err != nil {
		return nil, fmt.Errorf("invalid log level: %w", err)
	}

	// 创建编码器配置
	encoderConfig := zapcore.EncoderConfig{
		TimeKey:        "timestamp",
		LevelKey:       "level",
		NameKey:        "logger",
		CallerKey:      "caller",
		FunctionKey:    zapcore.OmitKey,
		MessageKey:     "message",
		StacktraceKey:  "stacktrace",
		LineEnding:     zapcore.DefaultLineEnding,
		EncodeLevel:    zapcore.LowercaseLevelEncoder,
		EncodeTime:     zapcore.ISO8601TimeEncoder,
		EncodeDuration: zapcore.SecondsDurationEncoder,
		EncodeCaller:   zapcore.ShortCallerEncoder,
	}

	// 选择编码器
	var encoder zapcore.Encoder
	if config.Format == "json" {
		encoder = zapcore.NewJSONEncoder(encoderConfig)
	} else {
		encoder = zapcore.NewConsoleEncoder(encoderConfig)
	}

	// 创建写入器
	var writeSyncer zapcore.WriteSyncer
	switch config.Output {
	case "stdout":
		writeSyncer = zapcore.AddSync(os.Stdout)
	case "stderr":
		writeSyncer = zapcore.AddSync(os.Stderr)
	case "file":
		if config.File == "" {
			return nil, fmt.Errorf("file path is required when output is file")
		}
		writeSyncer = zapcore.AddSync(&lumberjack.Logger{
			Filename:   config.File,
			MaxSize:    config.MaxSize,
			MaxAge:     config.MaxAge,
			MaxBackups: config.MaxBackups,
			Compress:   config.Compress,
		})
	default:
		writeSyncer = zapcore.AddSync(os.Stdout)
	}

	// 创建核心
	core := zapcore.NewCore(encoder, writeSyncer, level)

	// 创建logger
	logger := zap.New(core, zap.AddCaller(), zap.AddCallerSkip(1), zap.AddStacktrace(zapcore.ErrorLevel))

	return &ZapLogger{logger: logger}, nil
}

// With 创建带有额外字段的新logger实例
func (l *ZapLogger) With(fields ...any) Logger {
	zapFields := l.convertFields(fields...)
	return &ZapLogger{logger: l.logger.With(zapFields...)}
}

// Info 记录Info级别日志
func (l *ZapLogger) Info(ctx context.Context, msg string, fields ...any) {
	zapFields := l.addTraceFields(ctx, fields...)
	l.logger.Info(msg, zapFields...)
}

// Warn 记录Warn级别日志
func (l *ZapLogger) Warn(ctx context.Context, msg string, fields ...any) {
	zapFields := l.addTraceFields(ctx, fields...)
	l.logger.Warn(msg, zapFields...)
}

// Error 记录Error级别日志
func (l *ZapLogger) Error(ctx context.Context, msg string, fields ...any) {
	zapFields := l.addTraceFields(ctx, fields...)
	l.logger.Error(msg, zapFields...)
}

// Debug 记录Debug级别日志
func (l *ZapLogger) Debug(ctx context.Context, msg string, fields ...any) {
	zapFields := l.addTraceFields(ctx, fields...)
	l.logger.Debug(msg, zapFields...)
}

// InfoNoCtx 记录Info级别日志（无上下文）
func (l *ZapLogger) InfoNoCtx(msg string, fields ...any) {
	zapFields := l.convertFields(fields...)
	l.logger.Info(msg, zapFields...)
}

// WarnNoCtx 记录Warn级别日志（无上下文）
func (l *ZapLogger) WarnNoCtx(msg string, fields ...any) {
	zapFields := l.convertFields(fields...)
	l.logger.Warn(msg, zapFields...)
}

// ErrorNoCtx 记录Error级别日志（无上下文）
func (l *ZapLogger) ErrorNoCtx(msg string, fields ...any) {
	zapFields := l.convertFields(fields...)
	l.logger.Error(msg, zapFields...)
}

// DebugNoCtx 记录Debug级别日志（无上下文）
func (l *ZapLogger) DebugNoCtx(msg string, fields ...any) {
	zapFields := l.convertFields(fields...)
	l.logger.Debug(msg, zapFields...)
}

// addTraceFields 从context中提取链路信息并添加到日志字段中
func (l *ZapLogger) addTraceFields(ctx context.Context, fields ...any) []zap.Field {
	zapFields := l.convertFields(fields...)

	// 从context中提取span
	span := trace.SpanFromContext(ctx)
	if span.SpanContext().IsValid() {
		spanContext := span.SpanContext()
		zapFields = append(zapFields,
			zap.String("trace_id", spanContext.TraceID().String()),
			zap.String("span_id", spanContext.SpanID().String()),
		)
	}

	return zapFields
}

// convertFields 将通用字段转换为zap字段
func (l *ZapLogger) convertFields(fields ...any) []zap.Field {
	zapFields := make([]zap.Field, 0, len(fields))

	for i := 0; i < len(fields); i += 2 {
		if i+1 >= len(fields) {
			break
		}

		key, ok := fields[i].(string)
		if !ok {
			continue
		}

		value := fields[i+1]
		zapFields = append(zapFields, zap.Any(key, value))
	}

	return zapFields
}
