package logger

import (
	"context"
	"time"
)

// LogStandards 定义统一的日志标准
type LogStandards struct{}

// 标准字段名称常量
const (
	// 基础字段
	FieldRequestID     = "request_id"
	FieldCorrelationID = "correlation_id"
	FieldTraceID       = "trace_id"
	FieldSpanID        = "span_id"
	FieldUserID        = "user_id"
	FieldTenantID      = "tenant_id"
	FieldSessionID     = "session_id"

	// 业务字段
	FieldOperation  = "operation"
	FieldAction     = "action"
	FieldEntityType = "entity_type"
	FieldEntityID   = "entity_id"
	FieldStatus     = "status"
	FieldDuration   = "duration"
	FieldError      = "error"
	FieldErrorCode  = "error_code"

	// 技术字段
	FieldComponent  = "component"
	FieldLayer      = "layer"
	FieldMethod     = "method"
	FieldPath       = "path"
	FieldHTTPMethod = "http_method"
	FieldHTTPStatus = "http_status"
	FieldClientIP   = "client_ip"
	FieldUserAgent  = "user_agent"

	// 数据库字段
	FieldDBOperation    = "db_operation"
	FieldDBTable        = "db_table"
	FieldDBQuery        = "db_query"
	FieldDBDuration     = "db_duration"
	FieldDBRowsAffected = "db_rows_affected"

	// 缓存字段
	FieldCacheOperation = "cache_operation"
	FieldCacheKey       = "cache_key"
	FieldCacheHit       = "cache_hit"
	FieldCacheTTL       = "cache_ttl"
	FieldCacheSize      = "cache_size"

	// 外部调用字段
	FieldExternalService  = "external_service"
	FieldExternalURL      = "external_url"
	FieldExternalMethod   = "external_method"
	FieldExternalStatus   = "external_status"
	FieldExternalDuration = "external_duration"
)

// 日志级别使用规范
const (
	// DEBUG: 详细的调试信息，仅在开发和调试时使用
	// 用途：变量值、函数进入/退出、详细的执行流程
	LevelDebugUsage = "详细调试信息，开发调试时使用"

	// INFO: 一般信息，记录正常的业务流程
	// 用途：用户操作、业务流程开始/结束、重要状态变更
	LevelInfoUsage = "正常业务流程和重要操作记录"

	// WARN: 警告信息，可能的问题但不影响正常运行
	// 用途：配置问题、性能警告、降级操作、重试操作
	LevelWarnUsage = "潜在问题警告，不影响正常运行"

	// ERROR: 错误信息，影响功能正常运行
	// 用途：业务错误、系统错误、外部依赖错误
	LevelErrorUsage = "功能错误，影响正常运行"

	// FATAL: 致命错误，导致应用无法继续运行
	// 用途：系统启动失败、关键资源不可用
	LevelFatalUsage = "致命错误，应用无法继续运行"
)

// StandardContext 标准上下文信息
type StandardContext struct {
	RequestID     string
	CorrelationID string
	TraceID       string
	SpanID        string
	UserID        string
	TenantID      string
	SessionID     string
	Component     string
	Layer         string
}

// contextKey 定义 context key 类型
type contextKey string

const (
	contextKeyUserID        contextKey = "user_id"
	contextKeyTenantID      contextKey = "tenant_id"
	contextKeySessionID     contextKey = "session_id"
	contextKeyComponent     contextKey = "component"
	contextKeyLayer         contextKey = "layer"
	contextKeyOperation     contextKey = "operation"
	contextKeyAction        contextKey = "action"
	contextKeyRequestID     contextKey = "request_id"
	contextKeyCorrelationID contextKey = "correlation_id"
)

// ExtractStandardContext 从context中提取标准上下文信息
func ExtractStandardContext(ctx context.Context) *StandardContext {
	sc := &StandardContext{}

	// 从context中提取各种ID
	if val := ctx.Value(contextKeyRequestID); val != nil {
		if str, ok := val.(string); ok {
			sc.RequestID = str
		}
	}

	if val := ctx.Value(contextKeyCorrelationID); val != nil {
		if str, ok := val.(string); ok {
			sc.CorrelationID = str
		}
	}

	if val := ctx.Value(contextKeyUserID); val != nil {
		if str, ok := val.(string); ok {
			sc.UserID = str
		}
	}

	if val := ctx.Value(contextKeyTenantID); val != nil {
		if str, ok := val.(string); ok {
			sc.TenantID = str
		}
	}

	if val := ctx.Value(contextKeySessionID); val != nil {
		if str, ok := val.(string); ok {
			sc.SessionID = str
		}
	}

	if val := ctx.Value(contextKeyComponent); val != nil {
		if str, ok := val.(string); ok {
			sc.Component = str
		}
	}

	if val := ctx.Value(contextKeyLayer); val != nil {
		if str, ok := val.(string); ok {
			sc.Layer = str
		}
	}

	return sc
}

// ToFields 转换为日志字段
func (sc *StandardContext) ToFields() []interface{} {
	fields := make([]interface{}, 0, 14)

	if sc.RequestID != "" {
		fields = append(fields, FieldRequestID, sc.RequestID)
	}
	if sc.CorrelationID != "" {
		fields = append(fields, FieldCorrelationID, sc.CorrelationID)
	}
	if sc.TraceID != "" {
		fields = append(fields, FieldTraceID, sc.TraceID)
	}
	if sc.SpanID != "" {
		fields = append(fields, FieldSpanID, sc.SpanID)
	}
	if sc.UserID != "" {
		fields = append(fields, FieldUserID, sc.UserID)
	}
	if sc.TenantID != "" {
		fields = append(fields, FieldTenantID, sc.TenantID)
	}
	if sc.SessionID != "" {
		fields = append(fields, FieldSessionID, sc.SessionID)
	}
	if sc.Component != "" {
		fields = append(fields, FieldComponent, sc.Component)
	}
	if sc.Layer != "" {
		fields = append(fields, FieldLayer, sc.Layer)
	}

	return fields
}

// BusinessOperationLog 业务操作日志结构
type BusinessOperationLog struct {
	Operation  string                 `json:"operation"`
	Action     string                 `json:"action"`
	EntityType string                 `json:"entity_type,omitempty"`
	EntityID   string                 `json:"entity_id,omitempty"`
	Status     string                 `json:"status"`
	Duration   time.Duration          `json:"duration"`
	Details    map[string]interface{} `json:"details,omitempty"`
	Error      string                 `json:"error,omitempty"`
	ErrorCode  string                 `json:"error_code,omitempty"`
	StartTime  time.Time              `json:"start_time"`
	EndTime    time.Time              `json:"end_time"`
}

// DatabaseOperationLog 数据库操作日志结构
type DatabaseOperationLog struct {
	Operation    string        `json:"operation"`
	Table        string        `json:"table"`
	Query        string        `json:"query,omitempty"`
	Duration     time.Duration `json:"duration"`
	RowsAffected int64         `json:"rows_affected"`
	Error        string        `json:"error,omitempty"`
	StartTime    time.Time     `json:"start_time"`
	EndTime      time.Time     `json:"end_time"`
}

// CacheOperationLog 缓存操作日志结构
type CacheOperationLog struct {
	Operation string        `json:"operation"`
	Key       string        `json:"key"`
	Hit       bool          `json:"hit"`
	Size      int64         `json:"size,omitempty"`
	TTL       time.Duration `json:"ttl,omitempty"`
	Duration  time.Duration `json:"duration"`
	Error     string        `json:"error,omitempty"`
	StartTime time.Time     `json:"start_time"`
	EndTime   time.Time     `json:"end_time"`
}

// ExternalCallLog 外部调用日志结构
type ExternalCallLog struct {
	Service      string        `json:"service"`
	URL          string        `json:"url"`
	Method       string        `json:"method"`
	StatusCode   int           `json:"status_code"`
	Duration     time.Duration `json:"duration"`
	RequestSize  int64         `json:"request_size,omitempty"`
	ResponseSize int64         `json:"response_size,omitempty"`
	Error        string        `json:"error,omitempty"`
	StartTime    time.Time     `json:"start_time"`
	EndTime      time.Time     `json:"end_time"`
}

// SecurityEventLog 安全事件日志结构
type SecurityEventLog struct {
	EventType string                 `json:"event_type"`
	Action    string                 `json:"action"`
	Result    string                 `json:"result"`
	RiskLevel string                 `json:"risk_level"`
	ClientIP  string                 `json:"client_ip,omitempty"`
	UserAgent string                 `json:"user_agent,omitempty"`
	Details   map[string]interface{} `json:"details,omitempty"`
	Timestamp time.Time              `json:"timestamp"`
}
