package snowflake

import (
	"net"
	"sync"
	"time"

	"github.com/sony/sonyflake"
)

// Generator Sony雪花ID生成器包装器
type Generator struct {
	sf *sonyflake.Sonyflake
}

// NewGenerator 创建新的Sony雪花ID生成器
func NewGenerator(machineID uint16) *Generator {
	settings := sonyflake.Settings{
		MachineID: func() (uint16, error) {
			return machineID, nil
		},
		StartTime: time.Date(2025, 4, 13, 15, 18, 0, 0, time.UTC),
	}

	sf := sonyflake.NewSonyflake(settings)
	if sf == nil {
		panic("failed to create sonyflake generator")
	}

	return &Generator{sf: sf}
}

// NewGeneratorWithIP 使用IP地址创建生成器
func NewGeneratorWithIP() *Generator {
	// 自动获取本机IP作为机器ID
	var machineID func() (uint16, error)
	machineID = func() (uint16, error) {
		ip, err := getLocalIP()
		if err != nil {
			return 1, nil // 如果获取IP失败，使用默认值
		}
		return uint16(ip[2])<<8 + uint16(ip[3]), nil
	}

	settings := sonyflake.Settings{
		MachineID: machineID,
		StartTime: time.Date(2025, 4, 13, 15, 18, 0, 0, time.UTC),
	}

	sf := sonyflake.NewSonyflake(settings)
	if sf == nil {
		panic("failed to create sonyflake generator")
	}

	return &Generator{sf: sf}
}

// Generate 生成雪花ID
func (g *Generator) Generate() int64 {
	id, err := g.sf.NextID()
	if err != nil {
		panic("failed to generate ID: " + err.Error())
	}
	return int64(id)
}

// 获取本地IP地址
func getLocalIP() (net.IP, error) {
	addrs, err := net.InterfaceAddrs()
	if err != nil {
		return nil, err
	}

	for _, addr := range addrs {
		if ipnet, ok := addr.(*net.IPNet); ok && !ipnet.IP.IsLoopback() {
			if ipnet.IP.To4() != nil {
				return ipnet.IP.To4(), nil
			}
		}
	}

	return net.IPv4(127, 0, 0, 1), nil
}

// 全局生成器实例
var (
	defaultGenerator *Generator
	once             sync.Once
)

// Init 初始化全局生成器（使用指定机器ID）
func Init(machineID uint16) {
	once.Do(func() {
		defaultGenerator = NewGenerator(machineID)
	})
}

// InitWithIP 初始化全局生成器（使用IP地址）
func InitWithIP() {
	once.Do(func() {
		defaultGenerator = NewGeneratorWithIP()
	})
}

// Generate 使用全局生成器生成ID
func Generate() int64 {
	if defaultGenerator == nil {
		// 如果没有初始化，使用IP地址初始化
		InitWithIP()
	}
	return defaultGenerator.Generate()
}
