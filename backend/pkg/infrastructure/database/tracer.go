package database

import (
	"context"
	"fmt"
	"time"

	"backend/pkg/infrastructure/logger"
	"backend/pkg/infrastructure/monitoring"

	"go.opentelemetry.io/otel/attribute"
	"go.opentelemetry.io/otel/codes"
	"go.opentelemetry.io/otel/trace"
	"gorm.io/gorm"
)

// DatabaseTracer 数据库操作追踪器
type DatabaseTracer struct {
	logger         logger.Logger
	contextManager *logger.ContextManager
	tracingManager *monitoring.TracingManager
}

// NewDatabaseTracer 创建数据库追踪器
func NewDatabaseTracer(
	log logger.Logger,
	tracingManager *monitoring.TracingManager,
) *DatabaseTracer {
	return &DatabaseTracer{
		logger:         log,
		contextManager: logger.NewContextManager(log),
		tracingManager: tracingManager,
	}
}

// TraceQuery 追踪查询操作
func (dt *DatabaseTracer) TraceQuery(ctx context.Context, operation, table, query string, fn func() (int64, error)) error {
	start := time.Now()

	// 开始数据库span
	spanCtx, span := dt.tracingManager.StartDatabaseSpan(ctx, operation, table)
	defer span.End()

	// 添加查询信息到span
	span.SetAttributes(
		attribute.String("db.statement", query),
		attribute.String("db.operation", operation),
		attribute.String("db.table", table),
	)

	// 执行操作
	rowsAffected, err := fn()
	duration := time.Since(start)

	// 记录结果
	span.SetAttributes(
		attribute.Int64("db.rows_affected", rowsAffected),
		attribute.String("db.duration", duration.String()),
	)

	// 记录日志
	dbOp := logger.DatabaseOperationLog{
		Operation:    operation,
		Table:        table,
		Query:        query,
		Duration:     duration,
		RowsAffected: rowsAffected,
		StartTime:    start,
		EndTime:      time.Now(),
	}

	if err != nil {
		dbOp.Error = err.Error()
		dt.tracingManager.RecordError(span, err)
	} else {
		dt.tracingManager.SetSpanSuccess(span)
	}

	dt.contextManager.LogDatabaseOperation(spanCtx, dbOp)

	return err
}

// TraceTransaction 追踪事务操作
func (dt *DatabaseTracer) TraceTransaction(ctx context.Context, name string, fn func(tx *gorm.DB) error) error {
	start := time.Now()

	// 开始事务span
	spanCtx, span := dt.tracingManager.StartSpan(ctx, fmt.Sprintf("db.transaction.%s", name),
		trace.WithAttributes(
			attribute.String("operation.type", "transaction"),
			attribute.String("transaction.name", name),
		),
	)
	defer span.End()

	// 记录事务开始
	dt.contextManager.LogWithStandardContext(spanCtx, logger.LevelDebug,
		fmt.Sprintf("Starting transaction: %s", name))

	// 执行事务
	err := fn(nil) // 这里需要实际的事务对象
	duration := time.Since(start)

	// 记录结果
	span.SetAttributes(
		attribute.String("transaction.duration", duration.String()),
	)

	if err != nil {
		span.SetAttributes(attribute.String("transaction.error", err.Error()))
		dt.tracingManager.RecordError(span, err)
		dt.contextManager.LogWithStandardContext(spanCtx, logger.LevelError,
			fmt.Sprintf("Transaction failed: %s", name), "error", err.Error(), "duration", duration)
	} else {
		dt.tracingManager.SetSpanSuccess(span)
		dt.contextManager.LogWithStandardContext(spanCtx, logger.LevelInfo,
			fmt.Sprintf("Transaction completed: %s", name), "duration", duration)
	}

	return err
}

// TraceBatchOperation 追踪批量操作
func (dt *DatabaseTracer) TraceBatchOperation(ctx context.Context, operation, table string, batchSize int, fn func() (int64, error)) error {
	start := time.Now()

	// 开始批量操作span
	spanCtx, span := dt.tracingManager.StartDatabaseSpan(ctx, fmt.Sprintf("batch_%s", operation), table)
	defer span.End()

	// 添加批量操作信息
	span.SetAttributes(
		attribute.String("db.operation", fmt.Sprintf("batch_%s", operation)),
		attribute.String("db.table", table),
		attribute.Int("batch.size", batchSize),
	)

	// 执行批量操作
	rowsAffected, err := fn()
	duration := time.Since(start)

	// 记录结果
	span.SetAttributes(
		attribute.Int64("db.rows_affected", rowsAffected),
		attribute.String("db.duration", duration.String()),
		attribute.Float64("batch.throughput", float64(rowsAffected)/duration.Seconds()),
	)

	// 记录日志
	dbOp := logger.DatabaseOperationLog{
		Operation:    fmt.Sprintf("batch_%s", operation),
		Table:        table,
		Duration:     duration,
		RowsAffected: rowsAffected,
		StartTime:    start,
		EndTime:      time.Now(),
	}

	if err != nil {
		dbOp.Error = err.Error()
		dt.tracingManager.RecordError(span, err)
	} else {
		dt.tracingManager.SetSpanSuccess(span)
	}

	dt.contextManager.LogDatabaseOperation(spanCtx, dbOp)

	// 性能警告
	if duration > 5*time.Second {
		dt.contextManager.LogWithStandardContext(spanCtx, logger.LevelWarn,
			"Slow batch operation detected",
			"operation", operation,
			"table", table,
			"batch_size", batchSize,
			"duration", duration,
			"throughput", fmt.Sprintf("%.2f rows/sec", float64(rowsAffected)/duration.Seconds()),
		)
	}

	return err
}

// TraceConnectionPool 追踪连接池状态
func (dt *DatabaseTracer) TraceConnectionPool(ctx context.Context, stats interface{}) {
	// 这里可以记录连接池的统计信息
	dt.contextManager.LogWithStandardContext(ctx, logger.LevelDebug,
		"Database connection pool stats",
		"stats", stats,
	)
}

// TraceMigration 追踪数据库迁移
func (dt *DatabaseTracer) TraceMigration(ctx context.Context, version, direction string, fn func() error) error {
	start := time.Now()

	// 开始迁移span
	spanCtx, span := dt.tracingManager.StartSpan(ctx, fmt.Sprintf("db.migration.%s", direction),
		trace.WithAttributes(
			attribute.String("operation.type", "migration"),
			attribute.String("migration.version", version),
			attribute.String("migration.direction", direction),
		),
	)
	defer span.End()

	// 记录迁移开始
	dt.contextManager.LogWithStandardContext(spanCtx, logger.LevelInfo,
		fmt.Sprintf("Starting migration: %s %s", direction, version),
		"version", version,
		"direction", direction,
	)

	// 执行迁移
	err := fn()
	duration := time.Since(start)

	// 记录结果
	span.SetAttributes(
		attribute.String("migration.duration", duration.String()),
	)

	if err != nil {
		span.SetAttributes(attribute.String("migration.error", err.Error()))
		dt.tracingManager.RecordError(span, err)
		dt.contextManager.LogWithStandardContext(spanCtx, logger.LevelError,
			fmt.Sprintf("Migration failed: %s %s", direction, version),
			"version", version,
			"direction", direction,
			"error", err.Error(),
			"duration", duration,
		)
	} else {
		dt.tracingManager.SetSpanSuccess(span)
		dt.contextManager.LogWithStandardContext(spanCtx, logger.LevelInfo,
			fmt.Sprintf("Migration completed: %s %s", direction, version),
			"version", version,
			"direction", direction,
			"duration", duration,
		)
	}

	return err
}

// TraceSlowQuery 追踪慢查询
func (dt *DatabaseTracer) TraceSlowQuery(ctx context.Context, query string, duration time.Duration, threshold time.Duration) {
	if duration <= threshold {
		return
	}

	// 记录慢查询
	dt.contextManager.LogWithStandardContext(ctx, logger.LevelWarn,
		"Slow query detected",
		"query", query,
		"duration", duration,
		"threshold", threshold,
		"slowness_ratio", float64(duration)/float64(threshold),
	)

	// 如果启用了追踪，创建慢查询事件
	if dt.tracingManager.IsEnabled() {
		_, span := dt.tracingManager.StartSpan(ctx, "db.slow_query")
		defer span.End()

		span.SetAttributes(
			attribute.String("db.statement", query),
			attribute.String("db.duration", duration.String()),
			attribute.String("db.threshold", threshold.String()),
			attribute.Float64("db.slowness_ratio", float64(duration)/float64(threshold)),
		)

		span.SetStatus(codes.Error, "Slow query detected") // Warning status
	}
}
