package adapter

import (
	sharedTx "backend/internal/shared/transaction"
	database "backend/pkg/infrastructure/database/abstraction"
	"context"
)

// TransactionManagerAdapter 适配器，将 database.TransactionManager 适配为 shared/transaction.TransactionManager
// 便于应用层依赖抽象，底层实现复用完整事务能力
type TransactionManagerAdapter struct {
	dbTxManager database.TransactionManager
}

// NewTransactionManagerAdapter 创建适配器实例
func NewTransactionManagerAdapter(dbTxManager database.TransactionManager) sharedTx.TransactionManager {
	return &TransactionManagerAdapter{dbTxManager: dbTxManager}
}

// ExecuteInTransaction 实现 shared/transaction.TransactionManager 接口
func (tma *TransactionManagerAdapter) ExecuteInTransaction(ctx context.Context, fn func(txCtx context.Context) error) error {
	return tma.dbTxManager.WithTransaction(ctx, func(tx database.Transaction) error {
		txCtx := context.WithValue(ctx, sharedTx.TransactionKey, tx)
		return fn(txCtx)
	})
}
