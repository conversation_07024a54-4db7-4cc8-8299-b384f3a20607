package database

import (
	"fmt"

	apperrors "backend/pkg/common/errors"
	"backend/pkg/common/errors/codes"
	"backend/pkg/infrastructure/config"
	database "backend/pkg/infrastructure/database/abstraction"
	"backend/pkg/infrastructure/database/postgres"
)

// DatabaseFactory 数据库工厂
type DatabaseFactory struct{}

// NewDatabaseFactory 创建数据库工厂
func NewDatabaseFactory() *DatabaseFactory {
	return &DatabaseFactory{}
}

// CreateDatabaseManager 根据配置创建数据库管理器
func (f *DatabaseFactory) CreateDatabaseManager(config *config.DatabaseConfig) (database.Manager, error) {
	switch config.Driver {
	case "postgres":
		return postgres.NewDatabaseManager(config), nil
	case "mysql":
		return nil, apperrors.NewInternal(codes.DatabaseConnection, "MySQL数据库暂未支持").Build()
	default:
		return nil, apperrors.NewInternal(codes.DatabaseConnection, fmt.Sprintf("不支持的数据库驱动: %s", config.Driver)).Build()
	}
}

// CreateConnectionFactory 根据配置创建连接工厂
func (f *DatabaseFactory) CreateConnectionFactory(config *config.DatabaseConfig) (database.ConnectionFactory, error) {
	switch config.Driver {
	case "postgres":
		return postgres.NewConnectionFactory(config), nil
	case "mysql":
		return nil, apperrors.NewInternal(codes.DatabaseConnection, "MySQL数据库暂未支持").Build()
	default:
		return nil, apperrors.NewInternal(codes.DatabaseConnection, fmt.Sprintf("不支持的数据库驱动: %s", config.Driver)).Build()
	}
}
