package database

import (
	"context"
	"time"
)

// DatabaseContext 数据库操作上下文
type DatabaseContext struct {
	ctx       context.Context
	tenantID  string
	userID    string
	requestID string
	timeout   time.Duration
}

// NewDatabaseContext 创建数据库上下文
func NewDatabaseContext(ctx context.Context) *DatabaseContext {
	return &DatabaseContext{
		ctx:     ctx,
		timeout: 30 * time.Second,
	}
}

// WithTenant 设置租户ID
func (dc *DatabaseContext) WithTenant(tenantID string) *DatabaseContext {
	dc.tenantID = tenantID
	return dc
}

// WithUser 设置用户ID
func (dc *DatabaseContext) WithUser(userID string) *DatabaseContext {
	dc.userID = userID
	return dc
}

// WithRequestID 设置请求ID
func (dc *DatabaseContext) WithRequestID(requestID string) *DatabaseContext {
	dc.requestID = requestID
	return dc
}

// WithTimeout 设置超时时间
func (dc *DatabaseContext) WithTimeout(timeout time.Duration) *DatabaseContext {
	dc.timeout = timeout
	return dc
}

// Context 获取Go上下文
func (dc *DatabaseContext) Context() context.Context {
	if dc.timeout > 0 {
		ctx, _ := context.WithTimeout(dc.ctx, dc.timeout)
		return ctx
	}
	return dc.ctx
}

// TenantID 获取租户ID
func (dc *DatabaseContext) TenantID() string {
	return dc.tenantID
}

// UserID 获取用户ID
func (dc *DatabaseContext) UserID() string {
	return dc.userID
}

// RequestID 获取请求ID
func (dc *DatabaseContext) RequestID() string {
	return dc.requestID
}
