package database

import (
	"context"
)

// TransactionContext 事务上下文
type TransactionContext struct {
	TxID      string              // 事务ID
	Options   *TransactionOptions // 事务选项
	StartTime int64               // 开始时间
}

// TransactionCallback 事务回调函数
type TransactionCallback func(tx Transaction) error

// TransactionStatus 事务状态
type TransactionStatus int

const (
	TransactionStatusActive     TransactionStatus = iota // 活跃
	TransactionStatusCommitted                           // 已提交
	TransactionStatusRolledBack                          // 已回滚
)

// String 返回事务状态的字符串表示
func (s TransactionStatus) String() string {
	switch s {
	case TransactionStatusActive:
		return "active"
	case TransactionStatusCommitted:
		return "committed"
	case TransactionStatusRolledBack:
		return "rolled_back"
	default:
		return "unknown"
	}
}

// TransactionInfo 事务信息
type TransactionInfo struct {
	ID       string              // 事务ID
	Status   TransactionStatus   // 事务状态
	Options  *TransactionOptions // 事务选项
	Duration int64               // 持续时间（毫秒）
}

// NestedTransactionManager 嵌套事务管理器
type NestedTransactionManager interface {
	// BeginNested 开始嵌套事务
	BeginNested(ctx context.Context, parentTx Transaction) (Transaction, error)

	// GetNestedLevel 获取嵌套层级
	GetNestedLevel(tx Transaction) int

	// IsNestedTransaction 是否为嵌套事务
	IsNestedTransaction(tx Transaction) bool
}

// TransactionHook 事务钩子
type TransactionHook interface {
	// BeforeBegin 事务开始前
	BeforeBegin(ctx context.Context, options *TransactionOptions) error

	// AfterBegin 事务开始后
	AfterBegin(ctx context.Context, tx Transaction) error

	// BeforeCommit 提交前
	BeforeCommit(ctx context.Context, tx Transaction) error

	// AfterCommit 提交后
	AfterCommit(ctx context.Context, tx Transaction) error

	// BeforeRollback 回滚前
	BeforeRollback(ctx context.Context, tx Transaction) error

	// AfterRollback 回滚后
	AfterRollback(ctx context.Context, tx Transaction) error
}
