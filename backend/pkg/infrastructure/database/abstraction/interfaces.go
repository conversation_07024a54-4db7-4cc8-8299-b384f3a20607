package database

import (
	"context"
	"database/sql"
	"time"

	"gorm.io/gorm"
)

// Connection 数据库连接接口
type Connection interface {
	// GetDB 获取GORM数据库实例
	GetDB() *gorm.DB

	// GetSQLDB 获取原生SQL数据库实例
	GetSQLDB() *sql.DB

	// Close 关闭数据库连接
	Close() error

	// Ping 测试数据库连接
	Ping() error

	// HealthCheck 健康检查
	HealthCheck() error

	// Stats 获取连接池统计信息
	Stats() sql.DBStats
}

// Migrator 数据库迁移接口
type Migrator interface {
	// CheckPendingMigrations 检查是否有待应用的迁移
	CheckPendingMigrations() (bool, error)

	// ApplyMigrations 应用所有待处理的迁移
	ApplyMigrations() error

	// GetCurrentVersion 获取当前数据库版本
	GetCurrentVersion() (int64, error)

	// RollbackMigration 回滚最后一个迁移
	RollbackMigration() error

	// ResetMigrations 重置所有迁移 (危险操作)
	ResetMigrations() error

	// GetMigrationStatus 获取迁移状态
	GetMigrationStatus() error

	// PerformAutoMigration 执行自动迁移
	PerformAutoMigration(config AutoMigrationConfig) error
}

// AutoMigrationConfig 自动迁移配置
type AutoMigrationConfig struct {
	EnableAutoMigrate bool // 是否启用自动迁移
	FailOnError       bool // 迁移失败时是否停止应用启动
	LogMigrations     bool // 是否记录迁移日志
}

// ConnectionFactory 数据库连接工厂接口
type ConnectionFactory interface {
	// CreateConnection 创建数据库连接
	CreateConnection() (Connection, error)

	// CreateMigrator 创建迁移管理器
	CreateMigrator(connection Connection) (Migrator, error)
}

// ConnectionConfig 数据库连接配置接口
type ConnectionConfig interface {
	// GetDriver 获取数据库驱动名称
	GetDriver() string

	// GetDSN 获取数据库连接字符串
	GetDSN() string

	// GetMaxOpenConns 获取最大打开连接数
	GetMaxOpenConns() int

	// GetMaxIdleConns 获取最大空闲连接数
	GetMaxIdleConns() int

	// GetConnMaxLifetime 获取连接最大生存时间
	GetConnMaxLifetime() time.Duration

	// GetConnMaxIdleTime 获取连接最大空闲时间
	GetConnMaxIdleTime() time.Duration
}

// Manager 数据库管理器接口
type Manager interface {
	// GetConnection 获取数据库连接
	GetConnection() Connection

	// GetMigrator 获取迁移管理器
	GetMigrator() Migrator

	// Initialize 初始化数据库管理器
	Initialize() error

	// Close 关闭数据库管理器
	Close() error

	// HealthCheck 健康检查
	HealthCheck() error

	// 新增：获取抽象数据访问接口
	GetDataAccess() DataAccess
	GetQueryBuilder() QueryBuilder
	GetTransactionManager() TransactionManager
}

// ================ 新增：数据访问抽象接口 ================

// DataAccess 数据访问抽象接口
type DataAccess interface {
	// 基础CRUD操作
	Create(ctx context.Context, model interface{}) error
	Save(ctx context.Context, model interface{}) error
	Update(ctx context.Context, model interface{}) error
	Delete(ctx context.Context, model interface{}) error

	// 查询操作
	First(ctx context.Context, dest interface{}, conditions ...interface{}) error
	Find(ctx context.Context, dest interface{}, conditions ...interface{}) error
	FindByID(ctx context.Context, dest interface{}, id interface{}) error

	// 统计操作
	Count(ctx context.Context, model interface{}, conditions ...interface{}) (int64, error)
	Exists(ctx context.Context, model interface{}, conditions ...interface{}) (bool, error)

	// 批量操作
	CreateInBatches(ctx context.Context, models interface{}, batchSize int) error
	UpdateInBatches(ctx context.Context, model interface{}, updates interface{}, batchSize int) error
	DeleteInBatches(ctx context.Context, model interface{}, conditions interface{}, batchSize int) error
}

// QueryBuilder 查询构建器接口
type QueryBuilder interface {
	// 条件构建
	Where(query interface{}, args ...interface{}) QueryBuilder
	Or(query interface{}, args ...interface{}) QueryBuilder
	Not(query interface{}, args ...interface{}) QueryBuilder

	// 关联查询
	Joins(query string, args ...interface{}) QueryBuilder
	Preload(query string, args ...interface{}) QueryBuilder

	// 排序和分页
	Order(value interface{}) QueryBuilder
	Group(name string) QueryBuilder
	Having(query interface{}, args ...interface{}) QueryBuilder
	Limit(limit int) QueryBuilder
	Offset(offset int) QueryBuilder

	// 执行查询
	Find(dest interface{}) error
	First(dest interface{}) error
	Count(count *int64) error
	Pluck(column string, dest interface{}) error

	// 聚合函数
	Sum(column string) (float64, error)
	Avg(column string) (float64, error)
	Min(column string) (interface{}, error)
	Max(column string) (interface{}, error)

	// 原始SQL
	Raw(sql string, values ...interface{}) QueryBuilder
	Exec(sql string, values ...interface{}) error
}

// Transaction 事务接口
type Transaction interface {
	DataAccess

	// 事务控制
	Commit() error
	Rollback() error
	SavePoint(name string) error
	RollbackTo(name string) error

	// 获取原始事务对象（用于特殊情况）
	GetRawTransaction() interface{}
}

// TransactionManager 事务管理器接口
type TransactionManager interface {
	// 开始事务
	Begin(ctx context.Context) (Transaction, error)
	BeginWithOptions(ctx context.Context, opts *TransactionOptions) (Transaction, error)

	// 事务执行
	WithTransaction(ctx context.Context, fn func(tx Transaction) error) error
	WithTransactionOptions(ctx context.Context, opts *TransactionOptions, fn func(tx Transaction) error) error

	// 嵌套事务支持
	RequiresNew(ctx context.Context, fn func(tx Transaction) error) error
}

// TransactionOptions 事务选项
type TransactionOptions struct {
	Isolation sql.IsolationLevel
	ReadOnly  bool
	Timeout   time.Duration
}

// RepositoryContext 仓储上下文接口
type RepositoryContext interface {
	GetDataAccess() DataAccess
	GetQueryBuilder() QueryBuilder
	GetTransaction() Transaction
	IsInTransaction() bool
}

// ================ 错误定义 ================

// DatabaseError 数据库错误接口
type DatabaseError interface {
	error
	Code() string
	IsRetryable() bool
	IsConstraintViolation() bool
	IsNotFound() bool
	IsDuplicateKey() bool
}
