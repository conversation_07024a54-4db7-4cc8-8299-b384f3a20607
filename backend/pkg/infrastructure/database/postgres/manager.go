package postgres

import (
	"backend/pkg/infrastructure/config"
	database "backend/pkg/infrastructure/database/abstraction"
	"backend/pkg/infrastructure/database/postgres/adapter"
)

// NewDatabaseManager 创建PostgreSQL数据库管理器
func NewDatabaseManager(config *config.DatabaseConfig) database.Manager {
	return adapter.NewDatabaseManager(config)
}

// NewConnectionFactory 创建PostgreSQL连接工厂
func NewConnectionFactory(config *config.DatabaseConfig) database.ConnectionFactory {
	return adapter.NewPostgreSQLConnectionFactory(config)
}
