package adapter

import (
	"database/sql"
	"fmt"

	apperrors "backend/pkg/common/errors"
	"backend/pkg/common/errors/codes"
	database "backend/pkg/infrastructure/database/abstraction"

	"github.com/pressly/goose/v3"
)

// PostgreSQLMigrator PostgreSQL迁移器适配器
type PostgreSQLMigrator struct {
	sqlDB          *sql.DB
	migrationsDir  string
	currentVersion *int64
}

// NewPostgreSQLMigrator 创建PostgreSQL迁移器
func NewPostgreSQLMigrator(sqlDB *sql.DB, migrationsDir string) database.Migrator {
	return &PostgreSQLMigrator{
		sqlDB:         sqlDB,
		migrationsDir: migrationsDir,
	}
}

// init 初始化迁移器
func (m *PostgreSQLMigrator) init() error {
	// 设置Goose配置
	if err := goose.SetDialect("postgres"); err != nil {
		return apperrors.NewInternal(codes.DatabaseMigration, "设置Goose数据库方言失败").Wrap(err).Build()
	}

	version, err := goose.GetDBVersion(m.sqlDB)
	if err != nil {
		return apperrors.NewInternal(codes.DatabaseMigration, "获取数据库版本失败").Wrap(err).Build()
	}

	m.currentVersion = &version

	return nil
}

// CheckPendingMigrations 检查是否有待应用的迁移
func (m *PostgreSQLMigrator) CheckPendingMigrations() (bool, error) {
	if err := m.init(); err != nil {
		return false, err
	}

	// 获取最新版本
	// 首先尝试收集所有迁移文件以检查是否存在
	allMigrations, err := goose.CollectMigrations(m.migrationsDir, 0, goose.MaxVersion)
	if err != nil {
		return false, apperrors.NewInternal(codes.DatabaseMigration, "收集迁移失败").
			WithDetail("migrations_dir", m.migrationsDir).
			WithDetail("current_version", *m.currentVersion).
			Wrap(err).Build()
	}

	// 如果没有迁移文件，返回false
	if len(allMigrations) == 0 {
		return false, nil
	}

	// 收集待处理的迁移
	migrations, err := goose.CollectMigrations(m.migrationsDir, *m.currentVersion, goose.MaxVersion)
	if err != nil {
		// 如果错误是"no migration files found"，这意味着没有待处理的迁移，这是正常的
		if err.Error() == "no migration files found" {
			return false, nil
		}
		return false, apperrors.NewInternal(codes.DatabaseMigration, "收集待处理迁移失败").
			WithDetail("migrations_dir", m.migrationsDir).
			WithDetail("current_version", *m.currentVersion).
			Wrap(err).Build()
	}

	// 检查是否有待应用的迁移
	return len(migrations) > 0, nil
}

// ApplyMigrations 应用所有待处理的迁移
func (m *PostgreSQLMigrator) ApplyMigrations() error {
	if err := m.init(); err != nil {
		return err
	}

	// 执行迁移
	if err := goose.Up(m.sqlDB, m.migrationsDir); err != nil {
		return apperrors.NewInternal(codes.DatabaseMigration, "执行迁移失败").Wrap(err).Build()
	}

	return nil
}

// GetCurrentVersion 获取当前数据库版本
func (m *PostgreSQLMigrator) GetCurrentVersion() (int64, error) {
	if err := m.init(); err != nil {
		return 0, err
	}

	return *m.currentVersion, nil
}

// RollbackMigration 回滚最后一个迁移
func (m *PostgreSQLMigrator) RollbackMigration() error {
	if err := m.init(); err != nil {
		return err
	}

	// 执行回滚
	if err := goose.Down(m.sqlDB, m.migrationsDir); err != nil {
		return apperrors.NewInternal(codes.DatabaseMigration, "执行回滚失败").Wrap(err).Build()
	}

	return nil
}

// ResetMigrations 重置所有迁移 (危险操作)
func (m *PostgreSQLMigrator) ResetMigrations() error {
	if err := m.init(); err != nil {
		return err
	}

	// 重置迁移
	if err := goose.Reset(m.sqlDB, m.migrationsDir); err != nil {
		return apperrors.NewInternal(codes.DatabaseMigration, "重置迁移失败").Wrap(err).Build()
	}

	return nil
}

// GetMigrationStatus 获取迁移状态
func (m *PostgreSQLMigrator) GetMigrationStatus() error {
	if err := m.init(); err != nil {
		return err
	}

	// 获取状态
	if err := goose.Status(m.sqlDB, m.migrationsDir); err != nil {
		return apperrors.NewInternal(codes.DatabaseMigration, "获取迁移状态失败").Wrap(err).Build()
	}

	return nil
}

// PerformAutoMigration 执行自动迁移
func (m *PostgreSQLMigrator) PerformAutoMigration(config database.AutoMigrationConfig) error {
	if !config.EnableAutoMigrate {
		return nil // 未启用自动迁移
	}

	// 检查是否有待应用的迁移
	hasPending, err := m.CheckPendingMigrations()
	if err != nil {
		if config.FailOnError {
			return fmt.Errorf("检查待处理迁移失败: %w", err)
		}
		// 记录错误但不停止应用启动
		fmt.Printf("警告: 检查待处理迁移失败: %v\n", err)
		return nil
	}

	if !hasPending {
		if config.LogMigrations {
			fmt.Println("数据库迁移: 没有待处理的迁移")
		}
		return nil
	}

	// 执行迁移
	if config.LogMigrations {
		fmt.Println("数据库迁移: 开始执行自动迁移")
	}

	if err := m.ApplyMigrations(); err != nil {
		if config.FailOnError {
			return fmt.Errorf("自动迁移失败: %w", err)
		}
		// 记录错误但不停止应用启动
		fmt.Printf("警告: 自动迁移失败: %v\n", err)
		return nil
	}

	if config.LogMigrations {
		fmt.Println("数据库迁移: 自动迁移完成")
	}

	return nil
}
