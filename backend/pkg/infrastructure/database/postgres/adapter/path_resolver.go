package adapter

import (
	apperrors "backend/pkg/common/errors"
	"backend/pkg/common/errors/codes"
	"os"
	"path/filepath"
	"runtime"
	"strings"
)

// PathResolver 路径解析器
type PathResolver struct {
	projectRoot   string
	isTestMode    bool
	migrationsDir string
}

// NewPathResolver 创建路径解析器
func NewPathResolver() *PathResolver {
	return &PathResolver{
		isTestMode: isTestMode(),
	}
}

// ResolveProjectRoot 解析项目根路径
func (pr *PathResolver) ResolveProjectRoot() error {
	if pr.projectRoot != "" {
		return nil // 已经解析过
	}

	root, err := pr.findProjectRoot()
	if err != nil {
		return apperrors.NewInternal(codes.FileSystem, "解析项目根路径失败").Wrap(err).Build()
	}

	pr.projectRoot = root
	pr.migrationsDir = filepath.Join(root, "migrations", "postgres")
	return nil
}

// GetProjectRoot 获取项目根路径
func (pr *PathResolver) GetProjectRoot() string {
	if pr.projectRoot == "" {
		_ = pr.ResolveProjectRoot()
	}
	return pr.projectRoot
}

// GetMigrationsDir 获取迁移文件目录
func (pr *PathResolver) GetMigrationsDir() string {
	if pr.migrationsDir == "" {
		_ = pr.ResolveProjectRoot()
	}
	return pr.migrationsDir
}

// IsTestMode 是否为测试模式
func (pr *PathResolver) IsTestMode() bool {
	return pr.isTestMode
}

// findProjectRoot 查找项目根目录
func (pr *PathResolver) findProjectRoot() (string, error) {
	// 方法1：从当前工作目录向上查找
	if root := pr.findRootFromWorkingDir(); root != "" {
		return root, nil
	}

	// 方法2：从调用栈向上查找
	if root := pr.findRootFromCallerStack(); root != "" {
		return root, nil
	}

	// 方法3：从环境变量获取
	if root := os.Getenv("PROJECT_ROOT"); root != "" {
		if pr.isValidProjectRoot(root) {
			return root, nil
		}
	}

	// 方法4：使用默认路径（相对于当前文件）
	_, currentFile, _, ok := runtime.Caller(0)
	if ok {
		// 当前文件路径: pkg/infrastructure/database/postgres/adapter/path_resolver.go
		// 需要回到项目根目录: ../../../../../..
		dir := filepath.Dir(currentFile)
		for i := 0; i < 6; i++ {
			dir = filepath.Dir(dir)
		}
		if pr.isValidProjectRoot(dir) {
			return dir, nil
		}
	}

	return "", apperrors.NewInternal(codes.FileSystem, "查找项目根目录失败").Build()
}

// findRootFromWorkingDir 从工作目录向上查找
func (pr *PathResolver) findRootFromWorkingDir() string {
	wd, err := os.Getwd()
	if err != nil {
		return ""
	}

	return pr.findRootFromPath(wd)
}

// findRootFromCallerStack 从调用栈向上查找
func (pr *PathResolver) findRootFromCallerStack() string {
	for i := 1; i < 10; i++ { // 查找调用栈中的前10层
		_, file, _, ok := runtime.Caller(i)
		if !ok {
			break
		}

		dir := filepath.Dir(file)
		if root := pr.findRootFromPath(dir); root != "" {
			return root
		}
	}
	return ""
}

// findRootFromPath 从指定路径向上查找项目根
func (pr *PathResolver) findRootFromPath(startPath string) string {
	currentPath := startPath

	for {
		if pr.isValidProjectRoot(currentPath) {
			return currentPath
		}

		parentPath := filepath.Dir(currentPath)
		if parentPath == currentPath {
			// 已经到达文件系统根目录
			break
		}
		currentPath = parentPath
	}

	return ""
}

// isValidProjectRoot 检查是否为有效的项目根目录
func (pr *PathResolver) isValidProjectRoot(path string) bool {
	// 检查关键文件和目录是否存在
	indicators := []string{
		"go.mod",              // Go模块文件
		"Makefile",            // Makefile
		"internal",            // 内部目录
		"pkg",                 // 包目录
		"migrations/postgres", // 迁移目录
		"configs/config.yaml", // 配置文件
	}

	for _, indicator := range indicators {
		fullPath := filepath.Join(path, indicator)
		if _, err := os.Stat(fullPath); os.IsNotExist(err) {
			return false
		}
	}

	return true
}

// isTestMode 检查是否为测试模式
func isTestMode() bool {
	// 方法1：检查命令行参数
	for _, arg := range os.Args {
		if strings.Contains(arg, "test") || strings.Contains(arg, ".test") {
			return true
		}
	}

	// 方法2：检查调用栈中是否有testing相关的函数
	for i := 1; i < 10; i++ {
		_, file, _, ok := runtime.Caller(i)
		if !ok {
			break
		}

		if strings.Contains(file, "_test.go") ||
			strings.Contains(file, "testing") ||
			strings.Contains(file, "testify") {
			return true
		}
	}

	// 方法3：检查环境变量
	if os.Getenv("GO_ENV") == "test" || os.Getenv("ENV") == "test" {
		return true
	}

	return false
}

// GetRelativePath 获取相对于项目根的相对路径
func (pr *PathResolver) GetRelativePath(absolutePath string) (string, error) {
	if err := pr.ResolveProjectRoot(); err != nil {
		return "", err
	}

	rel, err := filepath.Rel(pr.projectRoot, absolutePath)
	if err != nil {
		return "", apperrors.NewInternal(codes.FileSystem, "获取相对于项目根的相对路径失败").Wrap(err).Build()
	}

	return rel, nil
}

// JoinProjectPath 连接项目路径
func (pr *PathResolver) JoinProjectPath(paths ...string) string {
	if err := pr.ResolveProjectRoot(); err != nil {
		return ""
	}

	allPaths := append([]string{pr.projectRoot}, paths...)
	return filepath.Join(allPaths...)
}
