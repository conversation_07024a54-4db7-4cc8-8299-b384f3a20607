package adapter

import (
	"context"
	"fmt"
	"time"

	apperrors "backend/pkg/common/errors"
	"backend/pkg/common/errors/codes"
	"backend/pkg/infrastructure/database/abstraction"

	"gorm.io/gorm"
)

// GormTransactionManager GORM事务管理器适配器
type GormTransactionManager struct {
	db *gorm.DB
}

// NewGormTransactionManager 创建GORM事务管理器适配器
func NewGormTransactionManager(db *gorm.DB) database.TransactionManager {
	return &GormTransactionManager{db: db}
}

// Begin 开始事务
func (gtm *GormTransactionManager) Begin(ctx context.Context) (database.Transaction, error) {
	tx := gtm.db.WithContext(ctx).Begin()
	if tx.Error != nil {
		return nil, apperrors.NewInternal(codes.DatabaseTransaction, "开始事务失败").Wrap(tx.Error).Build()
	}
	return NewGormTransaction(tx), nil
}

// BeginWithOptions 使用选项开始事务
func (gtm *GormTransactionManager) BeginWithOptions(ctx context.Context, opts *database.TransactionOptions) (database.Transaction, error) {
	if opts != nil && opts.Timeout > 0 {
		var cancel context.CancelFunc
		ctx, cancel = context.WithTimeout(ctx, time.Duration(opts.Timeout)*time.Second)
		defer cancel()
	}

	tx := gtm.db.WithContext(ctx).Begin()
	if tx.Error != nil {
		return nil, apperrors.NewInternal(codes.DatabaseTransaction, "开始事务失败").Wrap(tx.Error).Build()
	}

	return NewGormTransaction(tx), nil
}

// WithTransaction 在事务中执行函数
func (gtm *GormTransactionManager) WithTransaction(ctx context.Context, fn func(tx database.Transaction) error) error {
	tx, err := gtm.Begin(ctx)
	if err != nil {
		return err
	}

	defer func() {
		if r := recover(); r != nil {
			tx.Rollback()
			panic(r)
		}
	}()

	if err := fn(tx); err != nil {
		if rollbackErr := tx.Rollback(); rollbackErr != nil {
			return apperrors.NewInternal(codes.DatabaseTransaction, "回滚事务失败").Wrap(rollbackErr).Build()
		}
		return err
	}

	return tx.Commit()
}

// WithTransactionOptions 使用选项在事务中执行函数
func (gtm *GormTransactionManager) WithTransactionOptions(ctx context.Context, opts *database.TransactionOptions, fn func(tx database.Transaction) error) error {
	tx, err := gtm.BeginWithOptions(ctx, opts)
	if err != nil {
		return err
	}

	defer func() {
		if r := recover(); r != nil {
			tx.Rollback()
			panic(r)
		}
	}()

	if err := fn(tx); err != nil {
		if rollbackErr := tx.Rollback(); rollbackErr != nil {
			return apperrors.NewInternal(codes.DatabaseTransaction, "回滚事务失败").Wrap(rollbackErr).Build()
		}
		return err
	}

	return tx.Commit()
}

// GormTransaction GORM事务实现
type GormTransaction struct {
	tx *gorm.DB
}

// NewGormTransaction 创建GORM事务
func NewGormTransaction(tx *gorm.DB) database.Transaction {
	return &GormTransaction{tx: tx}
}

// Create 创建记录
func (gt *GormTransaction) Create(ctx context.Context, model interface{}) error {
	if err := gt.tx.WithContext(ctx).Create(model).Error; err != nil {
		return apperrors.NewInternal(codes.DatabaseTransaction, "创建记录失败").Wrap(err).Build()
	}
	return nil
}

// Save 保存记录
func (gt *GormTransaction) Save(ctx context.Context, model interface{}) error {
	if err := gt.tx.WithContext(ctx).Save(model).Error; err != nil {
		return apperrors.NewInternal(codes.DatabaseTransaction, "保存记录失败").Wrap(err).Build()
	}
	return nil
}

// Update 更新记录
func (gt *GormTransaction) Update(ctx context.Context, model interface{}) error {
	if err := gt.tx.WithContext(ctx).Updates(model).Error; err != nil {
		return apperrors.NewInternal(codes.DatabaseTransaction, "更新记录失败").Wrap(err).Build()
	}
	return nil
}

// Delete 删除记录
func (gt *GormTransaction) Delete(ctx context.Context, model interface{}) error {
	if err := gt.tx.WithContext(ctx).Delete(model).Error; err != nil {
		return apperrors.NewInternal(codes.DatabaseTransaction, "删除记录失败").Wrap(err).Build()
	}
	return nil
}

// First 查询第一条记录
func (gt *GormTransaction) First(ctx context.Context, dest interface{}, conditions ...interface{}) error {
	query := gt.tx.WithContext(ctx)
	if len(conditions) > 0 {
		query = query.Where(conditions[0], conditions[1:]...)
	}

	if err := query.First(dest).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			return apperrors.NewInternal(codes.DatabaseNotFound, "事务中记录未找到").Build()
		}
		return apperrors.NewInternal(codes.DatabaseTransaction, "查询第一条记录失败").Wrap(err).Build()
	}
	return nil
}

// Find 查询多条记录
func (gt *GormTransaction) Find(ctx context.Context, dest interface{}, conditions ...interface{}) error {
	query := gt.tx.WithContext(ctx)
	if len(conditions) > 0 {
		query = query.Where(conditions[0], conditions[1:]...)
	}

	if err := query.Find(dest).Error; err != nil {
		return apperrors.NewInternal(codes.DatabaseTransaction, "查询多条记录失败").Wrap(err).Build()
	}
	return nil
}

// FindByID 根据ID查询记录
func (gt *GormTransaction) FindByID(ctx context.Context, dest interface{}, id interface{}) error {
	if err := gt.tx.WithContext(ctx).First(dest, id).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			return apperrors.NewInternal(codes.DatabaseNotFound, fmt.Sprintf("事务中记录未找到: ID=%v", id)).Build()
		}
		return apperrors.NewInternal(codes.DatabaseTransaction, "查询记录失败").Wrap(err).Build()
	}
	return nil
}

// Count 统计记录数量
func (gt *GormTransaction) Count(ctx context.Context, model interface{}, conditions ...interface{}) (int64, error) {
	var count int64
	query := gt.tx.WithContext(ctx).Model(model)
	if len(conditions) > 0 {
		query = query.Where(conditions[0], conditions[1:]...)
	}

	if err := query.Count(&count).Error; err != nil {
		return 0, apperrors.NewInternal(codes.DatabaseTransaction, "统计记录数量失败").Wrap(err).Build()
	}
	return count, nil
}

// Exists 检查记录是否存在
func (gt *GormTransaction) Exists(ctx context.Context, model interface{}, conditions ...interface{}) (bool, error) {
	count, err := gt.Count(ctx, model, conditions...)
	if err != nil {
		return false, err
	}
	return count > 0, nil
}

// CreateInBatches 批量创建记录
func (gt *GormTransaction) CreateInBatches(ctx context.Context, models interface{}, batchSize int) error {
	if err := gt.tx.WithContext(ctx).CreateInBatches(models, batchSize).Error; err != nil {
		return apperrors.NewInternal(codes.DatabaseTransaction, "批量创建记录失败").Wrap(err).Build()
	}
	return nil
}

// UpdateInBatches 批量更新记录
func (gt *GormTransaction) UpdateInBatches(ctx context.Context, model interface{}, updates interface{}, batchSize int) error {
	if err := gt.tx.WithContext(ctx).Model(model).Updates(updates).Error; err != nil {
		return apperrors.NewInternal(codes.DatabaseTransaction, "批量更新记录失败").Wrap(err).Build()
	}
	return nil
}

// DeleteInBatches 批量删除记录
func (gt *GormTransaction) DeleteInBatches(ctx context.Context, model interface{}, conditions interface{}, batchSize int) error {
	query := gt.tx.WithContext(ctx).Where(conditions)
	if err := query.Delete(model).Error; err != nil {
		return apperrors.NewInternal(codes.DatabaseTransaction, "批量删除记录失败").Wrap(err).Build()
	}
	return nil
}

// Commit 提交事务
func (gt *GormTransaction) Commit() error {
	if err := gt.tx.Commit().Error; err != nil {
		return apperrors.NewInternal(codes.DatabaseTransaction, "提交事务失败").Wrap(err).Build()
	}
	return nil
}

// Rollback 回滚事务
func (gt *GormTransaction) Rollback() error {
	if err := gt.tx.Rollback().Error; err != nil {
		return apperrors.NewInternal(codes.DatabaseTransaction, "回滚事务失败").Wrap(err).Build()
	}
	return nil
}

// SavePoint 创建保存点
func (gt *GormTransaction) SavePoint(name string) error {
	if err := gt.tx.SavePoint(name).Error; err != nil {
		return apperrors.NewInternal(codes.DatabaseTransaction, "创建保存点失败").Wrap(err).Build()
	}
	return nil
}

// RollbackTo 回滚到保存点
func (gt *GormTransaction) RollbackTo(name string) error {
	if err := gt.tx.RollbackTo(name).Error; err != nil {
		return apperrors.NewInternal(codes.DatabaseTransaction, "回滚到保存点失败").Wrap(err).Build()
	}
	return nil
}

// GetRawTransaction 获取原始事务对象
func (gt *GormTransaction) GetRawTransaction() interface{} {
	return gt.tx
}

// RequiresNew 需要新事务执行函数（添加到GormTransactionManager）
func (gtm *GormTransactionManager) RequiresNew(ctx context.Context, fn func(tx database.Transaction) error) error {
	// 创建新的数据库连接来确保新事务
	newDB := gtm.db.Session(&gorm.Session{})
	newTxManager := &GormTransactionManager{db: newDB}
	return newTxManager.WithTransaction(ctx, fn)
}
