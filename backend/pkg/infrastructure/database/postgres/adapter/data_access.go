package adapter

import (
	"context"
	"fmt"

	apperrors "backend/pkg/common/errors"
	"backend/pkg/common/errors/codes"
	database "backend/pkg/infrastructure/database/abstraction"

	"gorm.io/gorm"
)

// GormDataAccess GORM数据访问适配器
type GormDataAccess struct {
	db *gorm.DB
}

// NewGormDataAccess 创建GORM数据访问适配器
func NewGormDataAccess(db *gorm.DB) database.DataAccess {
	return &GormDataAccess{db: db}
}

// Create 创建记录
func (gda *GormDataAccess) Create(ctx context.Context, model interface{}) error {
	if err := gda.db.WithContext(ctx).Create(model).Error; err != nil {
		return apperrors.NewInternal(codes.DatabaseQuery, "创建记录失败").Wrap(err).Build()
	}
	return nil
}

// Save 保存记录
func (gda *GormDataAccess) Save(ctx context.Context, model interface{}) error {
	if err := gda.db.WithContext(ctx).Save(model).Error; err != nil {
		return apperrors.NewInternal(codes.DatabaseQuery, "保存记录失败").Wrap(err).Build()
	}
	return nil
}

// Update 更新记录
func (gda *GormDataAccess) Update(ctx context.Context, model interface{}) error {
	if err := gda.db.WithContext(ctx).Updates(model).Error; err != nil {
		return apperrors.NewInternal(codes.DatabaseQuery, "更新记录失败").Wrap(err).Build()
	}
	return nil
}

// Delete 删除记录
func (gda *GormDataAccess) Delete(ctx context.Context, model interface{}) error {
	if err := gda.db.WithContext(ctx).Delete(model).Error; err != nil {
		return apperrors.NewInternal(codes.DatabaseQuery, "删除记录失败").Wrap(err).Build()
	}
	return nil
}

// First 查询第一条记录
func (gda *GormDataAccess) First(ctx context.Context, dest interface{}, conditions ...interface{}) error {
	query := gda.db.WithContext(ctx)
	if len(conditions) > 0 {
		query = query.Where(conditions[0], conditions[1:]...)
	}

	if err := query.First(dest).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			return apperrors.NewInternal(codes.DatabaseNotFound, "记录未找到").Build()
		}
		return apperrors.NewInternal(codes.DatabaseQuery, "查询第一条记录失败").Wrap(err).Build()
	}
	return nil
}

// Find 查询多条记录
func (gda *GormDataAccess) Find(ctx context.Context, dest interface{}, conditions ...interface{}) error {
	query := gda.db.WithContext(ctx)
	if len(conditions) > 0 {
		query = query.Where(conditions[0], conditions[1:]...)
	}

	if err := query.Find(dest).Error; err != nil {
		return apperrors.NewInternal(codes.DatabaseQuery, "查询多条记录失败").Wrap(err).Build()
	}
	return nil
}

// FindByID 根据ID查询记录
func (gda *GormDataAccess) FindByID(ctx context.Context, dest interface{}, id interface{}) error {
	if err := gda.db.WithContext(ctx).First(dest, id).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			return apperrors.NewInternal(codes.DatabaseNotFound, fmt.Sprintf("记录未找到: ID=%v", id)).Build()
		}
		return apperrors.NewInternal(codes.DatabaseQuery, "查询记录失败").Wrap(err).Build()
	}
	return nil
}

// Count 统计记录数量
func (gda *GormDataAccess) Count(ctx context.Context, model interface{}, conditions ...interface{}) (int64, error) {
	var count int64
	query := gda.db.WithContext(ctx).Model(model)
	if len(conditions) > 0 {
		query = query.Where(conditions[0], conditions[1:]...)
	}

	if err := query.Count(&count).Error; err != nil {
		return 0, apperrors.NewInternal(codes.DatabaseQuery, "统计记录数量失败").Wrap(err).Build()
	}
	return count, nil
}

// Exists 检查记录是否存在
func (gda *GormDataAccess) Exists(ctx context.Context, model interface{}, conditions ...interface{}) (bool, error) {
	count, err := gda.Count(ctx, model, conditions...)
	if err != nil {
		return false, err
	}
	return count > 0, nil
}

// CreateInBatches 批量创建记录
func (gda *GormDataAccess) CreateInBatches(ctx context.Context, models interface{}, batchSize int) error {
	if err := gda.db.WithContext(ctx).CreateInBatches(models, batchSize).Error; err != nil {
		return apperrors.NewInternal(codes.DatabaseQuery, "批量创建记录失败").Wrap(err).Build()
	}
	return nil
}

// UpdateInBatches 批量更新记录
func (gda *GormDataAccess) UpdateInBatches(ctx context.Context, model interface{}, updates interface{}, batchSize int) error {
	if err := gda.db.WithContext(ctx).Model(model).Updates(updates).Error; err != nil {
		return apperrors.NewInternal(codes.DatabaseQuery, "批量更新记录失败").Wrap(err).Build()
	}
	return nil
}

// DeleteInBatches 批量删除记录
func (gda *GormDataAccess) DeleteInBatches(ctx context.Context, model interface{}, conditions interface{}, batchSize int) error {
	query := gda.db.WithContext(ctx).Where(conditions)
	if err := query.Delete(model).Error; err != nil {
		return apperrors.NewInternal(codes.DatabaseQuery, "批量删除记录失败").Wrap(err).Build()
	}
	return nil
}
