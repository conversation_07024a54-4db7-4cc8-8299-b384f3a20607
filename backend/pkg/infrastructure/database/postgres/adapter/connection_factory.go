package adapter

import (
	"database/sql"
	"fmt"
	"time"

	apperrors "backend/pkg/common/errors"
	"backend/pkg/common/errors/codes"
	"backend/pkg/infrastructure/config"
	database "backend/pkg/infrastructure/database/abstraction"

	"gorm.io/driver/postgres"
	"gorm.io/gorm"
	"gorm.io/gorm/logger"
	"gorm.io/gorm/schema"
)

// PostgreSQLConnectionFactory PostgreSQL连接工厂
type PostgreSQLConnectionFactory struct {
	config       *config.DatabaseConfig
	pathResolver *PathResolver
}

// NewPostgreSQLConnectionFactory 创建PostgreSQL连接工厂
func NewPostgreSQLConnectionFactory(config *config.DatabaseConfig) database.ConnectionFactory {
	return &PostgreSQLConnectionFactory{
		config:       config,
		pathResolver: NewPathResolver(),
	}
}

// CreateConnection 创建数据库连接
func (f *PostgreSQLConnectionFactory) CreateConnection() (database.Connection, error) {
	// 构建DSN
	dsn := f.buildDSN()

	// 配置GORM
	gormConfig := f.buildGormConfig()

	// 打开数据库连接
	db, err := gorm.Open(postgres.Open(dsn), gormConfig)
	if err != nil {
		return nil, apperrors.NewInternal(codes.DatabaseConnection, "创建数据库连接失败").Wrap(err).Build()
	}

	// 获取底层SQL连接
	sqlDB, err := db.DB()
	if err != nil {
		return nil, apperrors.NewInternal(codes.DatabaseConnection, "获取底层SQL连接失败").Wrap(err).Build()
	}

	// 配置连接池
	if err := f.configureConnectionPool(sqlDB); err != nil {
		return nil, apperrors.NewInternal(codes.DatabaseConnection, "数据库连接池配置失败").Wrap(err).Build()
	}

	// 测试连接
	if err := sqlDB.Ping(); err != nil {
		return nil, apperrors.NewInternal(codes.DatabaseConnection, "数据库连接测试失败").Wrap(err).Build()
	}

	return NewPostgreSQLConnection(db, sqlDB), nil
}

// CreateMigrator 创建迁移管理器
func (f *PostgreSQLConnectionFactory) CreateMigrator(connection database.Connection) (database.Migrator, error) {
	// 解析迁移文件路径
	if err := f.pathResolver.ResolveProjectRoot(); err != nil {
		return nil, apperrors.NewInternal(codes.FileSystem, "解析迁移文件路径失败").Wrap(err).Build()
	}

	migrationsDir := f.pathResolver.GetMigrationsDir()

	// 获取SQL连接
	sqlDB := connection.GetSQLDB()
	if sqlDB == nil {
		return nil, apperrors.NewInternal(codes.DatabaseConnection, "无法获取SQL连接").Build()
	}

	return NewPostgreSQLMigrator(sqlDB, migrationsDir), nil
}

// buildDSN 构建数据库连接字符串
func (f *PostgreSQLConnectionFactory) buildDSN() string {
	sslMode := f.config.SSLMode
	if sslMode == "" {
		sslMode = "disable"
	}

	return fmt.Sprintf(
		"host=%s port=%d user=%s password=%s dbname=%s sslmode=%s TimeZone=UTC",
		f.config.Host,
		f.config.Port,
		f.config.Username,
		f.config.Password,
		f.config.Database,
		sslMode,
	)
}

// buildGormConfig 构建GORM配置
func (f *PostgreSQLConnectionFactory) buildGormConfig() *gorm.Config {
	config := &gorm.Config{
		// 禁用外键约束检查（在迁移中处理）
		DisableForeignKeyConstraintWhenMigrating: true,

		// 命名策略
		NamingStrategy: schema.NamingStrategy{
			TablePrefix:   "",
			SingularTable: false,
		},
	}

	// 根据环境配置日志级别
	if f.pathResolver.IsTestMode() {
		// 测试模式：静默日志
		config.Logger = logger.Default.LogMode(logger.Silent)
	} else {
		// 生产模式：仅错误日志
		config.Logger = logger.Default.LogMode(logger.Error)
	}

	return config
}

// configureConnectionPool 配置连接池
func (f *PostgreSQLConnectionFactory) configureConnectionPool(sqlDB *sql.DB) error {
	// 设置最大打开连接数
	if f.config.MaxOpenConns > 0 {
		sqlDB.SetMaxOpenConns(f.config.MaxOpenConns)
	} else {
		sqlDB.SetMaxOpenConns(25) // 默认值
	}

	// 设置最大空闲连接数
	if f.config.MaxIdleConns > 0 {
		sqlDB.SetMaxIdleConns(f.config.MaxIdleConns)
	} else {
		sqlDB.SetMaxIdleConns(5) // 默认值
	}

	// 设置连接最大生存时间
	if f.config.ConnMaxLifetime > 0 {
		sqlDB.SetConnMaxLifetime(time.Duration(f.config.ConnMaxLifetime) * time.Second)
	} else {
		sqlDB.SetConnMaxLifetime(time.Hour) // 默认1小时
	}

	// 设置连接最大空闲时间
	if f.config.ConnMaxIdleTime > 0 {
		sqlDB.SetConnMaxIdleTime(time.Duration(f.config.ConnMaxIdleTime) * time.Second)
	} else {
		sqlDB.SetConnMaxIdleTime(time.Minute * 30) // 默认30分钟
	}

	return nil
}

// PostgreSQLConnection PostgreSQL连接实现
type PostgreSQLConnection struct {
	gormDB *gorm.DB
	sqlDB  *sql.DB
}

// NewPostgreSQLConnection 创建PostgreSQL连接
func NewPostgreSQLConnection(gormDB *gorm.DB, sqlDB *sql.DB) database.Connection {
	return &PostgreSQLConnection{
		gormDB: gormDB,
		sqlDB:  sqlDB,
	}
}

// GetDB 获取GORM数据库实例
func (c *PostgreSQLConnection) GetDB() *gorm.DB {
	return c.gormDB
}

// GetSQLDB 获取原生SQL数据库实例
func (c *PostgreSQLConnection) GetSQLDB() *sql.DB {
	return c.sqlDB
}

// Close 关闭数据库连接
func (c *PostgreSQLConnection) Close() error {
	if c.sqlDB != nil {
		return c.sqlDB.Close()
	}
	return nil
}

// Ping 测试数据库连接
func (c *PostgreSQLConnection) Ping() error {
	if c.sqlDB != nil {
		return c.sqlDB.Ping()
	}
	return apperrors.NewInternal(codes.DatabaseConnection, "SQL连接未初始化").Build()
}

// HealthCheck 健康检查
func (c *PostgreSQLConnection) HealthCheck() error {
	if err := c.Ping(); err != nil {
		return apperrors.NewInternal(codes.DatabaseConnection, "数据库健康检查失败").Wrap(err).Build()
	}

	// 执行简单查询验证连接可用性
	var result int
	if err := c.gormDB.Raw("SELECT 1").Scan(&result).Error; err != nil {
		return apperrors.NewInternal(codes.DatabaseConnection, "数据库健康检查测试失败").Wrap(err).Build()
	}

	return nil
}

// Stats 获取连接池统计信息
func (c *PostgreSQLConnection) Stats() sql.DBStats {
	if c.sqlDB != nil {
		return c.sqlDB.Stats()
	}
	return sql.DBStats{}
}
