package adapter

import (
	apperrors "backend/pkg/common/errors"
	"backend/pkg/common/errors/codes"
	"backend/pkg/infrastructure/config"
	database "backend/pkg/infrastructure/database/abstraction"
)

// DatabaseManager 标准数据库管理器
type DatabaseManager struct {
	factory            database.ConnectionFactory
	connection         database.Connection
	migrator           database.Migrator
	dataAccess         database.DataAccess
	queryBuilder       database.QueryBuilder
	transactionManager database.TransactionManager
	config             *config.DatabaseConfig
}

// NewDatabaseManager 创建标准数据库管理器
func NewDatabaseManager(config *config.DatabaseConfig) database.Manager {
	factory := NewPostgreSQLConnectionFactory(config)
	return &DatabaseManager{
		factory: factory,
		config:  config,
	}
}

// Initialize 初始化数据库管理器
func (m *DatabaseManager) Initialize() error {
	// 创建数据库连接
	connection, err := m.factory.CreateConnection()
	if err != nil {
		return apperrors.NewInternal(codes.DatabaseConnection, "创建数据库连接失败").Wrap(err).Build()
	}
	m.connection = connection

	// 创建迁移管理器
	migrator, err := m.factory.CreateMigrator(connection)
	if err != nil {
		return apperrors.NewInternal(codes.DatabaseMigration, "创建迁移管理器失败").Wrap(err).Build()
	}
	m.migrator = migrator

	// 创建数据访问适配器
	m.dataAccess = NewGormDataAccess(connection.GetDB())

	// 创建查询构建器
	m.queryBuilder = NewGormQueryBuilder(connection.GetDB())

	// 创建事务管理器
	m.transactionManager = NewGormTransactionManager(connection.GetDB())

	// 执行自动迁移（如果启用）
	if err := m.performAutoMigration(); err != nil {
		return apperrors.NewInternal(codes.DatabaseMigration, "自动迁移失败").Wrap(err).Build()
	}

	return nil
}

// GetConnection 获取数据库连接
func (m *DatabaseManager) GetConnection() database.Connection {
	return m.connection
}

// GetMigrator 获取迁移管理器
func (m *DatabaseManager) GetMigrator() database.Migrator {
	return m.migrator
}

// GetDataAccess 获取抽象数据访问接口
func (m *DatabaseManager) GetDataAccess() database.DataAccess {
	return m.dataAccess
}

// GetQueryBuilder 获取查询构建器
func (m *DatabaseManager) GetQueryBuilder() database.QueryBuilder {
	return m.queryBuilder
}

// GetTransactionManager 获取事务管理器
func (m *DatabaseManager) GetTransactionManager() database.TransactionManager {
	return m.transactionManager
}

// Close 关闭数据库管理器
func (m *DatabaseManager) Close() error {
	if m.connection != nil {
		return m.connection.Close()
	}
	return nil
}

// HealthCheck 健康检查
func (m *DatabaseManager) HealthCheck() error {
	if m.connection == nil {
		return apperrors.NewInternal(codes.DatabaseConnection, "数据库连接未初始化").Build()
	}
	return m.connection.HealthCheck()
}

// performAutoMigration 执行自动迁移
func (m *DatabaseManager) performAutoMigration() error {
	if m.migrator == nil {
		return nil
	}

	// 构建自动迁移配置
	autoMigrationConfig := database.AutoMigrationConfig{
		EnableAutoMigrate: m.config.Migration.AutoMigrate,
		FailOnError:       m.config.Migration.FailOnError,
		LogMigrations:     m.config.Migration.LogMigrations,
	}

	return m.migrator.PerformAutoMigration(autoMigrationConfig)
}
