package adapter

import (
	apperrors "backend/pkg/common/errors"
	"backend/pkg/common/errors/codes"
	database "backend/pkg/infrastructure/database/abstraction"

	"gorm.io/gorm"
)

// GormQueryBuilder GORM查询构建器适配器
type GormQueryBuilder struct {
	db *gorm.DB
}

// NewGormQueryBuilder 创建GORM查询构建器适配器
func NewGormQueryBuilder(db *gorm.DB) database.QueryBuilder {
	return &GormQueryBuilder{db: db}
}

// Where 添加WHERE条件
func (gqb *GormQueryBuilder) Where(query interface{}, args ...interface{}) database.QueryBuilder {
	return &GormQueryBuilder{db: gqb.db.Where(query, args...)}
}

// Or 添加OR条件
func (gqb *GormQueryBuilder) Or(query interface{}, args ...interface{}) database.QueryBuilder {
	return &GormQueryBuilder{db: gqb.db.Or(query, args...)}
}

// Not 添加NOT条件
func (gqb *GormQueryBuilder) Not(query interface{}, args ...interface{}) database.QueryBuilder {
	return &GormQueryBuilder{db: gqb.db.Not(query, args...)}
}

// Joins 添加JOIN查询
func (gqb *GormQueryBuilder) Joins(query string, args ...interface{}) database.QueryBuilder {
	return &GormQueryBuilder{db: gqb.db.Joins(query, args...)}
}

// Preload 预加载关联数据
func (gqb *GormQueryBuilder) Preload(query string, args ...interface{}) database.QueryBuilder {
	return &GormQueryBuilder{db: gqb.db.Preload(query, args...)}
}

// Order 添加排序
func (gqb *GormQueryBuilder) Order(value interface{}) database.QueryBuilder {
	return &GormQueryBuilder{db: gqb.db.Order(value)}
}

// Group 添加分组
func (gqb *GormQueryBuilder) Group(name string) database.QueryBuilder {
	return &GormQueryBuilder{db: gqb.db.Group(name)}
}

// Having 添加HAVING条件
func (gqb *GormQueryBuilder) Having(query interface{}, args ...interface{}) database.QueryBuilder {
	return &GormQueryBuilder{db: gqb.db.Having(query, args...)}
}

// Limit 设置查询数量限制
func (gqb *GormQueryBuilder) Limit(limit int) database.QueryBuilder {
	return &GormQueryBuilder{db: gqb.db.Limit(limit)}
}

// Offset 设置查询偏移量
func (gqb *GormQueryBuilder) Offset(offset int) database.QueryBuilder {
	return &GormQueryBuilder{db: gqb.db.Offset(offset)}
}

// Find 执行查询并获取结果
func (gqb *GormQueryBuilder) Find(dest interface{}) error {
	if err := gqb.db.Find(dest).Error; err != nil {
		return apperrors.NewInternal(codes.DatabaseQuery, "查询失败").Wrap(err).Build()
	}
	return nil
}

// First 查询第一条记录
func (gqb *GormQueryBuilder) First(dest interface{}) error {
	if err := gqb.db.First(dest).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			return apperrors.NewInternal(codes.DatabaseNotFound, "记录未找到").Build()
		}
		return apperrors.NewInternal(codes.DatabaseQuery, "查询失败").Wrap(err).Build()
	}
	return nil
}

// Count 统计记录数量
func (gqb *GormQueryBuilder) Count(count *int64) error {
	if err := gqb.db.Count(count).Error; err != nil {
		return apperrors.NewInternal(codes.DatabaseQuery, "统计失败").Wrap(err).Build()
	}
	return nil
}

// Pluck 查询指定列的值
func (gqb *GormQueryBuilder) Pluck(column string, dest interface{}) error {
	if err := gqb.db.Pluck(column, dest).Error; err != nil {
		return apperrors.NewInternal(codes.DatabaseQuery, "查询失败").Wrap(err).Build()
	}
	return nil
}

// Sum 计算总和
func (gqb *GormQueryBuilder) Sum(column string) (float64, error) {
	var result struct {
		Sum float64
	}

	if err := gqb.db.Select("SUM(?) as sum", column).Scan(&result).Error; err != nil {
		return 0, apperrors.NewInternal(codes.DatabaseQuery, "查询失败").Wrap(err).Build()
	}
	return result.Sum, nil
}

// Avg 计算平均值
func (gqb *GormQueryBuilder) Avg(column string) (float64, error) {
	var result struct {
		Avg float64
	}

	if err := gqb.db.Select("AVG(?) as avg", column).Scan(&result).Error; err != nil {
		return 0, apperrors.NewInternal(codes.DatabaseQuery, "查询失败").Wrap(err).Build()
	}
	return result.Avg, nil
}

// Min 计算最小值
func (gqb *GormQueryBuilder) Min(column string) (interface{}, error) {
	var result interface{}

	if err := gqb.db.Select("MIN(?)", column).Scan(&result).Error; err != nil {
		return nil, apperrors.NewInternal(codes.DatabaseQuery, "查询失败").Wrap(err).Build()
	}
	return result, nil
}

// Max 计算最大值
func (gqb *GormQueryBuilder) Max(column string) (interface{}, error) {
	var result interface{}

	if err := gqb.db.Select("MAX(?)", column).Scan(&result).Error; err != nil {
		return nil, apperrors.NewInternal(codes.DatabaseQuery, "查询失败").Wrap(err).Build()
	}
	return result, nil
}

// Raw 执行原始SQL查询
func (gqb *GormQueryBuilder) Raw(sql string, values ...interface{}) database.QueryBuilder {
	return &GormQueryBuilder{db: gqb.db.Raw(sql, values...)}
}

// Exec 执行原始SQL
func (gqb *GormQueryBuilder) Exec(sql string, values ...interface{}) error {
	if err := gqb.db.Exec(sql, values...).Error; err != nil {
		return apperrors.NewInternal(codes.DatabaseQuery, "执行SQL失败").Wrap(err).Build()
	}
	return nil
}
