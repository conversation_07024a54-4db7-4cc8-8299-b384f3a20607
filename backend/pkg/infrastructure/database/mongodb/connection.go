package mongodb

import (
	"context"
	"database/sql"
	"fmt"
	"time"

	database "backend/pkg/infrastructure/database/abstraction"

	"go.mongodb.org/mongo-driver/mongo"
	"go.mongodb.org/mongo-driver/mongo/options"
	"gorm.io/gorm"
)

// Connection MongoDB连接实现
type Connection struct {
	client *mongo.Client
	db     *mongo.Database
	config database.ConnectionConfig
}

// NewConnection 创建MongoDB连接
func NewConnection(config database.ConnectionConfig) (*Connection, error) {
	if config.GetDriver() != "mongodb" {
		return nil, fmt.Errorf("不支持的数据库驱动: %s", config.GetDriver())
	}

	// 创建MongoDB客户端选项
	clientOptions := options.Client().ApplyURI(config.GetDSN())

	// 设置连接池配置
	if maxPool := config.GetMaxOpenConns(); maxPool > 0 {
		clientOptions.SetMaxPoolSize(uint64(maxPool))
	}

	if minPool := config.GetMaxIdleConns(); minPool > 0 {
		clientOptions.SetMinPoolSize(uint64(minPool))
	}

	if maxIdleTime := config.GetConnMaxIdleTime(); maxIdleTime > 0 {
		clientOptions.SetMaxConnIdleTime(maxIdleTime)
	}

	// 创建连接
	ctx, cancel := context.WithTimeout(context.Background(), 10*time.Second)
	defer cancel()

	client, err := mongo.Connect(ctx, clientOptions)
	if err != nil {
		return nil, fmt.Errorf("连接MongoDB失败: %w", err)
	}

	// 测试连接
	if err := client.Ping(ctx, nil); err != nil {
		return nil, fmt.Errorf("MongoDB连接测试失败: %w", err)
	}

	// 获取数据库实例
	dbName := extractDatabaseName(config.GetDSN())
	db := client.Database(dbName)

	return &Connection{
		client: client,
		db:     db,
		config: config,
	}, nil
}

// extractDatabaseName 从DSN中提取数据库名称
func extractDatabaseName(dsn string) string {
	// 简化实现，实际应该解析MongoDB URI
	return "erp_main"
}

// GetDB 获取GORM数据库实例（MongoDB不支持GORM）
func (c *Connection) GetDB() *gorm.DB {
	// MongoDB不支持GORM，返回nil
	return nil
}

// GetSQLDB 获取原生SQL数据库实例（MongoDB不支持SQL）
func (c *Connection) GetSQLDB() *sql.DB {
	// MongoDB不支持SQL，返回nil
	return nil
}

// GetMongoClient 获取MongoDB客户端
func (c *Connection) GetMongoClient() *mongo.Client {
	return c.client
}

// GetMongoDatabase 获取MongoDB数据库实例
func (c *Connection) GetMongoDatabase() *mongo.Database {
	return c.db
}

// Close 关闭数据库连接
func (c *Connection) Close() error {
	if c.client != nil {
		ctx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
		defer cancel()
		return c.client.Disconnect(ctx)
	}
	return nil
}

// Ping 测试数据库连接
func (c *Connection) Ping() error {
	if c.client != nil {
		ctx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
		defer cancel()
		return c.client.Ping(ctx, nil)
	}
	return fmt.Errorf("MongoDB客户端未初始化")
}

// Stats 获取连接池统计信息（MongoDB不支持sql.DBStats）
func (c *Connection) Stats() sql.DBStats {
	// MongoDB不支持sql.DBStats，返回空结构
	return sql.DBStats{}
}

// HealthCheck 健康检查
func (c *Connection) HealthCheck() error {
	ctx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
	defer cancel()

	return c.client.Ping(ctx, nil)
}
