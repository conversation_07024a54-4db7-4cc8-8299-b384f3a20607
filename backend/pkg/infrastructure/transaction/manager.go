package transaction

import (
	"context"

	"gorm.io/gorm"
)

// Manager 事务管理器接口
type Manager interface {
	// WithTransaction 在事务中执行操作
	WithTransaction(ctx context.Context, fn func(tx *gorm.DB) error) error
}

// GormTransactionManager GORM事务管理器实现
type GormTransactionManager struct {
	db *gorm.DB
}

// NewGormTransactionManager 创建新的GORM事务管理器
func NewGormTransactionManager(db *gorm.DB) Manager {
	return &GormTransactionManager{db: db}
}

// WithTransaction 在事务中执行操作
func (m *GormTransactionManager) WithTransaction(ctx context.Context, fn func(tx *gorm.DB) error) error {
	return m.db.WithContext(ctx).Transaction(fn)
}
