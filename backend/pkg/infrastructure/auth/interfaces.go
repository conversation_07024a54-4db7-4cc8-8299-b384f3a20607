package auth

import (
	"context"
	"time"

	"backend/internal/domain/auth/valueobject"
)

// JWTManager 定义JWT操作接口
// 这是基础设施层的JWT管理器接口，负责JWT令牌的生成、验证等操作
type JWTManager interface {
	// GenerateAccessToken 生成访问令牌
	GenerateAccessToken(userID, tenantID string, roles []string) (string, time.Time, error)

	// GenerateRefreshToken 生成刷新令牌
	GenerateRefreshToken(userID, tenantID string) (string, time.Time, error)

	// GeneratePreAuthToken 生成预认证令牌
	GeneratePreAuthToken(userID string) (string, time.Time, error)

	// ValidateToken 验证令牌并返回声明
	ValidateToken(tokenString string) (*valueobject.JWTClaims, error)

	// ExtractClaims 提取令牌声明（不验证签名）
	ExtractClaims(tokenString string) (*valueobject.JWTClaims, error)

	// ExtractJTI 从token中提取JTI
	ExtractJTI(tokenString string) (string, error)

	// GetTokenExpiration 获取令牌过期时间
	GetTokenExpiration(tokenString string) (time.Time, error)
}

// CasbinManager 定义权限管理接口
// 这是基础设施层的权限管理器接口，负责基于RBAC的权限控制
type CasbinManager interface {
	// CheckPermission 检查权限
	CheckPermission(ctx context.Context, subject, domain, object, action string) (bool, error)

	// AddPolicy 添加策略
	AddPolicy(ctx context.Context, subject, domain, object, action string) error

	// RemovePolicy 移除策略
	RemovePolicy(ctx context.Context, subject, domain, object, action string) error

	// AddRoleForUser 为用户添加角色
	AddRoleForUser(ctx context.Context, user, role, domain string) error

	// DeleteRoleForUser 删除用户角色
	DeleteRoleForUser(ctx context.Context, user, role, domain string) error

	// GetRolesForUser 获取用户角色
	GetRolesForUser(ctx context.Context, user, domain string) ([]string, error)

	// GetUsersForRole 获取角色的用户
	GetUsersForRole(ctx context.Context, role, domain string) ([]string, error)

	// GetPermissionsForUser 获取用户权限
	GetPermissionsForUser(ctx context.Context, user, domain string) ([][]string, error)

	// LoadPolicy 重新加载策略
	LoadPolicy() error

	// SavePolicy 保存策略
	SavePolicy() error
}
