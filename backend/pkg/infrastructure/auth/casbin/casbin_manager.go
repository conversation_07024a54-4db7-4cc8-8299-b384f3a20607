package casbin

import (
	"backend/pkg/infrastructure/auth"
	"context"
	"fmt"

	"github.com/casbin/casbin/v2"
	gormadapter "github.com/casbin/gorm-adapter/v3"
	"gorm.io/gorm"
)

// Config Casbin配置
type Config struct {
	ModelPath string `mapstructure:"model_path"`
	TableName string `mapstructure:"table_name"`
}

// Manager Casbin管理器
type Manager struct {
	enforcer *casbin.Enforcer
	adapter  *gormadapter.Adapter
}

// 确保Manager实现auth.CasbinManager接口
var _ auth.CasbinManager = (*Manager)(nil)

// NewManager 创建Casbin管理器
func NewManager(db *gorm.DB, config *Config) (*Manager, error) {
	// 创建GORM适配器 - 使用自定义表名
	adapter, err := gormadapter.NewAdapterByDBWithCustomTable(db, &gormadapter.CasbinRule{}, config.TableName)
	if err != nil {
		return nil, fmt.Errorf("创建Casbin适配器失败: %w", err)
	}

	// 创建Enforcer
	enforcer, err := casbin.NewEnforcer(config.ModelPath, adapter)
	if err != nil {
		return nil, fmt.Errorf("创建Casbin执行器失败: %w", err)
	}

	// 启用日志
	enforcer.EnableLog(true)

	// 加载策略
	if err := enforcer.LoadPolicy(); err != nil {
		return nil, fmt.Errorf("加载Casbin策略失败: %w", err)
	}

	return &Manager{
		enforcer: enforcer,
		adapter:  adapter,
	}, nil
}

// CheckPermission 检查权限
func (m *Manager) CheckPermission(ctx context.Context, subject, domain, object, action string) (bool, error) {
	result, err := m.enforcer.Enforce(subject, domain, object, action)
	if err != nil {
		return false, fmt.Errorf("权限检查失败: %w", err)
	}
	return result, nil
}

// AddPolicy 添加策略
func (m *Manager) AddPolicy(ctx context.Context, subject, domain, object, action string) error {
	added, err := m.enforcer.AddPolicy(subject, domain, object, action)
	if err != nil {
		return fmt.Errorf("添加策略失败: %w", err)
	}
	if !added {
		return fmt.Errorf("策略已存在")
	}
	return m.enforcer.SavePolicy()
}

// RemovePolicy 移除策略
func (m *Manager) RemovePolicy(ctx context.Context, subject, domain, object, action string) error {
	removed, err := m.enforcer.RemovePolicy(subject, domain, object, action)
	if err != nil {
		return fmt.Errorf("移除策略失败: %w", err)
	}
	if !removed {
		return fmt.Errorf("策略不存在")
	}
	return m.enforcer.SavePolicy()
}

// AddRoleForUser 为用户添加角色
func (m *Manager) AddRoleForUser(ctx context.Context, user, role, domain string) error {
	added, err := m.enforcer.AddRoleForUserInDomain(user, role, domain)
	if err != nil {
		return fmt.Errorf("为用户添加角色失败: %w", err)
	}
	if !added {
		return fmt.Errorf("角色分配已存在")
	}
	return m.enforcer.SavePolicy()
}

// DeleteRoleForUser 删除用户角色
func (m *Manager) DeleteRoleForUser(ctx context.Context, user, role, domain string) error {
	deleted, err := m.enforcer.DeleteRoleForUserInDomain(user, role, domain)
	if err != nil {
		return fmt.Errorf("为用户删除角色失败: %w", err)
	}
	if !deleted {
		return fmt.Errorf("角色分配不存在")
	}
	return m.enforcer.SavePolicy()
}

// GetRolesForUser 获取用户角色
func (m *Manager) GetRolesForUser(ctx context.Context, user, domain string) ([]string, error) {
	roles := m.enforcer.GetRolesForUserInDomain(user, domain)
	return roles, nil
}

// GetUsersForRole 获取角色的用户
func (m *Manager) GetUsersForRole(ctx context.Context, role, domain string) ([]string, error) {
	users := m.enforcer.GetUsersForRoleInDomain(role, domain)
	return users, nil
}

// GetPermissionsForUser 获取用户权限
func (m *Manager) GetPermissionsForUser(ctx context.Context, user, domain string) ([][]string, error) {
	permissions := m.enforcer.GetPermissionsForUserInDomain(user, domain)
	return permissions, nil
}

// GetPoliciesForDomain 获取域的所有策略
func (m *Manager) GetPoliciesForDomain(ctx context.Context, domain string) ([][]string, error) {
	allPolicies, err := m.enforcer.GetPolicy()
	if err != nil {
		return nil, fmt.Errorf("获取策略失败: %w", err)
	}
	var domainPolicies [][]string

	for _, policy := range allPolicies {
		if len(policy) >= 2 && policy[1] == domain {
			domainPolicies = append(domainPolicies, policy)
		}
	}

	return domainPolicies, nil
}

// LoadPolicy 重新加载策略
func (m *Manager) LoadPolicy() error {
	return m.enforcer.LoadPolicy()
}

// SavePolicy 保存策略
func (m *Manager) SavePolicy() error {
	return m.enforcer.SavePolicy()
}

// ClearPolicy 清空策略
func (m *Manager) ClearPolicy() error {
	m.enforcer.ClearPolicy()
	return m.enforcer.SavePolicy()
}

// GetEnforcer 获取Enforcer实例（用于高级操作）
func (m *Manager) GetEnforcer() *casbin.Enforcer {
	return m.enforcer
}
