package jwt

import (
	"context"
	"errors"
	"fmt"
	"time"

	"backend/internal/domain/auth/repository"
	"backend/internal/domain/auth/valueobject"
	apperrors "backend/pkg/common/errors"
	"backend/pkg/common/errors/codes"
	"backend/pkg/infrastructure/auth"
	"backend/pkg/infrastructure/config"
	"backend/pkg/infrastructure/logger"
	"backend/pkg/infrastructure/snowflake"

	"github.com/golang-jwt/jwt/v5"
)

var (
	ErrInvalidToken = errors.New("无效的令牌")
	ErrExpiredToken = errors.New("令牌已过期")
	ErrTokenClaims  = errors.New("无效的令牌声明")
)

// Config JWT配置
type Config struct {
	SecretKey            string        `mapstructure:"secret_key"`
	AccessTokenDuration  time.Duration `mapstructure:"access_token_duration"`
	RefreshTokenDuration time.Duration `mapstructure:"refresh_token_duration"`
	Issuer               string        `mapstructure:"issuer"`
}

// Manager JWT管理器
type Manager struct {
	config         *config.JWTConfig
	logger         logger.Logger
	snowflakeGen   *snowflake.Generator
	tokenCacheRepo repository.TokenCacheRepository
}

// 确保Manager实现auth.JWTManager接口
var _ auth.JWTManager = (*Manager)(nil)

// NewManager 创建JWT管理器
func NewManager(cfg *config.JWTConfig, logger logger.Logger, snowflakeGen *snowflake.Generator, tokenCacheRepo repository.TokenCacheRepository) *Manager {
	return &Manager{
		config:         cfg,
		logger:         logger,
		snowflakeGen:   snowflakeGen,
		tokenCacheRepo: tokenCacheRepo,
	}
}

// GenerateTokens 生成访问令牌和刷新令牌
func (m *Manager) GenerateTokens(ctx context.Context, userID, tenantID string, roles []string, deviceID, ipAddress, userAgent string) (accessToken, refreshToken string, err error) {
	// 生成访问令牌
	accessJTI := m.generateJTI()
	accessClaims := valueobject.NewJWTClaims(userID, tenantID, roles, m.config.AccessTokenDuration, accessJTI, valueobject.TokenTypeAccess)
	accessToken, err = m.generateToken(accessClaims)
	if err != nil {
		m.logger.Error(ctx, "生成访问令牌失败", "user_id", userID, "tenant_id", tenantID, "error", err)
		return "", "", apperrors.NewInternal(codes.Security, "生成访问令牌失败").Wrap(err).Build()
	}

	// 生成刷新令牌
	refreshJTI := m.generateJTI()
	refreshClaims := valueobject.NewJWTClaims(userID, tenantID, []string{"refresh"}, m.config.RefreshTokenDuration, refreshJTI, valueobject.TokenTypeRefresh)
	refreshToken, err = m.generateToken(refreshClaims)
	if err != nil {
		m.logger.Error(ctx, "生成刷新令牌失败", "user_id", userID, "tenant_id", tenantID, "error", err)
		return "", "", apperrors.NewInternal(codes.Security, "生成刷新令牌失败").Wrap(err).Build()
	}

	// 保存Token信息到缓存
	if err := m.saveTokenToCache(ctx, accessJTI, accessToken, userID, tenantID, deviceID, "access", accessClaims.ExpiresAt.Time, ipAddress, userAgent); err != nil {
		m.logger.Warn(ctx, "保存访问令牌到缓存失败", "jti", accessJTI, "error", err)
	}

	if err := m.saveTokenToCache(ctx, refreshJTI, refreshToken, userID, tenantID, deviceID, "refresh", refreshClaims.ExpiresAt.Time, ipAddress, userAgent); err != nil {
		m.logger.Warn(ctx, "保存刷新令牌到缓存失败", "jti", refreshJTI, "error", err)
	}

	// 添加到用户Token集合
	if err := m.tokenCacheRepo.AddUserToken(ctx, userID, tenantID, accessJTI); err != nil {
		m.logger.Warn(ctx, "添加访问令牌到用户集合失败", "jti", accessJTI, "error", err)
	}

	if err := m.tokenCacheRepo.AddUserToken(ctx, userID, tenantID, refreshJTI); err != nil {
		m.logger.Warn(ctx, "添加刷新令牌到用户集合失败", "jti", refreshJTI, "error", err)
	}

	return accessToken, refreshToken, nil
}

// GeneratePreAuthTokenWithCache 生成预认证令牌（带缓存管理）
func (m *Manager) GeneratePreAuthTokenWithCache(ctx context.Context, userID string, deviceID, ipAddress, userAgent string) (string, error) {
	jti := m.generateJTI()
	// 预认证令牌5分钟过期
	preAuthDuration := 5 * time.Minute
	// 预认证token不需要在roles中设置特殊值，TokenType已经标识了类型
	claims := valueobject.NewJWTClaims(userID, "", nil, preAuthDuration, jti, valueobject.TokenTypePreAuth)

	token, err := m.generateToken(claims)
	if err != nil {
		m.logger.Error(ctx, "生成预认证令牌失败", "user_id", userID, "error", err)
		return "", apperrors.NewInternal(codes.Security, "生成预认证令牌失败").Wrap(err).Build()
	}

	// 保存Token信息到缓存
	if err := m.saveTokenToCache(ctx, jti, token, userID, "", deviceID, "pre-auth", claims.ExpiresAt.Time, ipAddress, userAgent); err != nil {
		m.logger.Warn(ctx, "保存预认证令牌到缓存失败", "jti", jti, "error", err)
	}

	return token, nil
}

// ValidateToken 验证令牌
func (m *Manager) ValidateToken(tokenString string) (*valueobject.JWTClaims, error) {
	// 解析JWT
	token, err := jwt.ParseWithClaims(tokenString, &valueobject.JWTClaims{}, func(token *jwt.Token) (interface{}, error) {
		if _, ok := token.Method.(*jwt.SigningMethodHMAC); !ok {
			return nil, apperrors.NewInternal(codes.Security, "JWT签名方法无效").Build()
		}
		return []byte(m.config.SecretKey), nil
	})

	if err != nil {
		return nil, apperrors.NewInternal(codes.Security, "解析令牌失败").Wrap(err).Build()
	}

	claims, ok := token.Claims.(*valueobject.JWTClaims)
	if !ok || !token.Valid {
		return nil, apperrors.NewInternal(codes.Security, "无效的令牌声明").Build()
	}

	// 验证声明有效性
	if !claims.IsValid() {
		return nil, apperrors.NewInternal(codes.Security, "令牌声明无效").Build()
	}

	// 检查黑名单（如果有JTI）
	if claims.JTI != "" && m.tokenCacheRepo != nil {
		ctx := context.Background() // 这里可以考虑传入context
		isBlacklisted, err := m.tokenCacheRepo.IsTokenBlacklisted(ctx, claims.JTI)
		if err != nil {
			m.logger.Warn(ctx, "检查黑名单失败", "jti", claims.JTI, "error", err)
			// 不阻止验证，但记录警告
		} else if isBlacklisted {
			return nil, apperrors.NewInternal(codes.Security, "令牌已被撤销").Build()
		}
	}

	return claims, nil
}

// RevokeToken 撤销令牌
func (m *Manager) RevokeToken(ctx context.Context, tokenString string) error {
	// 解析令牌获取JTI
	claims, err := m.ValidateToken(tokenString)
	if err != nil {
		// 即使令牌无效，我们也尝试解析JTI
		token, parseErr := jwt.ParseWithClaims(tokenString, &valueobject.JWTClaims{}, func(token *jwt.Token) (interface{}, error) {
			return []byte(m.config.SecretKey), nil
		})
		if parseErr != nil {
			return apperrors.NewInternal(codes.Security, "无法解析令牌").Wrap(parseErr).Build()
		}

		if parsedClaims, ok := token.Claims.(*valueobject.JWTClaims); ok && parsedClaims.JTI != "" {
			claims = parsedClaims
		} else {
			return apperrors.NewInternal(codes.Security, "令牌中没有JTI").Build()
		}
	}

	if claims.JTI == "" {
		return apperrors.NewInternal(codes.Security, "令牌中没有JTI，无法撤销").Build()
	}

	// 添加到黑名单
	if err := m.tokenCacheRepo.AddToBlacklist(ctx, claims.JTI, claims.ExpiresAt.Time); err != nil {
		m.logger.Error(ctx, "添加令牌到黑名单失败", "jti", claims.JTI, "error", err)
		return apperrors.NewInternal(codes.Security, "撤销令牌失败").Wrap(err).Build()
	}

	// 从用户Token集合中移除
	if err := m.tokenCacheRepo.RemoveUserToken(ctx, claims.UserID, claims.TenantID, claims.JTI); err != nil {
		m.logger.Warn(ctx, "从用户Token集合中移除令牌失败", "jti", claims.JTI, "error", err)
	}

	m.logger.Info(ctx, "令牌已撤销", "jti", claims.JTI, "user_id", claims.UserID, "tenant_id", claims.TenantID)
	return nil
}

// RevokeAllUserTokens 撤销用户的所有令牌
func (m *Manager) RevokeAllUserTokens(ctx context.Context, userID, tenantID string) error {
	if err := m.tokenCacheRepo.RevokeAllUserTokens(ctx, userID, tenantID); err != nil {
		m.logger.Error(ctx, "撤销用户所有令牌失败", "user_id", userID, "tenant_id", tenantID, "error", err)
		return apperrors.NewInternal(codes.Security, "撤销用户所有令牌失败").Wrap(err).Build()
	}

	m.logger.Info(ctx, "已撤销用户所有令牌", "user_id", userID, "tenant_id", tenantID)
	return nil
}

// GetUserActiveTokenCount 获取用户活跃令牌数量
func (m *Manager) GetUserActiveTokenCount(ctx context.Context, userID, tenantID string) (int64, error) {
	return m.tokenCacheRepo.GetActiveTokenCount(ctx, userID, tenantID)
}

// ExtractClaims 提取令牌声明（不验证签名，用于兼容接口）
func (m *Manager) ExtractClaims(tokenString string) (*valueobject.JWTClaims, error) {
	token, _, err := new(jwt.Parser).ParseUnverified(tokenString, &valueobject.JWTClaims{})
	if err != nil {
		return nil, apperrors.NewInternal(codes.Security, "解析令牌失败").Wrap(err).Build()
	}

	claims, ok := token.Claims.(*valueobject.JWTClaims)
	if !ok {
		return nil, apperrors.NewInternal(codes.Security, "无效的令牌声明").Build()
	}

	return claims, nil
}

// === 向后兼容的方法（实现auth.JWTManager接口） ===

// GenerateAccessToken 生成访问令牌（兼容旧接口）
func (m *Manager) GenerateAccessToken(userID, tenantID string, roles []string) (string, time.Time, error) {
	jti := m.generateJTI()
	claims := valueobject.NewJWTClaims(userID, tenantID, roles, m.config.AccessTokenDuration, jti, valueobject.TokenTypeAccess)

	token, err := m.generateToken(claims)
	if err != nil {
		return "", time.Time{}, apperrors.NewInternal(codes.Security, "生成访问令牌失败").Wrap(err).Build()
	}

	return token, claims.ExpiresAt.Time, nil
}

// GenerateRefreshToken 生成刷新令牌（兼容旧接口）
func (m *Manager) GenerateRefreshToken(userID, tenantID string) (string, time.Time, error) {
	jti := m.generateJTI()
	claims := valueobject.NewJWTClaims(userID, tenantID, []string{"refresh"}, m.config.RefreshTokenDuration, jti, valueobject.TokenTypeRefresh)

	token, err := m.generateToken(claims)
	if err != nil {
		return "", time.Time{}, apperrors.NewInternal(codes.Security, "生成刷新令牌失败").Wrap(err).Build()
	}

	return token, claims.ExpiresAt.Time, nil
}

// GeneratePreAuthToken 生成预认证令牌（兼容旧接口）
func (m *Manager) GeneratePreAuthToken(userID string) (string, time.Time, error) {
	jti := m.generateJTI()
	preAuthDuration := 5 * time.Minute
	// 预认证token不需要在roles中设置特殊值，TokenType已经标识了类型
	claims := valueobject.NewJWTClaims(userID, "", nil, preAuthDuration, jti, valueobject.TokenTypePreAuth)

	token, err := m.generateToken(claims)
	if err != nil {
		return "", time.Time{}, apperrors.NewInternal(codes.Security, "生成预认证令牌失败").Wrap(err).Build()
	}

	return token, claims.ExpiresAt.Time, nil
}

// GetTokenExpiration 获取令牌过期时间（兼容旧接口）
func (m *Manager) GetTokenExpiration(tokenString string) (time.Time, error) {
	claims, err := m.ExtractClaims(tokenString)
	if err != nil {
		return time.Time{}, err
	}

	if claims.ExpiresAt == nil {
		return time.Time{}, apperrors.NewInternal(codes.Security, "令牌没有过期时间").Build()
	}

	return claims.ExpiresAt.Time, nil
}

// === 私有方法 ===

// generateToken 生成令牌
func (m *Manager) generateToken(claims *valueobject.JWTClaims) (string, error) {
	token := jwt.NewWithClaims(jwt.SigningMethodHS256, claims)
	return token.SignedString([]byte(m.config.SecretKey))
}

// generateJTI 生成JWT唯一标识符
func (m *Manager) generateJTI() string {
	return fmt.Sprintf("jwt_%d", m.snowflakeGen.Generate())
}

// saveTokenToCache 保存Token信息到缓存
func (m *Manager) saveTokenToCache(ctx context.Context, jti, token, userID, tenantID, deviceID, tokenType string, expiresAt time.Time, ipAddress, userAgent string) error {
	if m.tokenCacheRepo == nil {
		return nil // 如果没有缓存仓储，跳过
	}

	tokenInfo := &repository.TokenInfo{
		JTI:       jti,
		Token:     token,
		UserID:    userID,
		TenantID:  tenantID,
		DeviceID:  deviceID,
		TokenType: tokenType,
		CreatedAt: time.Now(),
		ExpiresAt: expiresAt,
		IPAddress: ipAddress,
		UserAgent: userAgent,
	}

	return m.tokenCacheRepo.SaveTokenInfo(ctx, tokenInfo)
}

// ExtractJTI 从token中提取JTI
func (m *Manager) ExtractJTI(tokenString string) (string, error) {
	// 解析token但不验证有效性，只为了获取JTI
	token, err := jwt.ParseWithClaims(tokenString, &valueobject.JWTClaims{}, func(token *jwt.Token) (interface{}, error) {
		if _, ok := token.Method.(*jwt.SigningMethodHMAC); !ok {
			return nil, apperrors.NewInternal(codes.Security, "JWT签名方法无效").Build()
		}
		return []byte(m.config.SecretKey), nil
	})

	if err != nil {
		return "", apperrors.NewInternal(codes.Security, "解析令牌失败").Wrap(err).Build()
	}

	claims, ok := token.Claims.(*valueobject.JWTClaims)
	if !ok {
		return "", apperrors.NewInternal(codes.Security, "无效的令牌声明").Build()
	}

	if claims.JTI == "" {
		return "", apperrors.NewInternal(codes.Security, "令牌中缺少JTI").Build()
	}

	return claims.JTI, nil
}
