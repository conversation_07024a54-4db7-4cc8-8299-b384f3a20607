package uuid

import (
	"context"
	"strings"
	"sync"

	apperrors "backend/pkg/common/errors"
	"backend/pkg/common/errors/codes"
	uuidTypes "backend/pkg/types/uuid"

	"github.com/google/uuid"
)

// generator UUID生成器实现
type generator struct {
	config      uuidTypes.GeneratorConfig
	nsManager   uuidTypes.NamespaceManager
	strategies  map[string]uuidTypes.DomainStrategy
	builderPool sync.Pool
	mu          sync.RWMutex
}

// NewGenerator 创建新的UUID生成器
func NewGenerator(config uuidTypes.GeneratorConfig) (uuidTypes.Generator, error) {
	if err := validateConfig(config); err != nil {
		return nil, apperrors.NewInternal(codes.GeneratorConfig, "UUID配置无效").Wrap(err).Build()
	}

	// 创建命名空间管理器
	nsManager, err := NewNamespaceManager(config.RootNamespace)
	if err != nil {
		return nil, apperrors.NewInternal(codes.GeneratorConfig, "创建命名空间管理器失败").Wrap(err).Build()
	}

	// 创建字符串构建器对象池
	builderPool := sync.Pool{
		New: func() interface{} {
			return &strings.Builder{}
		},
	}

	gen := &generator{
		config:      config,
		nsManager:   nsManager,
		strategies:  make(map[string]uuidTypes.DomainStrategy),
		builderPool: builderPool,
	}

	// 注册预定义策略
	gen.registerDefaultStrategies()

	// 注册配置中的策略
	if err := gen.registerConfigStrategies(); err != nil {
		return nil, apperrors.NewInternal(codes.GeneratorConfig, "注册配置策略失败").Wrap(err).Build()
	}

	return gen, nil
}

// Generate 生成语义化UUID
func (g *generator) Generate(ctx context.Context, domain string, attrs uuidTypes.DomainAttributes) (uuid.UUID, error) {
	if domain == "" {
		return uuid.Nil, apperrors.NewInternal(codes.GeneratorInvalidArg, "领域名称不能为空").Build()
	}

	// 验证属性
	if err := g.ValidateAttributes(domain, attrs); err != nil {
		return uuid.Nil, err
	}

	// 获取命名空间
	namespaceUUID, err := g.nsManager.GetOrCreate(domain, attrs.EntityType)
	if err != nil {
		return uuid.Nil, apperrors.NewInternal(codes.GeneratorInvalidArg, "创建命名空间管理器失败").Wrap(err).Build()
	}

	// 生成语义化名称
	semanticName, err := g.generateSemanticName(domain, attrs)
	if err != nil {
		return uuid.Nil, apperrors.NewInternal(codes.GeneratorInvalidArg, "生成语义化名称失败").Wrap(err).Build()
	}

	// 使用UUID v5生成确定性UUID
	generatedUUID := uuid.NewSHA1(namespaceUUID, []byte(semanticName))

	return generatedUUID, nil
}

// GenerateWithFallback 生成UUID，失败时可降级
func (g *generator) GenerateWithFallback(ctx context.Context, domain string, attrs uuidTypes.DomainAttributes) (uuid.UUID, error) {
	// 尝试正常生成
	generatedUUID, err := g.Generate(ctx, domain, attrs)
	if err == nil {
		return generatedUUID, nil
	}

	// 如果启用了降级，使用UUID v4
	if g.config.FallbackToV4 {
		fallbackUUID := uuid.New()
		return fallbackUUID, nil
	}

	return uuid.Nil, err
}

// GetNamespace 获取命名空间UUID
func (g *generator) GetNamespace(domain string, entityType string) (uuid.UUID, error) {
	return g.nsManager.GetOrCreate(domain, entityType)
}

// ValidateAttributes 验证属性
func (g *generator) ValidateAttributes(domain string, attrs uuidTypes.DomainAttributes) error {
	if attrs.TenantID == "" {
		return apperrors.NewInternal(codes.GeneratorInvalidArg, "tenant_id不能为空").Build()
	}
	if attrs.EntityType == "" {
		return apperrors.NewInternal(codes.GeneratorInvalidArg, "entity_type不能为空").Build()
	}
	if attrs.Key == "" {
		return apperrors.NewInternal(codes.GeneratorInvalidArg, "key不能为空").Build()
	}

	// 使用策略进行验证
	g.mu.RLock()
	strategy, exists := g.strategies[domain]
	g.mu.RUnlock()

	if exists {
		return strategy.ValidateAttributes(attrs)
	}

	// 如果没有特定策略，使用基础验证
	return nil
}

// generateSemanticName 生成语义化名称
func (g *generator) generateSemanticName(domain string, attrs uuidTypes.DomainAttributes) (string, error) {
	g.mu.RLock()
	strategy, exists := g.strategies[domain]
	g.mu.RUnlock()

	if exists {
		return strategy.GenerateSemanticName(attrs)
	}

	// 使用默认策略
	return g.generateDefaultSemanticName(attrs)
}

// generateDefaultSemanticName 生成默认语义化名称
func (g *generator) generateDefaultSemanticName(attrs uuidTypes.DomainAttributes) (string, error) {
	builder := g.builderPool.Get().(*strings.Builder)
	defer func() {
		builder.Reset()
		g.builderPool.Put(builder)
	}()

	// 默认格式：key-tenant_id-entity_type
	builder.WriteString(attrs.Key)
	builder.WriteString("-")
	builder.WriteString(attrs.TenantID)
	builder.WriteString("-")
	builder.WriteString(attrs.EntityType)

	return builder.String(), nil
}

// registerDefaultStrategies 注册默认策略
func (g *generator) registerDefaultStrategies() {
	g.mu.Lock()
	defer g.mu.Unlock()

	g.strategies["user"] = NewUserStrategy()
	g.strategies["product"] = NewProductStrategy()
	g.strategies["order"] = NewOrderStrategy()
}

// registerConfigStrategies 注册配置中的策略
func (g *generator) registerConfigStrategies() error {
	g.mu.Lock()
	defer g.mu.Unlock()

	for domain, strategyConfig := range g.config.Strategies {
		// 如果已经有预定义策略，跳过
		if _, exists := g.strategies[domain]; exists {
			continue
		}

		// 创建基础策略
		g.strategies[domain] = NewBaseStrategy(strategyConfig)
	}

	return nil
}

// RegisterStrategy 注册自定义策略
func (g *generator) RegisterStrategy(domain string, strategy uuidTypes.DomainStrategy) error {
	if domain == "" {
		return apperrors.NewInternal(codes.GeneratorInvalidArg, "领域名称不能为空").Build()
	}
	if strategy == nil {
		return apperrors.NewInternal(codes.GeneratorInvalidArg, "策略不能为空").Build()
	}

	g.mu.Lock()
	defer g.mu.Unlock()

	g.strategies[domain] = strategy
	return nil
}

// GetStrategy 获取策略（用于测试）
func (g *generator) GetStrategy(domain string) (uuidTypes.DomainStrategy, bool) {
	g.mu.RLock()
	defer g.mu.RUnlock()

	strategy, exists := g.strategies[domain]
	return strategy, exists
}

// ListDomains 列出所有已注册的领域
func (g *generator) ListDomains() []string {
	g.mu.RLock()
	defer g.mu.RUnlock()

	domains := make([]string, 0, len(g.strategies))
	for domain := range g.strategies {
		domains = append(domains, domain)
	}

	return domains
}

// validateConfig 验证配置
func validateConfig(config uuidTypes.GeneratorConfig) error {
	if config.RootNamespace == "" {
		return apperrors.NewInternal(codes.GeneratorConfig, "根命名空间不能为空").Build()
	}

	if config.EnableCache && config.CacheSize <= 0 {
		return apperrors.NewInternal(codes.GeneratorConfig, "缓存大小必须大于0").Build()
	}

	// 验证策略配置
	for domain, strategyConfig := range config.Strategies {
		if domain == "" {
			return apperrors.NewInternal(codes.GeneratorConfig, "领域名称不能为空").Build()
		}

		if len(strategyConfig.RequiredAttrs) == 0 {
			return apperrors.NewInternal(codes.GeneratorConfig, "领域必须至少有一个必需属性").Build()
		}
	}

	return nil
}

// GetStats 获取生成器统计信息（用于监控）
func (g *generator) GetStats() map[string]interface{} {
	g.mu.RLock()
	defer g.mu.RUnlock()

	stats := make(map[string]interface{})
	stats["registered_domains"] = len(g.strategies)
	stats["cache_enabled"] = g.config.EnableCache
	stats["fallback_enabled"] = g.config.FallbackToV4

	// 获取命名空间缓存统计
	if nsManager, ok := g.nsManager.(*namespaceManager); ok {
		stats["namespace_cache_size"] = nsManager.GetCacheSize()
	}

	return stats
}
