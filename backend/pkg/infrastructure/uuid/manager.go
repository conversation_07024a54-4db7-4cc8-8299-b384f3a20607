package uuid

import (
	"context"
	"sync"

	apperrors "backend/pkg/common/errors"
	"backend/pkg/common/errors/codes"
	uuidTypes "backend/pkg/types/uuid"

	"github.com/google/uuid"
)

// Manager UUID管理器，提供统一的UUID生成服务
type Manager struct {
	generator uuidTypes.Generator
	mu        sync.RWMutex
}

// NewManager 创建UUID管理器
func NewManager(config uuidTypes.GeneratorConfig) (*Manager, error) {
	generator, err := NewGenerator(config)
	if err != nil {
		return nil, apperrors.NewInternal(codes.GeneratorConfig, "创建生成器失败").Wrap(err).Build()
	}

	return &Manager{
		generator: generator,
	}, nil
}

// NewManagerWithDefaults 使用默认配置创建UUID管理器
func NewManagerWithDefaults() (*Manager, error) {
	config := uuidTypes.GeneratorConfig{
		RootNamespace: "com.9wings.erp",
		FallbackToV4:  true,
		EnableCache:   true,
		CacheSize:     10000,
		Strategies: map[string]uuidTypes.StrategyConfig{
			"user": {
				RequiredAttrs:  []string{"tenant_id", "key"},
				OptionalAttrs:  []string{"username", "email"},
				AttributeOrder: []string{"username", "tenant_id", "key"},
			},
			"product": {
				RequiredAttrs:  []string{"tenant_id", "key"},
				OptionalAttrs:  []string{"sku", "category", "brand"},
				AttributeOrder: []string{"sku", "category", "brand", "tenant_id", "key"},
			},
			"order": {
				RequiredAttrs:  []string{"tenant_id", "key"},
				OptionalAttrs:  []string{"order_number", "channel"},
				AttributeOrder: []string{"order_number", "channel", "tenant_id", "key"},
			},
		},
	}

	return NewManager(config)
}

// GenerateForUser 为用户生成UUID
func (m *Manager) GenerateForUser(ctx context.Context, tenantID, userKey string, metadata map[string]interface{}) (uuid.UUID, error) {
	attrs := uuidTypes.DomainAttributes{
		TenantID:   tenantID,
		EntityType: "entity", // 用户实体
		Key:        userKey,
		Metadata:   metadata,
	}

	return m.generator.Generate(ctx, "user", attrs)
}

// GenerateForProduct 为商品生成UUID
func (m *Manager) GenerateForProduct(ctx context.Context, tenantID, productKey string, metadata map[string]interface{}) (uuid.UUID, error) {
	attrs := uuidTypes.DomainAttributes{
		TenantID:   tenantID,
		EntityType: "entity", // 商品实体
		Key:        productKey,
		Metadata:   metadata,
	}

	return m.generator.Generate(ctx, "product", attrs)
}

// GenerateForOrder 为订单生成UUID
func (m *Manager) GenerateForOrder(ctx context.Context, tenantID, orderKey string, metadata map[string]interface{}) (uuid.UUID, error) {
	attrs := uuidTypes.DomainAttributes{
		TenantID:   tenantID,
		EntityType: "entity", // 订单实体
		Key:        orderKey,
		Metadata:   metadata,
	}

	return m.generator.Generate(ctx, "order", attrs)
}

// Generate 通用生成方法
func (m *Manager) Generate(ctx context.Context, domain string, attrs uuidTypes.DomainAttributes) (uuid.UUID, error) {
	m.mu.RLock()
	defer m.mu.RUnlock()

	return m.generator.Generate(ctx, domain, attrs)
}

// GenerateWithFallback 带降级的生成方法
func (m *Manager) GenerateWithFallback(ctx context.Context, domain string, attrs uuidTypes.DomainAttributes) (uuid.UUID, error) {
	m.mu.RLock()
	defer m.mu.RUnlock()

	return m.generator.GenerateWithFallback(ctx, domain, attrs)
}

// RegisterCustomStrategy 注册自定义策略
func (m *Manager) RegisterCustomStrategy(domain string, strategy uuidTypes.DomainStrategy) error {
	m.mu.Lock()
	defer m.mu.Unlock()

	if gen, ok := m.generator.(*generator); ok {
		return gen.RegisterStrategy(domain, strategy)
	}

	return apperrors.NewInternal(codes.GeneratorInvalidArg, "生成器不支持自定义策略").Build()
}

// GetNamespace 获取命名空间
func (m *Manager) GetNamespace(domain string, entityType string) (uuid.UUID, error) {
	m.mu.RLock()
	defer m.mu.RUnlock()

	return m.generator.GetNamespace(domain, entityType)
}

// ValidateAttributes 验证属性
func (m *Manager) ValidateAttributes(domain string, attrs uuidTypes.DomainAttributes) error {
	m.mu.RLock()
	defer m.mu.RUnlock()

	return m.generator.ValidateAttributes(domain, attrs)
}

// GetSupportedDomains 获取支持的领域列表
func (m *Manager) GetSupportedDomains() []string {
	m.mu.RLock()
	defer m.mu.RUnlock()

	if gen, ok := m.generator.(*generator); ok {
		return gen.ListDomains()
	}

	return []string{}
}

// GetStats 获取统计信息
func (m *Manager) GetStats() map[string]interface{} {
	m.mu.RLock()
	defer m.mu.RUnlock()

	if gen, ok := m.generator.(*generator); ok {
		return gen.GetStats()
	}

	return make(map[string]interface{})
}

// Close 关闭管理器（清理资源）
func (m *Manager) Close() error {
	m.mu.Lock()
	defer m.mu.Unlock()

	// 清理缓存等资源
	if gen, ok := m.generator.(*generator); ok {
		if nsManager, ok := gen.nsManager.(*namespaceManager); ok {
			nsManager.Clear()
		}
	}

	return nil
}

// 全局单例实例（可选使用）
var (
	globalManager *Manager
	globalOnce    sync.Once
)

// GetGlobalManager 获取全局UUID管理器实例
func GetGlobalManager() (*Manager, error) {
	var err error
	globalOnce.Do(func() {
		globalManager, err = NewManagerWithDefaults()
	})
	return globalManager, err
}

// 便捷函数

// GenerateUserUUID 生成用户UUID（使用全局实例）
func GenerateUserUUID(ctx context.Context, tenantID, userKey string, metadata map[string]interface{}) (uuid.UUID, error) {
	manager, err := GetGlobalManager()
	if err != nil {
		return uuid.Nil, err
	}
	return manager.GenerateForUser(ctx, tenantID, userKey, metadata)
}

// GenerateProductUUID 生成商品UUID（使用全局实例）
func GenerateProductUUID(ctx context.Context, tenantID, productKey string, metadata map[string]interface{}) (uuid.UUID, error) {
	manager, err := GetGlobalManager()
	if err != nil {
		return uuid.Nil, err
	}
	return manager.GenerateForProduct(ctx, tenantID, productKey, metadata)
}

// GenerateOrderUUID 生成订单UUID（使用全局实例）
func GenerateOrderUUID(ctx context.Context, tenantID, orderKey string, metadata map[string]interface{}) (uuid.UUID, error) {
	manager, err := GetGlobalManager()
	if err != nil {
		return uuid.Nil, err
	}
	return manager.GenerateForOrder(ctx, tenantID, orderKey, metadata)
}
