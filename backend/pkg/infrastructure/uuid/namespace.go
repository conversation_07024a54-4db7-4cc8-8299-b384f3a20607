package uuid

import (
	"fmt"
	"strings"
	"sync"

	apperrors "backend/pkg/common/errors"
	"backend/pkg/common/errors/codes"
	uuidTypes "backend/pkg/types/uuid"

	"github.com/google/uuid"
)

// namespaceManager 命名空间管理器实现
type namespaceManager struct {
	rootNamespace uuid.UUID
	cache         sync.Map // map[string]uuid.UUID
	mu            sync.RWMutex
}

// NewNamespaceManager 创建新的命名空间管理器
func NewNamespaceManager(rootNamespace string) (uuidTypes.NamespaceManager, error) {
	// 解析根命名空间
	var rootUUID uuid.UUID
	var err error

	if rootNamespace == "" {
		// 使用默认的九翼ERP根命名空间
		rootUUID = uuid.MustParse("550e8400-e29b-41d4-a716-************")
	} else {
		// 尝试解析为UUID
		rootUUID, err = uuid.Parse(rootNamespace)
		if err != nil {
			// 如果不是有效的UUID，则基于字符串生成UUID v5
			rootUUID = uuid.NewSHA1(uuid.NameSpaceDNS, []byte(rootNamespace))
		}
	}

	return &namespaceManager{
		rootNamespace: rootUUID,
		cache:         sync.Map{},
	}, nil
}

// GetOrCreate 获取或创建命名空间
func (nm *namespaceManager) GetOrCreate(domain string, entityType string) (uuid.UUID, error) {
	if domain == "" {
		return uuid.Nil, apperrors.NewInternal(codes.GeneratorInvalidArg, "领域名称不能为空").Build()
	}
	if entityType == "" {
		return uuid.Nil, apperrors.NewInternal(codes.GeneratorInvalidArg, "实体类型不能为空").Build()
	}

	// 生成缓存键
	cacheKey := fmt.Sprintf("%s:%s", domain, entityType)

	// 尝试从缓存获取
	if cached, exists := nm.cache.Load(cacheKey); exists {
		return cached.(uuid.UUID), nil
	}

	// 加锁创建
	nm.mu.Lock()
	defer nm.mu.Unlock()

	// 双重检查
	if cached, exists := nm.cache.Load(cacheKey); exists {
		return cached.(uuid.UUID), nil
	}

	// 生成层次化命名空间
	namespaceUUID := nm.generateHierarchicalNamespace(domain, entityType)

	// 存入缓存
	nm.cache.Store(cacheKey, namespaceUUID)

	return namespaceUUID, nil
}

// Exists 检查命名空间是否存在
func (nm *namespaceManager) Exists(domain string, entityType string) bool {
	cacheKey := fmt.Sprintf("%s:%s", domain, entityType)
	_, exists := nm.cache.Load(cacheKey)
	return exists
}

// Clear 清空缓存
func (nm *namespaceManager) Clear() {
	nm.mu.Lock()
	defer nm.mu.Unlock()

	nm.cache.Range(func(key, value interface{}) bool {
		nm.cache.Delete(key)
		return true
	})
}

// generateHierarchicalNamespace 生成层次化命名空间
func (nm *namespaceManager) generateHierarchicalNamespace(domain string, entityType string) uuid.UUID {
	// 层次结构: 根命名空间 -> 领域命名空间 -> 实体命名空间

	// 1. 生成领域命名空间
	domainName := fmt.Sprintf("domain:%s", strings.ToLower(domain))
	domainNamespace := uuid.NewSHA1(nm.rootNamespace, []byte(domainName))

	// 2. 生成实体命名空间
	entityName := fmt.Sprintf("entity:%s", strings.ToLower(entityType))
	entityNamespace := uuid.NewSHA1(domainNamespace, []byte(entityName))

	return entityNamespace
}

// GetRootNamespace 获取根命名空间（用于测试）
func (nm *namespaceManager) GetRootNamespace() uuid.UUID {
	return nm.rootNamespace
}

// GetCacheSize 获取缓存大小（用于监控）
func (nm *namespaceManager) GetCacheSize() int {
	count := 0
	nm.cache.Range(func(key, value interface{}) bool {
		count++
		return true
	})
	return count
}
