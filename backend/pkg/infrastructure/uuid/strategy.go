package uuid

import (
	"fmt"
	"sort"
	"strings"

	apperrors "backend/pkg/common/errors"
	"backend/pkg/common/errors/codes"
	uuidTypes "backend/pkg/types/uuid"
)

// baseStrategy 基础策略实现
type baseStrategy struct {
	requiredAttrs  []string
	optionalAttrs  []string
	attributeOrder []string
}

// NewBaseStrategy 创建基础策略
func NewBaseStrategy(config uuidTypes.StrategyConfig) uuidTypes.DomainStrategy {
	return &baseStrategy{
		requiredAttrs:  config.RequiredAttrs,
		optionalAttrs:  config.OptionalAttrs,
		attributeOrder: config.AttributeOrder,
	}
}

// GenerateSemanticName 生成语义化名称
func (bs *baseStrategy) GenerateSemanticName(attrs uuidTypes.DomainAttributes) (string, error) {
	if err := bs.ValidateAttributes(attrs); err != nil {
		return "", err
	}

	// 构建属性映射
	attrMap := make(map[string]string)
	attrMap["tenant_id"] = attrs.TenantID
	attrMap["entity_type"] = attrs.EntityType
	attrMap["key"] = attrs.Key

	// 添加元数据
	for k, v := range attrs.Metadata {
		if strVal, ok := v.(string); ok {
			attrMap[k] = strVal
		}
	}

	// 按配置的顺序构建名称
	var parts []string

	// 如果有属性顺序配置，按顺序添加
	if len(bs.attributeOrder) > 0 {
		for _, attr := range bs.attributeOrder {
			if value, exists := attrMap[attr]; exists && value != "" {
				parts = append(parts, fmt.Sprintf("%s", value))
			}
		}
	} else {
		// 默认顺序：key -> tenant_id -> entity_type -> 其他
		if attrs.Key != "" {
			parts = append(parts, attrs.Key)
		}
		if attrs.TenantID != "" {
			parts = append(parts, attrs.TenantID)
		}
		if attrs.EntityType != "" {
			parts = append(parts, attrs.EntityType)
		}

		// 添加其他属性（按字母顺序）
		var otherKeys []string
		for k := range attrMap {
			if k != "key" && k != "tenant_id" && k != "entity_type" {
				otherKeys = append(otherKeys, k)
			}
		}
		sort.Strings(otherKeys)

		for _, k := range otherKeys {
			if value := attrMap[k]; value != "" {
				parts = append(parts, value)
			}
		}
	}

	return strings.Join(parts, "-"), nil
}

// ValidateAttributes 验证属性
func (bs *baseStrategy) ValidateAttributes(attrs uuidTypes.DomainAttributes) error {
	// 检查必需属性
	for _, required := range bs.requiredAttrs {
		switch required {
		case "tenant_id":
			if attrs.TenantID == "" {
				return apperrors.NewInternal(codes.GeneratorInvalidArg, "tenant_id不能为空").Build()
			}
		case "entity_type":
			if attrs.EntityType == "" {
				return apperrors.NewInternal(codes.GeneratorInvalidArg, "entity_type不能为空").Build()
			}
		case "key":
			if attrs.Key == "" {
				return apperrors.NewInternal(codes.GeneratorInvalidArg, "key不能为空").Build()
			}
		default:
			// 检查元数据中的必需属性
			if attrs.Metadata == nil {
				return apperrors.NewInternal(codes.GeneratorInvalidArg, "元数据不能为空").Build()
			}
			if value, exists := attrs.Metadata[required]; !exists || value == nil {
				return apperrors.NewInternal(codes.GeneratorInvalidArg, "元数据不能为空").Build()
			}
		}
	}

	return nil
}

// GetRequiredAttributes 获取必需属性列表
func (bs *baseStrategy) GetRequiredAttributes() []string {
	return bs.requiredAttrs
}

// GetOptionalAttributes 获取可选属性列表
func (bs *baseStrategy) GetOptionalAttributes() []string {
	return bs.optionalAttrs
}

// 预定义的领域策略

// UserStrategy 用户领域策略
type UserStrategy struct {
	*baseStrategy
}

// NewUserStrategy 创建用户策略
func NewUserStrategy() uuidTypes.DomainStrategy {
	return &UserStrategy{
		baseStrategy: &baseStrategy{
			requiredAttrs:  []string{"tenant_id", "key"},
			optionalAttrs:  []string{"email", "username"},
			attributeOrder: []string{"key", "tenant_id", "username", "email"},
		},
	}
}

// GenerateSemanticName 用户特定的语义化名称生成
func (us *UserStrategy) GenerateSemanticName(attrs uuidTypes.DomainAttributes) (string, error) {
	if err := us.ValidateAttributes(attrs); err != nil {
		return "", err
	}

	var parts []string

	// 用户策略：username/email -> tenant_id -> key
	if username, exists := attrs.Metadata["username"]; exists {
		if usernameStr, ok := username.(string); ok && usernameStr != "" {
			parts = append(parts, usernameStr)
		}
	} else if email, exists := attrs.Metadata["email"]; exists {
		if emailStr, ok := email.(string); ok && emailStr != "" {
			// 提取邮箱域名
			if atIndex := strings.LastIndex(emailStr, "@"); atIndex > 0 {
				domain := emailStr[atIndex+1:]
				parts = append(parts, domain)
			}
		}
	}

	if attrs.TenantID != "" {
		parts = append(parts, attrs.TenantID)
	}

	if attrs.Key != "" {
		parts = append(parts, attrs.Key)
	}

	return strings.Join(parts, "-"), nil
}

// ProductStrategy 商品领域策略
type ProductStrategy struct {
	*baseStrategy
}

// NewProductStrategy 创建商品策略
func NewProductStrategy() uuidTypes.DomainStrategy {
	return &ProductStrategy{
		baseStrategy: &baseStrategy{
			requiredAttrs:  []string{"tenant_id", "key"},
			optionalAttrs:  []string{"sku", "category", "brand"},
			attributeOrder: []string{"sku", "category", "brand", "tenant_id", "key"},
		},
	}
}

// GenerateSemanticName 商品特定的语义化名称生成
func (ps *ProductStrategy) GenerateSemanticName(attrs uuidTypes.DomainAttributes) (string, error) {
	if err := ps.ValidateAttributes(attrs); err != nil {
		return "", err
	}

	var parts []string

	// 商品策略：sku -> category -> brand -> tenant_id -> key
	if sku, exists := attrs.Metadata["sku"]; exists {
		if skuStr, ok := sku.(string); ok && skuStr != "" {
			parts = append(parts, skuStr)
		}
	}

	if category, exists := attrs.Metadata["category"]; exists {
		if categoryStr, ok := category.(string); ok && categoryStr != "" {
			parts = append(parts, categoryStr)
		}
	}

	if brand, exists := attrs.Metadata["brand"]; exists {
		if brandStr, ok := brand.(string); ok && brandStr != "" {
			parts = append(parts, brandStr)
		}
	}

	if attrs.TenantID != "" {
		parts = append(parts, attrs.TenantID)
	}

	if attrs.Key != "" {
		parts = append(parts, attrs.Key)
	}

	return strings.Join(parts, "-"), nil
}

// OrderStrategy 订单领域策略
type OrderStrategy struct {
	*baseStrategy
}

// NewOrderStrategy 创建订单策略
func NewOrderStrategy() uuidTypes.DomainStrategy {
	return &OrderStrategy{
		baseStrategy: &baseStrategy{
			requiredAttrs:  []string{"tenant_id", "key"},
			optionalAttrs:  []string{"order_number", "channel"},
			attributeOrder: []string{"order_number", "channel", "tenant_id", "key"},
		},
	}
}

// GenerateSemanticName 订单特定的语义化名称生成
func (os *OrderStrategy) GenerateSemanticName(attrs uuidTypes.DomainAttributes) (string, error) {
	if err := os.ValidateAttributes(attrs); err != nil {
		return "", err
	}

	var parts []string

	// 订单策略：order_number -> channel -> tenant_id -> key
	if orderNumber, exists := attrs.Metadata["order_number"]; exists {
		if orderNumberStr, ok := orderNumber.(string); ok && orderNumberStr != "" {
			parts = append(parts, orderNumberStr)
		}
	}

	if channel, exists := attrs.Metadata["channel"]; exists {
		if channelStr, ok := channel.(string); ok && channelStr != "" {
			parts = append(parts, channelStr)
		}
	}

	if attrs.TenantID != "" {
		parts = append(parts, attrs.TenantID)
	}

	if attrs.Key != "" {
		parts = append(parts, attrs.Key)
	}

	return strings.Join(parts, "-"), nil
}
