package uuid

import (
	uuidTypes "backend/pkg/types/uuid"
	"context"
	"fmt"
	"log"
)

// BasicUsageExample 基础使用示例
func BasicUsageExample() {
	// 创建UUID管理器（使用默认配置）
	manager, err := NewManagerWithDefaults()
	if err != nil {
		log.Fatal(err)
	}
	defer manager.Close()

	ctx := context.Background()

	// 生成用户UUID
	userUUID, err := manager.GenerateForUser(ctx, "tenant001", "user123", map[string]interface{}{
		"username": "zhang<PERSON>",
		"email":    "<EMAIL>",
	})
	if err != nil {
		log.Fatal(err)
	}

	fmt.Printf("用户UUID: %s\n", userUUID)

	// 生成商品UUID
	productUUID, err := manager.GenerateForProduct(ctx, "tenant001", "product456", map[string]interface{}{
		"sku":      "PHONE001",
		"category": "electronics",
		"brand":    "apple",
	})
	if err != nil {
		log.Fatal(err)
	}

	fmt.Printf("商品UUID: %s\n", productUUID)
}

// AdvancedUsageExample 高级使用示例
func AdvancedUsageExample() {
	// 使用自定义配置创建管理器
	config := uuidTypes.GeneratorConfig{
		RootNamespace: "com.mycompany.app",
		FallbackToV4:  true,
		EnableCache:   true,
		CacheSize:     5000,
		Strategies: map[string]uuidTypes.StrategyConfig{
			"customer": {
				RequiredAttrs:  []string{"tenant_id", "key"},
				OptionalAttrs:  []string{"customer_code", "region"},
				AttributeOrder: []string{"customer_code", "region", "tenant_id", "key"},
			},
		},
	}

	manager, err := NewManager(config)
	if err != nil {
		log.Fatal(err)
	}
	defer manager.Close()

	ctx := context.Background()

	// 使用通用方法生成UUID
	attrs := uuidTypes.DomainAttributes{
		TenantID:   "tenant001",
		EntityType: "entity",
		Key:        "customer789",
		Metadata: map[string]interface{}{
			"customer_code": "CUST001",
			"region":        "APAC",
		},
	}

	customerUUID, err := manager.Generate(ctx, "customer", attrs)
	if err != nil {
		log.Fatal(err)
	}

	fmt.Printf("客户UUID: %s\n", customerUUID)

	// 验证确定性：相同输入生成相同UUID
	customerUUID2, err := manager.Generate(ctx, "customer", attrs)
	if err != nil {
		log.Fatal(err)
	}

	fmt.Printf("确定性验证: %v\n", customerUUID == customerUUID2)
}

// ConvenienceFunctionsExample 便捷函数示例
func ConvenienceFunctionsExample() {
	ctx := context.Background()

	// 使用全局便捷函数
	userUUID, err := GenerateUserUUID(ctx, "tenant001", "user456", map[string]interface{}{
		"username": "lisi",
		"email":    "<EMAIL>",
	})
	if err != nil {
		log.Fatal(err)
	}

	fmt.Printf("便捷函数生成的用户UUID: %s\n", userUUID)

	productUUID, err := GenerateProductUUID(ctx, "tenant001", "product789", map[string]interface{}{
		"sku":   "LAPTOP001",
		"brand": "dell",
	})
	if err != nil {
		log.Fatal(err)
	}

	fmt.Printf("便捷函数生成的商品UUID: %s\n", productUUID)
}

// ErrorHandlingExample 错误处理示例
func ErrorHandlingExample() {
	manager, err := NewManagerWithDefaults()
	if err != nil {
		log.Fatal(err)
	}
	defer manager.Close()

	ctx := context.Background()

	// 缺少必需属性的情况
	attrs := uuidTypes.DomainAttributes{
		TenantID:   "", // 缺少必需的tenant_id
		EntityType: "entity",
		Key:        "test",
	}

	// 正常生成会失败
	_, err = manager.Generate(ctx, "user", attrs)
	if err != nil {
		fmt.Printf("正常生成失败: %v\n", err)
	}

	// 使用降级生成可以成功
	fallbackUUID, err := manager.GenerateWithFallback(ctx, "user", attrs)
	if err != nil {
		log.Fatal(err)
	}

	fmt.Printf("降级生成成功: %s\n", fallbackUUID)
}

// MonitoringExample 监控示例
func MonitoringExample() {
	manager, err := NewManagerWithDefaults()
	if err != nil {
		log.Fatal(err)
	}
	defer manager.Close()

	// 获取支持的领域
	domains := manager.GetSupportedDomains()
	fmt.Printf("支持的领域: %v\n", domains)

	// 获取统计信息
	stats := manager.GetStats()
	fmt.Printf("统计信息: %+v\n", stats)

	// 获取命名空间
	userNamespace, err := manager.GetNamespace("user", "entity")
	if err != nil {
		log.Fatal(err)
	}
	fmt.Printf("用户领域命名空间: %s\n", userNamespace)
}
