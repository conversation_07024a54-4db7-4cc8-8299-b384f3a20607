package bus

import (
	"context"
	"encoding/json"
	"fmt"
	"sync"
	"time"

	"backend/internal/domain/event"
	"backend/pkg/infrastructure/logger"
	"backend/pkg/infrastructure/mq"
)

// RedisEventBus Redis事件总线实现
// 基于现有的Redis MQ系统构建
type RedisEventBus struct {
	mqManager mq.MQManager
	logger    logger.Logger
	config    *EventBusConfig

	// 订阅管理
	subscriptions map[string][]EventHandler
	subsMutex     sync.RWMutex

	// 运行状态
	isRunning bool
	runMutex  sync.RWMutex

	// 指标统计
	metrics      *EventBusMetrics
	metricsMutex sync.RWMutex

	// 观察者
	observers     []EventBusObserver
	observerMutex sync.RWMutex

	// 事件注册表
	registry *event.EventRegistry

	// 消费者管理
	consumers     map[string]mq.Consumer
	consumerMutex sync.RWMutex
}

// NewRedisEventBus 创建Redis事件总线
func NewRedisEventBus(
	mqManager mq.MQManager,
	logger logger.Logger,
	config *EventBusConfig,
	registry *event.EventRegistry,
) *RedisEventBus {
	if config == nil {
		config = DefaultEventBusConfig()
	}

	if registry == nil {
		registry = event.GlobalEventRegistry
	}

	return &RedisEventBus{
		mqManager:     mqManager,
		logger:        logger,
		config:        config,
		registry:      registry,
		subscriptions: make(map[string][]EventHandler),
		metrics:       &EventBusMetrics{},
		observers:     make([]EventBusObserver, 0),
		consumers:     make(map[string]mq.Consumer),
	}
}

// PublishEvent 发布单个事件
func (bus *RedisEventBus) PublishEvent(ctx context.Context, evt event.DomainEvent) error {
	if !bus.IsRunning() {
		return fmt.Errorf("event bus is not running")
	}

	bus.logger.Debug(ctx, "Publishing event to Redis", map[string]interface{}{
		"event_id":   evt.EventID(),
		"event_type": evt.EventType(),
		"topic":      evt.Topic(),
	})

	// 序列化事件为JSON
	eventEnvelope := event.ToEnvelope(evt)
	payload, err := json.Marshal(eventEnvelope)
	if err != nil {
		return fmt.Errorf("failed to marshal event: %w", err)
	}

	// 发送到Redis队列
	producer := bus.mqManager.Producer()
	headers := map[string]string{
		"event_id":       evt.EventID(),
		"event_type":     evt.EventType(),
		"aggregate_id":   evt.AggregateID(),
		"aggregate_type": evt.AggregateType(),
	}

	err = producer.Send(ctx, evt.Topic(), payload, headers)
	if err != nil {
		bus.incrementFailedEvents()
		return fmt.Errorf("failed to send event to Redis: %w", err)
	}

	bus.incrementPublishedEvents()
	bus.notifyEventPublished(ctx, evt)

	bus.logger.Debug(ctx, "Event published to Redis successfully", map[string]interface{}{
		"event_id": evt.EventID(),
		"topic":    evt.Topic(),
	})

	return nil
}

// PublishEvents 发布多个事件
func (bus *RedisEventBus) PublishEvents(ctx context.Context, events []event.DomainEvent) error {
	if len(events) == 0 {
		return nil
	}

	// 构建消息批次
	messages := make([]*mq.Message, len(events))
	for i, evt := range events {
		eventEnvelope := event.ToEnvelope(evt)
		payload, err := json.Marshal(eventEnvelope)
		if err != nil {
			return fmt.Errorf("failed to marshal event %d: %w", i, err)
		}

		messages[i] = &mq.Message{
			ID:      evt.EventID(),
			Topic:   evt.Topic(),
			Payload: payload,
			Headers: map[string]string{
				"event_id":       evt.EventID(),
				"event_type":     evt.EventType(),
				"aggregate_id":   evt.AggregateID(),
				"aggregate_type": evt.AggregateType(),
			},
			Timestamp: evt.OccurredAt(),
		}
	}

	// 批量发送
	producer := bus.mqManager.Producer()
	err := producer.SendBatch(ctx, messages)
	if err != nil {
		bus.incrementFailedEvents()
		return fmt.Errorf("failed to send batch events to Redis: %w", err)
	}

	// 更新指标和通知
	for _, evt := range events {
		bus.incrementPublishedEvents()
		bus.notifyEventPublished(ctx, evt)
	}

	bus.logger.Info(ctx, "Batch events published to Redis", map[string]interface{}{
		"event_count": len(events),
	})

	return nil
}

// Subscribe 订阅事件类型
func (bus *RedisEventBus) Subscribe(eventType string, handler EventHandler) error {
	bus.subsMutex.Lock()
	defer bus.subsMutex.Unlock()

	// 添加到订阅列表
	if bus.subscriptions[eventType] == nil {
		bus.subscriptions[eventType] = make([]EventHandler, 0)
	}
	bus.subscriptions[eventType] = append(bus.subscriptions[eventType], handler)

	// 如果事件总线正在运行，立即启动消费者
	if bus.IsRunning() {
		if err := bus.startConsumerForEventType(eventType); err != nil {
			return fmt.Errorf("failed to start consumer for event type %s: %w", eventType, err)
		}
	}

	bus.logger.Info(context.Background(), "Event handler subscribed", map[string]interface{}{
		"event_type":   eventType,
		"handler_name": handler.GetHandlerName(),
	})

	bus.notifySubscriptionAdded(eventType, handler)
	return nil
}

// Unsubscribe 取消订阅事件类型
func (bus *RedisEventBus) Unsubscribe(eventType string, handler EventHandler) error {
	bus.subsMutex.Lock()
	defer bus.subsMutex.Unlock()

	handlers := bus.subscriptions[eventType]
	if handlers == nil {
		return nil
	}

	// 查找并移除处理器
	for i, h := range handlers {
		if h.GetHandlerName() == handler.GetHandlerName() {
			bus.subscriptions[eventType] = append(handlers[:i], handlers[i+1:]...)
			break
		}
	}

	// 如果没有处理器了，停止消费者
	if len(bus.subscriptions[eventType]) == 0 {
		delete(bus.subscriptions, eventType)
		bus.stopConsumerForEventType(eventType)
	}

	bus.logger.Info(context.Background(), "Event handler unsubscribed", map[string]interface{}{
		"event_type":   eventType,
		"handler_name": handler.GetHandlerName(),
	})

	bus.notifySubscriptionRemoved(eventType, handler)
	return nil
}

// Start 启动事件总线
func (bus *RedisEventBus) Start(ctx context.Context) error {
	bus.runMutex.Lock()
	defer bus.runMutex.Unlock()

	if bus.isRunning {
		return fmt.Errorf("event bus is already running")
	}

	// 检查MQ连接
	if err := bus.mqManager.Health(); err != nil {
		return fmt.Errorf("MQ health check failed: %w", err)
	}

	// 为所有已订阅的事件类型启动消费者
	bus.subsMutex.RLock()
	for eventType := range bus.subscriptions {
		if err := bus.startConsumerForEventType(eventType); err != nil {
			bus.subsMutex.RUnlock()
			return fmt.Errorf("failed to start consumer for event type %s: %w", eventType, err)
		}
	}
	bus.subsMutex.RUnlock()

	bus.isRunning = true

	bus.logger.Info(ctx, "Redis event bus started", map[string]interface{}{
		"subscribed_event_types": len(bus.subscriptions),
	})

	return nil
}

// Stop 停止事件总线
func (bus *RedisEventBus) Stop(ctx context.Context) error {
	bus.runMutex.Lock()
	defer bus.runMutex.Unlock()

	if !bus.isRunning {
		return nil
	}

	// 停止所有消费者
	bus.consumerMutex.Lock()
	for eventType, consumer := range bus.consumers {
		if err := consumer.Close(); err != nil {
			bus.logger.Error(ctx, "Failed to close consumer", err, map[string]interface{}{
				"event_type": eventType,
			})
		}
	}
	bus.consumers = make(map[string]mq.Consumer)
	bus.consumerMutex.Unlock()

	bus.isRunning = false

	bus.logger.Info(ctx, "Redis event bus stopped", nil)
	return nil
}

// IsRunning 检查事件总线是否运行中
func (bus *RedisEventBus) IsRunning() bool {
	bus.runMutex.RLock()
	defer bus.runMutex.RUnlock()
	return bus.isRunning
}

// startConsumerForEventType 为指定事件类型启动消费者
func (bus *RedisEventBus) startConsumerForEventType(eventType string) error {
	bus.consumerMutex.Lock()
	defer bus.consumerMutex.Unlock()

	// 检查是否已经有消费者
	if _, exists := bus.consumers[eventType]; exists {
		return nil
	}

	// 创建新的消费者
	consumer := bus.mqManager.Consumer()

	// 创建消息处理器
	messageHandler := mq.MessageHandlerFunc(func(ctx context.Context, message *mq.Message) error {
		return bus.handleMessage(ctx, eventType, message)
	})

	// 订阅Redis队列
	if err := consumer.Subscribe(context.Background(), eventType, messageHandler); err != nil {
		return fmt.Errorf("failed to subscribe to Redis queue: %w", err)
	}

	bus.consumers[eventType] = consumer

	bus.logger.Debug(context.Background(), "Consumer started for event type", map[string]interface{}{
		"event_type": eventType,
	})

	return nil
}

// stopConsumerForEventType 停止指定事件类型的消费者
func (bus *RedisEventBus) stopConsumerForEventType(eventType string) {
	bus.consumerMutex.Lock()
	defer bus.consumerMutex.Unlock()

	consumer, exists := bus.consumers[eventType]
	if !exists {
		return
	}

	if err := consumer.Close(); err != nil {
		bus.logger.Error(context.Background(), "Failed to close consumer", err, map[string]interface{}{
			"event_type": eventType,
		})
	}

	delete(bus.consumers, eventType)

	bus.logger.Debug(context.Background(), "Consumer stopped for event type", map[string]interface{}{
		"event_type": eventType,
	})
}

// handleMessage 处理从Redis接收到的消息
func (bus *RedisEventBus) handleMessage(ctx context.Context, eventType string, message *mq.Message) error {
	startTime := time.Now()

	bus.logger.Debug(ctx, "Processing message from Redis", map[string]interface{}{
		"message_id": message.ID,
		"topic":      message.Topic,
		"event_type": eventType,
	})

	// 反序列化事件
	var eventEnvelope event.EventEnvelope
	if err := json.Unmarshal(message.Payload, &eventEnvelope); err != nil {
		bus.logger.Error(ctx, "Failed to unmarshal event envelope", err, map[string]interface{}{
			"message_id": message.ID,
		})
		return fmt.Errorf("failed to unmarshal event envelope: %w", err)
	}

	// 从事件信封创建领域事件
	domainEvent := event.FromEnvelope(&eventEnvelope)

	// 获取事件处理器
	bus.subsMutex.RLock()
	handlers := bus.subscriptions[eventType]
	bus.subsMutex.RUnlock()

	if len(handlers) == 0 {
		bus.logger.Debug(ctx, "No handlers for event type", map[string]interface{}{
			"event_type": eventType,
		})
		return nil
	}

	// 处理事件
	for _, handler := range handlers {
		if !handler.CanHandle(eventType) {
			continue
		}

		result := &EventProcessingResult{
			EventID:     domainEvent.EventID(),
			EventType:   domainEvent.EventType(),
			HandlerName: handler.GetHandlerName(),
		}

		err := handler.Handle(ctx, domainEvent)
		result.ProcessingTime = time.Since(startTime).Milliseconds()

		if err != nil {
			result.Success = false
			result.Error = err

			bus.incrementFailedEvents()
			bus.notifyEventFailed(ctx, domainEvent, err)

			bus.logger.Error(ctx, "Event processing failed", err, map[string]interface{}{
				"event_id":     domainEvent.EventID(),
				"event_type":   domainEvent.EventType(),
				"handler_name": handler.GetHandlerName(),
			})
		} else {
			result.Success = true
			bus.incrementProcessedEvents()

			bus.logger.Debug(ctx, "Event processed successfully", map[string]interface{}{
				"event_id":     domainEvent.EventID(),
				"event_type":   domainEvent.EventType(),
				"handler_name": handler.GetHandlerName(),
			})
		}

		bus.notifyEventProcessed(ctx, result)
	}

	return nil
}

// 指标相关方法
func (bus *RedisEventBus) incrementPublishedEvents() {
	bus.metricsMutex.Lock()
	defer bus.metricsMutex.Unlock()
	bus.metrics.PublishedEvents++
}

func (bus *RedisEventBus) incrementProcessedEvents() {
	bus.metricsMutex.Lock()
	defer bus.metricsMutex.Unlock()
	bus.metrics.ProcessedEvents++
}

func (bus *RedisEventBus) incrementFailedEvents() {
	bus.metricsMutex.Lock()
	defer bus.metricsMutex.Unlock()
	bus.metrics.FailedEvents++
}

// GetMetrics 获取指标
func (bus *RedisEventBus) GetMetrics() *EventBusMetrics {
	bus.metricsMutex.RLock()
	defer bus.metricsMutex.RUnlock()

	return &EventBusMetrics{
		PublishedEvents:     bus.metrics.PublishedEvents,
		ProcessedEvents:     bus.metrics.ProcessedEvents,
		FailedEvents:        bus.metrics.FailedEvents,
		ActiveSubscriptions: len(bus.subscriptions),
		QueuedEvents:        0, // Redis队列中的事件数量需要额外查询
	}
}

// 观察者通知方法
func (bus *RedisEventBus) notifyEventPublished(ctx context.Context, evt event.DomainEvent) {
	bus.observerMutex.RLock()
	defer bus.observerMutex.RUnlock()

	for _, observer := range bus.observers {
		observer.OnEventPublished(ctx, evt)
	}
}

func (bus *RedisEventBus) notifyEventProcessed(ctx context.Context, result *EventProcessingResult) {
	bus.observerMutex.RLock()
	defer bus.observerMutex.RUnlock()

	for _, observer := range bus.observers {
		observer.OnEventProcessed(ctx, result)
	}
}

func (bus *RedisEventBus) notifyEventFailed(ctx context.Context, evt event.DomainEvent, err error) {
	bus.observerMutex.RLock()
	defer bus.observerMutex.RUnlock()

	for _, observer := range bus.observers {
		observer.OnEventFailed(ctx, evt, err)
	}
}

func (bus *RedisEventBus) notifySubscriptionAdded(eventType string, handler EventHandler) {
	bus.observerMutex.RLock()
	defer bus.observerMutex.RUnlock()

	for _, observer := range bus.observers {
		observer.OnSubscriptionAdded(eventType, handler)
	}
}

func (bus *RedisEventBus) notifySubscriptionRemoved(eventType string, handler EventHandler) {
	bus.observerMutex.RLock()
	defer bus.observerMutex.RUnlock()

	for _, observer := range bus.observers {
		observer.OnSubscriptionRemoved(eventType, handler)
	}
}

// AddObserver 添加观察者
func (bus *RedisEventBus) AddObserver(observer EventBusObserver) {
	bus.observerMutex.Lock()
	defer bus.observerMutex.Unlock()
	bus.observers = append(bus.observers, observer)
}
