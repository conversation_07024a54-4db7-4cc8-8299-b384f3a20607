package bus

import (
	"context"

	"backend/internal/domain/event"
)

// EventBus 事件总线接口
// 负责事件的发布和订阅
type EventBus interface {
	// PublishEvent 发布单个事件
	PublishEvent(ctx context.Context, event event.DomainEvent) error
	
	// PublishEvents 发布多个事件
	PublishEvents(ctx context.Context, events []event.DomainEvent) error
	
	// Subscribe 订阅事件类型
	Subscribe(eventType string, handler EventHandler) error
	
	// Unsubscribe 取消订阅事件类型
	Unsubscribe(eventType string, handler EventHandler) error
	
	// Start 启动事件总线
	Start(ctx context.Context) error
	
	// Stop 停止事件总线
	Stop(ctx context.Context) error
	
	// IsRunning 检查事件总线是否运行中
	IsRunning() bool
}

// EventHandler 事件处理器接口
type EventHandler interface {
	// Handle 处理事件
	Handle(ctx context.Context, event event.DomainEvent) error
	
	// CanHandle 检查是否可以处理指定类型的事件
	CanHandle(eventType string) bool
	
	// GetHandlerName 获取处理器名称
	GetHandlerName() string
}

// EventSubscription 事件订阅信息
type EventSubscription struct {
	EventType string
	Handler   EventHandler
}

// EventBusConfig 事件总线配置
type EventBusConfig struct {
	// BufferSize 事件缓冲区大小
	BufferSize int
	
	// WorkerCount 工作协程数量
	WorkerCount int
	
	// RetryAttempts 重试次数
	RetryAttempts int
	
	// EnableMetrics 是否启用指标收集
	EnableMetrics bool
	
	// EnableLogging 是否启用日志记录
	EnableLogging bool
}

// DefaultEventBusConfig 默认事件总线配置
func DefaultEventBusConfig() *EventBusConfig {
	return &EventBusConfig{
		BufferSize:    1000,
		WorkerCount:   5,
		RetryAttempts: 3,
		EnableMetrics: true,
		EnableLogging: true,
	}
}

// EventBusMetrics 事件总线指标
type EventBusMetrics struct {
	// PublishedEvents 已发布事件数量
	PublishedEvents int64
	
	// ProcessedEvents 已处理事件数量
	ProcessedEvents int64
	
	// FailedEvents 失败事件数量
	FailedEvents int64
	
	// ActiveSubscriptions 活跃订阅数量
	ActiveSubscriptions int
	
	// QueuedEvents 队列中的事件数量
	QueuedEvents int
}

// EventProcessingResult 事件处理结果
type EventProcessingResult struct {
	// EventID 事件ID
	EventID string
	
	// EventType 事件类型
	EventType string
	
	// HandlerName 处理器名称
	HandlerName string
	
	// Success 是否成功
	Success bool
	
	// Error 错误信息
	Error error
	
	// ProcessingTime 处理时间(毫秒)
	ProcessingTime int64
}

// EventBusStatus 事件总线状态
type EventBusStatus struct {
	// IsRunning 是否运行中
	IsRunning bool
	
	// Metrics 指标信息
	Metrics *EventBusMetrics
	
	// Subscriptions 订阅信息
	Subscriptions []EventSubscription
	
	// LastError 最后一个错误
	LastError error
}

// EventBusObserver 事件总线观察者接口
type EventBusObserver interface {
	// OnEventPublished 事件发布时调用
	OnEventPublished(ctx context.Context, event event.DomainEvent)
	
	// OnEventProcessed 事件处理完成时调用
	OnEventProcessed(ctx context.Context, result *EventProcessingResult)
	
	// OnEventFailed 事件处理失败时调用
	OnEventFailed(ctx context.Context, event event.DomainEvent, err error)
	
	// OnSubscriptionAdded 添加订阅时调用
	OnSubscriptionAdded(eventType string, handler EventHandler)
	
	// OnSubscriptionRemoved 移除订阅时调用
	OnSubscriptionRemoved(eventType string, handler EventHandler)
}

// EventFilter 事件过滤器接口
type EventFilter interface {
	// ShouldProcess 判断是否应该处理事件
	ShouldProcess(ctx context.Context, event event.DomainEvent) bool
	
	// GetFilterName 获取过滤器名称
	GetFilterName() string
}

// EventMiddleware 事件中间件接口
type EventMiddleware interface {
	// Process 处理事件（可以修改事件或阻止传递）
	Process(ctx context.Context, event event.DomainEvent, next func(context.Context, event.DomainEvent) error) error
	
	// GetMiddlewareName 获取中间件名称
	GetMiddlewareName() string
}

// EventBusBuilder 事件总线构建器接口
type EventBusBuilder interface {
	// WithConfig 设置配置
	WithConfig(config *EventBusConfig) EventBusBuilder
	
	// WithObserver 添加观察者
	WithObserver(observer EventBusObserver) EventBusBuilder
	
	// WithFilter 添加过滤器
	WithFilter(filter EventFilter) EventBusBuilder
	
	// WithMiddleware 添加中间件
	WithMiddleware(middleware EventMiddleware) EventBusBuilder
	
	// Build 构建事件总线
	Build() EventBus
}
