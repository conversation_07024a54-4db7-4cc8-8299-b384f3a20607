# Bus Management (统一总线管理)

## 文件说明

### event.go - 事件总线（现有）
```go
// 事件总线接口
type EventBus interface {
    Publish(ctx context.Context, event Event) error
    Subscribe(eventType string, handler EventHandler) error
    Unsubscribe(eventType string, handler EventHandler) error
}

// 支持异步事件处理和Redis消息队列
type DefaultEventBus struct {
    publisher   publisher.EventPublisher
    subscribers map[string][]EventHandler
    mqManager   mq.MQManager
}
```

### command.go - 命令总线（新增）
```go
// 命令总线接口
type CommandBus interface {
    Send(ctx context.Context, cmd command.Command) error
    Register(commandType string, handler command.Handler)
}

// 命令总线实现
type DefaultCommandBus struct {
    handlers   map[string]command.Handler
    middleware []Middleware
    validator  Validator
}
```

### query.go - 查询总线（新增）
```go
// 查询总线接口
type QueryBus interface {
    Send(ctx context.Context, query query.Query) (interface{}, error)
    Register(queryType string, handler query.Handler)
}

// 查询总线实现（支持缓存）
type DefaultQueryBus struct {
    handlers map[string]query.Handler
    cache    Cache
    logger   Logger
}
```

### cqrs.go - CQRS统一总线（新增）
```go
// CQRS统一总线接口
type CQRSBus interface {
    // 命令操作
    SendCommand(ctx context.Context, cmd command.Command) error
    RegisterCommandHandler(commandType string, handler command.Handler)
    
    // 查询操作
    SendQuery(ctx context.Context, query query.Query) (interface{}, error)
    RegisterQueryHandler(queryType string, handler query.Handler)
    
    // 事件操作
    PublishEvent(ctx context.Context, event Event) error
    SubscribeEvent(eventType string, handler EventHandler) error
}

// 统一CQRS总线实现
type DefaultCQRSBus struct {
    commandBus CommandBus
    queryBus   QueryBus
    eventBus   EventBus
    middleware []CQRSMiddleware
}
```

## 设计特点

### 职责分离
- **事件总线**：处理异步的领域事件
- **命令总线**：处理同步的写操作命令
- **查询总线**：处理读操作查询，支持缓存
- **CQRS总线**：统一的操作入口

### 统一管理
- 所有总线在同一个包下
- 共享基础设施（Redis、日志、监控）
- 统一的中间件机制
- 一致的错误处理

### 可扩展性
- 支持中间件链
- 可插拔的缓存策略
- 灵活的路由机制
- 完善的监控支持

## 使用示例

```go
// 创建各个总线
eventBus := bus.NewEventBus(publisher, mqManager)
commandBus := bus.NewCommandBus()
queryBus := bus.NewQueryBus(cache)

// 创建统一CQRS总线
cqrsBus := bus.NewCQRSBus(commandBus, queryBus, eventBus)

// 使用统一入口
err := cqrsBus.SendCommand(ctx, &CreateProductCommand{...})
result, err := cqrsBus.SendQuery(ctx, &GetProductQuery{...})
err = cqrsBus.PublishEvent(ctx, &ProductCreatedEvent{...})
``` 