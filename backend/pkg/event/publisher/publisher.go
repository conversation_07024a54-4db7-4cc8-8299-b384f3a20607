package publisher

import (
	"context"
	"encoding/json"
	"log"

	apperrors "backend/pkg/common/errors"
	"backend/pkg/common/errors/codes"
	"backend/pkg/infrastructure/mq"
)

// EventPublisher 事件发布器
type EventPublisher struct {
	producer mq.Producer
}

// NewEventPublisher 创建事件发布器
func NewEventPublisher(producer mq.Producer) *EventPublisher {
	return &EventPublisher{producer: producer}
}

// PublishEvent 发布事件
func (p *EventPublisher) PublishEvent(ctx context.Context, event interface{}) error {
	// 获取事件主题
	topicProvider, ok := event.(interface{ Topic() string })
	if !ok {
		return apperrors.NewInternal(codes.MessageQueueOperation, "事件未实现Topic()方法").Build()
	}

	topic := topicProvider.Topic()

	// 序列化事件
	payload, err := json.Marshal(event)
	if err != nil {
		return apperrors.NewInternal(codes.MessageQueueOperation, "事件序列化失败").Wrap(err).Build()
	}

	// 发送到消息队列
	return p.producer.Send(ctx, topic, payload)
}

// PublishEventAsync 异步发布事件
func (p *EventPublisher) PublishEventAsync(ctx context.Context, event interface{}) {
	go func() {
		if err := p.PublishEvent(ctx, event); err != nil {
			// 记录错误日志
			log.Printf("发布事件失败: %v", err)
		}
	}()
}

// PublishBatchEvents 批量发布事件
func (p *EventPublisher) PublishBatchEvents(ctx context.Context, events []interface{}) error {
	messages := make([]*mq.Message, 0, len(events))

	for _, event := range events {
		// 获取事件主题
		topicProvider, ok := event.(interface{ Topic() string })
		if !ok {
			return apperrors.NewInternal(codes.MessageQueueOperation, "事件未实现Topic()方法").Build()
		}

		topic := topicProvider.Topic()

		// 序列化事件
		payload, err := json.Marshal(event)
		if err != nil {
			return apperrors.NewInternal(codes.MessageQueueOperation, "事件序列化失败").Wrap(err).Build()
		}

		messages = append(messages, &mq.Message{
			Topic:   topic,
			Payload: payload,
		})
	}

	return p.producer.SendBatch(ctx, messages)
}
