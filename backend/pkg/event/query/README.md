# Query Framework (查询框架)

## 文件说明

### query.go - 查询接口定义
```go
// 基础查询接口
type Query interface {
    QueryType() string    // 查询类型
    TenantID() string     // 租户ID
}

// 基础查询结构
type BaseQuery struct {
    Type   string `json:"type"`
    Tenant string `json:"tenant_id"`
}

// 分页查询结构
type PagedQuery struct {
    BaseQuery
    Page     int               `json:"page"`
    PageSize int               `json:"page_size"`
    Filters  map[string]string `json:"filters"`
    Sort     string            `json:"sort"`
}
```

### handler.go - 查询处理器接口
```go
// 查询处理器接口
type Handler interface {
    Handle(ctx context.Context, query Query) (interface{}, error)
}

// 处理器注册器
type Registry interface {
    Register(queryType string, handler Handler)
    GetHandler(queryType string) (Handler, bool)
}
```

### 查询总线
查询总线实现已移至 `pkg/event/bus/query.go`，提供：
- 查询路由和分发
- 处理器注册管理
- 缓存策略支持
- 性能优化机制

## 设计特点

- **灵活返回**：支持任意类型的查询结果
- **缓存支持**：内置缓存机制
- **分页支持**：标准的分页查询结构
- **性能优化**：针对读操作的优化 