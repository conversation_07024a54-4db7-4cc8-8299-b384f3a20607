# Command Framework (命令框架)

## 文件说明

### command.go - 命令接口定义
```go
// 基础命令接口
type Command interface {
    CommandType() string    // 命令类型
    AggregateID() string   // 聚合根ID
    TenantID() string      // 租户ID
}

// 基础命令结构
type BaseCommand struct {
    ID        string    `json:"id"`
    Type      string    `json:"type"`
    AggrID    string    `json:"aggregate_id"`
    Tenant    string    `json:"tenant_id"`
    Timestamp time.Time `json:"timestamp"`
    Version   int       `json:"version"`
}
```

### handler.go - 命令处理器接口
```go
// 命令处理器接口
type Handler interface {
    Handle(ctx context.Context, cmd Command) error
}

// 处理器注册器
type Registry interface {
    Register(commandType string, handler Handler)
    GetHandler(commandType string) (Handler, bool)
}
```

### 命令总线
命令总线实现已移至 `pkg/event/bus/command.go`，提供：
- 命令路由和分发
- 处理器注册管理
- 中间件支持
- 错误处理机制

## 设计特点

- **类型安全**：强类型的命令定义
- **可扩展**：支持中间件和拦截器
- **异步支持**：可选的异步命令处理
- **错误处理**：统一的错误处理机制 