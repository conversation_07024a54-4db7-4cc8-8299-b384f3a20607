package store

import (
	"context"
	"time"

	"backend/internal/domain/event"
)

// EventStore 事件存储接口
// 负责事件的持久化和查询
type EventStore interface {
	// SaveEvent 保存单个事件
	SaveEvent(ctx context.Context, event event.DomainEvent) error
	
	// SaveEvents 保存多个事件
	SaveEvents(ctx context.Context, events []event.DomainEvent) error
	
	// GetEvents 获取聚合的所有事件
	GetEvents(ctx context.Context, aggregateID string) ([]event.DomainEvent, error)
	
	// GetEventsByType 根据事件类型获取事件
	GetEventsByType(ctx context.Context, eventType string, limit int, offset int) ([]event.DomainEvent, error)
	
	// GetEventsByTimeRange 根据时间范围获取事件
	GetEventsByTimeRange(ctx context.Context, startTime, endTime time.Time, limit int, offset int) ([]event.DomainEvent, error)
	
	// GetEventsByAggregateType 根据聚合类型获取事件
	GetEventsByAggregateType(ctx context.Context, aggregateType string, limit int, offset int) ([]event.DomainEvent, error)
	
	// GetEventByID 根据事件ID获取事件
	GetEventByID(ctx context.Context, eventID string) (event.DomainEvent, error)
	
	// GetLatestEvents 获取最新的事件
	GetLatestEvents(ctx context.Context, limit int) ([]event.DomainEvent, error)
	
	// CountEvents 统计事件数量
	CountEvents(ctx context.Context) (int64, error)
	
	// CountEventsByType 根据事件类型统计数量
	CountEventsByType(ctx context.Context, eventType string) (int64, error)
	
	// CountEventsByAggregateType 根据聚合类型统计数量
	CountEventsByAggregateType(ctx context.Context, aggregateType string) (int64, error)
	
	// DeleteEvents 删除聚合的事件（谨慎使用）
	DeleteEvents(ctx context.Context, aggregateID string) error
	
	// Close 关闭事件存储
	Close() error
}

// EventStoreQuery 事件存储查询条件
type EventStoreQuery struct {
	// AggregateID 聚合ID
	AggregateID string
	
	// AggregateType 聚合类型
	AggregateType string
	
	// EventType 事件类型
	EventType string
	
	// StartTime 开始时间
	StartTime *time.Time
	
	// EndTime 结束时间
	EndTime *time.Time
	
	// MinVersion 最小版本
	MinVersion int
	
	// MaxVersion 最大版本
	MaxVersion int
	
	// Limit 限制数量
	Limit int
	
	// Offset 偏移量
	Offset int
	
	// OrderBy 排序字段
	OrderBy string
	
	// OrderDirection 排序方向 (ASC/DESC)
	OrderDirection string
}

// EventStoreResult 事件存储查询结果
type EventStoreResult struct {
	// Events 事件列表
	Events []event.DomainEvent
	
	// Total 总数量
	Total int64
	
	// HasMore 是否有更多数据
	HasMore bool
}

// EventStoreConfig 事件存储配置
type EventStoreConfig struct {
	// ConnectionString 连接字符串
	ConnectionString string
	
	// TableName 表名
	TableName string
	
	// MaxConnections 最大连接数
	MaxConnections int
	
	// ConnectionTimeout 连接超时时间
	ConnectionTimeout time.Duration
	
	// QueryTimeout 查询超时时间
	QueryTimeout time.Duration
	
	// EnableCompression 是否启用压缩
	EnableCompression bool
	
	// EnableEncryption 是否启用加密
	EnableEncryption bool
	
	// RetentionDays 数据保留天数（0表示永久保留）
	RetentionDays int
}

// DefaultEventStoreConfig 默认事件存储配置
func DefaultEventStoreConfig() *EventStoreConfig {
	return &EventStoreConfig{
		TableName:         "domain_events",
		MaxConnections:    10,
		ConnectionTimeout: 30 * time.Second,
		QueryTimeout:      30 * time.Second,
		EnableCompression: false,
		EnableEncryption:  false,
		RetentionDays:     0, // 永久保留
	}
}

// EventStoreMetrics 事件存储指标
type EventStoreMetrics struct {
	// TotalEvents 总事件数
	TotalEvents int64
	
	// EventsByType 按类型分组的事件数
	EventsByType map[string]int64
	
	// EventsByAggregateType 按聚合类型分组的事件数
	EventsByAggregateType map[string]int64
	
	// StorageSize 存储大小（字节）
	StorageSize int64
	
	// LastEventTime 最后事件时间
	LastEventTime time.Time
	
	// FirstEventTime 第一个事件时间
	FirstEventTime time.Time
}

// EventStoreSnapshot 事件存储快照
type EventStoreSnapshot struct {
	// AggregateID 聚合ID
	AggregateID string
	
	// AggregateType 聚合类型
	AggregateType string
	
	// Version 版本号
	Version int
	
	// Data 快照数据
	Data interface{}
	
	// CreatedAt 创建时间
	CreatedAt time.Time
}

// SnapshotStore 快照存储接口
type SnapshotStore interface {
	// SaveSnapshot 保存快照
	SaveSnapshot(ctx context.Context, snapshot *EventStoreSnapshot) error
	
	// GetSnapshot 获取快照
	GetSnapshot(ctx context.Context, aggregateID string) (*EventStoreSnapshot, error)
	
	// DeleteSnapshot 删除快照
	DeleteSnapshot(ctx context.Context, aggregateID string) error
	
	// GetLatestSnapshot 获取最新快照
	GetLatestSnapshot(ctx context.Context, aggregateType string) (*EventStoreSnapshot, error)
}

// EventStoreTransaction 事件存储事务接口
type EventStoreTransaction interface {
	// SaveEvent 在事务中保存事件
	SaveEvent(ctx context.Context, event event.DomainEvent) error
	
	// SaveEvents 在事务中保存多个事件
	SaveEvents(ctx context.Context, events []event.DomainEvent) error
	
	// Commit 提交事务
	Commit(ctx context.Context) error
	
	// Rollback 回滚事务
	Rollback(ctx context.Context) error
}

// TransactionalEventStore 支持事务的事件存储接口
type TransactionalEventStore interface {
	EventStore
	
	// BeginTransaction 开始事务
	BeginTransaction(ctx context.Context) (EventStoreTransaction, error)
}

// EventStoreObserver 事件存储观察者接口
type EventStoreObserver interface {
	// OnEventSaved 事件保存时调用
	OnEventSaved(ctx context.Context, event event.DomainEvent)
	
	// OnEventsSaved 多个事件保存时调用
	OnEventsSaved(ctx context.Context, events []event.DomainEvent)
	
	// OnEventQueried 事件查询时调用
	OnEventQueried(ctx context.Context, query *EventStoreQuery, result *EventStoreResult)
	
	// OnError 发生错误时调用
	OnError(ctx context.Context, operation string, err error)
}

// EventStoreBuilder 事件存储构建器接口
type EventStoreBuilder interface {
	// WithConfig 设置配置
	WithConfig(config *EventStoreConfig) EventStoreBuilder
	
	// WithObserver 添加观察者
	WithObserver(observer EventStoreObserver) EventStoreBuilder
	
	// WithSnapshotStore 设置快照存储
	WithSnapshotStore(snapshotStore SnapshotStore) EventStoreBuilder
	
	// Build 构建事件存储
	Build() (EventStore, error)
}
