package store

import (
	"context"
	"database/sql"
	"encoding/json"
	"fmt"
	"time"

	"backend/internal/domain/event"
	database "backend/pkg/infrastructure/database/abstraction"
	"backend/pkg/infrastructure/logger"
)

// PostgresEventStore PostgreSQL事件存储实现
type PostgresEventStore struct {
	db       database.Manager
	logger   logger.Logger
	config   *EventStoreConfig
	registry *event.EventRegistry

	// 观察者
	observers []EventStoreObserver
}

// EventRecord 事件记录（数据库表结构）
type EventRecord struct {
	ID            int64     `gorm:"primaryKey;autoIncrement"`
	EventID       string    `gorm:"size:36;uniqueIndex;not null"`
	EventType     string    `gorm:"size:255;index;not null"`
	AggregateID   string    `gorm:"size:36;index;not null"`
	AggregateType string    `gorm:"size:255;index;not null"`
	EventVersion  int       `gorm:"index;not null"`
	EventData     string    `gorm:"type:jsonb;not null"`
	Metadata      string    `gorm:"type:jsonb"`
	OccurredAt    time.Time `gorm:"index;not null"`
	CreatedAt     time.Time `gorm:"autoCreateTime"`
}

// TableName 指定表名
func (EventRecord) TableName() string {
	return "domain_events"
}

// NewPostgresEventStore 创建PostgreSQL事件存储
func NewPostgresEventStore(
	db database.Manager,
	logger logger.Logger,
	config *EventStoreConfig,
	registry *event.EventRegistry,
) *PostgresEventStore {
	if config == nil {
		config = DefaultEventStoreConfig()
	}

	store := &PostgresEventStore{
		db:        db,
		logger:    logger,
		config:    config,
		registry:  registry,
		observers: make([]EventStoreObserver, 0),
	}

	// 自动迁移表结构
	if err := store.migrate(); err != nil {
		logger.Error(context.Background(), "Failed to migrate event store tables", err, nil)
	}

	return store
}

// migrate 迁移数据库表结构
func (s *PostgresEventStore) migrate() error {
	return s.db.GetConnection().GetDB().AutoMigrate(&EventRecord{})
}

// SaveEvent 保存单个事件
func (s *PostgresEventStore) SaveEvent(ctx context.Context, evt event.DomainEvent) error {
	record, err := s.eventToRecord(evt)
	if err != nil {
		return fmt.Errorf("failed to convert event to record: %w", err)
	}

	if err := s.db.GetConnection().GetDB().WithContext(ctx).Create(record).Error; err != nil {
		s.notifyError(ctx, "SaveEvent", err)
		return fmt.Errorf("failed to save event: %w", err)
	}

	s.logger.Debug(ctx, "Event saved to store", map[string]interface{}{
		"event_id":   evt.EventID(),
		"event_type": evt.EventType(),
	})

	s.notifyEventSaved(ctx, evt)
	return nil
}

// SaveEvents 保存多个事件
func (s *PostgresEventStore) SaveEvents(ctx context.Context, events []event.DomainEvent) error {
	if len(events) == 0 {
		return nil
	}

	records := make([]*EventRecord, len(events))
	for i, evt := range events {
		record, err := s.eventToRecord(evt)
		if err != nil {
			return fmt.Errorf("failed to convert event %d to record: %w", i, err)
		}
		records[i] = record
	}

	// 批量插入
	if err := s.db.GetConnection().GetDB().WithContext(ctx).CreateInBatches(records, 100).Error; err != nil {
		s.notifyError(ctx, "SaveEvents", err)
		return fmt.Errorf("failed to save events: %w", err)
	}

	s.logger.Debug(ctx, "Events saved to store", map[string]interface{}{
		"event_count": len(events),
	})

	s.notifyEventsSaved(ctx, events)
	return nil
}

// GetEvents 获取聚合的所有事件
func (s *PostgresEventStore) GetEvents(ctx context.Context, aggregateID string) ([]event.DomainEvent, error) {
	var records []EventRecord

	err := s.db.GetConnection().GetDB().WithContext(ctx).
		Where("aggregate_id = ?", aggregateID).
		Order("event_version ASC").
		Find(&records).Error

	if err != nil {
		s.notifyError(ctx, "GetEvents", err)
		return nil, fmt.Errorf("failed to get events: %w", err)
	}

	events, err := s.recordsToEvents(records)
	if err != nil {
		return nil, fmt.Errorf("failed to convert records to events: %w", err)
	}

	return events, nil
}

// GetEventsByType 根据事件类型获取事件
func (s *PostgresEventStore) GetEventsByType(ctx context.Context, eventType string, limit int, offset int) ([]event.DomainEvent, error) {
	var records []EventRecord

	query := s.db.GetConnection().GetDB().WithContext(ctx).
		Where("event_type = ?", eventType).
		Order("occurred_at DESC")

	if limit > 0 {
		query = query.Limit(limit)
	}
	if offset > 0 {
		query = query.Offset(offset)
	}

	err := query.Find(&records).Error
	if err != nil {
		s.notifyError(ctx, "GetEventsByType", err)
		return nil, fmt.Errorf("failed to get events by type: %w", err)
	}

	events, err := s.recordsToEvents(records)
	if err != nil {
		return nil, fmt.Errorf("failed to convert records to events: %w", err)
	}

	return events, nil
}

// GetEventsByTimeRange 根据时间范围获取事件
func (s *PostgresEventStore) GetEventsByTimeRange(ctx context.Context, startTime, endTime time.Time, limit int, offset int) ([]event.DomainEvent, error) {
	var records []EventRecord

	query := s.db.GetConnection().GetDB().WithContext(ctx).
		Where("occurred_at BETWEEN ? AND ?", startTime, endTime).
		Order("occurred_at DESC")

	if limit > 0 {
		query = query.Limit(limit)
	}
	if offset > 0 {
		query = query.Offset(offset)
	}

	err := query.Find(&records).Error
	if err != nil {
		s.notifyError(ctx, "GetEventsByTimeRange", err)
		return nil, fmt.Errorf("failed to get events by time range: %w", err)
	}

	events, err := s.recordsToEvents(records)
	if err != nil {
		return nil, fmt.Errorf("failed to convert records to events: %w", err)
	}

	return events, nil
}

// GetEventsByAggregateType 根据聚合类型获取事件
func (s *PostgresEventStore) GetEventsByAggregateType(ctx context.Context, aggregateType string, limit int, offset int) ([]event.DomainEvent, error) {
	var records []EventRecord

	query := s.db.GetConnection().GetDB().WithContext(ctx).
		Where("aggregate_type = ?", aggregateType).
		Order("occurred_at DESC")

	if limit > 0 {
		query = query.Limit(limit)
	}
	if offset > 0 {
		query = query.Offset(offset)
	}

	err := query.Find(&records).Error
	if err != nil {
		s.notifyError(ctx, "GetEventsByAggregateType", err)
		return nil, fmt.Errorf("failed to get events by aggregate type: %w", err)
	}

	events, err := s.recordsToEvents(records)
	if err != nil {
		return nil, fmt.Errorf("failed to convert records to events: %w", err)
	}

	return events, nil
}

// GetEventByID 根据事件ID获取事件
func (s *PostgresEventStore) GetEventByID(ctx context.Context, eventID string) (event.DomainEvent, error) {
	var record EventRecord

	err := s.db.GetConnection().GetDB().WithContext(ctx).
		Where("event_id = ?", eventID).
		First(&record).Error

	if err != nil {
		if err == sql.ErrNoRows {
			return nil, fmt.Errorf("event not found: %s", eventID)
		}
		s.notifyError(ctx, "GetEventByID", err)
		return nil, fmt.Errorf("failed to get event by ID: %w", err)
	}

	evt, err := s.recordToEvent(&record)
	if err != nil {
		return nil, fmt.Errorf("failed to convert record to event: %w", err)
	}

	return evt, nil
}

// GetLatestEvents 获取最新的事件
func (s *PostgresEventStore) GetLatestEvents(ctx context.Context, limit int) ([]event.DomainEvent, error) {
	var records []EventRecord

	query := s.db.GetConnection().GetDB().WithContext(ctx).
		Order("occurred_at DESC")

	if limit > 0 {
		query = query.Limit(limit)
	}

	err := query.Find(&records).Error
	if err != nil {
		s.notifyError(ctx, "GetLatestEvents", err)
		return nil, fmt.Errorf("failed to get latest events: %w", err)
	}

	events, err := s.recordsToEvents(records)
	if err != nil {
		return nil, fmt.Errorf("failed to convert records to events: %w", err)
	}

	return events, nil
}

// CountEvents 统计事件数量
func (s *PostgresEventStore) CountEvents(ctx context.Context) (int64, error) {
	var count int64

	err := s.db.GetConnection().GetDB().WithContext(ctx).
		Model(&EventRecord{}).
		Count(&count).Error

	if err != nil {
		s.notifyError(ctx, "CountEvents", err)
		return 0, fmt.Errorf("failed to count events: %w", err)
	}

	return count, nil
}

// CountEventsByType 根据事件类型统计数量
func (s *PostgresEventStore) CountEventsByType(ctx context.Context, eventType string) (int64, error) {
	var count int64

	err := s.db.GetConnection().GetDB().WithContext(ctx).
		Model(&EventRecord{}).
		Where("event_type = ?", eventType).
		Count(&count).Error

	if err != nil {
		s.notifyError(ctx, "CountEventsByType", err)
		return 0, fmt.Errorf("failed to count events by type: %w", err)
	}

	return count, nil
}

// CountEventsByAggregateType 根据聚合类型统计数量
func (s *PostgresEventStore) CountEventsByAggregateType(ctx context.Context, aggregateType string) (int64, error) {
	var count int64

	err := s.db.GetConnection().GetDB().WithContext(ctx).
		Model(&EventRecord{}).
		Where("aggregate_type = ?", aggregateType).
		Count(&count).Error

	if err != nil {
		s.notifyError(ctx, "CountEventsByAggregateType", err)
		return 0, fmt.Errorf("failed to count events by aggregate type: %w", err)
	}

	return count, nil
}

// DeleteEvents 删除聚合的事件（谨慎使用）
func (s *PostgresEventStore) DeleteEvents(ctx context.Context, aggregateID string) error {
	err := s.db.GetConnection().GetDB().WithContext(ctx).
		Where("aggregate_id = ?", aggregateID).
		Delete(&EventRecord{}).Error

	if err != nil {
		s.notifyError(ctx, "DeleteEvents", err)
		return fmt.Errorf("failed to delete events: %w", err)
	}

	s.logger.Warn(ctx, "Events deleted from store", map[string]interface{}{
		"aggregate_id": aggregateID,
	})

	return nil
}

// Close 关闭事件存储
func (s *PostgresEventStore) Close() error {
	// PostgreSQL连接由database manager管理，这里不需要关闭
	return nil
}

// 辅助方法

// eventToRecord 将领域事件转换为数据库记录
func (s *PostgresEventStore) eventToRecord(evt event.DomainEvent) (*EventRecord, error) {
	eventDataBytes, err := json.Marshal(evt.EventData())
	if err != nil {
		return nil, fmt.Errorf("failed to marshal event data: %w", err)
	}

	metadataBytes, err := json.Marshal(evt.Metadata())
	if err != nil {
		return nil, fmt.Errorf("failed to marshal metadata: %w", err)
	}

	return &EventRecord{
		EventID:       evt.EventID(),
		EventType:     evt.EventType(),
		AggregateID:   evt.AggregateID(),
		AggregateType: evt.AggregateType(),
		EventVersion:  evt.EventVersion(),
		EventData:     string(eventDataBytes),
		Metadata:      string(metadataBytes),
		OccurredAt:    evt.OccurredAt(),
	}, nil
}

// recordToEvent 将数据库记录转换为领域事件
func (s *PostgresEventStore) recordToEvent(record *EventRecord) (event.DomainEvent, error) {
	// 从注册表创建事件实例
	_, exists := s.registry.Create(record.EventType)
	if !exists {
		// 如果没有注册，创建基础事件
		var eventData interface{}
		if err := json.Unmarshal([]byte(record.EventData), &eventData); err != nil {
			return nil, fmt.Errorf("failed to unmarshal event data: %w", err)
		}

		var metadata map[string]interface{}
		if err := json.Unmarshal([]byte(record.Metadata), &metadata); err != nil {
			return nil, fmt.Errorf("failed to unmarshal metadata: %w", err)
		}

		baseEvent := event.NewBaseDomainEvent(
			record.EventType,
			record.AggregateID,
			record.AggregateType,
			record.EventVersion,
			eventData,
		)

		// 设置元数据
		for key, value := range metadata {
			baseEvent.SetMetadata(key, value)
		}

		return baseEvent, nil
	}

	// TODO: 这里需要更复杂的反序列化逻辑
	// 目前简化处理，返回基础事件
	var eventData interface{}
	if err := json.Unmarshal([]byte(record.EventData), &eventData); err != nil {
		return nil, fmt.Errorf("failed to unmarshal event data: %w", err)
	}

	var metadata map[string]interface{}
	if err := json.Unmarshal([]byte(record.Metadata), &metadata); err != nil {
		return nil, fmt.Errorf("failed to unmarshal metadata: %w", err)
	}

	baseEvent := event.NewBaseDomainEvent(
		record.EventType,
		record.AggregateID,
		record.AggregateType,
		record.EventVersion,
		eventData,
	)

	// 设置元数据
	for key, value := range metadata {
		baseEvent.SetMetadata(key, value)
	}

	return baseEvent, nil
}

// recordsToEvents 将数据库记录列表转换为领域事件列表
func (s *PostgresEventStore) recordsToEvents(records []EventRecord) ([]event.DomainEvent, error) {
	events := make([]event.DomainEvent, len(records))

	for i, record := range records {
		evt, err := s.recordToEvent(&record)
		if err != nil {
			return nil, fmt.Errorf("failed to convert record %d to event: %w", i, err)
		}
		events[i] = evt
	}

	return events, nil
}

// 观察者通知方法

// AddObserver 添加观察者
func (s *PostgresEventStore) AddObserver(observer EventStoreObserver) {
	s.observers = append(s.observers, observer)
}

func (s *PostgresEventStore) notifyEventSaved(ctx context.Context, evt event.DomainEvent) {
	for _, observer := range s.observers {
		observer.OnEventSaved(ctx, evt)
	}
}

func (s *PostgresEventStore) notifyEventsSaved(ctx context.Context, events []event.DomainEvent) {
	for _, observer := range s.observers {
		observer.OnEventsSaved(ctx, events)
	}
}

func (s *PostgresEventStore) notifyError(ctx context.Context, operation string, err error) {
	for _, observer := range s.observers {
		observer.OnError(ctx, operation, err)
	}
}
