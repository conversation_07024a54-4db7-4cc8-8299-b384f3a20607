# Event System with CQRS Support (事件系统 + CQRS支持)

## 目录结构
```
event/
├── bus/              # 统一总线管理
│   ├── event.go      # 现有：事件总线
│   ├── command.go    # 新增：命令总线
│   ├── query.go      # 新增：查询总线
│   └── cqrs.go       # 新增：CQRS统一总线
├── publisher/        # 现有：事件发布器
├── dispatcher/       # 现有：事件分发器（可扩展支持命令/查询）
├── handler/          # 现有：事件处理器接口
├── command/          # 新增：命令框架
│   ├── handler.go    # 命令处理器接口
│   └── command.go    # 命令接口定义
└── query/            # 新增：查询框架
    ├── handler.go    # 查询处理器接口
    └── query.go      # 查询接口定义
```

## 组件职责

### 现有事件组件

#### bus/ - 事件总线
- 管理领域事件的发布和订阅
- 支持异步事件处理
- 集成Redis消息队列

#### publisher/ - 事件发布器
- 统一的事件发布接口
- 支持同步和异步发布
- 事件序列化和路由

#### dispatcher/ - 事件分发器
- 事件到处理器的分发
- 支持多个处理器订阅同一事件
- 错误处理和重试机制

#### handler/ - 事件处理器
- 事件处理器基础接口
- 支持跨领域事件协调
- 业务逻辑处理

### 新增CQRS组件

#### command/ - 命令框架
- 定义命令的基础接口
- 提供命令总线实现
- 管理命令处理器注册
- 支持命令验证和授权

#### query/ - 查询框架
- 定义查询的基础接口
- 提供查询总线实现
- 管理查询处理器注册
- 支持查询缓存策略

#### bus/ - 统一总线管理
- **event.go**：领域事件总线（现有）
- **command.go**：命令总线实现
- **query.go**：查询总线实现
- **cqrs.go**：CQRS统一总线，集成命令、查询和事件总线

## 核心接口

### 现有事件接口
```go
// 领域事件接口
type Event interface {
    Topic() string
    Timestamp() time.Time
}

// 事件处理器接口
type EventHandler interface {
    Handle(ctx context.Context, event Event) error
}
```

### 新增CQRS接口
```go
// 命令接口
type Command interface {
    CommandType() string
    AggregateID() string
    TenantID() string
}

// 查询接口
type Query interface {
    QueryType() string
    TenantID() string
}

// 命令处理器接口
type CommandHandler interface {
    Handle(ctx context.Context, cmd Command) error
}

// 查询处理器接口
type QueryHandler interface {
    Handle(ctx context.Context, query Query) (interface{}, error)
}
```

## 设计优势

### 复用现有基础设施
- 利用现有的Redis消息队列
- 复用事件总线和分发机制
- 统一的错误处理和监控

### 渐进式扩展
- 不破坏现有事件系统
- CQRS和事件系统共存
- 灵活的架构选择

### 统一管理
- 所有消息传递在一个包下
- 统一的配置和监控
- 简化依赖注入

## 使用示例

```go
// 创建各个总线
eventBus := bus.NewEventBus(publisher, mqManager)
commandBus := bus.NewCommandBus()
queryBus := bus.NewQueryBus(cache)

// 创建统一CQRS总线
cqrsBus := bus.NewCQRSBus(commandBus, queryBus, eventBus)

// 使用统一入口
err := cqrsBus.SendCommand(ctx, &CreateProductCommand{...})
result, err := cqrsBus.SendQuery(ctx, &GetProductQuery{...})
err = cqrsBus.PublishEvent(ctx, &ProductCreatedEvent{...})

// 或者直接使用专门的总线
err = commandBus.Send(ctx, &CreateProductCommand{...})
result, err = queryBus.Send(ctx, &GetProductQuery{...})
err = eventBus.Publish(ctx, &ProductCreatedEvent{...})
``` 