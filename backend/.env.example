# 九翼跨境电商ERP系统 - 环境变量配置示例
# 复制此文件为 .env.development, .env.testing, .env.production 等
# 环境变量名遵循 SECTION_SUBSECTION_FIELD 格式

# 应用环境
ENV=development

# 构建变量 (用于Docker构建)
VERSION=dev
BUILD_TIME=
COMMIT_HASH=

# 服务器配置
SERVER_HOST=0.0.0.0
SERVER_PORT=8080
SERVER_READ_TIMEOUT=30s
SERVER_WRITE_TIMEOUT=30s
SERVER_IDLE_TIMEOUT=60s
SERVER_GRPC_ENABLE=false
SERVER_GRPC_PORT=9090

# 数据库配置
DATABASE_DRIVER=postgres
DATABASE_HOST=localhost
DATABASE_PORT=5432
DATABASE_DATABASE=erp_development
DATABASE_USERNAME=postgres
DATABASE_PASSWORD=postgres
DATABASE_SSL_MODE=disable
DATABASE_MAX_OPEN_CONNS=25
DATABASE_MAX_IDLE_CONNS=5
DATABASE_CONN_MAX_LIFETIME=5m
DATABASE_CONN_MAX_IDLE_TIME=30m

# 数据库迁移配置
DATABASE_MIGRATION_AUTO_MIGRATE=true
DATABASE_MIGRATION_MIGRATIONS_DIR=migrations/postgres
DATABASE_MIGRATION_FAIL_ON_ERROR=true
DATABASE_MIGRATION_LOG_MIGRATIONS=true

# Redis配置
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_PASSWORD=
REDIS_DB=0
REDIS_POOL_SIZE=10

# 消息队列配置
MESSAGE_QUEUE_PROVIDER=redis
MESSAGE_QUEUE_REDIS_HOST=localhost
MESSAGE_QUEUE_REDIS_PORT=6379
MESSAGE_QUEUE_REDIS_PASSWORD=
MESSAGE_QUEUE_REDIS_DB=1
MESSAGE_QUEUE_REDIS_POOL_SIZE=10
MESSAGE_QUEUE_REDIS_MAX_RETRIES=3
MESSAGE_QUEUE_REDIS_RETRY_DELAY=1s

# 日志配置
LOGGER_LEVEL=info
LOGGER_FORMAT=json
LOGGER_OUTPUT=stdout
LOGGER_FILE=logs/app.log
LOGGER_MAX_SIZE=100
LOGGER_MAX_AGE=30
LOGGER_MAX_BACKUPS=10
LOGGER_COMPRESS=true

# JWT安全配置
SECURITY_JWT_SECRET_KEY=development_jwt_secret_key_change_in_production_very_long_secret
SECURITY_JWT_ACCESS_TOKEN_DURATION=24h
SECURITY_JWT_REFRESH_TOKEN_DURATION=168h
SECURITY_JWT_ISSUER=9-wings-erp

# Casbin配置
SECURITY_CASBIN_MODEL_PATH=configs/casbin_model.conf
SECURITY_CASBIN_TABLE_NAME=casbin_rules

# CORS配置
SECURITY_CORS_ALLOWED_ORIGINS=*
SECURITY_CORS_ALLOWED_METHODS=GET,POST,PUT,DELETE,OPTIONS
SECURITY_CORS_ALLOWED_HEADERS=*

# 限流配置
SECURITY_RATE_LIMIT_ENABLE=true
SECURITY_RATE_LIMIT_RPS=100
SECURITY_RATE_LIMIT_BURST=200

# 监控配置
MONITORING_METRICS_ENABLE=true
MONITORING_METRICS_PATH=/metrics
MONITORING_METRICS_PORT=9090
MONITORING_TRACING_ENABLE=false
MONITORING_TRACING_ENDPOINT=http://localhost:14268/api/traces
MONITORING_TRACING_SERVICE=9-wings-erp
MONITORING_TRACING_ENVIRONMENT=development
MONITORING_TRACING_VERSION=1.0.0
MONITORING_HEALTH_ENABLE=true
MONITORING_HEALTH_PATH=/health

# 业务配置
BUSINESS_SNOWFLAKE_MACHINE_ID=1
BUSINESS_UPLOAD_MAX_SIZE=10485760
BUSINESS_UPLOAD_ALLOWED_TYPES=jpg,jpeg,png,gif,pdf,doc,docx,xls,xlsx
BUSINESS_UPLOAD_PATH=uploads
BUSINESS_CACHE_DEFAULT_TTL=1h
BUSINESS_CACHE_MAX_KEYS=10000

# ===========================================
# 环境特定配置示例
# ===========================================

# 开发环境专用配置
# ERP_LOG_LEVEL=debug
# ERP_LOG_FORMAT=text
# ERP_RATE_LIMIT_ENABLE=false
# ERP_TRACING_ENABLE=true
# ERP_UPLOAD_PATH=./var/uploads/dev

# 生产环境专用配置
# ERP_DB_HOST=prod-postgres.internal
# ERP_DB_SSL_MODE=require
# ERP_REDIS_HOST=prod-redis.internal
# ERP_JWT_SECRET=VERY_STRONG_SECRET_KEY_FOR_PRODUCTION_AT_LEAST_64_CHARACTERS
# ERP_LOG_LEVEL=warn
# ERP_LOG_FORMAT=json
# ERP_SNOWFLAKE_MACHINE_ID=10

# 测试环境专用配置
# ERP_DB_NAME=erp_test
# ERP_LOG_LEVEL=error
# ERP_CACHE_DEFAULT_TTL=1m 