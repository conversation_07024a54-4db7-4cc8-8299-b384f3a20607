//go:build config_test
// +build config_test

package main

import (
	"fmt"
	"log"
	"os"
	"path/filepath"

	"backend/pkg/infrastructure/config"

	"github.com/joho/godotenv"
)

func main() {
	fmt.Println("=== 九翼ERP配置测试程序 ===")

	// 显示当前工作目录
	wd, _ := os.Getwd()
	fmt.Printf("当前工作目录: %s\n", wd)

	// 手动测试.env文件加载
	fmt.Println("\n=== 手动测试.env文件加载 ===")
	env := os.Getenv("APP_ENV")
	if env == "" {
		env = "development"
	}
	fmt.Printf("环境: %s\n", env)

	// 测试加载各个.env文件
	envFiles := []string{
		".env",
		fmt.Sprintf(".env.%s", env),
		".env.local",
	}

	for _, file := range envFiles {
		fmt.Printf("尝试加载文件: %s\n", file)
		if _, err := os.Stat(file); err == nil {
			fmt.Printf("  ✅ 文件存在\n")

			// 尝试读取文件内容
			envMap, err := godotenv.Read(file)
			if err != nil {
				fmt.Printf("  ❌ 读取失败: %v\n", err)
			} else {
				fmt.Printf("  ✅ 读取成功，包含 %d 个配置项\n", len(envMap))

				// 显示迁移相关配置
				migrationKeys := []string{
					"ERP_DB_MIGRATION_AUTO_MIGRATE",
					"ERP_DB_MIGRATION_MIGRATIONS_DIR",
					"ERP_DB_MIGRATION_FAIL_ON_ERROR",
					"ERP_DB_MIGRATION_LOG_MIGRATIONS",
				}

				for _, key := range migrationKeys {
					if value, exists := envMap[key]; exists {
						fmt.Printf("    %s = %s\n", key, value)
					}
				}
			}
		} else {
			fmt.Printf("  ❌ 文件不存在\n")
		}
	}

	// 显示环境变量
	fmt.Println("\n=== 环境变量检查 ===")
	envVars := []string{
		"APP_ENV",
		"ERP_DB_MIGRATION_AUTO_MIGRATE",
		"ERP_DB_MIGRATION_MIGRATIONS_DIR",
		"ERP_DB_MIGRATION_FAIL_ON_ERROR",
		"ERP_DB_MIGRATION_LOG_MIGRATIONS",
	}

	for _, envVar := range envVars {
		value := os.Getenv(envVar)
		if value != "" {
			fmt.Printf("%s = %s\n", envVar, value)
		} else {
			fmt.Printf("%s = <未设置>\n", envVar)
		}
	}

	// 检查迁移文件夹是否存在
	fmt.Println("\n=== 迁移文件夹检查 ===")
	migrationPaths := []string{
		"migrations/postgres",
		"./migrations/postgres",
		filepath.Join(wd, "migrations/postgres"),
	}

	for _, path := range migrationPaths {
		if stat, err := os.Stat(path); err == nil && stat.IsDir() {
			fmt.Printf("✅ %s 存在\n", path)

			// 列出迁移文件
			files, err := os.ReadDir(path)
			if err == nil {
				fmt.Printf("   包含 %d 个文件:\n", len(files))
				for _, file := range files {
					if !file.IsDir() {
						fmt.Printf("   - %s\n", file.Name())
					}
				}
			}
		} else {
			fmt.Printf("❌ %s 不存在\n", path)
		}
	}

	// 加载配置
	fmt.Println("\n=== 配置加载测试 ===")
	cfg, err := config.LoadFromPath("configs")
	if err != nil {
		log.Fatalf("加载配置失败: %v", err)
	}

	fmt.Printf("数据库迁移配置:\n")
	fmt.Printf("  AutoMigrate: %v\n", cfg.Database.Migration.AutoMigrate)
	fmt.Printf("  MigrationsDir: %s\n", cfg.Database.Migration.MigrationsDir)
	fmt.Printf("  FailOnError: %v\n", cfg.Database.Migration.FailOnError)
	fmt.Printf("  LogMigrations: %v\n", cfg.Database.Migration.LogMigrations)

	// 检查绝对路径
	fmt.Println("\n=== 绝对路径检查 ===")
	absPath := cfg.Database.Migration.MigrationsDir
	if !filepath.IsAbs(absPath) {
		absPath = filepath.Join(wd, absPath)
	}
	fmt.Printf("迁移文件绝对路径: %s\n", absPath)

	if stat, err := os.Stat(absPath); err == nil && stat.IsDir() {
		fmt.Printf("✅ 绝对路径存在\n")
	} else {
		fmt.Printf("❌ 绝对路径不存在: %v\n", err)
	}
}
