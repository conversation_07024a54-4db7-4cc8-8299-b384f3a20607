package main

import (
	"context"
	"flag"
	"fmt"
	"net/http"
	"os"
	"os/signal"
	"syscall"
	"time"

	_ "backend/api/openapi" // 导入生成的文档
	"backend/internal/shared/di/injector"
)

// @title 九翼跨境电商ERP系统API
// @version 1.0
// @description 基于DDD架构的跨境电商ERP管理系统
// @termsOfService http://swagger.io/terms/

// @contact.name API支持团队
// @contact.url http://www.swagger.io/support
// @contact.email <EMAIL>

// @license.name MIT
// @license.url https://opensource.org/licenses/MIT

// @host localhost:8080
// @BasePath /api/v1

// @securityDefinitions.apikey ApiKeyAuth
// @in header
// @name Authorization
// @description Bearer JWT令牌认证，格式：Bearer {token}

// @tag.name 认证
// @tag.description 用户认证和授权相关接口

// @tag.name 用户管理
// @tag.description 用户信息管理接口

// @tag.name 租户管理
// @tag.description 多租户管理接口

// @tag.name 产品管理
// @tag.description 产品信息管理接口

// @tag.name 订单管理
// @tag.description 订单处理相关接口

// @tag.name 采购管理
// @tag.description 采购流程管理接口

// @tag.name 库存管理
// @tag.description 库存监控和管理接口

// @tag.name 财务管理
// @tag.description 财务数据和报表接口

// @tag.name 系统管理
// @tag.description 系统配置和监控接口

func main() {
	// -- 1. 配置加载方式改变 --
	// 使用flag来指定配置文件路径，更灵活
	configPath := flag.String("config", "./configs", "path to config directory")
	flag.Parse()

	// -- 2. 初始化方式改变 --
	// 调用新的injector来初始化应用
	app, cleanup, err := injector.InitializeApp(*configPath)
	if err != nil {
		fmt.Printf("[FATAL] Failed to initialize application: %v\n", err)
		os.Exit(1)
	}
	defer cleanup()

	// 从容器中获取依赖
	cfg := app.Config
	logger := app.Logger
	router := app.Router

	// -- 3. 启动逻辑微调 --
	// 设置路由
	router.SetupRoutes()

	// 创建HTTP服务器
	server := &http.Server{
		Addr:         fmt.Sprintf("%s:%d", cfg.Server.Host, cfg.Server.Port),
		Handler:      router.GetEngine(),
		ReadTimeout:  cfg.Server.ReadTimeout,
		WriteTimeout: cfg.Server.WriteTimeout,
		IdleTimeout:  cfg.Server.IdleTimeout,
	}

	// 启动服务器
	go func() {
		logger.Info(context.Background(), fmt.Sprintf("HTTP server is starting on %s", server.Addr))
		if err := server.ListenAndServe(); err != nil && err != http.ErrServerClosed {
			logger.Warn(context.Background(), "Failed to start HTTP server", "error", err)
		}
	}()

	// 等待中断信号
	quit := make(chan os.Signal, 1)
	signal.Notify(quit, syscall.SIGINT, syscall.SIGTERM)
	<-quit

	logger.Info(context.Background(), "Shutting down server...")

	// 优雅关闭
	ctx, cancel := context.WithTimeout(context.Background(), 30*time.Second)
	defer cancel()

	if err := server.Shutdown(ctx); err != nil {
		logger.Error(context.Background(), "Server forced to shutdown", "error", err)
	}

	logger.Info(context.Background(), "Server exiting")
}
