package main

import (
	"flag"
	"fmt"
	"log"
	"os"

	"backend/pkg/infrastructure/config"
	"backend/pkg/infrastructure/database/postgres"
)

func main() {
	var (
		flags = flag.NewFlagSet("migrate", flag.ExitOnError)
		dir   = flags.String("dir", "migrations/postgres", "directory with migration files")
	)
	flags.Parse(os.Args[1:])
	args := flags.Args()

	if len(args) == 0 {
		printUsage()
		return
	}

	// 加载配置
	cfg, err := config.LoadFromPath("configs")
	if err != nil {
		log.Fatalf("Failed to load config: %v", err)
	}

	// 创建数据库管理器
	manager := postgres.NewDatabaseManager(&cfg.Database)

	// 初始化数据库管理器
	if err := manager.Initialize(); err != nil {
		log.Fatalf("Failed to initialize database manager: %v", err)
	}
	defer manager.Close()

	// 获取迁移器
	migrator := manager.GetMigrator()

	command := args[0]
	switch command {
	case "up":
		if err := migrator.ApplyMigrations(); err != nil {
			log.Fatalf("Migration up failed: %v", err)
		}
		fmt.Println("Migration up completed successfully")

	case "down":
		if err := migrator.RollbackMigration(); err != nil {
			log.Fatalf("Migration down failed: %v", err)
		}
		fmt.Println("Migration down completed successfully")

	case "status":
		if err := migrator.GetMigrationStatus(); err != nil {
			log.Fatalf("Migration status failed: %v", err)
		}

	case "create":
		if len(args) < 2 {
			log.Fatal("create command requires migration name")
		}
		// 创建迁移文件需要直接使用goose
		log.Printf("Creating migration file: %s", args[1])
		fmt.Printf("Please use: goose -dir %s create %s sql\n", *dir, args[1])

	case "version":
		version, err := migrator.GetCurrentVersion()
		if err != nil {
			log.Fatalf("Failed to get version: %v", err)
		}
		fmt.Printf("Current database version: %d\n", version)

	case "reset":
		if err := migrator.ResetMigrations(); err != nil {
			log.Fatalf("Migration reset failed: %v", err)
		}
		fmt.Println("Migration reset completed successfully")

	default:
		log.Fatalf("Unknown command: %s", command)
	}
}

func printUsage() {
	fmt.Println("Usage: go run cmd/migrate/main.go [options] <command> [args]")
	fmt.Println("")
	fmt.Println("Commands:")
	fmt.Println("  up                   Apply all pending migrations")
	fmt.Println("  down                 Roll back the last migration")
	fmt.Println("  status               Show migration status")
	fmt.Println("  create <name>        Create a new migration file (shows goose command)")
	fmt.Println("  version              Show current database version")
	fmt.Println("  reset                Show reset command (for safety)")
	fmt.Println("")
	fmt.Println("Options:")
	fmt.Println("  -dir string          Directory with migration files (default: migrations/postgres)")
	fmt.Println("")
	fmt.Println("Examples:")
	fmt.Println("  go run cmd/migrate/main.go up")
	fmt.Println("  go run cmd/migrate/main.go create add_users_table")
	fmt.Println("  go run cmd/migrate/main.go status")
}
