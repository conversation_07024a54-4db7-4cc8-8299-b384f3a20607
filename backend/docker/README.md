# Docker部署指南

## 概述

本目录包含九翼跨境电商ERP系统的Docker部署配置。支持多环境部署和容器化开发。

## 文件结构

```
docker/
├── nginx/
│   ├── nginx.conf          # Nginx主配置
│   └── conf.d/
│       └── default.conf    # API站点配置
├── Dockerfile              # 应用程序镜像
├── docker-compose.yml      # 基础服务配置
├── docker-compose.development.yml    # 开发环境覆盖
├── docker-compose.testing.yml        # 测试环境覆盖
├── docker-compose.production.yml     # 生产环境覆盖
└── README.md              # 本文档
```

## 快速开始

### 使用Makefile（推荐）

```bash
# 启动开发环境
make dev

# 启动测试环境
make test-env

# 启动生产环境
make prod

# 查看服务状态
make status

# 查看日志
make logs

# 停止服务
make down
```

### 直接使用Docker Compose

```bash
# 开发环境
docker-compose -f docker-compose.yml -f docker-compose.development.yml up -d

# 测试环境
docker-compose -f docker-compose.yml -f docker-compose.testing.yml up -d

# 生产环境
docker-compose -f docker-compose.yml -f docker-compose.production.yml up -d
```

## 环境配置

### 开发环境

- **特点**: 支持热重载，包含开发工具
- **端口**: 8080 (API), 8081 (Adminer), 8082 (Redis Commander)
- **工具**: Adminer数据库管理, Redis Commander缓存管理

### 测试环境

- **特点**: 隔离的测试数据库，支持自动化测试
- **端口**: 8080 (API), 5433 (PostgreSQL), 6380 (Redis)
- **工具**: 测试运行器，集成测试支持

### 生产环境

- **特点**: 高性能，高安全性，包含监控
- **服务**: Nginx反向代理, Prometheus监控, Grafana可视化
- **安全**: 不暴露内部端口，资源限制，健康检查

## 服务说明

### 核心服务

- **api**: ERP API服务
- **postgres**: PostgreSQL数据库
- **redis**: Redis缓存和消息队列

### 生产环境额外服务

- **nginx**: 反向代理和负载均衡
- **prometheus**: 指标收集
- **grafana**: 监控仪表盘
- **filebeat**: 日志收集

## 数据持久化

### 开发环境

- postgres_dev_data: 开发数据库数据
- redis_dev_data: 开发Redis数据

### 生产环境

- /data/erp/postgres: 生产数据库数据
- /data/erp/redis: 生产Redis数据
- /var/log/erp: 应用日志

## 监控和日志

### 健康检查

```bash
# 检查所有服务状态
make status

# 检查API健康状态
make health

# 查看指标
make metrics
```

### 访问监控

- Grafana: http://localhost:3000 (admin/admin)
- Prometheus: http://localhost:9001

## 故障排除

### 常见问题

1. **端口冲突**: 检查端口是否被占用
2. **数据库连接失败**: 等待数据库健康检查通过
3. **配置加载失败**: 检查.env文件是否正确设置

### 调试命令

```bash
# 查看服务日志
docker-compose logs api

# 进入容器调试
docker-compose exec api sh

# 重建服务
docker-compose up -d --build api

# 清理所有资源
make clean-all
```

## 安全注意事项

### 生产环境

1. 修改默认密码
2. 配置SSL证书
3. 设置防火墙规则
4. 定期更新镜像
5. 监控日志和指标 