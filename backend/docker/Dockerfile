# 九翼跨境电商ERP系统 - Docker镜像
# 多阶段构建，优化镜像大小和安全性

# 构建阶段
FROM golang:1.24.4-alpine AS builder

# 构建参数
ARG VERSION=dev
ARG BUILD_TIME
ARG COMMIT_HASH

# 设置工作目录
WORKDIR /app

# 安装必要的工具
RUN apk add --no-cache git ca-certificates tzdata make

# 复制go.mod和go.sum文件
COPY go.mod go.sum ./

# 下载依赖
RUN go mod download

# 复制源代码
COPY . .

# 确保模块缓存正确
RUN go mod tidy

# 设置Go环境变量，确保二进制文件安装到正确位置
ENV GOBIN=/go/bin
ENV PATH=$PATH:/go/bin
ENV GOPROXY=https://goproxy.cn

# 安装swag工具并验证安装
RUN echo "安装swag工具..." && \
    go install github.com/swaggo/swag/cmd/swag@latest && \
    echo "验证swag工具安装..." && \
    ls -la /go/bin/ && \
    /go/bin/swag --version

# 生成Swagger文档
RUN echo "生成Swagger文档..." && \
    mkdir -p docs && \
    /go/bin/swag init -g cmd/api/main.go -o docs --parseInternal --parseDependency --parseDepth 2 && \
    echo "验证Swagger文档生成..." && \
    ls -la docs/ && \
    test -f docs/swagger.json || (echo "Swagger文档生成失败" && exit 1)

# 构建应用程序
RUN echo "构建应用程序..." && \
    echo "VERSION: ${VERSION}" && \
    echo "BUILD_TIME: ${BUILD_TIME}" && \
    echo "COMMIT_HASH: ${COMMIT_HASH}" && \
    CGO_ENABLED=0 GOOS=linux GOARCH=amd64 go build \
    -ldflags="-w -s -X main.Version=${VERSION} -X main.BuildTime=${BUILD_TIME} -X main.CommitHash=${COMMIT_HASH}" \
    -o /app/bin/erp-api \
    cmd/api/main.go && \
    echo "验证构建结果..." && \
    ls -la /app/bin/ && \
    test -f /app/bin/erp-api || (echo "应用程序构建失败" && exit 1)

# 运行阶段
FROM alpine:3.19 AS runtime

# 添加ca证书和时区数据
RUN apk --no-cache add ca-certificates tzdata curl

# 创建非root用户
RUN addgroup -g 1001 -S appgroup && \
    adduser -u 1001 -S appuser -G appgroup

# 设置工作目录
WORKDIR /app

# 创建必要的目录
RUN mkdir -p /app/configs /app/logs /app/uploads && \
    chown -R appuser:appgroup /app

# 从构建阶段复制二进制文件和配置
COPY --from=builder /app/bin/erp-api /app/erp-api
COPY --from=builder /app/configs /app/configs
COPY --from=builder /app/api/openapi /app/api/openapi

# 设置权限
RUN chmod +x /app/erp-api && \
    chown -R appuser:appgroup /app

# 切换到非root用户
USER appuser

# 暴露端口
EXPOSE 8080 9090 9100

# 健康检查
HEALTHCHECK --interval=30s --timeout=10s --start-period=5s --retries=3 \
    CMD curl -f http://localhost:8080/health || exit 1

# 设置环境变量
ENV APP_ENV=production
ENV ERP_CONFIG_PATH=/app/configs

# 启动命令
CMD ["/app/erp-api"] 