# 九翼跨境电商ERP系统 - Docker Compose基础配置
services:
  # API服务
  api:
    build:
      context: ..
      dockerfile: docker/Dockerfile
      args:
        VERSION: ${VERSION:-dev}
        BUILD_TIME: ${BUILD_TIME:-$(date '+%Y-%m-%d_%H:%M:%S')}
        COMMIT_HASH: ${COMMIT_HASH:-$(git rev-parse --short HEAD 2>/dev/null || echo 'unknown')}
    container_name: erp-api
    ports:
      - "${ERP_SERVER_PORT:-8080}:8080"
      - "${ERP_GRPC_PORT:-9090}:9090"
      - "${ERP_METRICS_PORT:-9100}:9100"
    env_file:
      - ../.env
    environment:
      - APP_ENV=${APP_ENV:-development}
      - ERP_CONFIG_PATH=/app/configs
    volumes:
      - ../var/logs:/app/logs
      - ../var/uploads:/app/uploads
    depends_on:
      postgres:
        condition: service_healthy
      redis:
        condition: service_healthy
    networks:
      - erp-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8080/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

  # PostgreSQL数据库
  postgres:
    image: postgres:17-alpine
    container_name: erp-postgres
    environment:
      POSTGRES_DB: ${ERP_DB_NAME:-erp_development}
      POSTGRES_USER: ${ERP_DB_USER:-erp_dev}
      POSTGRES_PASSWORD: ${ERP_DB_PASSWORD:-dev_password_123}
      POSTGRES_INITDB_ARGS: "--encoding=UTF8 --locale=C"
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ../migrations/postgres:/docker-entrypoint-initdb.d
    ports:
      - "${ERP_DB_PORT:-5432}:5432"
    networks:
      - erp-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U ${ERP_DB_USER:-erp_dev} -d ${ERP_DB_NAME:-erp_development}"]
      interval: 10s
      timeout: 5s
      retries: 5

  # Redis缓存
  redis:
    image: redis:7-alpine
    container_name: erp-redis
    command: >
      redis-server
      --appendonly yes
      --requirepass ${ERP_REDIS_PASSWORD:-}
      --maxmemory 256mb
      --maxmemory-policy allkeys-lru
    volumes:
      - redis_data:/data
    ports:
      - "${ERP_REDIS_PORT:-6379}:6379"
    networks:
      - erp-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 10s
      timeout: 3s
      retries: 5

  # 数据库迁移工具
  migrate:
    build:
      context: ..
      dockerfile: docker/Dockerfile
    container_name: erp-migrate
    env_file:
      - ../.env
    environment:
      - APP_ENV=${APP_ENV:-development}
      - ERP_CONFIG_PATH=/app/configs
    volumes:
      - ../migrations:/app/migrations
    depends_on:
      postgres:
        condition: service_healthy
    networks:
      - erp-network
    command: ["go", "run", "cmd/migrate/main.go", "up"]
    profiles:
      - migration

# 网络配置
networks:
  erp-network:
    driver: bridge
    name: erp-network

# 数据卷
volumes:
  postgres_data:
    name: erp-postgres-data
  redis_data:
    name: erp-redis-data 