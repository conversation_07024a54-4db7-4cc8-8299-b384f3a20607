# 生产环境 Docker Compose 覆盖配置
services:
  api:
    image: ${DOCKER_IMAGE:-nine-wings-erp}:${DOCKER_TAG:-latest}
    container_name: erp-api-prod
    environment:
      - APP_ENV=production
      - ERP_LOG_LEVEL=warn
      - ERP_LOG_FORMAT=json
      - ERP_LOG_OUTPUT=file
      - ERP_LOG_FILE=/app/logs/app.log
    # 生产环境不暴露内部端口
    ports:
      - "80:8080"  # HTTP
      - "443:8080" # HTTPS (需要反向代理)
    volumes:
      - /var/log/erp:/app/logs
      - /data/erp/uploads:/app/uploads
    deploy:
      replicas: 2
      resources:
        limits:
          cpus: '1'
          memory: 1G
        reservations:
          cpus: '0.5'
          memory: 512M
      restart_policy:
        condition: any
        delay: 5s
        max_attempts: 3
        window: 120s
    restart: unless-stopped

  postgres:
    image: postgres:17-alpine
    container_name: erp-postgres-prod
    environment:
      POSTGRES_DB: ${ERP_DB_NAME}
      POSTGRES_USER: ${ERP_DB_USER}
      POSTGRES_PASSWORD: ${ERP_DB_PASSWORD}
      POSTGRES_INITDB_ARGS: "--encoding=UTF8 --locale=C"
    # 生产环境不暴露数据库端口
    # ports:
    #   - "5432:5432"
    volumes:
      - /data/erp/postgres:/var/lib/postgresql/data
      - ./configs/postgres/postgresql.conf:/etc/postgresql/postgresql.conf
    command: >
      postgres
      -c config_file=/etc/postgresql/postgresql.conf
      -c shared_preload_libraries=pg_stat_statements
      -c logging_collector=on
      -c log_directory=/var/log/postgresql
      -c log_filename=postgresql-%Y-%m-%d_%H%M%S.log
      -c log_statement=all
      -c log_min_duration_statement=1000
    deploy:
      resources:
        limits:
          cpus: '2'
          memory: 2G
        reservations:
          cpus: '1'
          memory: 1G
    restart: unless-stopped

  redis:
    image: redis:7-alpine
    container_name: erp-redis-prod
    command: >
      redis-server
      --appendonly yes
      --requirepass ${ERP_REDIS_PASSWORD}
      --maxmemory 1gb
      --maxmemory-policy allkeys-lru
      --save 900 1
      --save 300 10
      --save 60 10000
    # 生产环境不暴露Redis端口
    # ports:
    #   - "6379:6379"
    volumes:
      - /data/erp/redis:/data
    deploy:
      resources:
        limits:
          cpus: '0.5'
          memory: 1G
        reservations:
          cpus: '0.25'
          memory: 512M
    restart: unless-stopped

  # Nginx反向代理
  nginx:
    image: nginx:alpine
    container_name: erp-nginx-prod
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./docker/nginx/nginx.conf:/etc/nginx/nginx.conf
      - ./docker/nginx/conf.d:/etc/nginx/conf.d
      - /etc/ssl/certs:/etc/ssl/certs
      - /var/log/nginx:/var/log/nginx
    depends_on:
      - api
    networks:
      - erp-network
    restart: unless-stopped

  # 监控：Prometheus
  prometheus:
    image: prom/prometheus:latest
    container_name: erp-prometheus-prod
    ports:
      - "9001:9090"
    volumes:
      - ./docker/prometheus/prometheus.yml:/etc/prometheus/prometheus.yml
      - prometheus_data:/prometheus
    command:
      - '--config.file=/etc/prometheus/prometheus.yml'
      - '--storage.tsdb.path=/prometheus'
      - '--web.console.libraries=/etc/prometheus/console_libraries'
      - '--web.console.templates=/etc/prometheus/consoles'
      - '--storage.tsdb.retention.time=200h'
      - '--web.enable-lifecycle'
    networks:
      - erp-network
    restart: unless-stopped
    profiles:
      - monitoring

  # 监控：Grafana
  grafana:
    image: grafana/grafana:latest
    container_name: erp-grafana-prod
    ports:
      - "3000:3000"
    environment:
      - GF_SECURITY_ADMIN_PASSWORD=${GRAFANA_PASSWORD:-admin}
    volumes:
      - grafana_data:/var/lib/grafana
      - ./docker/grafana/provisioning:/etc/grafana/provisioning
    networks:
      - erp-network
    restart: unless-stopped
    profiles:
      - monitoring

  # 监控：Jaeger
  jaeger:
    image: jaegertracing/all-in-one:latest
    container_name: erp-jaeger-prod
    ports:
      - "16686:16686" # Jaeger UI
      - "14268:14268" # Jaeger HTTP collector
      - "4317:4317"   # OTLP gRPC receiver
      - "4318:4318"   # OTLP HTTP receiver
    environment:
      - COLLECTOR_OTLP_ENABLED=true
    networks:
      - erp-network
    restart: unless-stopped
    profiles:
      - monitoring

  # 日志收集：Filebeat
  filebeat:
    image: docker.elastic.co/beats/filebeat:8.10.4
    container_name: erp-filebeat-prod
    user: root
    volumes:
      - ./docker/filebeat/filebeat.yml:/usr/share/filebeat/filebeat.yml
      - /var/log/erp:/var/log/erp
      - /var/lib/docker/containers:/var/lib/docker/containers:ro
      - /var/run/docker.sock:/var/run/docker.sock:ro
    networks:
      - erp-network
    restart: unless-stopped
    profiles:
      - logging

volumes:
  prometheus_data:
    name: erp-prometheus-prod-data
  grafana_data:
    name: erp-grafana-prod-data 