# 开发环境 Docker Compose 覆盖配置
services:
  api:
    build:
      target: builder
      args:
        VERSION: ${VERSION:-dev}
        BUILD_TIME: ${BUILD_TIME:-$(date '+%Y-%m-%d_%H:%M:%S')}
        COMMIT_HASH: ${COMMIT_HASH:-$(git rev-parse --short HEAD 2>/dev/null || echo 'unknown')}
    container_name: erp-api-dev
    volumes:
      - ..:/app  # 映射整个项目目录到容器内
      - /app/bin  # 避免本地bin目录覆盖容器内的构建文件
    environment:
      - APP_ENV=development
      - ERP_LOG_LEVEL=debug
      - ERP_LOG_FORMAT=text
      - ERP_DB_HOST=postgres # 容器内通过服务名访问数据库
      - ERP_REDIS_HOST=redis # 容器内通过服务名访问Redis
    ports:
      - "8080:8080"
      - "9090:9090"
      - "9100:9100"
    # 开发环境使用热重载
    command: >
      sh -c "
        go install github.com/air-verse/air@latest &&
        air -c .air.toml
        # cd /app &&
        # go run cmd/api/main.go
      "
    networks:
      - erp-network
    restart: unless-stopped

  postgres:
    container_name: erp-postgres-dev
    environment:
      POSTGRES_DB: erp_development
      POSTGRES_USER: erp_dev
      POSTGRES_PASSWORD: dev_password_123
      TZ: UTC
      PGTZ: UTC
    ports:
      - "5432:5432"
    # 开发环境可以访问数据库
    volumes:
      - postgres_dev_data:/var/lib/postgresql/data
    networks:
      - erp-network

  redis:
    container_name: erp-redis-dev
    ports:
      - "6379:6379"
    volumes:
      - redis_dev_data:/data
    # 开发环境不设置密码
    command: redis-server --appendonly yes
    networks:
      - erp-network

  # Jaeger分布式追踪
  jaeger:
    image: jaegertracing/all-in-one:latest
    container_name: erp-jaeger-dev
    ports:
      - "16686:16686" # Jaeger UI
      - "14268:14268" # Jaeger HTTP collector
      - "4317:4317" # OTLP gRPC receiver
      - "4318:4318" # OTLP HTTP receiver
    environment:
      - COLLECTOR_OTLP_ENABLED=true
    networks:
      - erp-network
    restart: unless-stopped
    profiles:
      - tools

  # 开发工具
  adminer:
    image: adminer:4.8.1
    container_name: erp-adminer-dev
    ports:
      - "8081:8080"
    environment:
      ADMINER_DEFAULT_SERVER: postgres
    networks:
      - erp-network
    profiles:
      - tools

  # Redis管理工具
  redis-commander:
    image: rediscommander/redis-commander:latest
    container_name: erp-redis-commander-dev
    ports:
      - "8082:8081"
    environment:
      REDIS_HOSTS: local:redis:6379
    networks:
      - erp-network
    profiles:
      - tools

volumes:
  postgres_dev_data:
    name: erp-postgres-dev-data
  redis_dev_data:
    name: erp-redis-dev-data

networks:
  erp-network:
    driver: bridge
    name: erp-dev-network 