-- +goose Up
-- 创建租户相关的所有表

-- ========================================
-- 1. 创建租户主表
-- ========================================
CREATE TABLE IF NOT EXISTS tenants (
    -- 技术ID：雪花ID主键
    id              BIGINT PRIMARY KEY,
    
    -- 业务ID：UUID唯一标识
    business_id     VARCHAR(36) NOT NULL UNIQUE,
    
    -- 租户基本信息
    name            VARCHAR(255) NOT NULL,
    domain          VARCHAR(100) UNIQUE,
    display_name    VARCHAR(255) NOT NULL,
    description     TEXT,
    
    -- 租户类型和分类
    tenant_type     INTEGER NOT NULL DEFAULT 1,
    industry        VARCHAR(100),
    
    -- 地理位置信息
    country         VARCHAR(100),
    province        VARCHAR(100),
    city            VARCHAR(100),
    address         TEXT,
    
    -- 本地化配置
    timezone        VARCHAR(100) DEFAULT 'UTC',
    language        VARCHAR(10) DEFAULT 'en',
    currency        VARCHAR(10) DEFAULT 'USD',
    
    -- 租户规模信息
    company_size    VARCHAR(50),
    employee_count  INTEGER DEFAULT 0,
    
    -- 租户状态和有效期
    status          INTEGER NOT NULL DEFAULT 1,
    expires_at      TIMESTAMP WITH TIME ZONE,
    
    -- 层级关系（支持子租户功能）
    parent_tenant_id VARCHAR(36),
    
    -- 联系信息
    contact_email   VARCHAR(255),
    contact_phone   VARCHAR(50),
    
    -- 扩展字段（JSONB存储）
    settings        JSONB NOT NULL DEFAULT '{}',
    tags            JSONB DEFAULT '[]',
    custom_fields   JSONB DEFAULT '{}',
    
    -- 审计字段
    created_at      TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at      TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    deleted_at      TIMESTAMP WITH TIME ZONE DEFAULT NULL,
    version         INTEGER DEFAULT 1
);

-- 创建租户表基础索引
CREATE INDEX IF NOT EXISTS idx_tenants_business_id ON tenants(business_id);
CREATE INDEX IF NOT EXISTS idx_tenants_name ON tenants(name);
CREATE INDEX IF NOT EXISTS idx_tenants_status ON tenants(status);
CREATE INDEX IF NOT EXISTS idx_tenants_created_at ON tenants(created_at);

-- 扩展字段索引
CREATE INDEX IF NOT EXISTS idx_tenants_tenant_type ON tenants(tenant_type);
CREATE INDEX IF NOT EXISTS idx_tenants_industry ON tenants(industry);
CREATE INDEX IF NOT EXISTS idx_tenants_country ON tenants(country);
CREATE INDEX IF NOT EXISTS idx_tenants_province ON tenants(province);
CREATE INDEX IF NOT EXISTS idx_tenants_city ON tenants(city);
CREATE INDEX IF NOT EXISTS idx_tenants_expires_at ON tenants(expires_at);
CREATE INDEX IF NOT EXISTS idx_tenants_parent_tenant_id ON tenants(parent_tenant_id);

-- JSONB字段的GIN索引
CREATE INDEX IF NOT EXISTS idx_tenants_settings_gin ON tenants USING GIN (settings);
CREATE INDEX IF NOT EXISTS idx_tenants_tags_gin ON tenants USING GIN (tags);
CREATE INDEX IF NOT EXISTS idx_tenants_custom_fields_gin ON tenants USING GIN (custom_fields);

-- 添加外键约束（父租户关系）
ALTER TABLE tenants ADD CONSTRAINT fk_tenants_parent_tenant_id 
    FOREIGN KEY (parent_tenant_id) REFERENCES tenants(business_id) ON DELETE SET NULL;

-- ========================================
-- 2. 创建租户配额表
-- ========================================
CREATE TABLE IF NOT EXISTS tenant_quotas (
    -- 技术ID：雪花ID主键
    id              BIGINT PRIMARY KEY,
    
    -- 业务ID：UUID唯一标识
    business_id     VARCHAR(36) NOT NULL UNIQUE,
    
    -- 关联租户
    tenant_id       VARCHAR(36) NOT NULL,
    
    -- 用户配额
    user_used       INTEGER DEFAULT 0,
    user_limit      INTEGER DEFAULT 10,
    
    -- 存储配额 (MB)
    storage_used    BIGINT DEFAULT 0,
    storage_limit   BIGINT DEFAULT 1024,
    
    -- API配额 (每月)
    api_used_month      INTEGER DEFAULT 0,
    api_limit_month     INTEGER DEFAULT 10000,
    api_reset_date      TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    
    -- 商品配额
    product_used    INTEGER DEFAULT 0,
    product_limit   INTEGER DEFAULT 100,
    
    -- 订单配额 (每月)
    order_used_month    INTEGER DEFAULT 0,
    order_limit_month   INTEGER DEFAULT 1000,
    
    -- 文件上传配额 (每月)
    file_upload_used_month  INTEGER DEFAULT 0,
    file_upload_limit_month INTEGER DEFAULT 1000,
    
    -- 邮件发送配额 (每月)
    email_sent_month    INTEGER DEFAULT 0,
    email_limit_month   INTEGER DEFAULT 500,
    
    -- 审计字段
    created_at      TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at      TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    deleted_at      TIMESTAMP WITH TIME ZONE DEFAULT NULL,
    version         INTEGER DEFAULT 1
);

-- 创建租户配额表索引
CREATE INDEX IF NOT EXISTS idx_tenant_quotas_business_id ON tenant_quotas(business_id);
CREATE INDEX IF NOT EXISTS idx_tenant_quotas_tenant_id ON tenant_quotas(tenant_id);
CREATE INDEX IF NOT EXISTS idx_tenant_quotas_created_at ON tenant_quotas(created_at);

-- 添加外键约束
ALTER TABLE tenant_quotas ADD CONSTRAINT fk_tenant_quotas_tenant_id 
    FOREIGN KEY (tenant_id) REFERENCES tenants(business_id) ON DELETE CASCADE;

-- ========================================
-- 3. 创建租户订阅表
-- ========================================
CREATE TABLE IF NOT EXISTS tenant_subscriptions (
    -- 技术ID：雪花ID主键
    id              BIGINT PRIMARY KEY,
    
    -- 业务ID：UUID唯一标识
    business_id     VARCHAR(36) NOT NULL UNIQUE,
    
    -- 关联租户
    tenant_id       VARCHAR(36) NOT NULL,
    
    -- 订阅计划信息
    plan_id         VARCHAR(100) NOT NULL,
    plan_name       VARCHAR(255) NOT NULL,
    plan_type       VARCHAR(50) NOT NULL DEFAULT 'standard',
    
    -- 订阅状态
    status          INTEGER NOT NULL DEFAULT 1,
    
    -- 订阅时间
    start_date      TIMESTAMP WITH TIME ZONE NOT NULL,
    end_date        TIMESTAMP WITH TIME ZONE NOT NULL,
    
    -- 试用期信息
    is_trial_period BOOLEAN DEFAULT FALSE,
    trial_end_date  TIMESTAMP WITH TIME ZONE,
    
    -- 自动续费
    auto_renewal    BOOLEAN DEFAULT TRUE,
    
    -- 价格信息
    price           DECIMAL(15,2) NOT NULL DEFAULT 0.00,
    currency        VARCHAR(10) NOT NULL DEFAULT 'USD',
    billing_cycle   VARCHAR(20) NOT NULL DEFAULT 'monthly',
    
    -- 支付信息
    payment_method  VARCHAR(50),
    last_payment_date TIMESTAMP WITH TIME ZONE,
    next_payment_date TIMESTAMP WITH TIME ZONE,
    
    -- 取消信息
    cancelled_at    TIMESTAMP WITH TIME ZONE,
    cancellation_reason TEXT,
    
    -- 审计字段
    created_at      TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at      TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    deleted_at      TIMESTAMP WITH TIME ZONE DEFAULT NULL,
    version         INTEGER DEFAULT 1
);

-- 创建租户订阅表索引
CREATE INDEX IF NOT EXISTS idx_tenant_subscriptions_business_id ON tenant_subscriptions(business_id);
CREATE INDEX IF NOT EXISTS idx_tenant_subscriptions_tenant_id ON tenant_subscriptions(tenant_id);
CREATE INDEX IF NOT EXISTS idx_tenant_subscriptions_plan_id ON tenant_subscriptions(plan_id);
CREATE INDEX IF NOT EXISTS idx_tenant_subscriptions_status ON tenant_subscriptions(status);
CREATE INDEX IF NOT EXISTS idx_tenant_subscriptions_start_date ON tenant_subscriptions(start_date);
CREATE INDEX IF NOT EXISTS idx_tenant_subscriptions_end_date ON tenant_subscriptions(end_date);
CREATE INDEX IF NOT EXISTS idx_tenant_subscriptions_created_at ON tenant_subscriptions(created_at);

-- 添加外键约束
ALTER TABLE tenant_subscriptions ADD CONSTRAINT fk_tenant_subscriptions_tenant_id 
    FOREIGN KEY (tenant_id) REFERENCES tenants(business_id) ON DELETE CASCADE;

-- +goose Down
-- 按相反顺序删除表
DROP TABLE IF EXISTS tenant_subscriptions CASCADE;
DROP TABLE IF EXISTS tenant_quotas CASCADE;
DROP TABLE IF EXISTS tenants CASCADE; 