-- +goose Up
-- 创建用户相关的所有表

-- ========================================
-- 1. 创建用户主表
-- ========================================
CREATE TABLE IF NOT EXISTS users (
    -- 技术ID：雪花ID主键
    id                  BIGINT PRIMARY KEY,
    
    -- 业务ID：UUID唯一标识
    business_id         VARCHAR(36) NOT NULL UNIQUE,
    
    -- 用户基本信息 (保留，但不再用于登录唯一性验证)
    username            VARCHAR(100) NOT NULL,
    email               VARCHAR(255) UNIQUE,
    phone               VARCHAR(50) NOT NULL UNIQUE,
    
    -- 用户状态
    status              SMALLINT NOT NULL DEFAULT 1,
    
    -- 用户资料
    avatar              VARCHAR(512),
    nickname            VARCHAR(100),
    first_name          VARCHAR(50),
    last_name           VA<PERSON><PERSON><PERSON>(50),
    language            VARCHAR(10) DEFAULT 'zh-CN',
    timezone            VARCHAR(50) DEFAULT 'Asia/Shanghai',
    metadata            JSONB DEFAULT '{}',
    
    -- 审计字段
    created_at          TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at          TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    deleted_at          TIMESTAMP WITH TIME ZONE,
    version             INT NOT NULL DEFAULT 1
);

-- 用户表索引
CREATE INDEX IF NOT EXISTS idx_users_business_id ON users(business_id);
CREATE INDEX IF NOT EXISTS idx_users_username_profile ON users(username);
CREATE INDEX IF NOT EXISTS idx_users_email_profile ON users(email);
CREATE INDEX IF NOT EXISTS idx_users_phone_profile ON users(phone) WHERE phone IS NOT NULL AND deleted_at IS NULL;
CREATE INDEX IF NOT EXISTS idx_users_status ON users(status);
CREATE INDEX IF NOT EXISTS idx_users_created_at ON users(created_at);
CREATE INDEX IF NOT EXISTS idx_users_deleted_at ON users(deleted_at);

-- ========================================
-- 2. 创建用户认证表
-- ========================================
CREATE TABLE IF NOT EXISTS user_auths (
    -- 技术ID：雪花ID主键
    id                  BIGINT PRIMARY KEY,
    
    -- 业务ID：UUID唯一标识
    business_id         VARCHAR(36) NOT NULL UNIQUE,
    
    -- 用户关联（业务ID）
    user_business_id    VARCHAR(36) NOT NULL,
    
    -- 认证信息
    identity_type       VARCHAR(50) NOT NULL,
    identifier          VARCHAR(255) NOT NULL,
    credential          VARCHAR(500) NOT NULL,
    
    -- 认证状态
    status              INTEGER DEFAULT 1,
    
    -- 审计字段
    created_at          TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at          TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    deleted_at          TIMESTAMP WITH TIME ZONE,
    version             INTEGER DEFAULT 1
);

-- 创建用户认证表索引
CREATE INDEX IF NOT EXISTS idx_user_auths_business_id ON user_auths(business_id);
CREATE INDEX IF NOT EXISTS idx_user_auths_user_business_id ON user_auths(user_business_id);
CREATE UNIQUE INDEX IF NOT EXISTS idx_user_auths_unique ON user_auths(identity_type, identifier) WHERE deleted_at IS NULL;
CREATE INDEX IF NOT EXISTS idx_user_auths_status ON user_auths(status);
CREATE INDEX IF NOT EXISTS idx_user_auths_deleted_at ON user_auths(deleted_at);

-- ========================================
-- 3. 创建角色表
-- ========================================
CREATE TABLE IF NOT EXISTS roles (
    -- 技术ID：雪花ID主键
    id              BIGINT PRIMARY KEY,
    
    -- 业务ID：UUID唯一标识
    business_id     VARCHAR(36) NOT NULL UNIQUE,
    
    -- 租户ID：业务ID
    tenant_id       VARCHAR(36) NOT NULL,
    
    -- 角色信息
    name            VARCHAR(100) NOT NULL,
    display_name    VARCHAR(100) NOT NULL,
    description     VARCHAR(500),
    
    -- 角色状态
    status          INTEGER DEFAULT 1,
    is_system       BOOLEAN DEFAULT FALSE,
    
    -- 审计字段
    created_at      TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at      TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    deleted_at      TIMESTAMP WITH TIME ZONE,
    version         INTEGER DEFAULT 1
);

-- 角色表索引
CREATE INDEX IF NOT EXISTS idx_roles_business_id ON roles(business_id);
CREATE INDEX IF NOT EXISTS idx_roles_tenant_id ON roles(tenant_id);
CREATE UNIQUE INDEX IF NOT EXISTS idx_roles_name ON roles(tenant_id, name) WHERE deleted_at IS NULL;
CREATE INDEX IF NOT EXISTS idx_roles_status ON roles(status);
CREATE INDEX IF NOT EXISTS idx_roles_is_system ON roles(is_system);
CREATE INDEX IF NOT EXISTS idx_roles_deleted_at ON roles(deleted_at);

-- ========================================
-- 4. 创建权限表
-- ========================================
CREATE TABLE IF NOT EXISTS permissions (
    -- 技术ID：雪花ID主键
    id              BIGINT PRIMARY KEY,
    
    -- 业务ID：UUID唯一标识
    business_id     VARCHAR(36) NOT NULL UNIQUE,
    
    -- 权限信息
    resource        VARCHAR(100) NOT NULL,
    action          VARCHAR(100) NOT NULL,
    name            VARCHAR(100) NOT NULL,
    display_name    VARCHAR(100) NOT NULL,
    description     VARCHAR(500),
    
    -- 权限状态
    status          INTEGER DEFAULT 1,
    is_system       BOOLEAN DEFAULT FALSE,
    
    -- 审计字段
    created_at      TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at      TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    deleted_at      TIMESTAMP WITH TIME ZONE,
    version         INTEGER DEFAULT 1
);

-- 权限表索引
CREATE INDEX IF NOT EXISTS idx_permissions_business_id ON permissions(business_id);
CREATE UNIQUE INDEX IF NOT EXISTS idx_permissions_resource_action ON permissions(resource, action) WHERE deleted_at IS NULL;
CREATE INDEX IF NOT EXISTS idx_permissions_resource ON permissions(resource);
CREATE INDEX IF NOT EXISTS idx_permissions_status ON permissions(status);
CREATE INDEX IF NOT EXISTS idx_permissions_is_system ON permissions(is_system);
CREATE INDEX IF NOT EXISTS idx_permissions_deleted_at ON permissions(deleted_at);

-- ========================================
-- 5. 创建用户角色关联表
-- ========================================
CREATE TABLE IF NOT EXISTS user_roles (
    -- 技术ID：雪花ID主键
    id                  BIGINT PRIMARY KEY,
    
    -- 业务ID：UUID唯一标识
    business_id         VARCHAR(36) NOT NULL UNIQUE,
    
    -- 租户ID：业务ID
    tenant_id           VARCHAR(36) NOT NULL,
    
    -- 关联ID（技术ID，用于性能优化）
    user_id             BIGINT NOT NULL,
    role_id             BIGINT NOT NULL,
    
    -- 业务ID（对外暴露）
    user_business_id    VARCHAR(36) NOT NULL,
    role_business_id    VARCHAR(36) NOT NULL,
    
    -- 分配信息
    assigned_by         VARCHAR(36) NOT NULL,
    assigned_at         TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    expires_at          TIMESTAMP WITH TIME ZONE,
    is_active           BOOLEAN DEFAULT TRUE,
    
    -- 审计字段
    created_at          TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at          TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    deleted_at          TIMESTAMP WITH TIME ZONE,
    version             INTEGER DEFAULT 1,
    
    -- 外键约束
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (role_id) REFERENCES roles(id) ON DELETE CASCADE
);

-- 用户角色关联表索引
CREATE INDEX IF NOT EXISTS idx_user_roles_business_id ON user_roles(business_id);
CREATE INDEX IF NOT EXISTS idx_user_roles_tenant_id ON user_roles(tenant_id);
CREATE INDEX IF NOT EXISTS idx_user_roles_user_id ON user_roles(user_id);
CREATE INDEX IF NOT EXISTS idx_user_roles_role_id ON user_roles(role_id);
CREATE INDEX IF NOT EXISTS idx_user_roles_user_business_id ON user_roles(user_business_id);
CREATE INDEX IF NOT EXISTS idx_user_roles_role_business_id ON user_roles(role_business_id);
CREATE UNIQUE INDEX IF NOT EXISTS idx_user_roles_unique ON user_roles(tenant_id, user_business_id, role_business_id) WHERE deleted_at IS NULL;
CREATE INDEX IF NOT EXISTS idx_user_roles_expires_at ON user_roles(expires_at);
CREATE INDEX IF NOT EXISTS idx_user_roles_is_active ON user_roles(is_active);
CREATE INDEX IF NOT EXISTS idx_user_roles_deleted_at ON user_roles(deleted_at);

-- ========================================
-- 6. 创建角色权限关联表
-- ========================================
CREATE TABLE IF NOT EXISTS role_permissions (
    -- 技术ID：雪花ID主键
    id                      BIGINT PRIMARY KEY,
    
    -- 业务ID：UUID唯一标识
    business_id             VARCHAR(36) NOT NULL UNIQUE,
    
    -- 租户ID：业务ID
    tenant_id               VARCHAR(36) NOT NULL,
    
    -- 关联ID（技术ID，用于性能优化）
    role_id                 BIGINT NOT NULL,
    permission_id           BIGINT NOT NULL,
    
    -- 业务ID（对外暴露）
    role_business_id        VARCHAR(36) NOT NULL,
    permission_business_id  VARCHAR(36) NOT NULL,
    
    -- 分配信息
    assigned_by             VARCHAR(36) NOT NULL,
    assigned_at             TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    is_active               BOOLEAN DEFAULT TRUE,
    
    -- 审计字段
    created_at              TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at              TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    deleted_at              TIMESTAMP WITH TIME ZONE,
    version                 INTEGER DEFAULT 1,
    
    -- 外键约束
    FOREIGN KEY (role_id) REFERENCES roles(id) ON DELETE CASCADE,
    FOREIGN KEY (permission_id) REFERENCES permissions(id) ON DELETE CASCADE
);

-- 角色权限关联表索引
CREATE INDEX IF NOT EXISTS idx_role_permissions_business_id ON role_permissions(business_id);
CREATE INDEX IF NOT EXISTS idx_role_permissions_tenant_id ON role_permissions(tenant_id);
CREATE INDEX IF NOT EXISTS idx_role_permissions_role_id ON role_permissions(role_id);
CREATE INDEX IF NOT EXISTS idx_role_permissions_permission_id ON role_permissions(permission_id);
CREATE INDEX IF NOT EXISTS idx_role_permissions_role_business_id ON role_permissions(role_business_id);
CREATE INDEX IF NOT EXISTS idx_role_permissions_permission_business_id ON role_permissions(permission_business_id);
CREATE UNIQUE INDEX IF NOT EXISTS idx_role_permissions_unique ON role_permissions(tenant_id, role_business_id, permission_business_id) WHERE deleted_at IS NULL;
CREATE INDEX IF NOT EXISTS idx_role_permissions_is_active ON role_permissions(is_active);
CREATE INDEX IF NOT EXISTS idx_role_permissions_deleted_at ON role_permissions(deleted_at);

-- ========================================
-- 7. 创建用户会话表
-- ========================================
CREATE TABLE IF NOT EXISTS user_sessions (
    -- 技术ID：雪花ID主键
    id                  BIGINT PRIMARY KEY,
    
    -- 业务ID：UUID唯一标识
    business_id         VARCHAR(36) NOT NULL UNIQUE,
    
    -- 租户ID：业务ID
    tenant_id           VARCHAR(36) NOT NULL,
    
    -- 用户关联（技术ID，用于性能优化）
    user_id             BIGINT NOT NULL,
    
    -- 业务ID（对外暴露）
    user_business_id    VARCHAR(36) NOT NULL,
    
    -- 会话信息
    session_token       VARCHAR(255) NOT NULL UNIQUE,
    refresh_token       VARCHAR(255) NOT NULL UNIQUE,
    device_id           VARCHAR(100),
    device_info         JSONB,
    ip_address          VARCHAR(45),
    user_agent          TEXT,
    
    -- 会话状态
    status              INTEGER DEFAULT 1,
    is_active           BOOLEAN DEFAULT TRUE,
    last_activity       TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    expires_at          TIMESTAMP WITH TIME ZONE NOT NULL,
    revoked_at          TIMESTAMP WITH TIME ZONE,
    revoked_reason      VARCHAR(255),
    
    -- 审计字段
    created_at          TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at          TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    deleted_at          TIMESTAMP WITH TIME ZONE,
    version             INTEGER DEFAULT 1,
    
    -- 外键约束
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
);

-- 用户会话表索引
CREATE INDEX IF NOT EXISTS idx_user_sessions_business_id ON user_sessions(business_id);
CREATE INDEX IF NOT EXISTS idx_user_sessions_tenant_id ON user_sessions(tenant_id);
CREATE INDEX IF NOT EXISTS idx_user_sessions_user_id ON user_sessions(user_id);
CREATE INDEX IF NOT EXISTS idx_user_sessions_user_business_id ON user_sessions(user_business_id);
CREATE UNIQUE INDEX IF NOT EXISTS idx_user_sessions_session_token ON user_sessions(session_token);
CREATE UNIQUE INDEX IF NOT EXISTS idx_user_sessions_refresh_token ON user_sessions(refresh_token);
CREATE INDEX IF NOT EXISTS idx_user_sessions_device_id ON user_sessions(device_id);
CREATE INDEX IF NOT EXISTS idx_user_sessions_status ON user_sessions(status);
CREATE INDEX IF NOT EXISTS idx_user_sessions_is_active ON user_sessions(is_active);
CREATE INDEX IF NOT EXISTS idx_user_sessions_last_activity ON user_sessions(last_activity);
CREATE INDEX IF NOT EXISTS idx_user_sessions_expires_at ON user_sessions(expires_at);
CREATE INDEX IF NOT EXISTS idx_user_sessions_revoked_at ON user_sessions(revoked_at);
CREATE INDEX IF NOT EXISTS idx_user_sessions_deleted_at ON user_sessions(deleted_at);

-- ========================================
-- 8. 创建用户租户关联表
-- ========================================
CREATE TABLE IF NOT EXISTS user_tenants (
    -- 技术ID：雪花ID主键
    id                  BIGINT PRIMARY KEY,
    
    -- 业务ID：UUID唯一标识
    business_id         VARCHAR(36) NOT NULL UNIQUE,
    
    -- 关联的业务ID
    user_business_id    VARCHAR(36) NOT NULL,
    tenant_business_id  VARCHAR(36) NOT NULL,
    role_business_id    VARCHAR(36) NOT NULL,
    
    -- 关联状态
    status              INTEGER DEFAULT 1,
    
    -- 审计字段
    created_at          TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at          TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    deleted_at          TIMESTAMP WITH TIME ZONE,
    version             INTEGER DEFAULT 1
);

-- 为user_tenants表添加tenant_id字段（TenantScopedEntity要求）
ALTER TABLE user_tenants
ADD COLUMN IF NOT EXISTS tenant_id VARCHAR(36);

-- 将tenant_business_id的值复制到tenant_id字段
UPDATE user_tenants
SET tenant_id = tenant_business_id
WHERE tenant_id IS NULL;

-- 设置tenant_id为非空约束
ALTER TABLE user_tenants
ALTER COLUMN tenant_id SET NOT NULL;

-- 创建用户租户关联表索引
CREATE INDEX IF NOT EXISTS idx_user_tenants_business_id ON user_tenants(business_id);
CREATE INDEX IF NOT EXISTS idx_user_tenants_user_business_id ON user_tenants(user_business_id);
CREATE INDEX IF NOT EXISTS idx_user_tenants_tenant_business_id ON user_tenants(tenant_business_id);
CREATE INDEX IF NOT EXISTS idx_user_tenants_role_business_id ON user_tenants(role_business_id);
CREATE INDEX IF NOT EXISTS idx_user_tenants_tenant_id ON user_tenants(tenant_id);
CREATE UNIQUE INDEX IF NOT EXISTS idx_user_tenants_unique ON user_tenants(user_business_id, tenant_business_id) WHERE deleted_at IS NULL;
CREATE INDEX IF NOT EXISTS idx_user_tenants_status ON user_tenants(status);
CREATE INDEX IF NOT EXISTS idx_user_tenants_deleted_at ON user_tenants(deleted_at);

-- ========================================
-- 9. 优化索引以支持新的查询模式
-- ========================================

-- 为用户表添加更好的索引支持全局查询
CREATE INDEX IF NOT EXISTS idx_users_username_unique ON users(username) WHERE deleted_at IS NULL;
CREATE INDEX IF NOT EXISTS idx_users_email_unique ON users(email) WHERE deleted_at IS NULL AND email IS NOT NULL;
CREATE INDEX IF NOT EXISTS idx_users_phone_unique ON users(phone) WHERE deleted_at IS NULL;

-- 为用户认证表添加跨租户查询支持
CREATE INDEX IF NOT EXISTS idx_user_auths_identifier_type ON user_auths(identifier, identity_type) WHERE deleted_at IS NULL;

-- 为用户租户关联表添加租户范围查询支持
CREATE INDEX IF NOT EXISTS idx_user_tenants_tenant_user ON user_tenants(tenant_id, user_business_id) WHERE deleted_at IS NULL;
CREATE INDEX IF NOT EXISTS idx_user_tenants_tenant_role ON user_tenants(tenant_id, role_business_id) WHERE deleted_at IS NULL;

-- ========================================
-- 10. 添加表注释以反映实体继承策略
-- ========================================

COMMENT ON TABLE users IS '用户表 - GlobalEntity: 用户是全局唯一的实体，不属于特定租户';
COMMENT ON TABLE user_auths IS '用户认证表 - MultiTenantEntity: 认证信息可以跨租户复用（如邮箱登录）';
COMMENT ON TABLE user_tenants IS '用户租户关联表 - TenantScopedEntity: 关联关系属于特定租户范围';

COMMENT ON COLUMN users.business_id IS '用户业务ID - 全局唯一标识符';
COMMENT ON COLUMN user_auths.business_id IS '认证业务ID - 可跨租户复用的认证标识符';
COMMENT ON COLUMN user_tenants.business_id IS '关联业务ID - 租户范围内的关联标识符';
COMMENT ON COLUMN user_tenants.tenant_id IS '租户ID - 标识此关联属于哪个租户';

-- +goose Down
-- 回滚更改

-- 移除新增的索引
DROP INDEX IF EXISTS idx_users_username_unique;
DROP INDEX IF EXISTS idx_users_email_unique;
DROP INDEX IF EXISTS idx_users_phone_unique;
DROP INDEX IF EXISTS idx_user_auths_identifier_type;
DROP INDEX IF EXISTS idx_user_tenants_tenant_user;
DROP INDEX IF EXISTS idx_user_tenants_tenant_role;
DROP INDEX IF EXISTS idx_user_tenants_tenant_id;

-- 移除tenant_id字段
ALTER TABLE user_tenants DROP COLUMN IF EXISTS tenant_id;

-- 移除表注释
COMMENT ON TABLE users IS NULL;
COMMENT ON TABLE user_auths IS NULL;
COMMENT ON TABLE user_tenants IS NULL;
COMMENT ON COLUMN users.business_id IS NULL;
COMMENT ON COLUMN user_auths.business_id IS NULL;
COMMENT ON COLUMN user_tenants.business_id IS NULL;

-- 按相反顺序删除表（考虑外键依赖关系）
DROP TABLE IF EXISTS user_tenants CASCADE;
DROP TABLE IF EXISTS user_sessions CASCADE;
DROP TABLE IF EXISTS role_permissions CASCADE;
DROP TABLE IF EXISTS user_roles CASCADE;
DROP TABLE IF EXISTS permissions CASCADE;
DROP TABLE IF EXISTS roles CASCADE;
DROP TABLE IF EXISTS user_auths CASCADE;
DROP TABLE IF EXISTS users CASCADE;