-- +goose Up
-- 插入系统默认数据

-- ========================================
-- 1. 插入默认系统租户
-- ========================================
INSERT INTO tenants (
    id,
    business_id,
    name,
    domain,
    display_name,
    description,
    tenant_type,
    industry,
    country,
    province,
    city,
    address,
    timezone,
    language,
    currency,
    company_size,
    employee_count,
    status,
    expires_at,
    parent_tenant_id,
    contact_email,
    contact_phone,
    settings,
    tags,
    custom_fields,
    created_at,
    updated_at,
    deleted_at,
    version
) VALUES (
    1000000000000000001,              -- 固定的雪花ID作为系统租户的技术ID
    'sys-admin-tenant-id',            -- 固定的业务ID
    '九翼跨境电商ERP系统',              -- 租户名称
    'admin.9wings.com',               -- 管理域名
    '九翼ERP系统管理租户',              -- 显示名称
    '九翼跨境电商ERP系统的系统管理租户，用于系统级管理和配置', -- 描述
    0,                                -- 租户类型：0=系统租户
    'Technology',                     -- 行业：技术
    'China',                          -- 国家：中国
    'Guangdong',                      -- 省份：广东
    'Shenzhen',                       -- 城市：深圳
    '深圳市南山区科技园',                -- 地址
    'Asia/Shanghai',                  -- 时区
    'zh-CN',                          -- 语言：简体中文
    'CNY',                            -- 货币：人民币
    'large',                          -- 公司规模：大型
    500,                              -- 员工数量
    1,                                -- 状态：激活
    NULL,                             -- 永不过期
    NULL,                             -- 无父租户
    '<EMAIL>',               -- 联系邮箱
    '+86-***********',                -- 联系电话
    '{"system_tenant": true, "max_users": 10000, "features": ["all"]}', -- 系统设置
    '["system", "admin", "erp"]',     -- 标签
    '{"system_info": {"version": "1.0.0", "build": "initial"}}', -- 自定义字段
    NOW(),                            -- 创建时间
    NOW(),                            -- 更新时间
    NULL,                             -- 未删除
    1                                 -- 版本号
) ON CONFLICT (business_id) DO NOTHING; -- 如果已存在则忽略

-- ========================================
-- 2. 插入系统管理员角色
-- ========================================
INSERT INTO roles (
    id,
    business_id,
    tenant_id,
    name,
    display_name,
    description,
    status,
    is_system,
    created_at,
    updated_at,
    deleted_at,
    version
) VALUES (
    1000000000000000001,              -- 固定的雪花ID
    'sys-admin-role-id',              -- 固定的业务ID
    'sys-admin-tenant-id',            -- 关联系统租户
    'system_admin',                   -- 角色名称
    '系统管理员',                      -- 显示名称
    '系统最高权限管理员，拥有所有系统功能的完全访问权限', -- 描述
    1,                                -- 激活状态
    true,                             -- 系统角色
    NOW(),                            -- 创建时间
    NOW(),                            -- 更新时间
    NULL,                             -- 未删除
    1                                 -- 版本号
) ON CONFLICT (business_id) DO NOTHING; -- 如果已存在则忽略

-- ========================================
-- 3. 插入默认系统管理员用户
-- ========================================
INSERT INTO users (
    id,
    business_id,
    username,
    email,
    phone,
    status,
    avatar,
    nickname,
    first_name,
    last_name,
    language,
    timezone,
    metadata,
    created_at,
    updated_at,
    deleted_at,
    version
) VALUES (
    1000000000000000002,              -- 固定的雪花ID作为系统管理员的技术ID
    'sys-admin-user-id',              -- 固定的业务ID
    'admin',                          -- 用户名
    '<EMAIL>',               -- 邮箱
    '***********',                    -- 手机号
    1,                                -- 激活状态
    '',                               -- 头像（空）
    '系统管理员',                      -- 昵称
    'System',                         -- 名
    'Admin',                          -- 姓
    'zh-CN',                          -- 语言
    'Asia/Shanghai',                  -- 时区
    '{"role": "system_admin", "permissions": ["all"]}', -- 元数据
    NOW(),                            -- 创建时间
    NOW(),                            -- 更新时间
    NULL,                             -- 未删除
    1                                 -- 版本号
) ON CONFLICT (business_id) DO NOTHING; -- 如果已存在则忽略

-- ========================================
-- 4. 插入用户认证信息（密码）
-- ========================================
INSERT INTO user_auths (
    id,
    business_id,
    user_business_id,
    identity_type,
    identifier,
    credential,
    status,
    created_at,
    updated_at,
    deleted_at,
    version
) VALUES (
    1000000000000000003,              -- 固定的雪花ID作为认证记录的技术ID
    'sys-admin-auth-id',              -- 固定的业务ID
    'sys-admin-user-id',              -- 关联系统管理员用户
    'password',                       -- 认证类型：密码
    '<EMAIL>',               -- 认证标识符：邮箱
    '$2a$10$Tre6Ox9USRveU.vekrRkYedkrPUYXDf8Bs.6ISVuLeFISJSesXmBq', -- 加密后的密码（Admin@2024!Strong#Password$）
    1,                                -- 激活状态
    NOW(),                            -- 创建时间
    NOW(),                            -- 更新时间
    NULL,                             -- 未删除
    1                                 -- 版本号
) ON CONFLICT (business_id) DO NOTHING; -- 如果已存在则忽略

-- 插入第二种认证方式（用户名）
INSERT INTO user_auths (
    id,
    business_id,
    user_business_id,
    identity_type,
    identifier,
    credential,
    status,
    created_at,
    updated_at,
    deleted_at,
    version
) VALUES (
    1000000000000000004,              -- 固定的雪花ID
    'sys-admin-auth-username-id',     -- 固定的业务ID
    'sys-admin-user-id',              -- 关联系统管理员用户
    'username',                       -- 认证类型：用户名
    'admin',                          -- 认证标识符：用户名
    '$2a$10$Tre6Ox9USRveU.vekrRkYedkrPUYXDf8Bs.6ISVuLeFISJSesXmBq', -- 相同的加密密码
    1,                                -- 激活状态
    NOW(),                            -- 创建时间
    NOW(),                            -- 更新时间
    NULL,                             -- 未删除
    1                                 -- 版本号
) ON CONFLICT (business_id) DO NOTHING; -- 如果已存在则忽略

-- ========================================
-- 5. 关联用户到系统租户并分配系统管理员角色
-- ========================================
INSERT INTO user_tenants (
    id,
    business_id,
    user_business_id,
    tenant_business_id,
    role_business_id,
    status,
    created_at,
    updated_at,
    deleted_at,
    version
) VALUES (
    1000000000000000005,              -- 固定的雪花ID
    'sys-admin-user-tenant-id',       -- 固定的业务ID
    'sys-admin-user-id',              -- 关联系统管理员用户
    'sys-admin-tenant-id',            -- 关联系统租户
    'sys-admin-role-id',              -- 关联系统管理员角色
    1,                                -- 激活状态
    NOW(),                            -- 创建时间
    NOW(),                            -- 更新时间
    NULL,                             -- 未删除
    1                                 -- 版本号
) ON CONFLICT (business_id) DO NOTHING; -- 如果已存在则忽略

-- 在user_roles表中建立用户与角色的直接关联
INSERT INTO user_roles (
    id,
    business_id,
    tenant_id,
    user_id,
    role_id,
    user_business_id,
    role_business_id,
    assigned_by,
    assigned_at,
    expires_at,
    is_active,
    created_at,
    updated_at,
    deleted_at,
    version
) VALUES (
    1000000000000000006,              -- 固定的雪花ID
    'sys-admin-user-role-id',         -- 固定的业务ID
    'sys-admin-tenant-id',            -- 租户ID
    1000000000000000002,              -- 用户技术ID
    1000000000000000001,              -- 角色技术ID
    'sys-admin-user-id',              -- 用户业务ID
    'sys-admin-role-id',              -- 角色业务ID
    'sys-admin-user-id',              -- 分配者（自己）
    NOW(),                            -- 分配时间
    NULL,                             -- 无过期时间
    true,                             -- 激活状态
    NOW(),                            -- 创建时间
    NOW(),                            -- 更新时间
    NULL,                             -- 未删除
    1                                 -- 版本号
) ON CONFLICT (business_id) DO NOTHING; -- 如果已存在则忽略

-- ========================================
-- 6. 为默认系统管理员用户授予Casbin权限
-- ========================================
-- 建立用户与角色的绑定关系 (g规则)

-- 插入角色继承规则 (g规则)
-- 格式: ptype, user_business_id, role_name, domain
-- 将系统管理员用户绑定到system_admin角色
INSERT INTO casbin_rules (ptype, v0, v1, v2) VALUES
    ('g', 'sys-admin-user-id', 'system_admin', 'sys-admin-tenant-id');

-- 为了演示和测试，也可以添加一些其他角色的绑定（如果需要的话）
-- 这里预留了一些示例绑定，实际使用时可以根据需要调整

-- 如果将来需要为其他用户分配角色，可以参考以下格式：
-- INSERT INTO casbin_rules (ptype, v0, v1, v2) VALUES
--     ('g', 'other-user-business-id', 'admin', 'tenant-business-id');

-- +goose Down
-- 按相反顺序删除数据

-- 删除系统管理员用户的角色绑定
DELETE FROM casbin_rules WHERE ptype = 'g' AND v0 = 'sys-admin-user-id' AND v1 = 'system_admin' AND v2 = 'sys-admin-tenant-id';

-- 删除用户角色关联
DELETE FROM user_roles WHERE business_id = 'sys-admin-user-role-id';

-- 删除用户租户关联
DELETE FROM user_tenants WHERE business_id = 'sys-admin-user-tenant-id';

-- 删除用户认证信息
DELETE FROM user_auths WHERE business_id IN ('sys-admin-auth-id', 'sys-admin-auth-username-id');

-- 删除默认系统管理员用户
DELETE FROM users WHERE business_id = 'sys-admin-user-id';

-- 删除系统管理员角色
DELETE FROM roles WHERE business_id = 'sys-admin-role-id';

-- 删除默认系统租户
DELETE FROM tenants WHERE business_id = 'sys-admin-tenant-id'; 