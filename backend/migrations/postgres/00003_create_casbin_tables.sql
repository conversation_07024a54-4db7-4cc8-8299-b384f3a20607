-- +goose Up
-- 创建Casbin权限系统相关表和初始化规则

-- ========================================
-- 1. 创建Casbin规则表
-- ========================================
CREATE TABLE IF NOT EXISTS casbin_rules (
    id SERIAL PRIMARY KEY,
    ptype VARCHAR(100),
    v0 VARCHAR(100),
    v1 VARCHAR(100),
    v2 VARCHAR(100),
    v3 VARCHAR(100),
    v4 VARCHAR(100),
    v5 VARCHAR(100)
);

-- 创建索引
CREATE INDEX IF NOT EXISTS idx_casbin_rules_ptype ON casbin_rules(ptype);
CREATE INDEX IF NOT EXISTS idx_casbin_rules_v0 ON casbin_rules(v0);
CREATE INDEX IF NOT EXISTS idx_casbin_rules_v1 ON casbin_rules(v1);

-- ========================================
-- 2. 初始化Casbin RBAC权限规则
-- ========================================
-- 这些规则定义了系统的基础权限模型

-- 插入系统管理员角色的权限策略规则 (p规则)
-- 格式: ptype, subject, domain, object, action
-- 系统管理员在系统租户下拥有所有资源的所有权限

-- 用户管理权限
INSERT INTO casbin_rules (ptype, v0, v1, v2, v3) VALUES
    ('p', 'system_admin', 'sys-admin-tenant-id', '/api/v1/users/*', '(GET)|(POST)|(PUT)|(DELETE)'),
    ('p', 'system_admin', 'sys-admin-tenant-id', '/api/v1/users', '(GET)|(POST)'),
    ('p', 'system_admin', 'sys-admin-tenant-id', '/api/v1/auth/*', '(GET)|(POST)|(PUT)|(DELETE)');

-- 租户管理权限
INSERT INTO casbin_rules (ptype, v0, v1, v2, v3) VALUES
    ('p', 'system_admin', 'sys-admin-tenant-id', '/api/v1/tenants/*', '(GET)|(POST)|(PUT)|(DELETE)'),
    ('p', 'system_admin', 'sys-admin-tenant-id', '/api/v1/tenants', '(GET)|(POST)');

-- 角色与权限管理权限
INSERT INTO casbin_rules (ptype, v0, v1, v2, v3) VALUES
    ('p', 'system_admin', 'sys-admin-tenant-id', '/api/v1/roles/*', '(GET)|(POST)|(PUT)|(DELETE)'),
    ('p', 'system_admin', 'sys-admin-tenant-id', '/api/v1/roles', '(GET)|(POST)'),
    ('p', 'system_admin', 'sys-admin-tenant-id', '/api/v1/permissions/*', '(GET)|(POST)|(PUT)|(DELETE)'),
    ('p', 'system_admin', 'sys-admin-tenant-id', '/api/v1/permissions', '(GET)|(POST)');

-- 系统配置管理权限
INSERT INTO casbin_rules (ptype, v0, v1, v2, v3) VALUES
    ('p', 'system_admin', 'sys-admin-tenant-id', '/api/v1/system/*', '(GET)|(POST)|(PUT)|(DELETE)'),
    ('p', 'system_admin', 'sys-admin-tenant-id', '/api/v1/config/*', '(GET)|(POST)|(PUT)|(DELETE)');

-- 业务模块权限（为将来的业务模块预留）
INSERT INTO casbin_rules (ptype, v0, v1, v2, v3) VALUES
    ('p', 'system_admin', 'sys-admin-tenant-id', '/api/v1/products/*', '(GET)|(POST)|(PUT)|(DELETE)'),
    ('p', 'system_admin', 'sys-admin-tenant-id', '/api/v1/orders/*', '(GET)|(POST)|(PUT)|(DELETE)'),
    ('p', 'system_admin', 'sys-admin-tenant-id', '/api/v1/inventory/*', '(GET)|(POST)|(PUT)|(DELETE)'),
    ('p', 'system_admin', 'sys-admin-tenant-id', '/api/v1/purchase/*', '(GET)|(POST)|(PUT)|(DELETE)'),
    ('p', 'system_admin', 'sys-admin-tenant-id', '/api/v1/finance/*', '(GET)|(POST)|(PUT)|(DELETE)');

-- 报表和分析权限
INSERT INTO casbin_rules (ptype, v0, v1, v2, v3) VALUES
    ('p', 'system_admin', 'sys-admin-tenant-id', '/api/v1/reports/*', '(GET)|(POST)|(PUT)|(DELETE)'),
    ('p', 'system_admin', 'sys-admin-tenant-id', '/api/v1/analytics/*', '(GET)|(POST)|(PUT)|(DELETE)');

-- 通用权限规则模板（其他角色可以参考）
-- 普通管理员角色权限（示例）
INSERT INTO casbin_rules (ptype, v0, v1, v2, v3) VALUES
    ('p', 'admin', '*', '/api/v1/users', '(GET)|(POST)'),
    ('p', 'admin', '*', '/api/v1/users/*', '(GET)|(PUT)'),
    ('p', 'admin', '*', '/api/v1/products/*', '(GET)|(POST)|(PUT)'),
    ('p', 'admin', '*', '/api/v1/orders/*', '(GET)|(POST)|(PUT)'),
    ('p', 'admin', '*', '/api/v1/reports/*', '(GET)'),
    -- 新增：权限管理路径权限
    ('p', 'admin', '*', '/api/v1/permissions', '(GET)|(POST)'),
    ('p', 'admin', '*', '/api/v1/permissions/*', '(GET)|(PUT)|(DELETE)'),
    ('p', 'admin', '*', '/api/v1/roles/*', '(GET)|(POST)|(PUT)'),
    -- 新增：财务管理路径权限
    ('p', 'admin', '*', '/api/v1/finance', '(GET)|(POST)'),
    ('p', 'admin', '*', '/api/v1/finance/*', '(GET)|(POST)|(PUT)|(DELETE)');

-- 普通用户角色权限（示例）
INSERT INTO casbin_rules (ptype, v0, v1, v2, v3) VALUES
    ('p', 'user', '*', '/api/v1/profile/*', '(GET)|(PUT)'),
    ('p', 'user', '*', '/api/v1/orders', '(GET)|(POST)'),
    ('p', 'user', '*', '/api/v1/orders/*', '(GET)|(PUT)'),
    ('p', 'user', '*', '/api/v1/products', '(GET)|(POST)'),
    ('p', 'user', '*', '/api/v1/products/*', '(GET)|(PUT)'),
    -- 新增：库存管理权限
    ('p', 'user', '*', '/api/v1/inventory', '(GET)|(POST)'),
    ('p', 'user', '*', '/api/v1/inventory/*', '(GET)|(POST)|(PUT)'),
    -- 新增：采购管理权限
    ('p', 'user', '*', '/api/v1/purchase', '(GET)|(POST)'),
    ('p', 'user', '*', '/api/v1/purchase/*', '(GET)|(POST)|(PUT)');

-- 只读用户角色权限（示例）
INSERT INTO casbin_rules (ptype, v0, v1, v2, v3) VALUES
    ('p', 'viewer', '*', '/api/v1/products', '(GET)'),
    ('p', 'viewer', '*', '/api/v1/orders', '(GET)'),
    ('p', 'viewer', '*', '/api/v1/reports/*', '(GET)');

-- +goose Down
-- 删除所有插入的Casbin规则和表
DELETE FROM casbin_rules WHERE ptype = 'p' AND v0 IN ('system_admin', 'admin', 'user', 'viewer');
DROP TABLE IF EXISTS casbin_rules CASCADE; 