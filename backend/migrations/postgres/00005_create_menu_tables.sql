-- +goose Up
-- 创建菜单相关表

-- ========================================
-- 1. 创建菜单表
-- ========================================
CREATE TABLE IF NOT EXISTS menus (
    -- 技术ID：雪花ID主键
    id                  BIGINT PRIMARY KEY,
    
    -- 业务ID：UUID唯一标识
    business_id         VARCHAR(36) NOT NULL UNIQUE,
    
    -- 租户ID：业务ID（TenantScopedEntity）
    tenant_id           VARCHAR(36) NOT NULL,
    
    -- 菜单基本信息
    name                VARCHAR(100) NOT NULL,
    title               VARCHAR(100) NOT NULL,
    icon                VARCHAR(100),
    path                VARCHAR(200) NOT NULL,
    component           VARCHAR(200),
    
    -- 层级关系
    parent_id           VARCHAR(36),
    level               INTEGER DEFAULT 1,
    sort                INTEGER DEFAULT 0,
    
    -- 权限控制
    permission          VARCHAR(200),
    resource            VARCHAR(100),
    action              VARCHAR(100),
    
    -- 菜单属性
    menu_type           INTEGER DEFAULT 1,
    status              INTEGER DEFAULT 1,
    is_visible          BOOLEAN DEFAULT TRUE,
    is_external         BOOLEAN DEFAULT FALSE,
    keep_alive          BOOLEAN DEFAULT FALSE,
    
    -- 元数据（JSONB格式）
    meta                JSONB DEFAULT '{}',
    
    -- 审计字段
    created_at          TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at          TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    deleted_at          TIMESTAMP WITH TIME ZONE,
    version             INTEGER DEFAULT 1
);

-- ========================================
-- 2. 创建菜单表索引
-- ========================================

-- 基础索引
CREATE INDEX IF NOT EXISTS idx_menus_business_id ON menus(business_id);
CREATE INDEX IF NOT EXISTS idx_menus_tenant_id ON menus(tenant_id);
CREATE INDEX IF NOT EXISTS idx_menus_parent_id ON menus(parent_id);
CREATE INDEX IF NOT EXISTS idx_menus_level ON menus(level);
CREATE INDEX IF NOT EXISTS idx_menus_sort ON menus(sort);

-- 查询优化索引
CREATE INDEX IF NOT EXISTS idx_menus_tenant_parent ON menus(tenant_id, parent_id) WHERE deleted_at IS NULL;
CREATE INDEX IF NOT EXISTS idx_menus_tenant_level ON menus(tenant_id, level) WHERE deleted_at IS NULL;
CREATE INDEX IF NOT EXISTS idx_menus_tenant_type ON menus(tenant_id, menu_type) WHERE deleted_at IS NULL;
CREATE INDEX IF NOT EXISTS idx_menus_tenant_status ON menus(tenant_id, status) WHERE deleted_at IS NULL;
CREATE INDEX IF NOT EXISTS idx_menus_tenant_visible ON menus(tenant_id, is_visible) WHERE deleted_at IS NULL;

-- 唯一性约束索引
CREATE UNIQUE INDEX IF NOT EXISTS idx_menus_tenant_name_unique ON menus(tenant_id, name) WHERE deleted_at IS NULL;
CREATE UNIQUE INDEX IF NOT EXISTS idx_menus_tenant_path_unique ON menus(tenant_id, path) WHERE deleted_at IS NULL;

-- 权限相关索引
CREATE INDEX IF NOT EXISTS idx_menus_permission ON menus(permission) WHERE permission IS NOT NULL AND deleted_at IS NULL;
CREATE INDEX IF NOT EXISTS idx_menus_resource_action ON menus(resource, action) WHERE resource IS NOT NULL AND action IS NOT NULL AND deleted_at IS NULL;

-- 排序相关索引
CREATE INDEX IF NOT EXISTS idx_menus_parent_sort ON menus(parent_id, sort) WHERE deleted_at IS NULL;
CREATE INDEX IF NOT EXISTS idx_menus_tenant_parent_sort ON menus(tenant_id, parent_id, sort) WHERE deleted_at IS NULL;

-- 路径相关索引
CREATE INDEX IF NOT EXISTS idx_menus_path ON menus(path) WHERE deleted_at IS NULL;
CREATE INDEX IF NOT EXISTS idx_menus_path_prefix ON menus(path varchar_pattern_ops) WHERE deleted_at IS NULL;

-- JSONB元数据索引（GIN索引用于JSONB查询优化）
CREATE INDEX IF NOT EXISTS idx_menus_meta_gin ON menus USING GIN (meta) WHERE deleted_at IS NULL;

-- 审计字段索引
CREATE INDEX IF NOT EXISTS idx_menus_created_at ON menus(created_at);
CREATE INDEX IF NOT EXISTS idx_menus_updated_at ON menus(updated_at);
CREATE INDEX IF NOT EXISTS idx_menus_deleted_at ON menus(deleted_at);

-- ========================================
-- 3. 添加表注释和字段注释
-- ========================================

COMMENT ON TABLE menus IS '菜单表 - TenantScopedEntity: 租户范围内的菜单管理';

-- 基础字段注释
COMMENT ON COLUMN menus.id IS '技术ID - 雪花算法生成的主键';
COMMENT ON COLUMN menus.business_id IS '业务ID - UUID格式的业务标识符';
COMMENT ON COLUMN menus.tenant_id IS '租户ID - 菜单所属租户的业务ID';

-- 菜单信息注释
COMMENT ON COLUMN menus.name IS '菜单名称 - 系统内部使用的菜单标识';
COMMENT ON COLUMN menus.title IS '菜单标题 - 用户界面显示的菜单名称';
COMMENT ON COLUMN menus.icon IS '菜单图标 - 前端显示的图标标识';
COMMENT ON COLUMN menus.path IS '菜单路径 - 前端路由路径';
COMMENT ON COLUMN menus.component IS '组件路径 - 前端组件文件路径';

-- 层级关系注释
COMMENT ON COLUMN menus.parent_id IS '父菜单ID - 父菜单的业务ID，空值表示根菜单';
COMMENT ON COLUMN menus.level IS '菜单层级 - 菜单在树形结构中的层级，根菜单为1';
COMMENT ON COLUMN menus.sort IS '排序号 - 同级菜单的排序顺序';

-- 权限控制注释
COMMENT ON COLUMN menus.permission IS '权限标识 - 访问此菜单需要的权限码';
COMMENT ON COLUMN menus.resource IS '资源标识 - Casbin权限模型中的资源';
COMMENT ON COLUMN menus.action IS '操作标识 - Casbin权限模型中的操作';

-- 菜单属性注释
COMMENT ON COLUMN menus.menu_type IS '菜单类型 - 1:目录 2:菜单 3:按钮';
COMMENT ON COLUMN menus.status IS '菜单状态 - 0:未激活 1:激活 2:隐藏 3:禁用';
COMMENT ON COLUMN menus.is_visible IS '是否可见 - 控制菜单在界面中的显示';
COMMENT ON COLUMN menus.is_external IS '是否外部链接 - 标识菜单是否为外部链接';
COMMENT ON COLUMN menus.keep_alive IS '是否缓存 - 控制前端页面是否保持活跃状态';

-- 元数据注释
COMMENT ON COLUMN menus.meta IS '菜单元数据 - JSONB格式存储的扩展信息';

-- 审计字段注释
COMMENT ON COLUMN menus.created_at IS '创建时间 - 记录创建的时间戳';
COMMENT ON COLUMN menus.updated_at IS '更新时间 - 记录最后更新的时间戳';
COMMENT ON COLUMN menus.deleted_at IS '删除时间 - 软删除标记时间戳';
COMMENT ON COLUMN menus.version IS '版本号 - 乐观锁版本控制';

-- ========================================
-- 4. 插入默认菜单数据
-- ========================================

-- 插入系统默认菜单（仅在系统租户中）
INSERT INTO menus (
    id, business_id, tenant_id, name, title, icon, path, component,
    parent_id, level, sort, menu_type, status, is_visible, meta, created_at, updated_at
) VALUES 
-- 仪表板
(1, 'menu-dashboard', 'system-tenant', 'Dashboard', '仪表板', 'dashboard', '/dashboard', 'Dashboard',
 NULL, 1, 1, 2, 1, TRUE, '{"title": "仪表板", "breadcrumb": true, "keepAlive": true}', NOW(), NOW()),

-- 用户管理目录
(2, 'menu-user-management', 'system-tenant', 'UserManagement', '用户管理', 'user', '/users', NULL,
 NULL, 1, 2, 1, 1, TRUE, '{"title": "用户管理", "breadcrumb": true}', NOW(), NOW()),

-- 用户列表
(3, 'menu-user-list', 'system-tenant', 'UserList', '用户列表', NULL, '/users/list', 'UserList',
 'menu-user-management', 2, 1, 2, 1, TRUE, '{"title": "用户列表", "breadcrumb": true}', NOW(), NOW()),

-- 角色管理
(4, 'menu-role-management', 'system-tenant', 'RoleManagement', '角色管理', NULL, '/users/roles', 'RoleManagement',
 'menu-user-management', 2, 2, 2, 1, TRUE, '{"title": "角色管理", "breadcrumb": true}', NOW(), NOW()),

-- 租户管理目录
(5, 'menu-tenant-management', 'system-tenant', 'TenantManagement', '租户管理', 'apartment', '/tenants', NULL,
 NULL, 1, 3, 1, 1, TRUE, '{"title": "租户管理", "breadcrumb": true}', NOW(), NOW()),

-- 租户列表
(6, 'menu-tenant-list', 'system-tenant', 'TenantList', '租户列表', NULL, '/tenants/list', 'TenantList',
 'menu-tenant-management', 2, 1, 2, 1, TRUE, '{"title": "租户列表", "breadcrumb": true}', NOW(), NOW()),

-- 订阅管理
(7, 'menu-subscription-management', 'system-tenant', 'SubscriptionManagement', '订阅管理', NULL, '/tenants/subscriptions', 'SubscriptionManagement',
 'menu-tenant-management', 2, 2, 2, 1, TRUE, '{"title": "订阅管理", "breadcrumb": true}', NOW(), NOW()),

-- 系统设置目录
(8, 'menu-system-settings', 'system-tenant', 'SystemSettings', '系统设置', 'setting', '/settings', NULL,
 NULL, 1, 10, 1, 1, TRUE, '{"title": "系统设置", "breadcrumb": true}', NOW(), NOW()),

-- 菜单管理
(9, 'menu-menu-management', 'system-tenant', 'MenuManagement', '菜单管理', NULL, '/settings/menus', 'MenuManagement',
 'menu-system-settings', 2, 1, 2, 1, TRUE, '{"title": "菜单管理", "breadcrumb": true}', NOW(), NOW()),

-- 权限管理
(10, 'menu-permission-management', 'system-tenant', 'PermissionManagement', '权限管理', NULL, '/settings/permissions', 'PermissionManagement',
 'menu-system-settings', 2, 2, 2, 1, TRUE, '{"title": "权限管理", "breadcrumb": true}', NOW(), NOW())

ON CONFLICT (business_id) DO NOTHING;

-- ========================================
-- 5. 设置菜单权限
-- ========================================

-- 更新菜单权限设置
UPDATE menus SET resource = 'dashboard', action = 'read' WHERE business_id = 'menu-dashboard';
UPDATE menus SET resource = 'user', action = 'read' WHERE business_id = 'menu-user-list';
UPDATE menus SET resource = 'user', action = 'manage' WHERE business_id = 'menu-role-management';
UPDATE menus SET resource = 'tenant', action = 'read' WHERE business_id = 'menu-tenant-list';
UPDATE menus SET resource = 'tenant', action = 'manage' WHERE business_id = 'menu-subscription-management';
UPDATE menus SET resource = 'system', action = 'manage' WHERE business_id = 'menu-menu-management';
UPDATE menus SET resource = 'system', action = 'manage' WHERE business_id = 'menu-permission-management';

-- +goose Down
-- 回滚更改

-- 删除菜单表
DROP TABLE IF EXISTS menus CASCADE;
