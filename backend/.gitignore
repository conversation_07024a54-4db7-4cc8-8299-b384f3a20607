# Go编译产物
*.exe
*.exe~
*.dll
*.so
*.dylib
*.test
*.out
main

# 二进制文件
bin/
/bin

# 依赖目录
vendor/

# 测试缓存
/testdata

# IDE相关文件
.idea/
.vscode/
*.swp
*.swo
.DS_Store

# 环境变量和本地配置
.env
.env.local
.env.backup
*.local.yaml
*.local.yml

# 环境特定配置文件（包含敏感信息）
# 提交模板文件，但不提交实际使用的环境配置
!.env.development
.env.production  
.env.testing
!.env.example

# 临时配置文件
config-*.yaml

# 上传文件目录
uploads/
test/uploads/

# 日志文件
*.log
logs/

# 临时文件
tmp/
temp/

# 构建输出目录
/dist/
/build/

# 数据库备份
*.dump
# *.sql
*.db

# 证书和密钥
*.pem
*.key
*.crt

# 配置文件（可选，部分项目可能需要提交配置文件）
# config/config.yaml

# Go工作区文件
go.work

# Air热重载配置
.air.toml

# Docker卷数据
data/
postgres_data/

# 备份文件
*.backup
*.bak

# 其他
__debug_bin*