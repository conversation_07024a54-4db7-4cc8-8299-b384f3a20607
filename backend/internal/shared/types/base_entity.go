package types

import (
	"time"

	"gorm.io/gorm"
)

// CoreEntity 包含所有领域实体的核心字段
// 这是所有实体的基础，包含ID、审计和版本控制字段
type CoreEntity struct {
	// ID 是数据库的技术主键，使用雪花算法生成，优化数据库性能
	ID int64 `gorm:"primaryKey;autoIncrement:false" json:"-"`

	// BusinessID 是对外暴露的业务ID，使用语义化UUID，对外部系统和API友好
	BusinessID string `gorm:"uniqueIndex;size:36" json:"id"`

	// CreatedAt 记录了实体的创建时间
	CreatedAt time.Time `gorm:"index" json:"created_at"`

	// UpdatedAt 记录了实体的最后更新时间
	UpdatedAt time.Time `json:"updated_at"`

	// DeletedAt 用于GORM的软删除功能，保护数据不被物理删除
	DeletedAt gorm.DeletedAt `gorm:"index" json:"-"`

	// Version 是乐观锁版本号，用于处理并发更新冲突
	Version int `gorm:"default:1" json:"version"`
}

// TenantScopedEntity 租户范围实体
// 适用于属于特定租户的业务实体，如商品、订单、库存等
type TenantScopedEntity struct {
	CoreEntity

	// TenantID 租户ID，用于多租户数据隔离（必填字段）
	TenantID string `gorm:"index;size:36;not null" json:"tenant_id"`
}

// MultiTenantEntity 多租户关联实体
// 适用于可以跨多个租户的实体，如用户、用户-租户关联等
type MultiTenantEntity struct {
	CoreEntity
}

// GlobalEntity 全局实体
// 适用于系统级实体，如全局配置、权限模板等
type GlobalEntity struct {
	CoreEntity
}

// ==================== CoreEntity 方法 ====================

// NewCoreEntity 使用统一ID生成器创建CoreEntity实例
func NewCoreEntity(techID int64, businessID string) CoreEntity {
	return CoreEntity{
		ID:         techID,
		BusinessID: businessID,
		CreatedAt:  time.Now(),
		UpdatedAt:  time.Now(),
		Version:    1,
	}
}

// NewEmptyCoreEntity 创建空的CoreEntity，需要后续设置ID
// DEPRECATED: 请使用统一ID生成器代替此方法
func NewEmptyCoreEntity() CoreEntity {
	return CoreEntity{
		CreatedAt: time.Now(),
		UpdatedAt: time.Now(),
		Version:   1,
	}
}

// ==================== 实体状态检查方法 ====================

// IsPersisted 检查实体是否已经被持久化（即，是否已分配技术ID）
func (c *CoreEntity) IsPersisted() bool {
	return c.ID != 0
}

// HasCompleteIDs 检查实体是否具有完整的ID（技术ID和业务ID）
func (c *CoreEntity) HasCompleteIDs() bool {
	return c.ID != 0 && c.BusinessID != ""
}

// GetBusinessID 获取业务ID
func (c *CoreEntity) GetBusinessID() string {
	return c.BusinessID
}

// SetBusinessID 设置业务ID
func (c *CoreEntity) SetBusinessID(businessID string) {
	c.BusinessID = businessID
	c.UpdatedAt = time.Now()
}

// GetTechID 获取技术ID
func (c *CoreEntity) GetTechID() int64 {
	return c.ID
}

// SetTechID 设置技术ID
func (c *CoreEntity) SetTechID(techID int64) {
	c.ID = techID
	c.UpdatedAt = time.Now()
}

// GetEntityKey 获取实体关键标识（默认返回BusinessID）
func (c *CoreEntity) GetEntityKey() string {
	return c.BusinessID
}

// GetIDMetadata 获取ID生成元数据
func (c *CoreEntity) GetIDMetadata() map[string]interface{} {
	return map[string]interface{}{
		"entity_type": "core_entity",
		"created_at":  c.CreatedAt,
		"version":     c.Version,
	}
}

// GetDomain 获取实体所属领域（默认为generic）
func (c *CoreEntity) GetDomain() IDDomain {
	return IDDomainGeneric
}

// IncrementVersion 增加版本号
func (c *CoreEntity) IncrementVersion() {
	c.Version++
	c.UpdatedAt = time.Now()
}

// UpdateTimestamp 更新时间戳
func (c *CoreEntity) UpdateTimestamp() {
	c.UpdatedAt = time.Now()
}

// GetAuditInfo 获取审计信息
func (c *CoreEntity) GetAuditInfo() map[string]interface{} {
	return map[string]interface{}{
		"id":          c.ID,
		"business_id": c.BusinessID,
		"created_at":  c.CreatedAt,
		"updated_at":  c.UpdatedAt,
		"version":     c.Version,
		"is_deleted":  c.DeletedAt.Valid,
	}
}

// ==================== TenantScopedEntity 方法 ====================

// NewTenantScopedEntity 创建租户范围实体
func NewTenantScopedEntity(techID int64, businessID, tenantID string) TenantScopedEntity {
	return TenantScopedEntity{
		CoreEntity: NewCoreEntity(techID, businessID),
		TenantID:   tenantID,
	}
}

// NewEmptyTenantScopedEntity 创建空的租户范围实体
// DEPRECATED: 请使用统一ID生成器代替此方法
func NewEmptyTenantScopedEntity() TenantScopedEntity {
	return TenantScopedEntity{
		CoreEntity: NewEmptyCoreEntity(),
	}
}

// IsValid 检查租户范围实体的有效性
func (t *TenantScopedEntity) IsValid() bool {
	return t.HasCompleteIDs() && t.TenantID != ""
}

// GetTenantID 获取租户ID
func (t *TenantScopedEntity) GetTenantID() string {
	return t.TenantID
}

// SetTenantID 设置租户ID
func (t *TenantScopedEntity) SetTenantID(tenantID string) {
	t.TenantID = tenantID
	t.UpdatedAt = time.Now()
}

// GetIDMetadata 获取ID生成元数据（覆盖CoreEntity方法）
func (t *TenantScopedEntity) GetIDMetadata() map[string]interface{} {
	metadata := t.CoreEntity.GetIDMetadata()
	metadata["entity_type"] = "tenant_scoped_entity"
	metadata["tenant_id"] = t.TenantID
	return metadata
}

// GetAuditInfo 获取审计信息（覆盖CoreEntity方法）
func (t *TenantScopedEntity) GetAuditInfo() map[string]interface{} {
	auditInfo := t.CoreEntity.GetAuditInfo()
	auditInfo["tenant_id"] = t.TenantID
	return auditInfo
}

// ==================== MultiTenantEntity 方法 ====================

// NewMultiTenantEntity 创建多租户关联实体
func NewMultiTenantEntity(techID int64, businessID string) MultiTenantEntity {
	return MultiTenantEntity{
		CoreEntity: NewCoreEntity(techID, businessID),
	}
}

// NewEmptyMultiTenantEntity 创建空的多租户关联实体
// DEPRECATED: 请使用统一ID生成器代替此方法
func NewEmptyMultiTenantEntity() MultiTenantEntity {
	return MultiTenantEntity{
		CoreEntity: NewEmptyCoreEntity(),
	}
}

// IsValid 检查多租户实体的有效性
func (m *MultiTenantEntity) IsValid() bool {
	return m.HasCompleteIDs()
}

// GetIDMetadata 获取ID生成元数据（覆盖CoreEntity方法）
func (m *MultiTenantEntity) GetIDMetadata() map[string]interface{} {
	metadata := m.CoreEntity.GetIDMetadata()
	metadata["entity_type"] = "multi_tenant_entity"
	return metadata
}

// ==================== GlobalEntity 方法 ====================

// NewGlobalEntity 创建全局实体
func NewGlobalEntity(techID int64, businessID string) GlobalEntity {
	return GlobalEntity{
		CoreEntity: NewCoreEntity(techID, businessID),
	}
}

// NewEmptyGlobalEntity 创建空的全局实体
// DEPRECATED: 请使用统一ID生成器代替此方法
func NewEmptyGlobalEntity() GlobalEntity {
	return GlobalEntity{
		CoreEntity: NewEmptyCoreEntity(),
	}
}

// IsValid 检查全局实体的有效性
func (g *GlobalEntity) IsValid() bool {
	return g.HasCompleteIDs()
}

// GetIDMetadata 获取ID生成元数据（覆盖CoreEntity方法）
func (g *GlobalEntity) GetIDMetadata() map[string]interface{} {
	metadata := g.CoreEntity.GetIDMetadata()
	metadata["entity_type"] = "global_entity"
	return metadata
}
