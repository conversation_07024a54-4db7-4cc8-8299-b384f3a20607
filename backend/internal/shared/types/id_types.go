package types

// IDType ID类型枚举
type IDType string

const (
	// IDTypeSnowflake 雪花ID类型 - 用于技术主键
	IDTypeSnowflake IDType = "snowflake"

	// IDTypeSemanticUUID 语义化UUID类型 - 用于业务标识
	IDTypeSemanticUUID IDType = "semantic_uuid"

	// IDTypeRandomUUID 随机UUID类型 - 用于通用唯一标识
	IDTypeRandomUUID IDType = "random_uuid"

	// IDTypeSequence 序列号类型 - 用于业务单号
	IDTypeSequence IDType = "sequence"

	// IDTypeCustom 自定义类型 - 用于特殊需求
	IDTypeCustom IDType = "custom"
)

// IDPurpose ID用途枚举
type IDPurpose string

const (
	// IDPurposeTechnical 技术用途 - 数据库主键、内部关联
	IDPurposeTechnical IDPurpose = "technical"

	// IDPurposeBusiness 业务用途 - 对外暴露、API交互
	IDPurposeBusiness IDPurpose = "business"

	// IDPurposeDisplay 展示用途 - 用户可见、友好展示
	IDPurposeDisplay IDPurpose = "display"

	// IDPurposeSession 会话用途 - 临时标识、会话管理
	IDPurposeSession IDPurpose = "session"

	// IDPurposeTrace 追踪用途 - 链路追踪、日志关联
	IDPurposeTrace IDPurpose = "trace"
)

// IDDomain ID领域枚举
type IDDomain string

const (
	// IDDomainUser 用户领域
	IDDomainUser IDDomain = "user"

	// IDDomainProduct 商品领域
	IDDomainProduct IDDomain = "product"

	// IDDomainOrder 订单领域
	IDDomainOrder IDDomain = "order"

	// IDDomainInventory 库存领域
	IDDomainInventory IDDomain = "inventory"

	// IDDomainFinance 财务领域
	IDDomainFinance IDDomain = "finance"

	// IDDomainTenant 租户领域
	IDDomainTenant IDDomain = "tenant"

	// IDDomainSecurity 安全领域
	IDDomainSecurity IDDomain = "security"

	// IDDomainGeneric 通用领域
	IDDomainGeneric IDDomain = "generic"
)

// IDGenerationRequest ID生成请求
type IDGenerationRequest struct {
	// Type ID类型
	Type IDType `json:"type" validate:"required"`

	// Purpose ID用途
	Purpose IDPurpose `json:"purpose" validate:"required"`

	// Domain ID领域
	Domain IDDomain `json:"domain" validate:"required"`

	// TenantID 租户ID（可选，用于多租户隔离）
	TenantID string `json:"tenant_id,omitempty"`

	// EntityKey 实体关键标识（用于语义化UUID）
	EntityKey string `json:"entity_key,omitempty"`

	// Metadata 元数据（用于语义化生成）
	Metadata map[string]interface{} `json:"metadata,omitempty"`

	// Prefix 前缀（用于序列号和自定义类型）
	Prefix string `json:"prefix,omitempty"`

	// Suffix 后缀（用于序列号和自定义类型）
	Suffix string `json:"suffix,omitempty"`

	// Length 长度（用于序列号类型）
	Length int `json:"length,omitempty"`
}

// IDGenerationResult ID生成结果
type IDGenerationResult struct {
	// SnowflakeID 雪花ID（当Type为IDTypeSnowflake时）
	SnowflakeID int64 `json:"snowflake_id,omitempty"`

	// UUID UUID字符串（当Type为UUID类型时）
	UUID string `json:"uuid,omitempty"`

	// SequenceID 序列号（当Type为IDTypeSequence时）
	SequenceID string `json:"sequence_id,omitempty"`

	// Type 生成的ID类型
	Type IDType `json:"type"`

	// Domain 生成的ID领域
	Domain IDDomain `json:"domain"`

	// GeneratedAt 生成时间戳
	GeneratedAt int64 `json:"generated_at"`

	// SemanticName 语义化名称（仅用于语义化UUID）
	SemanticName string `json:"semantic_name,omitempty"`
}

// GetStringValue 获取ID的字符串表示
func (r *IDGenerationResult) GetStringValue() string {
	switch r.Type {
	case IDTypeSnowflake:
		return string(rune(r.SnowflakeID))
	case IDTypeSemanticUUID, IDTypeRandomUUID:
		return r.UUID
	case IDTypeSequence, IDTypeCustom:
		return r.SequenceID
	default:
		return ""
	}
}

// GetInt64Value 获取ID的int64表示（仅适用于雪花ID）
func (r *IDGenerationResult) GetInt64Value() int64 {
	if r.Type == IDTypeSnowflake {
		return r.SnowflakeID
	}
	return 0
}

// BatchIDGenerationRequest 批量ID生成请求
type BatchIDGenerationRequest struct {
	// BaseRequest 基础请求模板
	BaseRequest IDGenerationRequest `json:"base_request"`

	// Items 批量生成项目
	Items []BatchIDItem `json:"items" validate:"required,min=1,max=1000"`
}

// BatchIDItem 批量ID生成项目
type BatchIDItem struct {
	// EntityKey 实体关键标识
	EntityKey string `json:"entity_key,omitempty"`

	// Metadata 元数据
	Metadata map[string]interface{} `json:"metadata,omitempty"`

	// CustomSuffix 自定义后缀
	CustomSuffix string `json:"custom_suffix,omitempty"`
}

// BatchIDGenerationResult 批量ID生成结果
type BatchIDGenerationResult struct {
	// Results 生成结果列表
	Results []IDGenerationResult `json:"results"`

	// SuccessCount 成功生成数量
	SuccessCount int `json:"success_count"`

	// FailureCount 失败生成数量
	FailureCount int `json:"failure_count"`

	// Errors 错误信息（按索引对应）
	Errors map[int]error `json:"errors,omitempty"`
}
