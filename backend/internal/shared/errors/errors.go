package errors

import (
	"time"

	commonErrors "backend/pkg/common/errors"
	"backend/pkg/common/errors/codes"

	"github.com/gin-gonic/gin"
)

// 为了向后兼容，重新导出常用的错误创建函数

// TenantNotFound 租户不存在错误
func TenantNotFound(tenantID string) error {
	return commonErrors.NewNotFound(codes.TenantNotFound, "租户不存在").
		WithDetail("tenantID", tenantID).Build()
}

// NotFound 资源不存在错误
func NotFound(resource, id string) error {
	return commonErrors.NewNotFound(codes.ResourceNotFound, "资源不存在").
		WithDetail("resource", resource).
		WithDetail("id", id).Build()
}

// Exists 资源已存在错误
func Exists(resource, field, value string) error {
	return commonErrors.NewConflict(codes.ResourceAlreadyExists, "资源已存在").
		WithDetail("resource", resource).
		WithDetail("field", field).
		WithDetail("value", value).Build()
}

// Business 业务逻辑错误
func Business(message string) error {
	return commonErrors.NewValidation("business.logic.error", message).Build()
}

// Internal 内部错误
func Internal(message string) error {
	return commonErrors.NewInternal("internal.error", message).Build()
}

// Validation 验证错误
func Validation(message string) error {
	return commonErrors.NewValidation("validation.error", message).Build()
}

// Unauthorized 未授权错误
func Unauthorized(message string) error {
	return commonErrors.NewUnauthorized("auth.unauthorized", message).Build()
}

// Permission 权限不足错误
func Permission(message string) error {
	return commonErrors.NewPermission("auth.permission", message).Build()
}

// Database 数据库错误
func Database(err error) error {
	return commonErrors.NewInternal("database.error", "数据库操作失败").
		Wrap(err).Build()
}

// Conflict 资源冲突错误
func Conflict(resource, field, value string) error {
	return commonErrors.NewConflict(codes.ResourceConflict, "资源冲突").
		WithDetail("resource", resource).
		WithDetail("field", field).
		WithDetail("value", value).Build()
}

// UserNotFound 用户不存在错误
func UserNotFound(userID string) error {
	return commonErrors.NewNotFound(codes.ResourceNotFound, "用户不存在").
		WithDetail("userID", userID).Build()
}

// UserExists 用户已存在错误
func UserExists(value string) error {
	return commonErrors.NewConflict(codes.ResourceAlreadyExists, "用户已存在").
		WithDetail("value", value).Build()
}

// UserListFailed 用户列表查询失败错误
func UserListFailed(err error) error {
	return commonErrors.NewInternal("user.list.failed", "用户列表查询失败").
		Wrap(err).Build()
}

// ValidationFailed 验证失败错误
func ValidationFailed(message string) error {
	return commonErrors.NewValidation("validation.failed", message).Build()
}

// Forbidden 禁止访问错误
func Forbidden(message string) error {
	return commonErrors.NewPermission("auth.forbidden", message).Build()
}

// TokenExpired Token过期错误
func TokenExpired(message ...string) error {
	msg := "Token已过期"
	if len(message) > 0 {
		msg = message[0]
	}
	return commonErrors.NewUnauthorized("auth.token.expired", msg).Build()
}

// TokenBlacklisted Token被拉黑错误
func TokenBlacklisted(message string) error {
	return commonErrors.NewUnauthorized("auth.token.blacklisted", message).Build()
}

// PreAuthExpired 预认证过期错误
func PreAuthExpired(message ...string) error {
	msg := "预认证已过期"
	if len(message) > 0 {
		msg = message[0]
	}
	return commonErrors.NewUnauthorized("auth.preauth.expired", msg).Build()
}

// TooManyAttempts 尝试次数过多错误
func TooManyAttempts(attempts interface{}) error {
	return commonErrors.NewTooManyRequests("auth.attempts.exceeded", "尝试次数过多").
		WithDetail("attempts", attempts).Build()
}

// PreAuthInvalid 预认证无效错误
func PreAuthInvalid(message string) error {
	return commonErrors.NewUnauthorized("auth.preauth.invalid", message).Build()
}

// ConcurrentLoginLimit 并发登录限制错误
func ConcurrentLoginLimit(limit interface{}) error {
	return commonErrors.NewConflict("auth.concurrent.limit", "并发登录数量超限").
		WithDetail("limit", limit).Build()
}

// HandleError 处理HTTP错误响应
// 这是一个向后兼容的函数，推荐使用 c.Error(err) 让错误中间件统一处理
func HandleError(c *gin.Context, err error) {
	// 调用gin的Error方法来让中间件处理
	c.Error(err)
}

// HandleErrorWithAbort 处理错误并中止请求
func HandleErrorWithAbort(c *gin.Context, err error) {
	c.Error(err)
	c.Abort()
}

// HandleErrorDirect 直接处理错误响应（不推荐，仅用于特殊场景）
// 推荐使用 HandleError 让中间件统一处理
func HandleErrorDirect(c *gin.Context, err error) {
	if c.Writer.Written() {
		return
	}

	appErr := commonErrors.As(err)
	if appErr == nil {
		// 如果不是AppError，包装为内部错误
		wrappedErr := commonErrors.NewInternal("internal.error", "内部服务器错误").
			Wrap(err).Build()
		appErr = commonErrors.As(wrappedErr)
	}

	// 直接响应错误
	statusCode := getHTTPStatusFromErrorType(appErr.Type)
	c.JSON(statusCode, gin.H{
		"code":      appErr.Code,
		"message":   appErr.Message,
		"details":   appErr.Details,
		"timestamp": time.Now().Unix(),
	})
}

// getHTTPStatusFromErrorType 将错误类型转换为HTTP状态码
func getHTTPStatusFromErrorType(errorType commonErrors.ErrorType) int {
	switch errorType {
	case commonErrors.TypeInternal:
		return 500
	case commonErrors.TypeValidation:
		return 400
	case commonErrors.TypeNotFound:
		return 404
	case commonErrors.TypeConflict:
		return 409
	case commonErrors.TypePermission:
		return 403
	case commonErrors.TypeUnauthorized:
		return 401
	case commonErrors.TypeExternal:
		return 503
	case commonErrors.TypeTooManyRequests:
		return 429
	default:
		return 500
	}
}

// TenantCreateFailed 租户创建失败错误
func TenantCreateFailed(reason string) error {
	return commonErrors.NewInternal("tenant.create.failed", "创建租户失败").
		WithDetail("reason", reason).Build()
}

// TenantUpdateFailed 租户更新失败错误
func TenantUpdateFailed(operation string, err error) error {
	return commonErrors.NewInternal("tenant.update.failed", "更新租户失败").
		WithDetail("operation", operation).
		Wrap(err).Build()
}

// TenantListFailed 获取租户列表失败错误
func TenantListFailed(err error) error {
	return commonErrors.NewInternal("tenant.list.failed", "获取租户列表失败").
		Wrap(err).Build()
}

// TenantSearchFailed 搜索租户失败错误
func TenantSearchFailed(err error) error {
	return commonErrors.NewInternal("tenant.search.failed", "搜索租户失败").
		Wrap(err).Build()
}

// TenantQuotaUpdateFailed 租户配额更新失败错误
func TenantQuotaUpdateFailed(err error) error {
	return commonErrors.NewInternal("tenant.quota.update.failed", "更新租户配额失败").
		Wrap(err).Build()
}

// TenantHealthCheckFailed 租户健康检查失败错误
func TenantHealthCheckFailed(err error) error {
	return commonErrors.NewInternal("tenant.health.check.failed", "检查租户健康状态失败").
		Wrap(err).Build()
}

// TenantDeleteFailed 租户删除失败错误
func TenantDeleteFailed(err error) error {
	return commonErrors.NewInternal("tenant.delete.failed", "删除租户失败").
		Wrap(err).Build()
}

// TenantDomainValidationFailed 租户域名验证失败错误
func TenantDomainValidationFailed(err error) error {
	return commonErrors.NewInternal("tenant.domain.validation.failed", "验证域名失败").
		Wrap(err).Build()
}

// TenantBatchOperationFailed 租户批量操作失败错误
func TenantBatchOperationFailed(operation, tenantID string, err error) error {
	return commonErrors.NewInternal("tenant.batch.operation.failed", "批量操作失败").
		WithDetail("operation", operation).
		WithDetail("tenantID", tenantID).
		Wrap(err).Build()
}

// TenantDomainCheckFailed 检查域名唯一性失败错误
func TenantDomainCheckFailed() error {
	return commonErrors.NewInternal("tenant.domain.check.failed", "检查域名唯一性失败").Build()
}

// ==================== 商品管理错误 ====================

// ProductNotFound 商品不存在错误
func ProductNotFound(productID string) error {
	return commonErrors.NewNotFound(codes.ProductNotFound, "商品不存在").
		WithDetail("productID", productID).Build()
}

// ProductCreateFailed 商品创建失败错误
func ProductCreateFailed(reason string, err error) error {
	return commonErrors.NewInternal("product.create.failed", "创建商品失败").
		WithDetail("reason", reason).
		Wrap(err).Build()
}

// ProductUpdateFailed 商品更新失败错误
func ProductUpdateFailed(productID, operation string, err error) error {
	return commonErrors.NewInternal("product.update.failed", "更新商品失败").
		WithDetail("productID", productID).
		WithDetail("operation", operation).
		Wrap(err).Build()
}

// ProductDeleteFailed 商品删除失败错误
func ProductDeleteFailed(productID string, err error) error {
	return commonErrors.NewInternal("product.delete.failed", "删除商品失败").
		WithDetail("productID", productID).
		Wrap(err).Build()
}

// SKUAlreadyExists SKU已存在错误
func SKUAlreadyExists(sku string) error {
	return commonErrors.NewConflict(codes.SKUAlreadyExists, "SKU已存在").
		WithDetail("sku", sku).Build()
}

// CategoryNotFound 分类不存在错误
func CategoryNotFound(categoryID string) error {
	return commonErrors.NewNotFound(codes.CategoryNotFound, "商品分类不存在").
		WithDetail("categoryID", categoryID).Build()
}

// ProductOutOfStock 商品库存不足错误
func ProductOutOfStock(productID string, requested, available int) error {
	return commonErrors.NewConflict(codes.ProductOutOfStock, "商品库存不足").
		WithDetail("productID", productID).
		WithDetail("requested", requested).
		WithDetail("available", available).Build()
}

// ==================== 订单管理错误 ====================

// OrderNotFound 订单不存在错误
func OrderNotFound(orderID string) error {
	return commonErrors.NewNotFound(codes.OrderNotFound, "订单不存在").
		WithDetail("orderID", orderID).Build()
}

// OrderCreateFailed 订单创建失败错误
func OrderCreateFailed(reason string, err error) error {
	return commonErrors.NewInternal("order.create.failed", "创建订单失败").
		WithDetail("reason", reason).
		Wrap(err).Build()
}

// OrderUpdateFailed 订单更新失败错误
func OrderUpdateFailed(orderID, operation string, err error) error {
	return commonErrors.NewInternal("order.update.failed", "更新订单失败").
		WithDetail("orderID", orderID).
		WithDetail("operation", operation).
		Wrap(err).Build()
}

// OrderCancelled 订单已取消错误
func OrderCancelled(orderID string) error {
	return commonErrors.NewConflict(codes.OrderCancelled, "订单已取消").
		WithDetail("orderID", orderID).Build()
}

// OrderStatusInvalid 订单状态无效错误
func OrderStatusInvalid(orderID, currentStatus, targetStatus string) error {
	return commonErrors.NewValidation("order.status.invalid", "订单状态转换无效").
		WithDetail("orderID", orderID).
		WithDetail("currentStatus", currentStatus).
		WithDetail("targetStatus", targetStatus).Build()
}

// CustomerNotFound 客户不存在错误
func CustomerNotFound(customerID string) error {
	return commonErrors.NewNotFound(codes.CustomerNotFound, "客户不存在").
		WithDetail("customerID", customerID).Build()
}

// ==================== 库存管理错误 ====================

// InventoryInsufficient 库存不足错误
func InventoryInsufficient(productID string, warehouseID string, requested, available int) error {
	return commonErrors.NewConflict(codes.InventoryInsufficient, "库存不足").
		WithDetail("productID", productID).
		WithDetail("warehouseID", warehouseID).
		WithDetail("requested", requested).
		WithDetail("available", available).Build()
}

// WarehouseNotFound 仓库不存在错误
func WarehouseNotFound(warehouseID string) error {
	return commonErrors.NewNotFound(codes.WarehouseNotFound, "仓库不存在").
		WithDetail("warehouseID", warehouseID).Build()
}

// InventoryCreateFailed 库存创建失败错误
func InventoryCreateFailed(reason string, err error) error {
	return commonErrors.NewInternal("inventory.create.failed", "创建库存失败").
		WithDetail("reason", reason).
		Wrap(err).Build()
}

// InventoryUpdateFailed 库存更新失败错误
func InventoryUpdateFailed(productID, operation string, err error) error {
	return commonErrors.NewInternal("inventory.update.failed", "更新库存失败").
		WithDetail("productID", productID).
		WithDetail("operation", operation).
		Wrap(err).Build()
}

// InventoryTransferFailed 库存调拨失败错误
func InventoryTransferFailed(fromWarehouse, toWarehouse, productID string, err error) error {
	return commonErrors.NewInternal("inventory.transfer.failed", "库存调拨失败").
		WithDetail("fromWarehouse", fromWarehouse).
		WithDetail("toWarehouse", toWarehouse).
		WithDetail("productID", productID).
		Wrap(err).Build()
}

// ==================== 采购管理错误 ====================

// PurchaseNotFound 采购单不存在错误
func PurchaseNotFound(purchaseID string) error {
	return commonErrors.NewNotFound(codes.PurchaseNotFound, "采购单不存在").
		WithDetail("purchaseID", purchaseID).Build()
}

// PurchaseCreateFailed 采购单创建失败错误
func PurchaseCreateFailed(reason string, err error) error {
	return commonErrors.NewInternal("purchase.create.failed", "创建采购单失败").
		WithDetail("reason", reason).
		Wrap(err).Build()
}

// SupplierNotFound 供应商不存在错误
func SupplierNotFound(supplierID string) error {
	return commonErrors.NewNotFound(codes.SupplierNotFound, "供应商不存在").
		WithDetail("supplierID", supplierID).Build()
}

// PurchaseApprovalFailed 采购审批失败错误
func PurchaseApprovalFailed(purchaseID, reason string, err error) error {
	return commonErrors.NewInternal("purchase.approval.failed", "采购审批失败").
		WithDetail("purchaseID", purchaseID).
		WithDetail("reason", reason).
		Wrap(err).Build()
}

// ==================== 财务管理错误 ====================

// FinanceRecordNotFound 财务记录不存在错误
func FinanceRecordNotFound(recordID string) error {
	return commonErrors.NewNotFound("finance.record.notFound", "财务记录不存在").
		WithDetail("recordID", recordID).Build()
}

// FinanceCalculationFailed 财务计算失败错误
func FinanceCalculationFailed(operation string, err error) error {
	return commonErrors.NewInternal("finance.calculation.failed", "财务计算失败").
		WithDetail("operation", operation).
		Wrap(err).Build()
}

// CurrencyNotSupported 货币不支持错误
func CurrencyNotSupported(currency string) error {
	return commonErrors.NewValidation("finance.currency.notSupported", "不支持的货币").
		WithDetail("currency", currency).Build()
}

// ExchangeRateNotFound 汇率不存在错误
func ExchangeRateNotFound(fromCurrency, toCurrency string) error {
	return commonErrors.NewNotFound("finance.exchangeRate.notFound", "汇率不存在").
		WithDetail("fromCurrency", fromCurrency).
		WithDetail("toCurrency", toCurrency).Build()
}

// ==================== 安全管理错误 ====================

// SessionNotFound 会话不存在错误
func SessionNotFound(sessionID string) error {
	return commonErrors.NewNotFound("security.session.notFound", "会话不存在").
		WithDetail("sessionID", sessionID).Build()
}

// SessionExpired 会话已过期错误
func SessionExpired(sessionID string) error {
	return commonErrors.NewUnauthorized("security.session.expired", "会话已过期").
		WithDetail("sessionID", sessionID).Build()
}

// SessionCreateFailed 会话创建失败错误
func SessionCreateFailed(reason string, err error) error {
	return commonErrors.NewInternal("security.session.create.failed", "创建会话失败").
		WithDetail("reason", reason).
		Wrap(err).Build()
}

// TokenInvalid Token无效错误
func TokenInvalid(reason string) error {
	return commonErrors.NewUnauthorized("security.token.invalid", "Token无效").
		WithDetail("reason", reason).Build()
}

// ==================== 权限管理错误 ====================

// RoleNotFound 角色不存在错误
func RoleNotFound(roleID string) error {
	return commonErrors.NewNotFound("auth.role.notFound", "角色不存在").
		WithDetail("roleID", roleID).Build()
}

// PermissionNotFound 权限不存在错误
func PermissionNotFound(permissionID string) error {
	return commonErrors.NewNotFound("auth.permission.notFound", "权限不存在").
		WithDetail("permissionID", permissionID).Build()
}

// RoleAssignmentFailed 角色分配失败错误
func RoleAssignmentFailed(userID, roleID string, err error) error {
	return commonErrors.NewInternal("auth.role.assignment.failed", "角色分配失败").
		WithDetail("userID", userID).
		WithDetail("roleID", roleID).
		Wrap(err).Build()
}

// PermissionDeniedForResource 资源权限不足错误
func PermissionDeniedForResource(resource, action string) error {
	return commonErrors.NewPermission("auth.permission.denied", "资源权限不足").
		WithDetail("resource", resource).
		WithDetail("action", action).Build()
}

// ==================== 数据库相关错误 ====================

// DatabaseError 数据库错误（使用新的错误适配器）
func DatabaseError(err error) error {
	// 使用新的数据库错误适配器
	return commonErrors.NewInternal("database.error", "数据库操作失败").
		Wrap(err).Build()
}
