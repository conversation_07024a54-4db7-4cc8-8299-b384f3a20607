package context

import "context"

type tenantContextKey string

const key = tenantContextKey("tenant_context")

// TenantContext 用于在请求上下文中安全地传递租户信息
type TenantContext struct {
	ID   string // Tenant BusinessID
	Code string
	Name string
}

// NewContext 将租户上下文存储到 context 中
func NewContext(ctx context.Context, tenantCtx TenantContext) context.Context {
	return context.WithValue(ctx, key, tenantCtx)
}

// FromContext 从 context 中获取租户上下文
func FromContext(ctx context.Context) (TenantContext, bool) {
	tenantCtx, ok := ctx.Value(key).(TenantContext)
	return tenantCtx, ok
}
