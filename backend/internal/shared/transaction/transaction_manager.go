package transaction

import "context"

// TransactionManager 事务管理器接口
// 定义在Shared层，为所有领域提供统一的事务管理抽象
type TransactionManager interface {
	// ExecuteInTransaction 在事务中执行操作
	// 如果fn返回错误，事务将回滚；否则提交事务
	ExecuteInTransaction(ctx context.Context, fn func(txCtx context.Context) error) error
}

// TransactionContext 事务上下文标记接口
// 用于在仓储层识别当前是否在事务中执行
type TransactionContext interface {
	// IsTransaction 标记这是一个事务上下文
	IsTransaction() bool
}

// TxKey 事务上下文键类型
type TxKey string

const (
	// TransactionKey 事务在上下文中的键名
	TransactionKey TxKey = "transaction"
)
