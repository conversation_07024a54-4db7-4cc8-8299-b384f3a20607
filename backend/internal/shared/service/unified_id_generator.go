package service

import (
	"context"

	"backend/internal/shared/types"
)

// UnifiedIDGenerator 统一ID生成器接口
// 支持生成多种类型的ID，包括雪花ID、UUID、序列号等
type UnifiedIDGenerator interface {
	// === 单个ID生成 ===

	// GenerateID 生成单个ID
	GenerateID(ctx context.Context, request *types.IDGenerationRequest) (*types.IDGenerationResult, error)

	// === 批量ID生成 ===

	// GenerateBatchIDs 批量生成ID
	GenerateBatchIDs(ctx context.Context, request *types.BatchIDGenerationRequest) (*types.BatchIDGenerationResult, error)

	// === 便捷方法 - 雪花ID ===

	// GenerateSnowflakeID 生成雪花ID（用于技术主键）
	GenerateSnowflakeID(ctx context.Context, domain types.IDDomain) (int64, error)

	// GenerateSnowflakeIDs 批量生成雪花ID
	GenerateSnowflakeIDs(ctx context.Context, domain types.IDDomain, count int) ([]int64, error)

	// === 便捷方法 - 业务UUID ===

	// GenerateBusinessUUID 生成业务UUID（语义化）
	GenerateBusinessUUID(ctx context.Context, domain types.IDDomain, tenantID, entityKey string, metadata map[string]interface{}) (string, error)

	// GenerateSimpleUUID 生成简单UUID（随机）
	GenerateSimpleUUID(ctx context.Context, domain types.IDDomain) (string, error)

	// === 便捷方法 - 序列号 ===

	// GenerateSequenceID 生成序列号ID（用于业务单号）
	GenerateSequenceID(ctx context.Context, domain types.IDDomain, prefix string, length int) (string, error)

	// === ID验证 ===

	// ValidateID 验证ID格式和有效性
	ValidateID(ctx context.Context, idValue string, idType types.IDType, domain types.IDDomain) error

	// === 配置和管理 ===

	// GetSupportedTypes 获取支持的ID类型列表
	GetSupportedTypes() []types.IDType

	// GetSupportedDomains 获取支持的领域列表
	GetSupportedDomains() []types.IDDomain

	// GetGeneratorStats 获取生成器统计信息
	GetGeneratorStats(ctx context.Context) (map[string]interface{}, error)
}

// EntityIDGenerator 实体ID生成器接口
// 为实体提供便捷的ID生成方法，特别为BaseEntity设计
type EntityIDGenerator interface {
	// GenerateEntityIDs 为实体生成所需的所有ID
	// 返回技术ID(雪花ID)和业务ID(UUID)
	GenerateEntityIDs(ctx context.Context, domain types.IDDomain, tenantID, entityKey string, metadata map[string]interface{}) (techID int64, businessID string, err error)

	// GenerateEntityIDsSimple 为实体生成简单ID（不包含语义信息）
	GenerateEntityIDsSimple(ctx context.Context, domain types.IDDomain) (techID int64, businessID string, err error)

	// RegenerateBusinessID 重新生成业务ID（保持技术ID不变）
	RegenerateBusinessID(ctx context.Context, domain types.IDDomain, tenantID, entityKey string, metadata map[string]interface{}) (string, error)
}

// IDGeneratorFactory ID生成器工厂接口
// 用于创建和管理不同类型的ID生成器
type IDGeneratorFactory interface {
	// CreateUnifiedGenerator 创建统一ID生成器
	CreateUnifiedGenerator(config *IDGeneratorConfig) (UnifiedIDGenerator, error)

	// CreateEntityGenerator 创建实体ID生成器
	CreateEntityGenerator(config *IDGeneratorConfig) (EntityIDGenerator, error)

	// CreateDomainSpecificGenerator 创建领域特定的ID生成器
	CreateDomainSpecificGenerator(domain types.IDDomain, config *IDGeneratorConfig) (UnifiedIDGenerator, error)
}

// IDGeneratorConfig ID生成器配置
type IDGeneratorConfig struct {
	// SnowflakeConfig 雪花ID配置
	SnowflakeConfig *SnowflakeConfig `json:"snowflake_config,omitempty"`

	// UUIDConfig UUID配置
	UUIDConfig *UUIDConfig `json:"uuid_config,omitempty"`

	// SequenceConfig 序列号配置
	SequenceConfig *SequenceConfig `json:"sequence_config,omitempty"`

	// DefaultDomain 默认领域
	DefaultDomain types.IDDomain `json:"default_domain"`

	// EnableCache 是否启用缓存
	EnableCache bool `json:"enable_cache"`

	// CacheConfig 缓存配置
	CacheConfig *CacheConfig `json:"cache_config,omitempty"`

	// ValidationEnabled 是否启用验证
	ValidationEnabled bool `json:"validation_enabled"`

	// MetricsEnabled 是否启用指标统计
	MetricsEnabled bool `json:"metrics_enabled"`
}

// SnowflakeConfig 雪花ID配置
type SnowflakeConfig struct {
	// MachineID 机器ID
	MachineID uint16 `json:"machine_id"`

	// UseIPBased 是否使用IP生成机器ID
	UseIPBased bool `json:"use_ip_based"`
}

// UUIDConfig UUID配置
type UUIDConfig struct {
	// RootNamespace 根命名空间
	RootNamespace string `json:"root_namespace"`

	// EnableSemantic 是否启用语义化UUID
	EnableSemantic bool `json:"enable_semantic"`

	// FallbackToRandom 失败时是否降级到随机UUID
	FallbackToRandom bool `json:"fallback_to_random"`

	// DomainStrategies 领域特定策略配置
	DomainStrategies map[types.IDDomain]*DomainStrategy `json:"domain_strategies,omitempty"`
}

// SequenceConfig 序列号配置
type SequenceConfig struct {
	// RedisConfig Redis配置（用于分布式序列号）
	RedisConfig *RedisConfig `json:"redis_config,omitempty"`

	// DefaultPrefix 默认前缀
	DefaultPrefix string `json:"default_prefix"`

	// DefaultLength 默认长度
	DefaultLength int `json:"default_length"`

	// PadWithZeros 是否用零填充
	PadWithZeros bool `json:"pad_with_zeros"`
}

// CacheConfig 缓存配置
type CacheConfig struct {
	// Size 缓存大小
	Size int `json:"size"`

	// TTL 缓存过期时间（秒）
	TTL int `json:"ttl"`

	// EnableDistributed 是否启用分布式缓存
	EnableDistributed bool `json:"enable_distributed"`
}

// RedisConfig Redis配置
type RedisConfig struct {
	// Host Redis主机
	Host string `json:"host"`

	// Port Redis端口
	Port int `json:"port"`

	// Password Redis密码
	Password string `json:"password"`

	// Database Redis数据库
	Database int `json:"database"`
}

// DomainStrategy 领域策略配置
type DomainStrategy struct {
	// RequiredMetadata 必需的元数据字段
	RequiredMetadata []string `json:"required_metadata"`

	// OptionalMetadata 可选的元数据字段
	OptionalMetadata []string `json:"optional_metadata"`

	// AttributeOrder 属性排序（用于生成语义化名称）
	AttributeOrder []string `json:"attribute_order"`

	// NamespaceCustom 自定义命名空间
	NamespaceCustom string `json:"namespace_custom,omitempty"`
}
