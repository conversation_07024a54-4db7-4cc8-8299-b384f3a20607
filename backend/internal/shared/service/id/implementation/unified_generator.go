package implementation

import (
	"context"
	"fmt"
	"strconv"
	"time"

	"backend/internal/shared/service/id"
	"backend/internal/shared/types"
	apperrors "backend/pkg/common/errors"
	"backend/pkg/common/errors/codes"
	"backend/pkg/infrastructure/snowflake"
	"backend/pkg/infrastructure/uuid"
	uuidTypes "backend/pkg/types/uuid"
)

// UnifiedIDGeneratorImpl 统一ID生成器具体实现
type UnifiedIDGeneratorImpl struct {
	snowflakeGen *snowflake.Generator
	uuidManager  *uuid.Manager
	config       *id.IDGeneratorConfig
}

// NewUnifiedIDGenerator 创建统一ID生成器实例
func NewUnifiedIDGenerator(
	snowflakeGen *snowflake.Generator,
	uuidManager *uuid.Manager,
	config *id.IDGeneratorConfig,
) id.UnifiedIDGenerator {
	return &UnifiedIDGeneratorImpl{
		snowflakeGen: snowflakeGen,
		uuidManager:  uuidManager,
		config:       config,
	}
}

// GenerateID 生成单个ID
func (g *UnifiedIDGeneratorImpl) GenerateID(ctx context.Context, request *types.IDGenerationRequest) (*types.IDGenerationResult, error) {
	// 验证请求
	if err := g.validateRequest(request); err != nil {
		return nil, apperrors.NewInternal(codes.GeneratorInvalidArg, "请求参数无效").Wrap(err).Build()
	}

	result := &types.IDGenerationResult{
		Type:        request.Type,
		Domain:      request.Domain,
		GeneratedAt: time.Now().UnixNano(),
	}

	switch request.Type {
	case types.IDTypeSnowflake:
		id, err := g.generateSnowflakeID(ctx, request.Domain)
		if err != nil {
			return nil, err
		}
		result.SnowflakeID = id

	case types.IDTypeSemanticUUID:
		uuidStr, semanticName, err := g.generateSemanticUUID(ctx, request)
		if err != nil {
			return nil, err
		}
		result.UUID = uuidStr
		result.SemanticName = semanticName

	case types.IDTypeRandomUUID:
		uuidStr, err := g.generateRandomUUID(ctx)
		if err != nil {
			return nil, err
		}
		result.UUID = uuidStr

	case types.IDTypeSequence:
		seqID, err := g.generateSequenceID(ctx, request)
		if err != nil {
			return nil, err
		}
		result.SequenceID = seqID

	default:
		return nil, apperrors.NewInternal(codes.GeneratorInvalidArg, "不支持的ID类型").Build()
	}

	return result, nil
}

// GenerateBatchIDs 批量生成ID
func (g *UnifiedIDGeneratorImpl) GenerateBatchIDs(ctx context.Context, request *types.BatchIDGenerationRequest) (*types.BatchIDGenerationResult, error) {
	result := &types.BatchIDGenerationResult{
		Results: make([]types.IDGenerationResult, 0, len(request.Items)),
		Errors:  make(map[int]error),
	}

	for i, item := range request.Items {
		// 构建单个ID生成请求
		singleRequest := request.BaseRequest
		singleRequest.EntityKey = item.EntityKey
		singleRequest.Metadata = item.Metadata
		if item.CustomSuffix != "" {
			singleRequest.Suffix = item.CustomSuffix
		}

		// 生成ID
		idResult, err := g.GenerateID(ctx, &singleRequest)
		if err != nil {
			result.Errors[i] = err
			result.FailureCount++
			continue
		}

		result.Results = append(result.Results, *idResult)
		result.SuccessCount++
	}

	return result, nil
}

// GenerateSnowflakeID 生成雪花ID
func (g *UnifiedIDGeneratorImpl) GenerateSnowflakeID(ctx context.Context, domain types.IDDomain) (int64, error) {
	return g.generateSnowflakeID(ctx, domain)
}

// GenerateSnowflakeIDs 批量生成雪花ID
func (g *UnifiedIDGeneratorImpl) GenerateSnowflakeIDs(ctx context.Context, domain types.IDDomain, count int) ([]int64, error) {
	if count <= 0 || count > 1000 {
		return nil, apperrors.NewInternal(codes.GeneratorInvalidArg, "批量生成ID数量超出范围").Build()
	}

	ids := make([]int64, count)
	for i := 0; i < count; i++ {
		id, err := g.generateSnowflakeID(ctx, domain)
		if err != nil {
			return nil, apperrors.NewInternal(codes.GeneratorInvalidArg, "批量生成雪花ID失败").Wrap(err).Build()
		}
		ids[i] = id
	}

	return ids, nil
}

// GenerateBusinessUUID 生成业务UUID
func (g *UnifiedIDGeneratorImpl) GenerateBusinessUUID(ctx context.Context, domain types.IDDomain, tenantID, entityKey string, metadata map[string]interface{}) (string, error) {
	request := &types.IDGenerationRequest{
		Type:      types.IDTypeSemanticUUID,
		Purpose:   types.IDPurposeBusiness,
		Domain:    domain,
		TenantID:  tenantID,
		EntityKey: entityKey,
		Metadata:  metadata,
	}

	uuidStr, _, err := g.generateSemanticUUID(ctx, request)
	return uuidStr, err
}

// GenerateSimpleUUID 生成简单UUID
func (g *UnifiedIDGeneratorImpl) GenerateSimpleUUID(ctx context.Context, domain types.IDDomain) (string, error) {
	return g.generateRandomUUID(ctx)
}

// GenerateSequenceID 生成序列号ID
func (g *UnifiedIDGeneratorImpl) GenerateSequenceID(ctx context.Context, domain types.IDDomain, prefix string, length int) (string, error) {
	request := &types.IDGenerationRequest{
		Type:   types.IDTypeSequence,
		Domain: domain,
		Prefix: prefix,
		Length: length,
	}

	return g.generateSequenceID(ctx, request)
}

// ValidateID 验证ID格式和有效性
func (g *UnifiedIDGeneratorImpl) ValidateID(ctx context.Context, idValue string, idType types.IDType, domain types.IDDomain) error {
	switch idType {
	case types.IDTypeSnowflake:
		id, err := strconv.ParseInt(idValue, 10, 64)
		if err != nil {
			return apperrors.NewInternal(codes.GeneratorInvalidArg, "雪花ID格式错误").Build()
		}
		return g.validateSnowflakeID(id)

	case types.IDTypeSemanticUUID, types.IDTypeRandomUUID:
		return g.validateUUID(idValue)

	case types.IDTypeSequence:
		return g.validateSequenceID(idValue, domain)

	default:
		return apperrors.NewInternal(codes.GeneratorInvalidArg, "不支持的ID类型").Build()
	}
}

// GetSupportedTypes 获取支持的ID类型列表
func (g *UnifiedIDGeneratorImpl) GetSupportedTypes() []types.IDType {
	return []types.IDType{
		types.IDTypeSnowflake,
		types.IDTypeSemanticUUID,
		types.IDTypeRandomUUID,
		types.IDTypeSequence,
	}
}

// GetSupportedDomains 获取支持的领域列表
func (g *UnifiedIDGeneratorImpl) GetSupportedDomains() []types.IDDomain {
	return []types.IDDomain{
		types.IDDomainUser,
		types.IDDomainProduct,
		types.IDDomainOrder,
		types.IDDomainInventory,
		types.IDDomainFinance,
		types.IDDomainTenant,
		types.IDDomainSecurity,
		types.IDDomainGeneric,
	}
}

// GetGeneratorStats 获取生成器统计信息
func (g *UnifiedIDGeneratorImpl) GetGeneratorStats(ctx context.Context) (map[string]interface{}, error) {
	stats := make(map[string]interface{})

	stats["supported_types"] = g.GetSupportedTypes()
	stats["supported_domains"] = g.GetSupportedDomains()
	stats["config"] = map[string]interface{}{
		"default_domain":     g.config.DefaultDomain,
		"cache_enabled":      g.config.EnableCache,
		"validation_enabled": g.config.ValidationEnabled,
		"metrics_enabled":    g.config.MetricsEnabled,
	}

	return stats, nil
}

// ==================== 私有方法 ====================

// validateRequest 验证ID生成请求
func (g *UnifiedIDGeneratorImpl) validateRequest(request *types.IDGenerationRequest) error {
	if request.Type == "" {
		return fmt.Errorf("ID类型不能为空")
	}
	if request.Domain == "" {
		return fmt.Errorf("ID领域不能为空")
	}
	if request.Purpose == "" {
		return fmt.Errorf("ID用途不能为空")
	}

	// 验证类型支持
	supportedTypes := g.GetSupportedTypes()
	typeSupported := false
	for _, t := range supportedTypes {
		if t == request.Type {
			typeSupported = true
			break
		}
	}
	if !typeSupported {
		return fmt.Errorf("不支持的ID类型: %s", request.Type)
	}

	// 验证领域支持
	supportedDomains := g.GetSupportedDomains()
	domainSupported := false
	for _, d := range supportedDomains {
		if d == request.Domain {
			domainSupported = true
			break
		}
	}
	if !domainSupported {
		return fmt.Errorf("不支持的ID领域: %s", request.Domain)
	}

	return nil
}

// generateSnowflakeID 生成雪花ID
func (g *UnifiedIDGeneratorImpl) generateSnowflakeID(ctx context.Context, domain types.IDDomain) (int64, error) {
	if g.snowflakeGen == nil {
		return 0, apperrors.NewInternal(codes.GeneratorConfig, "雪花ID生成器未配置").Build()
	}

	newID := g.snowflakeGen.Generate()
	return newID, nil
}

// generateSemanticUUID 生成语义化UUID - 重构版本，完全使用项目内部UUID系统
func (g *UnifiedIDGeneratorImpl) generateSemanticUUID(ctx context.Context, request *types.IDGenerationRequest) (string, string, error) {
	if g.config.UUIDConfig == nil || !g.config.UUIDConfig.EnableSemantic {
		return "", "", apperrors.NewInternal(codes.GeneratorConfig, "语义化UUID功能未配置或未启用").Build()
	}

	// 1. 构建语义化名称
	semanticName := g.buildSemanticName(request)

	// 2. 构建UUID生成所需的属性
	attrs := uuidTypes.DomainAttributes{
		TenantID:   request.TenantID,
		EntityType: "entity", // 固定使用"entity"作为实体类型
		Key:        request.EntityKey,
		Metadata:   request.Metadata,
	}

	// 3. 使用项目内部的UUID管理器生成UUID
	newUUID, err := g.uuidManager.Generate(ctx, string(request.Domain), attrs)
	if err != nil {
		if g.config.UUIDConfig.FallbackToRandom {
			// 降级到随机UUID - 使用项目内部的UUID管理器
			fallbackUUID, fallbackErr := g.generateRandomUUIDInternal(ctx)
			if fallbackErr != nil {
				return "", "", apperrors.NewInternal(codes.GeneratorConfig, "语义化UUID生成失败，降级生成也失败").Wrap(fallbackErr).Build()
			}
			return fallbackUUID, "random-fallback", nil
		}
		return "", "", apperrors.NewInternal(codes.GeneratorConfig, "语义化UUID生成失败").Wrap(err).Build()
	}

	return newUUID.String(), semanticName, nil
}

// generateRandomUUID 生成随机UUID - 重构版本，完全使用项目内部UUID系统
func (g *UnifiedIDGeneratorImpl) generateRandomUUID(ctx context.Context) (string, error) {
	// 使用项目内部的UUID管理器生成随机UUID
	uuidStr, err := g.generateRandomUUIDInternal(ctx)
	if err != nil {
		return "", err
	}
	return uuidStr, nil
}

// generateRandomUUIDInternal 内部随机UUID生成方法
func (g *UnifiedIDGeneratorImpl) generateRandomUUIDInternal(ctx context.Context) (string, error) {
	// 使用空的DomainAttributes生成随机UUID
	attrs := uuidTypes.DomainAttributes{
		TenantID:   "",
		EntityType: "random",
		Key:        "",
		Metadata:   nil,
	}

	// 使用GenerateWithFallback确保总是能生成UUID
	newUUID, err := g.uuidManager.GenerateWithFallback(ctx, "generic", attrs)
	if err != nil {
		return "", apperrors.NewInternal(codes.GeneratorConfig, "随机UUID生成失败").Wrap(err).Build()
	}

	return newUUID.String(), nil
}

// generateSequenceID 生成序列号ID
func (g *UnifiedIDGeneratorImpl) generateSequenceID(ctx context.Context, request *types.IDGenerationRequest) (string, error) {
	// 基础序列号生成逻辑
	// 这里使用时间戳 + 随机数的简单实现
	// 在实际生产环境中，应该使用Redis或数据库序列
	timestamp := time.Now().UnixNano() / int64(time.Millisecond)

	prefix := request.Prefix
	if prefix == "" && g.config.SequenceConfig != nil {
		prefix = g.config.SequenceConfig.DefaultPrefix
	}
	if prefix == "" {
		prefix = string(request.Domain)
	}

	length := request.Length
	if length == 0 && g.config.SequenceConfig != nil {
		length = g.config.SequenceConfig.DefaultLength
	}
	if length == 0 {
		length = 10
	}

	// 生成序列号
	seqNum := timestamp % 100000 // 取后5位
	format := fmt.Sprintf("%%s%%0%dd", length)
	sequenceID := fmt.Sprintf(format, prefix, seqNum)

	return sequenceID, nil
}

// buildSemanticName 构建语义化名称
func (g *UnifiedIDGeneratorImpl) buildSemanticName(request *types.IDGenerationRequest) string {
	parts := []string{string(request.Domain)}

	if request.TenantID != "" {
		parts = append(parts, request.TenantID)
	}

	if request.EntityKey != "" {
		parts = append(parts, request.EntityKey)
	}

	// 添加元数据中的关键信息
	if request.Metadata != nil {
		if username, ok := request.Metadata["username"].(string); ok && username != "" {
			parts = append(parts, username)
		}
		if email, ok := request.Metadata["email"].(string); ok && email != "" {
			parts = append(parts, email)
		}
	}

	semanticName := ""
	for i, part := range parts {
		if i > 0 {
			semanticName += "."
		}
		semanticName += part
	}

	return semanticName
}

// validateSnowflakeID 验证雪花ID
func (g *UnifiedIDGeneratorImpl) validateSnowflakeID(id int64) error {
	if id <= 0 {
		return apperrors.NewInternal(codes.GeneratorInvalidArg, "雪花ID格式错误").Build()
	}
	// TODO: 可以增加更详细的验证，如时间戳、机器ID等
	return nil
}

// validateUUID 验证UUID格式 - 重构版本，使用项目内部UUID验证
func (g *UnifiedIDGeneratorImpl) validateUUID(id string) error {
	// 简单的UUID格式验证
	if len(id) != 36 {
		return apperrors.NewInternal(codes.GeneratorInvalidArg, "UUID长度错误").Build()
	}

	// 检查UUID格式：8-4-4-4-12
	if id[8] != '-' || id[13] != '-' || id[18] != '-' || id[23] != '-' {
		return apperrors.NewInternal(codes.GeneratorInvalidArg, "UUID格式错误").Build()
	}

	// 检查字符是否都是有效的十六进制字符
	for i, char := range id {
		if i == 8 || i == 13 || i == 18 || i == 23 {
			continue // 跳过连字符
		}
		if !((char >= '0' && char <= '9') || (char >= 'a' && char <= 'f') || (char >= 'A' && char <= 'F')) {
			return apperrors.NewInternal(codes.GeneratorInvalidArg, "UUID包含无效字符").Build()
		}
	}

	return nil
}

// validateSequenceID 验证序列号ID
func (g *UnifiedIDGeneratorImpl) validateSequenceID(id string, domain types.IDDomain) error {
	if id == "" {
		return apperrors.NewInternal(codes.GeneratorInvalidArg, "序列号ID不能为空").Build()
	}
	// 可以添加更多序列号格式验证逻辑
	return nil
}
