package implementation

import (
	"context"

	"backend/internal/shared/service/id"
	"backend/internal/shared/types"
	apperrors "backend/pkg/common/errors"
	"backend/pkg/common/errors/codes"
)

// EntityIDServiceImpl 实体ID服务具体实现
type EntityIDServiceImpl struct {
	unifiedGenerator id.UnifiedIDGenerator
}

// NewEntityIDService 创建实体ID服务实例
func NewEntityIDService(unifiedGenerator id.UnifiedIDGenerator) id.EntityIDService {
	return &EntityIDServiceImpl{
		unifiedGenerator: unifiedGenerator,
	}
}

// GenerateEntityIDs 为实体生成完整的ID组合
func (s *EntityIDServiceImpl) GenerateEntityIDs(ctx context.Context, domain types.IDDomain, tenantID, entityKey string, metadata map[string]interface{}) (int64, string, error) {
	// 生成技术ID（雪花ID）
	techID, err := s.unifiedGenerator.GenerateSnowflakeID(ctx, domain)
	if err != nil {
		return 0, "", err
	}

	// 生成业务ID（语义化UUID）
	businessID, err := s.unifiedGenerator.GenerateBusinessUUID(ctx, domain, tenantID, entityKey, metadata)
	if err != nil {
		return 0, "", err
	}

	return techID, businessID, nil
}

// GenerateEntityIDsSimple 生成简单的ID组合（无语义信息）
func (s *EntityIDServiceImpl) GenerateEntityIDsSimple(ctx context.Context, domain types.IDDomain) (int64, string, error) {
	// 生成技术ID（雪花ID）
	techID, err := s.unifiedGenerator.GenerateSnowflakeID(ctx, domain)
	if err != nil {
		return 0, "", err
	}

	// 生成业务ID（随机UUID）
	businessID, err := s.unifiedGenerator.GenerateSimpleUUID(ctx, domain)
	if err != nil {
		return 0, "", err
	}

	return techID, businessID, nil
}

// GenerateTechID 单独生成技术ID（雪花ID）
func (s *EntityIDServiceImpl) GenerateTechID(ctx context.Context, domain types.IDDomain) (int64, error) {
	return s.unifiedGenerator.GenerateSnowflakeID(ctx, domain)
}

// GenerateBusinessID 单独生成业务ID（UUID）
func (s *EntityIDServiceImpl) GenerateBusinessID(ctx context.Context, domain types.IDDomain, tenantID, entityKey string, metadata map[string]interface{}) (string, error) {
	return s.unifiedGenerator.GenerateBusinessUUID(ctx, domain, tenantID, entityKey, metadata)
}

// GenerateSimpleBusinessID 生成简单业务ID（随机UUID）
func (s *EntityIDServiceImpl) GenerateSimpleBusinessID(ctx context.Context, domain types.IDDomain) (string, error) {
	return s.unifiedGenerator.GenerateSimpleUUID(ctx, domain)
}

// ValidateBusinessID 验证业务ID格式
func (s *EntityIDServiceImpl) ValidateBusinessID(businessID string) error {
	ctx := context.Background()
	return s.unifiedGenerator.ValidateID(ctx, businessID, types.IDTypeSemanticUUID, types.IDDomainGeneric)
}

// ValidateTechID 验证技术ID格式
func (s *EntityIDServiceImpl) ValidateTechID(techID int64) error {
	if techID <= 0 {
		return apperrors.NewInternal(codes.Encryption, "技术ID必须大于0").Build()
	}
	return nil
}

// IsValidUUID 检查是否为有效UUID
func (s *EntityIDServiceImpl) IsValidUUID(id string) bool {
	ctx := context.Background()
	err := s.unifiedGenerator.ValidateID(ctx, id, types.IDTypeRandomUUID, types.IDDomainGeneric)
	return err == nil
}

// IsValidSnowflake 检查是否为有效雪花ID
func (s *EntityIDServiceImpl) IsValidSnowflake(id int64) bool {
	return s.ValidateTechID(id) == nil
}

// EntityIDHelperImpl 实体ID辅助器具体实现
type EntityIDHelperImpl struct {
	entityIDService id.EntityIDService
}

// NewEntityIDHelper 创建实体ID辅助器实例
func NewEntityIDHelper(entityIDService id.EntityIDService) id.EntityIDHelper {
	return &EntityIDHelperImpl{
		entityIDService: entityIDService,
	}
}

// GenerateIDsForEntity 为实体生成ID
func (h *EntityIDHelperImpl) GenerateIDsForEntity(ctx context.Context, entity id.IDEntity) error {
	// 检查是否已有完整ID
	if entity.GetTechID() != 0 && entity.GetBusinessID() != "" {
		return nil // 已有ID，跳过生成
	}

	// 生成新的ID组合
	techID, businessID, err := h.entityIDService.GenerateEntityIDs(
		ctx,
		entity.GetDomain(),
		entity.GetTenantID(),
		entity.GetEntityKey(),
		entity.GetIDMetadata(),
	)
	if err != nil {
		return id.NewEntityIDError(entity.GetDomain(), entity.GetTenantID(), entity.GetEntityKey(),
			"生成实体ID失败", err, -1)
	}

	// 设置到实体
	if entity.GetTechID() == 0 {
		entity.SetTechID(techID)
	}
	if entity.GetBusinessID() == "" {
		entity.SetBusinessID(businessID)
	}

	return nil
}

// RegenerateBusinessID 重新生成实体的业务ID
func (h *EntityIDHelperImpl) RegenerateBusinessID(ctx context.Context, entity id.IDEntity) error {
	// 生成新的业务ID
	businessID, err := h.entityIDService.GenerateBusinessID(
		ctx,
		entity.GetDomain(),
		entity.GetTenantID(),
		entity.GetEntityKey(),
		entity.GetIDMetadata(),
	)
	if err != nil {
		return id.NewEntityIDError(entity.GetDomain(), entity.GetTenantID(), entity.GetEntityKey(),
			"重新生成业务ID失败", err, -1)
	}

	// 更新实体
	entity.SetBusinessID(businessID)
	return nil
}

// GenerateIDsForEntities 为实体列表批量生成ID
func (h *EntityIDHelperImpl) GenerateIDsForEntities(ctx context.Context, entities []id.IDEntity) error {
	for i, entity := range entities {
		if err := h.GenerateIDsForEntity(ctx, entity); err != nil {
			return id.NewEntityIDError(entity.GetDomain(), entity.GetTenantID(), entity.GetEntityKey(),
				"批量生成ID失败", err, i)
		}
	}
	return nil
}

// ValidateEntity 验证实体ID的完整性
func (h *EntityIDHelperImpl) ValidateEntity(entity id.IDEntity) error {
	return h.ValidateEntityIDs(entity.GetTechID(), entity.GetBusinessID(), entity.GetDomain())
}

// ValidateEntityIDs 验证实体ID格式
func (h *EntityIDHelperImpl) ValidateEntityIDs(techID int64, businessID string, domain types.IDDomain) error {
	// 验证技术ID
	if err := h.entityIDService.ValidateTechID(techID); err != nil {
		return id.NewEntityIDError(domain, "", "", "技术ID验证失败", err, -1)
	}

	// 验证业务ID
	if err := h.entityIDService.ValidateBusinessID(businessID); err != nil {
		return id.NewEntityIDError(domain, "", "", "业务ID验证失败", err, -1)
	}

	return nil
}

// SimpleEntityIDService 简化的实体ID服务实现
// 提供默认实现，适用于大多数场景
type SimpleEntityIDService struct {
	idService id.EntityIDService
}

// NewSimpleEntityIDService 创建简化的实体ID服务
func NewSimpleEntityIDService(idService id.EntityIDService) id.EntityIDHelper {
	return &SimpleEntityIDService{
		idService: idService,
	}
}

// GenerateIDsForEntity 为实体生成ID
func (s *SimpleEntityIDService) GenerateIDsForEntity(ctx context.Context, entity id.IDEntity) error {
	// 检查是否已有完整ID
	if entity.GetTechID() != 0 && entity.GetBusinessID() != "" {
		return nil // 已有ID，跳过生成
	}

	// 生成新的ID组合
	techID, businessID, err := s.idService.GenerateEntityIDs(
		ctx,
		entity.GetDomain(),
		entity.GetTenantID(),
		entity.GetEntityKey(),
		entity.GetIDMetadata(),
	)
	if err != nil {
		return err
	}

	// 设置到实体
	if entity.GetTechID() == 0 {
		entity.SetTechID(techID)
	}
	if entity.GetBusinessID() == "" {
		entity.SetBusinessID(businessID)
	}

	return nil
}

// RegenerateBusinessID 重新生成实体的业务ID
func (s *SimpleEntityIDService) RegenerateBusinessID(ctx context.Context, entity id.IDEntity) error {
	// 生成新的业务ID
	businessID, err := s.idService.GenerateBusinessID(
		ctx,
		entity.GetDomain(),
		entity.GetTenantID(),
		entity.GetEntityKey(),
		entity.GetIDMetadata(),
	)
	if err != nil {
		return err
	}

	// 更新实体
	entity.SetBusinessID(businessID)
	return nil
}

// GenerateIDsForEntities 为实体列表批量生成ID
func (s *SimpleEntityIDService) GenerateIDsForEntities(ctx context.Context, entities []id.IDEntity) error {
	for i, entity := range entities {
		if err := s.GenerateIDsForEntity(ctx, entity); err != nil {
			return id.NewEntityIDError(entity.GetDomain(), entity.GetTenantID(), entity.GetEntityKey(),
				"批量生成ID失败", err, i)
		}
	}
	return nil
}

// ValidateEntity 验证实体ID的完整性
func (s *SimpleEntityIDService) ValidateEntity(entity id.IDEntity) error {
	return s.ValidateEntityIDs(entity.GetTechID(), entity.GetBusinessID(), entity.GetDomain())
}

// ValidateEntityIDs 验证实体ID格式
func (s *SimpleEntityIDService) ValidateEntityIDs(techID int64, businessID string, domain types.IDDomain) error {
	// 验证技术ID
	if err := s.idService.ValidateTechID(techID); err != nil {
		return id.NewEntityIDError(domain, "", "", "技术ID验证失败", err, -1)
	}

	// 验证业务ID
	if err := s.idService.ValidateBusinessID(businessID); err != nil {
		return id.NewEntityIDError(domain, "", "", "业务ID验证失败", err, -1)
	}

	return nil
}
