# 统一ID生成器服务

本目录包含了九翼跨境电商ERP系统的统一ID生成器服务接口定义，支持雪花ID、UUID、序列号等多种ID类型的生成。

## 📁 文件结构

```
internal/shared/
├── types/
│   ├── base_entity.go       # 更新：支持统一ID生成器
│   └── id_types.go         # 新增：ID类型定义和枚举
├── service/
│   ├── id_generator.go      # 统一ID生成器接口
│   ├── entity_id_service.go # 实体ID服务接口
│   └── README.md           # 本说明文档
└── errors/                  # 现有错误定义
```

## 🎯 核心设计理念

### 1. 统一ID管理
- ❄️ **雪花ID**：用于 `BaseEntity.ID`（技术主键，int64）
- 🆔 **语义化UUID**：用于 `BaseEntity.BusinessID`（业务标识，string）
- 🔢 **序列号ID**：用于业务单号（如订单号、商品编码）
- 🎲 **随机UUID**：用于会话、临时标识等

### 2. 分层接口设计
- **UnifiedIDGenerator**：完整功能的统一ID生成器
- **EntityIDService**：专门为BaseEntity设计的简化接口
- **EntityIDHelper**：实体ID生成和管理的辅助器

### 3. 向后兼容
- 保持现有 `NewBaseEntity()` 方法不变
- 新增 `NewBaseEntityWithIDs()` 支持统一ID生成
- 现有代码无需修改即可继续工作

## 🚀 ID类型支持

### IDType 枚举
```go
const (
    IDTypeSnowflake     IDType = "snowflake"      // 雪花ID
    IDTypeSemanticUUID  IDType = "semantic_uuid"  // 语义化UUID
    IDTypeRandomUUID    IDType = "random_uuid"    // 随机UUID
    IDTypeSequence      IDType = "sequence"       // 序列号
    IDTypeCustom        IDType = "custom"         // 自定义
)
```

### IDPurpose 枚举
```go
const (
    IDPurposeTechnical  IDPurpose = "technical"   // 技术用途
    IDPurposeBusiness   IDPurpose = "business"    // 业务用途
    IDPurposeDisplay    IDPurpose = "display"     // 展示用途
    IDPurposeSession    IDPurpose = "session"     // 会话用途
    IDPurposeTrace      IDPurpose = "trace"       // 追踪用途
)
```

### IDDomain 枚举
```go
const (
    IDDomainUser        IDDomain = "user"         // 用户领域
    IDDomainProduct     IDDomain = "product"      // 商品领域
    IDDomainOrder       IDDomain = "order"        // 订单领域
    IDDomainInventory   IDDomain = "inventory"    // 库存领域
    IDDomainFinance     IDDomain = "finance"      // 财务领域
    IDDomainTenant      IDDomain = "tenant"       // 租户领域
    IDDomainSecurity    IDDomain = "security"     // 安全领域
    IDDomainGeneric     IDDomain = "generic"      // 通用领域
)
```

## 📋 核心接口说明

### 1. UnifiedIDGenerator (统一ID生成器)

**功能特点**：
- 支持所有类型的ID生成
- 批量生成能力
- 完整的验证和配置功能
- 统计信息和监控

**主要方法**：
```go
// 基础生成
GenerateID(ctx, request) (*IDGenerationResult, error)
GenerateBatchIDs(ctx, request) (*BatchIDGenerationResult, error)

// 便捷方法
GenerateSnowflakeID(ctx, domain) (int64, error)
GenerateBusinessUUID(ctx, domain, tenantID, key, metadata) (string, error)
GenerateSequenceID(ctx, domain, prefix, length) (string, error)

// 验证管理
ValidateID(ctx, value, type, domain) error
GetSupportedTypes() []IDType
```

### 2. EntityIDService (实体ID服务)

**功能特点**：
- 专门为BaseEntity设计
- 同时生成技术ID和业务ID
- 简化的接口设计
- 支持语义化和简单生成模式

**主要方法**：
```go
// 生成实体ID组合
GenerateEntityIDs(ctx, domain, tenantID, key, metadata) (int64, string, error)
GenerateEntityIDsSimple(ctx, domain) (int64, string, error)

// 单独生成
GenerateTechID(ctx, domain) (int64, error)
GenerateBusinessID(ctx, domain, tenantID, key, metadata) (string, error)

// 验证
ValidateBusinessID(businessID) error
ValidateTechID(techID) error
```

### 3. EntityIDHelper (实体ID辅助器)

**功能特点**：
- 为实体提供ID生成辅助功能
- 自动检测已有ID，避免重复生成
- 支持批量处理
- 完整的验证功能

**主要方法**：
```go
// 实体ID生成
GenerateIDsForEntity(ctx, entity) error
RegenerateBusinessID(ctx, entity) error

// 批量处理
GenerateIDsForEntities(ctx, entities) error

// 验证
ValidateEntity(entity) error
```

## 🏗️ BaseEntity 集成

### 更新的BaseEntity结构
```go
type BaseEntity struct {
    ID         int64           `gorm:"primaryKey;autoIncrement:false" json:"-"`
    BusinessID string          `gorm:"uniqueIndex;size:36" json:"id"`
    TenantID   string          `gorm:"index;size:36" json:"tenant_id"`  // 新增
    CreatedAt  time.Time       `gorm:"index" json:"created_at"`
    UpdatedAt  time.Time       `json:"updated_at"`
    DeletedAt  gorm.DeletedAt  `gorm:"index" json:"-"`
    Version    int             `gorm:"default:1" json:"version"`
}
```

### 新增的构造方法
```go
// 向后兼容的原有方法
NewBaseEntity() BaseEntity

// 新增方法
NewBaseEntityWithTenant(tenantID string) BaseEntity
NewBaseEntityWithIDs(techID int64, businessID, tenantID string) BaseEntity
```

### 新增的ID管理方法
```go
// ID访问方法
GetTechID() int64
SetTechID(techID int64)
GetBusinessID() string
SetBusinessID(businessID string)
GetTenantID() string
SetTenantID(tenantID string)

// 状态检查
HasCompleteIDs() bool
IsPersisted() bool
```

## 📝 使用示例

### 1. 基础实体创建（向后兼容）
```go
// 现有代码无需修改
entity := types.NewBaseEntity()
```

### 2. 使用统一ID生成器
```go
// 生成完整的实体ID
techID, businessID, err := entityIDService.GenerateEntityIDs(
    ctx, 
    types.IDDomainUser, 
    "tenant123", 
    "john.doe", 
    map[string]interface{}{
        "username": "john.doe",
        "email":    "<EMAIL>",
    },
)

// 创建实体
entity := types.NewBaseEntityWithIDs(techID, businessID, "tenant123")
```

### 3. 仓储层集成
```go
type UserRepository struct {
    db        *gorm.DB
    idHelper  service.EntityIDHelper
}

func (r *UserRepository) Save(ctx context.Context, user *User) error {
    // 自动生成ID（如果尚未生成）
    if err := r.idHelper.GenerateIDsForEntity(ctx, user); err != nil {
        return err
    }
    
    return r.db.WithContext(ctx).Create(user).Error
}
```

### 4. 批量操作
```go
// 批量生成ID
var users []*User
for _, userData := range userDataList {
    user := &User{...}
    users = append(users, user)
}

// 批量生成ID
if err := idHelper.GenerateIDsForEntities(ctx, entities); err != nil {
    return err
}
```

## 🔧 配置支持

### ID生成器配置
```go
type IDGeneratorConfig struct {
    SnowflakeConfig *SnowflakeConfig
    UUIDConfig      *UUIDConfig
    SequenceConfig  *SequenceConfig
    DefaultDomain   types.IDDomain
    EnableCache     bool
}
```

### 雪花ID配置
```go
type SnowflakeConfig struct {
    MachineID   uint16
    UseIPBased  bool
}
```

### UUID配置
```go
type UUIDConfig struct {
    RootNamespace       string
    EnableSemantic      bool
    FallbackToRandom    bool
    DomainStrategies    map[types.IDDomain]*DomainStrategy
}
```

## 🚀 迁移指南

### 第一阶段：接口定义（已完成）
- ✅ 创建ID类型定义
- ✅ 定义统一ID生成器接口
- ✅ 更新BaseEntity支持新接口

### 第二阶段：基础实现
- 🔄 实现EntityIDService具体服务
- 🔄 集成现有的雪花ID和UUID基础设施
- 🔄 创建配置管理

### 第三阶段：仓储集成
- 🔄 更新仓储构造函数，注入ID服务
- 🔄 修改Save方法，使用统一ID生成
- 🔄 更新依赖注入配置

### 第四阶段：完善功能
- 🔄 添加序列号生成支持
- 🔄 实现批量操作优化
- 🔄 添加监控和统计功能

## 💡 设计优势

✅ **统一管理**：所有类型的ID生成都通过统一接口管理  
✅ **雪花ID集成**：完美集成现有的雪花ID基础设施  
✅ **语义化UUID**：业务ID包含语义信息，便于调试和运维  
✅ **向后兼容**：现有代码无需修改即可继续工作  
✅ **类型安全**：强类型接口设计，编译时检查  
✅ **可扩展性**：支持新的ID类型和生成策略  
✅ **性能优化**：支持批量生成和缓存机制  
✅ **多租户支持**：原生支持多租户数据隔离  

这个重构方案将现有的ID生成功能统一到shared目录下，为所有领域提供一致的ID生成服务，同时保持了向后兼容性和可扩展性。 