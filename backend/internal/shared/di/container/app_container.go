package container

import (
	"backend/internal/adapters/http/router"
	"backend/pkg/infrastructure/config"
	"backend/pkg/infrastructure/logger"

	"gorm.io/gorm"
)

// App 表示应用容器
type App struct {
	Config *config.Config
	Logger logger.Logger
	Router *router.Router
	DB     *gorm.DB
}

// NewApp 创建新的应用容器
func NewApp(
	cfg *config.Config,
	logger logger.Logger,
	router *router.Router,
	db *gorm.DB,
) *App {
	return &App{
		Config: cfg,
		Logger: logger,
		Router: router,
		DB:     db,
	}
}
