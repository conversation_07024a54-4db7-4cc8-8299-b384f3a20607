//go:build wireinject
// +build wireinject

package injector

import (
	"backend/internal/shared/di/container"
	"backend/internal/shared/di/provider"

	"github.com/google/wire"
)

// InitializeApp 初始化应用
func InitializeApp(configPath string) (*container.App, func(), error) {
	wire.Build(
		// 基础设施
		provider.AppInfraProviderSet,

		// 领域模块
		provider.AuthProviderSet,
		provider.UserProviderSet,
		provider.TenantProviderSet,
		provider.MenuProviderSet,

		// 处理器 & 路由
		provider.HandlerSet,
		provider.ProvideRouter,
		provider.DemoProviderSet,

		// 应用容器
		container.NewApp,
	)
	return nil, nil, nil
}
