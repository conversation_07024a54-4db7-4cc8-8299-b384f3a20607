// Code generated by Wire. DO NOT EDIT.

//go:generate go run -mod=mod github.com/google/wire/cmd/wire
//go:build !wireinject
// +build !wireinject

package injector

import (
	"backend/internal/adapters/http/handler"
	"backend/internal/adapters/http/middleware"
	"backend/internal/shared/di/container"
	"backend/internal/shared/di/provider"
	"backend/pkg/infrastructure/auth/casbin"
)

// Injectors from api.go:

// InitializeApp 初始化应用
func InitializeApp(configPath string) (*container.App, func(), error) {
	config, err := provider.ProvideConfig(configPath)
	if err != nil {
		return nil, nil, err
	}
	logger, err := provider.ProvideLogger(config)
	if err != nil {
		return nil, nil, err
	}
	tracingManager, err := provider.ProvideTracingManager(config)
	if err != nil {
		return nil, nil, err
	}
	metricsManager := provider.ProvideMetricsManager(config)
	healthHandler := handler.NewHealthHandler(logger)
	metricsHandler := handler.NewMetricsHandler(metricsManager)
	operationTracker := provider.ProvideOperationTracker(logger, metricsManager)
	debugHandler := handler.NewDebugHandler(logger, operationTracker, metricsManager)
	alertManager := provider.ProvideAlertManager(logger)
	performanceMonitor := provider.ProvidePerformanceMonitor(logger, metricsManager, alertManager)
	monitoringHandler := handler.NewMonitoringHandler(performanceMonitor, alertManager, metricsManager)
	demoHandler := provider.NewDemoHandler(logger, tracingManager)
	manager, err := provider.ProvideDatabaseManager(config, logger)
	if err != nil {
		return nil, nil, err
	}
	userRepository := provider.ProvideUserRepository(manager)
	userAuthRepository := provider.ProvideUserAuthRepository(manager)
	userTenantRepository := provider.ProvideUserTenantRepository(manager)
	sessionRepository := provider.ProvideSessionRepository(manager)
	connectionFactory := provider.ProvideRedisConnectionFactory(logger)
	connectionManager, err := provider.ProvideRedisConnectionManager(config, connectionFactory)
	if err != nil {
		return nil, nil, err
	}
	cacheFactory := provider.ProvideCacheFactory(config, logger)
	metricsCollector := provider.ProvideCacheMetricsCollector(cacheFactory)
	client := provider.ProvideCacheRedisClient(connectionManager, logger, metricsCollector)
	serializer, err := provider.ProvideCacheSerializer(config)
	if err != nil {
		return nil, nil, err
	}
	keyBuilder := provider.ProvideCacheKeyBuilder(cacheFactory)
	cacheAdapter := provider.ProvideCacheAdapter(client, connectionManager, config, logger, serializer, keyBuilder, metricsCollector)
	cacheManager, err := provider.ProvideCacheManager(config, logger, cacheAdapter)
	if err != nil {
		return nil, nil, err
	}
	tokenCacheRepository, err := provider.ProvideTokenCacheRepository(cacheManager)
	if err != nil {
		return nil, nil, err
	}
	jwtConfig := provider.ProvideJWTConfig(config)
	generator, err := provider.ProvideSnowflakeGenerator(config)
	if err != nil {
		return nil, nil, err
	}
	jwtManager := provider.ProvideJWTManager(jwtConfig, logger, generator, tokenCacheRepository)
	db := provider.ProvideGormDB(manager)
	casbinConfig := provider.ProvideCasbinConfig(config)
	casbinManager, err := casbin.NewManager(db, casbinConfig)
	if err != nil {
		return nil, nil, err
	}
	menuRepository := provider.ProvideMenuRepository(manager)
	menuDomainService := provider.ProvideMenuDomainService(menuRepository, logger)
	routeTreeService := provider.ProvideRouteTreeService(menuRepository, menuDomainService, casbinManager, logger)
	authCommandHandler := provider.ProvideAuthCommandHandler(userRepository, userAuthRepository, userTenantRepository, sessionRepository, tokenCacheRepository, jwtManager, casbinManager, routeTreeService, logger, generator)
	authQueryHandler := provider.ProvideAuthQueryHandler(userRepository, userAuthRepository, userTenantRepository, sessionRepository, tokenCacheRepository, jwtManager, casbinManager, logger)
	authHandler := provider.ProvideAuthHandler(authCommandHandler, authQueryHandler, logger)
	authMiddleware := middleware.NewAuthMiddleware(jwtManager, logger)
	userCommandHandler := provider.ProvideUserCommandHandler(userRepository, logger, generator)
	userAuthenticationService := provider.ProvideUserAuthenticationService()
	userQueryHandler := provider.ProvideUserQueryHandler(userAuthenticationService)
	transactionManager := provider.ProvideTransactionManager(manager)
	userRegistrationUseCase := provider.ProvideUserRegistrationUseCase(transactionManager, userRepository, userAuthRepository, userTenantRepository, generator, logger)
	userManagementUseCase := provider.ProvideUserManagementUseCase(transactionManager, userRepository, userTenantRepository, logger)
	userHandler := provider.ProvideUserHandler(userCommandHandler, userQueryHandler, userRegistrationUseCase, userManagementUseCase, logger)
	tenantRepository := provider.ProvideTenantRepository(manager)
	tenantQuotaRepository := provider.ProvideTenantQuotaRepository(manager)
	tenantSubscriptionRepository := provider.ProvideTenantSubscriptionRepository(manager)
	tenantService := provider.ProvideTenantService(tenantRepository, tenantQuotaRepository, tenantSubscriptionRepository)
	tenantAssembler := provider.ProvideTenantAssembler()
	tenantUseCase := provider.ProvideTenantUseCase(tenantRepository, tenantQuotaRepository, tenantSubscriptionRepository, tenantService, tenantAssembler)
	tenantHandler := provider.ProvideTenantHandler(tenantUseCase)
	subscriptionUseCase := provider.ProvideSubscriptionUseCase(tenantSubscriptionRepository, tenantRepository, tenantService, tenantAssembler)
	subscriptionHandler := provider.ProvideSubscriptionHandler(subscriptionUseCase)
	router := provider.ProvideRouter(logger, tracingManager, metricsManager, healthHandler, metricsHandler, debugHandler, monitoringHandler, demoHandler, authHandler, authMiddleware, userHandler, tenantHandler, subscriptionHandler)
	app := container.NewApp(config, logger, router, db)
	return app, func() {
	}, nil
}
