package provider

import (
	"backend/internal/adapters/http/handler"
	"backend/pkg/infrastructure/logger"
	"backend/pkg/infrastructure/monitoring"

	"github.com/google/wire"
)

// DemoProviderSet 提供演示处理器
var DemoProviderSet = wire.NewSet(
	NewDemoHandler,
)

// NewDemoHandler 创建新的演示处理器
func NewDemoHandler(logger logger.Logger, tracingManager *monitoring.TracingManager) *handler.DemoHandler {
	return handler.NewDemoHandler(logger, tracingManager)
}
