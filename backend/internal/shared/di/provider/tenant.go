package provider

import (
	"backend/internal/adapters/http/handler"
	tenantRepoImpl "backend/internal/adapters/persistence/repository/relational/tenant"
	"backend/internal/application/assembler"
	commandHandler "backend/internal/application/command/handler"
	queryHandler "backend/internal/application/query/handler"
	tenantUseCase "backend/internal/application/usecase/tenant"
	tenantRepo "backend/internal/domain/tenant/repository"
	tenantServ "backend/internal/domain/tenant/service"
	database "backend/pkg/infrastructure/database/abstraction"
	"backend/pkg/infrastructure/logger"
	"backend/pkg/infrastructure/snowflake"

	"github.com/google/wire"
)

// TenantProviderSet Tenant领域相关的依赖注入
var TenantProviderSet = wire.NewSet(
	// Tenant仓储
	ProvideTenantRepository,
	ProvideTenantQuotaRepository,
	ProvideTenantSubscriptionRepository,

	// Tenant服务
	ProvideTenantService,

	// Tenant组装器
	ProvideTenantAssembler,

	// Tenant CQRS Handlers
	ProvideTenantCommandHandler,
	ProvideTenant<PERSON><PERSON>y<PERSON>and<PERSON>,
	ProvideTenant<PERSON><PERSON><PERSON><PERSON>ommand<PERSON><PERSON><PERSON>,
	ProvideTenantQuotaQueryHandler,
	ProvideTenantSubscriptionCommandHandler,
	ProvideTenantSubscriptionQueryHandler,

	// Tenant用例
	ProvideTenantUseCase,
	ProvideSubscriptionUseCase,

	// Tenant处理器
	ProvideTenantHandler,
	ProvideSubscriptionHandler,
)

// ProvideTenantRepository 提供租户仓储
func ProvideTenantRepository(dbManager database.Manager) tenantRepo.TenantRepository {
	return tenantRepoImpl.NewTenantRepository(dbManager)
}

// ProvideTenantQuotaRepository 提供租户配额仓储
func ProvideTenantQuotaRepository(dbManager database.Manager) tenantRepo.TenantQuotaRepository {
	return tenantRepoImpl.NewQuotaRepository(dbManager)
}

// ProvideTenantSubscriptionRepository 提供租户订阅仓储
func ProvideTenantSubscriptionRepository(dbManager database.Manager) tenantRepo.TenantSubscriptionRepository {
	return tenantRepoImpl.NewSubscriptionRepository(dbManager)
}

// ProvideTenantService 提供租户服务
func ProvideTenantService(
	tenantRepo tenantRepo.TenantRepository,
	quotaRepo tenantRepo.TenantQuotaRepository,
	subscriptionRepo tenantRepo.TenantSubscriptionRepository,
) tenantServ.TenantService {
	return tenantServ.NewTenantService(tenantRepo, quotaRepo, subscriptionRepo)
}

// ProvideTenantAssembler 提供租户组装器
func ProvideTenantAssembler() *assembler.TenantAssembler {
	return assembler.NewTenantAssembler()
}

// ProvideTenantUseCase 提供租户用例
func ProvideTenantUseCase(
	tenantRepo tenantRepo.TenantRepository,
	quotaRepo tenantRepo.TenantQuotaRepository,
	subscriptionRepo tenantRepo.TenantSubscriptionRepository,
	tenantService tenantServ.TenantService,
	assembler *assembler.TenantAssembler,
) *tenantUseCase.TenantUseCase {
	return tenantUseCase.NewTenantUseCase(
		tenantRepo,
		quotaRepo,
		subscriptionRepo,
		tenantService,
		assembler,
	)
}

// ProvideSubscriptionUseCase 提供订阅用例
func ProvideSubscriptionUseCase(
	subscriptionRepo tenantRepo.TenantSubscriptionRepository,
	tenantRepo tenantRepo.TenantRepository,
	tenantService tenantServ.TenantService,
	assembler *assembler.TenantAssembler,
) *tenantUseCase.SubscriptionUseCase {
	return tenantUseCase.NewSubscriptionUseCase(
		subscriptionRepo,
		tenantRepo,
		tenantService,
		assembler,
	)
}

// ProvideTenantHandler 提供租户处理器
func ProvideTenantHandler(
	tenantUseCase *tenantUseCase.TenantUseCase,
) *handler.TenantHandler {
	return handler.NewTenantHandler(tenantUseCase)
}

// ProvideSubscriptionHandler 提供订阅处理器
func ProvideSubscriptionHandler(
	subscriptionUseCase *tenantUseCase.SubscriptionUseCase,
) *handler.SubscriptionHandler {
	return handler.NewSubscriptionHandler(subscriptionUseCase)
}

// ==================== CQRS Handler Providers ====================

// ProvideTenantCommandHandler 提供租户命令处理器
func ProvideTenantCommandHandler(
	tenantRepo tenantRepo.TenantRepository,
	quotaRepo tenantRepo.TenantQuotaRepository,
	subscriptionRepo tenantRepo.TenantSubscriptionRepository,
	tenantService tenantServ.TenantService,
	logger logger.Logger,
	snowflake snowflake.Generator,
) *commandHandler.TenantCommandHandler {
	return commandHandler.NewTenantCommandHandler(
		tenantRepo,
		quotaRepo,
		subscriptionRepo,
		tenantService,
		logger,
		snowflake,
	)
}

// ProvideTenantQueryHandler 提供租户查询处理器
func ProvideTenantQueryHandler(
	tenantRepo tenantRepo.TenantRepository,
	quotaRepo tenantRepo.TenantQuotaRepository,
	subscriptionRepo tenantRepo.TenantSubscriptionRepository,
	tenantService tenantServ.TenantService,
	tenantAssembler *assembler.TenantAssembler,
	logger logger.Logger,
) *queryHandler.TenantQueryHandler {
	return queryHandler.NewTenantQueryHandler(
		tenantRepo,
		quotaRepo,
		subscriptionRepo,
		tenantService,
		tenantAssembler,
		logger,
	)
}

// ProvideTenantQuotaCommandHandler 提供租户配额命令处理器
func ProvideTenantQuotaCommandHandler(
	tenantRepo tenantRepo.TenantRepository,
	quotaRepo tenantRepo.TenantQuotaRepository,
	tenantService tenantServ.TenantService,
	logger logger.Logger,
) *commandHandler.TenantQuotaCommandHandler {
	return commandHandler.NewTenantQuotaCommandHandler(
		tenantRepo,
		quotaRepo,
		tenantService,
		logger,
	)
}

// ProvideTenantQuotaQueryHandler 提供租户配额查询处理器
func ProvideTenantQuotaQueryHandler(
	tenantRepo tenantRepo.TenantRepository,
	quotaRepo tenantRepo.TenantQuotaRepository,
	logger logger.Logger,
) *queryHandler.TenantQuotaQueryHandler {
	return queryHandler.NewTenantQuotaQueryHandler(
		tenantRepo,
		quotaRepo,
		logger,
	)
}

// ProvideTenantSubscriptionCommandHandler 提供租户订阅命令处理器
func ProvideTenantSubscriptionCommandHandler(
	tenantRepo tenantRepo.TenantRepository,
	subscriptionRepo tenantRepo.TenantSubscriptionRepository,
	tenantService tenantServ.TenantService,
	logger logger.Logger,
	snowflake snowflake.Generator,
) *commandHandler.TenantSubscriptionCommandHandler {
	return commandHandler.NewTenantSubscriptionCommandHandler(
		tenantRepo,
		subscriptionRepo,
		tenantService,
		logger,
		snowflake,
	)
}

// ProvideTenantSubscriptionQueryHandler 提供租户订阅查询处理器
func ProvideTenantSubscriptionQueryHandler(
	tenantRepo tenantRepo.TenantRepository,
	subscriptionRepo tenantRepo.TenantSubscriptionRepository,
	logger logger.Logger,
) *queryHandler.TenantSubscriptionQueryHandler {
	return queryHandler.NewTenantSubscriptionQueryHandler(
		tenantRepo,
		subscriptionRepo,
		logger,
	)
}
