package provider

import (
	cacheRepoImpl "backend/internal/adapters/persistence/repository/cache/redis"
	securityRepoImpl "backend/internal/adapters/persistence/repository/relational/auth"
	"backend/internal/application/assembler"
	securityUseCase "backend/internal/application/usecase/auth"
	securityRepo "backend/internal/domain/auth/repository"
	securityServ "backend/internal/domain/auth/service"
	"backend/pkg/infrastructure/cache"
	database "backend/pkg/infrastructure/database/abstraction"
	"backend/pkg/infrastructure/snowflake"
)

// TokenProviders Token相关的依赖注入提供者
type TokenProviders struct {
	// 仓储层
	TokenRepository     securityRepo.TokenRepository
	SessionRepository   securityRepo.SessionRepository
	PreAuthRepository   securityRepo.PreAuthRepository
	BlacklistRepository securityRepo.BlacklistRepository

	// 领域服务层
	TokenService   securityServ.TokenService
	SessionService securityServ.SessionService
	PreAuthService securityServ.PreAuthService

	// 应用层
	TokenAssembler *assembler.TokenAssembler
	TokenUseCase   *securityUseCase.TokenUseCase
}

// NewTokenProviders 创建Token提供者
func NewTokenProviders(dbManager database.Manager, cache cache.AdvancedCache, idGenerator *snowflake.Generator) *TokenProviders {
	// 仓储层
	tokenRepo := securityRepoImpl.NewTokenRepository(dbManager)
	sessionRepo := cacheRepoImpl.NewSessionRepository(cache)
	preAuthRepo := cacheRepoImpl.NewPreAuthRepository(cache)
	blacklistRepo := cacheRepoImpl.NewBlacklistRepository(cache)

	// 领域服务层 (暂时使用空实现，后续需要实现)
	var tokenService securityServ.TokenService
	var sessionService securityServ.SessionService
	var preAuthService securityServ.PreAuthService

	// 应用层
	tokenAssembler := assembler.NewTokenAssembler()
	tokenUseCase := securityUseCase.NewTokenUseCase(
		tokenRepo,
		sessionRepo,
		preAuthRepo,
		blacklistRepo,
		tokenService,
		sessionService,
		preAuthService,
		tokenAssembler,
		idGenerator,
	)

	return &TokenProviders{
		// 仓储层
		TokenRepository:     tokenRepo,
		SessionRepository:   sessionRepo,
		PreAuthRepository:   preAuthRepo,
		BlacklistRepository: blacklistRepo,

		// 领域服务层
		TokenService:   tokenService,
		SessionService: sessionService,
		PreAuthService: preAuthService,

		// 应用层
		TokenAssembler: tokenAssembler,
		TokenUseCase:   tokenUseCase,
	}
}
