package provider

import (
	"backend/internal/adapters/http/handler"
	"backend/internal/adapters/http/middleware"
	"backend/internal/adapters/http/router"
	"backend/pkg/infrastructure/logger"
	"backend/pkg/infrastructure/monitoring"

	"github.com/google/wire"
)

// ProvideHealthHandler 提供健康检查处理器
func ProvideHealthHandler(logger logger.Logger) *handler.HealthHandler {
	return handler.NewHealthHandler(logger)
}

// ProvideMetricsHandler 提供指标处理器
func ProvideMetricsHandler(metricsManager *monitoring.MetricsManager) *handler.MetricsHandler {
	return handler.NewMetricsHandler(metricsManager)
}

// ProvideDebugHandler 提供调试处理器
func ProvideDebugHandler(
	logger logger.Logger,
	operationTracker *monitoring.OperationTracker,
	metricsManager *monitoring.MetricsManager,
) *handler.DebugHandler {
	return handler.NewDebugHandler(logger, operationTracker, metricsManager)
}

// ProvideMonitoringHandler 提供监控处理器
func ProvideMonitoringHandler(
	performanceMonitor *monitoring.PerformanceMonitor,
	alertManager *monitoring.AlertManager,
	metricsManager *monitoring.MetricsManager,
) *handler.MonitoringHandler {
	return handler.NewMonitoringHandler(performanceMonitor, alertManager, metricsManager)
}

// ProvideRouter 提供路由器
func ProvideRouter(
	logger logger.Logger,
	tracingManager *monitoring.TracingManager,
	metricsManager *monitoring.MetricsManager,
	healthHandler *handler.HealthHandler,
	metricsHandler *handler.MetricsHandler,
	debugHandler *handler.DebugHandler,
	monitoringHandler *handler.MonitoringHandler,
	demoHandler *handler.DemoHandler,
	authHandler *handler.AuthHandler,
	authMiddleware *middleware.AuthMiddleware,
	userHandler *handler.UserHandler,
	tenantHandler *handler.TenantHandler,
	subscriptionHandler *handler.SubscriptionHandler,
) *router.Router {
	return router.NewRouter(
		logger,
		tracingManager,
		metricsManager,
		healthHandler,
		metricsHandler,
		debugHandler,
		monitoringHandler,
		demoHandler,
		authHandler,
		authMiddleware,
		userHandler,
		tenantHandler,
		subscriptionHandler,
	)
}

// DI 提供者集合
var (
	// RepositorySet 现在由领域特定提供者文件管理 (例如 user.go)

	// UseCaseSet 现在由领域特定提供者文件管理 (例如 user.go, security.go)

	// HandlerSet 包含基础handler
	HandlerSet = wire.NewSet(
		middleware.NewAuthMiddleware,
		handler.NewHealthHandler,
		handler.NewMetricsHandler,
		handler.NewDebugHandler,
		handler.NewMonitoringHandler,
		// SecurityHandler由SecurityProviderSet提供
		// DemoHandler由DemoProviderSet提供
		// UserHandler, TenantHandler, SubscriptionHandler
		// 由各自的ProviderSet提供，避免重复绑定
	)
)
