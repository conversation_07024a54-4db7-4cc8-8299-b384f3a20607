package provider

import (
	"fmt"
	"os"
	"path/filepath"
	"runtime"
	"time"

	"backend/internal/shared/types"
	"backend/pkg/infrastructure/config"
	database "backend/pkg/infrastructure/database/abstraction"
	"backend/pkg/infrastructure/database/postgres"
	"backend/pkg/infrastructure/logger"
	"backend/pkg/infrastructure/monitoring"
	"backend/pkg/infrastructure/mq"
	redisMQ "backend/pkg/infrastructure/mq/redis"
	"backend/pkg/infrastructure/snowflake"
	"backend/pkg/infrastructure/uuid"
	uuidTypes "backend/pkg/types/uuid"

	"github.com/google/wire"
	redisClient "github.com/redis/go-redis/v9"
	"gorm.io/gorm"

	"backend/internal/shared/service/id"
	"backend/internal/shared/service/id/implementation"
)

// ProvideConfig 提供配置
func ProvideConfig(configPath string) (*config.Config, error) {
	loader := config.NewLoader(configPath)
	cfg, err := loader.Load()
	if err != nil {
		return nil, fmt.Errorf("failed to load config: %w", err)
	}

	return cfg, nil
}

// AppInfraProviderSet 基础设施提供者集合
var AppInfraProviderSet = wire.NewSet(
	ProvideConfig,
	ProvideLogger,
	ProvideDatabaseManager,
	ProvideRepositoryContext,
	ProvideGormDB, // 临时兼容性
	ProvideSnowflakeGenerator,
	ProvideMessageQueue,
	ProvideTracingManager,
	ProvideMetricsManager,
	ProvideOperationTracker,
	ProvideAlertManager,
	ProvidePerformanceMonitor,
	CacheProviderSet,
	ProvideUUIDManager,
	ProvideIDGeneratorConfig,
	ProvideUnifiedIDGenerator,
	ProvideEntityIDService,
	ProvideEntityIDHelper,
)

// dbConfig 数据库配置适配器
type dbConfig struct {
	cfg config.DatabaseConfig
}

func (d *dbConfig) GetDriver() string                 { return d.cfg.Driver }
func (d *dbConfig) GetDSN() string                    { return d.cfg.GetDSN() }
func (d *dbConfig) GetMaxOpenConns() int              { return d.cfg.MaxOpenConns }
func (d *dbConfig) GetMaxIdleConns() int              { return d.cfg.MaxIdleConns }
func (d *dbConfig) GetConnMaxLifetime() time.Duration { return d.cfg.ConnMaxLifetime }
func (d *dbConfig) GetConnMaxIdleTime() time.Duration { return d.cfg.ConnMaxIdleTime }

// ProvideLogger 提供日志服务
func ProvideLogger(cfg *config.Config) (logger.Logger, error) {
	loggerConfig := &logger.Config{
		Level:      cfg.Logger.Level,
		Format:     cfg.Logger.Format,
		Output:     cfg.Logger.Output,
		File:       cfg.Logger.File,
		MaxSize:    cfg.Logger.MaxSize,
		MaxAge:     cfg.Logger.MaxAge,
		MaxBackups: cfg.Logger.MaxBackups,
		Compress:   cfg.Logger.Compress,
	}
	return logger.NewZapLogger(loggerConfig)
}

// ProvideDatabaseManager 提供数据库管理器
func ProvideDatabaseManager(cfg *config.Config, log logger.Logger) (database.Manager, error) {
	// 创建PostgreSQL数据库管理器
	manager := postgres.NewDatabaseManager(&cfg.Database)

	// 初始化数据库管理器（建立连接和迁移器）
	if err := manager.Initialize(); err != nil {
		log.ErrorNoCtx("数据库管理器初始化失败", "error", err)
		return nil, fmt.Errorf("初始化数据库管理器失败: %w", err)
	}

	// 执行健康检查
	if err := manager.HealthCheck(); err != nil {
		log.WarnNoCtx("数据库健康检查失败", "error", err)
		// 健康检查失败不阻止启动，仅记录警告
	}

	log.InfoNoCtx("数据库管理器初始化成功",
		"driver", cfg.Database.Driver,
		"host", cfg.Database.Host,
		"database", cfg.Database.Database,
		"auto_migrate", cfg.Database.Migration.AutoMigrate)

	return manager, nil
}

// ProvideMessageQueue 提供消息队列
func ProvideMessageQueue(cfg *config.Config, log logger.Logger) (mq.MQManager, error) {
	// 创建Redis客户端
	client := redisClient.NewClient(&redisClient.Options{
		Addr:     fmt.Sprintf("%s:%d", cfg.Redis.Host, cfg.Redis.Port),
		Password: cfg.Redis.Password,
		DB:       cfg.Redis.DB,
	})

	// 使用Redis客户端创建MQ管理器
	return redisMQ.NewRedisMQManager(client), nil
}

// ProvideSnowflakeGenerator 提供雪花ID生成器
func ProvideSnowflakeGenerator(cfg *config.Config) (*snowflake.Generator, error) {
	machineID := cfg.Business.Snowflake.MachineID
	var generator *snowflake.Generator
	if machineID == 0 {
		generator = snowflake.NewGeneratorWithIP()
	} else {
		generator = snowflake.NewGenerator(uint16(machineID))
	}
	return generator, nil
}

// ProvideTracingManager 提供链路追踪管理器
func ProvideTracingManager(cfg *config.Config) (*monitoring.TracingManager, error) {
	tracingConfig := &monitoring.TracingConfig{
		Enable:      cfg.Monitoring.Tracing.Enable,
		Endpoint:    cfg.Monitoring.Tracing.Endpoint,
		ServiceName: cfg.Monitoring.Tracing.Service,
		Environment: cfg.Monitoring.Tracing.Environment,
		Version:     cfg.Monitoring.Tracing.Version,
	}

	return monitoring.NewTracingManager(tracingConfig)
}

// ProvideMetricsManager 提供指标管理器
func ProvideMetricsManager(cfg *config.Config) *monitoring.MetricsManager {
	metricsManager := monitoring.NewMetricsManager()

	// 设置应用信息
	metricsManager.SetAppInfo(
		cfg.Monitoring.Tracing.Version,
		cfg.Monitoring.Tracing.Environment,
		cfg.Monitoring.Tracing.Service,
	)

	return metricsManager
}

// ProvideOperationTracker 提供操作追踪器
func ProvideOperationTracker(logger logger.Logger, metricsManager *monitoring.MetricsManager) *monitoring.OperationTracker {
	return monitoring.NewOperationTracker(logger, metricsManager)
}

// ProvideAlertManager 提供告警管理器
func ProvideAlertManager(logger logger.Logger) *monitoring.AlertManager {
	alertManager := monitoring.NewAlertManager(logger)

	// 添加日志告警通道
	logChannel := monitoring.NewLogAlertChannel(logger)
	alertManager.AddChannel(logChannel)

	// TODO: 根据配置添加其他告警通道（邮件、Webhook等）

	return alertManager
}

// ProvidePerformanceMonitor 提供性能监控器
func ProvidePerformanceMonitor(
	logger logger.Logger,
	metricsManager *monitoring.MetricsManager,
	alertManager *monitoring.AlertManager,
) *monitoring.PerformanceMonitor {
	return monitoring.NewPerformanceMonitor(logger, metricsManager, alertManager)
}

// ProvideUUIDManager 提供UUID管理器
func ProvideUUIDManager(cfg *config.Config) (*uuid.Manager, error) {
	uuidConfig := uuidTypes.GeneratorConfig{
		RootNamespace: "9wings-erp",
		FallbackToV4:  true,
		EnableCache:   true,
		CacheSize:     1000,
	}
	return uuid.NewManager(uuidConfig)
}

// ProvideIDGeneratorConfig 提供ID生成器配置
func ProvideIDGeneratorConfig(cfg *config.Config) *id.IDGeneratorConfig {
	return &id.IDGeneratorConfig{
		SnowflakeConfig: &id.SnowflakeConfig{
			MachineID:  uint16(cfg.Business.Snowflake.MachineID),
			UseIPBased: cfg.Business.Snowflake.MachineID == 0,
		},
		UUIDConfig: &id.UUIDConfig{
			RootNamespace:    cfg.Business.UUID.RootNamespace,
			EnableSemantic:   cfg.Business.UUID.EnableSemantic,
			FallbackToRandom: cfg.Business.UUID.FallbackToRandom,
			DomainStrategies: buildDomainStrategies(cfg),
		},
		SequenceConfig: &id.SequenceConfig{
			DefaultPrefix: cfg.Business.IDGenerator.DefaultPrefix,
			DefaultLength: cfg.Business.IDGenerator.DefaultLength,
			PadWithZeros:  cfg.Business.IDGenerator.PadWithZeros,
		},
		DefaultDomain:     types.IDDomainGeneric,
		EnableCache:       cfg.Business.IDGenerator.EnableCache,
		ValidationEnabled: cfg.Business.IDGenerator.ValidationEnabled,
		MetricsEnabled:    cfg.Business.IDGenerator.MetricsEnabled,
	}
}

// ProvideUnifiedIDGenerator 提供统一ID生成器
func ProvideUnifiedIDGenerator(
	snowflakeGen *snowflake.Generator,
	uuidManager *uuid.Manager,
	idGenConfig *id.IDGeneratorConfig,
) (id.UnifiedIDGenerator, error) {
	return implementation.NewUnifiedIDGenerator(snowflakeGen, uuidManager, idGenConfig), nil
}

// ProvideEntityIDService 提供实体ID服务
func ProvideEntityIDService(unifiedGenerator id.UnifiedIDGenerator) id.EntityIDService {
	return implementation.NewEntityIDService(unifiedGenerator)
}

// ProvideEntityIDHelper 提供实体ID辅助器
func ProvideEntityIDHelper(entityIDService id.EntityIDService) id.EntityIDHelper {
	return implementation.NewEntityIDHelper(entityIDService)
}

// 智能解析迁移文件目录路径
func resolveMigrationsDir(migrationsDir string) string {
	// 如果是绝对路径，直接返回
	if filepath.IsAbs(migrationsDir) {
		return migrationsDir
	}

	// 获取当前文件所在目录
	_, currentFile, _, _ := runtime.Caller(0)
	projectRoot := findProjectRoot(currentFile)

	// 拼接绝对路径
	absolutePath := filepath.Join(projectRoot, migrationsDir)
	return absolutePath
}

// 查找项目根目录
func findProjectRoot(startPath string) string {
	dir := filepath.Dir(startPath)

	// 向上查找，直到找到go.mod文件
	for {
		goModPath := filepath.Join(dir, "go.mod")
		if fileExists(goModPath) {
			return dir
		}

		parent := filepath.Dir(dir)
		if parent == dir {
			// 已经到达根目录
			break
		}
		dir = parent
	}

	// 如果没找到go.mod，返回当前目录向上4级（基于项目结构）
	_, currentFile, _, _ := runtime.Caller(1)
	return filepath.Join(filepath.Dir(currentFile), "..", "..", "..", "..")
}

// 检查文件是否存在
func fileExists(filename string) bool {
	_, err := os.Stat(filename)
	return !os.IsNotExist(err)
}

// buildDomainStrategies 构建领域策略配置
func buildDomainStrategies(cfg *config.Config) map[types.IDDomain]*id.DomainStrategy {
	strategies := make(map[types.IDDomain]*id.DomainStrategy)

	// 用户领域策略
	strategies[types.IDDomainUser] = &id.DomainStrategy{
		RequiredMetadata: []string{"tenant_id", "entity_type", "key"},
		OptionalMetadata: []string{"username", "email", "role"},
		AttributeOrder:   []string{"tenant_id", "entity_type", "key", "username"},
	}

	// 商品领域策略
	strategies[types.IDDomainProduct] = &id.DomainStrategy{
		RequiredMetadata: []string{"tenant_id", "entity_type", "key"},
		OptionalMetadata: []string{"sku", "category", "brand"},
		AttributeOrder:   []string{"tenant_id", "entity_type", "category", "key"},
	}

	// 订单领域策略
	strategies[types.IDDomainOrder] = &id.DomainStrategy{
		RequiredMetadata: []string{"tenant_id", "entity_type", "key"},
		OptionalMetadata: []string{"customer_id", "status", "type"},
		AttributeOrder:   []string{"tenant_id", "entity_type", "key", "customer_id"},
	}

	// 租户领域策略
	strategies[types.IDDomainTenant] = &id.DomainStrategy{
		RequiredMetadata: []string{"entity_type", "key"},
		OptionalMetadata: []string{"domain", "type", "plan"},
		AttributeOrder:   []string{"entity_type", "key", "domain"},
	}

	// 安全领域策略
	strategies[types.IDDomainSecurity] = &id.DomainStrategy{
		RequiredMetadata: []string{"tenant_id", "entity_type", "key"},
		OptionalMetadata: []string{"user_id", "action", "resource"},
		AttributeOrder:   []string{"tenant_id", "entity_type", "key", "user_id"},
	}

	// 通用领域策略
	strategies[types.IDDomainGeneric] = &id.DomainStrategy{
		RequiredMetadata: []string{"entity_type", "key"},
		OptionalMetadata: []string{"tenant_id", "category"},
		AttributeOrder:   []string{"entity_type", "key"},
	}

	return strategies
}

// ProvideRepositoryContext 提供仓储上下文
func ProvideRepositoryContext(dbManager database.Manager) database.RepositoryContext {
	// 创建简单的仓储上下文实现
	return &repositoryContext{
		dbManager: dbManager,
	}
}

// repositoryContext 仓储上下文实现
type repositoryContext struct {
	dbManager          database.Manager
	currentTransaction database.Transaction
}

func (r *repositoryContext) GetDatabaseManager() database.Manager {
	return r.dbManager
}

func (r *repositoryContext) GetDataAccess() database.DataAccess {
	return r.dbManager.GetDataAccess()
}

func (r *repositoryContext) GetQueryBuilder() database.QueryBuilder {
	return r.dbManager.GetQueryBuilder()
}

func (r *repositoryContext) GetTransactionManager() database.TransactionManager {
	return r.dbManager.GetTransactionManager()
}

func (r *repositoryContext) GetTransaction() database.Transaction {
	return r.currentTransaction
}

func (r *repositoryContext) IsInTransaction() bool {
	return r.currentTransaction != nil
}

// ProvideGormDB 提供GORM数据库实例（临时兼容性）
func ProvideGormDB(dbManager database.Manager) *gorm.DB {
	connection := dbManager.GetConnection()
	return connection.GetDB()
}
