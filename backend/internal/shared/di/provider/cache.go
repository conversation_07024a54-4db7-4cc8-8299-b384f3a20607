package provider

import (
	"context"
	"fmt"

	"github.com/google/wire"

	redisAdapt "backend/internal/adapters/persistence/cache/redis"
	"backend/pkg/infrastructure/cache"
	redisInfra "backend/pkg/infrastructure/cache/redis"
	"backend/pkg/infrastructure/config"
	"backend/pkg/infrastructure/logger"
)

// CacheProviderSet 缓存相关的依赖注入提供者集合
var CacheProviderSet = wire.NewSet(
	ProvideRedisConnectionManager,
	ProvideCacheRedisClient,
	ProvideRedisConnectionFactory,
	ProvideCacheFactory,
	ProvideCacheKeyBuilder,
	ProvideCacheMetricsCollector,
	ProvideCacheSerializer,
	ProvideCacheAdapter,
	ProvideCacheManager,
)

// ProvideRedisConnectionFactory 提供Redis连接工厂
func ProvideRedisConnectionFactory(logger logger.Logger) *redisInfra.ConnectionFactory {
	return redisInfra.NewConnectionFactory(logger)
}

// ProvideRedisConnectionManager 提供Redis连接管理器
func ProvideRedisConnectionManager(
	cfg *config.Config,
	factory *redisInfra.ConnectionFactory,
) (*redisInfra.ConnectionManager, error) {

	// 创建Redis连接管理器
	manager, err := factory.CreateConnection(&cfg.Redis)
	if err != nil {
		return nil, fmt.Errorf("创建Redis连接管理器失败: %w", err)
	}

	// 建立连接
	ctx := context.Background()
	if err := manager.Connect(ctx); err != nil {
		return nil, fmt.Errorf("建立Redis连接失败: %w", err)
	}

	return manager, nil
}

// ProvideCacheRedisClient 提供Redis缓存客户端封装
func ProvideCacheRedisClient(
	manager *redisInfra.ConnectionManager,
	logger logger.Logger,
	metrics cache.MetricsCollector,
) *redisInfra.Client {
	redisClient := manager.GetClient()
	return redisInfra.NewClient(redisClient, logger, metrics)
}

// ProvideCacheFactory 提供缓存工厂
func ProvideCacheFactory(
	cfg *config.Config,
	logger logger.Logger,
) *cache.CacheFactory {
	return cache.NewCacheFactory(cfg, logger)
}

// ProvideCacheKeyBuilder 提供缓存键构建器
func ProvideCacheKeyBuilder(factory *cache.CacheFactory) cache.KeyBuilder {
	return factory.CreateKeyBuilder()
}

// ProvideCacheMetricsCollector 提供缓存指标收集器
func ProvideCacheMetricsCollector(factory *cache.CacheFactory) cache.MetricsCollector {
	return factory.CreateMetricsCollector()
}

// ProvideCacheSerializer 提供缓存序列化器
func ProvideCacheSerializer(cfg *config.Config) (cache.Serializer, error) {
	serializationType := cfg.Business.Cache.Redis.Serialization
	if serializationType == "" {
		serializationType = "json" // 默认使用JSON序列化
	}

	factory := redisAdapt.NewSerializerFactory()
	serializer, err := factory.CreateSerializer(serializationType)
	if err != nil {
		return nil, fmt.Errorf("创建序列化器失败: %w", err)
	}

	return serializer, nil
}

// ProvideCacheAdapter 提供Redis缓存适配器
func ProvideCacheAdapter(
	cacheClient *redisInfra.Client,
	manager *redisInfra.ConnectionManager,
	cfg *config.Config,
	logger logger.Logger,
	serializer cache.Serializer,
	keyBuilder cache.KeyBuilder,
	metrics cache.MetricsCollector,
) *redisAdapt.CacheAdapter {

	// 创建Redis缓存适配器
	adapter := redisAdapt.NewCacheAdapter(
		cacheClient,
		manager.GetClient(),
		&cfg.Business.Cache.Redis,
		logger,
		serializer,
		nil, // 暂时不使用压缩器
		keyBuilder,
		metrics,
	)

	return adapter
}

// ProvideCacheManager 提供缓存管理器
func ProvideCacheManager(
	cfg *config.Config,
	logger logger.Logger,
	adapter *redisAdapt.CacheAdapter,
) (*cache.Manager, error) {

	// 创建缓存管理器
	manager := cache.NewManager(&cfg.Business.Cache, logger)

	// 注册默认的Redis缓存适配器
	if err := manager.RegisterCache("default", adapter); err != nil {
		return nil, fmt.Errorf("注册缓存适配器失败: %w", err)
	}

	// 注册其他预定义的缓存实例
	cacheNames := []string{"user_profile", "product_info", "session", "inventory", "order"}
	for _, name := range cacheNames {
		if err := manager.RegisterCache(name, adapter); err != nil {
			logger.WarnNoCtx("注册缓存实例失败", "name", name, "error", err)
		}
	}

	return manager, nil
}

// CacheDependencies 缓存相关依赖的聚合结构
type CacheDependencies struct {
	Manager           *cache.Manager
	Adapter           *redisAdapt.CacheAdapter
	RedisClient       *redisInfra.Client
	ConnectionManager *redisInfra.ConnectionManager
	KeyBuilder        cache.KeyBuilder
	MetricsCollector  cache.MetricsCollector
	Serializer        cache.Serializer
}

// ProvideCacheDependencies 提供缓存相关依赖的聚合
func ProvideCacheDependencies(
	manager *cache.Manager,
	adapter *redisAdapt.CacheAdapter,
	cacheRedisClient *redisInfra.Client,
	connectionManager *redisInfra.ConnectionManager,
	keyBuilder cache.KeyBuilder,
	metricsCollector cache.MetricsCollector,
	serializer cache.Serializer,
) *CacheDependencies {
	return &CacheDependencies{
		Manager:           manager,
		Adapter:           adapter,
		RedisClient:       cacheRedisClient,
		ConnectionManager: connectionManager,
		KeyBuilder:        keyBuilder,
		MetricsCollector:  metricsCollector,
		Serializer:        serializer,
	}
}

// GetCache 便捷方法：获取缓存实例
func (cd *CacheDependencies) GetCache(name string) (cache.AdvancedCache, error) {
	return cd.Manager.GetCache(name)
}

// GetGenericCache 便捷方法：获取泛型缓存实例
func (cd *CacheDependencies) GetGenericCache(name string) (cache.GenericCache, error) {
	return cd.Manager.GetGenericCache(name)
}

// GetDistributedCache 便捷方法：获取分布式缓存实例
func (cd *CacheDependencies) GetDistributedCache(name string) (cache.DistributedCache, error) {
	return cd.Manager.GetDistributedCache(name)
}

// Close 关闭所有缓存连接
func (cd *CacheDependencies) Close() error {
	// 关闭缓存管理器
	if err := cd.Manager.Close(); err != nil {
		return fmt.Errorf("关闭缓存管理器失败: %w", err)
	}

	// 关闭Redis连接
	ctx := context.Background()
	if err := cd.ConnectionManager.Close(ctx); err != nil {
		return fmt.Errorf("关闭Redis连接失败: %w", err)
	}

	return nil
}
