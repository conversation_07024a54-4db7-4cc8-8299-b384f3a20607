package provider

import (
	"backend/internal/adapters/http/handler"
	userRepo "backend/internal/adapters/persistence/repository/relational/user"
	commandHandler "backend/internal/application/command/handler"
	queryHandler "backend/internal/application/query/handler"
	userUseCase "backend/internal/application/usecase/user"
	userDomain "backend/internal/domain/user/repository"
	userService "backend/internal/domain/user/service"
	database "backend/pkg/infrastructure/database/abstraction"
	"backend/pkg/infrastructure/logger"
	"backend/pkg/infrastructure/snowflake"

	"github.com/google/wire"

	authRepo "backend/internal/domain/auth/repository"
	"backend/internal/shared/transaction"
)

// UserProviderSet User领域相关的依赖注入
var UserProviderSet = wire.NewSet(
	// User仓储
	ProvideUserRepository,
	ProvideUserTenantRepository,

	// User领域服务
	ProvideUserAuthenticationService,

	// User CQRS handlers
	ProvideUserCommandHandler,
	ProvideUserQueryHandler,

	// User用例（过渡期保留）
	ProvideUserRegistrationUseCase,
	ProvideUserManagementUseCase,

	// User处理器
	ProvideUserHandler,
)

// ProvideUserRepository 提供用户仓储
func ProvideUserRepository(dbManager database.Manager) userDomain.UserRepository {
	return userRepo.NewRepository(dbManager)
}

// ProvideUserTenantRepository 提供用户租户仓储
func ProvideUserTenantRepository(dbManager database.Manager) userDomain.UserTenantRepository {
	return userRepo.NewUserTenantRepository(dbManager)
}

// ProvideUserAuthenticationService 提供用户认证服务
func ProvideUserAuthenticationService() userService.UserAuthenticationService {
	return userService.NewUserAuthenticationService()
}

// ProvideUserRegistrationUseCase 提供用户注册用例
func ProvideUserRegistrationUseCase(
	txManager transaction.TransactionManager,
	userRepository userDomain.UserRepository,
	userAuthRepository authRepo.UserAuthRepository,
	userTenantRepository userDomain.UserTenantRepository,
	snowflakeGen *snowflake.Generator,
	logger logger.Logger,
) *userUseCase.UserRegistrationUseCase {
	return userUseCase.NewUserRegistrationUseCase(
		txManager,
		userRepository,
		userAuthRepository,
		userTenantRepository,
		snowflakeGen,
		logger,
	)
}

// ProvideUserManagementUseCase 提供用户管理用例
func ProvideUserManagementUseCase(
	txManager transaction.TransactionManager,
	userRepository userDomain.UserRepository,
	userTenantRepository userDomain.UserTenantRepository,
	logger logger.Logger,
) *userUseCase.UserManagementUseCase {
	return userUseCase.NewUserManagementUseCase(
		txManager,
		userRepository,
		userTenantRepository,
		logger,
	)
}

// ProvideUserCommandHandler 提供用户命令处理器
func ProvideUserCommandHandler(
	userRepository userDomain.UserRepository,
	logger logger.Logger,
	snowflakeGen *snowflake.Generator,
) *commandHandler.UserCommandHandler {
	return commandHandler.NewUserCommandHandler(
		userRepository,
		logger,
		*snowflakeGen,
	)
}

// ProvideUserQueryHandler 提供用户查询处理器
func ProvideUserQueryHandler(
	userService userService.UserAuthenticationService,
) queryHandler.UserQueryHandler {
	return queryHandler.NewUserQueryHandler(userService)
}

// ProvideUserHandler 提供用户处理器
func ProvideUserHandler(
	userCommandHandler *commandHandler.UserCommandHandler,
	userQueryHandler queryHandler.UserQueryHandler,
	registrationUseCase *userUseCase.UserRegistrationUseCase,
	managementUseCase *userUseCase.UserManagementUseCase,
	logger logger.Logger,
) *handler.UserHandler {
	return handler.NewUserHandler(
		userCommandHandler,
		userQueryHandler,
		registrationUseCase,
		managementUseCase,
		logger,
	)
}
