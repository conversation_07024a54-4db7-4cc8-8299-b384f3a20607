package provider

import (
	menuRepo "backend/internal/adapters/persistence/repository/relational/menu"
	"backend/internal/application/service"
	"backend/internal/domain/menu/repository"
	menuService "backend/internal/domain/menu/service"
	"backend/pkg/infrastructure/auth"
	dbAbstraction "backend/pkg/infrastructure/database/abstraction"
	"backend/pkg/infrastructure/logger"

	"github.com/google/wire"
)

// ProvideMenuRepository 提供菜单仓储
func ProvideMenuRepository(dbManager dbAbstraction.Manager) repository.MenuRepository {
	return menuRepo.NewMenuRepository(dbManager)
}

// ProvideMenuDomainService 提供菜单领域服务
func ProvideMenuDomainService(
	menuRepo repository.MenuRepository,
	logger logger.Logger,
) menuService.MenuDomainService {
	return menuService.NewMenuDomainService(menuRepo, logger)
}

// ProvideRouteTreeService 提供路由树服务
func ProvideRouteTreeService(
	menuRepo repository.MenuRepository,
	menuDomainSvc menuService.MenuDomainService,
	casbinManager auth.CasbinManager,
	logger logger.Logger,
) service.RouteTreeService {
	return service.NewRouteTreeService(
		menuRepo,
		menuDomainSvc,
		casbinManager,
		logger,
	)
}

// MenuProviderSet 菜单模块提供者集合
var MenuProviderSet = wire.NewSet(
	ProvideMenuRepository,
	ProvideMenuDomainService,
	ProvideRouteTreeService,
)
