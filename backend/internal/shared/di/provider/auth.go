package provider

import (
	"backend/internal/adapters/http/handler"
	cacheRepoImpl "backend/internal/adapters/persistence/repository/cache/redis"
	authRepoImpl "backend/internal/adapters/persistence/repository/relational/auth"
	commandHandler "backend/internal/application/command/handler"
	queryHandler "backend/internal/application/query/handler"
	"backend/internal/application/service"
	authRepo "backend/internal/domain/auth/repository"
	userRepo "backend/internal/domain/user/repository"
	"backend/internal/shared/transaction"
	"backend/pkg/infrastructure/auth"
	"backend/pkg/infrastructure/auth/casbin"
	"backend/pkg/infrastructure/auth/jwt"
	"backend/pkg/infrastructure/cache"
	"backend/pkg/infrastructure/config"
	database "backend/pkg/infrastructure/database/abstraction"
	"backend/pkg/infrastructure/database/adapter"
	"backend/pkg/infrastructure/logger"
	"backend/pkg/infrastructure/snowflake"

	"github.com/google/wire"
)

// AuthProviderSet 提供了安全认证模块的所有依赖
var AuthProviderSet = wire.NewSet(
	// Security Repositories
	ProvideUserAuthRepository,
	ProvideTokenRepository,
	ProvideSessionRepository,

	// Token缓存仓储 - 使用Redis实现
	ProvideTokenCacheRepository,

	// Transaction Manager - 事务管理器
	ProvideTransactionManager,

	// Managers - 绑定到基础设施层接口
	ProvideJWTConfig,
	ProvideJWTManager,
	wire.Bind(new(auth.JWTManager), new(*jwt.Manager)),

	ProvideCasbinConfig,
	casbin.NewManager,
	wire.Bind(new(auth.CasbinManager), new(*casbin.Manager)),

	// CQRS Handlers
	ProvideAuthCommandHandler,
	ProvideAuthQueryHandler,

	// security处理器
	ProvideAuthHandler,
)

// ProvideTokenCacheRepository 提供Token缓存仓储
func ProvideTokenCacheRepository(cacheManager *cache.Manager) (authRepo.TokenCacheRepository, error) {
	advancedCache, err := cacheManager.GetCache("default")
	if err != nil {
		return nil, err
	}

	return cacheRepoImpl.NewTokenCacheRepository(advancedCache), nil
}

// ProvideJWTManager 提供JWT管理器
func ProvideJWTManager(cfg *config.JWTConfig, logger logger.Logger, snowflakeGen *snowflake.Generator, tokenCacheRepo authRepo.TokenCacheRepository) *jwt.Manager {
	return jwt.NewManager(cfg, logger, snowflakeGen, tokenCacheRepo)
}

// ProvideJWTConfig 提供JWT配置
func ProvideJWTConfig(cfg *config.Config) *config.JWTConfig {
	return &cfg.Auth.JWT
}

// ProvideCasbinConfig 提供Casbin配置
func ProvideCasbinConfig(cfg *config.Config) *casbin.Config {
	return &casbin.Config{
		ModelPath: cfg.Auth.Casbin.ModelPath,
		TableName: cfg.Auth.Casbin.TableName,
	}
}

// ProvideTransactionManager 提供事务管理器
func ProvideTransactionManager(manager database.Manager) transaction.TransactionManager {
	return adapter.NewTransactionManagerAdapter(manager.GetTransactionManager())
}

// ProvideAuthHandler 提供安全认证处理器
func ProvideAuthHandler(
	cmdHandler *commandHandler.AuthCommandHandler,
	qryHandler *queryHandler.AuthQueryHandler,
	logger logger.Logger,
) *handler.AuthHandler {
	return handler.NewAuthHandler(cmdHandler, qryHandler, logger)
}

// ProvideUserAuthRepository 提供用户认证仓储
func ProvideUserAuthRepository(dbManager database.Manager) authRepo.UserAuthRepository {
	return authRepoImpl.NewUserAuthRepository(dbManager)
}

// ProvideTokenRepository 提供Token仓储
func ProvideTokenRepository(dbManager database.Manager) authRepo.TokenRepository {
	return authRepoImpl.NewTokenRepository(dbManager)
}

// ProvideSessionRepository 提供会话仓储
func ProvideSessionRepository(dbManager database.Manager) authRepo.SessionRepository {
	return authRepoImpl.NewSessionRepository(dbManager)
}

// ProvideAuthCommandHandler 提供Auth命令处理器
func ProvideAuthCommandHandler(
	userRepo userRepo.UserRepository,
	userAuthRepo authRepo.UserAuthRepository,
	userTenantRepo userRepo.UserTenantRepository,
	sessionRepo authRepo.SessionRepository,
	tokenCache authRepo.TokenCacheRepository,
	jwtManager auth.JWTManager,
	casbinManager auth.CasbinManager,
	routeTreeSvc service.RouteTreeService,
	logger logger.Logger,
	snowflakeGen *snowflake.Generator,
) *commandHandler.AuthCommandHandler {
	return commandHandler.NewAuthCommandHandler(
		userRepo,
		userAuthRepo,
		userTenantRepo,
		sessionRepo,
		tokenCache,
		jwtManager,
		casbinManager,
		routeTreeSvc,
		logger,
		*snowflakeGen,
	)
}

// ProvideAuthQueryHandler 提供Auth查询处理器
func ProvideAuthQueryHandler(
	userRepo userRepo.UserRepository,
	userAuthRepo authRepo.UserAuthRepository,
	userTenantRepo userRepo.UserTenantRepository,
	sessionRepo authRepo.SessionRepository,
	tokenCache authRepo.TokenCacheRepository,
	jwtManager auth.JWTManager,
	casbinManager auth.CasbinManager,
	logger logger.Logger,
) *queryHandler.AuthQueryHandler {
	return queryHandler.NewAuthQueryHandler(
		userRepo,
		userAuthRepo,
		userTenantRepo,
		sessionRepo,
		tokenCache,
		jwtManager,
		casbinManager,
		logger,
	)
}
