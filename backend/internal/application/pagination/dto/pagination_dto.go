package dto

import (
	"backend/pkg/common/pagination"
)

// PaginationRequestDTO 分页请求DTO
type PaginationRequestDTO struct {
	Page     int    `json:"page" validate:"min=1" form:"page" example:"1"`
	PageSize int    `json:"page_size" validate:"min=1,max=100" form:"page_size" example:"10"`
	SortBy   string `json:"sort_by,omitempty" form:"sort_by" example:"created_at"`
	SortDesc bool   `json:"sort_desc,omitempty" form:"sort_desc" example:"true"`
	Search   string `json:"search,omitempty" form:"search" example:"keyword"`
}

// CursorPaginationRequestDTO 游标分页请求DTO
type CursorPaginationRequestDTO struct {
	Cursor    string `json:"cursor,omitempty" form:"cursor" example:"eyJpZCI6MTAwfQ=="`
	Limit     int    `json:"limit" validate:"min=1,max=100" form:"limit" example:"20"`
	SortBy    string `json:"sort_by" validate:"required" form:"sort_by" example:"id"`
	SortDesc  bool   `json:"sort_desc,omitempty" form:"sort_desc" example:"false"`
	Direction string `json:"direction,omitempty" form:"direction" example:"next"`
}

// PaginationResponseDTO 分页响应DTO
type PaginationResponseDTO[T any] struct {
	Items      []T                    `json:"items"`
	Pagination *PaginationMetaDTO     `json:"pagination"`
	Links      *PaginationLinksDTO    `json:"links,omitempty"`
	Performance *PerformanceDTO       `json:"performance,omitempty"`
}

// CursorPaginationResponseDTO 游标分页响应DTO
type CursorPaginationResponseDTO[T any] struct {
	Items   []T                `json:"items"`
	Cursors *CursorMetaDTO     `json:"cursors"`
	HasMore bool               `json:"has_more"`
	Performance *PerformanceDTO `json:"performance,omitempty"`
}

// PaginationMetaDTO 分页元信息DTO
type PaginationMetaDTO struct {
	Page        int   `json:"page" example:"1"`
	PageSize    int   `json:"page_size" example:"10"`
	Total       int64 `json:"total" example:"100"`
	TotalPages  int   `json:"total_pages" example:"10"`
	HasNext     bool  `json:"has_next" example:"true"`
	HasPrevious bool  `json:"has_previous" example:"false"`
	Count       int   `json:"count" example:"10"`
}

// CursorMetaDTO 游标元信息DTO
type CursorMetaDTO struct {
	Current  string `json:"current,omitempty" example:"eyJpZCI6MTAwfQ=="`
	Next     string `json:"next,omitempty" example:"eyJpZCI6MTIwfQ=="`
	Previous string `json:"previous,omitempty" example:"eyJpZCI6ODAfQ=="`
}

// PaginationLinksDTO 分页链接DTO
type PaginationLinksDTO struct {
	Self     string `json:"self,omitempty" example:"/api/users?page=1&page_size=10"`
	First    string `json:"first,omitempty" example:"/api/users?page=1&page_size=10"`
	Previous string `json:"previous,omitempty" example:"/api/users?page=0&page_size=10"`
	Next     string `json:"next,omitempty" example:"/api/users?page=2&page_size=10"`
	Last     string `json:"last,omitempty" example:"/api/users?page=10&page_size=10"`
}

// PerformanceDTO 性能信息DTO
type PerformanceDTO struct {
	QueryTime string `json:"query_time" example:"15ms"`
	TotalTime string `json:"total_time" example:"25ms"`
	FromCache bool   `json:"from_cache" example:"false"`
}

// ToCommonRequest 转换为通用分页请求
func (dto *PaginationRequestDTO) ToCommonRequest() *pagination.Request {
	req := &pagination.Request{
		Page:     dto.Page,
		PageSize: dto.PageSize,
	}
	
	// 处理排序
	if dto.SortBy != "" {
		direction := pagination.SortAsc
		if dto.SortDesc {
			direction = pagination.SortDesc
		}
		req.Sort = []pagination.SortField{
			{
				Field:     dto.SortBy,
				Direction: direction,
			},
		}
	}
	
	// 处理搜索
	if dto.Search != "" {
		req.Search = &pagination.SearchCondition{
			Query: dto.Search,
			Mode:  pagination.SearchFuzzy,
		}
	}
	
	return req
}

// ToCommonCursorRequest 转换为通用游标分页请求
func (dto *CursorPaginationRequestDTO) ToCommonCursorRequest() *pagination.CursorRequest {
	direction := pagination.CursorNext
	if dto.Direction == "prev" {
		direction = pagination.CursorPrev
	}
	
	return &pagination.CursorRequest{
		Cursor:    dto.Cursor,
		Limit:     dto.Limit,
		SortBy:    dto.SortBy,
		SortDesc:  dto.SortDesc,
		Direction: direction,
	}
}

// FromCommonResponse 从通用分页响应转换
func FromCommonResponse[T any](response *pagination.Response[T]) *PaginationResponseDTO[T] {
	dto := &PaginationResponseDTO[T]{
		Items: response.Items,
	}
	
	if response.Meta != nil {
		dto.Pagination = &PaginationMetaDTO{
			Page:        response.Meta.Page,
			PageSize:    response.Meta.PageSize,
			Total:       response.Meta.Total,
			TotalPages:  response.Meta.TotalPages,
			HasNext:     response.Meta.HasNext,
			HasPrevious: response.Meta.HasPrevious,
			Count:       response.Meta.Count,
		}
		
		if response.Meta.Performance != nil {
			dto.Performance = &PerformanceDTO{
				QueryTime: response.Meta.Performance.QueryTime.String(),
				TotalTime: response.Meta.Performance.TotalTime.String(),
				FromCache: response.Meta.Performance.FromCache,
			}
		}
	}
	
	if response.Links != nil {
		dto.Links = &PaginationLinksDTO{
			Self:     response.Links.Self,
			First:    response.Links.First,
			Previous: response.Links.Previous,
			Next:     response.Links.Next,
			Last:     response.Links.Last,
		}
	}
	
	return dto
}

// FromCommonCursorResponse 从通用游标分页响应转换
func FromCommonCursorResponse[T any](response *pagination.CursorResponse[T]) *CursorPaginationResponseDTO[T] {
	dto := &CursorPaginationResponseDTO[T]{
		Items:   response.Items,
		HasMore: response.HasMore,
	}
	
	if response.Cursors != nil {
		dto.Cursors = &CursorMetaDTO{
			Current:  response.Cursors.Current,
			Next:     response.Cursors.Next,
			Previous: response.Cursors.Previous,
		}
	}
	
	return dto
}

// SetDefaults 设置默认值
func (dto *PaginationRequestDTO) SetDefaults() {
	if dto.Page <= 0 {
		dto.Page = 1
	}
	if dto.PageSize <= 0 {
		dto.PageSize = 10
	}
	if dto.PageSize > 100 {
		dto.PageSize = 100
	}
}

// SetDefaults 设置默认值
func (dto *CursorPaginationRequestDTO) SetDefaults() {
	if dto.Limit <= 0 {
		dto.Limit = 20
	}
	if dto.Limit > 100 {
		dto.Limit = 100
	}
	if dto.Direction == "" {
		dto.Direction = "next"
	}
}
