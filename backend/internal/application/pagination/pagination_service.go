package pagination

import (
	"context"
	"backend/pkg/common/pagination"
)

// PaginationService 应用层分页服务接口
type PaginationService interface {
	// 通用分页查询
	Paginate(ctx context.Context, req *pagination.Request) (*pagination.Response[interface{}], error)
	
	// 游标分页查询
	CursorPaginate(ctx context.Context, req *pagination.CursorRequest) (*pagination.CursorResponse[interface{}], error)
	
	// 验证分页请求
	ValidateRequest(req *pagination.Request) error
	
	// 验证游标分页请求
	ValidateCursorRequest(req *pagination.CursorRequest) error
}

// DefaultPaginationService 默认分页服务实现
type DefaultPaginationService struct {
	validator *pagination.Validator
	config    *pagination.Config
}

// NewDefaultPaginationService 创建默认分页服务
func NewDefaultPaginationService(config *pagination.Config) *DefaultPaginationService {
	if config == nil {
		config = pagination.DefaultConfig()
	}
	
	validator := pagination.NewValidator().
		WithPageSizeRange(1, config.MaxPageSize)
	
	return &DefaultPaginationService{
		validator: validator,
		config:    config,
	}
}

// Paginate 执行分页查询（通用实现）
func (s *DefaultPaginationService) Paginate(ctx context.Context, req *pagination.Request) (*pagination.Response[interface{}], error) {
	// 验证请求
	if err := s.ValidateRequest(req); err != nil {
		return nil, err
	}
	
	// 设置默认值
	req.SetDefaults()
	
	// 这里是通用实现，具体的业务逻辑应该在各个领域的服务中实现
	// 返回空响应作为示例
	return pagination.NewResponse([]interface{}{}, 0, req), nil
}

// CursorPaginate 执行游标分页查询（通用实现）
func (s *DefaultPaginationService) CursorPaginate(ctx context.Context, req *pagination.CursorRequest) (*pagination.CursorResponse[interface{}], error) {
	// 验证请求
	if err := s.ValidateCursorRequest(req); err != nil {
		return nil, err
	}
	
	// 设置默认值
	req.SetDefaults()
	
	// 这里是通用实现，具体的业务逻辑应该在各个领域的服务中实现
	// 返回空响应作为示例
	return pagination.NewCursorResponse([]interface{}{}, "", "", req.Limit), nil
}

// ValidateRequest 验证分页请求
func (s *DefaultPaginationService) ValidateRequest(req *pagination.Request) error {
	return s.validator.ValidateRequest(req)
}

// ValidateCursorRequest 验证游标分页请求
func (s *DefaultPaginationService) ValidateCursorRequest(req *pagination.CursorRequest) error {
	return s.validator.ValidateCursorRequest(req)
}

// GetConfig 获取配置
func (s *DefaultPaginationService) GetConfig() *pagination.Config {
	return s.config
}

// SetValidator 设置验证器
func (s *DefaultPaginationService) SetValidator(validator *pagination.Validator) {
	s.validator = validator
}

// GetValidator 获取验证器
func (s *DefaultPaginationService) GetValidator() *pagination.Validator {
	return s.validator
}
