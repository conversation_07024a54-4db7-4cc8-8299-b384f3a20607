package handler

import (
	"context"
	"fmt"
	"time"

	"backend/internal/application/command/model"
	"backend/internal/domain/tenant/entity"
	"backend/internal/domain/tenant/repository"
	"backend/internal/domain/tenant/service"
	"backend/pkg/infrastructure/logger"
	"backend/pkg/infrastructure/snowflake"
)

// TenantSubscriptionCommandHandler 租户订阅命令处理器
type TenantSubscriptionCommandHandler struct {
	tenantRepo       repository.TenantRepository
	subscriptionRepo repository.TenantSubscriptionRepository
	tenantService    service.TenantService
	logger           logger.Logger
	snowflake        snowflake.Generator
}

// NewTenantSubscriptionCommandHandler 创建租户订阅命令处理器
func NewTenantSubscriptionCommandHandler(
	tenantRepo repository.TenantRepository,
	subscriptionRepo repository.TenantSubscriptionRepository,
	tenantService service.TenantService,
	logger logger.Logger,
	snowflake snowflake.Generator,
) *TenantSubscriptionCommandHandler {
	return &TenantSubscriptionCommandHandler{
		tenantRepo:       tenantRepo,
		subscriptionRepo: subscriptionRepo,
		tenantService:    tenantService,
		logger:           logger,
		snowflake:        snowflake,
	}
}

// CreateSubscription 处理创建订阅命令
func (h *TenantSubscriptionCommandHandler) CreateSubscription(ctx context.Context, cmd *model.CreateSubscriptionCommand) (*model.CreateSubscriptionResult, error) {
	h.logger.Info(ctx, "Processing CreateSubscription command", map[string]interface{}{
		"tenant_id": cmd.TenantID,
		"plan_id":   cmd.PlanID,
		"plan_name": cmd.PlanName,
	})

	// 1. 验证命令
	if err := cmd.Validate(); err != nil {
		h.logger.Error(ctx, "Invalid CreateSubscription command", err, map[string]interface{}{
			"command": cmd,
		})
		return nil, fmt.Errorf("无效的创建订阅命令: %w", err)
	}

	// 2. 检查租户是否存在
	exists, err := h.tenantRepo.Exists(ctx, cmd.TenantID)
	if err != nil {
		return nil, fmt.Errorf("检查租户是否存在失败: %w", err)
	}
	if !exists {
		return nil, fmt.Errorf("租户不存在: %s", cmd.TenantID)
	}

	// 3. 检查是否已有活跃订阅
	existingSubscription, err := h.subscriptionRepo.GetByTenantID(ctx, cmd.TenantID)
	if err == nil && existingSubscription.IsActive() {
		return nil, fmt.Errorf("租户已有活跃订阅")
	}

	// 4. 使用领域服务创建订阅
	createReq := service.CreateSubscriptionRequest{
		TenantID:     cmd.TenantID,
		PlanID:       cmd.PlanID,
		PlanName:     cmd.PlanName,
		StartDate:    cmd.StartDate,
		EndDate:      cmd.EndDate,
		Price:        cmd.Price,
		Currency:     cmd.Currency,
		BillingCycle: cmd.BillingCycle,
		UserLimit:    cmd.UserLimit,
		StorageLimit: cmd.StorageLimit,
		APILimit:     cmd.APILimit,
		ProductLimit: cmd.ProductLimit,
		OrderLimit:   cmd.OrderLimit,
	}

	subscription, err := h.tenantService.CreateSubscription(ctx, createReq)
	if err != nil {
		h.logger.Error(ctx, "Failed to create subscription", err, map[string]interface{}{
			"request": createReq,
		})
		return nil, fmt.Errorf("创建订阅失败: %w", err)
	}

	h.logger.Info(ctx, "Subscription created successfully", map[string]interface{}{
		"subscription_id": subscription.BusinessID,
		"tenant_id":       subscription.TenantID,
		"plan_id":         subscription.PlanID,
	})

	// 5. 返回结果
	return &model.CreateSubscriptionResult{
		SubscriptionID: subscription.BusinessID,
		TenantID:       subscription.TenantID,
		PlanID:         subscription.PlanID,
		Status:         subscription.Status.String(),
		CreatedAt:      subscription.CreatedAt,
	}, nil
}

// RenewSubscription 处理续费订阅命令
func (h *TenantSubscriptionCommandHandler) RenewSubscription(ctx context.Context, cmd *model.RenewSubscriptionCommand) (*model.RenewSubscriptionResult, error) {
	h.logger.Info(ctx, "Processing RenewSubscription command", map[string]interface{}{
		"subscription_id": cmd.SubscriptionID,
		"start_date":      cmd.StartDate,
		"end_date":        cmd.EndDate,
	})

	// 1. 验证命令
	if err := cmd.Validate(); err != nil {
		h.logger.Error(ctx, "Invalid RenewSubscription command", err, map[string]interface{}{
			"command": cmd,
		})
		return nil, fmt.Errorf("无效的续费订阅命令: %w", err)
	}

	// 2. 获取订阅
	subscription, err := h.subscriptionRepo.GetByID(ctx, cmd.SubscriptionID)
	if err != nil {
		h.logger.Error(ctx, "Failed to get subscription", err, map[string]interface{}{
			"subscription_id": cmd.SubscriptionID,
		})
		return nil, fmt.Errorf("获取订阅失败: %w", err)
	}

	// 3. 检查是否可以续费
	if !subscription.CanRenew() {
		return nil, fmt.Errorf("订阅当前状态不允许续费")
	}

	// 4. 使用领域服务续费订阅
	renewReq := service.RenewSubscriptionRequest{
		StartDate:    cmd.StartDate,
		EndDate:      cmd.EndDate,
		Price:        cmd.Price,
		Currency:     cmd.Currency,
		BillingCycle: cmd.BillingCycle,
	}

	if err := h.tenantService.RenewSubscription(ctx, cmd.SubscriptionID, renewReq); err != nil {
		h.logger.Error(ctx, "Failed to renew subscription", err, map[string]interface{}{
			"subscription_id": cmd.SubscriptionID,
			"request":         renewReq,
		})
		return nil, fmt.Errorf("续费订阅失败: %w", err)
	}

	h.logger.Info(ctx, "Subscription renewed successfully", map[string]interface{}{
		"subscription_id": cmd.SubscriptionID,
		"new_end_date":    cmd.EndDate,
	})

	// 5. 返回结果
	return &model.RenewSubscriptionResult{
		SubscriptionID: cmd.SubscriptionID,
		NewEndDate:     cmd.EndDate,
		RenewedAt:      time.Now(),
	}, nil
}

// CancelSubscription 处理取消订阅命令
func (h *TenantSubscriptionCommandHandler) CancelSubscription(ctx context.Context, cmd *model.CancelSubscriptionCommand) (*model.CancelSubscriptionResult, error) {
	h.logger.Info(ctx, "Processing CancelSubscription command", map[string]interface{}{
		"subscription_id": cmd.SubscriptionID,
		"reason":          cmd.Reason,
	})

	// 1. 验证命令
	if err := cmd.Validate(); err != nil {
		h.logger.Error(ctx, "Invalid CancelSubscription command", err, map[string]interface{}{
			"command": cmd,
		})
		return nil, fmt.Errorf("无效的取消订阅命令: %w", err)
	}

	// 2. 获取订阅
	_, err := h.subscriptionRepo.GetByID(ctx, cmd.SubscriptionID)
	if err != nil {
		h.logger.Error(ctx, "Failed to get subscription", err, map[string]interface{}{
			"subscription_id": cmd.SubscriptionID,
		})
		return nil, fmt.Errorf("获取订阅失败: %w", err)
	}

	// 3. 使用领域服务取消订阅
	if err := h.tenantService.CancelSubscription(ctx, cmd.SubscriptionID, cmd.Reason, "system"); err != nil {
		h.logger.Error(ctx, "Failed to cancel subscription", err, map[string]interface{}{
			"subscription_id": cmd.SubscriptionID,
			"reason":          cmd.Reason,
		})
		return nil, fmt.Errorf("取消订阅失败: %w", err)
	}

	h.logger.Info(ctx, "Subscription canceled successfully", map[string]interface{}{
		"subscription_id": cmd.SubscriptionID,
		"reason":          cmd.Reason,
	})

	// 4. 返回结果
	return &model.CancelSubscriptionResult{
		SubscriptionID: cmd.SubscriptionID,
		Status:         entity.SubscriptionStatusCanceled.String(),
		CanceledAt:     time.Now(),
		RefundAmount:   cmd.RefundAmount,
	}, nil
}

// ExtendTrial 处理延长试用期命令
func (h *TenantSubscriptionCommandHandler) ExtendTrial(ctx context.Context, cmd *model.ExtendTrialCommand) (*model.ExtendTrialResult, error) {
	h.logger.Info(ctx, "Processing ExtendTrial command", map[string]interface{}{
		"subscription_id": cmd.SubscriptionID,
		"days":            cmd.Days,
		"reason":          cmd.Reason,
	})

	// 1. 验证命令
	if err := cmd.Validate(); err != nil {
		h.logger.Error(ctx, "Invalid ExtendTrial command", err, map[string]interface{}{
			"command": cmd,
		})
		return nil, fmt.Errorf("无效的延长试用期命令: %w", err)
	}

	// 2. 获取订阅
	subscription, err := h.subscriptionRepo.GetByID(ctx, cmd.SubscriptionID)
	if err != nil {
		h.logger.Error(ctx, "Failed to get subscription", err, map[string]interface{}{
			"subscription_id": cmd.SubscriptionID,
		})
		return nil, fmt.Errorf("获取订阅失败: %w", err)
	}

	// 3. 检查是否是试用期订阅
	if !subscription.IsTrialPeriod {
		return nil, fmt.Errorf("非试用期订阅无法延长试用期")
	}

	// 4. 计算新的结束日期
	var newEndDate time.Time
	if subscription.TrialEndDate != nil {
		newEndDate = subscription.TrialEndDate.AddDate(0, 0, cmd.Days)
	} else {
		newEndDate = time.Now().AddDate(0, 0, cmd.Days)
	}

	// 5. 使用领域服务延长试用期
	if err := h.tenantService.ExtendTrialPeriod(ctx, cmd.SubscriptionID, cmd.Days); err != nil {
		h.logger.Error(ctx, "Failed to extend trial period", err, map[string]interface{}{
			"subscription_id": cmd.SubscriptionID,
			"days":            cmd.Days,
		})
		return nil, fmt.Errorf("延长试用期失败: %w", err)
	}

	h.logger.Info(ctx, "Trial period extended successfully", map[string]interface{}{
		"subscription_id": cmd.SubscriptionID,
		"days":            cmd.Days,
		"new_end_date":    newEndDate,
	})

	// 6. 返回结果
	return &model.ExtendTrialResult{
		SubscriptionID: cmd.SubscriptionID,
		NewEndDate:     newEndDate,
		ExtendedDays:   cmd.Days,
		ExtendedAt:     time.Now(),
	}, nil
}

// ActivateSubscription 激活订阅
func (h *TenantSubscriptionCommandHandler) ActivateSubscription(ctx context.Context, subscriptionID string) error {
	h.logger.Info(ctx, "Processing ActivateSubscription command", map[string]interface{}{
		"subscription_id": subscriptionID,
	})

	// 1. 获取订阅
	subscription, err := h.subscriptionRepo.GetByID(ctx, subscriptionID)
	if err != nil {
		h.logger.Error(ctx, "Failed to get subscription", err, map[string]interface{}{
			"subscription_id": subscriptionID,
		})
		return fmt.Errorf("获取订阅失败: %w", err)
	}

	// 2. 激活订阅
	if err := subscription.Activate(); err != nil {
		return fmt.Errorf("激活订阅失败: %w", err)
	}

	// 3. 保存更新
	if err := h.subscriptionRepo.Update(ctx, subscription); err != nil {
		h.logger.Error(ctx, "Failed to activate subscription", err, map[string]interface{}{
			"subscription_id": subscriptionID,
		})
		return fmt.Errorf("保存订阅激活失败: %w", err)
	}

	h.logger.Info(ctx, "Subscription activated successfully", map[string]interface{}{
		"subscription_id": subscriptionID,
	})

	return nil
}

// SuspendSubscription 暂停订阅
func (h *TenantSubscriptionCommandHandler) SuspendSubscription(ctx context.Context, subscriptionID string, reason string) error {
	h.logger.Info(ctx, "Processing SuspendSubscription command", map[string]interface{}{
		"subscription_id": subscriptionID,
		"reason":          reason,
	})

	// 1. 获取订阅
	subscription, err := h.subscriptionRepo.GetByID(ctx, subscriptionID)
	if err != nil {
		h.logger.Error(ctx, "Failed to get subscription", err, map[string]interface{}{
			"subscription_id": subscriptionID,
		})
		return fmt.Errorf("获取订阅失败: %w", err)
	}

	// 2. 暂停订阅
	if err := subscription.Suspend(reason); err != nil {
		return fmt.Errorf("暂停订阅失败: %w", err)
	}

	// 3. 保存更新
	if err := h.subscriptionRepo.Update(ctx, subscription); err != nil {
		h.logger.Error(ctx, "Failed to suspend subscription", err, map[string]interface{}{
			"subscription_id": subscriptionID,
			"reason":          reason,
		})
		return fmt.Errorf("保存订阅暂停失败: %w", err)
	}

	h.logger.Info(ctx, "Subscription suspended successfully", map[string]interface{}{
		"subscription_id": subscriptionID,
		"reason":          reason,
	})

	return nil
}
