package handler

import (
	"context"
	"fmt"
	"time"

	"backend/internal/application/command/model"
	"backend/internal/application/service"
	"backend/internal/domain/auth/entity"
	"backend/internal/domain/auth/repository"
	userRepo "backend/internal/domain/user/repository"
	"backend/pkg/infrastructure/auth"
	"backend/pkg/infrastructure/logger"
	"backend/pkg/infrastructure/snowflake"
)

// AuthCommandHandler 安全命令处理器
type AuthCommandHandler struct {
	userRepo       userRepo.UserRepository
	userAuthRepo   repository.UserAuthRepository
	userTenantRepo userRepo.UserTenantRepository
	sessionRepo    repository.SessionRepository
	tokenCache     repository.TokenCacheRepository
	jwtManager     auth.JWTManager
	casbinManager  auth.CasbinManager
	routeTreeSvc   service.RouteTreeService
	logger         logger.Logger
	snowflake      snowflake.Generator
}

// NewAuthCommandHandler 创建安全命令处理器
func NewAuthCommandHandler(
	userRepo userRepo.UserRepository,
	userAuthRepo repository.UserAuthRepository,
	userTenantRepo userRepo.UserTenantRepository,
	sessionRepo repository.SessionRepository,
	tokenCache repository.TokenCacheRepository,
	jwtManager auth.JWTManager,
	casbinManager auth.CasbinManager,
	routeTreeSvc service.RouteTreeService,
	logger logger.Logger,
	snowflake snowflake.Generator,
) *AuthCommandHandler {
	return &AuthCommandHandler{
		userRepo:       userRepo,
		userAuthRepo:   userAuthRepo,
		userTenantRepo: userTenantRepo,
		sessionRepo:    sessionRepo,
		tokenCache:     tokenCache,
		jwtManager:     jwtManager,
		casbinManager:  casbinManager,
		routeTreeSvc:   routeTreeSvc,
		logger:         logger,
		snowflake:      snowflake,
	}
}

// ==================== 认证命令处理 ====================

// Login 处理登录命令
func (h *AuthCommandHandler) Login(ctx context.Context, cmd *model.LoginCommand) (*model.LoginResult, error) {
	h.logger.Info(ctx, "Processing Login command", map[string]interface{}{
		"identity_type": cmd.IdentityType,
		"identifier":    cmd.Identifier,
	})

	// 1. 验证命令
	if err := cmd.Validate(); err != nil {
		h.logger.Error(ctx, "Invalid Login command", err, map[string]interface{}{
			"command": cmd,
		})
		return nil, fmt.Errorf("无效的登录命令: %w", err)
	}

	// 2. 查找认证信息
	userAuth, err := h.userAuthRepo.FindByIdentifier(ctx, cmd.IdentityType, cmd.Identifier)
	if err != nil {
		h.logger.Warn(ctx, "Authentication failed: identifier not found", "identifier", cmd.Identifier)
		return nil, fmt.Errorf("用户名或密码错误")
	}

	// 3. 验证凭证
	if !userAuth.CheckCredential(cmd.Credential) {
		h.logger.Warn(ctx, "Authentication failed: invalid credential", "identifier", cmd.Identifier)
		return nil, fmt.Errorf("用户名或密码错误")
	}

	// 4. 检查关联的用户账户状态
	user, err := h.userRepo.FindByBusinessID(ctx, userAuth.UserBusinessID)
	if err != nil {
		h.logger.Error(ctx, "Failed to find user profile after auth", "user_business_id", userAuth.UserBusinessID, "error", err)
		return nil, fmt.Errorf("用户不存在")
	}
	if !user.CanLogin() {
		return nil, fmt.Errorf("账户已被禁用")
	}

	// 5. 获取用户有权访问的租户列表
	tenants, err := h.userTenantRepo.FindByUserBusinessID(ctx, user.BusinessID)
	if err != nil {
		h.logger.Error(ctx, "Failed to get user tenants", "user_id", user.BusinessID, "error", err)
		return nil, fmt.Errorf("获取租户列表失败")
	}

	// 6. 构建租户信息
	tenantList := make([]model.Tenant, 0, len(tenants))
	for _, tenant := range tenants {
		// 获取用户在该租户下的权限
		roles, _ := h.casbinManager.GetRolesForUser(ctx, user.BusinessID, tenant.TenantBusinessID)
		permissions, _ := h.casbinManager.GetPermissionsForUser(ctx, user.BusinessID, tenant.TenantBusinessID)

		// 转换权限格式
		permissionList := make([]string, 0)
		for _, perm := range permissions {
			if len(perm) > 0 {
				permissionList = append(permissionList, perm[0])
			}
		}

		// 获取角色名称
		roleName := tenant.RoleBusinessID
		if len(roles) > 0 {
			roleName = roles[0]
		}

		tenantList = append(tenantList, model.Tenant{
			TenantID:    tenant.TenantBusinessID,
			TenantName:  "租户", // TODO: 需要从租户仓储获取实际名称
			Domain:      "",   // TODO: 需要从租户仓储获取实际域名
			Role:        roleName,
			Permissions: permissionList,
		})
	}

	h.logger.Info(ctx, "User authenticated successfully", map[string]interface{}{
		"user_id":      user.BusinessID,
		"tenant_count": len(tenantList),
	})

	// 7. 撤销该用户之前的预登录token（确保唯一性）
	if err := h.tokenCache.RevokeUserPreAuthTokens(ctx, user.BusinessID); err != nil {
		h.logger.Warn(ctx, "Failed to revoke previous pre-auth tokens", "user_id", user.BusinessID, "error", err)
	}

	// 8. 生成预登录token
	preAuthToken, preAuthExpiresAt, err := h.jwtManager.GeneratePreAuthToken(user.BusinessID)
	if err != nil {
		h.logger.Error(ctx, "Failed to generate pre-auth token", "user_id", user.BusinessID, "error", err)
		return nil, fmt.Errorf("生成预登录令牌失败")
	}

	// 9. 保存预登录token信息到缓存
	preAuthJTI, _ := h.jwtManager.ExtractJTI(preAuthToken)
	preAuthTokenInfo := &repository.TokenInfo{
		JTI:       preAuthJTI,
		Token:     preAuthToken,
		UserID:    user.BusinessID,
		TenantID:  "", // 预登录token没有租户ID
		DeviceID:  cmd.DeviceID,
		TokenType: "pre-auth",
		CreatedAt: time.Now(),
		ExpiresAt: preAuthExpiresAt,
		IPAddress: "", // 这里可以从context中获取IP地址
		UserAgent: "", // 这里可以从context中获取User-Agent
		IsUsed:    false,
	}
	if err := h.tokenCache.SaveTokenInfo(ctx, preAuthTokenInfo); err != nil {
		h.logger.Error(ctx, "Failed to store pre-auth token info", "jti", preAuthJTI, "error", err)
		return nil, fmt.Errorf("保存预登录令牌失败")
	}

	// 10. 返回登录结果
	return &model.LoginResult{
		UserID:           user.BusinessID,
		Username:         user.Username,
		Email:            user.Email,
		Phone:            user.Phone,
		DisplayName:      user.Profile.GetDisplayName(),
		Avatar:           user.Profile.Avatar,
		Tenants:          tenantList,
		RequiresMFA:      false, // TODO: 根据安全策略确定是否需要MFA
		LoginTime:        time.Now(),
		PreAuthToken:     preAuthToken,
		PreAuthExpiresAt: preAuthExpiresAt,
	}, nil
}

// SelectTenant 处理选择租户命令
func (h *AuthCommandHandler) SelectTenant(ctx context.Context, cmd *model.SelectTenantCommand) (*model.AuthResult, error) {
	h.logger.Info(ctx, "Processing SelectTenant command", map[string]interface{}{
		"user_id":   cmd.UserID,
		"tenant_id": cmd.TenantID,
	})

	// 1. 验证命令
	if err := cmd.Validate(); err != nil {
		return nil, fmt.Errorf("无效的选择租户命令: %w", err)
	}

	// 2. 验证预登录token的有效性和防重放攻击
	if cmd.PreAuthTokenJTI != "" {
		// 检查token是否已被使用
		isUsed, err := h.tokenCache.IsTokenUsed(ctx, cmd.PreAuthTokenJTI)
		if err != nil {
			h.logger.Error(ctx, "Failed to check token usage status", "jti", cmd.PreAuthTokenJTI, "error", err)
			return nil, fmt.Errorf("验证预登录令牌失败")
		}
		if isUsed {
			h.logger.Warn(ctx, "Pre-auth token already used - potential replay attack", "jti", cmd.PreAuthTokenJTI, "user_id", cmd.UserID)
			return nil, fmt.Errorf("预登录令牌已被使用，请重新登录")
		}

		// 验证设备和IP信息（防重放攻击）
		if err := h.tokenCache.ValidatePreAuthToken(ctx, cmd.PreAuthTokenJTI, cmd.DeviceID, ""); err != nil {
			h.logger.Warn(ctx, "Pre-auth token validation failed", "jti", cmd.PreAuthTokenJTI, "user_id", cmd.UserID, "error", err)
			return nil, fmt.Errorf("预登录令牌验证失败: %v", err)
		}

		// 立即标记token为已使用（防止重放攻击）
		if err := h.tokenCache.MarkTokenAsUsed(ctx, cmd.PreAuthTokenJTI); err != nil {
			h.logger.Error(ctx, "Failed to mark pre-auth token as used", "jti", cmd.PreAuthTokenJTI, "error", err)
			return nil, fmt.Errorf("标记预登录令牌失败")
		}

		h.logger.Info(ctx, "Pre-auth token validated and marked as used", "jti", cmd.PreAuthTokenJTI, "user_id", cmd.UserID)
	}

	// 3. 验证用户是否有权访问该租户
	userTenant, err := h.userTenantRepo.FindByUserAndTenant(ctx, cmd.UserID, cmd.TenantID)
	if err != nil {
		h.logger.Warn(ctx, "Tenant selection failed", "user_id", cmd.UserID, "tenant_id", cmd.TenantID, "error", err)
		return nil, fmt.Errorf("用户无权访问此租户")
	}

	// 4. 获取用户在该租户下的角色
	roles, err := h.casbinManager.GetRolesForUser(ctx, cmd.UserID, cmd.TenantID)
	if err != nil {
		h.logger.Error(ctx, "Failed to get roles from casbin", "user_id", cmd.UserID, "tenant_id", cmd.TenantID, "error", err)
		roles = []string{userTenant.RoleBusinessID}
	}

	// 5. 获取权限列表
	permissions, err := h.casbinManager.GetPermissionsForUser(ctx, cmd.UserID, cmd.TenantID)
	if err != nil {
		h.logger.Error(ctx, "Failed to get permissions", "user_id", cmd.UserID, "tenant_id", cmd.TenantID, "error", err)
		permissions = [][]string{}
	}

	// 转换权限格式
	permissionList := make([]string, 0)
	for _, perm := range permissions {
		if len(perm) > 0 {
			permissionList = append(permissionList, perm[0])
		}
	}

	// 6. 生成访问令牌
	accessToken, accessExpiresAt, err := h.jwtManager.GenerateAccessToken(cmd.UserID, cmd.TenantID, roles)
	if err != nil {
		h.logger.Error(ctx, "Failed to generate access token", "user_id", cmd.UserID, "error", err)
		return nil, fmt.Errorf("生成访问令牌失败")
	}

	// 7. 生成刷新令牌
	refreshToken, refreshExpiresAt, err := h.jwtManager.GenerateRefreshToken(cmd.UserID, cmd.TenantID)
	if err != nil {
		h.logger.Error(ctx, "Failed to generate refresh token", "user_id", cmd.UserID, "error", err)
		return nil, fmt.Errorf("生成刷新令牌失败")
	}

	// 8. 存储令牌信息到缓存
	accessJTI, _ := h.jwtManager.ExtractJTI(accessToken)
	refreshJTI, _ := h.jwtManager.ExtractJTI(refreshToken)

	// 保存访问令牌信息
	accessTokenInfo := &repository.TokenInfo{
		JTI:       accessJTI,
		Token:     accessToken,
		UserID:    cmd.UserID,
		TenantID:  cmd.TenantID,
		DeviceID:  cmd.DeviceID,
		TokenType: "access",
		CreatedAt: time.Now(),
		ExpiresAt: accessExpiresAt,
	}
	if err := h.tokenCache.SaveTokenInfo(ctx, accessTokenInfo); err != nil {
		h.logger.Error(ctx, "Failed to store access token info", "jti", accessJTI, "error", err)
	}

	// 保存刷新令牌信息
	refreshTokenInfo := &repository.TokenInfo{
		JTI:       refreshJTI,
		Token:     refreshToken,
		UserID:    cmd.UserID,
		TenantID:  cmd.TenantID,
		DeviceID:  cmd.DeviceID,
		TokenType: "refresh",
		CreatedAt: time.Now(),
		ExpiresAt: refreshExpiresAt,
	}
	if err := h.tokenCache.SaveTokenInfo(ctx, refreshTokenInfo); err != nil {
		h.logger.Error(ctx, "Failed to store refresh token info", "jti", refreshJTI, "error", err)
	}

	// 添加到用户令牌集合
	if err := h.tokenCache.AddUserToken(ctx, cmd.UserID, cmd.TenantID, accessJTI); err != nil {
		h.logger.Error(ctx, "Failed to add access token to user tokens", "user_id", cmd.UserID, "jti", accessJTI, "error", err)
	}
	if err := h.tokenCache.AddUserToken(ctx, cmd.UserID, cmd.TenantID, refreshJTI); err != nil {
		h.logger.Error(ctx, "Failed to add refresh token to user tokens", "user_id", cmd.UserID, "jti", refreshJTI, "error", err)
	}

	// 9. 删除预认证令牌（已标记为使用，现在可以删除）
	if cmd.PreAuthTokenJTI != "" {
		err = h.tokenCache.DeleteTokenInfo(ctx, cmd.PreAuthTokenJTI)
		if err != nil {
			h.logger.Warn(ctx, "Failed to delete pre-auth token from cache", "user_id", cmd.UserID, "tenant_id", cmd.TenantID, "jti", cmd.PreAuthTokenJTI, "error", err)
		} else {
			h.logger.Info(ctx, "Pre-auth token deleted successfully", "user_id", cmd.UserID, "tenant_id", cmd.TenantID, "jti", cmd.PreAuthTokenJTI)
		}
	}

	// 10. 生成用户路由树
	var routeTree []*model.RouteTreeNode
	if h.routeTreeSvc != nil {
		routeTreeResult, err := h.routeTreeSvc.GenerateUserRouteTree(ctx, cmd.UserID, cmd.TenantID)
		if err != nil {
			h.logger.Error(ctx, "Failed to generate user route tree", "user_id", cmd.UserID, "tenant_id", cmd.TenantID, "error", err)
			// 路由树生成失败不影响登录流程，继续执行
		} else {
			// 转换路由树格式
			routeTree = convertRouteTreeNodes(routeTreeResult.Routes)
			h.logger.Info(ctx, "User route tree generated successfully", map[string]interface{}{
				"user_id":     cmd.UserID,
				"tenant_id":   cmd.TenantID,
				"route_count": len(routeTree),
			})
		}
	}

	h.logger.Info(ctx, "Tenant selected successfully", map[string]interface{}{
		"user_id":   cmd.UserID,
		"tenant_id": cmd.TenantID,
		"roles":     roles,
	})

	// 11. 返回认证结果
	return &model.AuthResult{
		AccessToken:  accessToken,
		RefreshToken: refreshToken,
		TokenType:    "Bearer",
		ExpiresIn:    accessExpiresAt.Unix() - time.Now().Unix(),
		ExpiresAt:    accessExpiresAt,
		UserID:       cmd.UserID,
		TenantID:     cmd.TenantID,
		Roles:        roles,
		Permissions:  permissionList,
		RouteTree:    routeTree,
	}, nil
}

// Logout 处理登出命令
func (h *AuthCommandHandler) Logout(ctx context.Context, cmd *model.LogoutCommand) (*model.OperationResult, error) {
	h.logger.Info(ctx, "Processing Logout command", map[string]interface{}{
		"user_id":     cmd.UserID,
		"tenant_id":   cmd.TenantID,
		"logout_type": cmd.LogoutType,
	})

	// 1. 验证命令
	if err := cmd.Validate(); err != nil {
		return nil, fmt.Errorf("无效的登出命令: %w", err)
	}

	// 2. 根据登出类型处理
	switch cmd.LogoutType {
	case "current":
		// 只登出当前设备
		if cmd.DeviceID != "" {
			if err := h.tokenCache.RevokeDeviceTokens(ctx, cmd.DeviceID); err != nil {
				h.logger.Error(ctx, "Failed to revoke device tokens", "user_id", cmd.UserID, "device_id", cmd.DeviceID, "error", err)
				return nil, fmt.Errorf("登出失败")
			}
		} else {
			// 如果没有设备ID，则撤销该用户在该租户下的所有令牌
			if err := h.tokenCache.RevokeAllUserTokens(ctx, cmd.UserID, cmd.TenantID); err != nil {
				h.logger.Error(ctx, "Failed to revoke user tokens", "user_id", cmd.UserID, "tenant_id", cmd.TenantID, "error", err)
				return nil, fmt.Errorf("登出失败")
			}
		}
	case "all":
		// 登出所有设备 - 撤销该用户在该租户下的所有令牌
		if err := h.tokenCache.RevokeAllUserTokens(ctx, cmd.UserID, cmd.TenantID); err != nil {
			h.logger.Error(ctx, "Failed to revoke all user tokens", "user_id", cmd.UserID, "tenant_id", cmd.TenantID, "error", err)
			return nil, fmt.Errorf("登出失败")
		}
	}

	h.logger.Info(ctx, "User logged out successfully", map[string]interface{}{
		"user_id":     cmd.UserID,
		"logout_type": cmd.LogoutType,
	})

	// 3. 返回操作结果
	return &model.OperationResult{
		Success:   true,
		Message:   "登出成功",
		Timestamp: time.Now(),
	}, nil
}

// RefreshToken 处理刷新令牌命令
func (h *AuthCommandHandler) RefreshToken(ctx context.Context, cmd *model.RefreshTokenCommand) (*model.AuthResult, error) {
	h.logger.Info(ctx, "Processing RefreshToken command")

	// 1. 验证命令
	if err := cmd.Validate(); err != nil {
		return nil, fmt.Errorf("无效的刷新令牌命令: %w", err)
	}

	// 2. 验证刷新令牌
	claims, err := h.jwtManager.ValidateToken(cmd.RefreshToken)
	if err != nil {
		h.logger.Error(ctx, "Invalid refresh token", "error", err)
		return nil, fmt.Errorf("刷新令牌无效")
	}

	// 3. 检查令牌是否在黑名单中
	jti, _ := h.jwtManager.ExtractJTI(cmd.RefreshToken)
	isBlacklisted, err := h.tokenCache.IsTokenBlacklisted(ctx, jti)
	if err != nil {
		return nil, fmt.Errorf("检查令牌状态失败")
	}
	if isBlacklisted {
		return nil, fmt.Errorf("令牌已被撤销")
	}

	// 4. 获取用户信息
	user, err := h.userRepo.FindByBusinessID(ctx, claims.UserID)
	if err != nil {
		return nil, fmt.Errorf("用户不存在")
	}
	if !user.CanLogin() {
		return nil, fmt.Errorf("账户已被禁用")
	}

	// 5. 获取用户角色
	roles, err := h.casbinManager.GetRolesForUser(ctx, user.BusinessID, claims.TenantID)
	if err != nil {
		h.logger.Error(ctx, "Failed to get user roles", "user_id", user.BusinessID, "tenant_id", claims.TenantID, "error", err)
		roles = []string{}
	}

	// 6. 生成新的访问令牌
	accessToken, accessExpiresAt, err := h.jwtManager.GenerateAccessToken(user.BusinessID, claims.TenantID, roles)
	if err != nil {
		h.logger.Error(ctx, "Failed to generate new access token", "user_id", user.BusinessID, "error", err)
		return nil, fmt.Errorf("生成新访问令牌失败")
	}

	// 7. 存储新令牌
	newJTI, _ := h.jwtManager.ExtractJTI(accessToken)
	newTokenInfo := &repository.TokenInfo{
		JTI:       newJTI,
		Token:     accessToken,
		UserID:    user.BusinessID,
		TenantID:  claims.TenantID,
		TokenType: "access",
		CreatedAt: time.Now(),
		ExpiresAt: accessExpiresAt,
	}
	if err := h.tokenCache.SaveTokenInfo(ctx, newTokenInfo); err != nil {
		h.logger.Error(ctx, "Failed to store new token info", "jti", newJTI, "error", err)
	}

	// 添加到用户令牌集合
	if err := h.tokenCache.AddUserToken(ctx, user.BusinessID, claims.TenantID, newJTI); err != nil {
		h.logger.Error(ctx, "Failed to add new token to user tokens", "user_id", user.BusinessID, "jti", newJTI, "error", err)
	}

	// 8. 获取权限列表
	permissions, _ := h.casbinManager.GetPermissionsForUser(ctx, user.BusinessID, claims.TenantID)

	// 转换权限格式
	permissionList := make([]string, 0)
	for _, perm := range permissions {
		if len(perm) > 0 {
			permissionList = append(permissionList, perm[0])
		}
	}

	h.logger.Info(ctx, "Token refreshed successfully", map[string]interface{}{
		"user_id":   user.BusinessID,
		"tenant_id": claims.TenantID,
	})

	// 9. 返回新的认证结果
	return &model.AuthResult{
		AccessToken:  accessToken,
		RefreshToken: cmd.RefreshToken, // 刷新令牌保持不变
		TokenType:    "Bearer",
		ExpiresIn:    accessExpiresAt.Unix() - time.Now().Unix(),
		ExpiresAt:    accessExpiresAt,
		UserID:       user.BusinessID,
		TenantID:     claims.TenantID,
		Roles:        roles,
		Permissions:  permissionList,
	}, nil
}

// ==================== 密码管理命令处理 ====================

// ChangePassword 处理修改密码命令
func (h *AuthCommandHandler) ChangePassword(ctx context.Context, cmd *model.ChangePasswordCommand) (*model.OperationResult, error) {
	h.logger.Info(ctx, "Processing ChangePassword command", map[string]interface{}{
		"user_id": cmd.UserID,
	})

	// 1. 验证命令
	if err := cmd.Validate(); err != nil {
		return nil, fmt.Errorf("无效的修改密码命令: %w", err)
	}

	// 2. 查找用户认证信息
	userAuths, err := h.userAuthRepo.FindByUserBusinessID(ctx, cmd.UserID)
	if err != nil || len(userAuths) == 0 {
		h.logger.Error(ctx, "Failed to find user auth", "user_id", cmd.UserID, "error", err)
		return nil, fmt.Errorf("用户认证信息不存在")
	}

	// 3. 找到密码认证方式
	var passwordAuth *entity.UserAuth
	for _, auth := range userAuths {
		if auth.IdentityType == "username" || auth.IdentityType == "email" {
			passwordAuth = auth
			break
		}
	}
	if passwordAuth == nil {
		return nil, fmt.Errorf("未找到该用户的密码凭证")
	}

	// 4. 验证旧密码
	if !passwordAuth.CheckCredential(cmd.OldPassword) {
		h.logger.Warn(ctx, "Old password verification failed", "user_id", cmd.UserID)
		return nil, fmt.Errorf("旧密码错误")
	}

	// 5. 更新密码
	if err := passwordAuth.SetCredential(cmd.NewPassword); err != nil {
		h.logger.Error(ctx, "Failed to update password", "user_id", cmd.UserID, "error", err)
		return nil, fmt.Errorf("密码更新失败")
	}

	// 6. 保存更新
	if err := h.userAuthRepo.Update(ctx, passwordAuth); err != nil {
		h.logger.Error(ctx, "Failed to save password update", "user_id", cmd.UserID, "error", err)
		return nil, fmt.Errorf("保存密码失败")
	}

	// 7. 撤销所有现有令牌（强制重新登录）
	// 注意：这里需要遍历所有租户来撤销令牌
	tenants, err := h.userTenantRepo.FindByUserBusinessID(ctx, cmd.UserID)
	if err == nil {
		for _, tenant := range tenants {
			if err := h.tokenCache.RevokeAllUserTokens(ctx, cmd.UserID, tenant.TenantBusinessID); err != nil {
				h.logger.Error(ctx, "Failed to revoke user tokens after password change", "user_id", cmd.UserID, "tenant_id", tenant.TenantBusinessID, "error", err)
			}
		}
	}

	h.logger.Info(ctx, "Password changed successfully", map[string]interface{}{
		"user_id": cmd.UserID,
	})

	return &model.OperationResult{
		Success:   true,
		Message:   "密码修改成功",
		Timestamp: time.Now(),
	}, nil
}

// ResetPassword 处理重置密码命令
func (h *AuthCommandHandler) ResetPassword(ctx context.Context, cmd *model.ResetPasswordCommand) (*model.OperationResult, error) {
	h.logger.Info(ctx, "Processing ResetPassword command", map[string]interface{}{
		"identity_type": cmd.IdentityType,
		"identifier":    cmd.Identifier,
	})

	// 1. 验证命令
	if err := cmd.Validate(); err != nil {
		return nil, fmt.Errorf("无效的重置密码命令: %w", err)
	}

	// TODO: 验证验证码
	// 这里应该验证短信或邮件验证码的有效性
	// if !h.verifyCode(ctx, cmd.IdentityType, cmd.Identifier, cmd.VerifyCode) {
	//     return nil, fmt.Errorf("验证码无效或已过期")
	// }

	// 2. 查找用户认证信息
	userAuth, err := h.userAuthRepo.FindByIdentifier(ctx, cmd.IdentityType, cmd.Identifier)
	if err != nil {
		h.logger.Error(ctx, "Failed to find user auth for reset", "identifier", cmd.Identifier, "error", err)
		return nil, fmt.Errorf("用户不存在")
	}

	// 3. 更新密码
	if err := userAuth.SetCredential(cmd.NewPassword); err != nil {
		h.logger.Error(ctx, "Failed to reset password", "identifier", cmd.Identifier, "error", err)
		return nil, fmt.Errorf("密码重置失败")
	}

	// 4. 保存更新
	if err := h.userAuthRepo.Update(ctx, userAuth); err != nil {
		h.logger.Error(ctx, "Failed to save password reset", "identifier", cmd.Identifier, "error", err)
		return nil, fmt.Errorf("保存密码失败")
	}

	// 5. 撤销所有现有令牌 - 需要遍历所有租户
	tenants, err := h.userTenantRepo.FindByUserBusinessID(ctx, userAuth.UserBusinessID)
	if err == nil {
		for _, tenant := range tenants {
			if err := h.tokenCache.RevokeAllUserTokens(ctx, userAuth.UserBusinessID, tenant.TenantBusinessID); err != nil {
				h.logger.Error(ctx, "Failed to revoke user tokens after password reset", "user_id", userAuth.UserBusinessID, "tenant_id", tenant.TenantBusinessID, "error", err)
			}
		}
	}

	h.logger.Info(ctx, "Password reset successfully", map[string]interface{}{
		"user_id": userAuth.UserBusinessID,
	})

	return &model.OperationResult{
		Success:   true,
		Message:   "密码重置成功",
		Timestamp: time.Now(),
	}, nil
}

// ==================== 角色权限管理命令处理 ====================

// AssignRole 处理分配角色命令
func (h *AuthCommandHandler) AssignRole(ctx context.Context, cmd *model.AssignRoleCommand) (*model.OperationResult, error) {
	h.logger.Info(ctx, "Processing AssignRole command", map[string]interface{}{
		"user_id":   cmd.UserID,
		"tenant_id": cmd.TenantID,
		"role_id":   cmd.RoleID,
	})

	// 1. 验证命令
	if err := cmd.Validate(); err != nil {
		return nil, fmt.Errorf("无效的分配角色命令: %w", err)
	}

	// 2. 检查用户是否存在
	_, err := h.userRepo.FindByBusinessID(ctx, cmd.UserID)
	if err != nil {
		return nil, fmt.Errorf("用户不存在")
	}

	// 3. 分配角色
	if err := h.casbinManager.AddRoleForUser(ctx, cmd.UserID, cmd.RoleID, cmd.TenantID); err != nil {
		h.logger.Error(ctx, "Failed to assign role", "user_id", cmd.UserID, "role_id", cmd.RoleID, "error", err)
		return nil, fmt.Errorf("分配角色失败")
	}

	h.logger.Info(ctx, "Role assigned successfully", map[string]interface{}{
		"user_id":   cmd.UserID,
		"tenant_id": cmd.TenantID,
		"role_id":   cmd.RoleID,
	})

	return &model.OperationResult{
		Success: true,
		Message: "角色分配成功",
		Data: map[string]interface{}{
			"user_id":   cmd.UserID,
			"tenant_id": cmd.TenantID,
			"role_id":   cmd.RoleID,
		},
		Timestamp: time.Now(),
	}, nil
}

// RemoveRole 处理移除角色命令
func (h *AuthCommandHandler) RemoveRole(ctx context.Context, cmd *model.RemoveRoleCommand) (*model.OperationResult, error) {
	h.logger.Info(ctx, "Processing RemoveRole command", map[string]interface{}{
		"user_id":   cmd.UserID,
		"tenant_id": cmd.TenantID,
		"role_id":   cmd.RoleID,
	})

	// 1. 验证命令
	if err := cmd.Validate(); err != nil {
		return nil, fmt.Errorf("无效的移除角色命令: %w", err)
	}

	// 2. 移除角色
	if err := h.casbinManager.DeleteRoleForUser(ctx, cmd.UserID, cmd.RoleID, cmd.TenantID); err != nil {
		h.logger.Error(ctx, "Failed to remove role", "user_id", cmd.UserID, "role_id", cmd.RoleID, "error", err)
		return nil, fmt.Errorf("移除角色失败")
	}

	h.logger.Info(ctx, "Role removed successfully", map[string]interface{}{
		"user_id":   cmd.UserID,
		"tenant_id": cmd.TenantID,
		"role_id":   cmd.RoleID,
	})

	return &model.OperationResult{
		Success: true,
		Message: "角色移除成功",
		Data: map[string]interface{}{
			"user_id":   cmd.UserID,
			"tenant_id": cmd.TenantID,
			"role_id":   cmd.RoleID,
		},
		Timestamp: time.Now(),
	}, nil
}

// convertRouteTreeNodes 转换路由树节点格式
func convertRouteTreeNodes(serviceNodes []*service.RouteTreeNode) []*model.RouteTreeNode {
	if serviceNodes == nil {
		return nil
	}

	modelNodes := make([]*model.RouteTreeNode, len(serviceNodes))
	for i, serviceNode := range serviceNodes {
		modelNodes[i] = convertRouteTreeNode(serviceNode)
	}
	return modelNodes
}

// convertRouteTreeNode 转换单个路由树节点
func convertRouteTreeNode(serviceNode *service.RouteTreeNode) *model.RouteTreeNode {
	if serviceNode == nil {
		return nil
	}

	modelNode := &model.RouteTreeNode{
		ID:        serviceNode.ID,
		Name:      serviceNode.Name,
		Path:      serviceNode.Path,
		Component: serviceNode.Component,
		Redirect:  serviceNode.Redirect,
		Meta: model.RouteNodeMeta{
			Title:       serviceNode.Meta.Title,
			Icon:        serviceNode.Meta.Icon,
			Roles:       serviceNode.Meta.Roles,
			Permissions: serviceNode.Meta.Permissions,
			NoCache:     serviceNode.Meta.NoCache,
			Breadcrumb:  serviceNode.Meta.Breadcrumb,
			ActiveMenu:  serviceNode.Meta.ActiveMenu,
			Hidden:      serviceNode.Meta.Hidden,
			KeepAlive:   serviceNode.Meta.KeepAlive,
			IsExternal:  serviceNode.Meta.IsExternal,
			Badge:       serviceNode.Meta.Badge,
			BadgeType:   serviceNode.Meta.BadgeType,
			Target:      serviceNode.Meta.Target,
		},
	}

	// 递归转换子节点
	if serviceNode.Children != nil {
		modelNode.Children = convertRouteTreeNodes(serviceNode.Children)
	}

	return modelNode
}
