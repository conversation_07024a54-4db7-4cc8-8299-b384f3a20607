package handler

import (
	"context"
	"fmt"
	"time"

	"backend/internal/application/command/model"
	"backend/internal/domain/tenant/repository"
	"backend/internal/domain/tenant/service"
	"backend/internal/domain/tenant/valueobject"
	"backend/pkg/infrastructure/logger"
	"backend/pkg/infrastructure/snowflake"
)

// TenantCommandHandler 租户命令处理器
type TenantCommandHandler struct {
	tenantRepo       repository.TenantRepository
	quotaRepo        repository.TenantQuotaRepository
	subscriptionRepo repository.TenantSubscriptionRepository
	tenantService    service.TenantService
	logger           logger.Logger
	snowflake        snowflake.Generator
}

// NewTenantCommandHandler 创建租户命令处理器
func NewTenantCommandHandler(
	tenantRepo repository.TenantRepository,
	quotaRepo repository.TenantQuotaRepository,
	subscriptionRepo repository.TenantSubscriptionRepository,
	tenantService service.TenantService,
	logger logger.Logger,
	snowflake snowflake.Generator,
) *TenantCommandHandler {
	return &TenantCommandHandler{
		tenantRepo:       tenantRepo,
		quotaRepo:        quotaRepo,
		subscriptionRepo: subscriptionRepo,
		tenantService:    tenantService,
		logger:           logger,
		snowflake:        snowflake,
	}
}

// CreateTenant 处理创建租户命令
func (h *TenantCommandHandler) CreateTenant(ctx context.Context, cmd *model.CreateTenantCommand) (*model.CreateTenantResult, error) {
	h.logger.Info(ctx, "Processing CreateTenant command", map[string]interface{}{
		"name":   cmd.Name,
		"domain": cmd.Domain,
		"type":   cmd.Type,
	})

	// 1. 验证命令
	if err := cmd.Validate(); err != nil {
		h.logger.Error(ctx, "Invalid CreateTenant command", err, map[string]interface{}{
			"command": cmd,
		})
		return nil, fmt.Errorf("无效的创建租户命令: %w", err)
	}

	// 2. 检查域名是否已存在
	domainExists, err := h.tenantRepo.ExistsByDomain(ctx, cmd.Domain)
	if err != nil {
		return nil, fmt.Errorf("检查域名是否存在失败: %w", err)
	}
	if domainExists {
		return nil, fmt.Errorf("域名 %s 已被占用", cmd.Domain)
	}

	// 3. 转换租户类型
	var tenantType valueobject.TenantType
	switch cmd.Type {
	case "system":
		tenantType = valueobject.TenantTypeSystem
	case "enterprise":
		tenantType = valueobject.TenantTypeEnterprise
	case "professional":
		tenantType = valueobject.TenantTypeProfessional
	case "basic":
		tenantType = valueobject.TenantTypeBasic
	case "trial":
		tenantType = valueobject.TenantTypeTrial
	default:
		return nil, fmt.Errorf("无效的租户类型: %s", cmd.Type)
	}

	// 4. 使用领域服务创建租户
	createReq := service.CreateTenantRequest{
		Name:         cmd.Name,
		Domain:       cmd.Domain,
		DisplayName:  cmd.DisplayName,
		Description:  cmd.Description,
		Type:         tenantType,
		Industry:     cmd.Industry,
		Country:      cmd.Country,
		Province:     cmd.Province,
		City:         cmd.City,
		Address:      cmd.Address,
		ContactEmail: cmd.ContactEmail,
		ContactPhone: cmd.ContactPhone,
	}

	tenant, err := h.tenantService.CreateTenant(ctx, createReq)
	if err != nil {
		h.logger.Error(ctx, "Failed to create tenant", err, map[string]interface{}{
			"request": createReq,
		})
		return nil, fmt.Errorf("创建租户失败: %w", err)
	}

	h.logger.Info(ctx, "Tenant created successfully", map[string]interface{}{
		"tenant_id": tenant.BusinessID,
		"name":      tenant.Name,
		"domain":    tenant.Domain,
	})

	// 5. 返回结果
	return &model.CreateTenantResult{
		TenantID:  tenant.BusinessID,
		Name:      tenant.Name,
		Domain:    tenant.Domain,
		Type:      tenant.Type.String(),
		Status:    tenant.Status.String(),
		CreatedAt: tenant.CreatedAt,
	}, nil
}

// UpdateTenant 处理更新租户命令
func (h *TenantCommandHandler) UpdateTenant(ctx context.Context, cmd *model.UpdateTenantCommand) (*model.UpdateTenantResult, error) {
	h.logger.Info(ctx, "Processing UpdateTenant command", map[string]interface{}{
		"tenant_id": cmd.TenantID,
	})

	// 1. 验证命令
	if err := cmd.Validate(); err != nil {
		h.logger.Error(ctx, "Invalid UpdateTenant command", err, map[string]interface{}{
			"command": cmd,
		})
		return nil, fmt.Errorf("无效的更新租户命令: %w", err)
	}

	// 2. 获取租户
	tenant, err := h.tenantRepo.GetByID(ctx, cmd.TenantID)
	if err != nil {
		return nil, fmt.Errorf("获取租户失败: %w", err)
	}

	// 3. 更新基本信息
	if cmd.BasicInfo != nil {
		if cmd.BasicInfo.Name != "" {
			tenant.Name = cmd.BasicInfo.Name
		}
		if cmd.BasicInfo.DisplayName != "" {
			tenant.DisplayName = cmd.BasicInfo.DisplayName
		}
		if cmd.BasicInfo.Description != "" {
			tenant.Description = cmd.BasicInfo.Description
		}
		if cmd.BasicInfo.Industry != "" {
			tenant.Industry = cmd.BasicInfo.Industry
		}
	}

	// 4. 更新联系信息
	if cmd.ContactInfo != nil {
		if err := tenant.UpdateContactInfo(cmd.ContactInfo.ContactEmail, cmd.ContactInfo.ContactPhone); err != nil {
			return nil, fmt.Errorf("更新联系信息失败: %w", err)
		}
	}

	// 5. 更新地址信息
	if cmd.AddressInfo != nil {
		if cmd.AddressInfo.Country != "" {
			tenant.Country = cmd.AddressInfo.Country
		}
		if cmd.AddressInfo.Province != "" {
			tenant.Province = cmd.AddressInfo.Province
		}
		if cmd.AddressInfo.City != "" {
			tenant.City = cmd.AddressInfo.City
		}
		if cmd.AddressInfo.Address != "" {
			tenant.Address = cmd.AddressInfo.Address
		}
	}

	// 6. 更新业务信息
	if cmd.BusinessInfo != nil {
		// 这里可以扩展业务信息的更新逻辑
		// 目前租户实体中没有这些字段，可以通过Settings存储
		if tenant.Settings == nil {
			tenant.Settings = make(map[string]interface{})
		}
		if cmd.BusinessInfo.BusinessLicense != "" {
			tenant.Settings["business_license"] = cmd.BusinessInfo.BusinessLicense
		}
		if cmd.BusinessInfo.TaxID != "" {
			tenant.Settings["tax_id"] = cmd.BusinessInfo.TaxID
		}
	}

	// 7. 更新设置
	if cmd.Settings != nil {
		if tenant.Settings == nil {
			tenant.Settings = make(map[string]interface{})
		}
		for key, value := range cmd.Settings {
			tenant.Settings[key] = value
		}
	}

	// 8. 保存更新
	if err := h.tenantRepo.Update(ctx, tenant); err != nil {
		h.logger.Error(ctx, "Failed to update tenant", err, map[string]interface{}{
			"tenant_id": cmd.TenantID,
		})
		return nil, fmt.Errorf("更新租户失败: %w", err)
	}

	h.logger.Info(ctx, "Tenant updated successfully", map[string]interface{}{
		"tenant_id": tenant.BusinessID,
		"name":      tenant.Name,
	})

	// 9. 返回结果
	return &model.UpdateTenantResult{
		TenantID:  tenant.BusinessID,
		Name:      tenant.Name,
		Domain:    tenant.Domain,
		Status:    tenant.Status.String(),
		Version:   tenant.Version,
		UpdatedAt: tenant.UpdatedAt,
	}, nil
}

// ActivateTenant 处理激活租户命令
func (h *TenantCommandHandler) ActivateTenant(ctx context.Context, cmd *model.ActivateTenantCommand) (*model.ActivateTenantResult, error) {
	h.logger.Info(ctx, "Processing ActivateTenant command", map[string]interface{}{
		"tenant_id": cmd.TenantID,
		"reason":    cmd.Reason,
	})

	// 1. 验证命令
	if err := cmd.Validate(); err != nil {
		h.logger.Error(ctx, "Invalid ActivateTenant command", err, map[string]interface{}{
			"command": cmd,
		})
		return nil, fmt.Errorf("无效的激活租户命令: %w", err)
	}

	// 2. 使用领域服务激活租户
	if err := h.tenantService.ActivateTenant(ctx, cmd.TenantID); err != nil {
		h.logger.Error(ctx, "Failed to activate tenant", err, map[string]interface{}{
			"tenant_id": cmd.TenantID,
		})
		return nil, fmt.Errorf("激活租户失败: %w", err)
	}

	h.logger.Info(ctx, "Tenant activated successfully", map[string]interface{}{
		"tenant_id": cmd.TenantID,
	})

	// 3. 返回结果
	return &model.ActivateTenantResult{
		TenantID:    cmd.TenantID,
		Status:      valueobject.TenantStatusActive.String(),
		ActivatedAt: time.Now(),
	}, nil
}

// SuspendTenant 处理暂停租户命令
func (h *TenantCommandHandler) SuspendTenant(ctx context.Context, cmd *model.SuspendTenantCommand) (*model.SuspendTenantResult, error) {
	h.logger.Info(ctx, "Processing SuspendTenant command", map[string]interface{}{
		"tenant_id": cmd.TenantID,
		"reason":    cmd.Reason,
	})

	// 1. 验证命令
	if err := cmd.Validate(); err != nil {
		h.logger.Error(ctx, "Invalid SuspendTenant command", err, map[string]interface{}{
			"command": cmd,
		})
		return nil, fmt.Errorf("无效的暂停租户命令: %w", err)
	}

	// 2. 使用领域服务暂停租户
	if err := h.tenantService.SuspendTenant(ctx, cmd.TenantID, cmd.Reason); err != nil {
		h.logger.Error(ctx, "Failed to suspend tenant", err, map[string]interface{}{
			"tenant_id": cmd.TenantID,
			"reason":    cmd.Reason,
		})
		return nil, fmt.Errorf("暂停租户失败: %w", err)
	}

	h.logger.Info(ctx, "Tenant suspended successfully", map[string]interface{}{
		"tenant_id": cmd.TenantID,
		"reason":    cmd.Reason,
	})

	// 3. 返回结果
	return &model.SuspendTenantResult{
		TenantID:    cmd.TenantID,
		Status:      valueobject.TenantStatusSuspended.String(),
		Reason:      cmd.Reason,
		SuspendedAt: time.Now(),
	}, nil
}

// TerminateTenant 处理终止租户命令
func (h *TenantCommandHandler) TerminateTenant(ctx context.Context, cmd *model.TerminateTenantCommand) (*model.TerminateTenantResult, error) {
	h.logger.Info(ctx, "Processing TerminateTenant command", map[string]interface{}{
		"tenant_id": cmd.TenantID,
		"reason":    cmd.Reason,
	})

	// 1. 验证命令
	if err := cmd.Validate(); err != nil {
		h.logger.Error(ctx, "Invalid TerminateTenant command", err, map[string]interface{}{
			"command": cmd,
		})
		return nil, fmt.Errorf("无效的终止租户命令: %w", err)
	}

	// 2. 使用领域服务终止租户
	if err := h.tenantService.TerminateTenant(ctx, cmd.TenantID, cmd.Reason); err != nil {
		h.logger.Error(ctx, "Failed to terminate tenant", err, map[string]interface{}{
			"tenant_id": cmd.TenantID,
			"reason":    cmd.Reason,
		})
		return nil, fmt.Errorf("终止租户失败: %w", err)
	}

	h.logger.Info(ctx, "Tenant terminated successfully", map[string]interface{}{
		"tenant_id": cmd.TenantID,
		"reason":    cmd.Reason,
	})

	// 3. 返回结果
	return &model.TerminateTenantResult{
		TenantID:     cmd.TenantID,
		Status:       valueobject.TenantStatusTerminated.String(),
		Reason:       cmd.Reason,
		TerminatedAt: time.Now(),
	}, nil
}

// DeleteTenant 处理删除租户命令（软删除）
func (h *TenantCommandHandler) DeleteTenant(ctx context.Context, cmd *model.DeleteTenantCommand) (*model.DeleteTenantResult, error) {
	h.logger.Info(ctx, "Processing DeleteTenant command", map[string]interface{}{
		"tenant_id": cmd.TenantID,
		"reason":    cmd.Reason,
	})

	// 1. 验证命令
	if err := cmd.Validate(); err != nil {
		h.logger.Error(ctx, "Invalid DeleteTenant command", err, map[string]interface{}{
			"command": cmd,
		})
		return nil, fmt.Errorf("无效的删除租户命令: %w", err)
	}

	// 2. 获取租户
	tenant, err := h.tenantRepo.GetByID(ctx, cmd.TenantID)
	if err != nil {
		return nil, fmt.Errorf("获取租户失败: %w", err)
	}

	// 3. 执行软删除（使用Terminate方法）
	if err := tenant.Terminate(cmd.Reason); err != nil {
		return nil, fmt.Errorf("删除租户失败: %w", err)
	}

	// 4. 保存更新
	if err := h.tenantRepo.Update(ctx, tenant); err != nil {
		h.logger.Error(ctx, "Failed to delete tenant", err, map[string]interface{}{
			"tenant_id": cmd.TenantID,
		})
		return nil, fmt.Errorf("删除租户失败: %w", err)
	}

	h.logger.Info(ctx, "Tenant deleted successfully", map[string]interface{}{
		"tenant_id": cmd.TenantID,
		"reason":    cmd.Reason,
	})

	// 5. 返回结果
	return &model.DeleteTenantResult{
		TenantID:  cmd.TenantID,
		DeletedAt: time.Now(),
	}, nil
}
