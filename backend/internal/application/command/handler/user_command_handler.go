package handler

import (
	"context"
	"fmt"

	"backend/internal/application/command/model"
	"backend/internal/domain/user/entity"
	"backend/internal/domain/user/repository"
	"backend/internal/domain/user/valueobject"
	"backend/pkg/infrastructure/logger"
	"backend/pkg/infrastructure/snowflake"
)

// UserCommandHandler 用户命令处理器
type UserCommandHandler struct {
	userRepo  repository.UserRepository
	logger    logger.Logger
	snowflake snowflake.Generator
}

// NewUserCommandHandler 创建用户命令处理器
func NewUserCommandHandler(
	userRepo repository.UserRepository,
	logger logger.Logger,
	snowflake snowflake.Generator,
) *UserCommandHandler {
	return &UserCommandHandler{
		userRepo:  userRepo,
		logger:    logger,
		snowflake: snowflake,
	}
}

// CreateUser 处理创建用户命令
func (h *UserCommandHandler) CreateUser(ctx context.Context, cmd *model.CreateUserCommand) (*model.CreateUserResult, error) {
	h.logger.Info(ctx, "Processing CreateUser command", map[string]interface{}{
		"username": cmd.Username,
	})

	// 1. 验证命令
	if err := cmd.Validate(); err != nil {
		h.logger.Error(ctx, "Invalid CreateUser command", err, map[string]interface{}{
			"command": cmd,
		})
		return nil, fmt.Errorf("无效的创建用户命令: %w", err)
	}

	// 2. 检查用户名、邮箱、手机号是否已存在
	emailExists, err := h.userRepo.ExistsByEmail(ctx, cmd.Email)
	if err != nil {
		return nil, fmt.Errorf("检查邮箱是否存在失败: %w", err)
	}
	if emailExists {
		return nil, fmt.Errorf("邮箱 %s 已被占用", cmd.Email)
	}

	phoneExists, err := h.userRepo.ExistsByPhone(ctx, cmd.Phone)
	if err != nil {
		return nil, fmt.Errorf("检查手机号是否存在失败: %w", err)
	}
	if phoneExists {
		return nil, fmt.Errorf("手机号 %s 已被占用", cmd.Phone)
	}

	// 3. 创建用户实体
	profile := valueobject.UserProfile{
		Nickname:  cmd.Nickname,
		FirstName: cmd.FirstName,
		LastName:  cmd.LastName,
		Avatar:    cmd.Avatar,
		Language:  cmd.Language,
		Timezone:  cmd.Timezone,
	}

	user, err := entity.NewUser(profile, cmd.Username, cmd.Email, cmd.Phone)
	if err != nil {
		h.logger.Error(ctx, "User validation failed", err, map[string]interface{}{"user": user})
		return nil, fmt.Errorf("用户验证失败: %w", err)
	}

	// 4. 保存用户
	if err := h.userRepo.Save(ctx, user); err != nil {
		h.logger.Error(ctx, "Failed to save user", err, map[string]interface{}{
			"user": user,
		})
		return nil, fmt.Errorf("保存用户失败: %w", err)
	}

	h.logger.Info(ctx, "User created successfully", map[string]interface{}{
		"user_id":  user.BusinessID,
		"username": user.Username,
	})

	// 5. 返回结果
	return &model.CreateUserResult{
		UserID:    user.BusinessID,
		Username:  user.Username,
		Email:     user.Email,
		Status:    user.Status.String(),
		CreatedAt: user.CreatedAt,
	}, nil
}

// UpdateUser 处理更新用户命令
func (h *UserCommandHandler) UpdateUser(ctx context.Context, cmd *model.UpdateUserCommand) (*model.UpdateUserResult, error) {
	h.logger.Info(ctx, "Processing UpdateUser command", map[string]interface{}{
		"user_id": cmd.UserID,
	})

	// 1. 验证命令
	if err := cmd.Validate(); err != nil {
		return nil, fmt.Errorf("无效的更新用户命令: %w", err)
	}

	// 2. 查找用户
	user, err := h.userRepo.FindByBusinessID(ctx, cmd.UserID)
	if err != nil {
		h.logger.Error(ctx, "Failed to find user", err, map[string]interface{}{
			"user_id": cmd.UserID,
		})
		return nil, fmt.Errorf("未找到用户: %w", err)
	}

	// 3. 更新用户信息
	newProfile := user.Profile
	if cmd.Profile != nil {
		newProfile.Nickname = cmd.Profile.Nickname
		newProfile.FirstName = cmd.Profile.FirstName
		newProfile.LastName = cmd.Profile.LastName
		newProfile.Avatar = cmd.Profile.Avatar
	}
	if cmd.Preferences != nil {
		newProfile.Language = cmd.Preferences.Language
		newProfile.Timezone = cmd.Preferences.Timezone
	}
	user.UpdateProfile(newProfile)

	if cmd.ContactInfo != nil {
		user.Email = cmd.ContactInfo.Email
		user.Phone = cmd.ContactInfo.Phone
	}

	// 4. 保存更新
	if err := h.userRepo.Update(ctx, user); err != nil {
		h.logger.Error(ctx, "Failed to update user", err, map[string]interface{}{
			"user_id": user.BusinessID,
		})
		return nil, fmt.Errorf("更新用户失败: %w", err)
	}

	h.logger.Info(ctx, "User updated successfully", map[string]interface{}{
		"user_id": user.BusinessID,
		"version": user.Version,
	})

	// 5. 返回结果
	return &model.UpdateUserResult{
		UserID:    user.BusinessID,
		Username:  user.Username,
		Email:     user.Email,
		Status:    user.Status.String(),
		Version:   user.Version,
		UpdatedAt: user.UpdatedAt,
	}, nil
}

// ActivateUser 处理激活用户命令
func (h *UserCommandHandler) ActivateUser(ctx context.Context, cmd *model.ActivateUserCommand) (*model.ActivateUserResult, error) {
	h.logger.Info(ctx, "Processing ActivateUser command", map[string]interface{}{
		"user_id": cmd.UserID,
	})

	// 1. 验证命令
	if err := cmd.Validate(); err != nil {
		return nil, fmt.Errorf("无效的激活用户命令: %w", err)
	}

	// 2. 查找用户
	user, err := h.userRepo.FindByBusinessID(ctx, cmd.UserID)
	if err != nil {
		return nil, fmt.Errorf("未找到用户: %w", err)
	}

	// 3. 激活用户
	if err := user.Activate(); err != nil {
		return nil, fmt.Errorf("激活用户失败: %w", err)
	}

	// 4. 保存更新
	if err := h.userRepo.Update(ctx, user); err != nil {
		h.logger.Error(ctx, "Failed to activate user", err, map[string]interface{}{
			"user_id": user.BusinessID,
		})
		return nil, fmt.Errorf("激活用户失败: %w", err)
	}

	h.logger.Info(ctx, "User activated successfully", map[string]interface{}{
		"user_id": user.BusinessID,
		"status":  user.Status.String(),
	})

	// 5. 返回结果
	return &model.ActivateUserResult{
		UserID:      user.BusinessID,
		Status:      user.Status.String(),
		ActivatedAt: user.UpdatedAt,
	}, nil
}

// DeactivateUser 处理停用用户命令
func (h *UserCommandHandler) DeactivateUser(ctx context.Context, cmd *model.DeactivateUserCommand) (*model.DeactivateUserResult, error) {
	h.logger.Info(ctx, "Processing DeactivateUser command", map[string]interface{}{
		"user_id": cmd.UserID,
	})

	// 1. 验证命令
	if err := cmd.Validate(); err != nil {
		return nil, fmt.Errorf("无效的停用用户命令: %w", err)
	}

	// 2. 查找用户
	user, err := h.userRepo.FindByBusinessID(ctx, cmd.UserID)
	if err != nil {
		return nil, fmt.Errorf("未找到用户: %w", err)
	}

	// 3. 停用用户
	if err := user.Deactivate(); err != nil {
		return nil, fmt.Errorf("停用用户失败: %w", err)
	}

	// 4. 保存更新
	if err := h.userRepo.Update(ctx, user); err != nil {
		h.logger.Error(ctx, "Failed to deactivate user", err, map[string]interface{}{
			"user_id": user.BusinessID,
		})
		return nil, fmt.Errorf("停用用户失败: %w", err)
	}

	h.logger.Info(ctx, "User deactivated successfully", map[string]interface{}{
		"user_id": user.BusinessID,
		"status":  user.Status.String(),
	})

	// 5. 返回结果
	return &model.DeactivateUserResult{
		UserID:        user.BusinessID,
		Status:        user.Status.String(),
		DeactivatedAt: user.UpdatedAt,
	}, nil
}
