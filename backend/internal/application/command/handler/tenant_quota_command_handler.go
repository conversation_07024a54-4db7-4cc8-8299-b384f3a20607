package handler

import (
	"context"
	"fmt"
	"time"

	"backend/internal/application/command/model"
	"backend/internal/domain/tenant/repository"
	"backend/internal/domain/tenant/service"
	"backend/pkg/infrastructure/logger"
)

// TenantQuotaCommandHandler 租户配额命令处理器
type TenantQuotaCommandHandler struct {
	tenantRepo    repository.TenantRepository
	quotaRepo     repository.TenantQuotaRepository
	tenantService service.TenantService
	logger        logger.Logger
}

// NewTenantQuotaCommandHandler 创建租户配额命令处理器
func NewTenantQuotaCommandHandler(
	tenantRepo repository.TenantRepository,
	quotaRepo repository.TenantQuotaRepository,
	tenantService service.TenantService,
	logger logger.Logger,
) *TenantQuotaCommandHandler {
	return &TenantQuotaCommandHandler{
		tenantRepo:    tenantRepo,
		quotaRepo:     quotaRepo,
		tenantService: tenantService,
		logger:        logger,
	}
}

// UpdateQuota 处理更新配额命令
func (h *TenantQuotaCommandHandler) UpdateQuota(ctx context.Context, cmd *model.UpdateQuotaCommand) (*model.UpdateQuotaResult, error) {
	h.logger.Info(ctx, "Processing UpdateQuota command", map[string]interface{}{
		"tenant_id": cmd.TenantID,
	})

	// 1. 验证命令
	if err := cmd.Validate(); err != nil {
		h.logger.Error(ctx, "Invalid UpdateQuota command", err, map[string]interface{}{
			"command": cmd,
		})
		return nil, fmt.Errorf("无效的更新配额命令: %w", err)
	}

	// 2. 获取租户配额
	quota, err := h.quotaRepo.GetByTenantID(ctx, cmd.TenantID)
	if err != nil {
		h.logger.Error(ctx, "Failed to get tenant quota", err, map[string]interface{}{
			"tenant_id": cmd.TenantID,
		})
		return nil, fmt.Errorf("获取租户配额失败: %w", err)
	}

	// 3. 更新配额限制
	userLimit := quota.UserLimit
	storageLimit := quota.StorageLimit
	apiLimit := quota.APILimitMonth
	productLimit := quota.ProductLimit
	orderLimit := quota.OrderLimitMonth
	fileUploadLimit := quota.FileUploadLimitMonth
	emailLimit := quota.EmailLimitMonth

	// 应用更新
	if cmd.UserLimit != nil {
		userLimit = *cmd.UserLimit
	}
	if cmd.StorageLimit != nil {
		storageLimit = *cmd.StorageLimit
	}
	if cmd.APILimit != nil {
		apiLimit = *cmd.APILimit
	}
	if cmd.ProductLimit != nil {
		productLimit = *cmd.ProductLimit
	}
	if cmd.OrderLimit != nil {
		orderLimit = *cmd.OrderLimit
	}
	if cmd.FileUploadLimit != nil {
		fileUploadLimit = *cmd.FileUploadLimit
	}
	if cmd.EmailLimit != nil {
		emailLimit = *cmd.EmailLimit
	}

	// 4. 更新配额实体
	if err := quota.UpdateLimits(userLimit, storageLimit, apiLimit, productLimit, orderLimit, fileUploadLimit, emailLimit); err != nil {
		return nil, fmt.Errorf("更新配额限制失败: %w", err)
	}

	// 5. 保存更新
	if err := h.quotaRepo.Update(ctx, quota); err != nil {
		h.logger.Error(ctx, "Failed to update quota", err, map[string]interface{}{
			"tenant_id": cmd.TenantID,
		})
		return nil, fmt.Errorf("保存配额更新失败: %w", err)
	}

	h.logger.Info(ctx, "Quota updated successfully", map[string]interface{}{
		"tenant_id": cmd.TenantID,
	})

	// 6. 返回结果
	return &model.UpdateQuotaResult{
		TenantID:  cmd.TenantID,
		UpdatedAt: time.Now(),
	}, nil
}

// ResetQuota 处理重置配额命令
func (h *TenantQuotaCommandHandler) ResetQuota(ctx context.Context, cmd *model.ResetQuotaCommand) (*model.ResetQuotaResult, error) {
	h.logger.Info(ctx, "Processing ResetQuota command", map[string]interface{}{
		"tenant_id":   cmd.TenantID,
		"quota_types": cmd.QuotaTypes,
	})

	// 1. 验证命令
	if err := cmd.Validate(); err != nil {
		h.logger.Error(ctx, "Invalid ResetQuota command", err, map[string]interface{}{
			"command": cmd,
		})
		return nil, fmt.Errorf("无效的重置配额命令: %w", err)
	}

	// 2. 获取租户配额
	quota, err := h.quotaRepo.GetByTenantID(ctx, cmd.TenantID)
	if err != nil {
		h.logger.Error(ctx, "Failed to get tenant quota", err, map[string]interface{}{
			"tenant_id": cmd.TenantID,
		})
		return nil, fmt.Errorf("获取租户配额失败: %w", err)
	}

	// 3. 重置指定类型的配额
	resetTypes := make([]string, 0)
	for _, quotaType := range cmd.QuotaTypes {
		switch quotaType {
		case "user":
			quota.UserUsed = 0
			resetTypes = append(resetTypes, "user")
		case "storage":
			quota.StorageUsed = 0
			resetTypes = append(resetTypes, "storage")
		case "api":
			quota.APIUsedMonth = 0
			resetTypes = append(resetTypes, "api")
		case "product":
			quota.ProductUsed = 0
			resetTypes = append(resetTypes, "product")
		case "order":
			quota.OrderUsedMonth = 0
			resetTypes = append(resetTypes, "order")
		case "file_upload":
			quota.FileUploadUsedMonth = 0
			resetTypes = append(resetTypes, "file_upload")
		case "email":
			quota.EmailSentMonth = 0
			resetTypes = append(resetTypes, "email")
		}
	}

	// 4. 保存更新
	if err := h.quotaRepo.Update(ctx, quota); err != nil {
		h.logger.Error(ctx, "Failed to reset quota", err, map[string]interface{}{
			"tenant_id":   cmd.TenantID,
			"quota_types": cmd.QuotaTypes,
		})
		return nil, fmt.Errorf("重置配额失败: %w", err)
	}

	h.logger.Info(ctx, "Quota reset successfully", map[string]interface{}{
		"tenant_id":    cmd.TenantID,
		"reset_types":  resetTypes,
	})

	// 5. 返回结果
	return &model.ResetQuotaResult{
		TenantID:   cmd.TenantID,
		ResetTypes: resetTypes,
		ResetAt:    time.Now(),
	}, nil
}

// ConsumeQuota 处理消耗配额命令
func (h *TenantQuotaCommandHandler) ConsumeQuota(ctx context.Context, tenantID string, quotaType string, amount int64) error {
	h.logger.Info(ctx, "Processing ConsumeQuota command", map[string]interface{}{
		"tenant_id":  tenantID,
		"quota_type": quotaType,
		"amount":     amount,
	})

	// 1. 获取租户配额
	quota, err := h.quotaRepo.GetByTenantID(ctx, tenantID)
	if err != nil {
		h.logger.Error(ctx, "Failed to get tenant quota", err, map[string]interface{}{
			"tenant_id": tenantID,
		})
		return fmt.Errorf("获取租户配额失败: %w", err)
	}

	// 2. 根据类型消耗配额
	switch quotaType {
	case "user":
		for i := int64(0); i < amount; i++ {
			if err := quota.ConsumeUserQuota(); err != nil {
				return fmt.Errorf("消耗用户配额失败: %w", err)
			}
		}
	case "storage":
		if err := quota.ConsumeStorageQuota(amount); err != nil {
			return fmt.Errorf("消耗存储配额失败: %w", err)
		}
	case "api":
		for i := int64(0); i < amount; i++ {
			if err := quota.ConsumeAPIQuota(); err != nil {
				return fmt.Errorf("消耗API配额失败: %w", err)
			}
		}
	case "product":
		for i := int64(0); i < amount; i++ {
			if err := quota.ConsumeProductQuota(); err != nil {
				return fmt.Errorf("消耗商品配额失败: %w", err)
			}
		}
	case "order":
		for i := int64(0); i < amount; i++ {
			if err := quota.ConsumeOrderQuota(); err != nil {
				return fmt.Errorf("消耗订单配额失败: %w", err)
			}
		}
	case "file_upload":
		for i := int64(0); i < amount; i++ {
			if err := quota.ConsumeFileUploadQuota(); err != nil {
				return fmt.Errorf("消耗文件上传配额失败: %w", err)
			}
		}
	case "email":
		for i := int64(0); i < amount; i++ {
			if err := quota.ConsumeEmailQuota(); err != nil {
				return fmt.Errorf("消耗邮件配额失败: %w", err)
			}
		}
	default:
		return fmt.Errorf("不支持的配额类型: %s", quotaType)
	}

	// 3. 保存更新
	if err := h.quotaRepo.Update(ctx, quota); err != nil {
		h.logger.Error(ctx, "Failed to consume quota", err, map[string]interface{}{
			"tenant_id":  tenantID,
			"quota_type": quotaType,
			"amount":     amount,
		})
		return fmt.Errorf("保存配额消耗失败: %w", err)
	}

	h.logger.Info(ctx, "Quota consumed successfully", map[string]interface{}{
		"tenant_id":  tenantID,
		"quota_type": quotaType,
		"amount":     amount,
	})

	return nil
}

// ReleaseQuota 处理释放配额命令
func (h *TenantQuotaCommandHandler) ReleaseQuota(ctx context.Context, tenantID string, quotaType string, amount int64) error {
	h.logger.Info(ctx, "Processing ReleaseQuota command", map[string]interface{}{
		"tenant_id":  tenantID,
		"quota_type": quotaType,
		"amount":     amount,
	})

	// 1. 获取租户配额
	quota, err := h.quotaRepo.GetByTenantID(ctx, tenantID)
	if err != nil {
		h.logger.Error(ctx, "Failed to get tenant quota", err, map[string]interface{}{
			"tenant_id": tenantID,
		})
		return fmt.Errorf("获取租户配额失败: %w", err)
	}

	// 2. 根据类型释放配额
	switch quotaType {
	case "user":
		for i := int64(0); i < amount; i++ {
			if err := quota.ReleaseUserQuota(); err != nil {
				return fmt.Errorf("释放用户配额失败: %w", err)
			}
		}
	case "storage":
		if err := quota.ReleaseStorageQuota(amount); err != nil {
			return fmt.Errorf("释放存储配额失败: %w", err)
		}
	case "product":
		for i := int64(0); i < amount; i++ {
			if err := quota.ReleaseProductQuota(); err != nil {
				return fmt.Errorf("释放商品配额失败: %w", err)
			}
		}
	default:
		return fmt.Errorf("不支持释放的配额类型: %s", quotaType)
	}

	// 3. 保存更新
	if err := h.quotaRepo.Update(ctx, quota); err != nil {
		h.logger.Error(ctx, "Failed to release quota", err, map[string]interface{}{
			"tenant_id":  tenantID,
			"quota_type": quotaType,
			"amount":     amount,
		})
		return fmt.Errorf("保存配额释放失败: %w", err)
	}

	h.logger.Info(ctx, "Quota released successfully", map[string]interface{}{
		"tenant_id":  tenantID,
		"quota_type": quotaType,
		"amount":     amount,
	})

	return nil
}
