package model

import (
	"fmt"
	"strings"
	"time"

	"github.com/go-playground/validator/v10"
)

// ==================== 认证命令 ====================

// LoginCommand 登录命令
type LoginCommand struct {
	IdentityType string `json:"identity_type" validate:"required,oneof=username email phone"`
	Identifier   string `json:"identifier" validate:"required,min=1,max=255"`
	Credential   string `json:"credential" validate:"required,min=6,max=255"`
	DeviceID     string `json:"device_id,omitempty" validate:"omitempty,max=64"`
	DeviceInfo   string `json:"device_info,omitempty" validate:"omitempty,max=500"`
	RememberMe   bool   `json:"remember_me"`
}

// Validate 验证登录命令
func (c *LoginCommand) Validate() error {
	validate := validator.New()
	if err := validate.Struct(c); err != nil {
		return fmt.Errorf("登录命令验证失败: %w", err)
	}

	// 根据身份类型验证标识符格式
	switch c.IdentityType {
	case "email":
		if !strings.Contains(c.Identifier, "@") {
			return fmt.Errorf("邮箱格式无效")
		}
	case "phone":
		if len(c.Identifier) < 11 {
			return fmt.Errorf("手机号格式无效")
		}
	case "username":
		if len(c.Identifier) < 3 {
			return fmt.Errorf("用户名长度不能少于3位")
		}
	}

	return nil
}

// SelectTenantCommand 选择租户命令
type SelectTenantCommand struct {
	UserID          string `json:"user_id" validate:"required,max=36"`
	TenantID        string `json:"tenant_id" validate:"required,max=36"`
	PreAuthTokenJTI string `json:"-"` // 从中间件传递的JTI，不在JSON中序列化
	DeviceID        string `json:"device_id,omitempty" validate:"omitempty,max=64"`
}

// Validate 验证选择租户命令
func (c *SelectTenantCommand) Validate() error {
	validate := validator.New()
	return validate.Struct(c)
}

// LogoutCommand 登出命令
type LogoutCommand struct {
	UserID     string `json:"user_id" validate:"required,max=36"`
	TenantID   string `json:"tenant_id" validate:"required,max=36"`
	LogoutType string `json:"logout_type" validate:"required,oneof=current all"`
	DeviceID   string `json:"device_id,omitempty" validate:"omitempty,max=64"`
}

// Validate 验证登出命令
func (c *LogoutCommand) Validate() error {
	validate := validator.New()
	return validate.Struct(c)
}

// RefreshTokenCommand 刷新令牌命令
type RefreshTokenCommand struct {
	RefreshToken string `json:"refresh_token" validate:"required"`
	DeviceID     string `json:"device_id,omitempty" validate:"omitempty,max=64"`
}

// Validate 验证刷新令牌命令
func (c *RefreshTokenCommand) Validate() error {
	validate := validator.New()
	return validate.Struct(c)
}

// ==================== 密码管理命令 ====================

// ChangePasswordCommand 修改密码命令
type ChangePasswordCommand struct {
	UserID      string `json:"user_id" validate:"required,max=36"`
	OldPassword string `json:"old_password" validate:"required,min=6,max=255"`
	NewPassword string `json:"new_password" validate:"required,min=8,max=255"`
}

// Validate 验证修改密码命令
func (c *ChangePasswordCommand) Validate() error {
	validate := validator.New()
	if err := validate.Struct(c); err != nil {
		return fmt.Errorf("修改密码命令验证失败: %w", err)
	}

	if c.OldPassword == c.NewPassword {
		return fmt.Errorf("新密码不能与旧密码相同")
	}

	return nil
}

// ResetPasswordCommand 重置密码命令
type ResetPasswordCommand struct {
	IdentityType string `json:"identity_type" validate:"required,oneof=email phone"`
	Identifier   string `json:"identifier" validate:"required,min=1,max=255"`
	NewPassword  string `json:"new_password" validate:"required,min=8,max=255"`
	VerifyCode   string `json:"verify_code" validate:"required,len=6"`
}

// Validate 验证重置密码命令
func (c *ResetPasswordCommand) Validate() error {
	validate := validator.New()
	return validate.Struct(c)
}

// ==================== 角色权限管理命令 ====================

// AssignRoleCommand 分配角色命令
type AssignRoleCommand struct {
	UserID   string `json:"user_id" validate:"required,max=36"`
	TenantID string `json:"tenant_id" validate:"required,max=36"`
	RoleID   string `json:"role_id" validate:"required,max=36"`
	Reason   string `json:"reason,omitempty" validate:"omitempty,max=500"`
}

// Validate 验证分配角色命令
func (c *AssignRoleCommand) Validate() error {
	validate := validator.New()
	return validate.Struct(c)
}

// RemoveRoleCommand 移除角色命令
type RemoveRoleCommand struct {
	UserID   string `json:"user_id" validate:"required,max=36"`
	TenantID string `json:"tenant_id" validate:"required,max=36"`
	RoleID   string `json:"role_id" validate:"required,max=36"`
	Reason   string `json:"reason,omitempty" validate:"omitempty,max=500"`
}

// Validate 验证移除角色命令
func (c *RemoveRoleCommand) Validate() error {
	validate := validator.New()
	return validate.Struct(c)
}

// UpdatePermissionCommand 更新权限命令
type UpdatePermissionCommand struct {
	RoleID    string   `json:"role_id" validate:"required,max=36"`
	TenantID  string   `json:"tenant_id" validate:"required,max=36"`
	Resource  string   `json:"resource" validate:"required,max=100"`
	Actions   []string `json:"actions" validate:"required,min=1"`
	Operation string   `json:"operation" validate:"required,oneof=add remove"`
}

// Validate 验证更新权限命令
func (c *UpdatePermissionCommand) Validate() error {
	validate := validator.New()
	if err := validate.Struct(c); err != nil {
		return fmt.Errorf("更新权限命令验证失败: %w", err)
	}

	// 验证操作类型
	validActions := map[string]bool{
		"create": true, "read": true, "update": true, "delete": true,
		"list": true, "export": true, "import": true, "approve": true,
	}

	for _, action := range c.Actions {
		if !validActions[action] {
			return fmt.Errorf("无效的权限操作: %s", action)
		}
	}

	return nil
}

// ==================== 会话管理命令 ====================

// CreateSessionCommand 创建会话命令
type CreateSessionCommand struct {
	UserID     string                 `json:"user_id" validate:"required,max=36"`
	TenantID   string                 `json:"tenant_id" validate:"required,max=36"`
	DeviceID   string                 `json:"device_id" validate:"required,max=64"`
	DeviceInfo string                 `json:"device_info,omitempty" validate:"omitempty,max=500"`
	IPAddress  string                 `json:"ip_address,omitempty" validate:"omitempty,ip"`
	UserAgent  string                 `json:"user_agent,omitempty" validate:"omitempty,max=500"`
	Metadata   map[string]interface{} `json:"metadata,omitempty"`
}

// Validate 验证创建会话命令
func (c *CreateSessionCommand) Validate() error {
	validate := validator.New()
	return validate.Struct(c)
}

// TerminateSessionCommand 终止会话命令
type TerminateSessionCommand struct {
	SessionID string `json:"session_id" validate:"required,max=36"`
	Reason    string `json:"reason,omitempty" validate:"omitempty,max=500"`
}

// Validate 验证终止会话命令
func (c *TerminateSessionCommand) Validate() error {
	validate := validator.New()
	return validate.Struct(c)
}

// ==================== 命令结果 ====================

// LoginResult 登录结果
type LoginResult struct {
	UserID           string    `json:"user_id"`
	Username         string    `json:"username"`
	Email            string    `json:"email"`
	Phone            string    `json:"phone"`
	DisplayName      string    `json:"display_name"`
	Avatar           string    `json:"avatar"`
	Tenants          []Tenant  `json:"tenants"`
	RequiresMFA      bool      `json:"requires_mfa"`
	LoginTime        time.Time `json:"login_time"`
	PreAuthToken     string    `json:"pre_auth_token"`      // 预登录token
	PreAuthExpiresAt time.Time `json:"pre_auth_expires_at"` // 预登录token过期时间
}

// Tenant 租户信息
type Tenant struct {
	TenantID    string   `json:"tenant_id"`
	TenantName  string   `json:"tenant_name"`
	Domain      string   `json:"domain"`
	Role        string   `json:"role"`
	Permissions []string `json:"permissions"`
}

// RouteTreeNode 路由树节点
type RouteTreeNode struct {
	ID        string           `json:"id"`
	Name      string           `json:"name"`
	Path      string           `json:"path"`
	Component string           `json:"component,omitempty"`
	Redirect  string           `json:"redirect,omitempty"`
	Meta      RouteNodeMeta    `json:"meta"`
	Children  []*RouteTreeNode `json:"children,omitempty"`
}

// RouteNodeMeta 路由节点元数据
type RouteNodeMeta struct {
	Title       string   `json:"title"`
	Icon        string   `json:"icon,omitempty"`
	Roles       []string `json:"roles,omitempty"`
	Permissions []string `json:"permissions,omitempty"`
	NoCache     bool     `json:"noCache"`
	Breadcrumb  bool     `json:"breadcrumb"`
	ActiveMenu  string   `json:"activeMenu,omitempty"`
	Hidden      bool     `json:"hidden"`
	KeepAlive   bool     `json:"keepAlive"`
	IsExternal  bool     `json:"isExternal"`
	Badge       string   `json:"badge,omitempty"`
	BadgeType   string   `json:"badgeType,omitempty"`
	Target      string   `json:"target,omitempty"`
}

// AuthResult 认证结果
type AuthResult struct {
	AccessToken  string           `json:"access_token"`
	RefreshToken string           `json:"refresh_token"`
	TokenType    string           `json:"token_type"`
	ExpiresIn    int64            `json:"expires_in"`
	ExpiresAt    time.Time        `json:"expires_at"`
	UserID       string           `json:"user_id"`
	TenantID     string           `json:"tenant_id"`
	Roles        []string         `json:"roles"`
	Permissions  []string         `json:"permissions"`
	RouteTree    []*RouteTreeNode `json:"route_tree,omitempty"` // 新增：用户路由树
}

// OperationResult 操作结果
type OperationResult struct {
	Success   bool                   `json:"success"`
	Message   string                 `json:"message"`
	Data      map[string]interface{} `json:"data,omitempty"`
	Timestamp time.Time              `json:"timestamp"`
}

// SessionResult 会话结果
type SessionResult struct {
	SessionID  string                 `json:"session_id"`
	UserID     string                 `json:"user_id"`
	TenantID   string                 `json:"tenant_id"`
	DeviceID   string                 `json:"device_id"`
	Status     string                 `json:"status"`
	CreatedAt  time.Time              `json:"created_at"`
	LastActive time.Time              `json:"last_active"`
	ExpiresAt  time.Time              `json:"expires_at"`
	Metadata   map[string]interface{} `json:"metadata,omitempty"`
}
