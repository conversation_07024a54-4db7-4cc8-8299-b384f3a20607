package model

import (
	"testing"
	"time"

	"github.com/shopspring/decimal"
	"github.com/stretchr/testify/assert"
)

// TestCreateTenantCommand_Validate 测试创建租户命令验证
func TestCreateTenantCommand_Validate(t *testing.T) {
	t.Run("有效的创建租户命令", func(t *testing.T) {
		cmd := &CreateTenantCommand{
			Name:         "测试租户",
			Domain:       "test.example.com",
			DisplayName:  "测试租户显示名",
			Description:  "这是一个测试租户",
			Type:         "basic",
			Industry:     "科技",
			Country:      "中国",
			Province:     "北京",
			City:         "北京",
			Address:      "测试地址",
			ContactEmail: "<EMAIL>",
			ContactPhone: "13800138000",
		}

		err := cmd.Validate()
		assert.NoError(t, err)
	})

	t.Run("租户名称为空", func(t *testing.T) {
		cmd := &CreateTenantCommand{
			Name:   "",
			Domain: "test.example.com",
			Type:   "basic",
		}

		err := cmd.Validate()
		assert.Error(t, err)
		assert.Contains(t, err.Error(), "租户名称是必需的")
	})

	t.Run("租户名称过短", func(t *testing.T) {
		cmd := &CreateTenantCommand{
			Name:   "a",
			Domain: "test.example.com",
			Type:   "basic",
		}

		err := cmd.Validate()
		assert.Error(t, err)
		assert.Contains(t, err.Error(), "租户名称长度必须在2到100个字符之间")
	})

	t.Run("域名为空", func(t *testing.T) {
		cmd := &CreateTenantCommand{
			Name:   "测试租户",
			Domain: "",
			Type:   "basic",
		}

		err := cmd.Validate()
		assert.Error(t, err)
		assert.Contains(t, err.Error(), "租户域名是必需的")
	})

	t.Run("无效的租户类型", func(t *testing.T) {
		cmd := &CreateTenantCommand{
			Name:   "测试租户",
			Domain: "test.example.com",
			Type:   "invalid",
		}

		err := cmd.Validate()
		assert.Error(t, err)
		assert.Contains(t, err.Error(), "无效的租户类型")
	})

	t.Run("无效的邮箱格式", func(t *testing.T) {
		cmd := &CreateTenantCommand{
			Name:         "测试租户",
			Domain:       "test.example.com",
			Type:         "basic",
			ContactEmail: "invalid-email",
		}

		err := cmd.Validate()
		assert.Error(t, err)
		assert.Contains(t, err.Error(), "无效的联系邮箱格式")
	})
}

// TestUpdateTenantCommand_Validate 测试更新租户命令验证
func TestUpdateTenantCommand_Validate(t *testing.T) {
	t.Run("有效的更新租户命令", func(t *testing.T) {
		cmd := &UpdateTenantCommand{
			TenantID: "tenant-123",
			BasicInfo: &UpdateTenantBasicInfo{
				Name:        "新租户名称",
				DisplayName: "新显示名称",
				Description: "新描述",
				Industry:    "新行业",
			},
		}

		err := cmd.Validate()
		assert.NoError(t, err)
	})

	t.Run("租户ID为空", func(t *testing.T) {
		cmd := &UpdateTenantCommand{
			TenantID: "",
			BasicInfo: &UpdateTenantBasicInfo{
				Name: "新租户名称",
			},
		}

		err := cmd.Validate()
		assert.Error(t, err)
		assert.Contains(t, err.Error(), "租户ID是必需的")
	})

	t.Run("没有更新字段", func(t *testing.T) {
		cmd := &UpdateTenantCommand{
			TenantID: "tenant-123",
		}

		err := cmd.Validate()
		assert.Error(t, err)
		assert.Contains(t, err.Error(), "至少需要更新一个字段")
	})
}

// TestActivateTenantCommand_Validate 测试激活租户命令验证
func TestActivateTenantCommand_Validate(t *testing.T) {
	t.Run("有效的激活租户命令", func(t *testing.T) {
		cmd := &ActivateTenantCommand{
			TenantID: "tenant-123",
			Reason:   "测试激活",
		}

		err := cmd.Validate()
		assert.NoError(t, err)
	})

	t.Run("租户ID为空", func(t *testing.T) {
		cmd := &ActivateTenantCommand{
			TenantID: "",
		}

		err := cmd.Validate()
		assert.Error(t, err)
		assert.Contains(t, err.Error(), "租户ID是必需的")
	})
}

// TestSuspendTenantCommand_Validate 测试暂停租户命令验证
func TestSuspendTenantCommand_Validate(t *testing.T) {
	t.Run("有效的暂停租户命令", func(t *testing.T) {
		cmd := &SuspendTenantCommand{
			TenantID: "tenant-123",
			Reason:   "违反使用条款",
		}

		err := cmd.Validate()
		assert.NoError(t, err)
	})

	t.Run("租户ID为空", func(t *testing.T) {
		cmd := &SuspendTenantCommand{
			TenantID: "",
			Reason:   "违反使用条款",
		}

		err := cmd.Validate()
		assert.Error(t, err)
		assert.Contains(t, err.Error(), "租户ID是必需的")
	})

	t.Run("暂停原因为空", func(t *testing.T) {
		cmd := &SuspendTenantCommand{
			TenantID: "tenant-123",
			Reason:   "",
		}

		err := cmd.Validate()
		assert.Error(t, err)
		assert.Contains(t, err.Error(), "暂停租户需要提供原因")
	})
}

// TestUpdateQuotaCommand_Validate 测试更新配额命令验证
func TestUpdateQuotaCommand_Validate(t *testing.T) {
	t.Run("有效的更新配额命令", func(t *testing.T) {
		userLimit := 100
		storageLimit := int64(10240)
		cmd := &UpdateQuotaCommand{
			TenantID:     "tenant-123",
			UserLimit:    &userLimit,
			StorageLimit: &storageLimit,
		}

		err := cmd.Validate()
		assert.NoError(t, err)
	})

	t.Run("租户ID为空", func(t *testing.T) {
		userLimit := 100
		cmd := &UpdateQuotaCommand{
			TenantID:  "",
			UserLimit: &userLimit,
		}

		err := cmd.Validate()
		assert.Error(t, err)
		assert.Contains(t, err.Error(), "租户ID是必需的")
	})

	t.Run("没有更新配额字段", func(t *testing.T) {
		cmd := &UpdateQuotaCommand{
			TenantID: "tenant-123",
		}

		err := cmd.Validate()
		assert.Error(t, err)
		assert.Contains(t, err.Error(), "至少需要更新一个配额字段")
	})
}

// TestCreateSubscriptionCommand_Validate 测试创建订阅命令验证
func TestCreateSubscriptionCommand_Validate(t *testing.T) {
	t.Run("有效的创建订阅命令", func(t *testing.T) {
		startDate := time.Now()
		endDate := startDate.AddDate(1, 0, 0) // 一年后
		cmd := &CreateSubscriptionCommand{
			TenantID:     "tenant-123",
			PlanID:       "plan-basic",
			PlanName:     "基础版",
			StartDate:    startDate,
			EndDate:      endDate,
			Price:        decimal.NewFromFloat(99.99),
			Currency:     "CNY",
			BillingCycle: "monthly",
			UserLimit:    10,
			StorageLimit: 1024,
			APILimit:     10000,
			ProductLimit: 100,
			OrderLimit:   1000,
		}

		err := cmd.Validate()
		assert.NoError(t, err)
	})

	t.Run("租户ID为空", func(t *testing.T) {
		startDate := time.Now()
		endDate := startDate.AddDate(1, 0, 0)
		cmd := &CreateSubscriptionCommand{
			TenantID:     "",
			PlanID:       "plan-basic",
			PlanName:     "基础版",
			StartDate:    startDate,
			EndDate:      endDate,
			Price:        decimal.NewFromFloat(99.99),
			Currency:     "CNY",
			BillingCycle: "monthly",
		}

		err := cmd.Validate()
		assert.Error(t, err)
		assert.Contains(t, err.Error(), "租户ID是必需的")
	})

	t.Run("结束日期早于开始日期", func(t *testing.T) {
		startDate := time.Now()
		endDate := startDate.AddDate(-1, 0, 0) // 一年前
		cmd := &CreateSubscriptionCommand{
			TenantID:     "tenant-123",
			PlanID:       "plan-basic",
			PlanName:     "基础版",
			StartDate:    startDate,
			EndDate:      endDate,
			Price:        decimal.NewFromFloat(99.99),
			Currency:     "CNY",
			BillingCycle: "monthly",
		}

		err := cmd.Validate()
		assert.Error(t, err)
		assert.Contains(t, err.Error(), "结束日期不能早于开始日期")
	})

	t.Run("无效的计费周期", func(t *testing.T) {
		startDate := time.Now()
		endDate := startDate.AddDate(1, 0, 0)
		cmd := &CreateSubscriptionCommand{
			TenantID:     "tenant-123",
			PlanID:       "plan-basic",
			PlanName:     "基础版",
			StartDate:    startDate,
			EndDate:      endDate,
			Price:        decimal.NewFromFloat(99.99),
			Currency:     "CNY",
			BillingCycle: "invalid",
		}

		err := cmd.Validate()
		assert.Error(t, err)
		assert.Contains(t, err.Error(), "无效的计费周期")
	})
}
