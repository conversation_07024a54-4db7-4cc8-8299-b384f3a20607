package model

import (
	"errors"
	"strings"
	"time"
)

// ==================== 创建用户命令 ====================

// CreateUserCommand 创建用户命令
type CreateUserCommand struct {
	TenantID  string `json:"tenant_id" validate:"required"`
	Username  string `json:"username" validate:"required,min=3,max=50"`
	Email     string `json:"email" validate:"required,email"`
	Phone     string `json:"phone" validate:"omitempty,min=10,max=20"`
	FirstName string `json:"first_name" validate:"omitempty,max=50"`
	LastName  string `json:"last_name" validate:"omitempty,max=50"`
	Nickname  string `json:"nickname" validate:"omitempty,max=100"`
	Avatar    string `json:"avatar" validate:"omitempty,url"`
	Language  string `json:"language" validate:"omitempty,len=5"`
	Timezone  string `json:"timezone" validate:"omitempty"`
}

// Validate 验证创建用户命令
func (c *CreateUserCommand) Validate() error {
	if strings.TrimSpace(c.TenantID) == "" {
		return errors.New("租户ID是必需的")
	}

	if strings.TrimSpace(c.Username) == "" {
		return errors.New("用户名是必需的")
	}

	if len(c.Username) < 3 || len(c.Username) > 50 {
		return errors.New("用户名长度必须在3到50个字符之间")
	}

	if strings.TrimSpace(c.Email) == "" {
		return errors.New("邮箱是必需的")
	}

	// 简单的邮箱格式验证
	if !strings.Contains(c.Email, "@") || !strings.Contains(c.Email, ".") {
		return errors.New("无效的邮箱格式")
	}

	return nil
}

// CreateUserResult 创建用户结果
type CreateUserResult struct {
	UserID    string    `json:"user_id"`
	Username  string    `json:"username"`
	Email     string    `json:"email"`
	Status    string    `json:"status"`
	CreatedAt time.Time `json:"created_at"`
}

// ==================== 更新用户命令 ====================

// UpdateUserCommand 更新用户命令
type UpdateUserCommand struct {
	UserID      string                 `json:"user_id" validate:"required"`
	TenantID    string                 `json:"tenant_id" validate:"required"`
	Profile     *UpdateProfileData     `json:"profile,omitempty"`
	ContactInfo *UpdateContactInfoData `json:"contact_info,omitempty"`
	Preferences *UpdatePreferencesData `json:"preferences,omitempty"`
}

// UpdateProfileData 更新资料数据
type UpdateProfileData struct {
	Nickname  string `json:"nickname" validate:"omitempty,max=100"`
	FirstName string `json:"first_name" validate:"omitempty,max=50"`
	LastName  string `json:"last_name" validate:"omitempty,max=50"`
	Avatar    string `json:"avatar" validate:"omitempty,url"`
}

// UpdateContactInfoData 更新联系信息数据
type UpdateContactInfoData struct {
	Email string `json:"email" validate:"omitempty,email"`
	Phone string `json:"phone" validate:"omitempty,min=10,max=20"`
}

// UpdatePreferencesData 更新偏好设置数据
type UpdatePreferencesData struct {
	Language string `json:"language" validate:"omitempty,len=5"`
	Timezone string `json:"timezone" validate:"omitempty"`
}

// Validate 验证更新用户命令
func (c *UpdateUserCommand) Validate() error {
	if strings.TrimSpace(c.UserID) == "" {
		return errors.New("用户ID是必需的")
	}

	if strings.TrimSpace(c.TenantID) == "" {
		return errors.New("租户ID是必需的")
	}

	// 至少需要更新一个字段
	if c.Profile == nil && c.ContactInfo == nil && c.Preferences == nil {
		return errors.New("至少需要更新一个字段")
	}

	// 验证邮箱格式
	if c.ContactInfo != nil && c.ContactInfo.Email != "" {
		if !strings.Contains(c.ContactInfo.Email, "@") || !strings.Contains(c.ContactInfo.Email, ".") {
			return errors.New("无效的邮箱格式")
		}
	}

	return nil
}

// UpdateUserResult 更新用户结果
type UpdateUserResult struct {
	UserID    string    `json:"user_id"`
	Username  string    `json:"username"`
	Email     string    `json:"email"`
	Status    string    `json:"status"`
	Version   int       `json:"version"`
	UpdatedAt time.Time `json:"updated_at"`
}

// ==================== 激活用户命令 ====================

// ActivateUserCommand 激活用户命令
type ActivateUserCommand struct {
	UserID   string `json:"user_id" validate:"required"`
	TenantID string `json:"tenant_id" validate:"required"`
}

// Validate 验证激活用户命令
func (c *ActivateUserCommand) Validate() error {
	if strings.TrimSpace(c.UserID) == "" {
		return errors.New("用户ID是必需的")
	}

	if strings.TrimSpace(c.TenantID) == "" {
		return errors.New("租户ID是必需的")
	}

	return nil
}

// ActivateUserResult 激活用户结果
type ActivateUserResult struct {
	UserID      string    `json:"user_id"`
	Status      string    `json:"status"`
	ActivatedAt time.Time `json:"activated_at"`
}

// ==================== 停用用户命令 ====================

// DeactivateUserCommand 停用用户命令
type DeactivateUserCommand struct {
	UserID   string `json:"user_id" validate:"required"`
	TenantID string `json:"tenant_id" validate:"required"`
	Reason   string `json:"reason" validate:"omitempty,max=500"`
}

// Validate 验证停用用户命令
func (c *DeactivateUserCommand) Validate() error {
	if strings.TrimSpace(c.UserID) == "" {
		return errors.New("用户ID是必需的")
	}

	if strings.TrimSpace(c.TenantID) == "" {
		return errors.New("租户ID是必需的")
	}

	return nil
}

// DeactivateUserResult 停用用户结果
type DeactivateUserResult struct {
	UserID        string    `json:"user_id"`
	Status        string    `json:"status"`
	DeactivatedAt time.Time `json:"deactivated_at"`
}

// ==================== 暂停用户命令 ====================

// SuspendUserCommand 暂停用户命令
type SuspendUserCommand struct {
	UserID   string `json:"user_id" validate:"required"`
	TenantID string `json:"tenant_id" validate:"required"`
	Reason   string `json:"reason" validate:"required,max=500"`
}

// Validate 验证暂停用户命令
func (c *SuspendUserCommand) Validate() error {
	if strings.TrimSpace(c.UserID) == "" {
		return errors.New("用户ID是必需的")
	}

	if strings.TrimSpace(c.TenantID) == "" {
		return errors.New("租户ID是必需的")
	}

	if strings.TrimSpace(c.Reason) == "" {
		return errors.New("暂停用户需要提供原因")
	}

	return nil
}

// SuspendUserResult 暂停用户结果
type SuspendUserResult struct {
	UserID      string    `json:"user_id"`
	Status      string    `json:"status"`
	Reason      string    `json:"reason"`
	SuspendedAt time.Time `json:"suspended_at"`
}

// ==================== 封禁用户命令 ====================

// BanUserCommand 封禁用户命令
type BanUserCommand struct {
	UserID   string `json:"user_id" validate:"required"`
	TenantID string `json:"tenant_id" validate:"required"`
	Reason   string `json:"reason" validate:"required,max=500"`
}

// Validate 验证封禁用户命令
func (c *BanUserCommand) Validate() error {
	if strings.TrimSpace(c.UserID) == "" {
		return errors.New("用户ID是必需的")
	}

	if strings.TrimSpace(c.TenantID) == "" {
		return errors.New("租户ID是必需的")
	}

	if strings.TrimSpace(c.Reason) == "" {
		return errors.New("封禁用户需要提供原因")
	}

	return nil
}

// BanUserResult 封禁用户结果
type BanUserResult struct {
	UserID   string    `json:"user_id"`
	Status   string    `json:"status"`
	Reason   string    `json:"reason"`
	BannedAt time.Time `json:"banned_at"`
}
