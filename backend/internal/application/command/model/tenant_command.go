package model

import (
	"errors"
	"strings"
	"time"

	"github.com/shopspring/decimal"
)

// ==================== 创建租户命令 ====================

// CreateTenantCommand 创建租户命令
type CreateTenantCommand struct {
	Name         string `json:"name" validate:"required,min=2,max=100"`
	Domain       string `json:"domain" validate:"required,min=3,max=100"`
	DisplayName  string `json:"display_name" validate:"max=100"`
	Description  string `json:"description" validate:"max=500"`
	Type         string `json:"type" validate:"required,oneof=system enterprise professional basic trial"`
	Industry     string `json:"industry" validate:"max=100"`
	Country      string `json:"country" validate:"max=50"`
	Province     string `json:"province" validate:"max=50"`
	City         string `json:"city" validate:"max=50"`
	Address      string `json:"address" validate:"max=255"`
	ContactEmail string `json:"contact_email" validate:"omitempty,email"`
	ContactPhone string `json:"contact_phone" validate:"omitempty,len=11"`
}

// Validate 验证创建租户命令
func (c *CreateTenantCommand) Validate() error {
	if strings.TrimSpace(c.Name) == "" {
		return errors.New("租户名称是必需的")
	}

	if len(c.Name) < 2 || len(c.Name) > 100 {
		return errors.New("租户名称长度必须在2到100个字符之间")
	}

	if strings.TrimSpace(c.Domain) == "" {
		return errors.New("租户域名是必需的")
	}

	if len(c.Domain) < 3 || len(c.Domain) > 100 {
		return errors.New("租户域名长度必须在3到100个字符之间")
	}

	if strings.TrimSpace(c.Type) == "" {
		return errors.New("租户类型是必需的")
	}

	validTypes := []string{"system", "enterprise", "professional", "basic", "trial"}
	isValidType := false
	for _, validType := range validTypes {
		if c.Type == validType {
			isValidType = true
			break
		}
	}
	if !isValidType {
		return errors.New("无效的租户类型")
	}

	// 验证邮箱格式
	if c.ContactEmail != "" {
		if !strings.Contains(c.ContactEmail, "@") || !strings.Contains(c.ContactEmail, ".") {
			return errors.New("无效的联系邮箱格式")
		}
	}

	return nil
}

// CreateTenantResult 创建租户结果
type CreateTenantResult struct {
	TenantID  string    `json:"tenant_id"`
	Name      string    `json:"name"`
	Domain    string    `json:"domain"`
	Type      string    `json:"type"`
	Status    string    `json:"status"`
	CreatedAt time.Time `json:"created_at"`
}

// ==================== 更新租户命令 ====================

// UpdateTenantCommand 更新租户命令
type UpdateTenantCommand struct {
	TenantID     string                    `json:"tenant_id" validate:"required"`
	BasicInfo    *UpdateTenantBasicInfo    `json:"basic_info,omitempty"`
	ContactInfo  *UpdateTenantContactInfo  `json:"contact_info,omitempty"`
	AddressInfo  *UpdateTenantAddressInfo  `json:"address_info,omitempty"`
	BusinessInfo *UpdateTenantBusinessInfo `json:"business_info,omitempty"`
	Settings     map[string]interface{}    `json:"settings,omitempty"`
}

// UpdateTenantBasicInfo 更新租户基本信息
type UpdateTenantBasicInfo struct {
	Name        string `json:"name" validate:"omitempty,min=2,max=100"`
	DisplayName string `json:"display_name" validate:"omitempty,max=100"`
	Description string `json:"description" validate:"omitempty,max=500"`
	Industry    string `json:"industry" validate:"omitempty,max=100"`
}

// UpdateTenantContactInfo 更新租户联系信息
type UpdateTenantContactInfo struct {
	ContactEmail string `json:"contact_email" validate:"omitempty,email"`
	ContactPhone string `json:"contact_phone" validate:"omitempty,len=11"`
}

// UpdateTenantAddressInfo 更新租户地址信息
type UpdateTenantAddressInfo struct {
	Country  string `json:"country" validate:"omitempty,max=50"`
	Province string `json:"province" validate:"omitempty,max=50"`
	City     string `json:"city" validate:"omitempty,max=50"`
	Address  string `json:"address" validate:"omitempty,max=255"`
}

// UpdateTenantBusinessInfo 更新租户业务信息
type UpdateTenantBusinessInfo struct {
	BusinessLicense string `json:"business_license" validate:"omitempty,max=100"`
	TaxID           string `json:"tax_id" validate:"omitempty,max=50"`
}

// Validate 验证更新租户命令
func (c *UpdateTenantCommand) Validate() error {
	if strings.TrimSpace(c.TenantID) == "" {
		return errors.New("租户ID是必需的")
	}

	// 至少需要更新一个字段
	if c.BasicInfo == nil && c.ContactInfo == nil && c.AddressInfo == nil && c.BusinessInfo == nil && c.Settings == nil {
		return errors.New("至少需要更新一个字段")
	}

	// 验证邮箱格式
	if c.ContactInfo != nil && c.ContactInfo.ContactEmail != "" {
		if !strings.Contains(c.ContactInfo.ContactEmail, "@") || !strings.Contains(c.ContactInfo.ContactEmail, ".") {
			return errors.New("无效的联系邮箱格式")
		}
	}

	return nil
}

// UpdateTenantResult 更新租户结果
type UpdateTenantResult struct {
	TenantID  string    `json:"tenant_id"`
	Name      string    `json:"name"`
	Domain    string    `json:"domain"`
	Status    string    `json:"status"`
	Version   int       `json:"version"`
	UpdatedAt time.Time `json:"updated_at"`
}

// ==================== 激活租户命令 ====================

// ActivateTenantCommand 激活租户命令
type ActivateTenantCommand struct {
	TenantID string `json:"tenant_id" validate:"required"`
	Reason   string `json:"reason" validate:"omitempty,max=500"`
}

// Validate 验证激活租户命令
func (c *ActivateTenantCommand) Validate() error {
	if strings.TrimSpace(c.TenantID) == "" {
		return errors.New("租户ID是必需的")
	}
	return nil
}

// ActivateTenantResult 激活租户结果
type ActivateTenantResult struct {
	TenantID    string    `json:"tenant_id"`
	Status      string    `json:"status"`
	ActivatedAt time.Time `json:"activated_at"`
}

// ==================== 暂停租户命令 ====================

// SuspendTenantCommand 暂停租户命令
type SuspendTenantCommand struct {
	TenantID string `json:"tenant_id" validate:"required"`
	Reason   string `json:"reason" validate:"required,max=500"`
}

// Validate 验证暂停租户命令
func (c *SuspendTenantCommand) Validate() error {
	if strings.TrimSpace(c.TenantID) == "" {
		return errors.New("租户ID是必需的")
	}

	if strings.TrimSpace(c.Reason) == "" {
		return errors.New("暂停租户需要提供原因")
	}

	return nil
}

// SuspendTenantResult 暂停租户结果
type SuspendTenantResult struct {
	TenantID    string    `json:"tenant_id"`
	Status      string    `json:"status"`
	Reason      string    `json:"reason"`
	SuspendedAt time.Time `json:"suspended_at"`
}

// ==================== 终止租户命令 ====================

// TerminateTenantCommand 终止租户命令
type TerminateTenantCommand struct {
	TenantID string `json:"tenant_id" validate:"required"`
	Reason   string `json:"reason" validate:"required,max=500"`
}

// Validate 验证终止租户命令
func (c *TerminateTenantCommand) Validate() error {
	if strings.TrimSpace(c.TenantID) == "" {
		return errors.New("租户ID是必需的")
	}

	if strings.TrimSpace(c.Reason) == "" {
		return errors.New("终止租户需要提供原因")
	}

	return nil
}

// TerminateTenantResult 终止租户结果
type TerminateTenantResult struct {
	TenantID     string    `json:"tenant_id"`
	Status       string    `json:"status"`
	Reason       string    `json:"reason"`
	TerminatedAt time.Time `json:"terminated_at"`
}

// ==================== 删除租户命令 ====================

// DeleteTenantCommand 删除租户命令（软删除）
type DeleteTenantCommand struct {
	TenantID string `json:"tenant_id" validate:"required"`
	Reason   string `json:"reason" validate:"required,max=500"`
}

// Validate 验证删除租户命令
func (c *DeleteTenantCommand) Validate() error {
	if strings.TrimSpace(c.TenantID) == "" {
		return errors.New("租户ID是必需的")
	}

	if strings.TrimSpace(c.Reason) == "" {
		return errors.New("删除租户需要提供原因")
	}

	return nil
}

// DeleteTenantResult 删除租户结果
type DeleteTenantResult struct {
	TenantID  string    `json:"tenant_id"`
	DeletedAt time.Time `json:"deleted_at"`
}

// ==================== 配额管理命令 ====================

// UpdateQuotaCommand 更新配额命令
type UpdateQuotaCommand struct {
	TenantID        string `json:"tenant_id" validate:"required"`
	UserLimit       *int   `json:"user_limit,omitempty" validate:"omitempty,min=1"`
	StorageLimit    *int64 `json:"storage_limit,omitempty" validate:"omitempty,min=1"`
	APILimit        *int   `json:"api_limit,omitempty" validate:"omitempty,min=1"`
	ProductLimit    *int   `json:"product_limit,omitempty" validate:"omitempty,min=1"`
	OrderLimit      *int   `json:"order_limit,omitempty" validate:"omitempty,min=1"`
	FileUploadLimit *int   `json:"file_upload_limit,omitempty" validate:"omitempty,min=1"`
	EmailLimit      *int   `json:"email_limit,omitempty" validate:"omitempty,min=1"`
}

// Validate 验证更新配额命令
func (c *UpdateQuotaCommand) Validate() error {
	if strings.TrimSpace(c.TenantID) == "" {
		return errors.New("租户ID是必需的")
	}

	// 至少需要更新一个配额字段
	if c.UserLimit == nil && c.StorageLimit == nil && c.APILimit == nil &&
		c.ProductLimit == nil && c.OrderLimit == nil && c.FileUploadLimit == nil && c.EmailLimit == nil {
		return errors.New("至少需要更新一个配额字段")
	}

	return nil
}

// UpdateQuotaResult 更新配额结果
type UpdateQuotaResult struct {
	TenantID  string    `json:"tenant_id"`
	UpdatedAt time.Time `json:"updated_at"`
}

// ResetQuotaCommand 重置配额命令
type ResetQuotaCommand struct {
	TenantID   string   `json:"tenant_id" validate:"required"`
	QuotaTypes []string `json:"quota_types" validate:"required,min=1"`
}

// Validate 验证重置配额命令
func (c *ResetQuotaCommand) Validate() error {
	if strings.TrimSpace(c.TenantID) == "" {
		return errors.New("租户ID是必需的")
	}

	if len(c.QuotaTypes) == 0 {
		return errors.New("配额类型是必需的")
	}

	validTypes := []string{"user", "storage", "api", "product", "order", "file_upload", "email"}
	for _, quotaType := range c.QuotaTypes {
		isValid := false
		for _, validType := range validTypes {
			if quotaType == validType {
				isValid = true
				break
			}
		}
		if !isValid {
			return errors.New("无效的配额类型: " + quotaType)
		}
	}

	return nil
}

// ResetQuotaResult 重置配额结果
type ResetQuotaResult struct {
	TenantID   string    `json:"tenant_id"`
	ResetTypes []string  `json:"reset_types"`
	ResetAt    time.Time `json:"reset_at"`
}

// ==================== 订阅管理命令 ====================

// CreateSubscriptionCommand 创建订阅命令
type CreateSubscriptionCommand struct {
	TenantID     string          `json:"tenant_id" validate:"required"`
	PlanID       string          `json:"plan_id" validate:"required"`
	PlanName     string          `json:"plan_name" validate:"required"`
	StartDate    time.Time       `json:"start_date" validate:"required"`
	EndDate      time.Time       `json:"end_date" validate:"required"`
	Price        decimal.Decimal `json:"price" validate:"required"`
	Currency     string          `json:"currency" validate:"required,len=3"`
	BillingCycle string          `json:"billing_cycle" validate:"required,oneof=monthly yearly lifetime"`
	UserLimit    int             `json:"user_limit" validate:"min=1"`
	StorageLimit int64           `json:"storage_limit" validate:"min=1"`
	APILimit     int             `json:"api_limit" validate:"min=1"`
	ProductLimit int             `json:"product_limit" validate:"min=1"`
	OrderLimit   int             `json:"order_limit" validate:"min=1"`
}

// Validate 验证创建订阅命令
func (c *CreateSubscriptionCommand) Validate() error {
	if strings.TrimSpace(c.TenantID) == "" {
		return errors.New("租户ID是必需的")
	}

	if strings.TrimSpace(c.PlanID) == "" {
		return errors.New("计划ID是必需的")
	}

	if strings.TrimSpace(c.PlanName) == "" {
		return errors.New("计划名称是必需的")
	}

	if c.StartDate.IsZero() {
		return errors.New("开始日期是必需的")
	}

	if c.EndDate.IsZero() {
		return errors.New("结束日期是必需的")
	}

	if c.EndDate.Before(c.StartDate) {
		return errors.New("结束日期不能早于开始日期")
	}

	if c.Price.IsNegative() {
		return errors.New("价格不能为负数")
	}

	if len(c.Currency) != 3 {
		return errors.New("货币代码必须是3位字符")
	}

	validCycles := []string{"monthly", "yearly", "lifetime"}
	isValidCycle := false
	for _, validCycle := range validCycles {
		if c.BillingCycle == validCycle {
			isValidCycle = true
			break
		}
	}
	if !isValidCycle {
		return errors.New("无效的计费周期")
	}

	return nil
}

// CreateSubscriptionResult 创建订阅结果
type CreateSubscriptionResult struct {
	SubscriptionID string    `json:"subscription_id"`
	TenantID       string    `json:"tenant_id"`
	PlanID         string    `json:"plan_id"`
	Status         string    `json:"status"`
	CreatedAt      time.Time `json:"created_at"`
}

// RenewSubscriptionCommand 续费订阅命令
type RenewSubscriptionCommand struct {
	SubscriptionID string          `json:"subscription_id" validate:"required"`
	StartDate      time.Time       `json:"start_date" validate:"required"`
	EndDate        time.Time       `json:"end_date" validate:"required"`
	Price          decimal.Decimal `json:"price" validate:"required"`
	Currency       string          `json:"currency" validate:"required,len=3"`
	BillingCycle   string          `json:"billing_cycle" validate:"required,oneof=monthly yearly lifetime"`
}

// Validate 验证续费订阅命令
func (c *RenewSubscriptionCommand) Validate() error {
	if strings.TrimSpace(c.SubscriptionID) == "" {
		return errors.New("订阅ID是必需的")
	}

	if c.StartDate.IsZero() {
		return errors.New("开始日期是必需的")
	}

	if c.EndDate.IsZero() {
		return errors.New("结束日期是必需的")
	}

	if c.EndDate.Before(c.StartDate) {
		return errors.New("结束日期不能早于开始日期")
	}

	if c.Price.IsNegative() {
		return errors.New("价格不能为负数")
	}

	return nil
}

// RenewSubscriptionResult 续费订阅结果
type RenewSubscriptionResult struct {
	SubscriptionID string    `json:"subscription_id"`
	NewEndDate     time.Time `json:"new_end_date"`
	RenewedAt      time.Time `json:"renewed_at"`
}

// CancelSubscriptionCommand 取消订阅命令
type CancelSubscriptionCommand struct {
	SubscriptionID string  `json:"subscription_id" validate:"required"`
	Reason         string  `json:"reason" validate:"required,max=500"`
	RefundAmount   float64 `json:"refund_amount,omitempty" validate:"omitempty,min=0"`
	RefundReason   string  `json:"refund_reason,omitempty" validate:"omitempty,max=500"`
}

// Validate 验证取消订阅命令
func (c *CancelSubscriptionCommand) Validate() error {
	if strings.TrimSpace(c.SubscriptionID) == "" {
		return errors.New("订阅ID是必需的")
	}

	if strings.TrimSpace(c.Reason) == "" {
		return errors.New("取消原因是必需的")
	}

	return nil
}

// CancelSubscriptionResult 取消订阅结果
type CancelSubscriptionResult struct {
	SubscriptionID string    `json:"subscription_id"`
	Status         string    `json:"status"`
	CanceledAt     time.Time `json:"canceled_at"`
	RefundAmount   float64   `json:"refund_amount,omitempty"`
}

// ExtendTrialCommand 延长试用期命令
type ExtendTrialCommand struct {
	SubscriptionID string `json:"subscription_id" validate:"required"`
	Days           int    `json:"days" validate:"required,min=1,max=90"`
	Reason         string `json:"reason" validate:"required,max=500"`
}

// Validate 验证延长试用期命令
func (c *ExtendTrialCommand) Validate() error {
	if strings.TrimSpace(c.SubscriptionID) == "" {
		return errors.New("订阅ID是必需的")
	}

	if c.Days <= 0 || c.Days > 90 {
		return errors.New("延长天数必须在1到90天之间")
	}

	if strings.TrimSpace(c.Reason) == "" {
		return errors.New("延长原因是必需的")
	}

	return nil
}

// ExtendTrialResult 延长试用期结果
type ExtendTrialResult struct {
	SubscriptionID string    `json:"subscription_id"`
	NewEndDate     time.Time `json:"new_end_date"`
	ExtendedDays   int       `json:"extended_days"`
	ExtendedAt     time.Time `json:"extended_at"`
}
