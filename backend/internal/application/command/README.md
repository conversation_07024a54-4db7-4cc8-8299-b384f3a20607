# Command Layer (命令层)

## 目录结构
```
command/
├── handler/      # 命令处理器实现
├── model/        # 命令模型定义  
└── gateway/      # 命令网关（路由）
```

## 职责说明

### handler/ - 命令处理器
- 处理具体的命令逻辑
- 每个领域一个处理器
- 负责调用领域服务和仓储
- 发布领域事件

### model/ - 命令模型
- 定义各种命令结构
- 继承BaseCommand
- 包含命令验证逻辑

### gateway/ - 命令网关
- 命令路由和分发
- 命令验证和授权
- 统一的命令入口

## 适用场景
- 简单CRUD操作
- 单一聚合根操作
- 无复杂事务协调

## 示例操作
- CreateProduct
- UpdateProductPrice  
- DeleteProduct
- CreateCategory
- UpdateTenantConfig 