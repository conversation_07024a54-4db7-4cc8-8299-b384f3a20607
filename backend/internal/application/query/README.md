# Query Layer (查询层)

## 目录结构
```
query/
├── handler/      # 查询处理器实现
├── model/        # 查询模型定义
├── view/         # 查询视图模型  
└── gateway/      # 查询网关（路由）
```

## 职责说明

### handler/ - 查询处理器
- 处理所有查询请求
- 每个领域一个处理器
- 可以访问读模型和写模型
- 优化查询性能

### model/ - 查询模型
- 定义各种查询结构
- 继承BaseQuery或PagedQuery
- 包含查询参数和过滤条件

### view/ - 查询视图模型
- 针对查询优化的数据模型
- 可以聚合多个数据源
- 支持复杂的查询场景

### gateway/ - 查询网关
- 查询路由和分发
- 查询授权检查
- 缓存策略控制

## 适用场景
- 所有读操作
- 数据聚合查询
- 报表和统计
- 搜索功能

## 示例操作
- GetProductByID
- SearchProducts
- GetOrderHistory
- InventoryReport
- FinancialDashboard 