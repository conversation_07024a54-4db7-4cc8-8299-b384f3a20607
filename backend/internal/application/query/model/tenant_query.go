package model

import (
	"errors"
	"strings"
	"time"
)

// ==================== 租户查询模型 ====================

// GetTenantByIDQuery 根据ID获取租户查询
type GetTenantByIDQuery struct {
	TenantID string `json:"tenant_id" validate:"required"`
}

// Validate 验证查询参数
func (q *GetTenantByIDQuery) Validate() error {
	if strings.TrimSpace(q.TenantID) == "" {
		return errors.New("租户ID是必需的")
	}
	return nil
}

// GetTenantByDomainQuery 根据域名获取租户查询
type GetTenantByDomainQuery struct {
	Domain string `json:"domain" validate:"required"`
}

// Validate 验证查询参数
func (q *GetTenantByDomainQuery) Validate() error {
	if strings.TrimSpace(q.Domain) == "" {
		return errors.New("域名是必需的")
	}
	return nil
}

// ListTenantsQuery 租户列表查询
type ListTenantsQuery struct {
	// 过滤条件
	Name     string `json:"name,omitempty"`
	Type     string `json:"type,omitempty"`
	Status   string `json:"status,omitempty"`
	Industry string `json:"industry,omitempty"`
	Country  string `json:"country,omitempty"`
	Province string `json:"province,omitempty"`
	City     string `json:"city,omitempty"`
	Keywords string `json:"keywords,omitempty"`

	// 时间过滤
	CreatedFrom *time.Time `json:"created_from,omitempty"`
	CreatedTo   *time.Time `json:"created_to,omitempty"`
	UpdatedFrom *time.Time `json:"updated_from,omitempty"`
	UpdatedTo   *time.Time `json:"updated_to,omitempty"`

	// 分页参数
	Page     int    `json:"page" validate:"min=1"`
	PageSize int    `json:"page_size" validate:"min=1,max=100"`
	SortBy   string `json:"sort_by,omitempty"`
	SortDesc bool   `json:"sort_desc,omitempty"`
}

// Validate 验证查询参数
func (q *ListTenantsQuery) Validate() error {
	if q.Page <= 0 {
		q.Page = 1
	}
	if q.PageSize <= 0 {
		q.PageSize = 10
	}
	if q.PageSize > 100 {
		q.PageSize = 100
	}
	if q.SortBy == "" {
		q.SortBy = "created_at"
	}

	// 验证租户类型
	if q.Type != "" {
		validTypes := []string{"system", "enterprise", "professional", "basic", "trial"}
		isValid := false
		for _, validType := range validTypes {
			if q.Type == validType {
				isValid = true
				break
			}
		}
		if !isValid {
			return errors.New("无效的租户类型")
		}
	}

	// 验证租户状态
	if q.Status != "" {
		validStatuses := []string{"inactive", "active", "suspended", "terminated", "expired"}
		isValid := false
		for _, validStatus := range validStatuses {
			if q.Status == validStatus {
				isValid = true
				break
			}
		}
		if !isValid {
			return errors.New("无效的租户状态")
		}
	}

	return nil
}

// SearchTenantsQuery 搜索租户查询
type SearchTenantsQuery struct {
	Query  string   `json:"query" validate:"required,min=1"`
	Fields []string `json:"fields,omitempty"` // name, domain, description, industry

	// 分页参数
	Page     int    `json:"page" validate:"min=1"`
	PageSize int    `json:"page_size" validate:"min=1,max=100"`
	SortBy   string `json:"sort_by,omitempty"`
	SortDesc bool   `json:"sort_desc,omitempty"`
}

// Validate 验证查询参数
func (q *SearchTenantsQuery) Validate() error {
	if strings.TrimSpace(q.Query) == "" {
		return errors.New("搜索关键词是必需的")
	}

	if q.Page <= 0 {
		q.Page = 1
	}
	if q.PageSize <= 0 {
		q.PageSize = 10
	}
	if q.PageSize > 100 {
		q.PageSize = 100
	}
	if q.SortBy == "" {
		q.SortBy = "created_at"
	}

	// 验证搜索字段
	if len(q.Fields) > 0 {
		validFields := []string{"name", "domain", "description", "industry"}
		for _, field := range q.Fields {
			isValid := false
			for _, validField := range validFields {
				if field == validField {
					isValid = true
					break
				}
			}
			if !isValid {
				return errors.New("无效的搜索字段: " + field)
			}
		}
	}

	return nil
}

// GetTenantHealthQuery 获取租户健康状态查询
type GetTenantHealthQuery struct {
	TenantID            string `json:"tenant_id" validate:"required"`
	IncludeQuota        bool   `json:"include_quota,omitempty"`
	IncludeSubscription bool   `json:"include_subscription,omitempty"`
}

// Validate 验证查询参数
func (q *GetTenantHealthQuery) Validate() error {
	if strings.TrimSpace(q.TenantID) == "" {
		return errors.New("租户ID是必需的")
	}
	return nil
}

// GetTenantStatisticsQuery 获取租户统计查询
type GetTenantStatisticsQuery struct {
	StartDate *time.Time `json:"start_date,omitempty"`
	EndDate   *time.Time `json:"end_date,omitempty"`
	Type      string     `json:"type,omitempty"`
	Status    string     `json:"status,omitempty"`
	Country   string     `json:"country,omitempty"`
	Province  string     `json:"province,omitempty"`
	Industry  string     `json:"industry,omitempty"`
	GroupBy   string     `json:"group_by,omitempty"`
}

// Validate 验证查询参数
func (q *GetTenantStatisticsQuery) Validate() error {
	// 验证分组方式
	if q.GroupBy != "" {
		validGroupBy := []string{"day", "week", "month", "year"}
		isValid := false
		for _, valid := range validGroupBy {
			if q.GroupBy == valid {
				isValid = true
				break
			}
		}
		if !isValid {
			return errors.New("无效的分组方式")
		}
	}

	return nil
}

// ==================== 配额查询模型 ====================

// GetTenantQuotaQuery 获取租户配额查询
type GetTenantQuotaQuery struct {
	TenantID string `json:"tenant_id" validate:"required"`
}

// Validate 验证查询参数
func (q *GetTenantQuotaQuery) Validate() error {
	if strings.TrimSpace(q.TenantID) == "" {
		return errors.New("租户ID是必需的")
	}
	return nil
}

// GetQuotaUsageQuery 获取配额使用情况查询
type GetQuotaUsageQuery struct {
	TenantID   string     `json:"tenant_id" validate:"required"`
	QuotaTypes []string   `json:"quota_types,omitempty"`
	StartDate  *time.Time `json:"start_date,omitempty"`
	EndDate    *time.Time `json:"end_date,omitempty"`
}

// Validate 验证查询参数
func (q *GetQuotaUsageQuery) Validate() error {
	if strings.TrimSpace(q.TenantID) == "" {
		return errors.New("租户ID是必需的")
	}

	// 验证配额类型
	if len(q.QuotaTypes) > 0 {
		validTypes := []string{"user", "storage", "api", "product", "order", "file_upload", "email"}
		for _, quotaType := range q.QuotaTypes {
			isValid := false
			for _, validType := range validTypes {
				if quotaType == validType {
					isValid = true
					break
				}
			}
			if !isValid {
				return errors.New("无效的配额类型: " + quotaType)
			}
		}
	}

	return nil
}

// GetQuotaAlertsQuery 获取配额告警查询
type GetQuotaAlertsQuery struct {
	TenantID  string `json:"tenant_id" validate:"required"`
	AlertType string `json:"alert_type,omitempty"`
	Level     string `json:"level,omitempty"`
}

// Validate 验证查询参数
func (q *GetQuotaAlertsQuery) Validate() error {
	if strings.TrimSpace(q.TenantID) == "" {
		return errors.New("租户ID是必需的")
	}

	// 验证告警级别
	if q.Level != "" {
		validLevels := []string{"warning", "critical"}
		isValid := false
		for _, validLevel := range validLevels {
			if q.Level == validLevel {
				isValid = true
				break
			}
		}
		if !isValid {
			return errors.New("无效的告警级别")
		}
	}

	return nil
}

// ==================== 订阅查询模型 ====================

// GetSubscriptionQuery 获取订阅查询
type GetSubscriptionQuery struct {
	SubscriptionID string `json:"subscription_id" validate:"required"`
}

// Validate 验证查询参数
func (q *GetSubscriptionQuery) Validate() error {
	if strings.TrimSpace(q.SubscriptionID) == "" {
		return errors.New("订阅ID是必需的")
	}
	return nil
}

// GetSubscriptionHistoryQuery 获取订阅历史查询
type GetSubscriptionHistoryQuery struct {
	TenantID string `json:"tenant_id" validate:"required"`
	Page     int    `json:"page"`
	PageSize int    `json:"page_size"`
	SortBy   string `json:"sort_by"`
	SortDesc bool   `json:"sort_desc"`
}

// Validate 验证查询参数
func (q *GetSubscriptionHistoryQuery) Validate() error {
	if strings.TrimSpace(q.TenantID) == "" {
		return errors.New("租户ID是必需的")
	}

	// 设置默认分页参数
	if q.Page <= 0 {
		q.Page = 1
	}
	if q.PageSize <= 0 {
		q.PageSize = 20
	}
	if q.PageSize > 100 {
		q.PageSize = 100
	}
	if q.SortBy == "" {
		q.SortBy = "created_at"
	}

	return nil
}

// GetSubscriptionStatisticsQuery 获取订阅统计查询
type GetSubscriptionStatisticsQuery struct {
	StartDate    *time.Time `json:"start_date,omitempty"`
	EndDate      *time.Time `json:"end_date,omitempty"`
	Currency     string     `json:"currency,omitempty"`
	BillingCycle string     `json:"billing_cycle,omitempty"`
	GroupBy      string     `json:"group_by,omitempty"` // day, week, month, year
}

// Validate 验证查询参数
func (q *GetSubscriptionStatisticsQuery) Validate() error {
	// 验证计费周期
	if q.BillingCycle != "" {
		validCycles := []string{"monthly", "yearly", "lifetime"}
		valid := false
		for _, cycle := range validCycles {
			if q.BillingCycle == cycle {
				valid = true
				break
			}
		}
		if !valid {
			return errors.New("无效的计费周期")
		}
	}

	// 验证分组方式
	if q.GroupBy != "" {
		validGroupBy := []string{"day", "week", "month", "year"}
		valid := false
		for _, group := range validGroupBy {
			if q.GroupBy == group {
				valid = true
				break
			}
		}
		if !valid {
			return errors.New("无效的分组方式")
		}
	}

	// 设置默认分组方式
	if q.GroupBy == "" {
		q.GroupBy = "month"
	}

	return nil
}

// GetTenantSubscriptionQuery 获取租户订阅查询
type GetTenantSubscriptionQuery struct {
	TenantID string `json:"tenant_id" validate:"required"`
}

// Validate 验证查询参数
func (q *GetTenantSubscriptionQuery) Validate() error {
	if strings.TrimSpace(q.TenantID) == "" {
		return errors.New("租户ID是必需的")
	}
	return nil
}

// ListSubscriptionsQuery 订阅列表查询
type ListSubscriptionsQuery struct {
	// 过滤条件
	TenantID       string `json:"tenant_id,omitempty"`
	PlanID         string `json:"plan_id,omitempty"`
	PlanName       string `json:"plan_name,omitempty"`
	Status         string `json:"status,omitempty"`
	IsTrialPeriod  *bool  `json:"is_trial_period,omitempty"`
	AutoRenewal    *bool  `json:"auto_renewal,omitempty"`
	Currency       string `json:"currency,omitempty"`
	BillingCycle   string `json:"billing_cycle,omitempty"`
	ExpiringInDays int    `json:"expiring_in_days,omitempty"`

	// 时间过滤
	CreatedFrom *time.Time `json:"created_from,omitempty"`
	CreatedTo   *time.Time `json:"created_to,omitempty"`

	// 分页参数
	Page     int    `json:"page" validate:"min=1"`
	PageSize int    `json:"page_size" validate:"min=1,max=100"`
	SortBy   string `json:"sort_by,omitempty"`
	SortDesc bool   `json:"sort_desc,omitempty"`
}

// Validate 验证查询参数
func (q *ListSubscriptionsQuery) Validate() error {
	if q.Page <= 0 {
		q.Page = 1
	}
	if q.PageSize <= 0 {
		q.PageSize = 10
	}
	if q.PageSize > 100 {
		q.PageSize = 100
	}
	if q.SortBy == "" {
		q.SortBy = "created_at"
	}

	// 验证订阅状态
	if q.Status != "" {
		validStatuses := []string{"pending", "active", "suspended", "canceled", "expired"}
		isValid := false
		for _, validStatus := range validStatuses {
			if q.Status == validStatus {
				isValid = true
				break
			}
		}
		if !isValid {
			return errors.New("无效的订阅状态")
		}
	}

	// 验证计费周期
	if q.BillingCycle != "" {
		validCycles := []string{"monthly", "yearly", "lifetime"}
		isValid := false
		for _, validCycle := range validCycles {
			if q.BillingCycle == validCycle {
				isValid = true
				break
			}
		}
		if !isValid {
			return errors.New("无效的计费周期")
		}
	}

	return nil
}
