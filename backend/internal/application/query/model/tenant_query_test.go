package model

import (
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
)

// TestGetTenantByIDQuery_Validate 测试根据ID获取租户查询验证
func TestGetTenantByIDQuery_Validate(t *testing.T) {
	t.Run("有效的查询", func(t *testing.T) {
		query := &GetTenantByIDQuery{
			TenantID: "tenant-123",
		}

		err := query.Validate()
		assert.NoError(t, err)
	})

	t.Run("租户ID为空", func(t *testing.T) {
		query := &GetTenantByIDQuery{
			TenantID: "",
		}

		err := query.Validate()
		assert.Error(t, err)
		assert.Contains(t, err.Error(), "租户ID是必需的")
	})

	t.Run("租户ID只有空格", func(t *testing.T) {
		query := &GetTenantByIDQuery{
			TenantID: "   ",
		}

		err := query.Validate()
		assert.Error(t, err)
		assert.Contains(t, err.Error(), "租户ID是必需的")
	})
}

// TestGetTenantByDomainQuery_Validate 测试根据域名获取租户查询验证
func TestGetTenantByDomainQuery_Validate(t *testing.T) {
	t.Run("有效的查询", func(t *testing.T) {
		query := &GetTenantByDomainQuery{
			Domain: "test.example.com",
		}

		err := query.Validate()
		assert.NoError(t, err)
	})

	t.Run("域名为空", func(t *testing.T) {
		query := &GetTenantByDomainQuery{
			Domain: "",
		}

		err := query.Validate()
		assert.Error(t, err)
		assert.Contains(t, err.Error(), "域名是必需的")
	})
}

// TestListTenantsQuery_Validate 测试租户列表查询验证
func TestListTenantsQuery_Validate(t *testing.T) {
	t.Run("有效的查询", func(t *testing.T) {
		query := &ListTenantsQuery{
			Name:     "测试租户",
			Type:     "basic",
			Status:   "active",
			Industry: "科技",
			Country:  "中国",
			Province: "北京",
			City:     "北京",
			Page:     1,
			PageSize: 10,
			SortBy:   "created_at",
			SortDesc: false,
		}

		err := query.Validate()
		assert.NoError(t, err)
		assert.Equal(t, 1, query.Page)
		assert.Equal(t, 10, query.PageSize)
		assert.Equal(t, "created_at", query.SortBy)
	})

	t.Run("默认分页参数", func(t *testing.T) {
		query := &ListTenantsQuery{}

		err := query.Validate()
		assert.NoError(t, err)
		assert.Equal(t, 1, query.Page)
		assert.Equal(t, 10, query.PageSize)
		assert.Equal(t, "created_at", query.SortBy)
	})

	t.Run("页面大小超过限制", func(t *testing.T) {
		query := &ListTenantsQuery{
			Page:     1,
			PageSize: 200,
		}

		err := query.Validate()
		assert.NoError(t, err)
		assert.Equal(t, 100, query.PageSize) // 应该被限制为100
	})

	t.Run("无效的租户类型", func(t *testing.T) {
		query := &ListTenantsQuery{
			Type: "invalid",
		}

		err := query.Validate()
		assert.Error(t, err)
		assert.Contains(t, err.Error(), "无效的租户类型")
	})

	t.Run("无效的租户状态", func(t *testing.T) {
		query := &ListTenantsQuery{
			Status: "invalid",
		}

		err := query.Validate()
		assert.Error(t, err)
		assert.Contains(t, err.Error(), "无效的租户状态")
	})
}

// TestSearchTenantsQuery_Validate 测试搜索租户查询验证
func TestSearchTenantsQuery_Validate(t *testing.T) {
	t.Run("有效的搜索查询", func(t *testing.T) {
		query := &SearchTenantsQuery{
			Query:    "测试",
			Fields:   []string{"name", "domain", "description"},
			Page:     1,
			PageSize: 20,
			SortBy:   "created_at",
			SortDesc: true,
		}

		err := query.Validate()
		assert.NoError(t, err)
	})

	t.Run("搜索关键词为空", func(t *testing.T) {
		query := &SearchTenantsQuery{
			Query: "",
		}

		err := query.Validate()
		assert.Error(t, err)
		assert.Contains(t, err.Error(), "搜索关键词是必需的")
	})

	t.Run("搜索关键词只有空格", func(t *testing.T) {
		query := &SearchTenantsQuery{
			Query: "   ",
		}

		err := query.Validate()
		assert.Error(t, err)
		assert.Contains(t, err.Error(), "搜索关键词是必需的")
	})

	t.Run("无效的搜索字段", func(t *testing.T) {
		query := &SearchTenantsQuery{
			Query:  "测试",
			Fields: []string{"name", "invalid_field"},
		}

		err := query.Validate()
		assert.Error(t, err)
		assert.Contains(t, err.Error(), "无效的搜索字段")
	})
}

// TestGetTenantHealthQuery_Validate 测试获取租户健康状态查询验证
func TestGetTenantHealthQuery_Validate(t *testing.T) {
	t.Run("有效的健康状态查询", func(t *testing.T) {
		query := &GetTenantHealthQuery{
			TenantID:            "tenant-123",
			IncludeQuota:        true,
			IncludeSubscription: true,
		}

		err := query.Validate()
		assert.NoError(t, err)
	})

	t.Run("租户ID为空", func(t *testing.T) {
		query := &GetTenantHealthQuery{
			TenantID: "",
		}

		err := query.Validate()
		assert.Error(t, err)
		assert.Contains(t, err.Error(), "租户ID是必需的")
	})
}

// TestGetTenantQuotaQuery_Validate 测试获取租户配额查询验证
func TestGetTenantQuotaQuery_Validate(t *testing.T) {
	t.Run("有效的配额查询", func(t *testing.T) {
		query := &GetTenantQuotaQuery{
			TenantID: "tenant-123",
		}

		err := query.Validate()
		assert.NoError(t, err)
	})

	t.Run("租户ID为空", func(t *testing.T) {
		query := &GetTenantQuotaQuery{
			TenantID: "",
		}

		err := query.Validate()
		assert.Error(t, err)
		assert.Contains(t, err.Error(), "租户ID是必需的")
	})
}

// TestGetQuotaUsageQuery_Validate 测试获取配额使用情况查询验证
func TestGetQuotaUsageQuery_Validate(t *testing.T) {
	t.Run("有效的配额使用查询", func(t *testing.T) {
		startDate := time.Now().AddDate(0, -1, 0)
		endDate := time.Now()
		query := &GetQuotaUsageQuery{
			TenantID:   "tenant-123",
			QuotaTypes: []string{"user", "storage", "api"},
			StartDate:  &startDate,
			EndDate:    &endDate,
		}

		err := query.Validate()
		assert.NoError(t, err)
	})

	t.Run("租户ID为空", func(t *testing.T) {
		query := &GetQuotaUsageQuery{
			TenantID: "",
		}

		err := query.Validate()
		assert.Error(t, err)
		assert.Contains(t, err.Error(), "租户ID是必需的")
	})

	t.Run("无效的配额类型", func(t *testing.T) {
		query := &GetQuotaUsageQuery{
			TenantID:   "tenant-123",
			QuotaTypes: []string{"user", "invalid_type"},
		}

		err := query.Validate()
		assert.Error(t, err)
		assert.Contains(t, err.Error(), "无效的配额类型")
	})
}

// TestGetQuotaAlertsQuery_Validate 测试获取配额告警查询验证
func TestGetQuotaAlertsQuery_Validate(t *testing.T) {
	t.Run("有效的配额告警查询", func(t *testing.T) {
		query := &GetQuotaAlertsQuery{
			TenantID:  "tenant-123",
			AlertType: "quota_exceeded",
			Level:     "warning",
		}

		err := query.Validate()
		assert.NoError(t, err)
	})

	t.Run("租户ID为空", func(t *testing.T) {
		query := &GetQuotaAlertsQuery{
			TenantID: "",
		}

		err := query.Validate()
		assert.Error(t, err)
		assert.Contains(t, err.Error(), "租户ID是必需的")
	})

	t.Run("无效的告警级别", func(t *testing.T) {
		query := &GetQuotaAlertsQuery{
			TenantID: "tenant-123",
			Level:    "invalid",
		}

		err := query.Validate()
		assert.Error(t, err)
		assert.Contains(t, err.Error(), "无效的告警级别")
	})
}

// TestGetSubscriptionQuery_Validate 测试获取订阅查询验证
func TestGetSubscriptionQuery_Validate(t *testing.T) {
	t.Run("有效的订阅查询", func(t *testing.T) {
		query := &GetSubscriptionQuery{
			SubscriptionID: "subscription-123",
		}

		err := query.Validate()
		assert.NoError(t, err)
	})

	t.Run("订阅ID为空", func(t *testing.T) {
		query := &GetSubscriptionQuery{
			SubscriptionID: "",
		}

		err := query.Validate()
		assert.Error(t, err)
		assert.Contains(t, err.Error(), "订阅ID是必需的")
	})
}

// TestListSubscriptionsQuery_Validate 测试订阅列表查询验证
func TestListSubscriptionsQuery_Validate(t *testing.T) {
	t.Run("有效的订阅列表查询", func(t *testing.T) {
		autoRenewal := true
		query := &ListSubscriptionsQuery{
			TenantID:     "tenant-123",
			PlanID:       "plan-basic",
			Status:       "active",
			AutoRenewal:  &autoRenewal,
			Currency:     "CNY",
			BillingCycle: "monthly",
			Page:         1,
			PageSize:     20,
		}

		err := query.Validate()
		assert.NoError(t, err)
	})

	t.Run("无效的订阅状态", func(t *testing.T) {
		query := &ListSubscriptionsQuery{
			Status: "invalid",
		}

		err := query.Validate()
		assert.Error(t, err)
		assert.Contains(t, err.Error(), "无效的订阅状态")
	})

	t.Run("无效的计费周期", func(t *testing.T) {
		query := &ListSubscriptionsQuery{
			BillingCycle: "invalid",
		}

		err := query.Validate()
		assert.Error(t, err)
		assert.Contains(t, err.Error(), "无效的计费周期")
	})
}
