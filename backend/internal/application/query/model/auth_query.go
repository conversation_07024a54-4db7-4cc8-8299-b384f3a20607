package model

import (
	"fmt"
	"time"

	"github.com/go-playground/validator/v10"
)

// ==================== 认证查询 ====================

// ValidateTokenQuery 验证令牌查询
type ValidateTokenQuery struct {
	Token string `json:"token" validate:"required"`
}

// Validate 验证令牌查询验证
func (q *ValidateTokenQuery) Validate() error {
	validate := validator.New()
	return validate.Struct(q)
}

// GetUserAuthQuery 获取用户认证信息查询
type GetUserAuthQuery struct {
	UserID string `json:"user_id" validate:"required,max=36"`
}

// Validate 验证获取用户认证信息查询
func (q *GetUserAuthQuery) Validate() error {
	validate := validator.New()
	return validate.Struct(q)
}

// CheckPermissionQuery 检查权限查询
type CheckPermissionQuery struct {
	UserID   string `json:"user_id" validate:"required,max=36"`
	TenantID string `json:"tenant_id" validate:"required,max=36"`
	Resource string `json:"resource" validate:"required,max=100"`
	Action   string `json:"action" validate:"required,max=50"`
}

// Validate 验证检查权限查询
func (q *CheckPermissionQuery) Validate() error {
	validate := validator.New()
	return validate.Struct(q)
}

// ==================== 角色权限查询 ====================

// GetUserRolesQuery 获取用户角色查询
type GetUserRolesQuery struct {
	UserID   string `json:"user_id" validate:"required,max=36"`
	TenantID string `json:"tenant_id" validate:"required,max=36"`
}

// Validate 验证获取用户角色查询
func (q *GetUserRolesQuery) Validate() error {
	validate := validator.New()
	return validate.Struct(q)
}

// GetRolePermissionsQuery 获取角色权限查询
type GetRolePermissionsQuery struct {
	RoleID   string `json:"role_id" validate:"required,max=36"`
	TenantID string `json:"tenant_id" validate:"required,max=36"`
}

// Validate 验证获取角色权限查询
func (q *GetRolePermissionsQuery) Validate() error {
	validate := validator.New()
	return validate.Struct(q)
}

// ListRolesQuery 角色列表查询
type ListRolesQuery struct {
	TenantID string `json:"tenant_id" validate:"required,max=36"`
	Page     int    `json:"page"`
	PageSize int    `json:"page_size"`
	SortBy   string `json:"sort_by" validate:"omitempty,oneof=name created_at updated_at"`
	SortDesc bool   `json:"sort_desc"`
}

// Validate 验证角色列表查询
func (q *ListRolesQuery) Validate() error {
	// 先设置默认值
	if q.Page <= 0 {
		q.Page = 1
	}
	if q.PageSize <= 0 {
		q.PageSize = 20
	}
	if q.PageSize > 100 {
		q.PageSize = 100
	}
	if q.SortBy == "" {
		q.SortBy = "created_at"
	}

	// 然后进行验证
	validate := validator.New()
	if err := validate.Struct(q); err != nil {
		return fmt.Errorf("角色列表查询验证失败: %w", err)
	}

	return nil
}

// ==================== 会话查询 ====================

// GetSessionQuery 获取会话查询
type GetSessionQuery struct {
	SessionID string `json:"session_id" validate:"required,max=36"`
}

// Validate 验证获取会话查询
func (q *GetSessionQuery) Validate() error {
	validate := validator.New()
	return validate.Struct(q)
}

// ListUserSessionsQuery 用户会话列表查询
type ListUserSessionsQuery struct {
	UserID   string `json:"user_id" validate:"required,max=36"`
	TenantID string `json:"tenant_id,omitempty" validate:"omitempty,max=36"`
	Status   string `json:"status,omitempty" validate:"omitempty,oneof=active inactive expired"`
	Page     int    `json:"page"`
	PageSize int    `json:"page_size"`
}

// Validate 验证用户会话列表查询
func (q *ListUserSessionsQuery) Validate() error {
	// 先设置默认值
	if q.Page <= 0 {
		q.Page = 1
	}
	if q.PageSize <= 0 {
		q.PageSize = 20
	}
	if q.PageSize > 100 {
		q.PageSize = 100
	}

	// 然后进行验证
	validate := validator.New()
	if err := validate.Struct(q); err != nil {
		return fmt.Errorf("用户会话列表查询验证失败: %w", err)
	}

	return nil
}

// ==================== 安全审计查询 ====================

// GetSecurityEventsQuery 获取安全事件查询
type GetSecurityEventsQuery struct {
	UserID    string    `json:"user_id,omitempty" validate:"omitempty,max=36"`
	TenantID  string    `json:"tenant_id,omitempty" validate:"omitempty,max=36"`
	EventType string    `json:"event_type,omitempty" validate:"omitempty,max=50"`
	StartTime time.Time `json:"start_time,omitempty"`
	EndTime   time.Time `json:"end_time,omitempty"`
	Page      int       `json:"page"`
	PageSize  int       `json:"page_size"`
}

// Validate 验证获取安全事件查询
func (q *GetSecurityEventsQuery) Validate() error {
	// 先设置默认值
	if q.Page <= 0 {
		q.Page = 1
	}
	if q.PageSize <= 0 {
		q.PageSize = 20
	}
	if q.PageSize > 100 {
		q.PageSize = 100
	}

	// 验证时间范围
	if !q.StartTime.IsZero() && !q.EndTime.IsZero() && q.StartTime.After(q.EndTime) {
		return fmt.Errorf("开始时间不能晚于结束时间")
	}

	// 然后进行验证
	validate := validator.New()
	if err := validate.Struct(q); err != nil {
		return fmt.Errorf("安全事件查询验证失败: %w", err)
	}

	return nil
}

// GetLoginAttemptsQuery 获取登录尝试查询
type GetLoginAttemptsQuery struct {
	UserID    string    `json:"user_id,omitempty" validate:"omitempty,max=36"`
	IPAddress string    `json:"ip_address,omitempty" validate:"omitempty,ip"`
	StartTime time.Time `json:"start_time,omitempty"`
	EndTime   time.Time `json:"end_time,omitempty"`
	Success   *bool     `json:"success,omitempty"`
	Page      int       `json:"page"`
	PageSize  int       `json:"page_size"`
}

// Validate 验证获取登录尝试查询
func (q *GetLoginAttemptsQuery) Validate() error {
	// 先设置默认值
	if q.Page <= 0 {
		q.Page = 1
	}
	if q.PageSize <= 0 {
		q.PageSize = 20
	}
	if q.PageSize > 100 {
		q.PageSize = 100
	}

	// 然后进行验证
	validate := validator.New()
	if err := validate.Struct(q); err != nil {
		return fmt.Errorf("登录尝试查询验证失败: %w", err)
	}

	return nil
}

// ==================== 查询结果 ====================

// TokenValidationResult 令牌验证结果
type TokenValidationResult struct {
	Valid       bool      `json:"valid"`
	UserID      string    `json:"user_id,omitempty"`
	TenantID    string    `json:"tenant_id,omitempty"`
	Roles       []string  `json:"roles,omitempty"`
	Permissions []string  `json:"permissions,omitempty"`
	ExpiresAt   time.Time `json:"expires_at,omitempty"`
	Error       string    `json:"error,omitempty"`
}

// UserAuthResult 用户认证结果
type UserAuthResult struct {
	UserID      string    `json:"user_id"`
	Username    string    `json:"username"`
	Email       string    `json:"email"`
	Phone       string    `json:"phone"`
	DisplayName string    `json:"display_name"`
	Avatar      string    `json:"avatar"`
	Status      string    `json:"status"`
	LastLoginAt time.Time `json:"last_login_at"`
	CreatedAt   time.Time `json:"created_at"`
	UpdatedAt   time.Time `json:"updated_at"`
}

// PermissionCheckResult 权限检查结果
type PermissionCheckResult struct {
	Allowed  bool     `json:"allowed"`
	UserID   string   `json:"user_id"`
	TenantID string   `json:"tenant_id"`
	Resource string   `json:"resource"`
	Action   string   `json:"action"`
	Roles    []string `json:"roles"`
	Reason   string   `json:"reason,omitempty"`
}

// UserRolesResult 用户角色结果
type UserRolesResult struct {
	UserID   string `json:"user_id"`
	TenantID string `json:"tenant_id"`
	Roles    []Role `json:"roles"`
}

// Role 角色信息
type Role struct {
	RoleID      string    `json:"role_id"`
	RoleName    string    `json:"role_name"`
	Description string    `json:"description"`
	Permissions []string  `json:"permissions"`
	CreatedAt   time.Time `json:"created_at"`
}

// RolePermissionsResult 角色权限结果
type RolePermissionsResult struct {
	RoleID      string       `json:"role_id"`
	RoleName    string       `json:"role_name"`
	TenantID    string       `json:"tenant_id"`
	Permissions []Permission `json:"permissions"`
}

// Permission 权限信息
type Permission struct {
	Resource    string   `json:"resource"`
	Actions     []string `json:"actions"`
	Description string   `json:"description"`
}

// RoleListResult 角色列表结果
type RoleListResult struct {
	Roles      []Role `json:"roles"`
	Total      int64  `json:"total"`
	Page       int    `json:"page"`
	PageSize   int    `json:"page_size"`
	TotalPages int    `json:"total_pages"`
}

// SessionResult 会话结果
type SessionResult struct {
	SessionID  string                 `json:"session_id"`
	UserID     string                 `json:"user_id"`
	TenantID   string                 `json:"tenant_id"`
	DeviceID   string                 `json:"device_id"`
	DeviceInfo string                 `json:"device_info"`
	IPAddress  string                 `json:"ip_address"`
	UserAgent  string                 `json:"user_agent"`
	Status     string                 `json:"status"`
	CreatedAt  time.Time              `json:"created_at"`
	LastActive time.Time              `json:"last_active"`
	ExpiresAt  time.Time              `json:"expires_at"`
	Metadata   map[string]interface{} `json:"metadata,omitempty"`
}

// SessionListResult 会话列表结果
type SessionListResult struct {
	Sessions   []SessionResult `json:"sessions"`
	Total      int64           `json:"total"`
	Page       int             `json:"page"`
	PageSize   int             `json:"page_size"`
	TotalPages int             `json:"total_pages"`
}

// SecurityEventResult 安全事件结果
type SecurityEventResult struct {
	EventID     string                 `json:"event_id"`
	UserID      string                 `json:"user_id"`
	TenantID    string                 `json:"tenant_id"`
	EventType   string                 `json:"event_type"`
	Description string                 `json:"description"`
	IPAddress   string                 `json:"ip_address"`
	UserAgent   string                 `json:"user_agent"`
	Success     bool                   `json:"success"`
	Details     map[string]interface{} `json:"details,omitempty"`
	CreatedAt   time.Time              `json:"created_at"`
}

// SecurityEventListResult 安全事件列表结果
type SecurityEventListResult struct {
	Events     []SecurityEventResult `json:"events"`
	Total      int64                 `json:"total"`
	Page       int                   `json:"page"`
	PageSize   int                   `json:"page_size"`
	TotalPages int                   `json:"total_pages"`
}
