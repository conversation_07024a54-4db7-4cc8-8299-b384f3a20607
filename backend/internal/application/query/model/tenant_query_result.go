package model

import (
	"time"

	"github.com/shopspring/decimal"
)

// ==================== 租户查询结果 ====================

// TenantQueryResult 租户查询结果
type TenantQueryResult struct {
	TenantID      string                 `json:"tenant_id"`
	Name          string                 `json:"name"`
	Domain        string                 `json:"domain"`
	DisplayName   string                 `json:"display_name"`
	Description   string                 `json:"description"`
	Type          string                 `json:"type"`
	Status        string                 `json:"status"`
	StatusDisplay string                 `json:"status_display"`
	Industry      string                 `json:"industry"`
	Country       string                 `json:"country"`
	Province      string                 `json:"province"`
	City          string                 `json:"city"`
	Address       string                 `json:"address"`
	ContactEmail  string                 `json:"contact_email"`
	ContactPhone  string                 `json:"contact_phone"`
	Settings      map[string]interface{} `json:"settings"`

	// 配额信息
	MaxUsers    int   `json:"max_users"`
	MaxStorage  int64 `json:"max_storage"`
	MaxAPIQuota int   `json:"max_api_quota"`

	// 订阅信息
	SubscriptionPlan string     `json:"subscription_plan,omitempty"`
	ExpiryDate       *time.Time `json:"expiry_date,omitempty"`

	// 审计信息
	CreatedAt time.Time `json:"created_at"`
	UpdatedAt time.Time `json:"updated_at"`
	Version   int       `json:"version"`
}

// TenantListResult 租户列表查询结果
type TenantListResult struct {
	Items       []TenantQueryResult `json:"items"`
	Total       int64               `json:"total"`
	Page        int                 `json:"page"`
	PageSize    int                 `json:"page_size"`
	TotalPages  int                 `json:"total_pages"`
	HasNext     bool                `json:"has_next"`
	HasPrevious bool                `json:"has_previous"`
}

// TenantHealthResult 租户健康状态查询结果
type TenantHealthResult struct {
	TenantID      string                 `json:"tenant_id"`
	TenantName    string                 `json:"tenant_name"`
	Domain        string                 `json:"domain"`
	IsHealthy     bool                   `json:"is_healthy"`
	HealthScore   int                    `json:"health_score"` // 0-100分
	Alerts        []HealthAlertResult    `json:"alerts"`
	CheckedAt     time.Time              `json:"checked_at"`
	LastHealthyAt *time.Time             `json:"last_healthy_at,omitempty"`
	Details       map[string]interface{} `json:"details,omitempty"`
}

// HealthAlertResult 健康告警查询结果
type HealthAlertResult struct {
	Type      string    `json:"type"`  // tenant_status, subscription_expired, quota_exceeded
	Level     string    `json:"level"` // info, warning, critical
	Message   string    `json:"message"`
	Severity  string    `json:"severity"` // low, medium, high
	CreatedAt time.Time `json:"created_at"`
}

// TenantStatisticsResult 租户统计查询结果
type TenantStatisticsResult struct {
	TotalTenants        int64 `json:"total_tenants"`
	ActiveTenants       int64 `json:"active_tenants"`
	TrialTenants        int64 `json:"trial_tenants"`
	ExpiredTenants      int64 `json:"expired_tenants"`
	NewTenantsToday     int64 `json:"new_tenants_today"`
	NewTenantsThisMonth int64 `json:"new_tenants_this_month"`

	// 状态分布
	StatusDistribution map[string]int64 `json:"status_distribution"`
	TypeDistribution   map[string]int64 `json:"type_distribution"`

	// 地理分布
	CountryDistribution  map[string]int64 `json:"country_distribution"`
	ProvinceDistribution map[string]int64 `json:"province_distribution"`

	// 行业分布
	IndustryDistribution map[string]int64 `json:"industry_distribution"`

	// 增长趋势
	GrowthTrend []GrowthTrendResult `json:"growth_trend"`

	// 生成时间
	GeneratedAt time.Time `json:"generated_at"`
}

// GrowthTrendResult 增长趋势查询结果
type GrowthTrendResult struct {
	Date       time.Time `json:"date"`
	NewCount   int64     `json:"new_count"`
	TotalCount int64     `json:"total_count"`
	GrowthRate float64   `json:"growth_rate"` // 增长率百分比
}

// ==================== 配额查询结果 ====================

// TenantQuotaResult 租户配额查询结果
type TenantQuotaResult struct {
	TenantID string `json:"tenant_id"`

	// 用户配额
	UserUsed        int     `json:"user_used"`
	UserLimit       int     `json:"user_limit"`
	UserUtilization float64 `json:"user_utilization"`

	// 存储配额
	StorageUsed        int64   `json:"storage_used"`
	StorageLimit       int64   `json:"storage_limit"`
	StorageUtilization float64 `json:"storage_utilization"`

	// API配额
	APIUsedMonth   int       `json:"api_used_month"`
	APILimitMonth  int       `json:"api_limit_month"`
	APIUtilization float64   `json:"api_utilization"`
	APIResetDate   time.Time `json:"api_reset_date"`

	// 商品配额
	ProductUsed        int     `json:"product_used"`
	ProductLimit       int     `json:"product_limit"`
	ProductUtilization float64 `json:"product_utilization"`

	// 订单配额
	OrderUsedMonth   int     `json:"order_used_month"`
	OrderLimitMonth  int     `json:"order_limit_month"`
	OrderUtilization float64 `json:"order_utilization"`

	// 其他配额
	FileUploadUsed  int `json:"file_upload_used"`
	FileUploadLimit int `json:"file_upload_limit"`
	EmailUsedMonth  int `json:"email_used_month"`
	EmailLimitMonth int `json:"email_limit_month"`

	// 状态信息
	IsOverQuota bool                `json:"is_over_quota"`
	Alerts      []QuotaAlertResult  `json:"alerts"`
	LastUpdated time.Time           `json:"last_updated"`
}

// QuotaAlertResult 配额告警查询结果
type QuotaAlertResult struct {
	Type      string    `json:"type"`  // user, storage, api, product, order
	Level     string    `json:"level"` // warning, critical
	Message   string    `json:"message"`
	Threshold float64   `json:"threshold"` // 告警阈值
	Current   float64   `json:"current"`   // 当前使用率
	CreatedAt time.Time `json:"created_at"`
}

// QuotaUsageResult 配额使用情况查询结果
type QuotaUsageResult struct {
	TenantID   string                 `json:"tenant_id"`
	QuotaType  string                 `json:"quota_type"`
	Used       int64                  `json:"used"`
	Limit      int64                  `json:"limit"`
	Percentage float64                `json:"percentage"`
	History    []QuotaUsageHistoryResult `json:"history,omitempty"`
	UpdatedAt  time.Time              `json:"updated_at"`
}

// QuotaUsageHistoryResult 配额使用历史查询结果
type QuotaUsageHistoryResult struct {
	Date       time.Time `json:"date"`
	Used       int64     `json:"used"`
	Limit      int64     `json:"limit"`
	Percentage float64   `json:"percentage"`
}

// ==================== 订阅查询结果 ====================

// SubscriptionQueryResult 订阅查询结果
type SubscriptionQueryResult struct {
	SubscriptionID string          `json:"subscription_id"`
	TenantID       string          `json:"tenant_id"`
	PlanID         string          `json:"plan_id"`
	PlanName       string          `json:"plan_name"`
	Status         string          `json:"status"`
	StatusDisplay  string          `json:"status_display"`
	StartDate      time.Time       `json:"start_date"`
	EndDate        time.Time       `json:"end_date"`
	Price          decimal.Decimal `json:"price"`
	Currency       string          `json:"currency"`
	BillingCycle   string          `json:"billing_cycle"`
	AutoRenewal    bool            `json:"auto_renewal"`

	// 使用限制
	UserLimit    int   `json:"user_limit"`
	StorageLimit int64 `json:"storage_limit"`
	APILimit     int   `json:"api_limit"`
	ProductLimit int   `json:"product_limit"`
	OrderLimit   int   `json:"order_limit"`

	// 功能权限
	Features []string `json:"features"`

	// 试用信息
	IsTrialPeriod   bool       `json:"is_trial_period"`
	TrialEndDate    *time.Time `json:"trial_end_date,omitempty"`
	TrialExtensions int        `json:"trial_extensions"`

	// 到期信息
	DaysUntilExpiry int  `json:"days_until_expiry"`
	IsExpired       bool `json:"is_expired"`
	IsExpiringSoon  bool `json:"is_expiring_soon"`
	CanExtendTrial  bool `json:"can_extend_trial"`

	// 审计信息
	CreatedAt time.Time `json:"created_at"`
	UpdatedAt time.Time `json:"updated_at"`
	Version   int       `json:"version"`
}

// SubscriptionListResult 订阅列表查询结果
type SubscriptionListResult struct {
	Items       []SubscriptionQueryResult `json:"items"`
	Total       int64                     `json:"total"`
	Page        int                       `json:"page"`
	PageSize    int                       `json:"page_size"`
	TotalPages  int                       `json:"total_pages"`
	HasNext     bool                      `json:"has_next"`
	HasPrevious bool                      `json:"has_previous"`
}

// SubscriptionStatisticsResult 订阅统计查询结果
type SubscriptionStatisticsResult struct {
	TotalSubscriptions   int64 `json:"total_subscriptions"`
	ActiveSubscriptions  int64 `json:"active_subscriptions"`
	TrialSubscriptions   int64 `json:"trial_subscriptions"`
	ExpiredSubscriptions int64 `json:"expired_subscriptions"`

	// 状态分布
	StatusDistribution       map[string]int64 `json:"status_distribution"`
	PlanDistribution         map[string]int64 `json:"plan_distribution"`
	CurrencyDistribution     map[string]int64 `json:"currency_distribution"`
	BillingCycleDistribution map[string]int64 `json:"billing_cycle_distribution"`

	// 关键指标
	ChurnRate      float64 `json:"churn_rate"`      // 流失率
	RenewalRate    float64 `json:"renewal_rate"`    // 续费率
	AverageRevenue float64 `json:"average_revenue"` // 平均收入
	MRR            float64 `json:"mrr"`             // 月经常性收入
	ARR            float64 `json:"arr"`             // 年经常性收入

	// 收入统计
	TotalRevenue      float64            `json:"total_revenue"`
	RevenueByPlan     map[string]float64 `json:"revenue_by_plan"`
	RevenueByCurrency map[string]float64 `json:"revenue_by_currency"`

	// 生成时间
	GeneratedAt time.Time `json:"generated_at"`
}
