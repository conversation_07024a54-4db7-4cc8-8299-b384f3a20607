package model

import (
	"errors"
	"strings"
	"time"
)

// ==================== 用户查询模型 ====================

// GetUserByIDQuery 根据ID获取用户查询
type GetUserByIDQuery struct {
	UserID   string `json:"user_id" validate:"required"`
	TenantID string `json:"tenant_id" validate:"required"`
}

// Validate 验证查询参数
func (q *GetUserByIDQuery) Validate() error {
	if strings.TrimSpace(q.UserID) == "" {
		return errors.New("用户ID是必需的")
	}
	if strings.TrimSpace(q.TenantID) == "" {
		return errors.New("租户ID是必需的")
	}
	return nil
}

// GetUserByUsernameQuery 根据用户名获取用户查询
type GetUserByUsernameQuery struct {
	Username string `json:"username" validate:"required"`
	TenantID string `json:"tenant_id" validate:"required"`
}

// Validate 验证查询参数
func (q *GetUserByUsernameQuery) Validate() error {
	if strings.TrimSpace(q.Username) == "" {
		return errors.New("用户名是必需的")
	}
	if strings.TrimSpace(q.TenantID) == "" {
		return errors.New("租户ID是必需的")
	}
	return nil
}

// GetUserByEmailQuery 根据邮箱获取用户查询
type GetUserByEmailQuery struct {
	Email    string `json:"email" validate:"required,email"`
	TenantID string `json:"tenant_id" validate:"required"`
}

// Validate 验证查询参数
func (q *GetUserByEmailQuery) Validate() error {
	if strings.TrimSpace(q.Email) == "" {
		return errors.New("邮箱是必需的")
	}
	if !strings.Contains(q.Email, "@") {
		return errors.New("无效的邮箱格式")
	}
	if strings.TrimSpace(q.TenantID) == "" {
		return errors.New("租户ID是必需的")
	}
	return nil
}

// GetUserByPhoneQuery 根据手机号获取用户查询
type GetUserByPhoneQuery struct {
	Phone    string `json:"phone" validate:"required"`
	TenantID string `json:"tenant_id" validate:"required"`
}

// Validate 验证查询参数
func (q *GetUserByPhoneQuery) Validate() error {
	if strings.TrimSpace(q.Phone) == "" {
		return errors.New("手机号是必需的")
	}
	if strings.TrimSpace(q.TenantID) == "" {
		return errors.New("租户ID是必需的")
	}
	return nil
}

// ListUsersQuery 用户列表查询
type ListUsersQuery struct {
	TenantID string `json:"tenant_id" validate:"required"`
	Keyword  string `json:"keyword,omitempty"`
	Status   string `json:"status,omitempty"`
	RoleID   string `json:"role_id,omitempty"`
	Page     int    `json:"page" validate:"min=1"`
	PageSize int    `json:"page_size" validate:"min=1,max=100"`
	SortBy   string `json:"sort_by,omitempty"`
	SortDesc bool   `json:"sort_desc,omitempty"`
}

// Validate 验证查询参数
func (q *ListUsersQuery) Validate() error {
	if strings.TrimSpace(q.TenantID) == "" {
		return errors.New("租户ID是必需的")
	}
	if q.Page <= 0 {
		q.Page = 1
	}
	if q.PageSize <= 0 {
		q.PageSize = 10
	}
	if q.PageSize > 100 {
		q.PageSize = 100
	}
	if q.SortBy == "" {
		q.SortBy = "created_at"
	}
	return nil
}

// SearchUsersQuery 用户搜索查询
type SearchUsersQuery struct {
	TenantID    string     `json:"tenant_id" validate:"required"`
	Keyword     string     `json:"keyword,omitempty"`
	Status      string     `json:"status,omitempty"`
	Email       string     `json:"email,omitempty"`
	Phone       string     `json:"phone,omitempty"`
	CreatedFrom *time.Time `json:"created_from,omitempty"`
	CreatedTo   *time.Time `json:"created_to,omitempty"`
	Page        int        `json:"page" validate:"min=1"`
	PageSize    int        `json:"page_size" validate:"min=1,max=100"`
	SortBy      string     `json:"sort_by,omitempty"`
	SortDesc    bool       `json:"sort_desc,omitempty"`
}

// Validate 验证查询参数
func (q *SearchUsersQuery) Validate() error {
	if strings.TrimSpace(q.TenantID) == "" {
		return errors.New("租户ID是必需的")
	}
	if q.Page <= 0 {
		q.Page = 1
	}
	if q.PageSize <= 0 {
		q.PageSize = 10
	}
	if q.PageSize > 100 {
		q.PageSize = 100
	}
	if q.SortBy == "" {
		q.SortBy = "created_at"
	}
	
	// 验证时间范围
	if q.CreatedFrom != nil && q.CreatedTo != nil {
		if q.CreatedFrom.After(*q.CreatedTo) {
			return errors.New("开始时间不能晚于结束时间")
		}
	}
	
	return nil
}

// GetUserTenantsQuery 获取用户租户列表查询
type GetUserTenantsQuery struct {
	UserID string `json:"user_id" validate:"required"`
}

// Validate 验证查询参数
func (q *GetUserTenantsQuery) Validate() error {
	if strings.TrimSpace(q.UserID) == "" {
		return errors.New("用户ID是必需的")
	}
	return nil
}

// GetTenantUsersQuery 获取租户用户列表查询
type GetTenantUsersQuery struct {
	TenantID string `json:"tenant_id" validate:"required"`
	Status   string `json:"status,omitempty"`
	RoleID   string `json:"role_id,omitempty"`
	Keyword  string `json:"keyword,omitempty"`
	Page     int    `json:"page" validate:"min=1"`
	PageSize int    `json:"page_size" validate:"min=1,max=100"`
	SortBy   string `json:"sort_by,omitempty"`
	SortDesc bool   `json:"sort_desc,omitempty"`
}

// Validate 验证查询参数
func (q *GetTenantUsersQuery) Validate() error {
	if strings.TrimSpace(q.TenantID) == "" {
		return errors.New("租户ID是必需的")
	}
	if q.Page <= 0 {
		q.Page = 1
	}
	if q.PageSize <= 0 {
		q.PageSize = 10
	}
	if q.PageSize > 100 {
		q.PageSize = 100
	}
	if q.SortBy == "" {
		q.SortBy = "created_at"
	}
	return nil
}

// GetUserStatisticsQuery 获取用户统计查询
type GetUserStatisticsQuery struct {
	TenantID string `json:"tenant_id" validate:"required"`
}

// Validate 验证查询参数
func (q *GetUserStatisticsQuery) Validate() error {
	if strings.TrimSpace(q.TenantID) == "" {
		return errors.New("租户ID是必需的")
	}
	return nil
}

// GetUserActivityQuery 获取用户活动查询
type GetUserActivityQuery struct {
	UserID    string     `json:"user_id" validate:"required"`
	TenantID  string     `json:"tenant_id" validate:"required"`
	StartTime *time.Time `json:"start_time,omitempty"`
	EndTime   *time.Time `json:"end_time,omitempty"`
}

// Validate 验证查询参数
func (q *GetUserActivityQuery) Validate() error {
	if strings.TrimSpace(q.UserID) == "" {
		return errors.New("用户ID是必需的")
	}
	if strings.TrimSpace(q.TenantID) == "" {
		return errors.New("租户ID是必需的")
	}
	
	// 验证时间范围
	if q.StartTime != nil && q.EndTime != nil {
		if q.StartTime.After(*q.EndTime) {
			return errors.New("开始时间不能晚于结束时间")
		}
	}
	
	// 如果没有指定时间范围，默认查询最近30天
	if q.StartTime == nil && q.EndTime == nil {
		now := time.Now()
		endTime := now
		startTime := now.AddDate(0, 0, -30)
		q.StartTime = &startTime
		q.EndTime = &endTime
	}
	
	return nil
}

// ==================== 查询结果模型 ====================

// UserQueryResult 用户查询结果
type UserQueryResult struct {
	UserID      string                 `json:"user_id"`
	BusinessID  string                 `json:"business_id"`
	Username    string                 `json:"username"`
	Email       string                 `json:"email"`
	Phone       string                 `json:"phone"`
	Status      string                 `json:"status"`
	Profile     UserProfileResult      `json:"profile"`
	Tenants     []UserTenantResult     `json:"tenants,omitempty"`
	Permissions []string               `json:"permissions,omitempty"`
	Roles       []string               `json:"roles,omitempty"`
	Metadata    map[string]interface{} `json:"metadata,omitempty"`
	CreatedAt   time.Time              `json:"created_at"`
	UpdatedAt   time.Time              `json:"updated_at"`
	Version     int                    `json:"version"`
}

// UserProfileResult 用户资料查询结果
type UserProfileResult struct {
	Avatar    string `json:"avatar,omitempty"`
	Nickname  string `json:"nickname,omitempty"`
	FirstName string `json:"first_name,omitempty"`
	LastName  string `json:"last_name,omitempty"`
	Language  string `json:"language,omitempty"`
	Timezone  string `json:"timezone,omitempty"`
}

// UserTenantResult 用户租户关联查询结果
type UserTenantResult struct {
	TenantID   string    `json:"tenant_id"`
	TenantName string    `json:"tenant_name"`
	RoleID     string    `json:"role_id"`
	RoleName   string    `json:"role_name"`
	Status     string    `json:"status"`
	JoinedAt   time.Time `json:"joined_at"`
}

// UserListResult 用户列表查询结果
type UserListResult struct {
	Users      []UserQueryResult `json:"users"`
	Total      int64             `json:"total"`
	Page       int               `json:"page"`
	PageSize   int               `json:"page_size"`
	TotalPages int               `json:"total_pages"`
}

// UserStatisticsResult 用户统计查询结果
type UserStatisticsResult struct {
	TotalUsers        int64                  `json:"total_users"`
	ActiveUsers       int64                  `json:"active_users"`
	InactiveUsers     int64                  `json:"inactive_users"`
	SuspendedUsers    int64                  `json:"suspended_users"`
	BannedUsers       int64                  `json:"banned_users"`
	StatusBreakdown   map[string]int64       `json:"status_breakdown"`
	NewUsersToday     int64                  `json:"new_users_today"`
	NewUsersThisWeek  int64                  `json:"new_users_this_week"`
	NewUsersThisMonth int64                  `json:"new_users_this_month"`
	RoleDistribution  map[string]int64       `json:"role_distribution"`
	LastUpdated       time.Time              `json:"last_updated"`
}

// UserActivityResult 用户活动查询结果
type UserActivityResult struct {
	UserID          string    `json:"user_id"`
	LoginCount      int64     `json:"login_count"`
	LastLoginTime   time.Time `json:"last_login_time"`
	SessionDuration int64     `json:"session_duration"` // 总会话时长（秒）
	ActiveDays      int       `json:"active_days"`      // 活跃天数
	ActionsCount    int64     `json:"actions_count"`    // 操作次数
	StartTime       time.Time `json:"start_time"`
	EndTime         time.Time `json:"end_time"`
}
