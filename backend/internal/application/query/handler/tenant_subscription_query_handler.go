package handler

import (
	"context"
	"fmt"
	"math"
	"strings"
	"time"

	"backend/internal/application/query/model"
	"backend/internal/domain/tenant/entity"
	"backend/internal/domain/tenant/repository"
	"backend/pkg/infrastructure/logger"
)

// TenantSubscriptionQueryHandler 租户订阅查询处理器
type TenantSubscriptionQueryHandler struct {
	tenantRepo       repository.TenantRepository
	subscriptionRepo repository.TenantSubscriptionRepository
	logger           logger.Logger
}

// NewTenantSubscriptionQueryHandler 创建租户订阅查询处理器
func NewTenantSubscriptionQueryHandler(
	tenantRepo repository.TenantRepository,
	subscriptionRepo repository.TenantSubscriptionRepository,
	logger logger.Logger,
) *TenantSubscriptionQueryHandler {
	return &TenantSubscriptionQueryHandler{
		tenantRepo:       tenantRepo,
		subscriptionRepo: subscriptionRepo,
		logger:           logger,
	}
}

// GetSubscription 获取订阅
func (h *TenantSubscriptionQueryHandler) GetSubscription(ctx context.Context, query *model.GetSubscriptionQuery) (*model.SubscriptionQueryResult, error) {
	h.logger.Info(ctx, "Processing GetSubscription query", map[string]interface{}{
		"subscription_id": query.SubscriptionID,
	})

	// 1. 验证查询参数
	if err := query.Validate(); err != nil {
		h.logger.Error(ctx, "Invalid GetSubscription query", err, map[string]interface{}{
			"query": query,
		})
		return nil, fmt.Errorf("无效的查询参数: %w", err)
	}

	// 2. 获取订阅
	subscription, err := h.subscriptionRepo.GetByID(ctx, query.SubscriptionID)
	if err != nil {
		h.logger.Error(ctx, "Failed to get subscription", err, map[string]interface{}{
			"subscription_id": query.SubscriptionID,
		})
		return nil, fmt.Errorf("获取订阅失败: %w", err)
	}

	// 3. 转换为查询结果
	result := h.convertToSubscriptionQueryResult(subscription)

	h.logger.Info(ctx, "Subscription retrieved successfully", map[string]interface{}{
		"subscription_id": query.SubscriptionID,
		"tenant_id":       result.TenantID,
		"plan_id":         result.PlanID,
		"status":          result.Status,
	})

	return result, nil
}

// GetTenantSubscription 获取租户订阅
func (h *TenantSubscriptionQueryHandler) GetTenantSubscription(ctx context.Context, query *model.GetTenantSubscriptionQuery) (*model.SubscriptionQueryResult, error) {
	h.logger.Info(ctx, "Processing GetTenantSubscription query", map[string]interface{}{
		"tenant_id": query.TenantID,
	})

	// 1. 验证查询参数
	if err := query.Validate(); err != nil {
		h.logger.Error(ctx, "Invalid GetTenantSubscription query", err, map[string]interface{}{
			"query": query,
		})
		return nil, fmt.Errorf("无效的查询参数: %w", err)
	}

	// 2. 获取租户订阅
	subscription, err := h.subscriptionRepo.GetByTenantID(ctx, query.TenantID)
	if err != nil {
		h.logger.Error(ctx, "Failed to get tenant subscription", err, map[string]interface{}{
			"tenant_id": query.TenantID,
		})
		return nil, fmt.Errorf("获取租户订阅失败: %w", err)
	}

	// 3. 转换为查询结果
	result := h.convertToSubscriptionQueryResult(subscription)

	h.logger.Info(ctx, "Tenant subscription retrieved successfully", map[string]interface{}{
		"tenant_id":       query.TenantID,
		"subscription_id": result.SubscriptionID,
		"plan_id":         result.PlanID,
		"status":          result.Status,
	})

	return result, nil
}

// ListSubscriptions 订阅列表查询
func (h *TenantSubscriptionQueryHandler) ListSubscriptions(ctx context.Context, query *model.ListSubscriptionsQuery) (*model.SubscriptionListResult, error) {
	h.logger.Info(ctx, "Processing ListSubscriptions query", map[string]interface{}{
		"tenant_id": query.TenantID,
		"plan_id":   query.PlanID,
		"status":    query.Status,
		"page":      query.Page,
		"page_size": query.PageSize,
	})

	// 1. 验证查询参数
	if err := query.Validate(); err != nil {
		h.logger.Error(ctx, "Invalid ListSubscriptions query", err, map[string]interface{}{
			"query": query,
		})
		return nil, fmt.Errorf("无效的查询参数: %w", err)
	}

	// 2. 构建过滤条件
	filter := h.buildSubscriptionFilter(query)

	// 3. 构建分页参数
	pagination := repository.TenantPagination{
		Page:     query.Page,
		PageSize: query.PageSize,
		SortBy:   query.SortBy,
		SortDesc: query.SortDesc,
	}

	// 4. 查询订阅列表
	subscriptions, listResult, err := h.subscriptionRepo.List(ctx, filter, pagination)
	if err != nil {
		h.logger.Error(ctx, "Failed to list subscriptions", err, map[string]interface{}{
			"filter":     filter,
			"pagination": pagination,
		})
		return nil, fmt.Errorf("查询订阅列表失败: %w", err)
	}

	// 5. 转换为查询结果
	items := make([]model.SubscriptionQueryResult, 0, len(subscriptions))
	for _, subscription := range subscriptions {
		result := h.convertToSubscriptionQueryResult(subscription)
		items = append(items, *result)
	}

	// 6. 构建分页结果
	totalPages := int(math.Ceil(float64(listResult.Total) / float64(query.PageSize)))

	result := &model.SubscriptionListResult{
		Items:       items,
		Total:       listResult.Total,
		Page:        query.Page,
		PageSize:    query.PageSize,
		TotalPages:  totalPages,
		HasNext:     query.Page < totalPages,
		HasPrevious: query.Page > 1,
	}

	h.logger.Info(ctx, "Subscriptions listed successfully", map[string]interface{}{
		"total":      result.Total,
		"page":       result.Page,
		"page_size":  result.PageSize,
		"item_count": len(result.Items),
	})

	return result, nil
}

// GetSubscriptionHistory 获取订阅历史
func (h *TenantSubscriptionQueryHandler) GetSubscriptionHistory(ctx context.Context, query *model.GetSubscriptionHistoryQuery) (*model.SubscriptionListResult, error) {
	h.logger.Info(ctx, "Processing GetSubscriptionHistory query", map[string]interface{}{
		"tenant_id": query.TenantID,
		"page":      query.Page,
		"page_size": query.PageSize,
	})

	// 1. 验证查询参数
	if err := query.Validate(); err != nil {
		h.logger.Error(ctx, "Invalid GetSubscriptionHistory query", err, map[string]interface{}{
			"query": query,
		})
		return nil, fmt.Errorf("无效的查询参数: %w", err)
	}

	// 2. 构建过滤条件（包含已删除的记录）
	filter := repository.SubscriptionFilter{
		TenantID: query.TenantID,
	}

	// 3. 构建分页参数
	pagination := repository.TenantPagination{
		Page:     query.Page,
		PageSize: query.PageSize,
		SortBy:   query.SortBy,
		SortDesc: query.SortDesc,
	}

	// 4. 查询订阅历史
	subscriptions, listResult, err := h.subscriptionRepo.List(ctx, filter, pagination)
	if err != nil {
		h.logger.Error(ctx, "Failed to get subscription history", err, map[string]interface{}{
			"tenant_id":  query.TenantID,
			"pagination": pagination,
		})
		return nil, fmt.Errorf("查询订阅历史失败: %w", err)
	}

	// 5. 转换为查询结果
	items := make([]model.SubscriptionQueryResult, 0, len(subscriptions))
	for _, subscription := range subscriptions {
		result := h.convertToSubscriptionQueryResult(subscription)
		items = append(items, *result)
	}

	// 6. 构建分页结果
	totalPages := int(math.Ceil(float64(listResult.Total) / float64(query.PageSize)))

	result := &model.SubscriptionListResult{
		Items:       items,
		Total:       listResult.Total,
		Page:        query.Page,
		PageSize:    query.PageSize,
		TotalPages:  totalPages,
		HasNext:     query.Page < totalPages,
		HasPrevious: query.Page > 1,
	}

	h.logger.Info(ctx, "Subscription history retrieved successfully", map[string]interface{}{
		"tenant_id":  query.TenantID,
		"total":      result.Total,
		"page":       result.Page,
		"item_count": len(result.Items),
	})

	return result, nil
}

// GetSubscriptionStatistics 获取订阅统计
func (h *TenantSubscriptionQueryHandler) GetSubscriptionStatistics(ctx context.Context, query *model.GetSubscriptionStatisticsQuery) (*model.SubscriptionStatisticsResult, error) {
	h.logger.Info(ctx, "Processing GetSubscriptionStatistics query", map[string]interface{}{
		"start_date":    query.StartDate,
		"end_date":      query.EndDate,
		"currency":      query.Currency,
		"billing_cycle": query.BillingCycle,
	})

	// 1. 验证查询参数
	if err := query.Validate(); err != nil {
		h.logger.Error(ctx, "Invalid GetSubscriptionStatistics query", err, map[string]interface{}{
			"query": query,
		})
		return nil, fmt.Errorf("无效的查询参数: %w", err)
	}

	// 2. 获取所有活跃订阅
	activeSubscriptions, err := h.subscriptionRepo.GetActiveSubscriptions(ctx)
	if err != nil {
		h.logger.Error(ctx, "Failed to get active subscriptions", err, nil)
		return nil, fmt.Errorf("获取活跃订阅失败: %w", err)
	}

	// 3. 获取试用订阅
	trialSubscriptions, err := h.subscriptionRepo.GetTrialSubscriptions(ctx)
	if err != nil {
		h.logger.Error(ctx, "Failed to get trial subscriptions", err, nil)
		return nil, fmt.Errorf("获取试用订阅失败: %w", err)
	}

	// 4. 获取过期订阅
	expiredSubscriptions, err := h.subscriptionRepo.GetExpiredSubscriptions(ctx)
	if err != nil {
		h.logger.Error(ctx, "Failed to get expired subscriptions", err, nil)
		return nil, fmt.Errorf("获取过期订阅失败: %w", err)
	}

	// 5. 计算统计数据
	result := &model.SubscriptionStatisticsResult{
		TotalSubscriptions:       int64(len(activeSubscriptions) + len(trialSubscriptions) + len(expiredSubscriptions)),
		ActiveSubscriptions:      int64(len(activeSubscriptions)),
		TrialSubscriptions:       int64(len(trialSubscriptions)),
		ExpiredSubscriptions:     int64(len(expiredSubscriptions)),
		StatusDistribution:       make(map[string]int64),
		PlanDistribution:         make(map[string]int64),
		CurrencyDistribution:     make(map[string]int64),
		BillingCycleDistribution: make(map[string]int64),
		RevenueByPlan:            make(map[string]float64),
		RevenueByCurrency:        make(map[string]float64),
		GeneratedAt:              time.Now(),
	}

	// 6. 计算分布统计
	allSubscriptions := append(activeSubscriptions, trialSubscriptions...)
	allSubscriptions = append(allSubscriptions, expiredSubscriptions...)

	for _, subscription := range allSubscriptions {
		// 状态分布
		result.StatusDistribution[subscription.Status.String()]++

		// 计划分布
		result.PlanDistribution[subscription.PlanID]++

		// 货币分布
		result.CurrencyDistribution[subscription.Currency]++

		// 计费周期分布
		result.BillingCycleDistribution[subscription.BillingCycle]++

		// 收入统计
		price, _ := subscription.Price.Float64()
		result.RevenueByPlan[subscription.PlanID] += price
		result.RevenueByCurrency[subscription.Currency] += price
		result.TotalRevenue += price
	}

	// 7. 计算关键指标
	if result.TotalSubscriptions > 0 {
		result.ChurnRate = float64(result.ExpiredSubscriptions) / float64(result.TotalSubscriptions) * 100
		result.RenewalRate = 100 - result.ChurnRate
	}

	if len(activeSubscriptions) > 0 {
		result.AverageRevenue = result.TotalRevenue / float64(len(activeSubscriptions))
	}

	// 计算MRR和ARR（简化计算）
	for _, subscription := range activeSubscriptions {
		price, _ := subscription.Price.Float64()
		switch subscription.BillingCycle {
		case "monthly":
			result.MRR += price
		case "yearly":
			result.ARR += price
			result.MRR += price / 12
		}
	}
	result.ARR += result.MRR * 12

	h.logger.Info(ctx, "Subscription statistics calculated successfully", map[string]interface{}{
		"total_subscriptions":  result.TotalSubscriptions,
		"active_subscriptions": result.ActiveSubscriptions,
		"total_revenue":        result.TotalRevenue,
		"mrr":                  result.MRR,
		"arr":                  result.ARR,
	})

	return result, nil
}

// convertToSubscriptionQueryResult 转换为订阅查询结果
func (h *TenantSubscriptionQueryHandler) convertToSubscriptionQueryResult(subscription *entity.TenantSubscription) *model.SubscriptionQueryResult {
	// 解析功能列表
	features := make([]string, 0)
	if subscription.Features != "" {
		features = strings.Split(subscription.Features, ",")
	}

	return &model.SubscriptionQueryResult{
		SubscriptionID: subscription.BusinessID,
		TenantID:       subscription.TenantID,
		PlanID:         subscription.PlanID,
		PlanName:       subscription.PlanName,
		Status:         subscription.Status.String(),
		StatusDisplay:  h.getStatusDisplay(subscription.Status),
		StartDate:      subscription.StartDate,
		EndDate:        subscription.EndDate,
		Price:          subscription.Price,
		Currency:       subscription.Currency,
		BillingCycle:   subscription.BillingCycle,
		AutoRenewal:    subscription.AutoRenewal,

		// 使用限制
		UserLimit:    subscription.UserLimit,
		StorageLimit: subscription.StorageLimit,
		APILimit:     subscription.APILimit,
		ProductLimit: subscription.ProductLimit,
		OrderLimit:   subscription.OrderLimit,

		// 功能权限
		Features: features,

		// 试用信息
		IsTrialPeriod:   subscription.IsTrialPeriod,
		TrialEndDate:    subscription.TrialEndDate,
		TrialExtensions: subscription.TrialExtensions,

		// 到期信息
		DaysUntilExpiry: subscription.DaysUntilExpiry(),
		IsExpired:       subscription.IsExpired(),
		IsExpiringSoon:  subscription.DaysUntilExpiry() <= 7 && subscription.DaysUntilExpiry() > 0,
		CanExtendTrial:  subscription.IsTrialPeriod && subscription.TrialExtensions < 3,

		// 审计信息
		CreatedAt: subscription.CreatedAt,
		UpdatedAt: subscription.UpdatedAt,
		Version:   subscription.Version,
	}
}

// getStatusDisplay 获取状态显示名称
func (h *TenantSubscriptionQueryHandler) getStatusDisplay(status entity.SubscriptionStatus) string {
	switch status {
	case entity.SubscriptionStatusPending:
		return "待激活"
	case entity.SubscriptionStatusActive:
		return "活跃"
	case entity.SubscriptionStatusSuspended:
		return "已暂停"
	case entity.SubscriptionStatusCanceled:
		return "已取消"
	case entity.SubscriptionStatusExpired:
		return "已过期"
	default:
		return "未知"
	}
}

// buildSubscriptionFilter 构建订阅过滤条件
func (h *TenantSubscriptionQueryHandler) buildSubscriptionFilter(query *model.ListSubscriptionsQuery) repository.SubscriptionFilter {
	filter := repository.SubscriptionFilter{
		TenantID:       query.TenantID,
		PlanID:         query.PlanID,
		PlanName:       query.PlanName,
		Currency:       query.Currency,
		BillingCycle:   query.BillingCycle,
		ExpiringInDays: query.ExpiringInDays,
		CreatedFrom:    query.CreatedFrom,
		CreatedTo:      query.CreatedTo,
	}

	// 转换订阅状态
	if query.Status != "" {
		switch query.Status {
		case "pending":
			filter.Status = entity.SubscriptionStatusPending
		case "active":
			filter.Status = entity.SubscriptionStatusActive
		case "suspended":
			filter.Status = entity.SubscriptionStatusSuspended
		case "canceled":
			filter.Status = entity.SubscriptionStatusCanceled
		case "expired":
			filter.Status = entity.SubscriptionStatusExpired
		}
	}

	// 设置布尔值过滤
	if query.IsTrialPeriod != nil {
		filter.IsTrialPeriod = query.IsTrialPeriod
	}
	if query.AutoRenewal != nil {
		filter.AutoRenewal = query.AutoRenewal
	}

	return filter
}
