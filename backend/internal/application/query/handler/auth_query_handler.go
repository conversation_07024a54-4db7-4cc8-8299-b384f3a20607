package handler

import (
	"context"
	"fmt"
	"time"

	"backend/internal/application/query/model"
	"backend/internal/domain/auth/entity"
	"backend/internal/domain/auth/repository"
	userRepo "backend/internal/domain/user/repository"
	"backend/pkg/infrastructure/logger"
	"backend/pkg/infrastructure/auth"
)

// AuthQueryHandler 安全查询处理器
type AuthQueryHandler struct {
	userRepo       userRepo.UserRepository
	userAuthRepo   repository.UserAuthRepository
	userTenantRepo userRepo.UserTenantRepository
	sessionRepo    repository.SessionRepository
	tokenCache     repository.TokenCacheRepository
	jwtManager     auth.JWTManager
	casbinManager  auth.CasbinManager
	logger         logger.Logger
}

// NewAuthQueryHandler 创建安全查询处理器
func NewAuthQueryHandler(
	userRepo userRepo.UserRepository,
	userAuthRepo repository.UserAuthRepository,
	userTenantRepo userRepo.UserTenantRepository,
	sessionRepo repository.SessionRepository,
	tokenCache repository.TokenCacheRepository,
	jwtManager auth.JWTManager,
	casbinManager auth.CasbinManager,
	logger logger.Logger,
) *AuthQueryHandler {
	return &AuthQueryHandler{
		userRepo:       userRepo,
		userAuthRepo:   userAuthRepo,
		userTenantRepo: userTenantRepo,
		sessionRepo:    sessionRepo,
		tokenCache:     tokenCache,
		jwtManager:     jwtManager,
		casbinManager:  casbinManager,
		logger:         logger,
	}
}

// ==================== 认证查询处理 ====================

// ValidateToken 处理验证令牌查询
func (h *AuthQueryHandler) ValidateToken(ctx context.Context, query *model.ValidateTokenQuery) (*model.TokenValidationResult, error) {
	h.logger.Info(ctx, "Processing ValidateToken query")

	// 1. 验证查询
	if err := query.Validate(); err != nil {
		return nil, fmt.Errorf("无效的验证令牌查询: %w", err)
	}

	// 2. 验证令牌
	claims, err := h.jwtManager.ValidateToken(query.Token)
	if err != nil {
		h.logger.Warn(ctx, "Token validation failed", "error", err)
		return &model.TokenValidationResult{
			Valid: false,
			Error: "令牌无效",
		}, nil
	}

	// 3. 检查令牌是否在黑名单中
	jti, _ := h.jwtManager.ExtractJTI(query.Token)
	isBlacklisted, err := h.tokenCache.IsTokenBlacklisted(ctx, jti)
	if err != nil {
		h.logger.Error(ctx, "Failed to check token blacklist", "jti", jti, "error", err)
		return &model.TokenValidationResult{
			Valid: false,
			Error: "检查令牌状态失败",
		}, nil
	}

	if isBlacklisted {
		return &model.TokenValidationResult{
			Valid: false,
			Error: "令牌已被撤销",
		}, nil
	}

	// 4. 获取用户权限
	permissions, _ := h.casbinManager.GetPermissionsForUser(ctx, claims.UserID, claims.TenantID)
	permissionList := make([]string, 0)
	for _, perm := range permissions {
		if len(perm) > 0 {
			permissionList = append(permissionList, perm[0])
		}
	}

	// 5. 返回验证结果
	return &model.TokenValidationResult{
		Valid:       true,
		UserID:      claims.UserID,
		TenantID:    claims.TenantID,
		Roles:       claims.Roles,
		Permissions: permissionList,
		ExpiresAt:   claims.ExpiresAt.Time,
	}, nil
}

// GetUserAuth 处理获取用户认证信息查询
func (h *AuthQueryHandler) GetUserAuth(ctx context.Context, query *model.GetUserAuthQuery) (*model.UserAuthResult, error) {
	h.logger.Info(ctx, "Processing GetUserAuth query", "user_id", query.UserID)

	// 1. 验证查询
	if err := query.Validate(); err != nil {
		return nil, fmt.Errorf("无效的获取用户认证信息查询: %w", err)
	}

	// 2. 查找用户
	user, err := h.userRepo.FindByBusinessID(ctx, query.UserID)
	if err != nil {
		h.logger.Error(ctx, "Failed to find user", "user_id", query.UserID, "error", err)
		return nil, fmt.Errorf("用户不存在")
	}

	// 3. 返回用户认证信息
	return &model.UserAuthResult{
		UserID:      user.BusinessID,
		Username:    user.Username,
		Email:       user.Email,
		Phone:       user.Phone,
		DisplayName: user.Profile.GetDisplayName(),
		Avatar:      user.Profile.Avatar,
		Status:      user.Status.String(),
		LastLoginAt: time.Time{}, // TODO: 从认证记录中获取最后登录时间
		CreatedAt:   user.CreatedAt,
		UpdatedAt:   user.UpdatedAt,
	}, nil
}

// CheckPermission 处理检查权限查询
func (h *AuthQueryHandler) CheckPermission(ctx context.Context, query *model.CheckPermissionQuery) (*model.PermissionCheckResult, error) {
	h.logger.Info(ctx, "Processing CheckPermission query", map[string]any{
		"user_id":   query.UserID,
		"tenant_id": query.TenantID,
		"resource":  query.Resource,
		"action":    query.Action,
	})

	// 1. 验证查询
	if err := query.Validate(); err != nil {
		return nil, fmt.Errorf("无效的检查权限查询: %w", err)
	}

	// 2. 检查权限
	allowed, err := h.casbinManager.CheckPermission(ctx, query.UserID, query.TenantID, query.Resource, query.Action)
	if err != nil {
		h.logger.Error(ctx, "Failed to check permission", "user_id", query.UserID, "error", err)
		return &model.PermissionCheckResult{
			Allowed:  false,
			UserID:   query.UserID,
			TenantID: query.TenantID,
			Resource: query.Resource,
			Action:   query.Action,
			Reason:   "权限检查失败",
		}, nil
	}

	// 3. 获取用户角色
	roles, _ := h.casbinManager.GetRolesForUser(ctx, query.UserID, query.TenantID)

	// 4. 返回权限检查结果
	result := &model.PermissionCheckResult{
		Allowed:  allowed,
		UserID:   query.UserID,
		TenantID: query.TenantID,
		Resource: query.Resource,
		Action:   query.Action,
		Roles:    roles,
	}

	if !allowed {
		result.Reason = "权限不足"
	}

	return result, nil
}

// ==================== 角色权限查询处理 ====================

// GetUserRoles 处理获取用户角色查询
func (h *AuthQueryHandler) GetUserRoles(ctx context.Context, query *model.GetUserRolesQuery) (*model.UserRolesResult, error) {
	h.logger.Info(ctx, "Processing GetUserRoles query", "user_id", query.UserID, "tenant_id", query.TenantID)

	// 1. 验证查询
	if err := query.Validate(); err != nil {
		return nil, fmt.Errorf("无效的获取用户角色查询: %w", err)
	}

	// 2. 获取用户角色
	roles, err := h.casbinManager.GetRolesForUser(ctx, query.UserID, query.TenantID)
	if err != nil {
		h.logger.Error(ctx, "Failed to get user roles", "user_id", query.UserID, "tenant_id", query.TenantID, "error", err)
		return nil, fmt.Errorf("获取用户角色失败")
	}

	// 3. 构建角色信息
	roleList := make([]model.Role, 0, len(roles))
	for _, roleID := range roles {
		// 获取角色权限
		permissions, _ := h.casbinManager.GetPermissionsForUser(ctx, query.UserID, query.TenantID)
		permissionList := make([]string, 0)
		for _, perm := range permissions {
			if len(perm) > 0 {
				permissionList = append(permissionList, perm[0])
			}
		}

		roleList = append(roleList, model.Role{
			RoleID:      roleID,
			RoleName:    roleID, // TODO: 从角色仓储获取实际角色名称
			Description: "",     // TODO: 从角色仓储获取角色描述
			Permissions: permissionList,
			CreatedAt:   time.Now(), // TODO: 从角色仓储获取创建时间
		})
	}

	// 4. 返回用户角色结果
	return &model.UserRolesResult{
		UserID:   query.UserID,
		TenantID: query.TenantID,
		Roles:    roleList,
	}, nil
}

// GetRolePermissions 处理获取角色权限查询
func (h *AuthQueryHandler) GetRolePermissions(ctx context.Context, query *model.GetRolePermissionsQuery) (*model.RolePermissionsResult, error) {
	h.logger.Info(ctx, "Processing GetRolePermissions query", "role_id", query.RoleID, "tenant_id", query.TenantID)

	// 1. 验证查询
	if err := query.Validate(); err != nil {
		return nil, fmt.Errorf("无效的获取角色权限查询: %w", err)
	}

	// 2. 获取角色权限 (这里需要根据实际的Casbin策略来实现)
	// TODO: 实现获取特定角色的权限逻辑
	permissions := []model.Permission{
		{
			Resource:    "user",
			Actions:     []string{"read", "create", "update"},
			Description: "用户管理权限",
		},
		{
			Resource:    "tenant",
			Actions:     []string{"read"},
			Description: "租户查看权限",
		},
	}

	// 3. 返回角色权限结果
	return &model.RolePermissionsResult{
		RoleID:      query.RoleID,
		RoleName:    query.RoleID, // TODO: 从角色仓储获取实际角色名称
		TenantID:    query.TenantID,
		Permissions: permissions,
	}, nil
}

// ListRoles 处理角色列表查询
func (h *AuthQueryHandler) ListRoles(ctx context.Context, query *model.ListRolesQuery) (*model.RoleListResult, error) {
	h.logger.Info(ctx, "Processing ListRoles query", "tenant_id", query.TenantID)

	// 1. 验证查询
	if err := query.Validate(); err != nil {
		return nil, fmt.Errorf("无效的角色列表查询: %w", err)
	}

	// 2. 获取角色列表 (这里需要根据实际的角色仓储来实现)
	// TODO: 实现从角色仓储获取角色列表的逻辑
	roles := []model.Role{
		{
			RoleID:      "admin",
			RoleName:    "管理员",
			Description: "系统管理员角色",
			Permissions: []string{"user:*", "tenant:*", "role:*"},
			CreatedAt:   time.Now(),
		},
		{
			RoleID:      "user",
			RoleName:    "普通用户",
			Description: "普通用户角色",
			Permissions: []string{"user:read", "tenant:read"},
			CreatedAt:   time.Now(),
		},
	}

	// 3. 计算分页
	total := int64(len(roles))
	totalPages := int((total + int64(query.PageSize) - 1) / int64(query.PageSize))

	// 4. 返回角色列表结果
	return &model.RoleListResult{
		Roles:      roles,
		Total:      total,
		Page:       query.Page,
		PageSize:   query.PageSize,
		TotalPages: totalPages,
	}, nil
}

// ==================== 会话查询处理 ====================

// GetSession 处理获取会话查询
func (h *AuthQueryHandler) GetSession(ctx context.Context, query *model.GetSessionQuery) (*model.SessionResult, error) {
	h.logger.Info(ctx, "Processing GetSession query", "session_id", query.SessionID)

	// 1. 验证查询
	if err := query.Validate(); err != nil {
		return nil, fmt.Errorf("无效的获取会话查询: %w", err)
	}

	// 2. 查找会话
	session, err := h.sessionRepo.FindBySessionID(ctx, query.SessionID)
	if err != nil {
		h.logger.Error(ctx, "Failed to find session", "session_id", query.SessionID, "error", err)
		return nil, fmt.Errorf("会话不存在")
	}

	// 3. 返回会话结果
	return &model.SessionResult{
		SessionID:  session.SessionID,
		UserID:     session.UserID,
		TenantID:   session.TenantID,
		DeviceID:   session.DeviceID,
		DeviceInfo: session.DeviceName,
		IPAddress:  session.ClientIP,
		UserAgent:  session.UserAgent,
		Status:     statusToString(session.Status),
		CreatedAt:  session.CreatedAt,
		LastActive: session.LastActiveAt,
		ExpiresAt:  session.ExpiresAt,
		Metadata:   parseMetadata(session.Metadata),
	}, nil
}

// ListUserSessions 处理用户会话列表查询
func (h *AuthQueryHandler) ListUserSessions(ctx context.Context, query *model.ListUserSessionsQuery) (*model.SessionListResult, error) {
	h.logger.Info(ctx, "Processing ListUserSessions query", "user_id", query.UserID)

	// 1. 验证查询
	if err := query.Validate(); err != nil {
		return nil, fmt.Errorf("无效的用户会话列表查询: %w", err)
	}

	// 2. 查找用户会话
	sessions, err := h.sessionRepo.FindByUserID(ctx, query.UserID)
	if err != nil {
		h.logger.Error(ctx, "Failed to find user sessions", "user_id", query.UserID, "error", err)
		return nil, fmt.Errorf("获取用户会话失败")
	}

	// 3. 过滤和分页
	filteredSessions := make([]model.SessionResult, 0)
	for _, session := range sessions {
		// 状态过滤
		if query.Status != "" && statusToString(session.Status) != query.Status {
			continue
		}

		// 租户过滤
		if query.TenantID != "" && session.TenantID != query.TenantID {
			continue
		}

		filteredSessions = append(filteredSessions, model.SessionResult{
			SessionID:  session.SessionID,
			UserID:     session.UserID,
			TenantID:   session.TenantID,
			DeviceID:   session.DeviceID,
			DeviceInfo: session.DeviceName,
			IPAddress:  session.ClientIP,
			UserAgent:  session.UserAgent,
			Status:     statusToString(session.Status),
			CreatedAt:  session.CreatedAt,
			LastActive: session.LastActiveAt,
			ExpiresAt:  session.ExpiresAt,
			Metadata:   parseMetadata(session.Metadata),
		})
	}

	// 4. 计算分页
	total := int64(len(filteredSessions))
	totalPages := int((total + int64(query.PageSize) - 1) / int64(query.PageSize))

	// 简单分页处理
	start := (query.Page - 1) * query.PageSize
	end := start + query.PageSize
	if start >= len(filteredSessions) {
		filteredSessions = []model.SessionResult{}
	} else {
		if end > len(filteredSessions) {
			end = len(filteredSessions)
		}
		filteredSessions = filteredSessions[start:end]
	}

	// 5. 返回会话列表结果
	return &model.SessionListResult{
		Sessions:   filteredSessions,
		Total:      total,
		Page:       query.Page,
		PageSize:   query.PageSize,
		TotalPages: totalPages,
	}, nil
}

// ==================== 安全审计查询处理 ====================

// GetSecurityEvents 处理获取安全事件查询
func (h *AuthQueryHandler) GetSecurityEvents(ctx context.Context, query *model.GetSecurityEventsQuery) (*model.SecurityEventListResult, error) {
	h.logger.Info(ctx, "Processing GetSecurityEvents query")

	// 1. 验证查询
	if err := query.Validate(); err != nil {
		return nil, fmt.Errorf("无效的安全事件查询: %w", err)
	}

	// 2. 模拟安全事件数据 (实际应该从安全事件仓储获取)
	// TODO: 实现从安全事件仓储获取数据的逻辑
	events := []model.SecurityEventResult{
		{
			EventID:     "event-001",
			UserID:      query.UserID,
			TenantID:    query.TenantID,
			EventType:   "login",
			Description: "用户登录",
			IPAddress:   "*************",
			UserAgent:   "Mozilla/5.0...",
			Success:     true,
			Details:     map[string]interface{}{"device": "desktop"},
			CreatedAt:   time.Now().Add(-1 * time.Hour),
		},
		{
			EventID:     "event-002",
			UserID:      query.UserID,
			TenantID:    query.TenantID,
			EventType:   "password_change",
			Description: "密码修改",
			IPAddress:   "*************",
			UserAgent:   "Mozilla/5.0...",
			Success:     true,
			Details:     map[string]interface{}{"method": "self_service"},
			CreatedAt:   time.Now().Add(-2 * time.Hour),
		},
	}

	// 3. 过滤事件
	filteredEvents := make([]model.SecurityEventResult, 0)
	for _, event := range events {
		// 用户过滤
		if query.UserID != "" && event.UserID != query.UserID {
			continue
		}

		// 租户过滤
		if query.TenantID != "" && event.TenantID != query.TenantID {
			continue
		}

		// 事件类型过滤
		if query.EventType != "" && event.EventType != query.EventType {
			continue
		}

		// 时间范围过滤
		if !query.StartTime.IsZero() && event.CreatedAt.Before(query.StartTime) {
			continue
		}
		if !query.EndTime.IsZero() && event.CreatedAt.After(query.EndTime) {
			continue
		}

		filteredEvents = append(filteredEvents, event)
	}

	// 4. 计算分页
	total := int64(len(filteredEvents))
	totalPages := int((total + int64(query.PageSize) - 1) / int64(query.PageSize))

	// 简单分页处理
	start := (query.Page - 1) * query.PageSize
	end := start + query.PageSize
	if start >= len(filteredEvents) {
		filteredEvents = []model.SecurityEventResult{}
	} else {
		if end > len(filteredEvents) {
			end = len(filteredEvents)
		}
		filteredEvents = filteredEvents[start:end]
	}

	// 5. 返回安全事件列表结果
	return &model.SecurityEventListResult{
		Events:     filteredEvents,
		Total:      total,
		Page:       query.Page,
		PageSize:   query.PageSize,
		TotalPages: totalPages,
	}, nil
}

// GetLoginAttempts 处理获取登录尝试查询
func (h *AuthQueryHandler) GetLoginAttempts(ctx context.Context, query *model.GetLoginAttemptsQuery) (*model.SecurityEventListResult, error) {
	h.logger.Info(ctx, "Processing GetLoginAttempts query")

	// 1. 验证查询
	if err := query.Validate(); err != nil {
		return nil, fmt.Errorf("无效的登录尝试查询: %w", err)
	}

	// 2. 模拟登录尝试数据 (实际应该从登录日志仓储获取)
	// TODO: 实现从登录日志仓储获取数据的逻辑
	attempts := []model.SecurityEventResult{
		{
			EventID:     "attempt-001",
			UserID:      query.UserID,
			EventType:   "login_attempt",
			Description: "登录尝试",
			IPAddress:   query.IPAddress,
			UserAgent:   "Mozilla/5.0...",
			Success:     true,
			Details:     map[string]interface{}{"method": "password"},
			CreatedAt:   time.Now().Add(-30 * time.Minute),
		},
		{
			EventID:     "attempt-002",
			UserID:      query.UserID,
			EventType:   "login_attempt",
			Description: "登录尝试失败",
			IPAddress:   query.IPAddress,
			UserAgent:   "Mozilla/5.0...",
			Success:     false,
			Details:     map[string]interface{}{"reason": "invalid_password"},
			CreatedAt:   time.Now().Add(-1 * time.Hour),
		},
	}

	// 3. 过滤登录尝试
	filteredAttempts := make([]model.SecurityEventResult, 0)
	for _, attempt := range attempts {
		// 用户过滤
		if query.UserID != "" && attempt.UserID != query.UserID {
			continue
		}

		// IP地址过滤
		if query.IPAddress != "" && attempt.IPAddress != query.IPAddress {
			continue
		}

		// 成功状态过滤
		if query.Success != nil && attempt.Success != *query.Success {
			continue
		}

		// 时间范围过滤
		if !query.StartTime.IsZero() && attempt.CreatedAt.Before(query.StartTime) {
			continue
		}
		if !query.EndTime.IsZero() && attempt.CreatedAt.After(query.EndTime) {
			continue
		}

		filteredAttempts = append(filteredAttempts, attempt)
	}

	// 4. 计算分页
	total := int64(len(filteredAttempts))
	totalPages := int((total + int64(query.PageSize) - 1) / int64(query.PageSize))

	// 简单分页处理
	start := (query.Page - 1) * query.PageSize
	end := start + query.PageSize
	if start >= len(filteredAttempts) {
		filteredAttempts = []model.SecurityEventResult{}
	} else {
		if end > len(filteredAttempts) {
			end = len(filteredAttempts)
		}
		filteredAttempts = filteredAttempts[start:end]
	}

	// 5. 返回登录尝试列表结果
	return &model.SecurityEventListResult{
		Events:     filteredAttempts,
		Total:      total,
		Page:       query.Page,
		PageSize:   query.PageSize,
		TotalPages: totalPages,
	}, nil
}

// ==================== 辅助函数 ====================

// statusToString 将会话状态转换为字符串
func statusToString(status entity.SessionStatus) string {
	switch status {
	case entity.SessionStatusActive:
		return "active"
	case entity.SessionStatusExpired:
		return "expired"
	case entity.SessionStatusTerminated:
		return "terminated"
	default:
		return "unknown"
	}
}

// parseMetadata 解析元数据JSON字符串
func parseMetadata(metadata string) map[string]interface{} {
	if metadata == "" {
		return map[string]interface{}{}
	}

	// TODO: 实现JSON解析
	// var result map[string]interface{}
	// if err := json.Unmarshal([]byte(metadata), &result); err != nil {
	//     return map[string]interface{}{}
	// }
	// return result

	return map[string]interface{}{
		"raw": metadata,
	}
}
