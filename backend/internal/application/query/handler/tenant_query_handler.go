package handler

import (
	"context"
	"fmt"
	"math"
	"time"

	"backend/internal/application/assembler"
	"backend/internal/application/query/model"
	"backend/internal/domain/tenant/entity"
	"backend/internal/domain/tenant/repository"
	"backend/internal/domain/tenant/service"
	"backend/internal/domain/tenant/valueobject"
	"backend/pkg/infrastructure/logger"
)

// TenantQueryHandler 租户查询处理器
type TenantQueryHandler struct {
	tenantRepo       repository.TenantRepository
	quotaRepo        repository.TenantQuotaRepository
	subscriptionRepo repository.TenantSubscriptionRepository
	tenantService    service.TenantService
	tenantAssembler  *assembler.TenantAssembler
	logger           logger.Logger
}

// NewTenantQueryHandler 创建租户查询处理器
func NewTenantQueryHandler(
	tenantRepo repository.TenantRepository,
	quotaRepo repository.TenantQuotaRepository,
	subscriptionRepo repository.TenantSubscriptionRepository,
	tenantService service.TenantService,
	tenantAssembler *assembler.TenantAssembler,
	logger logger.Logger,
) *TenantQueryHandler {
	return &TenantQueryHandler{
		tenantRepo:       tenantRepo,
		quotaRepo:        quotaRepo,
		subscriptionRepo: subscriptionRepo,
		tenantService:    tenantService,
		tenantAssembler:  tenantAssembler,
		logger:           logger,
	}
}

// GetTenantByID 根据ID获取租户
func (h *TenantQueryHandler) GetTenantByID(ctx context.Context, query *model.GetTenantByIDQuery) (*model.TenantQueryResult, error) {
	h.logger.Info(ctx, "Processing GetTenantByID query", map[string]interface{}{
		"tenant_id": query.TenantID,
	})

	// 1. 验证查询参数
	if err := query.Validate(); err != nil {
		h.logger.Error(ctx, "Invalid GetTenantByID query", err, map[string]interface{}{
			"query": query,
		})
		return nil, fmt.Errorf("无效的查询参数: %w", err)
	}

	// 2. 获取租户
	tenant, err := h.tenantRepo.GetByID(ctx, query.TenantID)
	if err != nil {
		h.logger.Error(ctx, "Failed to get tenant by ID", err, map[string]interface{}{
			"tenant_id": query.TenantID,
		})
		return nil, fmt.Errorf("获取租户失败: %w", err)
	}

	// 3. 转换为查询结果
	result := h.convertToTenantQueryResult(tenant)

	// 4. 获取配额信息
	if quota, err := h.quotaRepo.GetByTenantID(ctx, query.TenantID); err == nil {
		result.MaxUsers = quota.UserLimit
		result.MaxStorage = quota.StorageLimit
		result.MaxAPIQuota = quota.APILimitMonth
	}

	// 5. 获取订阅信息
	if subscription, err := h.subscriptionRepo.GetByTenantID(ctx, query.TenantID); err == nil {
		result.SubscriptionPlan = subscription.PlanName
		result.ExpiryDate = &subscription.EndDate
	}

	h.logger.Info(ctx, "Tenant retrieved successfully", map[string]interface{}{
		"tenant_id": query.TenantID,
		"name":      result.Name,
	})

	return result, nil
}

// GetTenantByDomain 根据域名获取租户
func (h *TenantQueryHandler) GetTenantByDomain(ctx context.Context, query *model.GetTenantByDomainQuery) (*model.TenantQueryResult, error) {
	h.logger.Info(ctx, "Processing GetTenantByDomain query", map[string]interface{}{
		"domain": query.Domain,
	})

	// 1. 验证查询参数
	if err := query.Validate(); err != nil {
		h.logger.Error(ctx, "Invalid GetTenantByDomain query", err, map[string]interface{}{
			"query": query,
		})
		return nil, fmt.Errorf("无效的查询参数: %w", err)
	}

	// 2. 获取租户
	tenant, err := h.tenantRepo.GetByDomain(ctx, query.Domain)
	if err != nil {
		h.logger.Error(ctx, "Failed to get tenant by domain", err, map[string]interface{}{
			"domain": query.Domain,
		})
		return nil, fmt.Errorf("获取租户失败: %w", err)
	}

	// 3. 转换为查询结果
	result := h.convertToTenantQueryResult(tenant)

	h.logger.Info(ctx, "Tenant retrieved successfully", map[string]interface{}{
		"domain":    query.Domain,
		"tenant_id": result.TenantID,
		"name":      result.Name,
	})

	return result, nil
}

// ListTenants 租户列表查询
func (h *TenantQueryHandler) ListTenants(ctx context.Context, query *model.ListTenantsQuery) (*model.TenantListResult, error) {
	h.logger.Info(ctx, "Processing ListTenants query", map[string]interface{}{
		"page":      query.Page,
		"page_size": query.PageSize,
		"type":      query.Type,
		"status":    query.Status,
	})

	// 1. 验证查询参数
	if err := query.Validate(); err != nil {
		h.logger.Error(ctx, "Invalid ListTenants query", err, map[string]interface{}{
			"query": query,
		})
		return nil, fmt.Errorf("无效的查询参数: %w", err)
	}

	// 2. 构建过滤条件
	filter := h.buildTenantFilter(query)

	// 3. 构建分页参数
	pagination := repository.TenantPagination{
		Page:     query.Page,
		PageSize: query.PageSize,
		SortBy:   query.SortBy,
		SortDesc: query.SortDesc,
	}

	// 4. 查询租户列表
	tenants, listResult, err := h.tenantRepo.List(ctx, filter, pagination)
	if err != nil {
		h.logger.Error(ctx, "Failed to list tenants", err, map[string]interface{}{
			"filter":     filter,
			"pagination": pagination,
		})
		return nil, fmt.Errorf("查询租户列表失败: %w", err)
	}

	// 5. 转换为查询结果
	items := make([]model.TenantQueryResult, 0, len(tenants))
	for _, tenant := range tenants {
		result := h.convertToTenantQueryResult(tenant)
		items = append(items, *result)
	}

	// 6. 构建分页结果
	totalPages := int(math.Ceil(float64(listResult.Total) / float64(query.PageSize)))

	result := &model.TenantListResult{
		Items:       items,
		Total:       listResult.Total,
		Page:        query.Page,
		PageSize:    query.PageSize,
		TotalPages:  totalPages,
		HasNext:     query.Page < totalPages,
		HasPrevious: query.Page > 1,
	}

	h.logger.Info(ctx, "Tenants listed successfully", map[string]interface{}{
		"total":      result.Total,
		"page":       result.Page,
		"page_size":  result.PageSize,
		"item_count": len(result.Items),
	})

	return result, nil
}

// SearchTenants 搜索租户
func (h *TenantQueryHandler) SearchTenants(ctx context.Context, query *model.SearchTenantsQuery) (*model.TenantListResult, error) {
	h.logger.Info(ctx, "Processing SearchTenants query", map[string]interface{}{
		"query":     query.Query,
		"fields":    query.Fields,
		"page":      query.Page,
		"page_size": query.PageSize,
	})

	// 1. 验证查询参数
	if err := query.Validate(); err != nil {
		h.logger.Error(ctx, "Invalid SearchTenants query", err, map[string]interface{}{
			"query": query,
		})
		return nil, fmt.Errorf("无效的查询参数: %w", err)
	}

	// 2. 构建搜索过滤条件
	filter := repository.TenantFilter{
		Keywords: query.Query,
	}

	// 3. 构建分页参数
	pagination := repository.TenantPagination{
		Page:     query.Page,
		PageSize: query.PageSize,
		SortBy:   query.SortBy,
		SortDesc: query.SortDesc,
	}

	// 4. 执行搜索
	tenants, listResult, err := h.tenantRepo.List(ctx, filter, pagination)
	if err != nil {
		h.logger.Error(ctx, "Failed to search tenants", err, map[string]interface{}{
			"filter":     filter,
			"pagination": pagination,
		})
		return nil, fmt.Errorf("搜索租户失败: %w", err)
	}

	// 5. 转换为查询结果
	items := make([]model.TenantQueryResult, 0, len(tenants))
	for _, tenant := range tenants {
		result := h.convertToTenantQueryResult(tenant)
		items = append(items, *result)
	}

	// 6. 构建分页结果
	totalPages := int(math.Ceil(float64(listResult.Total) / float64(query.PageSize)))

	result := &model.TenantListResult{
		Items:       items,
		Total:       listResult.Total,
		Page:        query.Page,
		PageSize:    query.PageSize,
		TotalPages:  totalPages,
		HasNext:     query.Page < totalPages,
		HasPrevious: query.Page > 1,
	}

	h.logger.Info(ctx, "Tenants searched successfully", map[string]interface{}{
		"total":      result.Total,
		"page":       result.Page,
		"page_size":  result.PageSize,
		"item_count": len(result.Items),
		"query":      query.Query,
	})

	return result, nil
}

// GetTenantHealth 获取租户健康状态
func (h *TenantQueryHandler) GetTenantHealth(ctx context.Context, query *model.GetTenantHealthQuery) (*model.TenantHealthResult, error) {
	h.logger.Info(ctx, "Processing GetTenantHealth query", map[string]interface{}{
		"tenant_id":            query.TenantID,
		"include_quota":        query.IncludeQuota,
		"include_subscription": query.IncludeSubscription,
	})

	// 1. 验证查询参数
	if err := query.Validate(); err != nil {
		h.logger.Error(ctx, "Invalid GetTenantHealth query", err, map[string]interface{}{
			"query": query,
		})
		return nil, fmt.Errorf("无效的查询参数: %w", err)
	}

	// 2. 使用领域服务检查租户健康状态
	healthStatus, err := h.tenantService.CheckTenantHealth(ctx, query.TenantID)
	if err != nil {
		h.logger.Error(ctx, "Failed to check tenant health", err, map[string]interface{}{
			"tenant_id": query.TenantID,
		})
		return nil, fmt.Errorf("检查租户健康状态失败: %w", err)
	}

	// 3. 获取租户基本信息
	tenant, err := h.tenantRepo.GetByID(ctx, query.TenantID)
	if err != nil {
		return nil, fmt.Errorf("获取租户信息失败: %w", err)
	}

	// 4. 构建健康状态结果
	result := &model.TenantHealthResult{
		TenantID:    query.TenantID,
		TenantName:  tenant.Name,
		Domain:      tenant.Domain,
		IsHealthy:   healthStatus.IsHealthy,
		HealthScore: calculateHealthScore(healthStatus),
		CheckedAt:   healthStatus.CheckedAt,
		Details:     make(map[string]interface{}),
	}

	// 5. 添加告警信息
	alerts := make([]model.HealthAlertResult, 0)
	for _, alert := range healthStatus.Alerts {
		alerts = append(alerts, model.HealthAlertResult{
			Type:      alert.Type,
			Level:     alert.Level,
			Message:   alert.Message,
			Severity:  alert.Severity,
			CreatedAt: time.Now(),
		})
	}
	result.Alerts = alerts

	h.logger.Info(ctx, "Tenant health checked successfully", map[string]interface{}{
		"tenant_id":    query.TenantID,
		"is_healthy":   result.IsHealthy,
		"health_score": result.HealthScore,
		"alert_count":  len(result.Alerts),
	})

	return result, nil
}

// convertToTenantQueryResult 转换租户实体为查询结果
func (h *TenantQueryHandler) convertToTenantQueryResult(tenant *entity.Tenant) *model.TenantQueryResult {
	return &model.TenantQueryResult{
		TenantID:      tenant.BusinessID,
		Name:          tenant.Name,
		Domain:        tenant.Domain,
		DisplayName:   tenant.DisplayName,
		Description:   tenant.Description,
		Type:          tenant.Type.String(),
		Status:        tenant.Status.String(),
		StatusDisplay: h.getStatusDisplay(tenant.Status),
		Industry:      tenant.Industry,
		Country:       tenant.Country,
		Province:      tenant.Province,
		City:          tenant.City,
		Address:       tenant.Address,
		ContactEmail:  tenant.ContactEmail,
		ContactPhone:  tenant.ContactPhone,
		Settings:      tenant.Settings,
		MaxUsers:      tenant.MaxUsers,
		MaxStorage:    tenant.MaxStorage,
		MaxAPIQuota:   tenant.MaxAPIQuota,
		CreatedAt:     tenant.CreatedAt,
		UpdatedAt:     tenant.UpdatedAt,
		Version:       tenant.Version,
	}
}

// getStatusDisplay 获取状态显示名称
func (h *TenantQueryHandler) getStatusDisplay(status valueobject.TenantStatus) string {
	switch status {
	case valueobject.TenantStatusInactive:
		return "未激活"
	case valueobject.TenantStatusActive:
		return "活跃"
	case valueobject.TenantStatusSuspended:
		return "已暂停"
	case valueobject.TenantStatusTerminated:
		return "已终止"
	case valueobject.TenantStatusExpired:
		return "已过期"
	default:
		return "未知"
	}
}

// buildTenantFilter 构建租户过滤条件
func (h *TenantQueryHandler) buildTenantFilter(query *model.ListTenantsQuery) repository.TenantFilter {
	filter := repository.TenantFilter{
		Name:        query.Name,
		Keywords:    query.Keywords,
		Industry:    query.Industry,
		Country:     query.Country,
		Province:    query.Province,
		City:        query.City,
		CreatedFrom: query.CreatedFrom,
		CreatedTo:   query.CreatedTo,
		UpdatedFrom: query.UpdatedFrom,
		UpdatedTo:   query.UpdatedTo,
	}

	// 转换租户类型
	if query.Type != "" {
		switch query.Type {
		case "system":
			filter.Type = valueobject.TenantTypeSystem
		case "enterprise":
			filter.Type = valueobject.TenantTypeEnterprise
		case "professional":
			filter.Type = valueobject.TenantTypeProfessional
		case "basic":
			filter.Type = valueobject.TenantTypeBasic
		case "trial":
			filter.Type = valueobject.TenantTypeTrial
		}
	}

	// 转换租户状态
	if query.Status != "" {
		switch query.Status {
		case "inactive":
			filter.Status = valueobject.TenantStatusInactive
		case "active":
			filter.Status = valueobject.TenantStatusActive
		case "suspended":
			filter.Status = valueobject.TenantStatusSuspended
		case "terminated":
			filter.Status = valueobject.TenantStatusTerminated
		case "expired":
			filter.Status = valueobject.TenantStatusExpired
		}
	}

	return filter
}

// calculateHealthScore 计算健康分数
func calculateHealthScore(status *service.TenantHealthStatus) int {
	if status.IsHealthy {
		return 100
	}

	score := 100
	for _, alert := range status.Alerts {
		switch alert.Level {
		case "critical":
			score -= 30
		case "warning":
			score -= 10
		case "info":
			score -= 5
		}
	}

	if score < 0 {
		score = 0
	}

	return score
}
