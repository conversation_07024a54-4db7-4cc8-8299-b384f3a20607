package handler

import (
	"context"

	"backend/internal/application/query/model"
	"backend/internal/domain/user/service"
)

// ==================== 用户查询处理器接口 ====================

// UserQueryHandler 用户查询处理器接口
type UserQueryHandler interface {
	// 单个用户查询
	GetUserByID(ctx context.Context, query *model.GetUserByIDQuery) (*model.UserQueryResult, error)
	GetUserByUsername(ctx context.Context, query *model.GetUserByUsernameQuery) (*model.UserQueryResult, error)
	GetUserByEmail(ctx context.Context, query *model.GetUserByEmailQuery) (*model.UserQueryResult, error)
	GetUserByPhone(ctx context.Context, query *model.GetUserByPhoneQuery) (*model.UserQueryResult, error)

	// 用户列表查询
	ListUsers(ctx context.Context, query *model.ListUsersQuery) (*model.UserListResult, error)
	SearchUsers(ctx context.Context, query *model.SearchUsersQuery) (*model.UserListResult, error)

	// 租户关联查询
	GetUserTenants(ctx context.Context, query *model.GetUserTenantsQuery) ([]model.UserTenantResult, error)
	GetTenantUsers(ctx context.Context, query *model.GetTenantUsersQuery) (*model.UserListResult, error)

	// 统计和分析查询
	GetUserStatistics(ctx context.Context, query *model.GetUserStatisticsQuery) (*model.UserStatisticsResult, error)
	GetUserActivity(ctx context.Context, query *model.GetUserActivityQuery) (*model.UserActivityResult, error)
}

// ==================== 用户查询处理器实现 ====================

// UserQueryHandlerImpl 用户查询处理器实现
type UserQueryHandlerImpl struct {
	userService service.UserAuthenticationService
}

// NewUserQueryHandler 创建用户查询处理器
func NewUserQueryHandler(userService service.UserAuthenticationService) UserQueryHandler {
	return &UserQueryHandlerImpl{
		userService: userService,
	}
}

// ==================== 单个用户查询实现 ====================

// GetUserByID 根据ID获取用户
func (h *UserQueryHandlerImpl) GetUserByID(ctx context.Context, query *model.GetUserByIDQuery) (*model.UserQueryResult, error) {
	// 验证查询参数
	if err := query.Validate(); err != nil {
		return nil, err
	}

	// 调用领域服务
	user, err := h.userService.FindUserByID(ctx, query.UserID)
	if err != nil {
		return nil, err
	}

	// 转换为查询结果
	result := h.convertToUserQueryResult(user)
	return result, nil
}

// GetUserByUsername 根据用户名获取用户
func (h *UserQueryHandlerImpl) GetUserByUsername(ctx context.Context, query *model.GetUserByUsernameQuery) (*model.UserQueryResult, error) {
	// 验证查询参数
	if err := query.Validate(); err != nil {
		return nil, err
	}

	// 调用领域服务
	user, err := h.userService.FindUserByUsername(ctx, query.Username)
	if err != nil {
		return nil, err
	}

	// 转换为查询结果
	result := h.convertToUserQueryResult(user)
	return result, nil
}

// GetUserByEmail 根据邮箱获取用户
func (h *UserQueryHandlerImpl) GetUserByEmail(ctx context.Context, query *model.GetUserByEmailQuery) (*model.UserQueryResult, error) {
	// 验证查询参数
	if err := query.Validate(); err != nil {
		return nil, err
	}

	// 调用领域服务
	user, err := h.userService.FindUserByEmail(ctx, query.Email)
	if err != nil {
		return nil, err
	}

	// 转换为查询结果
	result := h.convertToUserQueryResult(user)
	return result, nil
}

// GetUserByPhone 根据手机号获取用户
func (h *UserQueryHandlerImpl) GetUserByPhone(ctx context.Context, query *model.GetUserByPhoneQuery) (*model.UserQueryResult, error) {
	// 验证查询参数
	if err := query.Validate(); err != nil {
		return nil, err
	}

	// 调用领域服务
	user, err := h.userService.FindUserByPhone(ctx, query.Phone)
	if err != nil {
		return nil, err
	}

	// 转换为查询结果
	result := h.convertToUserQueryResult(user)
	return result, nil
}

// ==================== 用户列表查询实现 ====================

// ListUsers 获取用户列表
func (h *UserQueryHandlerImpl) ListUsers(ctx context.Context, query *model.ListUsersQuery) (*model.UserListResult, error) {
	// 验证查询参数
	if err := query.Validate(); err != nil {
		return nil, err
	}

	// 构建搜索条件
	criteria := &service.UserSearchCriteria{
		TenantID: query.TenantID,
		Keyword:  query.Keyword,
		Page:     query.Page,
		PageSize: query.PageSize,
		SortBy:   query.SortBy,
		SortDesc: query.SortDesc,
	}

	// 调用领域服务
	users, total, err := h.userService.SearchUsers(ctx, criteria)
	if err != nil {
		return nil, err
	}

	// 转换为查询结果
	result := &model.UserListResult{
		Users:      make([]model.UserQueryResult, len(users)),
		Total:      total,
		Page:       query.Page,
		PageSize:   query.PageSize,
		TotalPages: int((total + int64(query.PageSize) - 1) / int64(query.PageSize)),
	}

	for i, user := range users {
		result.Users[i] = *h.convertToUserQueryResult(user)
	}

	return result, nil
}

// SearchUsers 搜索用户
func (h *UserQueryHandlerImpl) SearchUsers(ctx context.Context, query *model.SearchUsersQuery) (*model.UserListResult, error) {
	// 验证查询参数
	if err := query.Validate(); err != nil {
		return nil, err
	}

	// 构建搜索条件
	criteria := &service.UserSearchCriteria{
		TenantID:    query.TenantID,
		Keyword:     query.Keyword,
		Email:       query.Email,
		Phone:       query.Phone,
		CreatedFrom: query.CreatedFrom,
		CreatedTo:   query.CreatedTo,
		Page:        query.Page,
		PageSize:    query.PageSize,
		SortBy:      query.SortBy,
		SortDesc:    query.SortDesc,
	}

	// 调用领域服务
	users, total, err := h.userService.SearchUsers(ctx, criteria)
	if err != nil {
		return nil, err
	}

	// 转换为查询结果
	result := &model.UserListResult{
		Users:      make([]model.UserQueryResult, len(users)),
		Total:      total,
		Page:       query.Page,
		PageSize:   query.PageSize,
		TotalPages: int((total + int64(query.PageSize) - 1) / int64(query.PageSize)),
	}

	for i, user := range users {
		result.Users[i] = *h.convertToUserQueryResult(user)
	}

	return result, nil
}

// ==================== 租户关联查询实现 ====================

// GetUserTenants 获取用户租户列表
func (h *UserQueryHandlerImpl) GetUserTenants(ctx context.Context, query *model.GetUserTenantsQuery) ([]model.UserTenantResult, error) {
	// 验证查询参数
	if err := query.Validate(); err != nil {
		return nil, err
	}

	// 调用领域服务
	userTenants, err := h.userService.GetUserTenants(ctx, query.UserID)
	if err != nil {
		return nil, err
	}

	// 转换为查询结果
	results := make([]model.UserTenantResult, len(userTenants))
	for i, ut := range userTenants {
		results[i] = model.UserTenantResult{
			TenantID: ut.TenantBusinessID,
			RoleID:   ut.RoleBusinessID,
			Status:   h.convertUserTenantStatus(ut.Status),
			JoinedAt: ut.CreatedAt,
		}
	}

	return results, nil
}

// GetTenantUsers 获取租户用户列表
func (h *UserQueryHandlerImpl) GetTenantUsers(ctx context.Context, query *model.GetTenantUsersQuery) (*model.UserListResult, error) {
	// 验证查询参数
	if err := query.Validate(); err != nil {
		return nil, err
	}

	// 构建过滤条件
	filters := &service.TenantUserFilters{
		RoleID:   query.RoleID,
		Keyword:  query.Keyword,
		Page:     query.Page,
		PageSize: query.PageSize,
		SortBy:   query.SortBy,
		SortDesc: query.SortDesc,
	}

	// 调用领域服务
	users, err := h.userService.GetTenantUsers(ctx, query.TenantID, filters)
	if err != nil {
		return nil, err
	}

	// 转换为查询结果
	result := &model.UserListResult{
		Users:      make([]model.UserQueryResult, len(users)),
		Total:      int64(len(users)), // TODO: 需要从服务返回总数
		Page:       query.Page,
		PageSize:   query.PageSize,
		TotalPages: int((int64(len(users)) + int64(query.PageSize) - 1) / int64(query.PageSize)),
	}

	for i, user := range users {
		result.Users[i] = *h.convertToUserQueryResult(user)
	}

	return result, nil
}

// ==================== 统计和分析查询实现 ====================

// GetUserStatistics 获取用户统计
func (h *UserQueryHandlerImpl) GetUserStatistics(ctx context.Context, query *model.GetUserStatisticsQuery) (*model.UserStatisticsResult, error) {
	// 验证查询参数
	if err := query.Validate(); err != nil {
		return nil, err
	}

	// 调用领域服务
	stats, err := h.userService.GetUserStatistics(ctx, query.TenantID)
	if err != nil {
		return nil, err
	}

	// 转换为查询结果
	result := &model.UserStatisticsResult{
		TotalUsers:        stats.TotalUsers,
		ActiveUsers:       stats.ActiveUsers,
		InactiveUsers:     stats.InactiveUsers,
		SuspendedUsers:    stats.SuspendedUsers,
		BannedUsers:       stats.BannedUsers,
		StatusBreakdown:   h.convertStatusBreakdown(stats.StatusBreakdown),
		NewUsersToday:     stats.NewUsersToday,
		NewUsersThisWeek:  stats.NewUsersThisWeek,
		NewUsersThisMonth: stats.NewUsersThisMonth,
		LastUpdated:       stats.LastUpdated,
	}

	return result, nil
}

// ==================== 辅助转换方法 ====================

// convertToUserQueryResult 转换用户实体为查询结果
func (h *UserQueryHandlerImpl) convertToUserQueryResult(user interface{}) *model.UserQueryResult {
	// TODO: 实现实际的转换逻辑
	// 这里需要根据实际的用户实体结构进行转换
	return &model.UserQueryResult{
		// UserID:     user.GetBusinessID(),
		// BusinessID: user.GetBusinessID(),
		// Username:   user.Username,
		// Email:      user.Email,
		// Phone:      user.Phone,
		// Status:     h.convertUserStatus(user.Status),
		// Profile:    h.convertUserProfile(user.Profile),
		// CreatedAt:  user.CreatedAt,
		// UpdatedAt:  user.UpdatedAt,
		// Version:    user.Version,
	}
}

// convertUserProfile 转换用户资料
func (h *UserQueryHandlerImpl) convertUserProfile(profile interface{}) model.UserProfileResult {
	// TODO: 实现实际的转换逻辑
	return model.UserProfileResult{
		// Avatar:    profile.Avatar,
		// Nickname:  profile.Nickname,
		// FirstName: profile.FirstName,
		// LastName:  profile.LastName,
		// Language:  profile.Language,
		// Timezone:  profile.Timezone,
	}
}

// convertUserStatus 转换用户状态
func (h *UserQueryHandlerImpl) convertUserStatus(status interface{}) string {
	// TODO: 实现实际的转换逻辑
	return "active"
}

// convertUserTenantStatus 转换用户租户状态
func (h *UserQueryHandlerImpl) convertUserTenantStatus(status int) string {
	switch status {
	case 1:
		return "active"
	case 0:
		return "inactive"
	default:
		return "unknown"
	}
}

// convertStatusBreakdown 转换状态分布
func (h *UserQueryHandlerImpl) convertStatusBreakdown(breakdown interface{}) map[string]int64 {
	// TODO: 实现实际的转换逻辑
	return map[string]int64{
		"active":    0,
		"inactive":  0,
		"suspended": 0,
		"banned":    0,
	}
}

// GetUserActivity 获取用户活动
func (h *UserQueryHandlerImpl) GetUserActivity(ctx context.Context, query *model.GetUserActivityQuery) (*model.UserActivityResult, error) {
	// 验证查询参数
	if err := query.Validate(); err != nil {
		return nil, err
	}

	// 构建时间周期
	period := &service.TimePeriod{
		StartTime: *query.StartTime,
		EndTime:   *query.EndTime,
	}

	// 调用领域服务
	activity, err := h.userService.GetUserActivitySummary(ctx, query.UserID, period)
	if err != nil {
		return nil, err
	}

	// 转换为查询结果
	result := &model.UserActivityResult{
		UserID:          activity.UserID,
		LoginCount:      activity.LoginCount,
		LastLoginTime:   activity.LastLoginTime,
		SessionDuration: activity.SessionDuration,
		ActiveDays:      activity.ActiveDays,
		ActionsCount:    activity.ActionsCount,
		StartTime:       activity.Period.StartTime,
		EndTime:         activity.Period.EndTime,
	}

	return result, nil
}
