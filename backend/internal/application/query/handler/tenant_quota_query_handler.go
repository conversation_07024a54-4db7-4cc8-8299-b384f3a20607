package handler

import (
	"context"
	"fmt"
	"time"

	"backend/internal/application/query/model"
	"backend/internal/domain/tenant/entity"
	"backend/internal/domain/tenant/repository"
	"backend/pkg/infrastructure/logger"
)

// TenantQuotaQueryHandler 租户配额查询处理器
type TenantQuotaQueryHandler struct {
	tenantRepo repository.TenantRepository
	quotaRepo  repository.TenantQuotaRepository
	logger     logger.Logger
}

// NewTenantQuotaQueryHandler 创建租户配额查询处理器
func NewTenantQuotaQueryHandler(
	tenantRepo repository.TenantRepository,
	quotaRepo repository.TenantQuotaRepository,
	logger logger.Logger,
) *TenantQuotaQueryHandler {
	return &TenantQuotaQueryHandler{
		tenantRepo: tenantRepo,
		quotaRepo:  quotaRepo,
		logger:     logger,
	}
}

// GetTenantQuota 获取租户配额
func (h *TenantQuotaQueryHandler) GetTenantQuota(ctx context.Context, query *model.GetTenantQuotaQuery) (*model.TenantQuotaResult, error) {
	h.logger.Info(ctx, "Processing GetTenantQuota query", map[string]interface{}{
		"tenant_id": query.TenantID,
	})

	// 1. 验证查询参数
	if err := query.Validate(); err != nil {
		h.logger.Error(ctx, "Invalid GetTenantQuota query", err, map[string]interface{}{
			"query": query,
		})
		return nil, fmt.Errorf("无效的查询参数: %w", err)
	}

	// 2. 获取租户配额
	quota, err := h.quotaRepo.GetByTenantID(ctx, query.TenantID)
	if err != nil {
		h.logger.Error(ctx, "Failed to get tenant quota", err, map[string]interface{}{
			"tenant_id": query.TenantID,
		})
		return nil, fmt.Errorf("获取租户配额失败: %w", err)
	}

	// 3. 转换为查询结果
	result := h.convertToTenantQuotaResult(quota)

	// 4. 获取配额告警
	alerts := quota.GetQuotaAlerts()
	quotaAlerts := make([]model.QuotaAlertResult, 0, len(alerts))
	for _, alert := range alerts {
		quotaAlerts = append(quotaAlerts, model.QuotaAlertResult{
			Type:      alert.Type,
			Level:     alert.Level,
			Message:   alert.Message,
			Threshold: 80.0, // 默认告警阈值
			Current:   alert.Utilization,
			CreatedAt: time.Now(),
		})
	}
	result.Alerts = quotaAlerts

	// 5. 检查是否超限
	result.IsOverQuota = h.checkOverQuota(quota)

	h.logger.Info(ctx, "Tenant quota retrieved successfully", map[string]interface{}{
		"tenant_id":    query.TenantID,
		"is_over_quota": result.IsOverQuota,
		"alert_count":  len(result.Alerts),
	})

	return result, nil
}

// GetQuotaUsage 获取配额使用情况
func (h *TenantQuotaQueryHandler) GetQuotaUsage(ctx context.Context, query *model.GetQuotaUsageQuery) (*model.QuotaUsageResult, error) {
	h.logger.Info(ctx, "Processing GetQuotaUsage query", map[string]interface{}{
		"tenant_id":   query.TenantID,
		"quota_types": query.QuotaTypes,
	})

	// 1. 验证查询参数
	if err := query.Validate(); err != nil {
		h.logger.Error(ctx, "Invalid GetQuotaUsage query", err, map[string]interface{}{
			"query": query,
		})
		return nil, fmt.Errorf("无效的查询参数: %w", err)
	}

	// 2. 获取租户配额
	quota, err := h.quotaRepo.GetByTenantID(ctx, query.TenantID)
	if err != nil {
		h.logger.Error(ctx, "Failed to get tenant quota", err, map[string]interface{}{
			"tenant_id": query.TenantID,
		})
		return nil, fmt.Errorf("获取租户配额失败: %w", err)
	}

	// 3. 如果没有指定配额类型，返回所有类型的使用情况
	if len(query.QuotaTypes) == 0 {
		query.QuotaTypes = []string{"user", "storage", "api", "product", "order", "file_upload", "email"}
	}

	// 4. 构建使用情况结果
	results := make([]*model.QuotaUsageResult, 0, len(query.QuotaTypes))
	for _, quotaType := range query.QuotaTypes {
		usage := h.getQuotaUsageByType(quota, quotaType)
		if usage != nil {
			results = append(results, usage)
		}
	}

	h.logger.Info(ctx, "Quota usage retrieved successfully", map[string]interface{}{
		"tenant_id":    query.TenantID,
		"quota_count":  len(results),
	})

	// 返回第一个结果（如果只查询一种类型）或者需要修改返回类型为数组
	if len(results) > 0 {
		return results[0], nil
	}

	return nil, fmt.Errorf("未找到指定类型的配额使用情况")
}

// GetQuotaAlerts 获取配额告警
func (h *TenantQuotaQueryHandler) GetQuotaAlerts(ctx context.Context, query *model.GetQuotaAlertsQuery) ([]model.QuotaAlertResult, error) {
	h.logger.Info(ctx, "Processing GetQuotaAlerts query", map[string]interface{}{
		"tenant_id":  query.TenantID,
		"alert_type": query.AlertType,
		"level":      query.Level,
	})

	// 1. 验证查询参数
	if err := query.Validate(); err != nil {
		h.logger.Error(ctx, "Invalid GetQuotaAlerts query", err, map[string]interface{}{
			"query": query,
		})
		return nil, fmt.Errorf("无效的查询参数: %w", err)
	}

	// 2. 获取租户配额
	quota, err := h.quotaRepo.GetByTenantID(ctx, query.TenantID)
	if err != nil {
		h.logger.Error(ctx, "Failed to get tenant quota", err, map[string]interface{}{
			"tenant_id": query.TenantID,
		})
		return nil, fmt.Errorf("获取租户配额失败: %w", err)
	}

	// 3. 获取配额告警
	alerts := quota.GetQuotaAlerts()
	results := make([]model.QuotaAlertResult, 0)

	for _, alert := range alerts {
		// 过滤告警类型
		if query.AlertType != "" && alert.Type != query.AlertType {
			continue
		}

		// 过滤告警级别
		if query.Level != "" && alert.Level != query.Level {
			continue
		}

		results = append(results, model.QuotaAlertResult{
			Type:      alert.Type,
			Level:     alert.Level,
			Message:   alert.Message,
			Threshold: 80.0, // 默认告警阈值
			Current:   alert.Utilization,
			CreatedAt: time.Now(),
		})
	}

	h.logger.Info(ctx, "Quota alerts retrieved successfully", map[string]interface{}{
		"tenant_id":   query.TenantID,
		"alert_count": len(results),
	})

	return results, nil
}

// convertToTenantQuotaResult 转换为租户配额查询结果
func (h *TenantQuotaQueryHandler) convertToTenantQuotaResult(quota *entity.TenantQuota) *model.TenantQuotaResult {
	return &model.TenantQuotaResult{
		TenantID: quota.TenantID,

		// 用户配额
		UserUsed:        quota.UserUsed,
		UserLimit:       quota.UserLimit,
		UserUtilization: quota.GetUserUtilization(),

		// 存储配额
		StorageUsed:        quota.StorageUsed,
		StorageLimit:       quota.StorageLimit,
		StorageUtilization: quota.GetStorageUtilization(),

		// API配额
		APIUsedMonth:   quota.APIUsedMonth,
		APILimitMonth:  quota.APILimitMonth,
		APIUtilization: quota.GetAPIUtilization(),
		APIResetDate:   quota.APIResetDate,

		// 商品配额
		ProductUsed:        quota.ProductUsed,
		ProductLimit:       quota.ProductLimit,
		ProductUtilization: quota.GetProductUtilization(),

		// 订单配额
		OrderUsedMonth:   quota.OrderUsedMonth,
		OrderLimitMonth:  quota.OrderLimitMonth,
		OrderUtilization: h.calculateUtilization(int64(quota.OrderUsedMonth), int64(quota.OrderLimitMonth)),

		// 其他配额
		FileUploadUsed:  quota.FileUploadUsedMonth,
		FileUploadLimit: quota.FileUploadLimitMonth,
		EmailUsedMonth:  quota.EmailSentMonth,
		EmailLimitMonth: quota.EmailLimitMonth,

		// 状态信息
		LastUpdated: quota.UpdatedAt,
	}
}

// getQuotaUsageByType 根据类型获取配额使用情况
func (h *TenantQuotaQueryHandler) getQuotaUsageByType(quota *entity.TenantQuota, quotaType string) *model.QuotaUsageResult {
	switch quotaType {
	case "user":
		return &model.QuotaUsageResult{
			TenantID:   quota.TenantID,
			QuotaType:  "user",
			Used:       int64(quota.UserUsed),
			Limit:      int64(quota.UserLimit),
			Percentage: quota.GetUserUtilization(),
			UpdatedAt:  quota.UpdatedAt,
		}
	case "storage":
		return &model.QuotaUsageResult{
			TenantID:   quota.TenantID,
			QuotaType:  "storage",
			Used:       quota.StorageUsed,
			Limit:      quota.StorageLimit,
			Percentage: quota.GetStorageUtilization(),
			UpdatedAt:  quota.UpdatedAt,
		}
	case "api":
		return &model.QuotaUsageResult{
			TenantID:   quota.TenantID,
			QuotaType:  "api",
			Used:       int64(quota.APIUsedMonth),
			Limit:      int64(quota.APILimitMonth),
			Percentage: quota.GetAPIUtilization(),
			UpdatedAt:  quota.UpdatedAt,
		}
	case "product":
		return &model.QuotaUsageResult{
			TenantID:   quota.TenantID,
			QuotaType:  "product",
			Used:       int64(quota.ProductUsed),
			Limit:      int64(quota.ProductLimit),
			Percentage: quota.GetProductUtilization(),
			UpdatedAt:  quota.UpdatedAt,
		}
	case "order":
		return &model.QuotaUsageResult{
			TenantID:   quota.TenantID,
			QuotaType:  "order",
			Used:       int64(quota.OrderUsedMonth),
			Limit:      int64(quota.OrderLimitMonth),
			Percentage: h.calculateUtilization(int64(quota.OrderUsedMonth), int64(quota.OrderLimitMonth)),
			UpdatedAt:  quota.UpdatedAt,
		}
	case "file_upload":
		return &model.QuotaUsageResult{
			TenantID:   quota.TenantID,
			QuotaType:  "file_upload",
			Used:       int64(quota.FileUploadUsedMonth),
			Limit:      int64(quota.FileUploadLimitMonth),
			Percentage: h.calculateUtilization(int64(quota.FileUploadUsedMonth), int64(quota.FileUploadLimitMonth)),
			UpdatedAt:  quota.UpdatedAt,
		}
	case "email":
		return &model.QuotaUsageResult{
			TenantID:   quota.TenantID,
			QuotaType:  "email",
			Used:       int64(quota.EmailSentMonth),
			Limit:      int64(quota.EmailLimitMonth),
			Percentage: h.calculateUtilization(int64(quota.EmailSentMonth), int64(quota.EmailLimitMonth)),
			UpdatedAt:  quota.UpdatedAt,
		}
	default:
		return nil
	}
}

// checkOverQuota 检查是否超限
func (h *TenantQuotaQueryHandler) checkOverQuota(quota *entity.TenantQuota) bool {
	// 检查各种配额是否超限
	if quota.UserLimit > 0 && quota.UserUsed >= quota.UserLimit {
		return true
	}
	if quota.StorageLimit > 0 && quota.StorageUsed >= quota.StorageLimit {
		return true
	}
	if quota.APILimitMonth > 0 && quota.APIUsedMonth >= quota.APILimitMonth {
		return true
	}
	if quota.ProductLimit > 0 && quota.ProductUsed >= quota.ProductLimit {
		return true
	}
	if quota.OrderLimitMonth > 0 && quota.OrderUsedMonth >= quota.OrderLimitMonth {
		return true
	}
	if quota.FileUploadLimitMonth > 0 && quota.FileUploadUsedMonth >= quota.FileUploadLimitMonth {
		return true
	}
	if quota.EmailLimitMonth > 0 && quota.EmailSentMonth >= quota.EmailLimitMonth {
		return true
	}
	return false
}

// calculateUtilization 计算使用率
func (h *TenantQuotaQueryHandler) calculateUtilization(used, limit int64) float64 {
	if limit <= 0 {
		return 0.0
	}
	if limit == -1 { // 无限制
		return 0.0
	}
	utilization := float64(used) / float64(limit) * 100
	if utilization > 100 {
		return 100.0
	}
	return utilization
}
