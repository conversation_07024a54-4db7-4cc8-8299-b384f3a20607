package security

import (
	"context"
	"crypto/sha256"
	"encoding/hex"
	"strconv"
	"time"

	"backend/internal/application/assembler"
	"backend/internal/application/dto"
	"backend/internal/domain/auth/entity"
	"backend/internal/domain/auth/repository"
	"backend/internal/domain/auth/service"
	"backend/internal/shared/errors"
	"backend/pkg/infrastructure/snowflake"
)

// TokenUseCase Token管理用例
type TokenUseCase struct {
	tokenRepo      repository.TokenRepository
	sessionRepo    repository.SessionRepository
	preAuthRepo    repository.PreAuthRepository
	blacklistRepo  repository.BlacklistRepository
	tokenService   service.TokenService
	sessionService service.SessionService
	preAuthService service.PreAuthService
	assembler      *assembler.TokenAssembler
	idGenerator    *snowflake.Generator
}

// NewTokenUseCase 创建Token用例
func NewTokenUseCase(
	tokenRepo repository.TokenRepository,
	sessionRepo repository.SessionRepository,
	preAuthRepo repository.PreAuthRepository,
	blacklistRepo repository.BlacklistRepository,
	tokenService service.TokenService,
	sessionService service.SessionService,
	preAuthService service.PreAuthService,
	assembler *assembler.TokenAssembler,
	idGenerator *snowflake.Generator,
) *TokenUseCase {
	return &TokenUseCase{
		tokenRepo:      tokenRepo,
		sessionRepo:    sessionRepo,
		preAuthRepo:    preAuthRepo,
		blacklistRepo:  blacklistRepo,
		tokenService:   tokenService,
		sessionService: sessionService,
		preAuthService: preAuthService,
		assembler:      assembler,
		idGenerator:    idGenerator,
	}
}

// === Token管理用例 ===

// CreateTokenPair 创建Token对（访问令牌+刷新令牌）
func (uc *TokenUseCase) CreateTokenPair(ctx context.Context, req *dto.CreateTokenRequest) (*dto.TokenPairDTO, error) {
	// 生成会话ID（如果没有提供）
	sessionID := req.SessionID
	if sessionID == "" {
		sessionID = uc.generateTokenID()
	}

	// 生成Token ID
	accessTokenID := uc.generateTokenID()
	refreshTokenID := uc.generateTokenID()

	// 计算过期时间
	accessExpiresAt := time.Now().Add(2 * time.Hour)       // 访问令牌2小时
	refreshExpiresAt := time.Now().Add(7 * 24 * time.Hour) // 刷新令牌7天

	if req.ExpiresIn > 0 {
		accessExpiresAt = time.Now().Add(time.Duration(req.ExpiresIn) * time.Second)
	}

	// 创建访问令牌
	accessToken := entity.NewTokenStorage(
		accessTokenID,
		uc.generateTokenHash(accessTokenID),
		entity.TokenTypeAccess,
		req.UserID,
		req.TenantID,
		sessionID,
		req.DeviceID,
		accessExpiresAt,
	)
	accessToken.Scope = req.Scope

	// 创建刷新令牌
	refreshToken := entity.NewTokenStorage(
		refreshTokenID,
		uc.generateTokenHash(refreshTokenID),
		entity.TokenTypeRefresh,
		req.UserID,
		req.TenantID,
		sessionID,
		req.DeviceID,
		refreshExpiresAt,
	)

	// 保存Token
	if err := uc.tokenRepo.Save(ctx, accessToken); err != nil {
		return nil, err
	}
	if err := uc.tokenRepo.Save(ctx, refreshToken); err != nil {
		return nil, err
	}

	// 转换为DTO
	return uc.assembler.ToTokenPairDTO(accessToken, refreshToken), nil
}

// RefreshTokenPair 刷新Token对
func (uc *TokenUseCase) RefreshTokenPair(ctx context.Context, req *dto.RefreshTokenRequest) (*dto.TokenPairDTO, error) {
	// 查找刷新令牌
	refreshToken, err := uc.tokenRepo.FindByTokenHash(ctx, uc.generateTokenHash(req.RefreshToken))
	if err != nil {
		return nil, err
	}

	// 验证刷新令牌
	if !refreshToken.IsValid() {
		return nil, errors.TokenExpired()
	}

	// 检查是否在黑名单中
	if blacklisted, err := uc.blacklistRepo.IsTokenBlacklisted(ctx, refreshToken.TokenID); err != nil {
		return nil, err
	} else if blacklisted {
		return nil, errors.TokenBlacklisted(refreshToken.TokenID)
	}

	// 撤销旧的访问令牌
	if err := uc.tokenRepo.RevokeTokensBySession(ctx, refreshToken.SessionID); err != nil {
		return nil, err
	}

	// 创建新的Token对
	createReq := &dto.CreateTokenRequest{
		UserID:    refreshToken.UserID,
		TenantID:  refreshToken.TenantID,
		SessionID: refreshToken.SessionID,
		DeviceID:  refreshToken.DeviceID,
		TokenType: string(entity.TokenTypeAccess),
		Scope:     refreshToken.Scope,
	}

	return uc.CreateTokenPair(ctx, createReq)
}

// RevokeToken 撤销Token
func (uc *TokenUseCase) RevokeToken(ctx context.Context, req *dto.RevokeTokenRequest) (*dto.OperationResult, error) {
	// 查找Token
	token, err := uc.tokenRepo.FindByTokenID(ctx, req.TokenID)
	if err != nil {
		return nil, err
	}

	// 撤销Token
	token.Revoke()
	if err := uc.tokenRepo.Update(ctx, token); err != nil {
		return nil, err
	}

	// 加入黑名单
	blacklistEntry := entity.NewTokenBlacklistEntry(
		token.TokenID,
		token.TokenHash,
		token.TokenType,
		token.UserID,
		token.TenantID,
		token.SessionID,
		token.ExpiresAt,
		req.Reason,
	)
	if err := uc.blacklistRepo.Save(ctx, blacklistEntry); err != nil {
		return nil, err
	}

	return uc.assembler.ToOperationResult(true, "Token已成功撤销", nil), nil
}

// ValidateToken 验证Token
func (uc *TokenUseCase) ValidateToken(ctx context.Context, req *dto.ValidateTokenRequest) (*dto.ValidateTokenResponse, error) {
	var token *entity.TokenStorage
	var err error

	// 根据提供的参数查找Token
	if req.TokenID != "" {
		token, err = uc.tokenRepo.FindByTokenID(ctx, req.TokenID)
	} else if req.TokenValue != "" {
		tokenHash := uc.generateTokenHash(req.TokenValue)
		token, err = uc.tokenRepo.FindByTokenHash(ctx, tokenHash)
	} else {
		return uc.assembler.ToValidateTokenResponse(false, nil, nil, "缺少Token参数"), nil
	}

	if err != nil {
		return uc.assembler.ToValidateTokenResponse(false, nil, nil, "Token不存在"), nil
	}

	// 检查Token是否有效
	if !token.IsValid() {
		reason := "Token已过期"
		if token.Status == entity.TokenStatusRevoked {
			reason = "Token已被撤销"
		} else if token.Status == entity.TokenStatusBlacklisted {
			reason = "Token已被拉黑"
		}
		return uc.assembler.ToValidateTokenResponse(false, token, nil, reason), nil
	}

	// 检查黑名单
	if blacklisted, err := uc.blacklistRepo.IsTokenBlacklisted(ctx, token.TokenID); err != nil {
		return nil, err
	} else if blacklisted {
		return uc.assembler.ToValidateTokenResponse(false, token, nil, "Token已被拉黑"), nil
	}

	// 查找关联的会话
	var session *entity.UserSession
	if token.SessionID != "" {
		session, _ = uc.sessionRepo.FindBySessionID(ctx, token.SessionID)
	}

	// 更新最后使用时间
	token.UpdateLastUsed()
	uc.tokenRepo.Update(ctx, token)

	return uc.assembler.ToValidateTokenResponse(true, token, session, ""), nil
}

// === 前置认证用例 ===

// CreatePreAuth 创建前置认证
func (uc *TokenUseCase) CreatePreAuth(ctx context.Context, req *dto.CreatePreAuthRequest) (*dto.PreAuthTokenDTO, error) {
	// 生成上下文ID
	contextID := uc.generateTokenID()

	// 创建前置认证上下文
	preAuthContext := uc.assembler.FromCreatePreAuthRequest(req, contextID)
	if err := uc.preAuthRepo.Save(ctx, preAuthContext); err != nil {
		return nil, err
	}

	// 创建前置认证Token
	tokenID := uc.generateTokenID()
	preAuthToken := entity.NewTokenStorage(
		tokenID,
		uc.generateTokenHash(tokenID),
		entity.TokenTypePreAuth,
		req.UserID,
		req.TenantID,
		"", // 前置认证没有会话ID
		req.DeviceID,
		preAuthContext.ExpiresAt,
	)

	if err := uc.tokenRepo.Save(ctx, preAuthToken); err != nil {
		return nil, err
	}

	// 转换步骤枚举
	requiredSteps := make([]entity.AuthStep, len(req.RequiredSteps))
	for i, step := range req.RequiredSteps {
		requiredSteps[i] = entity.AuthStep(step)
	}

	return uc.assembler.ToPreAuthTokenDTO(contextID, preAuthToken, requiredSteps), nil
}

// CompleteAuthStep 完成认证步骤
func (uc *TokenUseCase) CompleteAuthStep(ctx context.Context, req *dto.CompleteStepRequest) (*dto.OperationResult, error) {
	// 查找前置认证上下文
	preAuthContext, err := uc.preAuthRepo.FindByContextID(ctx, req.ContextID)
	if err != nil {
		return nil, err
	}

	// 检查上下文是否有效
	if !preAuthContext.IsValid() {
		return nil, errors.PreAuthExpired()
	}

	// 检查尝试次数
	if !preAuthContext.CanAttempt() {
		return nil, errors.TooManyAttempts(preAuthContext.MaxAttempts)
	}

	// 完成步骤
	step := entity.AuthStep(req.Step)
	preAuthContext.CompleteStep(step)

	// 更新上下文
	if err := uc.preAuthRepo.Update(ctx, preAuthContext); err != nil {
		return nil, err
	}

	message := "认证步骤已完成"
	if preAuthContext.IsCompleted() {
		message = "所有认证步骤已完成"
	}

	return uc.assembler.ToOperationResult(true, message, nil), nil
}

// CompletePreAuth 完成前置认证，生成正式Token
func (uc *TokenUseCase) CompletePreAuth(ctx context.Context, contextID string) (*dto.TokenPairDTO, error) {
	// 查找前置认证上下文
	preAuthContext, err := uc.preAuthRepo.FindByContextID(ctx, contextID)
	if err != nil {
		return nil, err
	}

	// 检查是否已完成所有步骤
	if !preAuthContext.IsCompleted() {
		return nil, errors.PreAuthInvalid("认证步骤未完成")
	}

	// 标记前置认证完成
	if err := uc.preAuthRepo.MarkCompleted(ctx, contextID); err != nil {
		return nil, err
	}

	// 创建正式Token对
	createReq := &dto.CreateTokenRequest{
		UserID:   preAuthContext.UserID,
		TenantID: preAuthContext.TenantID,
		DeviceID: preAuthContext.DeviceID,
	}

	return uc.CreateTokenPair(ctx, createReq)
}

// === 会话管理用例 ===

// CreateSession 创建会话
func (uc *TokenUseCase) CreateSession(ctx context.Context, req *dto.CreateSessionRequest) (*dto.SessionDTO, error) {
	// 生成会话ID
	sessionID := uc.generateTokenID()

	// 检查并发会话限制
	activeCount, err := uc.sessionRepo.CountActiveByUser(ctx, req.UserID)
	if err != nil {
		return nil, err
	}

	maxConcurrent := 5 // 默认最多5个并发会话
	if activeCount >= int64(maxConcurrent) {
		return nil, errors.ConcurrentLoginLimit(maxConcurrent)
	}

	// 创建会话
	session := uc.assembler.FromCreateSessionRequest(req, sessionID)
	if err := uc.sessionRepo.Save(ctx, session); err != nil {
		return nil, err
	}

	return uc.assembler.ToSessionDTO(session), nil
}

// TerminateSession 终止会话
func (uc *TokenUseCase) TerminateSession(ctx context.Context, req *dto.TerminateSessionRequest) (*dto.OperationResult, error) {
	// 查找会话
	session, err := uc.sessionRepo.FindBySessionID(ctx, req.SessionID)
	if err != nil {
		return nil, err
	}

	// 终止会话
	session.Terminate()
	if err := uc.sessionRepo.Update(ctx, session); err != nil {
		return nil, err
	}

	// 撤销会话相关的所有Token
	if err := uc.tokenRepo.RevokeTokensBySession(ctx, req.SessionID); err != nil {
		return nil, err
	}

	return uc.assembler.ToOperationResult(true, "会话已成功终止", nil), nil
}

// === 查询用例 ===

// GetTokens 查询Token列表
func (uc *TokenUseCase) GetTokens(ctx context.Context, req *dto.TokenQueryRequest) (*dto.TokenListResponse, error) {
	// 转换查询条件
	query := uc.assembler.FromTokenQueryRequest(req)

	// 这里需要实现复杂查询逻辑，暂时简化
	// 实际实现中应该根据query条件进行数据库查询
	tokens := make([]*entity.TokenStorage, 0)
	totalCount := int64(0)

	page := query["page"].(int)
	pageSize := query["page_size"].(int)

	return uc.assembler.ToTokenListResponse(tokens, totalCount, page, pageSize), nil
}

// GetSessions 查询会话列表
func (uc *TokenUseCase) GetSessions(ctx context.Context, req *dto.SessionQueryRequest) (*dto.SessionListResponse, error) {
	// 转换查询条件
	query := uc.assembler.FromSessionQueryRequest(req)

	// 这里需要实现复杂查询逻辑，暂时简化
	sessions := make([]*entity.UserSession, 0)
	totalCount := int64(0)

	page := query["page"].(int)
	pageSize := query["page_size"].(int)

	return uc.assembler.ToSessionListResponse(sessions, totalCount, page, pageSize), nil
}

// === 清理用例 ===

// CleanupExpired 清理过期数据
func (uc *TokenUseCase) CleanupExpired(ctx context.Context) (*dto.CleanupResult, error) {
	now := time.Now()

	// 清理过期Token
	expiredTokens, err := uc.tokenRepo.DeleteExpiredTokens(ctx, now)
	if err != nil {
		return nil, err
	}

	// 清理过期会话
	expiredSessions, err := uc.sessionRepo.DeleteExpiredSessions(ctx, now)
	if err != nil {
		return nil, err
	}

	// 清理过期黑名单
	expiredBlacklist, err := uc.blacklistRepo.DeleteExpiredEntries(ctx, now)
	if err != nil {
		return nil, err
	}

	// 清理过期前置认证
	expiredPreAuth, err := uc.preAuthRepo.DeleteExpiredContexts(ctx, now)
	if err != nil {
		return nil, err
	}

	return uc.assembler.ToCleanupResult(expiredTokens, expiredSessions, expiredBlacklist, expiredPreAuth), nil
}

// === 辅助方法 ===

// generateTokenID 生成Token ID
func (uc *TokenUseCase) generateTokenID() string {
	id := uc.idGenerator.Generate()
	return strconv.FormatInt(id, 10)
}

// generateTokenHash 生成Token哈希
func (uc *TokenUseCase) generateTokenHash(token string) string {
	hash := sha256.Sum256([]byte(token))
	return hex.EncodeToString(hash[:])
}
