package user

import (
	"context"
	"fmt"

	"backend/internal/domain/user/entity"
	userRepo "backend/internal/domain/user/repository"
	userValueObject "backend/internal/domain/user/valueobject"
	"backend/internal/shared/errors"
	"backend/internal/shared/transaction"
	"backend/pkg/infrastructure/logger"
)

// UserManagementUseCase 用户管理用例 - 处理用户生命周期操作
type UserManagementUseCase struct {
	txManager      transaction.TransactionManager
	userRepo       userRepo.UserRepository
	userTenantRepo userRepo.UserTenantRepository
	logger         logger.Logger
}

// NewUserManagementUseCase 创建用户管理用例
func NewUserManagementUseCase(
	txManager transaction.TransactionManager,
	userRepo userRepo.UserRepository,
	userTenantRepo userRepo.UserTenantRepository,
	logger logger.Logger,
) *UserManagementUseCase {
	return &UserManagementUseCase{
		txManager:      txManager,
		userRepo:       userRepo,
		userTenantRepo: userTenantRepo,
		logger:         logger,
	}
}

// GetUserRequest 获取用户请求
type GetUserRequest struct {
	UserID   string `json:"user_id" validate:"required"`
	TenantID string `json:"tenant_id" validate:"required"`
}

// GetUserResponse 获取用户响应
type GetUserResponse struct {
	UserID    string                      `json:"user_id"`
	Username  string                      `json:"username"`
	Email     string                      `json:"email"`
	Phone     string                      `json:"phone"`
	Profile   userValueObject.UserProfile `json:"profile"`
	Status    string                      `json:"status"`
	CreatedAt string                      `json:"created_at"`
	UpdatedAt string                      `json:"updated_at"`
}

// UpdateUserProfileRequest 更新用户资料请求
type UpdateUserProfileRequest struct {
	UserID    string `json:"user_id" validate:"required"`
	TenantID  string `json:"tenant_id" validate:"required"`
	FirstName string `json:"first_name,omitempty"`
	LastName  string `json:"last_name,omitempty"`
	Nickname  string `json:"nickname,omitempty"`
	Avatar    string `json:"avatar,omitempty"`
	Language  string `json:"language,omitempty"`
	Timezone  string `json:"timezone,omitempty"`
}

// UpdateUserProfileResponse 更新用户资料响应
type UpdateUserProfileResponse struct {
	UserID  string                      `json:"user_id"`
	Profile userValueObject.UserProfile `json:"profile"`
}

// ActivateUserRequest 激活用户请求
type ActivateUserRequest struct {
	UserID   string `json:"user_id" validate:"required"`
	TenantID string `json:"tenant_id" validate:"required"`
}

// DeactivateUserRequest 禁用用户请求
type DeactivateUserRequest struct {
	UserID   string `json:"user_id" validate:"required"`
	TenantID string `json:"tenant_id" validate:"required"`
	Reason   string `json:"reason,omitempty"`
}

// ListUsersRequest 获取用户列表请求
type ListUsersRequest struct {
	TenantID string `json:"tenant_id" validate:"required"`
	Keyword  string `json:"keyword,omitempty"`
	Status   string `json:"status,omitempty"`
	Page     int    `json:"page,omitempty" validate:"min=1"`
	PageSize int    `json:"page_size,omitempty" validate:"min=1,max=100"`
}

// ListUsersResponse 获取用户列表响应
type ListUsersResponse struct {
	Total       int64             `json:"total"`
	Page        int               `json:"page"`
	PageSize    int               `json:"page_size"`
	TotalPages  int               `json:"total_pages"`
	HasNext     bool              `json:"has_next"`
	HasPrevious bool              `json:"has_previous"`
	Items       []GetUserResponse `json:"items"`
}

// GetUserProfile 获取用户资料
func (uc *UserManagementUseCase) GetUserProfile(ctx context.Context, req *GetUserRequest) (*GetUserResponse, error) {
	// 1. 验证用户在租户中的权限
	if err := uc.validateUserTenantAccess(ctx, req.UserID, req.TenantID); err != nil {
		return nil, err
	}

	// 2. 获取用户信息
	user, err := uc.userRepo.FindByBusinessID(ctx, req.UserID)
	if err != nil {
		uc.logger.Error(ctx, "查找用户失败", "user_id", req.UserID, "error", err)
		return nil, errors.UserNotFound(req.UserID)
	}

	// 3. 构建响应
	response := &GetUserResponse{
		UserID:    user.BusinessID,
		Username:  user.Username,
		Email:     user.Email,
		Phone:     user.Phone,
		Profile:   user.Profile,
		Status:    user.Status.String(),
		CreatedAt: user.CreatedAt.Format("2006-01-02 15:04:05"),
		UpdatedAt: user.UpdatedAt.Format("2006-01-02 15:04:05"),
	}

	return response, nil
}

// UpdateUserProfile 更新用户资料
func (uc *UserManagementUseCase) UpdateUserProfile(ctx context.Context, req *UpdateUserProfileRequest) (*UpdateUserProfileResponse, error) {
	// 1. 验证用户在租户中的权限
	if err := uc.validateUserTenantAccess(ctx, req.UserID, req.TenantID); err != nil {
		return nil, err
	}

	var result *UpdateUserProfileResponse

	// 2. 在事务中执行更新操作
	err := uc.txManager.ExecuteInTransaction(ctx, func(txCtx context.Context) error {
		// 2.1 获取用户实体
		user, err := uc.userRepo.FindByBusinessID(txCtx, req.UserID)
		if err != nil {
			return errors.UserNotFound(req.UserID)
		}

		// 2.2 更新用户资料（使用领域方法）
		newProfile := userValueObject.UserProfile{
			FirstName: req.FirstName,
			LastName:  req.LastName,
			Nickname:  req.Nickname,
			Avatar:    req.Avatar,
			Language:  req.Language,
			Timezone:  req.Timezone,
		}

		user.UpdateProfile(newProfile)

		// 2.3 保存用户
		if err := uc.userRepo.Update(txCtx, user); err != nil {
			return fmt.Errorf("保存用户资料失败: %w", err)
		}

		// 2.4 构建响应
		result = &UpdateUserProfileResponse{
			UserID:  user.BusinessID,
			Profile: user.Profile,
		}

		return nil
	})

	if err != nil {
		uc.logger.Error(ctx, "更新用户资料失败", "user_id", req.UserID, "error", err)
		return nil, err
	}

	uc.logger.Info(ctx, "用户资料更新成功", "user_id", req.UserID)
	return result, nil
}

// ActivateUser 激活用户
func (uc *UserManagementUseCase) ActivateUser(ctx context.Context, req *ActivateUserRequest) error {
	// 1. 验证用户在租户中的权限
	if err := uc.validateUserTenantAccess(ctx, req.UserID, req.TenantID); err != nil {
		return err
	}

	// 2. 在事务中执行激活操作
	return uc.txManager.ExecuteInTransaction(ctx, func(txCtx context.Context) error {
		// 2.1 获取用户实体
		user, err := uc.userRepo.FindByBusinessID(txCtx, req.UserID)
		if err != nil {
			return errors.UserNotFound(req.UserID)
		}

		// 2.2 激活用户（使用领域方法）
		if err := user.Activate(); err != nil {
			return fmt.Errorf("激活用户失败: %w", err)
		}

		// 2.3 保存用户状态
		if err := uc.userRepo.Update(txCtx, user); err != nil {
			return fmt.Errorf("保存用户状态失败: %w", err)
		}

		uc.logger.Info(ctx, "用户激活成功", "user_id", req.UserID)
		return nil
	})
}

// DeactivateUser 禁用用户
func (uc *UserManagementUseCase) DeactivateUser(ctx context.Context, req *DeactivateUserRequest) error {
	// 1. 验证用户在租户中的权限
	if err := uc.validateUserTenantAccess(ctx, req.UserID, req.TenantID); err != nil {
		return err
	}

	// 2. 在事务中执行禁用操作
	return uc.txManager.ExecuteInTransaction(ctx, func(txCtx context.Context) error {
		// 2.1 获取用户实体
		user, err := uc.userRepo.FindByBusinessID(txCtx, req.UserID)
		if err != nil {
			return errors.UserNotFound(req.UserID)
		}

		// 2.2 禁用用户（使用领域方法）
		if err := user.Deactivate(); err != nil {
			return fmt.Errorf("禁用用户失败: %w", err)
		}

		// 2.3 保存用户状态
		if err := uc.userRepo.Update(txCtx, user); err != nil {
			return fmt.Errorf("保存用户状态失败: %w", err)
		}

		uc.logger.Info(ctx, "用户禁用成功", "user_id", req.UserID, "reason", req.Reason)
		return nil
	})
}

// ListUsers 获取用户列表（支持分页和筛选）
func (uc *UserManagementUseCase) ListUsers(ctx context.Context, req *ListUsersRequest) (*ListUsersResponse, error) {
	// 1. 验证请求参数
	if err := uc.validateListUsersRequest(req); err != nil {
		return nil, err
	}

	// 2. 设置默认分页参数
	if req.Page <= 0 {
		req.Page = 1
	}
	if req.PageSize <= 0 {
		req.PageSize = 10
	}
	if req.PageSize > 100 {
		req.PageSize = 100
	}

	// 3. 计算偏移量
	offset := (req.Page - 1) * req.PageSize

	// 4. 根据是否有关键词选择查询方法
	var users []*entity.User
	var total int64
	var err error

	if req.Keyword != "" {
		// 使用搜索功能
		users, total, err = uc.userRepo.SearchUsers(ctx, req.TenantID, req.Keyword, req.PageSize, offset)
	} else {
		// 使用普通分页查询
		users, total, err = uc.userRepo.FindByTenantID(ctx, req.TenantID, req.PageSize, offset)
	}

	if err != nil {
		uc.logger.Error(ctx, "查询用户列表失败", "tenant_id", req.TenantID, "error", err)
		return nil, errors.UserListFailed(err)
	}

	// 5. 过滤用户状态（如果指定了状态筛选）
	if req.Status != "" {
		users = uc.filterUsersByStatus(users, req.Status)
		// 重新计算总数（这里简化处理，实际应该在数据库层面过滤）
		total = int64(len(users))
	}

	// 6. 转换为响应格式
	items := make([]GetUserResponse, 0, len(users))
	for _, user := range users {
		items = append(items, GetUserResponse{
			UserID:    user.BusinessID,
			Username:  user.Username,
			Email:     user.Email,
			Phone:     user.Phone,
			Profile:   user.Profile,
			Status:    user.Status.String(),
			CreatedAt: user.CreatedAt.Format("2006-01-02 15:04:05"),
			UpdatedAt: user.UpdatedAt.Format("2006-01-02 15:04:05"),
		})
	}

	// 7. 计算分页信息
	totalPages := int((total + int64(req.PageSize) - 1) / int64(req.PageSize))
	hasNext := req.Page < totalPages
	hasPrevious := req.Page > 1

	// 8. 构建响应
	response := &ListUsersResponse{
		Total:       total,
		Page:        req.Page,
		PageSize:    req.PageSize,
		TotalPages:  totalPages,
		HasNext:     hasNext,
		HasPrevious: hasPrevious,
		Items:       items,
	}

	uc.logger.Info(ctx, "用户列表查询成功",
		"tenant_id", req.TenantID,
		"total", total,
		"page", req.Page,
		"page_size", req.PageSize)

	return response, nil
}

// validateListUsersRequest 验证用户列表请求参数
func (uc *UserManagementUseCase) validateListUsersRequest(req *ListUsersRequest) error {
	if req.TenantID == "" {
		return errors.ValidationFailed("租户ID不能为空")
	}

	if req.Page < 0 {
		return errors.ValidationFailed("页码不能小于0")
	}

	if req.PageSize < 0 || req.PageSize > 100 {
		return errors.ValidationFailed("页面大小必须在1-100之间")
	}

	// 验证状态参数（如果提供）
	if req.Status != "" {
		validStatuses := []string{"pending", "active", "inactive", "suspended", "banned"}
		isValid := false
		for _, status := range validStatuses {
			if req.Status == status {
				isValid = true
				break
			}
		}
		if !isValid {
			return errors.ValidationFailed("无效的用户状态: " + req.Status)
		}
	}

	return nil
}

// filterUsersByStatus 根据状态过滤用户
func (uc *UserManagementUseCase) filterUsersByStatus(users []*entity.User, status string) []*entity.User {
	if status == "" {
		return users
	}

	filtered := make([]*entity.User, 0)
	for _, user := range users {
		if user.Status.String() == status {
			filtered = append(filtered, user)
		}
	}
	return filtered
}

// validateUserTenantAccess 验证用户在指定租户中的访问权限
func (uc *UserManagementUseCase) validateUserTenantAccess(ctx context.Context, userID, tenantID string) error {
	userTenants, err := uc.userTenantRepo.FindByUserBusinessID(ctx, userID)
	if err != nil {
		return fmt.Errorf("查询用户租户关联失败: %w", err)
	}

	// 检查用户是否有权限访问指定租户
	for _, ut := range userTenants {
		if ut.TenantBusinessID == tenantID {
			return nil // 用户有权限访问此租户
		}
	}

	return errors.Forbidden("用户无权访问指定租户")
}
