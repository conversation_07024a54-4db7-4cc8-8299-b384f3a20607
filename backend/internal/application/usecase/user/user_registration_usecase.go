package user

import (
	"context"
	"fmt"

	authEntity "backend/internal/domain/auth/entity"
	authRepo "backend/internal/domain/auth/repository"
	userEntity "backend/internal/domain/user/entity"
	userRepo "backend/internal/domain/user/repository"
	"backend/internal/domain/user/valueobject"
	"backend/internal/shared/errors"
	"backend/internal/shared/transaction"
	"backend/pkg/infrastructure/logger"
	"backend/pkg/infrastructure/snowflake"
)

// UserRegistrationUseCase 用户注册用例 - 用户生命周期管理
type UserRegistrationUseCase struct {
	txManager      transaction.TransactionManager
	userRepo       userRepo.UserRepository
	userAuthRepo   authRepo.UserAuthRepository
	userTenantRepo userRepo.UserTenantRepository
	snowflakeGen   *snowflake.Generator
	logger         logger.Logger
}

// NewUserRegistrationUseCase 创建用户注册用例
func NewUserRegistrationUseCase(
	txManager transaction.TransactionManager,
	userRepo userRepo.UserRepository,
	userAuthRepo authRepo.UserAuthRepository,
	userTenantRepo userRepo.UserTenantRepository,
	snowflakeGen *snowflake.Generator,
	logger logger.Logger,
) *UserRegistrationUseCase {
	return &UserRegistrationUseCase{
		txManager:      txManager,
		userRepo:       userRepo,
		userAuthRepo:   userAuthRepo,
		userTenantRepo: userTenantRepo,
		snowflakeGen:   snowflakeGen,
		logger:         logger,
	}
}

// RegisterUserRequest 用户注册请求
type RegisterUserRequest struct {
	TenantID  string `json:"tenant_id" validate:"required"`
	Username  string `json:"username" validate:"required,min=3,max=50"`
	Email     string `json:"email" validate:"required,email"`
	Phone     string `json:"phone" validate:"required"`
	Password  string `json:"password" validate:"required,min=8"`
	FirstName string `json:"first_name,omitempty"`
	LastName  string `json:"last_name,omitempty"`
	Language  string `json:"language,omitempty"`
	Timezone  string `json:"timezone,omitempty"`
}

// RegisterUserResponse 用户注册响应
type RegisterUserResponse struct {
	UserID    string `json:"user_id"`
	Username  string `json:"username"`
	Email     string `json:"email"`
	Phone     string `json:"phone"`
	FirstName string `json:"first_name"`
	LastName  string `json:"last_name"`
}

// Execute 执行用户注册流程
func (uc *UserRegistrationUseCase) Execute(ctx context.Context, req *RegisterUserRequest) (*RegisterUserResponse, error) {
	// 1. 业务规则验证 - 检查用户是否已存在（事务外检查，避免长时间锁定）
	if err := uc.validateUserNotExists(ctx, req); err != nil {
		return nil, err
	}

	var result *RegisterUserResponse

	// 2. 在事务中执行用户创建流程
	err := uc.txManager.ExecuteInTransaction(ctx, func(txCtx context.Context) error {
		// 2.1 创建用户实体（用户生命周期管理的核心职责）
		user, err := uc.createUserEntity(req)
		if err != nil {
			return fmt.Errorf("创建用户实体失败: %w", err)
		}

		// 2.2 保存用户到用户仓储
		if err := uc.userRepo.Save(txCtx, user); err != nil {
			return fmt.Errorf("保存用户失败: %w", err)
		}

		// 2.3 创建用户认证信息（委托给Security领域）
		if err := uc.createUserAuthentication(txCtx, user.BusinessID, req); err != nil {
			return fmt.Errorf("创建用户认证信息失败: %w", err)
		}

		// 2.4 建立用户与租户的关联
		if err := uc.associateUserWithTenant(txCtx, user.BusinessID, req.TenantID); err != nil {
			return fmt.Errorf("关联用户与租户失败: %w", err)
		}

		// 2.5 构建响应结果
		result = &RegisterUserResponse{
			UserID:    user.BusinessID,
			Username:  user.Username,
			Email:     user.Email,
			Phone:     user.Phone,
			FirstName: req.FirstName,
			LastName:  req.LastName,
		}

		return nil
	})

	if err != nil {
		uc.logger.Error(ctx, "用户注册失败", "error", err, "email", req.Email, "phone", req.Phone)
		return nil, err
	}

	uc.logger.Info(ctx, "用户注册成功", "user_id", result.UserID, "email", req.Email)
	return result, nil
}

// validateUserNotExists 验证用户是否已存在
func (uc *UserRegistrationUseCase) validateUserNotExists(ctx context.Context, req *RegisterUserRequest) error {
	// 检查邮箱是否已注册
	emailExists, err := uc.userRepo.ExistsByEmail(ctx, req.Email)
	if err != nil {
		return fmt.Errorf("检查邮箱是否存在失败: %w", err)
	}
	if emailExists {
		return errors.UserExists(req.Email)
	}

	// 检查手机号是否已注册
	phoneExists, err := uc.userRepo.ExistsByPhone(ctx, req.Phone)
	if err != nil {
		return fmt.Errorf("检查手机号是否存在失败: %w", err)
	}
	if phoneExists {
		return errors.UserExists(req.Phone)
	}

	return nil
}

// createUserEntity 创建用户实体（用户领域的核心逻辑）
func (uc *UserRegistrationUseCase) createUserEntity(req *RegisterUserRequest) (*userEntity.User, error) {
	// 构建用户档案
	profile := valueobject.UserProfile{
		Nickname:  req.Username,
		FirstName: req.FirstName,
		LastName:  req.LastName,
		Language:  req.Language,
		Timezone:  req.Timezone,
	}

	// 创建用户实体
	user, err := userEntity.NewUser(profile, req.Username, req.Email, req.Phone)
	if err != nil {
		return nil, fmt.Errorf("创建用户实体失败: %w", err)
	}

	// 设置技术ID
	user.ID = uc.snowflakeGen.Generate()

	return user, nil
}

// createUserAuthentication 创建用户认证信息（委托给Security领域）
func (uc *UserRegistrationUseCase) createUserAuthentication(ctx context.Context, userBusinessID string, req *RegisterUserRequest) error {
	// 创建主要认证方式（手机号认证）
	phoneAuth, err := authEntity.NewUserAuth(userBusinessID, "phone", req.Phone, req.Password)
	if err != nil {
		return fmt.Errorf("创建手机认证失败: %w", err)
	}
	phoneAuth.ID = uc.snowflakeGen.Generate()

	if err := uc.userAuthRepo.Save(ctx, phoneAuth); err != nil {
		return fmt.Errorf("保存手机认证失败: %w", err)
	}

	// 创建邮箱认证方式（可选，用于多种登录方式）
	if req.Email != req.Phone {
		emailAuth, err := authEntity.NewUserAuth(userBusinessID, "email", req.Email, req.Password)
		if err != nil {
			return fmt.Errorf("创建邮箱认证失败: %w", err)
		}
		emailAuth.ID = uc.snowflakeGen.Generate()

		if err := uc.userAuthRepo.Save(ctx, emailAuth); err != nil {
			return fmt.Errorf("保存邮箱认证失败: %w", err)
		}
	}

	return nil
}

// associateUserWithTenant 建立用户与租户的关联
func (uc *UserRegistrationUseCase) associateUserWithTenant(ctx context.Context, userBusinessID, tenantID string) error {
	// TODO: 角色应该从租户的默认设置或邀请信息中获取
	// 这里暂时使用默认的 "member" 角色
	defaultRole := "member"

	userTenant := userEntity.NewUserTenant(userBusinessID, tenantID, defaultRole)
	userTenant.ID = uc.snowflakeGen.Generate()

	return uc.userTenantRepo.Create(ctx, userTenant)
}
