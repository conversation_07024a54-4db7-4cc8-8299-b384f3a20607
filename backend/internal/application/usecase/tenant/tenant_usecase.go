package tenant

import (
	"context"
	"time"

	"backend/internal/application/assembler"
	"backend/internal/application/dto"
	paginationDTO "backend/internal/application/pagination/dto"
	"backend/internal/domain/tenant/entity"
	"backend/internal/domain/tenant/repository"
	"backend/internal/domain/tenant/service"
	"backend/internal/domain/tenant/valueobject"
	"backend/internal/shared/errors"
	"backend/pkg/common/pagination"
	"backend/pkg/common/utils"
)

// TenantUseCase 租户用例
type TenantUseCase struct {
	tenantRepo       repository.TenantRepository
	quotaRepo        repository.TenantQuotaRepository
	subscriptionRepo repository.TenantSubscriptionRepository
	tenantService    service.TenantService
	assembler        *assembler.TenantAssembler
}

// NewTenantUseCase 创建租户用例
func NewTenantUseCase(
	tenantRepo repository.TenantRepository,
	quotaRepo repository.TenantQuotaRepository,
	subscriptionRepo repository.TenantSubscriptionRepository,
	tenantService service.TenantService,
	assembler *assembler.TenantAssembler,
) *TenantUseCase {
	return &TenantUseCase{
		tenantRepo:       tenantRepo,
		quotaRepo:        quotaRepo,
		subscriptionRepo: subscriptionRepo,
		tenantService:    tenantService,
		assembler:        assembler,
	}
}

// CreateTenant 创建租户
func (uc *TenantUseCase) CreateTenant(ctx context.Context, req *dto.CreateTenantRequestDTO) (*dto.TenantDTO, error) {
	// 验证域名唯一性
	existingTenant, err := uc.tenantRepo.GetByDomain(ctx, req.Domain)
	if err != nil && !isNotFoundError(err) {
		return nil, errors.TenantDomainCheckFailed()
	}
	if existingTenant != nil {
		return nil, errors.Conflict("tenant", "domain", req.Domain)
	}

	// 转换DTO为服务请求
	serviceReq := service.CreateTenantRequest{
		Name:         req.Name,
		Domain:       req.Domain,
		DisplayName:  req.DisplayName,
		Description:  req.Description,
		Type:         valueobject.TenantType(req.Type),
		Industry:     req.Industry,
		Country:      req.Country,
		Province:     req.Province,
		City:         req.City,
		Address:      req.Address,
		ContactEmail: req.ContactEmail,
		ContactPhone: req.ContactPhone,
	}

	// 使用领域服务创建租户
	createdTenant, err := uc.tenantService.CreateTenant(ctx, serviceReq)
	if err != nil {
		return nil, errors.TenantCreateFailed(err.Error())
	}

	return uc.assembler.EntityToDTO(createdTenant), nil
}

// GetTenant 获取租户详情
func (uc *TenantUseCase) GetTenant(ctx context.Context, tenantID string) (*dto.TenantDTO, error) {
	tenant, err := uc.tenantRepo.GetByID(ctx, tenantID)
	if err != nil {
		if isNotFoundError(err) {
			return nil, errors.TenantNotFound(tenantID)
		}
		return nil, errors.Database(err)
	}

	return uc.assembler.EntityToDTO(tenant), nil
}

// UpdateTenant 更新租户信息
func (uc *TenantUseCase) UpdateTenant(ctx context.Context, tenantID string, req *dto.UpdateTenantRequestDTO) (*dto.TenantDTO, error) {
	tenant, err := uc.tenantRepo.GetByID(ctx, tenantID)
	if err != nil {
		if isNotFoundError(err) {
			return nil, errors.TenantNotFound(tenantID)
		}
		return nil, errors.Database(err)
	}

	// 更新字段
	if req.Name != nil {
		tenant.Name = *req.Name
	}
	if req.DisplayName != nil {
		tenant.DisplayName = *req.DisplayName
	}
	if req.Description != nil {
		tenant.Description = *req.Description
	}
	if req.Industry != nil {
		tenant.Industry = *req.Industry
	}
	if req.Country != nil {
		tenant.Country = *req.Country
	}
	if req.Province != nil {
		tenant.Province = *req.Province
	}
	if req.City != nil {
		tenant.City = *req.City
	}
	if req.Address != nil {
		tenant.Address = *req.Address
	}
	if req.ContactEmail != nil {
		tenant.ContactEmail = *req.ContactEmail
	}
	if req.ContactPhone != nil {
		tenant.ContactPhone = *req.ContactPhone
	}

	err = uc.tenantRepo.Update(ctx, tenant)
	if err != nil {
		return nil, errors.TenantUpdateFailed("update", err)
	}

	return uc.assembler.EntityToDTO(tenant), nil
}

// ActivateTenant 激活租户
func (uc *TenantUseCase) ActivateTenant(ctx context.Context, tenantID string) error {
	tenant, err := uc.tenantRepo.GetByID(ctx, tenantID)
	if err != nil {
		if isNotFoundError(err) {
			return errors.TenantNotFound(tenantID)
		}
		return errors.Database(err)
	}

	err = tenant.Activate()
	if err != nil {
		return errors.Business(err.Error())
	}

	err = uc.tenantRepo.Update(ctx, tenant)
	if err != nil {
		return errors.TenantUpdateFailed("activate", err)
	}

	return nil
}

// SuspendTenant 暂停租户
func (uc *TenantUseCase) SuspendTenant(ctx context.Context, tenantID string, reason string) error {
	tenant, err := uc.tenantRepo.GetByID(ctx, tenantID)
	if err != nil {
		if isNotFoundError(err) {
			return errors.TenantNotFound(tenantID)
		}
		return errors.Database(err)
	}

	err = tenant.Suspend(reason)
	if err != nil {
		return errors.Business(err.Error())
	}

	err = uc.tenantRepo.Update(ctx, tenant)
	if err != nil {
		return errors.TenantUpdateFailed("suspend", err)
	}

	return nil
}

// TerminateTenant 终止租户
func (uc *TenantUseCase) TerminateTenant(ctx context.Context, tenantID string, reason string) error {
	tenant, err := uc.tenantRepo.GetByID(ctx, tenantID)
	if err != nil {
		if isNotFoundError(err) {
			return errors.TenantNotFound(tenantID)
		}
		return errors.Database(err)
	}

	err = tenant.Terminate(reason)
	if err != nil {
		return errors.Business(err.Error())
	}

	err = uc.tenantRepo.Update(ctx, tenant)
	if err != nil {
		return errors.TenantUpdateFailed("terminate", err)
	}

	return nil
}

// // ListTenants 获取租户列表
// func (uc *TenantUseCase) ListTenants(ctx context.Context, filter *dto.TenantFilterRequestDTO, pagination *dto.PaginationRequestDTO) (*dto.TenantListDTO, error) {
// 	// 转换过滤条件
// 	tenantFilter := uc.buildTenantFilter(filter)
// 	tenantPagination := uc.buildTenantPagination(pagination)

// 	// 获取租户列表
// 	tenants, result, err := uc.tenantRepo.List(ctx, tenantFilter, tenantPagination)
// 	if err != nil {
// 		return nil, errors.TenantListFailed(err)
// 	}

// 	return uc.assembler.BuildTenantListDTO(tenants, result.Total, result.Page, result.PageSize), nil
// }

// ListTenants 获取租户列表
func (uc *TenantUseCase) ListTenants(ctx context.Context, req *paginationDTO.PaginationRequestDTO) (*paginationDTO.PaginationResponseDTO[*dto.TenantDTO], error) {
	// 验证分页参数
	if err := uc.validatePaginationRequest(req); err != nil {
		return nil, err
	}

	// 转换为通用分页请求
	paginationReq := req.ToCommonRequest()

	// 构建过滤条件
	filter := uc.buildTenantFilterFromSearch(req.Search)

	// 创建分页服务
	service := pagination.NewService[*entity.Tenant](
		&tenantRepositoryAdapter{repo: uc.tenantRepo, filter: filter},
		pagination.NewNullCache(), // 暂时不使用缓存
		nil,                       // 使用默认配置
	)

	// 执行分页查询
	response, err := service.Paginate(ctx, paginationReq)
	if err != nil {
		return nil, errors.TenantListFailed(err)
	}

	// 转换实体为DTO
	tenantDTOs := make([]*dto.TenantDTO, len(response.Items))
	for i, tenant := range response.Items {
		tenantDTOs[i] = uc.assembler.EntityToDTO(tenant)
	}

	// 创建新的响
	newResponse := &pagination.Response[*dto.TenantDTO]{
		Items:     tenantDTOs,
		Meta:      response.Meta,
		Links:     response.Links,
		Timestamp: response.Timestamp,
	}

	return paginationDTO.FromCommonResponse(newResponse), nil
}

// SearchTenants 搜索租户
func (uc *TenantUseCase) SearchTenants(ctx context.Context, req *paginationDTO.PaginationRequestDTO, filters map[string]interface{}) (*paginationDTO.PaginationResponseDTO[*dto.TenantDTO], error) {
	// 验证分页参数
	if err := uc.validatePaginationRequest(req); err != nil {
		return nil, err
	}

	// 转换为通用分页请求
	paginationReq := req.ToCommonRequest()

	// 构建高级过滤条件
	filter := uc.buildAdvancedTenantFilter(req.Search, filters)

	// 创建分页服务
	service := pagination.NewService[*entity.Tenant](
		&tenantRepositoryAdapter{repo: uc.tenantRepo, filter: filter},
		pagination.NewNullCache(),
		nil,
	)

	// 执行分页查询
	response, err := service.Paginate(ctx, paginationReq)
	if err != nil {
		return nil, errors.TenantSearchFailed(err)
	}

	// 转换实体为DTO
	tenantDTOs := make([]*dto.TenantDTO, len(response.Items))
	for i, tenant := range response.Items {
		tenantDTOs[i] = uc.assembler.EntityToDTO(tenant)
	}

	// 创建新的响应
	newResponse := &pagination.Response[*dto.TenantDTO]{
		Items:     tenantDTOs,
		Meta:      response.Meta,
		Links:     response.Links,
		Timestamp: response.Timestamp,
	}

	return paginationDTO.FromCommonResponse(newResponse), nil
}

// GetTenantQuota 获取租户配额信息
func (uc *TenantUseCase) GetTenantQuota(ctx context.Context, tenantID string) (*dto.TenantQuotaDTO, error) {
	quota, err := uc.quotaRepo.GetByTenantID(ctx, tenantID)
	if err != nil {
		if isNotFoundError(err) {
			return nil, errors.NotFound("quota", tenantID)
		}
		return nil, errors.Database(err)
	}

	return uc.assembler.QuotaEntityToDTO(quota), nil
}

// UpdateTenantQuota 更新租户配额
func (uc *TenantUseCase) UpdateTenantQuota(ctx context.Context, tenantID string, req *dto.UpdateQuotaLimitsRequestDTO) (*dto.TenantQuotaDTO, error) {
	quota, err := uc.quotaRepo.GetByTenantID(ctx, tenantID)
	if err != nil {
		if isNotFoundError(err) {
			return nil, errors.NotFound("quota", tenantID)
		}
		return nil, errors.Database(err)
	}

	// 更新配额限制
	userLimit := quota.UserLimit
	storageLimit := quota.StorageLimit
	apiLimit := quota.APILimitMonth
	productLimit := quota.ProductLimit
	orderLimit := quota.OrderLimitMonth
	fileUploadLimit := quota.FileUploadLimitMonth
	emailLimit := quota.EmailLimitMonth

	if req.UserLimit != nil {
		userLimit = *req.UserLimit
	}
	if req.StorageLimit != nil {
		storageLimit = *req.StorageLimit
	}
	if req.APILimit != nil {
		apiLimit = *req.APILimit
	}
	if req.ProductLimit != nil {
		productLimit = *req.ProductLimit
	}
	if req.OrderLimit != nil {
		orderLimit = *req.OrderLimit
	}
	if req.FileUploadLimit != nil {
		fileUploadLimit = *req.FileUploadLimit
	}
	if req.EmailLimit != nil {
		emailLimit = *req.EmailLimit
	}

	err = quota.UpdateLimits(userLimit, storageLimit, apiLimit, productLimit, orderLimit, fileUploadLimit, emailLimit)
	if err != nil {
		return nil, errors.Business(err.Error())
	}

	err = uc.quotaRepo.Update(ctx, quota)
	if err != nil {
		return nil, errors.TenantQuotaUpdateFailed(err)
	}

	return uc.assembler.QuotaEntityToDTO(quota), nil
}

// // SearchTenants 搜索租户（使用List方法实现）
// func (uc *TenantUseCase) SearchTenants(ctx context.Context, req *dto.SearchTenantsRequestDTO) (*dto.TenantListDTO, error) {
// 	// 构建搜索过滤条件
// 	tenantFilter := repository.TenantFilter{
// 		Keywords: req.Query,
// 	}

// 	tenantPagination := uc.buildTenantPagination(&req.PaginationRequestDTO)

// 	// 执行搜索
// 	tenants, result, err := uc.tenantRepo.List(ctx, tenantFilter, tenantPagination)
// 	if err != nil {
// 		return nil, errors.TenantSearchFailed(err)
// 	}

// 	return uc.assembler.BuildTenantListDTO(tenants, result.Total, result.Page, result.PageSize), nil
// }

// ValidateDomain 验证域名是否可用
func (uc *TenantUseCase) ValidateDomain(ctx context.Context, domain string) (bool, error) {
	tenant, err := uc.tenantRepo.GetByDomain(ctx, domain)
	if err != nil && !isNotFoundError(err) {
		return false, errors.TenantDomainValidationFailed(err)
	}

	// 如果没有找到租户，说明域名可用
	return tenant == nil, nil
}

// GetTenantHealth 获取租户健康状态
func (uc *TenantUseCase) GetTenantHealth(ctx context.Context, tenantID string) (*dto.TenantHealthDTO, error) {
	tenant, err := uc.tenantRepo.GetByID(ctx, tenantID)
	if err != nil {
		if isNotFoundError(err) {
			return nil, errors.TenantNotFound(tenantID)
		}
		return nil, errors.Database(err)
	}

	// 检查租户健康状态
	healthStatus, err := uc.tenantService.CheckTenantHealth(ctx, tenantID)
	if err != nil {
		return nil, errors.TenantHealthCheckFailed(err)
	}

	healthDTO := &dto.TenantHealthDTO{
		TenantID:    tenant.BusinessID,
		TenantName:  tenant.Name,
		Domain:      tenant.Domain,
		IsHealthy:   healthStatus.IsHealthy,
		HealthScore: calculateHealthScore(healthStatus),
		CheckedAt:   healthStatus.CheckedAt,
	}

	// 转换告警信息
	healthAlerts := make([]dto.HealthAlertDTO, len(healthStatus.Alerts))
	for i, alert := range healthStatus.Alerts {
		healthAlerts[i] = dto.HealthAlertDTO{
			Type:      alert.Type,
			Level:     alert.Level,
			Message:   alert.Message,
			Severity:  alert.Severity,
			CreatedAt: utils.Now(),
		}
	}
	healthDTO.Alerts = healthAlerts

	if healthStatus.IsHealthy {
		now := utils.Now()
		healthDTO.LastHealthyAt = &now
	}

	return healthDTO, nil
}

// BatchUpdateStatus 批量更新租户状态
func (uc *TenantUseCase) BatchUpdateStatus(ctx context.Context, req *dto.BatchUpdateStatusRequestDTO) error {
	for _, tenantID := range req.TenantIDs {
		switch req.Status {
		case "active":
			err := uc.ActivateTenant(ctx, tenantID)
			if err != nil {
				return errors.TenantBatchOperationFailed("activate", tenantID, err)
			}
		case "suspended":
			err := uc.SuspendTenant(ctx, tenantID, req.Reason)
			if err != nil {
				return errors.TenantBatchOperationFailed("suspend", tenantID, err)
			}
		case "terminated":
			err := uc.TerminateTenant(ctx, tenantID, req.Reason)
			if err != nil {
				return errors.TenantBatchOperationFailed("terminate", tenantID, err)
			}
		default:
			return errors.Validation("无效的状态: " + req.Status)
		}
	}

	return nil
}

// DeleteTenant 删除租户（软删除）
func (uc *TenantUseCase) DeleteTenant(ctx context.Context, tenantID string) error {
	tenant, err := uc.tenantRepo.GetByID(ctx, tenantID)
	if err != nil {
		if isNotFoundError(err) {
			return errors.TenantNotFound(tenantID)
		}
		return errors.Database(err)
	}

	// 先终止租户
	err = tenant.Terminate("租户删除")
	if err != nil {
		return errors.Business(err.Error())
	}

	// 软删除租户
	err = uc.tenantRepo.Delete(ctx, tenant.BusinessID)
	if err != nil {
		return errors.TenantDeleteFailed(err)
	}

	return nil
}

// isNotFoundError 检查是否是未找到错误
func isNotFoundError(err error) bool {
	if err == nil {
		return false
	}

	// 检查错误消息是否包含"not found"
	errMsg := err.Error()
	return errMsg == "record not found" || errMsg == "not found"
}

// buildTenantFilter 构建租户过滤条件
func (uc *TenantUseCase) buildTenantFilter(req *dto.TenantFilterRequestDTO) repository.TenantFilter {
	if req == nil {
		return repository.TenantFilter{}
	}

	filter := repository.TenantFilter{
		Name:     req.Name,
		Domain:   req.Domain,
		Industry: req.Industry,
		Country:  req.Country,
		Province: req.Province,
		City:     req.City,
		Keywords: req.Keywords,
	}

	if req.Type != "" {
		filter.Type = valueobject.TenantType(req.Type)
	}

	if req.Status != "" {
		filter.Status = parseStatusFromString(req.Status)
	}

	if req.CreatedFrom != nil {
		filter.CreatedFrom = req.CreatedFrom
	}

	if req.CreatedTo != nil {
		filter.CreatedTo = req.CreatedTo
	}

	if req.UpdatedFrom != nil {
		filter.UpdatedFrom = req.UpdatedFrom
	}

	if req.UpdatedTo != nil {
		filter.UpdatedTo = req.UpdatedTo
	}

	return filter
}

// parseStatusFromString 从字符串解析租户状态
func parseStatusFromString(status string) valueobject.TenantStatus {
	switch status {
	case "inactive":
		return valueobject.TenantStatusInactive
	case "active":
		return valueobject.TenantStatusActive
	case "suspended":
		return valueobject.TenantStatusSuspended
	case "expired":
		return valueobject.TenantStatusExpired
	case "terminated":
		return valueobject.TenantStatusTerminated
	default:
		return valueobject.TenantStatusInactive // 默认为未激活状态
	}
}

// buildTenantPagination 构建租户分页参数
func (uc *TenantUseCase) buildTenantPagination(req *dto.PaginationRequestDTO) repository.TenantPagination {
	if req == nil {
		return repository.TenantPagination{
			Page:     1,
			PageSize: 10,
			SortBy:   "created_at",
			SortDesc: true,
		}
	}

	page := req.Page
	if page <= 0 {
		page = 1
	}

	pageSize := req.PageSize
	if pageSize <= 0 {
		pageSize = 10
	} else if pageSize > 100 {
		pageSize = 100
	}

	sortBy := req.SortBy
	if sortBy == "" {
		sortBy = "created_at"
	}

	return repository.TenantPagination{
		Page:     page,
		PageSize: pageSize,
		SortBy:   sortBy,
		SortDesc: req.SortDesc,
	}
}

// calculateHealthScore 计算健康分数
func calculateHealthScore(status *service.TenantHealthStatus) int {
	if status.IsHealthy {
		return 100
	}

	score := 100
	for _, alert := range status.Alerts {
		switch alert.Level {
		case "critical":
			score -= 30
		case "warning":
			score -= 10
		case "info":
			score -= 5
		}
	}

	if score < 0 {
		score = 0
	}

	return score
}

// tenantRepositoryAdapter 租户仓储适配器，实现分页接口
type tenantRepositoryAdapter struct {
	repo   repository.TenantRepository
	filter repository.TenantFilter
}

// Count 实现分页接口的Count方法
func (a *tenantRepositoryAdapter) Count(ctx context.Context, filter pagination.Filter) (int64, error) {
	// 使用适配器的过滤条件
	return a.repo.Count(ctx, a.filter)
}

// FindWithPagination 实现分页接口的FindWithPagination方法
func (a *tenantRepositoryAdapter) FindWithPagination(ctx context.Context, req *pagination.Request, filter pagination.Filter) ([]*entity.Tenant, error) {
	// 转换分页请求
	paginationReq := &repository.PaginationRequest{
		Page:     req.Page,
		PageSize: req.PageSize,
	}

	// 处理排序
	if len(req.Sort) > 0 {
		paginationReq.SortBy = req.Sort[0].Field
		paginationReq.SortDesc = req.Sort[0].Direction == pagination.SortDesc
	}

	// 调用仓储方法
	return a.repo.FindWithPagination(ctx, paginationReq, a.filter)
}

// FindWithCursor 实现分页接口的FindWithCursor方法
func (a *tenantRepositoryAdapter) FindWithCursor(ctx context.Context, req *pagination.CursorRequest, filter pagination.Filter) ([]*entity.Tenant, error) {
	// 暂时不实现游标分页，返回空结果
	return []*entity.Tenant{}, nil
}

// validatePaginationRequest 验证分页请求参数
func (uc *TenantUseCase) validatePaginationRequest(req *paginationDTO.PaginationRequestDTO) error {
	if req.Page < 1 {
		return errors.Validation("页码必须大于0")
	}

	if req.PageSize < 1 || req.PageSize > 100 {
		return errors.Validation("每页数量必须在1-100之间")
	}

	// 验证排序字段
	if req.SortBy != "" {
		allowedSortFields := []string{"created_at", "updated_at", "name", "domain", "status"}
		valid := false
		for _, field := range allowedSortFields {
			if req.SortBy == field {
				valid = true
				break
			}
		}
		if !valid {
			return errors.Validation("无效的排序字段: " + req.SortBy)
		}
	}

	return nil
}

// buildTenantFilterFromSearch 从搜索关键词构建租户过滤条件
func (uc *TenantUseCase) buildTenantFilterFromSearch(search string) repository.TenantFilter {
	filter := repository.TenantFilter{}

	if search != "" {
		// 使用关键词字段进行搜索
		filter.Keywords = search
	}

	return filter
}

// buildAdvancedTenantFilter 构建高级租户过滤条件
func (uc *TenantUseCase) buildAdvancedTenantFilter(search string, filters map[string]interface{}) repository.TenantFilter {
	filter := uc.buildTenantFilterFromSearch(search)

	// 处理状态过滤
	if status, ok := filters["status"].(string); ok && status != "" {
		switch status {
		case "inactive":
			filter.Status = valueobject.TenantStatusInactive
		case "active":
			filter.Status = valueobject.TenantStatusActive
		case "suspended":
			filter.Status = valueobject.TenantStatusSuspended
		case "expired":
			filter.Status = valueobject.TenantStatusExpired
		case "terminated":
			filter.Status = valueobject.TenantStatusTerminated
		}
	}

	// 处理租户类型过滤
	if tenantType, ok := filters["type"].(string); ok && tenantType != "" {
		switch tenantType {
		case "system":
			filter.Type = valueobject.TenantTypeSystem
		case "enterprise":
			filter.Type = valueobject.TenantTypeEnterprise
		case "professional":
			filter.Type = valueobject.TenantTypeProfessional
		case "basic":
			filter.Type = valueobject.TenantTypeBasic
		case "trial":
			filter.Type = valueobject.TenantTypeTrial
		}
	}

	// 处理行业过滤
	if industry, ok := filters["industry"].(string); ok && industry != "" {
		filter.Industry = industry
	}

	// 处理地区过滤
	if country, ok := filters["country"].(string); ok && country != "" {
		filter.Country = country
	}

	if province, ok := filters["province"].(string); ok && province != "" {
		filter.Province = province
	}

	if city, ok := filters["city"].(string); ok && city != "" {
		filter.City = city
	}

	// 处理创建时间范围过滤
	if createdFrom, ok := filters["created_from"].(string); ok && createdFrom != "" {
		if t, err := time.Parse(time.RFC3339, createdFrom); err == nil {
			filter.CreatedFrom = &t
		}
	}

	if createdTo, ok := filters["created_to"].(string); ok && createdTo != "" {
		if t, err := time.Parse(time.RFC3339, createdTo); err == nil {
			filter.CreatedTo = &t
		}
	}

	return filter
}
