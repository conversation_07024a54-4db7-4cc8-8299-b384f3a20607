package tenant

import (
	"context"
	"fmt"

	"backend/internal/application/assembler"
	"backend/internal/application/dto"
	"backend/internal/domain/tenant/entity"
	"backend/internal/domain/tenant/repository"
	"backend/internal/domain/tenant/service"
	"backend/internal/shared/errors"
	"backend/pkg/common/utils"
)

// SubscriptionUseCase 订阅用例
type SubscriptionUseCase struct {
	subscriptionRepo repository.TenantSubscriptionRepository
	tenantRepo       repository.TenantRepository
	tenantService    service.TenantService
	assembler        *assembler.TenantAssembler
}

// NewSubscriptionUseCase 创建订阅用例
func NewSubscriptionUseCase(
	subscriptionRepo repository.TenantSubscriptionRepository,
	tenantRepo repository.TenantRepository,
	tenantService service.TenantService,
	assembler *assembler.TenantAssembler,
) *SubscriptionUseCase {
	return &SubscriptionUseCase{
		subscriptionRepo: subscriptionRepo,
		tenantRepo:       tenantRepo,
		tenantService:    tenantService,
		assembler:        assembler,
	}
}

// CreateSubscription 创建订阅
func (uc *SubscriptionUseCase) CreateSubscription(ctx context.Context, req *dto.CreateSubscriptionRequestDTO) (*dto.TenantSubscriptionDTO, error) {
	// 检查租户是否存在
	_, err := uc.tenantRepo.GetByID(ctx, req.TenantID)
	if err != nil {
		if isNotFoundError(err) {
			return nil, errors.TenantNotFound(req.TenantID)
		}
		return nil, fmt.Errorf("获取租户失败: %w", err)
	}

	// 检查是否已有有效订阅
	existingSubscription, err := uc.subscriptionRepo.GetByTenantID(ctx, req.TenantID)
	if err != nil && !isNotFoundError(err) {
		return nil, fmt.Errorf("检查现有订阅失败: %w", err)
	}
	if existingSubscription != nil && existingSubscription.IsActive() {
		return nil, errors.Conflict("subscription", "tenant_id", req.TenantID)
	}

	// 转换DTO为服务请求
	serviceReq := service.CreateSubscriptionRequest{
		TenantID:     req.TenantID,
		PlanID:       req.PlanID,
		PlanName:     req.PlanName,
		StartDate:    req.StartDate,
		EndDate:      req.EndDate,
		Price:        req.Price,
		Currency:     req.Currency,
		BillingCycle: req.BillingCycle,
		UserLimit:    req.UserLimit,
		StorageLimit: req.StorageLimit,
		APILimit:     req.APILimit,
		ProductLimit: req.ProductLimit,
		OrderLimit:   req.OrderLimit,
	}

	// 使用领域服务创建订阅
	subscription, err := uc.tenantService.CreateSubscription(ctx, serviceReq)
	if err != nil {
		return nil, fmt.Errorf("创建订阅失败: %w", err)
	}

	return uc.assembler.SubscriptionEntityToDTO(subscription), nil
}

// GetSubscription 获取订阅详情
func (uc *SubscriptionUseCase) GetSubscription(ctx context.Context, subscriptionID string) (*dto.TenantSubscriptionDTO, error) {
	subscription, err := uc.subscriptionRepo.GetByID(ctx, subscriptionID)
	if err != nil {
		if isNotFoundError(err) {
			return nil, errors.NotFound("subscription", subscriptionID)
		}
		return nil, fmt.Errorf("获取订阅失败: %w", err)
	}

	return uc.assembler.SubscriptionEntityToDTO(subscription), nil
}

// GetTenantSubscription 获取租户的订阅
func (uc *SubscriptionUseCase) GetTenantSubscription(ctx context.Context, tenantID string) (*dto.TenantSubscriptionDTO, error) {
	subscription, err := uc.subscriptionRepo.GetByTenantID(ctx, tenantID)
	if err != nil {
		if isNotFoundError(err) {
			return nil, errors.NotFound("subscription", tenantID)
		}
		return nil, fmt.Errorf("获取租户订阅失败: %w", err)
	}

	return uc.assembler.SubscriptionEntityToDTO(subscription), nil
}

// RenewSubscription 续费订阅
func (uc *SubscriptionUseCase) RenewSubscription(ctx context.Context, subscriptionID string, req *dto.RenewSubscriptionRequestDTO) (*dto.TenantSubscriptionDTO, error) {
	subscription, err := uc.subscriptionRepo.GetByID(ctx, subscriptionID)
	if err != nil {
		if isNotFoundError(err) {
			return nil, errors.NotFound("subscription", subscriptionID)
		}
		return nil, fmt.Errorf("获取订阅失败: %w", err)
	}

	// 检查是否可以续费
	if !subscription.CanRenew() {
		return nil, errors.Business("订阅当前状态不允许续费")
	}

	// 转换DTO为服务请求
	serviceReq := service.RenewSubscriptionRequest{
		StartDate:    req.StartDate,
		EndDate:      req.EndDate,
		Price:        req.Price,
		Currency:     req.Currency,
		BillingCycle: req.BillingCycle,
	}

	// 使用领域服务续费订阅
	err = uc.tenantService.RenewSubscription(ctx, subscriptionID, serviceReq)
	if err != nil {
		return nil, fmt.Errorf("续费订阅失败: %w", err)
	}

	// 重新获取更新后的订阅
	updatedSubscription, err := uc.subscriptionRepo.GetByID(ctx, subscriptionID)
	if err != nil {
		return nil, fmt.Errorf("获取更新后的订阅失败: %w", err)
	}

	return uc.assembler.SubscriptionEntityToDTO(updatedSubscription), nil
}

// CancelSubscription 取消订阅
func (uc *SubscriptionUseCase) CancelSubscription(ctx context.Context, subscriptionID string, req *dto.CancelSubscriptionRequestDTO) error {
	subscription, err := uc.subscriptionRepo.GetByID(ctx, subscriptionID)
	if err != nil {
		if isNotFoundError(err) {
			return errors.NotFound("subscription", subscriptionID)
		}
		return fmt.Errorf("获取订阅失败: %w", err)
	}

	// 检查是否可以取消
	if subscription.Status == entity.SubscriptionStatusCanceled {
		return errors.Business("订阅已经被取消")
	}

	// 使用领域服务取消订阅
	err = uc.tenantService.CancelSubscription(ctx, subscriptionID, req.Reason, "")
	if err != nil {
		return fmt.Errorf("取消订阅失败: %w", err)
	}

	return nil
}

// ExtendTrialPeriod 延长试用期
func (uc *SubscriptionUseCase) ExtendTrialPeriod(ctx context.Context, subscriptionID string, req *dto.ExtendTrialRequestDTO) (*dto.TenantSubscriptionDTO, error) {
	subscription, err := uc.subscriptionRepo.GetByID(ctx, subscriptionID)
	if err != nil {
		if isNotFoundError(err) {
			return nil, errors.NotFound("subscription", subscriptionID)
		}
		return nil, fmt.Errorf("获取订阅失败: %w", err)
	}

	// 检查是否是试用期订阅
	if !subscription.IsTrialPeriod {
		return nil, errors.Business("非试用期订阅无法延长试用期")
	}

	// 使用领域服务延长试用期
	err = uc.tenantService.ExtendTrialPeriod(ctx, subscriptionID, req.Days)
	if err != nil {
		return nil, fmt.Errorf("延长试用期失败: %w", err)
	}

	// 重新获取更新后的订阅
	updatedSubscription, err := uc.subscriptionRepo.GetByID(ctx, subscriptionID)
	if err != nil {
		return nil, fmt.Errorf("获取更新后的订阅失败: %w", err)
	}

	return uc.assembler.SubscriptionEntityToDTO(updatedSubscription), nil
}

// ListSubscriptions 获取订阅列表
func (uc *SubscriptionUseCase) ListSubscriptions(ctx context.Context, filter *dto.SubscriptionFilterRequestDTO, pagination *dto.PaginationRequestDTO) (*dto.SubscriptionListDTO, error) {
	// 转换过滤条件
	subscriptionFilter := uc.buildSubscriptionFilter(filter)
	tenantPagination := uc.buildTenantPagination(pagination)

	// 获取订阅列表
	subscriptions, result, err := uc.subscriptionRepo.List(ctx, subscriptionFilter, tenantPagination)
	if err != nil {
		return nil, fmt.Errorf("获取订阅列表失败: %w", err)
	}

	return uc.assembler.BuildSubscriptionListDTO(subscriptions, result.Total, result.Page, result.PageSize), nil
}

// GetExpiringSubscriptions 获取即将到期的订阅
func (uc *SubscriptionUseCase) GetExpiringSubscriptions(ctx context.Context, days int) ([]*dto.TenantSubscriptionDTO, error) {
	subscriptions, err := uc.subscriptionRepo.GetExpiringSubscriptions(ctx, days)
	if err != nil {
		return nil, fmt.Errorf("获取即将到期的订阅失败: %w", err)
	}

	return uc.assembler.SubscriptionEntitiesToDTOs(subscriptions), nil
}

// GetExpiredSubscriptions 获取已过期的订阅
func (uc *SubscriptionUseCase) GetExpiredSubscriptions(ctx context.Context) ([]*dto.TenantSubscriptionDTO, error) {
	subscriptions, err := uc.subscriptionRepo.GetExpiredSubscriptions(ctx)
	if err != nil {
		return nil, fmt.Errorf("获取已过期的订阅失败: %w", err)
	}

	return uc.assembler.SubscriptionEntitiesToDTOs(subscriptions), nil
}

// GetTrialSubscriptions 获取试用期订阅
func (uc *SubscriptionUseCase) GetTrialSubscriptions(ctx context.Context) ([]*dto.TenantSubscriptionDTO, error) {
	subscriptions, err := uc.subscriptionRepo.GetTrialSubscriptions(ctx)
	if err != nil {
		return nil, fmt.Errorf("获取试用期订阅失败: %w", err)
	}

	return uc.assembler.SubscriptionEntitiesToDTOs(subscriptions), nil
}

// GetSubscriptionStatistics 获取订阅统计信息
func (uc *SubscriptionUseCase) GetSubscriptionStatistics(ctx context.Context) (*dto.SubscriptionStatisticsDTO, error) {
	stats, err := uc.subscriptionRepo.GetSubscriptionStatistics(ctx)
	if err != nil {
		return nil, fmt.Errorf("获取订阅统计失败: %w", err)
	}

	// 转换为DTO
	return &dto.SubscriptionStatisticsDTO{
		TotalSubscriptions:       stats.TotalSubscriptions,
		ActiveSubscriptions:      stats.ActiveSubscriptions,
		TrialSubscriptions:       stats.TrialSubscriptions,
		ExpiredSubscriptions:     stats.ExpiredSubscriptions,
		StatusDistribution:       convertStatusDistribution(stats.StatusCounts),
		PlanDistribution:         stats.PlanCounts,
		CurrencyDistribution:     stats.CurrencyCounts,
		BillingCycleDistribution: stats.BillingCycleCounts,
		ChurnRate:                stats.ChurnRate,
		RenewalRate:              stats.RenewalRate,
		AverageRevenue:           stats.AverageRevenue,
		MRR:                      stats.MRR,
		ARR:                      stats.ARR,
		GeneratedAt:              utils.Now(),
	}, nil
}

// UpdateSubscriptionLimits 更新订阅限制
func (uc *SubscriptionUseCase) UpdateSubscriptionLimits(ctx context.Context, subscriptionID string, req *dto.UpdateQuotaLimitsRequestDTO) (*dto.TenantSubscriptionDTO, error) {
	subscription, err := uc.subscriptionRepo.GetByID(ctx, subscriptionID)
	if err != nil {
		if isNotFoundError(err) {
			return nil, errors.NotFound("subscription", subscriptionID)
		}
		return nil, fmt.Errorf("获取订阅失败: %w", err)
	}

	// 更新限制
	err = subscription.UpdateQuotaLimits(
		*req.UserLimit,
		*req.StorageLimit,
		*req.APILimit,
		*req.ProductLimit,
		*req.OrderLimit,
	)
	if err != nil {
		return nil, errors.Business(err.Error())
	}

	// 保存更新
	err = uc.subscriptionRepo.Update(ctx, subscription)
	if err != nil {
		return nil, fmt.Errorf("更新订阅限制失败: %w", err)
	}

	return uc.assembler.SubscriptionEntityToDTO(subscription), nil
}

// buildSubscriptionFilter 构建订阅过滤条件
func (uc *SubscriptionUseCase) buildSubscriptionFilter(req *dto.SubscriptionFilterRequestDTO) repository.SubscriptionFilter {
	if req == nil {
		return repository.SubscriptionFilter{}
	}

	filter := repository.SubscriptionFilter{
		TenantID:       req.TenantID,
		PlanID:         req.PlanID,
		PlanName:       req.PlanName,
		Currency:       req.Currency,
		BillingCycle:   req.BillingCycle,
		ExpiringInDays: req.ExpiringInDays,
		CreatedFrom:    req.CreatedFrom,
		CreatedTo:      req.CreatedTo,
	}

	if req.Status != "" {
		filter.Status = parseSubscriptionStatusFromString(req.Status)
	}

	if req.IsTrialPeriod != nil {
		filter.IsTrialPeriod = req.IsTrialPeriod
	}

	if req.AutoRenewal != nil {
		filter.AutoRenewal = req.AutoRenewal
	}

	return filter
}

// buildTenantPagination 构建分页参数
func (uc *SubscriptionUseCase) buildTenantPagination(req *dto.PaginationRequestDTO) repository.TenantPagination {
	if req == nil {
		return repository.TenantPagination{
			Page:     1,
			PageSize: 10,
			SortBy:   "created_at",
			SortDesc: true,
		}
	}

	page := req.Page
	if page <= 0 {
		page = 1
	}

	pageSize := req.PageSize
	if pageSize <= 0 {
		pageSize = 10
	} else if pageSize > 100 {
		pageSize = 100
	}

	sortBy := req.SortBy
	if sortBy == "" {
		sortBy = "created_at"
	}

	return repository.TenantPagination{
		Page:     page,
		PageSize: pageSize,
		SortBy:   sortBy,
		SortDesc: req.SortDesc,
	}
}

// parseSubscriptionStatusFromString 从字符串解析订阅状态
func parseSubscriptionStatusFromString(status string) entity.SubscriptionStatus {
	switch status {
	case "pending":
		return entity.SubscriptionStatusPending
	case "active":
		return entity.SubscriptionStatusActive
	case "suspended":
		return entity.SubscriptionStatusSuspended
	case "canceled":
		return entity.SubscriptionStatusCanceled
	case "expired":
		return entity.SubscriptionStatusExpired
	default:
		return entity.SubscriptionStatusPending // 默认为待激活状态
	}
}

// convertStatusDistribution 转换状态分布
func convertStatusDistribution(statusCounts map[entity.SubscriptionStatus]int64) map[string]int64 {
	result := make(map[string]int64)
	for status, count := range statusCounts {
		result[status.String()] = count
	}
	return result
}
