# ReadModel Layer (读模型层)

## 目录结构
```
readmodel/
├── repository/   # 读模型仓储接口
├── projection/   # 投影构建器
└── synchronizer/ # 读写模型同步器
```

## 职责说明

### repository/ - 读模型仓储
- 定义读模型的仓储接口
- 优化查询性能的数据访问
- 可以使用不同的存储技术
- 支持复杂的聚合查询

### projection/ - 投影构建器
- 负责构建查询投影
- 从领域事件构建读模型
- 处理数据转换和聚合
- 维护读模型的一致性

### synchronizer/ - 同步器
- 保持读写模型同步
- 处理事件到投影的转换
- 管理同步策略（实时/批量）
- 处理同步失败和重试

## 同步策略

### 实时同步
- 关键业务数据
- 通过事件立即更新
- 保证数据及时性

### 批量同步
- 统计和报表数据
- 定时批量更新
- 优化性能和资源

### 按需同步
- 低频访问数据
- 查询时动态聚合
- 减少存储成本

## 数据源聚合
- 来自多个聚合根的数据
- 跨领域的数据整合
- 复杂查询的性能优化 