package dto

import (
	"time"
)

// === Token相关DTO ===

// TokenDTO Token数据传输对象
type TokenDTO struct {
	ID        string    `json:"id"`
	TokenID   string    `json:"token_id"`
	TokenType string    `json:"token_type"`
	Status    string    `json:"status"`
	UserID    string    `json:"user_id"`
	TenantID  string    `json:"tenant_id"`
	SessionID string    `json:"session_id"`
	DeviceID  string    `json:"device_id"`
	IssuedAt  time.Time `json:"issued_at"`
	ExpiresAt time.Time `json:"expires_at"`
	ClientIP  string    `json:"client_ip,omitempty"`
	UserAgent string    `json:"user_agent,omitempty"`
	Scope     string    `json:"scope,omitempty"`
}

// CreateTokenRequest 创建Token请求
type CreateTokenRequest struct {
	UserID    string            `json:"user_id" validate:"required"`
	TenantID  string            `json:"tenant_id" validate:"required"`
	SessionID string            `json:"session_id,omitempty"`
	DeviceID  string            `json:"device_id,omitempty"`
	TokenType string            `json:"token_type" validate:"required,oneof=access refresh pre_auth"`
	ExpiresIn int64             `json:"expires_in,omitempty"` // 秒数
	Scope     string            `json:"scope,omitempty"`
	Metadata  map[string]string `json:"metadata,omitempty"`
}

// RefreshTokenRequest 刷新Token请求
type RefreshTokenRequest struct {
	RefreshToken string `json:"refresh_token" validate:"required"`
}

// RevokeTokenRequest 撤销Token请求
type RevokeTokenRequest struct {
	TokenID string `json:"token_id" validate:"required"`
	Reason  string `json:"reason,omitempty"`
}

// TokenPairDTO Token对DTO
type TokenPairDTO struct {
	AccessToken  *TokenDTO `json:"access_token"`
	RefreshToken *TokenDTO `json:"refresh_token"`
	ExpiresIn    int64     `json:"expires_in"`
	TokenType    string    `json:"token_type"`
}

// === 会话相关DTO ===

// SessionDTO 会话数据传输对象
type SessionDTO struct {
	ID           string    `json:"id"`
	SessionID    string    `json:"session_id"`
	UserID       string    `json:"user_id"`
	TenantID     string    `json:"tenant_id"`
	DeviceID     string    `json:"device_id"`
	Status       string    `json:"status"`
	CreatedAt    time.Time `json:"created_at"`
	ExpiresAt    time.Time `json:"expires_at"`
	LastActiveAt time.Time `json:"last_active_at"`
	ClientIP     string    `json:"client_ip,omitempty"`
	UserAgent    string    `json:"user_agent,omitempty"`
	DeviceType   string    `json:"device_type,omitempty"`
	DeviceName   string    `json:"device_name,omitempty"`
	LoginMethod  string    `json:"login_method,omitempty"`
	IsSecure     bool      `json:"is_secure"`
}

// CreateSessionRequest 创建会话请求
type CreateSessionRequest struct {
	UserID      string            `json:"user_id" validate:"required"`
	TenantID    string            `json:"tenant_id" validate:"required"`
	DeviceID    string            `json:"device_id" validate:"required"`
	DeviceType  string            `json:"device_type,omitempty"`
	DeviceName  string            `json:"device_name,omitempty"`
	LoginMethod string            `json:"login_method,omitempty"`
	ClientIP    string            `json:"client_ip,omitempty"`
	UserAgent   string            `json:"user_agent,omitempty"`
	IsSecure    bool              `json:"is_secure"`
	ExpiresIn   int64             `json:"expires_in,omitempty"` // 秒数
	Metadata    map[string]string `json:"metadata,omitempty"`
}

// UpdateSessionRequest 更新会话请求
type UpdateSessionRequest struct {
	SessionID string `json:"session_id" validate:"required"`
	Status    string `json:"status,omitempty"`
}

// TerminateSessionRequest 终止会话请求
type TerminateSessionRequest struct {
	SessionID string `json:"session_id" validate:"required"`
	Reason    string `json:"reason,omitempty"`
}

// === 前置认证相关DTO ===

// PreAuthContextDTO 前置认证上下文DTO
type PreAuthContextDTO struct {
	ID               string    `json:"id"`
	ContextID        string    `json:"context_id"`
	UserID           string    `json:"user_id"`
	TenantID         string    `json:"tenant_id"`
	CurrentStep      string    `json:"current_step"`
	RequiredSteps    []string  `json:"required_steps"`
	CompletedSteps   []string  `json:"completed_steps"`
	CreatedAt        time.Time `json:"created_at"`
	ExpiresAt        time.Time `json:"expires_at"`
	AttemptCount     int       `json:"attempt_count"`
	MaxAttempts      int       `json:"max_attempts"`
	VerificationData string    `json:"verification_data,omitempty"`
	ClientIP         string    `json:"client_ip,omitempty"`
	UserAgent        string    `json:"user_agent,omitempty"`
	DeviceID         string    `json:"device_id,omitempty"`
}

// CreatePreAuthRequest 创建前置认证请求
type CreatePreAuthRequest struct {
	UserID        string   `json:"user_id" validate:"required"`
	TenantID      string   `json:"tenant_id" validate:"required"`
	RequiredSteps []string `json:"required_steps" validate:"required,min=1"`
	DeviceID      string   `json:"device_id,omitempty"`
	ClientIP      string   `json:"client_ip,omitempty"`
	UserAgent     string   `json:"user_agent,omitempty"`
	ExpiresIn     int64    `json:"expires_in,omitempty"` // 秒数，默认15分钟
}

// CompleteStepRequest 完成认证步骤请求
type CompleteStepRequest struct {
	ContextID        string      `json:"context_id" validate:"required"`
	Step             string      `json:"step" validate:"required"`
	VerificationData interface{} `json:"verification_data,omitempty"`
}

// PreAuthTokenDTO 前置认证Token DTO
type PreAuthTokenDTO struct {
	ContextID     string   `json:"context_id"`
	Token         TokenDTO `json:"token"`
	RequiredSteps []string `json:"required_steps"`
	ExpiresIn     int64    `json:"expires_in"`
}

// === 黑名单相关DTO ===

// BlacklistEntryDTO 黑名单条目DTO
type BlacklistEntryDTO struct {
	ID            string    `json:"id"`
	TokenID       string    `json:"token_id"`
	TokenType     string    `json:"token_type"`
	UserID        string    `json:"user_id"`
	TenantID      string    `json:"tenant_id"`
	SessionID     string    `json:"session_id"`
	BlacklistedAt time.Time `json:"blacklisted_at"`
	ExpiresAt     time.Time `json:"expires_at"`
	Reason        string    `json:"reason"`
}

// BlacklistTokenRequest 拉黑Token请求
type BlacklistTokenRequest struct {
	TokenID string `json:"token_id" validate:"required"`
	Reason  string `json:"reason" validate:"required"`
}

// === 查询相关DTO ===

// TokenQueryRequest Token查询请求
type TokenQueryRequest struct {
	UserID    string `json:"user_id,omitempty"`
	TenantID  string `json:"tenant_id,omitempty"`
	SessionID string `json:"session_id,omitempty"`
	DeviceID  string `json:"device_id,omitempty"`
	TokenType string `json:"token_type,omitempty"`
	Status    string `json:"status,omitempty"`
	StartTime string `json:"start_time,omitempty"` // RFC3339格式
	EndTime   string `json:"end_time,omitempty"`   // RFC3339格式
	Page      int    `json:"page,omitempty"`
	PageSize  int    `json:"page_size,omitempty"`
}

// SessionQueryRequest 会话查询请求
type SessionQueryRequest struct {
	UserID     string `json:"user_id,omitempty"`
	TenantID   string `json:"tenant_id,omitempty"`
	DeviceID   string `json:"device_id,omitempty"`
	Status     string `json:"status,omitempty"`
	DeviceType string `json:"device_type,omitempty"`
	StartTime  string `json:"start_time,omitempty"` // RFC3339格式
	EndTime    string `json:"end_time,omitempty"`   // RFC3339格式
	Page       int    `json:"page,omitempty"`
	PageSize   int    `json:"page_size,omitempty"`
}

// === 统计相关DTO ===

// TokenStatisticsDTO Token统计DTO
type TokenStatisticsDTO struct {
	TotalTokens    int64            `json:"total_tokens"`
	ActiveTokens   int64            `json:"active_tokens"`
	ExpiredTokens  int64            `json:"expired_tokens"`
	RevokedTokens  int64            `json:"revoked_tokens"`
	TokensByType   map[string]int64 `json:"tokens_by_type"`
	TokensByStatus map[string]int64 `json:"tokens_by_status"`
}

// SessionStatisticsDTO 会话统计DTO
type SessionStatisticsDTO struct {
	TotalSessions    int64            `json:"total_sessions"`
	ActiveSessions   int64            `json:"active_sessions"`
	ExpiredSessions  int64            `json:"expired_sessions"`
	SessionsByStatus map[string]int64 `json:"sessions_by_status"`
	SessionsByDevice map[string]int64 `json:"sessions_by_device"`
}

// === 响应相关DTO ===

// TokenListResponse Token列表响应
type TokenListResponse struct {
	Tokens     []*TokenDTO `json:"tokens"`
	TotalCount int64       `json:"total_count"`
	Page       int         `json:"page"`
	PageSize   int         `json:"page_size"`
	TotalPages int         `json:"total_pages"`
}

// SessionListResponse 会话列表响应
type SessionListResponse struct {
	Sessions   []*SessionDTO `json:"sessions"`
	TotalCount int64         `json:"total_count"`
	Page       int           `json:"page"`
	PageSize   int           `json:"page_size"`
	TotalPages int           `json:"total_pages"`
}

// === 验证相关DTO ===

// ValidateTokenRequest 验证Token请求
type ValidateTokenRequest struct {
	TokenID    string `json:"token_id,omitempty"`
	TokenValue string `json:"token_value,omitempty"`
}

// ValidateTokenResponse 验证Token响应
type ValidateTokenResponse struct {
	Valid     bool        `json:"valid"`
	Token     *TokenDTO   `json:"token,omitempty"`
	Session   *SessionDTO `json:"session,omitempty"`
	ExpiresAt time.Time   `json:"expires_at,omitempty"`
	Reason    string      `json:"reason,omitempty"` // 无效原因
}

// === 操作结果DTO ===

// OperationResult 操作结果
type OperationResult struct {
	Success bool        `json:"success"`
	Message string      `json:"message"`
	Data    interface{} `json:"data,omitempty"`
}

// CleanupResult 清理操作结果
type CleanupResult struct {
	ExpiredTokens    int64 `json:"expired_tokens"`
	ExpiredSessions  int64 `json:"expired_sessions"`
	ExpiredBlacklist int64 `json:"expired_blacklist"`
	ExpiredPreAuth   int64 `json:"expired_pre_auth"`
	TotalCleaned     int64 `json:"total_cleaned"`
}
