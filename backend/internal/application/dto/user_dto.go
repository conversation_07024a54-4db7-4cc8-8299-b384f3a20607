package dto

import (
	"time"
)

// ==================== 用户基础DTO ====================

// UserDTO 用户数据传输对象
type UserDTO struct {
	ID          string                 `json:"id"`
	BusinessID  string                 `json:"business_id"`
	Username    string                 `json:"username"`
	Email       string                 `json:"email"`
	Phone       string                 `json:"phone"`
	Status      string                 `json:"status"`
	Profile     UserProfileDTO         `json:"profile"`
	Tenants     []UserTenantDTO        `json:"tenants,omitempty"`
	Permissions []string               `json:"permissions,omitempty"`
	Roles       []string               `json:"roles,omitempty"`
	Metadata    map[string]interface{} `json:"metadata,omitempty"`
	CreatedAt   time.Time              `json:"created_at"`
	UpdatedAt   time.Time              `json:"updated_at"`
	Version     int                    `json:"version"`
}

// UserProfileDTO 用户资料数据传输对象
type UserProfileDTO struct {
	Avatar    string `json:"avatar,omitempty"`
	Nickname  string `json:"nickname,omitempty"`
	FirstName string `json:"first_name,omitempty"`
	LastName  string `json:"last_name,omitempty"`
	Language  string `json:"language,omitempty"`
	Timezone  string `json:"timezone,omitempty"`
}

// UserTenantDTO 用户租户关联数据传输对象
type UserTenantDTO struct {
	TenantID   string    `json:"tenant_id"`
	TenantName string    `json:"tenant_name"`
	RoleID     string    `json:"role_id"`
	RoleName   string    `json:"role_name"`
	Status     string    `json:"status"`
	JoinedAt   time.Time `json:"joined_at"`
}

// UserContactDTO 用户联系方式数据传输对象
type UserContactDTO struct {
	Email         string `json:"email"`
	Phone         string `json:"phone"`
	Wechat        string `json:"wechat,omitempty"`
	EmailVerified bool   `json:"email_verified"`
	PhoneVerified bool   `json:"phone_verified"`
}

// ==================== 用户请求DTO ====================

// CreateUserRequestDTO 创建用户请求DTO
type CreateUserRequestDTO struct {
	TenantID  string         `json:"tenant_id" validate:"required"`
	Username  string         `json:"username" validate:"required,min=3,max=50"`
	Email     string         `json:"email" validate:"required,email"`
	Phone     string         `json:"phone" validate:"omitempty,min=10,max=20"`
	Password  string         `json:"password" validate:"required,min=8"`
	Profile   UserProfileDTO `json:"profile"`
	RoleID    string         `json:"role_id,omitempty"`
}

// UpdateUserRequestDTO 更新用户请求DTO
type UpdateUserRequestDTO struct {
	UserID      string                  `json:"user_id" validate:"required"`
	TenantID    string                  `json:"tenant_id" validate:"required"`
	Profile     *UpdateUserProfileDTO   `json:"profile,omitempty"`
	ContactInfo *UpdateUserContactDTO   `json:"contact_info,omitempty"`
	Preferences *UpdateUserPreferencesDTO `json:"preferences,omitempty"`
}

// UpdateUserProfileDTO 更新用户资料DTO
type UpdateUserProfileDTO struct {
	Nickname  string `json:"nickname" validate:"omitempty,max=100"`
	FirstName string `json:"first_name" validate:"omitempty,max=50"`
	LastName  string `json:"last_name" validate:"omitempty,max=50"`
	Avatar    string `json:"avatar" validate:"omitempty,url"`
}

// UpdateUserContactDTO 更新用户联系方式DTO
type UpdateUserContactDTO struct {
	Email string `json:"email" validate:"omitempty,email"`
	Phone string `json:"phone" validate:"omitempty,min=10,max=20"`
}

// UpdateUserPreferencesDTO 更新用户偏好设置DTO
type UpdateUserPreferencesDTO struct {
	Language string `json:"language" validate:"omitempty,len=5"`
	Timezone string `json:"timezone" validate:"omitempty"`
}

// UserStatusChangeRequestDTO 用户状态变更请求DTO
type UserStatusChangeRequestDTO struct {
	UserID   string `json:"user_id" validate:"required"`
	TenantID string `json:"tenant_id" validate:"required"`
	Reason   string `json:"reason,omitempty"`
}

// AssignUserToTenantRequestDTO 分配用户到租户请求DTO
type AssignUserToTenantRequestDTO struct {
	UserID   string `json:"user_id" validate:"required"`
	TenantID string `json:"tenant_id" validate:"required"`
	RoleID   string `json:"role_id" validate:"required"`
}

// ==================== 用户查询DTO ====================

// UserListRequestDTO 用户列表请求DTO
type UserListRequestDTO struct {
	TenantID string `json:"tenant_id" validate:"required"`
	Keyword  string `json:"keyword,omitempty"`
	Status   string `json:"status,omitempty"`
	RoleID   string `json:"role_id,omitempty"`
	Page     int    `json:"page" validate:"min=1"`
	PageSize int    `json:"page_size" validate:"min=1,max=100"`
	SortBy   string `json:"sort_by,omitempty"`
	SortDesc bool   `json:"sort_desc,omitempty"`
}

// UserSearchRequestDTO 用户搜索请求DTO
type UserSearchRequestDTO struct {
	TenantID    string     `json:"tenant_id" validate:"required"`
	Keyword     string     `json:"keyword,omitempty"`
	Status      string     `json:"status,omitempty"`
	Email       string     `json:"email,omitempty"`
	Phone       string     `json:"phone,omitempty"`
	CreatedFrom *time.Time `json:"created_from,omitempty"`
	CreatedTo   *time.Time `json:"created_to,omitempty"`
	Page        int        `json:"page" validate:"min=1"`
	PageSize    int        `json:"page_size" validate:"min=1,max=100"`
	SortBy      string     `json:"sort_by,omitempty"`
	SortDesc    bool       `json:"sort_desc,omitempty"`
}

// UserActivityRequestDTO 用户活动请求DTO
type UserActivityRequestDTO struct {
	UserID    string     `json:"user_id" validate:"required"`
	TenantID  string     `json:"tenant_id" validate:"required"`
	StartTime *time.Time `json:"start_time,omitempty"`
	EndTime   *time.Time `json:"end_time,omitempty"`
}

// ==================== 用户响应DTO ====================

// UserResponseDTO 用户响应DTO
type UserResponseDTO struct {
	User UserDTO `json:"user"`
}

// UserListResponseDTO 用户列表响应DTO
type UserListResponseDTO struct {
	Users      []UserDTO `json:"users"`
	Total      int64     `json:"total"`
	Page       int       `json:"page"`
	PageSize   int       `json:"page_size"`
	TotalPages int       `json:"total_pages"`
}

// UserStatisticsResponseDTO 用户统计响应DTO
type UserStatisticsResponseDTO struct {
	TotalUsers        int64            `json:"total_users"`
	ActiveUsers       int64            `json:"active_users"`
	InactiveUsers     int64            `json:"inactive_users"`
	SuspendedUsers    int64            `json:"suspended_users"`
	BannedUsers       int64            `json:"banned_users"`
	StatusBreakdown   map[string]int64 `json:"status_breakdown"`
	NewUsersToday     int64            `json:"new_users_today"`
	NewUsersThisWeek  int64            `json:"new_users_this_week"`
	NewUsersThisMonth int64            `json:"new_users_this_month"`
	RoleDistribution  map[string]int64 `json:"role_distribution"`
	LastUpdated       time.Time        `json:"last_updated"`
}

// UserActivityResponseDTO 用户活动响应DTO
type UserActivityResponseDTO struct {
	UserID          string    `json:"user_id"`
	LoginCount      int64     `json:"login_count"`
	LastLoginTime   time.Time `json:"last_login_time"`
	SessionDuration int64     `json:"session_duration"` // 总会话时长（秒）
	ActiveDays      int       `json:"active_days"`      // 活跃天数
	ActionsCount    int64     `json:"actions_count"`    // 操作次数
	StartTime       time.Time `json:"start_time"`
	EndTime         time.Time `json:"end_time"`
}

// ==================== 认证相关DTO ====================

// LoginRequestDTO 登录请求DTO
type LoginRequestDTO struct {
	TenantID   string `json:"tenant_id" validate:"required"`
	Identifier string `json:"identifier" validate:"required"` // 支持用户名/邮箱/手机号
	Password   string `json:"password" validate:"required"`
	DeviceID   string `json:"device_id,omitempty"`
	DeviceType string `json:"device_type,omitempty"`
	DeviceName string `json:"device_name,omitempty"`
	UserAgent  string `json:"user_agent,omitempty"`
	IPAddress  string `json:"ip_address,omitempty"`
	Location   string `json:"location,omitempty"`
	RememberMe bool   `json:"remember_me,omitempty"`
}

// LoginResponseDTO 登录响应DTO
type LoginResponseDTO struct {
	User         UserDTO   `json:"user"`
	AccessToken  string    `json:"access_token"`
	RefreshToken string    `json:"refresh_token"`
	ExpiresAt    time.Time `json:"expires_at"`
	SessionID    string    `json:"session_id"`
	Roles        []string  `json:"roles"`
	Permissions  []string  `json:"permissions"`
}

// RegisterRequestDTO 注册请求DTO
type RegisterRequestDTO struct {
	TenantID  string         `json:"tenant_id" validate:"required"`
	Username  string         `json:"username" validate:"required,min=3,max=30"`
	Email     string         `json:"email" validate:"required,email"`
	Phone     string         `json:"phone,omitempty"`
	Password  string         `json:"password" validate:"required,min=8"`
	Profile   UserProfileDTO `json:"profile"`
}

// RegisterResponseDTO 注册响应DTO
type RegisterResponseDTO struct {
	User         UserDTO   `json:"user"`
	AccessToken  string    `json:"access_token"`
	RefreshToken string    `json:"refresh_token"`
	ExpiresAt    time.Time `json:"expires_at"`
}

// ChangePasswordRequestDTO 修改密码请求DTO
type ChangePasswordRequestDTO struct {
	TenantID    string `json:"tenant_id" validate:"required"`
	UserID      string `json:"user_id" validate:"required"`
	OldPassword string `json:"old_password" validate:"required"`
	NewPassword string `json:"new_password" validate:"required,min=8"`
}

// ResetPasswordRequestDTO 重置密码请求DTO
type ResetPasswordRequestDTO struct {
	TenantID   string `json:"tenant_id" validate:"required"`
	Identifier string `json:"identifier" validate:"required"`               // 邮箱或手机号
	Method     string `json:"method" validate:"required,oneof=email phone"` // 重置方式
}

// VerifyEmailRequestDTO 邮箱验证请求DTO
type VerifyEmailRequestDTO struct {
	TenantID string `json:"tenant_id" validate:"required"`
	UserID   string `json:"user_id" validate:"required"`
	Token    string `json:"token" validate:"required"`
}

// VerifyPhoneRequestDTO 手机验证请求DTO
type VerifyPhoneRequestDTO struct {
	TenantID string `json:"tenant_id" validate:"required"`
	UserID   string `json:"user_id" validate:"required"`
	Code     string `json:"code" validate:"required"`
}

// ==================== 通用响应DTO ====================

// SuccessResponseDTO 成功响应DTO
type SuccessResponseDTO struct {
	Success bool   `json:"success"`
	Message string `json:"message"`
}

// ErrorResponseDTO 错误响应DTO
type ErrorResponseDTO struct {
	Success bool   `json:"success"`
	Error   string `json:"error"`
	Code    string `json:"code,omitempty"`
}
