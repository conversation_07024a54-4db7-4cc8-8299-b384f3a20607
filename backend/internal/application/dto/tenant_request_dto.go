package dto

import (
	"time"

	"github.com/shopspring/decimal"
)

// CreateTenantRequestDTO 创建租户请求DTO
type CreateTenantRequestDTO struct {
	Name         string `json:"name" validate:"required,min=2,max=100"`
	Domain       string `json:"domain" validate:"required,min=3,max=100"`
	DisplayName  string `json:"display_name" validate:"max=100"`
	Description  string `json:"description" validate:"max=500"`
	Type         string `json:"type" validate:"required,oneof=system enterprise professional basic trial"`
	Industry     string `json:"industry" validate:"max=100"`
	Country      string `json:"country" validate:"max=50"`
	Province     string `json:"province" validate:"max=50"`
	City         string `json:"city" validate:"max=50"`
	Address      string `json:"address" validate:"max=255"`
	ContactEmail string `json:"contact_email" validate:"omitempty,email"`
	ContactPhone string `json:"contact_phone" validate:"omitempty,len=11"`
}

// UpdateTenantRequestDTO 更新租户请求DTO
type UpdateTenantRequestDTO struct {
	Name         *string `json:"name,omitempty" validate:"omitempty,min=2,max=100"`
	DisplayName  *string `json:"display_name,omitempty" validate:"omitempty,max=100"`
	Description  *string `json:"description,omitempty" validate:"omitempty,max=500"`
	Industry     *string `json:"industry,omitempty" validate:"omitempty,max=100"`
	Country      *string `json:"country,omitempty" validate:"omitempty,max=50"`
	Province     *string `json:"province,omitempty" validate:"omitempty,max=50"`
	City         *string `json:"city,omitempty" validate:"omitempty,max=50"`
	Address      *string `json:"address,omitempty" validate:"omitempty,max=255"`
	ContactEmail *string `json:"contact_email,omitempty" validate:"omitempty,email"`
	ContactPhone *string `json:"contact_phone,omitempty" validate:"omitempty,len=11"`
}

// UpdateTenantStatusRequestDTO 更新租户状态请求DTO
type UpdateTenantStatusRequestDTO struct {
	Status string `json:"status" validate:"required,oneof=active suspended terminated"`
	Reason string `json:"reason" validate:"max=500"`
}

// TenantFilterRequestDTO 租户过滤请求DTO
type TenantFilterRequestDTO struct {
	Name        string     `json:"name,omitempty"`
	Domain      string     `json:"domain,omitempty"`
	Type        string     `json:"type,omitempty" validate:"omitempty,oneof=system enterprise professional basic trial"`
	Status      string     `json:"status,omitempty" validate:"omitempty,oneof=inactive active suspended terminated expired"`
	Industry    string     `json:"industry,omitempty"`
	Country     string     `json:"country,omitempty"`
	Province    string     `json:"province,omitempty"`
	City        string     `json:"city,omitempty"`
	CreatedFrom *time.Time `json:"created_from,omitempty"`
	CreatedTo   *time.Time `json:"created_to,omitempty"`
	UpdatedFrom *time.Time `json:"updated_from,omitempty"`
	UpdatedTo   *time.Time `json:"updated_to,omitempty"`
	Keywords    string     `json:"keywords,omitempty"`
}

// PaginationRequestDTO 分页请求DTO
type PaginationRequestDTO struct {
	Page     int    `json:"page" validate:"min=1" form:"page"`
	PageSize int    `json:"page_size" validate:"min=1,max=100" form:"page_size"`
	SortBy   string `json:"sort_by,omitempty" form:"sort_by"`
	SortDesc bool   `json:"sort_desc,omitempty" form:"sort_desc"`
}

// CreateSubscriptionRequestDTO 创建订阅请求DTO
type CreateSubscriptionRequestDTO struct {
	TenantID     string          `json:"tenant_id" validate:"required"`
	PlanID       string          `json:"plan_id" validate:"required"`
	PlanName     string          `json:"plan_name" validate:"required"`
	StartDate    time.Time       `json:"start_date" validate:"required"`
	EndDate      time.Time       `json:"end_date" validate:"required"`
	Price        decimal.Decimal `json:"price" validate:"required"`
	Currency     string          `json:"currency" validate:"required,len=3"`
	BillingCycle string          `json:"billing_cycle" validate:"required,oneof=monthly yearly lifetime"`
	UserLimit    int             `json:"user_limit" validate:"min=1"`
	StorageLimit int64           `json:"storage_limit" validate:"min=1"`
	APILimit     int             `json:"api_limit" validate:"min=1"`
	ProductLimit int             `json:"product_limit" validate:"min=1"`
	OrderLimit   int             `json:"order_limit" validate:"min=1"`
}

// UpdateSubscriptionRequestDTO 更新订阅请求DTO
type UpdateSubscriptionRequestDTO struct {
	PlanName     *string          `json:"plan_name,omitempty"`
	Price        *decimal.Decimal `json:"price,omitempty"`
	Currency     *string          `json:"currency,omitempty" validate:"omitempty,len=3"`
	BillingCycle *string          `json:"billing_cycle,omitempty" validate:"omitempty,oneof=monthly yearly lifetime"`
	AutoRenewal  *bool            `json:"auto_renewal,omitempty"`
	UserLimit    *int             `json:"user_limit,omitempty" validate:"omitempty,min=1"`
	StorageLimit *int64           `json:"storage_limit,omitempty" validate:"omitempty,min=1"`
	APILimit     *int             `json:"api_limit,omitempty" validate:"omitempty,min=1"`
	ProductLimit *int             `json:"product_limit,omitempty" validate:"omitempty,min=1"`
	OrderLimit   *int             `json:"order_limit,omitempty" validate:"omitempty,min=1"`
}

// RenewSubscriptionRequestDTO 续费订阅请求DTO
type RenewSubscriptionRequestDTO struct {
	StartDate    time.Time       `json:"start_date" validate:"required"`
	EndDate      time.Time       `json:"end_date" validate:"required"`
	Price        decimal.Decimal `json:"price" validate:"required"`
	Currency     string          `json:"currency" validate:"required,len=3"`
	BillingCycle string          `json:"billing_cycle" validate:"required,oneof=monthly yearly lifetime"`
}

// CancelSubscriptionRequestDTO 取消订阅请求DTO
type CancelSubscriptionRequestDTO struct {
	Reason       string  `json:"reason" validate:"required,max=500"`
	RefundAmount float64 `json:"refund_amount,omitempty" validate:"omitempty,min=0"`
	RefundReason string  `json:"refund_reason,omitempty" validate:"omitempty,max=500"`
}

// ExtendTrialRequestDTO 延长试用期请求DTO
type ExtendTrialRequestDTO struct {
	Days   int    `json:"days" validate:"required,min=1,max=90"`
	Reason string `json:"reason" validate:"required,max=500"`
}

// SubscriptionFilterRequestDTO 订阅过滤请求DTO
type SubscriptionFilterRequestDTO struct {
	TenantID       string     `json:"tenant_id,omitempty"`
	PlanID         string     `json:"plan_id,omitempty"`
	PlanName       string     `json:"plan_name,omitempty"`
	Status         string     `json:"status,omitempty" validate:"omitempty,oneof=pending active suspended canceled expired"`
	IsTrialPeriod  *bool      `json:"is_trial_period,omitempty"`
	AutoRenewal    *bool      `json:"auto_renewal,omitempty"`
	Currency       string     `json:"currency,omitempty"`
	BillingCycle   string     `json:"billing_cycle,omitempty" validate:"omitempty,oneof=monthly yearly lifetime"`
	ExpiringInDays int        `json:"expiring_in_days,omitempty" validate:"omitempty,min=0,max=365"`
	CreatedFrom    *time.Time `json:"created_from,omitempty"`
	CreatedTo      *time.Time `json:"created_to,omitempty"`
}

// UpdateQuotaLimitsRequestDTO 更新配额限制请求DTO
type UpdateQuotaLimitsRequestDTO struct {
	UserLimit       *int   `json:"user_limit,omitempty" validate:"omitempty,min=1"`
	StorageLimit    *int64 `json:"storage_limit,omitempty" validate:"omitempty,min=1"`
	APILimit        *int   `json:"api_limit,omitempty" validate:"omitempty,min=1"`
	ProductLimit    *int   `json:"product_limit,omitempty" validate:"omitempty,min=1"`
	OrderLimit      *int   `json:"order_limit,omitempty" validate:"omitempty,min=1"`
	FileUploadLimit *int   `json:"file_upload_limit,omitempty" validate:"omitempty,min=1"`
	EmailLimit      *int   `json:"email_limit,omitempty" validate:"omitempty,min=1"`
}

// BatchUpdateStatusRequestDTO 批量更新状态请求DTO
type BatchUpdateStatusRequestDTO struct {
	TenantIDs []string `json:"tenant_ids" validate:"required,min=1"`
	Status    string   `json:"status" validate:"required,oneof=active suspended terminated"`
	Reason    string   `json:"reason" validate:"max=500"`
}

// BatchDeleteRequestDTO 批量删除请求DTO
type BatchDeleteRequestDTO struct {
	TenantIDs []string `json:"tenant_ids" validate:"required,min=1"`
	Reason    string   `json:"reason" validate:"required,max=500"`
}

// StatisticsFilterRequestDTO 统计过滤请求DTO
type StatisticsFilterRequestDTO struct {
	StartDate *time.Time `json:"start_date,omitempty"`
	EndDate   *time.Time `json:"end_date,omitempty"`
	Type      string     `json:"type,omitempty" validate:"omitempty,oneof=system enterprise professional basic trial"`
	Status    string     `json:"status,omitempty" validate:"omitempty,oneof=inactive active suspended terminated expired"`
	Country   string     `json:"country,omitempty"`
	Province  string     `json:"province,omitempty"`
	Industry  string     `json:"industry,omitempty"`
	GroupBy   string     `json:"group_by,omitempty" validate:"omitempty,oneof=day week month year"`
}

// RevenueFilterRequestDTO 收入统计过滤请求DTO
type RevenueFilterRequestDTO struct {
	StartDate    time.Time `json:"start_date" validate:"required"`
	EndDate      time.Time `json:"end_date" validate:"required"`
	Currency     string    `json:"currency,omitempty"`
	PlanID       string    `json:"plan_id,omitempty"`
	BillingCycle string    `json:"billing_cycle,omitempty" validate:"omitempty,oneof=monthly yearly lifetime"`
	GroupBy      string    `json:"group_by,omitempty" validate:"omitempty,oneof=day week month year"`
}

// HealthCheckRequestDTO 健康检查请求DTO
type HealthCheckRequestDTO struct {
	TenantIDs           []string `json:"tenant_ids,omitempty"`
	CheckAll            bool     `json:"check_all,omitempty"`
	IncludeQuota        bool     `json:"include_quota,omitempty"`
	IncludeSubscription bool     `json:"include_subscription,omitempty"`
}

// TenantSettingsRequestDTO 租户设置请求DTO
type TenantSettingsRequestDTO struct {
	Settings map[string]interface{} `json:"settings" validate:"required"`
}

// ValidateDomainRequestDTO 验证域名请求DTO
type ValidateDomainRequestDTO struct {
	Domain string `json:"domain" validate:"required,min=3,max=100"`
}

// SearchTenantsRequestDTO 搜索租户请求DTO
type SearchTenantsRequestDTO struct {
	Query  string   `json:"query" validate:"required,min=1"`
	Fields []string `json:"fields,omitempty"` // name, domain, description, industry
	PaginationRequestDTO
}

// ExportTenantsRequestDTO 导出租户请求DTO
type ExportTenantsRequestDTO struct {
	Format              string                  `json:"format" validate:"required,oneof=csv excel json"`
	Fields              []string                `json:"fields,omitempty"`
	Filter              *TenantFilterRequestDTO `json:"filter,omitempty"`
	IncludeQuota        bool                    `json:"include_quota,omitempty"`
	IncludeSubscription bool                    `json:"include_subscription,omitempty"`
}

// ImportTenantsRequestDTO 导入租户请求DTO
type ImportTenantsRequestDTO struct {
	Format     string `json:"format" validate:"required,oneof=csv excel json"`
	Data       string `json:"data" validate:"required"`
	DryRun     bool   `json:"dry_run,omitempty"`
	SkipErrors bool   `json:"skip_errors,omitempty"`
}
