package service

import (
	"context"
	"fmt"
	"sort"
	"time"

	"backend/internal/domain/menu/entity"
	"backend/internal/domain/menu/repository"
	"backend/internal/domain/menu/service"
	"backend/pkg/infrastructure/auth"
	"backend/pkg/infrastructure/logger"
)

// RouteTreeNode 路由树节点
type RouteTreeNode struct {
	ID          string           `json:"id"`
	Name        string           `json:"name"`
	Path        string           `json:"path"`
	Component   string           `json:"component,omitempty"`
	Redirect    string           `json:"redirect,omitempty"`
	Meta        RouteNodeMeta    `json:"meta"`
	Children    []*RouteTreeNode `json:"children,omitempty"`
}

// RouteNodeMeta 路由节点元数据
type RouteNodeMeta struct {
	Title       string   `json:"title"`
	Icon        string   `json:"icon,omitempty"`
	Roles       []string `json:"roles,omitempty"`
	Permissions []string `json:"permissions,omitempty"`
	NoCache     bool     `json:"noCache"`
	Breadcrumb  bool     `json:"breadcrumb"`
	ActiveMenu  string   `json:"activeMenu,omitempty"`
	Hidden      bool     `json:"hidden"`
	KeepAlive   bool     `json:"keepAlive"`
	IsExternal  bool     `json:"isExternal"`
	Badge       string   `json:"badge,omitempty"`
	BadgeType   string   `json:"badgeType,omitempty"`
	Target      string   `json:"target,omitempty"`
}

// UserRouteTreeResult 用户路由树结果
type UserRouteTreeResult struct {
	UserID      string           `json:"user_id"`
	TenantID    string           `json:"tenant_id"`
	Routes      []*RouteTreeNode `json:"routes"`
	Permissions []string         `json:"permissions"`    // 用户所有权限列表
	Roles       []string         `json:"roles"`          // 用户角色列表
	GeneratedAt time.Time        `json:"generated_at"`
}

// RouteTreeService 路由树服务接口
type RouteTreeService interface {
	// 生成用户路由树
	GenerateUserRouteTree(ctx context.Context, userID, tenantID string) (*UserRouteTreeResult, error)
	
	// 检查路由访问权限
	CheckRouteAccess(ctx context.Context, userID, tenantID, routePath string) (bool, error)
	
	// 获取用户可访问的菜单列表
	GetUserAccessibleMenus(ctx context.Context, userID, tenantID string) ([]*entity.Menu, error)
	
	// 刷新用户路由树缓存
	RefreshUserRouteTreeCache(ctx context.Context, userID, tenantID string) error
}

// routeTreeService 路由树服务实现
type routeTreeService struct {
	menuRepo        repository.MenuRepository
	menuDomainSvc   service.MenuDomainService
	casbinManager   auth.CasbinManager
	logger          logger.Logger
}

// NewRouteTreeService 创建路由树服务
func NewRouteTreeService(
	menuRepo repository.MenuRepository,
	menuDomainSvc service.MenuDomainService,
	casbinManager auth.CasbinManager,
	logger logger.Logger,
) RouteTreeService {
	return &routeTreeService{
		menuRepo:      menuRepo,
		menuDomainSvc: menuDomainSvc,
		casbinManager: casbinManager,
		logger:        logger,
	}
}

// GenerateUserRouteTree 生成用户路由树
func (s *routeTreeService) GenerateUserRouteTree(ctx context.Context, userID, tenantID string) (*UserRouteTreeResult, error) {
	s.logger.Info(ctx, "Generating user route tree", map[string]interface{}{
		"user_id":   userID,
		"tenant_id": tenantID,
	})

	// 1. 获取用户角色
	roles, err := s.casbinManager.GetRolesForUser(ctx, userID, tenantID)
	if err != nil {
		return nil, fmt.Errorf("获取用户角色失败: %w", err)
	}

	// 2. 获取用户权限
	permissions, err := s.casbinManager.GetPermissionsForUser(ctx, userID, tenantID)
	if err != nil {
		return nil, fmt.Errorf("获取用户权限失败: %w", err)
	}

	// 3. 获取用户可访问的菜单
	accessibleMenus, err := s.GetUserAccessibleMenus(ctx, userID, tenantID)
	if err != nil {
		return nil, fmt.Errorf("获取用户可访问菜单失败: %w", err)
	}

	// 4. 构建路由树
	routeTree := s.buildRouteTree(accessibleMenus)

	// 5. 转换权限格式
	permissionList := s.convertPermissions(permissions)

	result := &UserRouteTreeResult{
		UserID:      userID,
		TenantID:    tenantID,
		Routes:      routeTree,
		Permissions: permissionList,
		Roles:       roles,
		GeneratedAt: time.Now(),
	}

	s.logger.Info(ctx, "User route tree generated successfully", map[string]interface{}{
		"user_id":     userID,
		"tenant_id":   tenantID,
		"route_count": len(routeTree),
		"permissions": len(permissionList),
		"roles":       len(roles),
	})

	return result, nil
}

// GetUserAccessibleMenus 获取用户可访问的菜单列表
func (s *routeTreeService) GetUserAccessibleMenus(ctx context.Context, userID, tenantID string) ([]*entity.Menu, error) {
	// 1. 获取用户角色
	roles, err := s.casbinManager.GetRolesForUser(ctx, userID, tenantID)
	if err != nil {
		return nil, fmt.Errorf("获取用户角色失败: %w", err)
	}

	// 2. 获取用户权限
	permissions, err := s.casbinManager.GetPermissionsForUser(ctx, userID, tenantID)
	if err != nil {
		return nil, fmt.Errorf("获取用户权限失败: %w", err)
	}

	// 3. 获取所有可见菜单
	allMenus, err := s.menuRepo.FindVisibleMenus(ctx, tenantID)
	if err != nil {
		return nil, fmt.Errorf("获取可见菜单失败: %w", err)
	}

	// 4. 过滤用户可访问的菜单
	accessibleMenus := s.filterAccessibleMenus(ctx, allMenus, userID, tenantID, roles, permissions)

	return accessibleMenus, nil
}

// CheckRouteAccess 检查路由访问权限
func (s *routeTreeService) CheckRouteAccess(ctx context.Context, userID, tenantID, routePath string) (bool, error) {
	// 1. 根据路径查找菜单
	menu, err := s.menuRepo.FindByExactPath(ctx, tenantID, routePath)
	if err != nil {
		// 如果菜单不存在，默认允许访问（可能是动态路由）
		return true, nil
	}

	// 2. 检查菜单权限
	return s.checkMenuPermission(ctx, menu, userID, tenantID)
}

// RefreshUserRouteTreeCache 刷新用户路由树缓存
func (s *routeTreeService) RefreshUserRouteTreeCache(ctx context.Context, userID, tenantID string) error {
	// TODO: 实现缓存刷新逻辑
	s.logger.Info(ctx, "Refreshing user route tree cache", map[string]interface{}{
		"user_id":   userID,
		"tenant_id": tenantID,
	})
	return nil
}

// filterAccessibleMenus 过滤用户可访问的菜单
func (s *routeTreeService) filterAccessibleMenus(ctx context.Context, menus []*entity.Menu, userID, tenantID string, roles []string, permissions [][]string) []*entity.Menu {
	var accessibleMenus []*entity.Menu

	for _, menu := range menus {
		// 检查菜单权限
		if accessible, err := s.checkMenuPermissionWithRolesAndPermissions(ctx, menu, userID, tenantID, roles, permissions); err == nil && accessible {
			accessibleMenus = append(accessibleMenus, menu)
		}
	}

	return accessibleMenus
}

// checkMenuPermission 检查菜单权限
func (s *routeTreeService) checkMenuPermission(ctx context.Context, menu *entity.Menu, userID, tenantID string) (bool, error) {
	// 如果菜单没有权限要求，默认可访问
	if !menu.HasPermission() {
		return true, nil
	}

	// 获取用户角色
	roles, err := s.casbinManager.GetRolesForUser(ctx, userID, tenantID)
	if err != nil {
		return false, fmt.Errorf("获取用户角色失败: %w", err)
	}

	// 检查具体权限
	if menu.Resource != "" && menu.Action != "" {
		for _, role := range roles {
			allowed, err := s.casbinManager.CheckPermission(ctx, role, tenantID, menu.Resource, menu.Action)
			if err == nil && allowed {
				return true, nil
			}
		}
	}

	// 检查权限标识
	if menu.Permission != "" {
		permissions, err := s.casbinManager.GetPermissionsForUser(ctx, userID, tenantID)
		if err != nil {
			return false, fmt.Errorf("获取用户权限失败: %w", err)
		}

		for _, perm := range permissions {
			if len(perm) > 0 && perm[0] == menu.Permission {
				return true, nil
			}
		}
	}

	return false, nil
}

// checkMenuPermissionWithRolesAndPermissions 使用已获取的角色和权限检查菜单权限
func (s *routeTreeService) checkMenuPermissionWithRolesAndPermissions(ctx context.Context, menu *entity.Menu, userID, tenantID string, roles []string, permissions [][]string) (bool, error) {
	// 如果菜单没有权限要求，默认可访问
	if !menu.HasPermission() {
		return true, nil
	}

	// 检查具体权限
	if menu.Resource != "" && menu.Action != "" {
		for _, role := range roles {
			allowed, err := s.casbinManager.CheckPermission(ctx, role, tenantID, menu.Resource, menu.Action)
			if err == nil && allowed {
				return true, nil
			}
		}
	}

	// 检查权限标识
	if menu.Permission != "" {
		for _, perm := range permissions {
			if len(perm) > 0 && perm[0] == menu.Permission {
				return true, nil
			}
		}
	}

	return false, nil
}

// buildRouteTree 构建路由树
func (s *routeTreeService) buildRouteTree(menus []*entity.Menu) []*RouteTreeNode {
	// 创建菜单映射
	menuMap := make(map[string]*entity.Menu)
	for _, menu := range menus {
		menuMap[menu.BusinessID] = menu
	}

	// 构建树形结构
	var rootNodes []*RouteTreeNode

	for _, menu := range menus {
		if menu.IsRoot() {
			// 根节点
			node := s.convertMenuToRouteNode(menu)
			s.buildChildNodes(node, menu.BusinessID, menuMap)
			rootNodes = append(rootNodes, node)
		}
	}

	// 按排序字段排序
	sort.Slice(rootNodes, func(i, j int) bool {
		return menuMap[rootNodes[i].ID].Sort < menuMap[rootNodes[j].ID].Sort
	})

	return rootNodes
}

// buildChildNodes 构建子节点
func (s *routeTreeService) buildChildNodes(parentNode *RouteTreeNode, parentID string, menuMap map[string]*entity.Menu) {
	for _, menu := range menuMap {
		if menu.ParentID == parentID {
			childNode := s.convertMenuToRouteNode(menu)
			s.buildChildNodes(childNode, menu.BusinessID, menuMap)
			
			if parentNode.Children == nil {
				parentNode.Children = make([]*RouteTreeNode, 0)
			}
			parentNode.Children = append(parentNode.Children, childNode)
		}
	}

	// 排序子节点
	if parentNode.Children != nil {
		sort.Slice(parentNode.Children, func(i, j int) bool {
			return menuMap[parentNode.Children[i].ID].Sort < menuMap[parentNode.Children[j].ID].Sort
		})
	}
}

// convertMenuToRouteNode 转换菜单为路由节点
func (s *routeTreeService) convertMenuToRouteNode(menu *entity.Menu) *RouteTreeNode {
	node := &RouteTreeNode{
		ID:        menu.BusinessID,
		Name:      menu.Name,
		Path:      menu.Path,
		Component: menu.Component,
		Meta: RouteNodeMeta{
			Title:      menu.Title,
			Icon:       menu.Icon,
			NoCache:    menu.Meta.NoCache,
			Breadcrumb: menu.Meta.Breadcrumb,
			ActiveMenu: menu.Meta.ActiveMenu,
			Hidden:     menu.Meta.Hidden,
			KeepAlive:  menu.KeepAlive,
			IsExternal: menu.IsExternal,
			Badge:      menu.Meta.Badge,
			BadgeType:  menu.Meta.BadgeType,
			Target:     menu.Meta.Target,
		},
	}

	// 设置权限信息
	if menu.HasPermission() {
		node.Meta.Permissions = []string{menu.GetPermissionString()}
	}

	// 设置角色信息
	if len(menu.Meta.Roles) > 0 {
		node.Meta.Roles = menu.Meta.Roles
	}

	return node
}

// convertPermissions 转换权限格式
func (s *routeTreeService) convertPermissions(permissions [][]string) []string {
	var permissionList []string
	for _, perm := range permissions {
		if len(perm) > 0 {
			permissionList = append(permissionList, perm[0])
		}
	}
	return permissionList
}
