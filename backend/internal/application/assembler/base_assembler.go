package assembler

import (
	"backend/internal/application/pagination"
	commonPagination "backend/pkg/common/pagination"
)

// BaseAssembler 基础组装器
type BaseAssembler struct {
	paginationService pagination.PaginationService
}

// NewBaseAssembler 创建基础组装器
func NewBaseAssembler() *BaseAssembler {
	return &BaseAssembler{
		paginationService: pagination.NewDefaultPaginationService(nil),
	}
}

// CreatePaginationRequest 创建分页请求
func (a *BaseAssembler) CreatePaginationRequest(page, pageSize int) *commonPagination.Request {
	req := &commonPagination.Request{
		Page:     page,
		PageSize: pageSize,
	}
	req.SetDefaults()
	return req
}

// CreatePaginationRequestWithSort 创建带排序的分页请求
func (a *BaseAssembler) CreatePaginationRequestWithSort(
	page, pageSize int,
	sorts []commonPagination.SortField,
) *commonPagination.Request {
	req := &commonPagination.Request{
		Page:     page,
		PageSize: pageSize,
		Sort:     sorts,
	}
	req.SetDefaults()
	return req
}

// CreatePaginationRequestWithFilters 创建带过滤的分页请求
func (a *BaseAssembler) CreatePaginationRequestWithFilters(
	page, pageSize int,
	filter commonPagination.Filter,
) *commonPagination.Request {
	req := &commonPagination.Request{
		Page:     page,
		PageSize: pageSize,
		Filter:   filter,
	}
	req.SetDefaults()
	return req
}

// ValidatePaginationRequest 验证分页请求
func (a *BaseAssembler) ValidatePaginationRequest(request *commonPagination.Request) error {
	return a.paginationService.ValidateRequest(request)
}

// CreateCursorPaginationRequest 创建游标分页请求
func (a *BaseAssembler) CreateCursorPaginationRequest(cursor string, limit int, sortBy string) *commonPagination.CursorRequest {
	req := &commonPagination.CursorRequest{
		Cursor: cursor,
		Limit:  limit,
		SortBy: sortBy,
	}
	req.SetDefaults()
	return req
}

// 全局基础组装器实例
var defaultBaseAssembler = NewBaseAssembler()

// 全局便捷函数

// BuildPaginationResponse 构建分页响应
func BuildPaginationResponse[T any](
	items []T,
	total int64,
	request *commonPagination.Request,
) *commonPagination.Response[T] {
	return commonPagination.NewResponse(items, total, request)
}

// BuildSimplePaginationResponse 构建简单分页响应
func BuildSimplePaginationResponse[T any](
	items []T,
	total int64,
	page, pageSize int,
) *commonPagination.Response[T] {
	request := defaultBaseAssembler.CreatePaginationRequest(page, pageSize)
	return commonPagination.NewResponse(items, total, request)
}

// BuildSortedPaginationResponse 构建带排序的分页响应
func BuildSortedPaginationResponse[T any](
	items []T,
	total int64,
	page, pageSize int,
	sorts []commonPagination.SortField,
) *commonPagination.Response[T] {
	request := defaultBaseAssembler.CreatePaginationRequestWithSort(page, pageSize, sorts)
	return commonPagination.NewResponse(items, total, request)
}

// BuildFilteredPaginationResponse 构建带过滤的分页响应
func BuildFilteredPaginationResponse[T any](
	items []T,
	total int64,
	page, pageSize int,
	filter commonPagination.Filter,
) *commonPagination.Response[T] {
	request := defaultBaseAssembler.CreatePaginationRequestWithFilters(page, pageSize, filter)
	return commonPagination.NewResponse(items, total, request)
}

// BuildCursorPaginationResponse 构建游标分页响应
func BuildCursorPaginationResponse[T any](
	items []T,
	nextCursor, prevCursor string,
	limit int,
) *commonPagination.CursorResponse[T] {
	return commonPagination.NewCursorResponse(items, nextCursor, prevCursor, limit)
}

// CreatePaginationRequest 全局便捷函数
func CreatePaginationRequest(page, pageSize int) *commonPagination.Request {
	return defaultBaseAssembler.CreatePaginationRequest(page, pageSize)
}

// CreatePaginationRequestWithSort 全局便捷函数
func CreatePaginationRequestWithSort(
	page, pageSize int,
	sorts []commonPagination.SortField,
) *commonPagination.Request {
	return defaultBaseAssembler.CreatePaginationRequestWithSort(page, pageSize, sorts)
}

// CreatePaginationRequestWithFilters 全局便捷函数
func CreatePaginationRequestWithFilters(
	page, pageSize int,
	filter commonPagination.Filter,
) *commonPagination.Request {
	return defaultBaseAssembler.CreatePaginationRequestWithFilters(page, pageSize, filter)
}

// ValidatePaginationRequest 全局便捷函数
func ValidatePaginationRequest(request *commonPagination.Request) error {
	return defaultBaseAssembler.ValidatePaginationRequest(request)
}
