package assembler

import (
	"fmt"
	"time"

	"backend/internal/application/dto"
	"backend/internal/domain/tenant/entity"
	"backend/internal/domain/tenant/valueobject"
)

// TenantAssembler 租户组装器
type TenantAssembler struct{}

// NewTenantAssembler 创建租户组装器
func NewTenantAssembler() *TenantAssembler {
	return &TenantAssembler{}
}

// EntityToDTO 将租户实体转换为DTO
func (a *TenantAssembler) EntityToDTO(tenant *entity.Tenant) *dto.TenantDTO {
	if tenant == nil {
		return nil
	}

	// 转换设置为map
	settingsMap := make(map[string]interface{})
	if tenant.Settings != nil {
		settingsMap = map[string]interface{}(tenant.Settings)
	}

	return &dto.TenantDTO{
		ID:               fmt.Sprintf("%d", tenant.ID),
		Name:             tenant.Name,
		Domain:           tenant.Domain,
		DisplayName:      tenant.DisplayName,
		Description:      tenant.Description,
		Type:             tenant.Type.String(),
		Status:           tenant.Status.String(),
		StatusDisplay:    tenant.Status.GetDisplayName(),
		Industry:         tenant.Industry,
		Country:          tenant.Country,
		Province:         tenant.Province,
		City:             tenant.City,
		Address:          tenant.Address,
		ContactEmail:     tenant.ContactEmail,
		ContactPhone:     tenant.ContactPhone,
		Settings:         settingsMap,
		MaxUsers:         tenant.MaxUsers,
		MaxStorage:       tenant.MaxStorage,
		MaxAPIQuota:      tenant.MaxAPIQuota,
		SubscriptionPlan: tenant.SubscriptionPlan,
		ExpiryDate:       tenant.SubscriptionEnd,
		CreatedAt:        tenant.CreatedAt,
		UpdatedAt:        tenant.UpdatedAt,
		Version:          tenant.Version,
	}
}

// EntitiesToDTOs 将租户实体列表转换为DTO列表
func (a *TenantAssembler) EntitiesToDTOs(tenants []*entity.Tenant) []*dto.TenantDTO {
	if tenants == nil {
		return nil
	}

	dtos := make([]*dto.TenantDTO, len(tenants))
	for i, tenant := range tenants {
		dtos[i] = a.EntityToDTO(tenant)
	}
	return dtos
}

// CreateRequestToEntity 将创建请求DTO转换为租户实体
func (a *TenantAssembler) CreateRequestToEntity(req *dto.CreateTenantRequestDTO) (*entity.Tenant, error) {
	if req == nil {
		return nil, nil
	}

	// 创建租户类型值对象
	tenantType := valueobject.TenantType(req.Type)
	if !tenantType.IsValid() {
		return nil, fmt.Errorf("无效的租户类型: %s", req.Type)
	}

	// 创建租户实体
	tenant, err := entity.NewTenant(req.Name, req.Domain, req.DisplayName, tenantType)
	if err != nil {
		return nil, err
	}

	// 设置可选字段
	if req.Description != "" {
		tenant.Description = req.Description
	}
	if req.Industry != "" {
		tenant.Industry = req.Industry
	}
	if req.Country != "" {
		tenant.Country = req.Country
	}
	if req.Province != "" {
		tenant.Province = req.Province
	}
	if req.City != "" {
		tenant.City = req.City
	}
	if req.Address != "" {
		tenant.Address = req.Address
	}
	if req.ContactEmail != "" {
		tenant.ContactEmail = req.ContactEmail
	}
	if req.ContactPhone != "" {
		tenant.ContactPhone = req.ContactPhone
	}

	return tenant, nil
}

// QuotaEntityToDTO 将配额实体转换为DTO
func (a *TenantAssembler) QuotaEntityToDTO(quota *entity.TenantQuota) *dto.TenantQuotaDTO {
	if quota == nil {
		return nil
	}

	// 计算利用率
	userUtilization := float64(quota.UserUsed) / float64(quota.UserLimit) * 100
	if quota.UserLimit == 0 {
		userUtilization = 0
	}

	storageUtilization := float64(quota.StorageUsed) / float64(quota.StorageLimit) * 100
	if quota.StorageLimit == 0 {
		storageUtilization = 0
	}

	apiUtilization := float64(quota.APIUsedMonth) / float64(quota.APILimitMonth) * 100
	if quota.APILimitMonth == 0 {
		apiUtilization = 0
	}

	productUtilization := float64(quota.ProductUsed) / float64(quota.ProductLimit) * 100
	if quota.ProductLimit == 0 {
		productUtilization = 0
	}

	orderUtilization := float64(quota.OrderUsedMonth) / float64(quota.OrderLimitMonth) * 100
	if quota.OrderLimitMonth == 0 {
		orderUtilization = 0
	}

	// 检查是否超配额
	isOverQuota := userUtilization > 100 || storageUtilization > 100 ||
		apiUtilization > 100 || productUtilization > 100 || orderUtilization > 100

	// 生成告警
	alerts := a.generateQuotaAlerts(quota)

	return &dto.TenantQuotaDTO{
		TenantID:           quota.TenantID,
		UserUsed:           quota.UserUsed,
		UserLimit:          quota.UserLimit,
		UserUtilization:    userUtilization,
		StorageUsed:        quota.StorageUsed,
		StorageLimit:       quota.StorageLimit,
		StorageUtilization: storageUtilization,
		APIUsedMonth:       quota.APIUsedMonth,
		APILimitMonth:      quota.APILimitMonth,
		APIUtilization:     apiUtilization,
		APIResetDate:       quota.APIResetDate,
		ProductUsed:        quota.ProductUsed,
		ProductLimit:       quota.ProductLimit,
		ProductUtilization: productUtilization,
		OrderUsedMonth:     quota.OrderUsedMonth,
		OrderLimitMonth:    quota.OrderLimitMonth,
		OrderUtilization:   orderUtilization,
		FileUploadUsed:     quota.FileUploadUsedMonth,
		FileUploadLimit:    quota.FileUploadLimitMonth,
		EmailUsedMonth:     quota.EmailSentMonth,
		EmailLimitMonth:    quota.EmailLimitMonth,
		IsOverQuota:        isOverQuota,
		Alerts:             alerts,
		LastUpdated:        quota.UpdatedAt,
	}
}

// generateQuotaAlerts 生成配额告警
func (a *TenantAssembler) generateQuotaAlerts(quota *entity.TenantQuota) []dto.QuotaAlertDTO {
	var alerts []dto.QuotaAlertDTO
	now := time.Now()

	// 检查用户配额
	if userRate := float64(quota.UserUsed) / float64(quota.UserLimit) * 100; userRate >= 90 {
		level := "warning"
		if userRate >= 100 {
			level = "critical"
		}
		alerts = append(alerts, dto.QuotaAlertDTO{
			Type:      "user",
			Level:     level,
			Message:   "用户配额使用率过高",
			Threshold: 90.0,
			Current:   userRate,
			CreatedAt: now,
		})
	}

	// 检查存储配额
	if storageRate := float64(quota.StorageUsed) / float64(quota.StorageLimit) * 100; storageRate >= 90 {
		level := "warning"
		if storageRate >= 100 {
			level = "critical"
		}
		alerts = append(alerts, dto.QuotaAlertDTO{
			Type:      "storage",
			Level:     level,
			Message:   "存储配额使用率过高",
			Threshold: 90.0,
			Current:   storageRate,
			CreatedAt: now,
		})
	}

	// 检查API配额
	if apiRate := float64(quota.APIUsedMonth) / float64(quota.APILimitMonth) * 100; apiRate >= 90 {
		level := "warning"
		if apiRate >= 100 {
			level = "critical"
		}
		alerts = append(alerts, dto.QuotaAlertDTO{
			Type:      "api",
			Level:     level,
			Message:   "API配额使用率过高",
			Threshold: 90.0,
			Current:   apiRate,
			CreatedAt: now,
		})
	}

	return alerts
}

// SubscriptionEntityToDTO 将订阅实体转换为DTO
func (a *TenantAssembler) SubscriptionEntityToDTO(subscription *entity.TenantSubscription) *dto.TenantSubscriptionDTO {
	if subscription == nil {
		return nil
	}

	// 计算到期天数
	daysUntilExpiry := subscription.DaysUntilExpiry()

	// 判断状态
	isExpired := subscription.IsExpired()
	isExpiringSoon := daysUntilExpiry <= 30 && daysUntilExpiry > 0
	canExtendTrial := subscription.IsTrialPeriod && subscription.TrialExtensions < 3 // 假设最多延期3次

	// 解析功能列表
	features := subscription.GetFeatures()

	return &dto.TenantSubscriptionDTO{
		ID:              fmt.Sprintf("%d", subscription.ID),
		TenantID:        subscription.TenantID,
		PlanID:          subscription.PlanID,
		PlanName:        subscription.PlanName,
		Status:          subscription.Status.String(),
		StatusDisplay:   subscription.Status.GetDisplayName(),
		StartDate:       subscription.StartDate,
		EndDate:         subscription.EndDate,
		RenewalDate:     subscription.RenewalDate,
		Price:           subscription.Price,
		Currency:        subscription.Currency,
		BillingCycle:    subscription.BillingCycle,
		FormattedPrice:  fmt.Sprintf("%.2f %s", subscription.Price.InexactFloat64(), subscription.Currency),
		AutoRenewal:     subscription.AutoRenewal,
		UserLimit:       subscription.UserLimit,
		StorageLimit:    subscription.StorageLimit,
		APILimit:        subscription.APILimit,
		ProductLimit:    subscription.ProductLimit,
		OrderLimit:      subscription.OrderLimit,
		Features:        features,
		IsTrialPeriod:   subscription.IsTrialPeriod,
		TrialEndDate:    subscription.TrialEndDate,
		TrialExtensions: subscription.TrialExtensions,
		DaysUntilExpiry: daysUntilExpiry,
		IsExpired:       isExpired,
		IsExpiringSoon:  isExpiringSoon,
		CanExtendTrial:  canExtendTrial,
		CanceledAt:      subscription.CanceledAt,
		CancelReason:    subscription.CancelReason,
		CanceledByUser:  subscription.CanceledByUser,
		CreatedAt:       subscription.CreatedAt,
		UpdatedAt:       subscription.UpdatedAt,
	}
}

// SubscriptionEntitiesToDTOs 将订阅实体列表转换为DTO列表
func (a *TenantAssembler) SubscriptionEntitiesToDTOs(subscriptions []*entity.TenantSubscription) []*dto.TenantSubscriptionDTO {
	if subscriptions == nil {
		return nil
	}

	dtos := make([]*dto.TenantSubscriptionDTO, len(subscriptions))
	for i, subscription := range subscriptions {
		dtos[i] = a.SubscriptionEntityToDTO(subscription)
	}
	return dtos
}

// CreateSubscriptionRequestToEntity 将创建订阅请求DTO转换为订阅实体
func (a *TenantAssembler) CreateSubscriptionRequestToEntity(req *dto.CreateSubscriptionRequestDTO) (*entity.TenantSubscription, error) {
	if req == nil {
		return nil, nil
	}

	// 创建订阅周期值对象
	period, err := valueobject.NewTenantSubscriptionPeriod(req.StartDate, req.EndDate)
	if err != nil {
		return nil, err
	}

	// 创建定价值对象
	pricing, err := valueobject.NewTenantPricing(req.Price, req.Currency, req.BillingCycle)
	if err != nil {
		return nil, err
	}

	// 创建订阅实体
	subscription, err := entity.NewTenantSubscription(
		req.TenantID,
		req.PlanID,
		req.PlanName,
		period,
		pricing,
	)
	if err != nil {
		return nil, err
	}

	// 设置限制
	err = subscription.UpdateQuotaLimits(
		req.UserLimit,
		req.StorageLimit,
		req.APILimit,
		req.ProductLimit,
		req.OrderLimit,
	)
	if err != nil {
		return nil, err
	}

	return subscription, nil
}

// FilterRequestToFilter 将过滤请求DTO转换为仓储过滤器
func (a *TenantAssembler) FilterRequestToFilter(req *dto.TenantFilterRequestDTO) map[string]interface{} {
	if req == nil {
		return nil
	}

	filter := make(map[string]interface{})

	if req.Name != "" {
		filter["name"] = req.Name
	}
	if req.Domain != "" {
		filter["domain"] = req.Domain
	}
	if req.Type != "" {
		filter["type"] = req.Type
	}
	if req.Status != "" {
		filter["status"] = req.Status
	}
	if req.Industry != "" {
		filter["industry"] = req.Industry
	}
	if req.Country != "" {
		filter["country"] = req.Country
	}
	if req.Province != "" {
		filter["province"] = req.Province
	}
	if req.City != "" {
		filter["city"] = req.City
	}
	if req.CreatedFrom != nil {
		filter["created_from"] = req.CreatedFrom
	}
	if req.CreatedTo != nil {
		filter["created_to"] = req.CreatedTo
	}
	if req.UpdatedFrom != nil {
		filter["updated_from"] = req.UpdatedFrom
	}
	if req.UpdatedTo != nil {
		filter["updated_to"] = req.UpdatedTo
	}
	if req.Keywords != "" {
		filter["keywords"] = req.Keywords
	}

	return filter
}

// SubscriptionFilterRequestToFilter 将订阅过滤请求DTO转换为仓储过滤器
func (a *TenantAssembler) SubscriptionFilterRequestToFilter(req *dto.SubscriptionFilterRequestDTO) map[string]interface{} {
	if req == nil {
		return nil
	}

	filter := make(map[string]interface{})

	if req.TenantID != "" {
		filter["tenant_id"] = req.TenantID
	}
	if req.PlanID != "" {
		filter["plan_id"] = req.PlanID
	}
	if req.PlanName != "" {
		filter["plan_name"] = req.PlanName
	}
	if req.Status != "" {
		filter["status"] = req.Status
	}
	if req.IsTrialPeriod != nil {
		filter["is_trial_period"] = *req.IsTrialPeriod
	}
	if req.AutoRenewal != nil {
		filter["auto_renewal"] = *req.AutoRenewal
	}
	if req.Currency != "" {
		filter["currency"] = req.Currency
	}
	if req.BillingCycle != "" {
		filter["billing_cycle"] = req.BillingCycle
	}
	if req.ExpiringInDays > 0 {
		filter["expiring_in_days"] = req.ExpiringInDays
	}
	if req.CreatedFrom != nil {
		filter["created_from"] = req.CreatedFrom
	}
	if req.CreatedTo != nil {
		filter["created_to"] = req.CreatedTo
	}

	return filter
}

// PaginationRequestToPagination 将分页请求DTO转换为分页参数
func (a *TenantAssembler) PaginationRequestToPagination(req *dto.PaginationRequestDTO) map[string]interface{} {
	if req == nil {
		return map[string]interface{}{
			"page":      1,
			"page_size": 10,
			"sort_by":   "created_at",
			"sort_desc": true,
		}
	}

	pagination := make(map[string]interface{})

	page := req.Page
	if page <= 0 {
		page = 1
	}
	pagination["page"] = page

	pageSize := req.PageSize
	if pageSize <= 0 {
		pageSize = 10
	} else if pageSize > 100 {
		pageSize = 100
	}
	pagination["page_size"] = pageSize

	sortBy := req.SortBy
	if sortBy == "" {
		sortBy = "created_at"
	}
	pagination["sort_by"] = sortBy
	pagination["sort_desc"] = req.SortDesc

	return pagination
}

// BuildTenantListDTO 构建租户列表DTO
func (a *TenantAssembler) BuildTenantListDTO(tenants []*entity.Tenant, total int64, page, pageSize int) *dto.TenantListDTO {
	dtoPointers := a.EntitiesToDTOs(tenants)

	// 转换为值切片
	items := make([]dto.TenantDTO, len(dtoPointers))
	for i, dto := range dtoPointers {
		if dto != nil {
			items[i] = *dto
		}
	}

	totalPages := int((total + int64(pageSize) - 1) / int64(pageSize))
	hasNext := page < totalPages
	hasPrevious := page > 1

	return &dto.TenantListDTO{
		Items:       items,
		Total:       total,
		Page:        page,
		PageSize:    pageSize,
		TotalPages:  totalPages,
		HasNext:     hasNext,
		HasPrevious: hasPrevious,
	}
}

// BuildSubscriptionListDTO 构建订阅列表DTO
func (a *TenantAssembler) BuildSubscriptionListDTO(subscriptions []*entity.TenantSubscription, total int64, page, pageSize int) *dto.SubscriptionListDTO {
	dtoPointers := a.SubscriptionEntitiesToDTOs(subscriptions)

	// 转换为值切片
	items := make([]dto.TenantSubscriptionDTO, len(dtoPointers))
	for i, dto := range dtoPointers {
		if dto != nil {
			items[i] = *dto
		}
	}

	totalPages := int((total + int64(pageSize) - 1) / int64(pageSize))
	hasNext := page < totalPages
	hasPrevious := page > 1

	return &dto.SubscriptionListDTO{
		Items:       items,
		Total:       total,
		Page:        page,
		PageSize:    pageSize,
		TotalPages:  totalPages,
		HasNext:     hasNext,
		HasPrevious: hasPrevious,
	}
}
