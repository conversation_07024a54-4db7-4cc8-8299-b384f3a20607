package assembler

import (
	"backend/internal/application/command/model"
	"backend/internal/application/dto"
	queryModel "backend/internal/application/query/model"
	"backend/internal/domain/user/entity"
	"backend/internal/domain/user/service"
	"backend/internal/domain/user/valueobject"
)

// ==================== 用户Assembler接口 ====================

// UserAssembler 用户数据转换器接口
type UserAssembler interface {
	// DTO到领域对象转换
	ToCreateUserCommand(dto *dto.CreateUserRequestDTO) *service.CreateUserCommand
	ToUpdateUserCommand(dto *dto.UpdateUserRequestDTO) *model.UpdateUserCommand
	ToUserSearchCriteria(dto *dto.UserSearchRequestDTO) *service.UserSearchCriteria
	ToUserListQuery(dto *dto.UserListRequestDTO) *queryModel.ListUsersQuery
	ToUserActivityQuery(dto *dto.UserActivityRequestDTO) *queryModel.GetUserActivityQuery

	// 领域对象到DTO转换
	ToUserDTO(user *entity.User) *dto.UserDTO
	ToUserListResponseDTO(users []*entity.User, total int64, page, pageSize int) *dto.UserListResponseDTO
	ToUserStatisticsResponseDTO(stats *service.UserStatistics) *dto.UserStatisticsResponseDTO
	ToUserActivityResponseDTO(activity *service.UserActivitySummary) *dto.UserActivityResponseDTO

	// 查询结果到DTO转换
	FromUserQueryResult(result *queryModel.UserQueryResult) *dto.UserDTO
	FromUserListResult(result *queryModel.UserListResult) *dto.UserListResponseDTO
	FromUserStatisticsResult(result *queryModel.UserStatisticsResult) *dto.UserStatisticsResponseDTO
	FromUserActivityResult(result *queryModel.UserActivityResult) *dto.UserActivityResponseDTO
}

// ==================== 用户Assembler实现 ====================

// UserAssemblerImpl 用户数据转换器实现
type UserAssemblerImpl struct{}

// NewUserAssembler 创建用户数据转换器
func NewUserAssembler() UserAssembler {
	return &UserAssemblerImpl{}
}

// ==================== DTO到领域对象转换 ====================

// ToCreateUserCommand 转换创建用户请求DTO为命令
func (a *UserAssemblerImpl) ToCreateUserCommand(dto *dto.CreateUserRequestDTO) *service.CreateUserCommand {
	return &service.CreateUserCommand{
		Username: dto.Username,
		Email:    dto.Email,
		Phone:    dto.Phone,
		Profile: valueobject.UserProfile{
			Avatar:    dto.Profile.Avatar,
			Nickname:  dto.Profile.Nickname,
			FirstName: dto.Profile.FirstName,
			LastName:  dto.Profile.LastName,
			Language:  dto.Profile.Language,
			Timezone:  dto.Profile.Timezone,
		},
		TenantID:  dto.TenantID,
		RoleID:    dto.RoleID,
		CreatedBy: "", // TODO: 从上下文获取
	}
}

// ToUpdateUserCommand 转换更新用户请求DTO为命令
func (a *UserAssemblerImpl) ToUpdateUserCommand(dto *dto.UpdateUserRequestDTO) *model.UpdateUserCommand {
	cmd := &model.UpdateUserCommand{
		UserID:   dto.UserID,
		TenantID: dto.TenantID,
	}

	if dto.Profile != nil {
		cmd.Profile = &model.UpdateProfileData{
			Nickname:  dto.Profile.Nickname,
			FirstName: dto.Profile.FirstName,
			LastName:  dto.Profile.LastName,
			Avatar:    dto.Profile.Avatar,
		}
	}

	if dto.ContactInfo != nil {
		cmd.ContactInfo = &model.UpdateContactInfoData{
			Email: dto.ContactInfo.Email,
			Phone: dto.ContactInfo.Phone,
		}
	}

	if dto.Preferences != nil {
		cmd.Preferences = &model.UpdatePreferencesData{
			Language: dto.Preferences.Language,
			Timezone: dto.Preferences.Timezone,
		}
	}

	return cmd
}

// ToUserSearchCriteria 转换搜索请求DTO为搜索条件
func (a *UserAssemblerImpl) ToUserSearchCriteria(dto *dto.UserSearchRequestDTO) *service.UserSearchCriteria {
	return &service.UserSearchCriteria{
		TenantID:    dto.TenantID,
		Keyword:     dto.Keyword,
		Email:       dto.Email,
		Phone:       dto.Phone,
		CreatedFrom: dto.CreatedFrom,
		CreatedTo:   dto.CreatedTo,
		Page:        dto.Page,
		PageSize:    dto.PageSize,
		SortBy:      dto.SortBy,
		SortDesc:    dto.SortDesc,
	}
}

// ToUserListQuery 转换列表请求DTO为查询
func (a *UserAssemblerImpl) ToUserListQuery(dto *dto.UserListRequestDTO) *queryModel.ListUsersQuery {
	return &queryModel.ListUsersQuery{
		TenantID: dto.TenantID,
		Keyword:  dto.Keyword,
		Status:   dto.Status,
		Page:     dto.Page,
		PageSize: dto.PageSize,
		SortBy:   dto.SortBy,
		SortDesc: dto.SortDesc,
	}
}

// ToUserActivityQuery 转换活动请求DTO为查询
func (a *UserAssemblerImpl) ToUserActivityQuery(dto *dto.UserActivityRequestDTO) *queryModel.GetUserActivityQuery {
	return &queryModel.GetUserActivityQuery{
		UserID:    dto.UserID,
		TenantID:  dto.TenantID,
		StartTime: dto.StartTime,
		EndTime:   dto.EndTime,
	}
}

// ==================== 领域对象到DTO转换 ====================

// ToUserDTO 转换用户实体为DTO
func (a *UserAssemblerImpl) ToUserDTO(user *entity.User) *dto.UserDTO {
	if user == nil {
		return nil
	}

	userDTO := &dto.UserDTO{
		ID:         user.BusinessID, // 使用BusinessID作为对外ID
		BusinessID: user.BusinessID,
		Username:   user.Username,
		Email:      user.Email,
		Phone:      user.Phone,
		Status:     a.convertUserStatus(user.Status),
		Profile: dto.UserProfileDTO{
			Avatar:    user.Profile.Avatar,
			Nickname:  user.Profile.Nickname,
			FirstName: user.Profile.FirstName,
			LastName:  user.Profile.LastName,
			Language:  user.Profile.Language,
			Timezone:  user.Profile.Timezone,
		},
		CreatedAt: user.CreatedAt,
		UpdatedAt: user.UpdatedAt,
		Version:   user.Version,
	}

	// 转换租户关联
	if len(user.Tenants) > 0 {
		userDTO.Tenants = make([]dto.UserTenantDTO, len(user.Tenants))
		for i, tenant := range user.Tenants {
			userDTO.Tenants[i] = dto.UserTenantDTO{
				TenantID: tenant.TenantBusinessID,
				RoleID:   tenant.RoleBusinessID,
				Status:   a.convertUserTenantStatus(tenant.Status),
				JoinedAt: tenant.CreatedAt,
			}
		}
	}

	return userDTO
}

// ToUserListResponseDTO 转换用户列表为响应DTO
func (a *UserAssemblerImpl) ToUserListResponseDTO(users []*entity.User, total int64, page, pageSize int) *dto.UserListResponseDTO {
	userDTOs := make([]dto.UserDTO, len(users))
	for i, user := range users {
		if userDTO := a.ToUserDTO(user); userDTO != nil {
			userDTOs[i] = *userDTO
		}
	}

	return &dto.UserListResponseDTO{
		Users:      userDTOs,
		Total:      total,
		Page:       page,
		PageSize:   pageSize,
		TotalPages: int((total + int64(pageSize) - 1) / int64(pageSize)),
	}
}

// ToUserStatisticsResponseDTO 转换用户统计为响应DTO
func (a *UserAssemblerImpl) ToUserStatisticsResponseDTO(stats *service.UserStatistics) *dto.UserStatisticsResponseDTO {
	if stats == nil {
		return nil
	}

	return &dto.UserStatisticsResponseDTO{
		TotalUsers:        stats.TotalUsers,
		ActiveUsers:       stats.ActiveUsers,
		InactiveUsers:     stats.InactiveUsers,
		SuspendedUsers:    stats.SuspendedUsers,
		BannedUsers:       stats.BannedUsers,
		StatusBreakdown:   a.convertStatusBreakdown(stats.StatusBreakdown),
		NewUsersToday:     stats.NewUsersToday,
		NewUsersThisWeek:  stats.NewUsersThisWeek,
		NewUsersThisMonth: stats.NewUsersThisMonth,
		LastUpdated:       stats.LastUpdated,
	}
}

// ToUserActivityResponseDTO 转换用户活动为响应DTO
func (a *UserAssemblerImpl) ToUserActivityResponseDTO(activity *service.UserActivitySummary) *dto.UserActivityResponseDTO {
	if activity == nil {
		return nil
	}

	return &dto.UserActivityResponseDTO{
		UserID:          activity.UserID,
		LoginCount:      activity.LoginCount,
		LastLoginTime:   activity.LastLoginTime,
		SessionDuration: activity.SessionDuration,
		ActiveDays:      activity.ActiveDays,
		ActionsCount:    activity.ActionsCount,
		StartTime:       activity.Period.StartTime,
		EndTime:         activity.Period.EndTime,
	}
}

// ==================== 查询结果到DTO转换 ====================

// FromUserQueryResult 从查询结果转换为用户DTO
func (a *UserAssemblerImpl) FromUserQueryResult(result *queryModel.UserQueryResult) *dto.UserDTO {
	if result == nil {
		return nil
	}

	userDTO := &dto.UserDTO{
		ID:         result.UserID,
		BusinessID: result.BusinessID,
		Username:   result.Username,
		Email:      result.Email,
		Phone:      result.Phone,
		Status:     result.Status,
		Profile: dto.UserProfileDTO{
			Avatar:    result.Profile.Avatar,
			Nickname:  result.Profile.Nickname,
			FirstName: result.Profile.FirstName,
			LastName:  result.Profile.LastName,
			Language:  result.Profile.Language,
			Timezone:  result.Profile.Timezone,
		},
		Permissions: result.Permissions,
		Roles:       result.Roles,
		Metadata:    result.Metadata,
		CreatedAt:   result.CreatedAt,
		UpdatedAt:   result.UpdatedAt,
		Version:     result.Version,
	}

	// 转换租户关联
	if len(result.Tenants) > 0 {
		userDTO.Tenants = make([]dto.UserTenantDTO, len(result.Tenants))
		for i, tenant := range result.Tenants {
			userDTO.Tenants[i] = dto.UserTenantDTO{
				TenantID:   tenant.TenantID,
				TenantName: tenant.TenantName,
				RoleID:     tenant.RoleID,
				RoleName:   tenant.RoleName,
				Status:     tenant.Status,
				JoinedAt:   tenant.JoinedAt,
			}
		}
	}

	return userDTO
}

// FromUserListResult 从列表查询结果转换为响应DTO
func (a *UserAssemblerImpl) FromUserListResult(result *queryModel.UserListResult) *dto.UserListResponseDTO {
	if result == nil {
		return nil
	}

	userDTOs := make([]dto.UserDTO, len(result.Users))
	for i, user := range result.Users {
		if userDTO := a.FromUserQueryResult(&user); userDTO != nil {
			userDTOs[i] = *userDTO
		}
	}

	return &dto.UserListResponseDTO{
		Users:      userDTOs,
		Total:      result.Total,
		Page:       result.Page,
		PageSize:   result.PageSize,
		TotalPages: result.TotalPages,
	}
}

// FromUserStatisticsResult 从统计查询结果转换为响应DTO
func (a *UserAssemblerImpl) FromUserStatisticsResult(result *queryModel.UserStatisticsResult) *dto.UserStatisticsResponseDTO {
	if result == nil {
		return nil
	}

	return &dto.UserStatisticsResponseDTO{
		TotalUsers:        result.TotalUsers,
		ActiveUsers:       result.ActiveUsers,
		InactiveUsers:     result.InactiveUsers,
		SuspendedUsers:    result.SuspendedUsers,
		BannedUsers:       result.BannedUsers,
		StatusBreakdown:   result.StatusBreakdown,
		NewUsersToday:     result.NewUsersToday,
		NewUsersThisWeek:  result.NewUsersThisWeek,
		NewUsersThisMonth: result.NewUsersThisMonth,
		RoleDistribution:  result.RoleDistribution,
		LastUpdated:       result.LastUpdated,
	}
}

// FromUserActivityResult 从活动查询结果转换为响应DTO
func (a *UserAssemblerImpl) FromUserActivityResult(result *queryModel.UserActivityResult) *dto.UserActivityResponseDTO {
	if result == nil {
		return nil
	}

	return &dto.UserActivityResponseDTO{
		UserID:          result.UserID,
		LoginCount:      result.LoginCount,
		LastLoginTime:   result.LastLoginTime,
		SessionDuration: result.SessionDuration,
		ActiveDays:      result.ActiveDays,
		ActionsCount:    result.ActionsCount,
		StartTime:       result.StartTime,
		EndTime:         result.EndTime,
	}
}

// ==================== 辅助转换方法 ====================

// convertUserStatus 转换用户状态
func (a *UserAssemblerImpl) convertUserStatus(status entity.UserStatus) string {
	switch status {
	case entity.UserStatusPending:
		return "pending"
	case entity.UserStatusActive:
		return "active"
	case entity.UserStatusInactive:
		return "inactive"
	case entity.UserStatusSuspended:
		return "suspended"
	case entity.UserStatusBanned:
		return "banned"
	default:
		return "unknown"
	}
}

// convertUserTenantStatus 转换用户租户状态
func (a *UserAssemblerImpl) convertUserTenantStatus(status int) string {
	switch status {
	case 1:
		return "active"
	case 0:
		return "inactive"
	default:
		return "unknown"
	}
}

// convertStatusBreakdown 转换状态分布
func (a *UserAssemblerImpl) convertStatusBreakdown(breakdown map[entity.UserStatus]int64) map[string]int64 {
	result := make(map[string]int64)
	for status, count := range breakdown {
		result[a.convertUserStatus(status)] = count
	}
	return result
}
