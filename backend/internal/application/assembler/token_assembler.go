package assembler

import (
	"encoding/json"
	"strconv"
	"time"

	"backend/internal/application/dto"
	"backend/internal/domain/auth/entity"
)

// TokenAssembler Token组装器
type TokenAssembler struct{}

// NewTokenAssembler 创建Token组装器
func NewTokenAssembler() *TokenAssembler {
	return &TokenAssembler{}
}

// === Token相关转换 ===

// ToTokenDTO 实体转换为DTO
func (a *TokenAssembler) ToTokenDTO(token *entity.TokenStorage) *dto.TokenDTO {
	if token == nil {
		return nil
	}

	return &dto.TokenDTO{
		ID:        strconv.FormatInt(token.ID, 10),
		TokenID:   token.TokenID,
		TokenType: string(token.TokenType),
		Status:    string(token.Status),
		UserID:    token.UserID,
		TenantID:  token.TenantID,
		SessionID: token.SessionID,
		DeviceID:  token.DeviceID,
		IssuedAt:  token.IssuedAt,
		ExpiresAt: token.ExpiresAt,
		ClientIP:  token.ClientIP,
		UserAgent: token.UserAgent,
		Scope:     token.Scope,
	}
}

// ToTokenDTOList 实体列表转换为DTO列表
func (a *TokenAssembler) ToTokenDTOList(tokens []*entity.TokenStorage) []*dto.TokenDTO {
	if tokens == nil {
		return nil
	}

	result := make([]*dto.TokenDTO, len(tokens))
	for i, token := range tokens {
		result[i] = a.ToTokenDTO(token)
	}
	return result
}

// ToTokenPairDTO Token对转换为DTO
func (a *TokenAssembler) ToTokenPairDTO(accessToken, refreshToken *entity.TokenStorage) *dto.TokenPairDTO {
	if accessToken == nil {
		return nil
	}

	result := &dto.TokenPairDTO{
		AccessToken: a.ToTokenDTO(accessToken),
		TokenType:   "Bearer",
	}

	if refreshToken != nil {
		result.RefreshToken = a.ToTokenDTO(refreshToken)
		// result.ExpiresIn = int64(accessToken.ExpiresAt.Sub(time.Now()).Seconds())
		result.ExpiresIn = int64(time.Until(accessToken.ExpiresAt).Seconds())
	}

	return result
}

// === Session相关转换 ===

// ToSessionDTO 实体转换为DTO
func (a *TokenAssembler) ToSessionDTO(session *entity.UserSession) *dto.SessionDTO {
	if session == nil {
		return nil
	}

	return &dto.SessionDTO{
		ID:           strconv.FormatInt(session.ID, 10),
		SessionID:    session.SessionID,
		UserID:       session.UserID,
		TenantID:     session.TenantID,
		DeviceID:     session.DeviceID,
		Status:       string(session.Status),
		CreatedAt:    session.CreatedAt,
		ExpiresAt:    session.ExpiresAt,
		LastActiveAt: session.LastActiveAt,
		ClientIP:     session.ClientIP,
		UserAgent:    session.UserAgent,
		DeviceType:   session.DeviceType,
		DeviceName:   session.DeviceName,
		LoginMethod:  session.LoginMethod,
		IsSecure:     session.IsSecure,
	}
}

// ToSessionDTOList 实体列表转换为DTO列表
func (a *TokenAssembler) ToSessionDTOList(sessions []*entity.UserSession) []*dto.SessionDTO {
	if sessions == nil {
		return nil
	}

	result := make([]*dto.SessionDTO, len(sessions))
	for i, session := range sessions {
		result[i] = a.ToSessionDTO(session)
	}
	return result
}

// FromCreateSessionRequest 请求转换为实体
func (a *TokenAssembler) FromCreateSessionRequest(req *dto.CreateSessionRequest, sessionID string) *entity.UserSession {
	if req == nil {
		return nil
	}

	expiresAt := time.Now().Add(24 * time.Hour) // 默认24小时
	if req.ExpiresIn > 0 {
		expiresAt = time.Now().Add(time.Duration(req.ExpiresIn) * time.Second)
	}

	session := entity.NewUserSession(sessionID, req.UserID, req.TenantID, req.DeviceID, expiresAt)
	session.DeviceType = req.DeviceType
	session.DeviceName = req.DeviceName
	session.LoginMethod = req.LoginMethod
	session.ClientIP = req.ClientIP
	session.UserAgent = req.UserAgent
	session.IsSecure = req.IsSecure

	if req.Metadata != nil {
		metadata, _ := json.Marshal(req.Metadata)
		session.Metadata = string(metadata)
	}

	return session
}

// === PreAuth相关转换 ===

// ToPreAuthContextDTO 实体转换为DTO
func (a *TokenAssembler) ToPreAuthContextDTO(context *entity.PreAuthContext) *dto.PreAuthContextDTO {
	if context == nil {
		return nil
	}

	// 解析步骤数据
	var requiredSteps []string
	var completedSteps []string

	if context.RequiredSteps != "" {
		json.Unmarshal([]byte(context.RequiredSteps), &requiredSteps)
	}
	if context.CompletedSteps != "" {
		json.Unmarshal([]byte(context.CompletedSteps), &completedSteps)
	}

	return &dto.PreAuthContextDTO{
		ID:               strconv.FormatInt(context.ID, 10),
		ContextID:        context.ContextID,
		UserID:           context.UserID,
		TenantID:         context.TenantID,
		CurrentStep:      string(context.CurrentStep),
		RequiredSteps:    requiredSteps,
		CompletedSteps:   completedSteps,
		CreatedAt:        context.CreatedAt,
		ExpiresAt:        context.ExpiresAt,
		AttemptCount:     context.AttemptCount,
		MaxAttempts:      context.MaxAttempts,
		VerificationData: context.VerificationData,
		ClientIP:         context.ClientIP,
		UserAgent:        context.UserAgent,
		DeviceID:         context.DeviceID,
	}
}

// ToPreAuthTokenDTO 前置认证Token转换为DTO
func (a *TokenAssembler) ToPreAuthTokenDTO(contextID string, token *entity.TokenStorage, requiredSteps []entity.AuthStep) *dto.PreAuthTokenDTO {
	if token == nil {
		return nil
	}

	// 转换步骤枚举为字符串
	stepStrings := make([]string, len(requiredSteps))
	for i, step := range requiredSteps {
		stepStrings[i] = string(step)
	}

	return &dto.PreAuthTokenDTO{
		ContextID:     contextID,
		Token:         *a.ToTokenDTO(token),
		RequiredSteps: stepStrings,
		// ExpiresIn:     int64(token.ExpiresAt.Sub(time.Now()).Seconds()),
		ExpiresIn: int64(time.Until(token.ExpiresAt).Seconds()),
	}
}

// FromCreatePreAuthRequest 请求转换为实体
func (a *TokenAssembler) FromCreatePreAuthRequest(req *dto.CreatePreAuthRequest, contextID string) *entity.PreAuthContext {
	if req == nil {
		return nil
	}

	// 转换步骤字符串为枚举
	requiredSteps := make([]entity.AuthStep, len(req.RequiredSteps))
	for i, step := range req.RequiredSteps {
		requiredSteps[i] = entity.AuthStep(step)
	}

	expiresAt := time.Now().Add(15 * time.Minute) // 默认15分钟
	if req.ExpiresIn > 0 {
		expiresAt = time.Now().Add(time.Duration(req.ExpiresIn) * time.Second)
	}

	context := entity.NewPreAuthContext(contextID, req.UserID, req.TenantID, requiredSteps, expiresAt)
	context.ClientIP = req.ClientIP
	context.UserAgent = req.UserAgent
	context.DeviceID = req.DeviceID

	return context
}

// === Blacklist相关转换 ===

// ToBlacklistEntryDTO 实体转换为DTO
func (a *TokenAssembler) ToBlacklistEntryDTO(entry *entity.TokenBlacklistEntry) *dto.BlacklistEntryDTO {
	if entry == nil {
		return nil
	}

	return &dto.BlacklistEntryDTO{
		ID:            strconv.FormatInt(entry.ID, 10),
		TokenID:       entry.TokenID,
		TokenType:     string(entry.TokenType),
		UserID:        entry.UserID,
		TenantID:      entry.TenantID,
		SessionID:     entry.SessionID,
		BlacklistedAt: entry.BlacklistedAt,
		ExpiresAt:     entry.ExpiresAt,
		Reason:        entry.Reason,
	}
}

// ToBlacklistEntryDTOList 实体列表转换为DTO列表
func (a *TokenAssembler) ToBlacklistEntryDTOList(entries []*entity.TokenBlacklistEntry) []*dto.BlacklistEntryDTO {
	if entries == nil {
		return nil
	}

	result := make([]*dto.BlacklistEntryDTO, len(entries))
	for i, entry := range entries {
		result[i] = a.ToBlacklistEntryDTO(entry)
	}
	return result
}

// === 查询转换 ===

// FromTokenQueryRequest 查询请求转换
func (a *TokenAssembler) FromTokenQueryRequest(req *dto.TokenQueryRequest) map[string]interface{} {
	if req == nil {
		return nil
	}

	query := make(map[string]interface{})

	if req.UserID != "" {
		query["user_id"] = req.UserID
	}
	if req.TenantID != "" {
		query["tenant_id"] = req.TenantID
	}
	if req.SessionID != "" {
		query["session_id"] = req.SessionID
	}
	if req.DeviceID != "" {
		query["device_id"] = req.DeviceID
	}
	if req.TokenType != "" {
		query["token_type"] = req.TokenType
	}
	if req.Status != "" {
		query["status"] = req.Status
	}
	if req.StartTime != "" {
		if startTime, err := time.Parse(time.RFC3339, req.StartTime); err == nil {
			query["start_time"] = startTime
		}
	}
	if req.EndTime != "" {
		if endTime, err := time.Parse(time.RFC3339, req.EndTime); err == nil {
			query["end_time"] = endTime
		}
	}

	// 分页参数
	page := req.Page
	if page <= 0 {
		page = 1
	}
	pageSize := req.PageSize
	if pageSize <= 0 {
		pageSize = 20
	}
	query["page"] = page
	query["page_size"] = pageSize
	query["offset"] = (page - 1) * pageSize

	return query
}

// FromSessionQueryRequest 会话查询请求转换
func (a *TokenAssembler) FromSessionQueryRequest(req *dto.SessionQueryRequest) map[string]interface{} {
	if req == nil {
		return nil
	}

	query := make(map[string]interface{})

	if req.UserID != "" {
		query["user_id"] = req.UserID
	}
	if req.TenantID != "" {
		query["tenant_id"] = req.TenantID
	}
	if req.DeviceID != "" {
		query["device_id"] = req.DeviceID
	}
	if req.Status != "" {
		query["status"] = req.Status
	}
	if req.DeviceType != "" {
		query["device_type"] = req.DeviceType
	}
	if req.StartTime != "" {
		if startTime, err := time.Parse(time.RFC3339, req.StartTime); err == nil {
			query["start_time"] = startTime
		}
	}
	if req.EndTime != "" {
		if endTime, err := time.Parse(time.RFC3339, req.EndTime); err == nil {
			query["end_time"] = endTime
		}
	}

	// 分页参数
	page := req.Page
	if page <= 0 {
		page = 1
	}
	pageSize := req.PageSize
	if pageSize <= 0 {
		pageSize = 20
	}
	query["page"] = page
	query["page_size"] = pageSize
	query["offset"] = (page - 1) * pageSize

	return query
}

// === 列表响应转换 ===

// ToTokenListResponse Token列表响应
func (a *TokenAssembler) ToTokenListResponse(tokens []*entity.TokenStorage, totalCount int64, page, pageSize int) *dto.TokenListResponse {
	tokenDTOs := a.ToTokenDTOList(tokens)
	totalPages := int((totalCount + int64(pageSize) - 1) / int64(pageSize))

	return &dto.TokenListResponse{
		Tokens:     tokenDTOs,
		TotalCount: totalCount,
		Page:       page,
		PageSize:   pageSize,
		TotalPages: totalPages,
	}
}

// ToSessionListResponse 会话列表响应
func (a *TokenAssembler) ToSessionListResponse(sessions []*entity.UserSession, totalCount int64, page, pageSize int) *dto.SessionListResponse {
	sessionDTOs := a.ToSessionDTOList(sessions)
	totalPages := int((totalCount + int64(pageSize) - 1) / int64(pageSize))

	return &dto.SessionListResponse{
		Sessions:   sessionDTOs,
		TotalCount: totalCount,
		Page:       page,
		PageSize:   pageSize,
		TotalPages: totalPages,
	}
}

// === 验证响应转换 ===

// ToValidateTokenResponse 验证Token响应
func (a *TokenAssembler) ToValidateTokenResponse(valid bool, token *entity.TokenStorage, session *entity.UserSession, reason string) *dto.ValidateTokenResponse {
	response := &dto.ValidateTokenResponse{
		Valid:  valid,
		Reason: reason,
	}

	if token != nil {
		response.Token = a.ToTokenDTO(token)
		response.ExpiresAt = token.ExpiresAt
	}

	if session != nil {
		response.Session = a.ToSessionDTO(session)
	}

	return response
}

// === 操作结果转换 ===

// ToOperationResult 操作结果
func (a *TokenAssembler) ToOperationResult(success bool, message string, data interface{}) *dto.OperationResult {
	return &dto.OperationResult{
		Success: success,
		Message: message,
		Data:    data,
	}
}

// ToCleanupResult 清理结果
func (a *TokenAssembler) ToCleanupResult(expiredTokens, expiredSessions, expiredBlacklist, expiredPreAuth int64) *dto.CleanupResult {
	return &dto.CleanupResult{
		ExpiredTokens:    expiredTokens,
		ExpiredSessions:  expiredSessions,
		ExpiredBlacklist: expiredBlacklist,
		ExpiredPreAuth:   expiredPreAuth,
		TotalCleaned:     expiredTokens + expiredSessions + expiredBlacklist + expiredPreAuth,
	}
}
