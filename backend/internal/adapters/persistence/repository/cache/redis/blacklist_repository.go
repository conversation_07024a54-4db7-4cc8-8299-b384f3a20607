package redis

import (
	"context"
	"encoding/json"
	"fmt"
	"time"

	"gorm.io/gorm"

	"backend/internal/domain/auth/entity"
	"backend/internal/domain/auth/repository"
	apperrors "backend/pkg/common/errors"
	"backend/pkg/common/errors/codes"
	"backend/pkg/infrastructure/cache"
)

// BlacklistRepository Redis实现的Token黑名单仓储 - 简化版本
type BlacklistRepository struct {
	cache cache.AdvancedCache
}

// NewBlacklistRepository 创建Redis黑名单仓储
func NewBlacklistRepository(cache cache.AdvancedCache) repository.BlacklistRepository {
	return &BlacklistRepository{cache: cache}
}

// === 基础CRUD操作 ===

// Save 保存Token黑名单条目到Redis
func (r *BlacklistRepository) Save(ctx context.Context, entry *entity.TokenBlacklistEntry) error {
	key := fmt.Sprintf("blacklist:%s", entry.TokenID)

	data, err := json.Marshal(entry)
	if err != nil {
		return fmt.Errorf("黑名单条目序列化失败: %w", err)
	}

	ttl := time.Until(entry.ExpiresAt)
	if ttl <= 0 {
		ttl = 24 * time.Hour // 默认24小时
	}

	return r.cache.Set(ctx, key, data, ttl)
}

// Update 更新Token黑名单条目
func (r *BlacklistRepository) Update(ctx context.Context, entry *entity.TokenBlacklistEntry) error {
	return r.Save(ctx, entry)
}

// Delete 删除Token黑名单条目
func (r *BlacklistRepository) Delete(ctx context.Context, tokenID string) error {
	key := fmt.Sprintf("blacklist:%s", tokenID)
	return r.cache.Del(ctx, key)
}

// SaveWithTx Redis不支持GORM事务，直接调用Save
func (r *BlacklistRepository) SaveWithTx(ctx context.Context, tx *gorm.DB, entry *entity.TokenBlacklistEntry) error {
	return r.Save(ctx, entry)
}

// === 查询操作 ===

// FindByTokenID 通过TokenID查找黑名单条目
func (r *BlacklistRepository) FindByTokenID(ctx context.Context, tokenID string) (*entity.TokenBlacklistEntry, error) {
	key := fmt.Sprintf("blacklist:%s", tokenID)

	data, err := r.cache.Get(ctx, key)
	if err != nil {
		return nil, apperrors.NewInternal(codes.CacheNotFound, "缓存获取失败").Wrap(err).Build()
	}
	if len(data) == 0 {
		return nil, apperrors.NewInternal(codes.CacheInvalid, "Token黑名单条目").Wrap(err).Build()
	}

	var entry entity.TokenBlacklistEntry
	if err := json.Unmarshal(data, &entry); err != nil {
		return nil, fmt.Errorf("黑名单条目反序列化失败: %w", err)
	}

	return &entry, nil
}

// FindByTokenHash 通过Token哈希查找黑名单条目
func (r *BlacklistRepository) FindByTokenHash(ctx context.Context, tokenHash string) (*entity.TokenBlacklistEntry, error) {
	// 简化实现：通过哈希索引查找
	hashKey := fmt.Sprintf("blacklist_hash:%s", tokenHash)

	tokenIDData, err := r.cache.Get(ctx, hashKey)
	if err != nil {
		return nil, apperrors.NewInternal(codes.CacheNotFound, "缓存获取失败").Wrap(err).Build()
	}
	if len(tokenIDData) == 0 {
		return nil, apperrors.NewInternal(codes.CacheInvalid, "Token黑名单条目").Wrap(err).Build()
	}

	var tokenID string
	if err := json.Unmarshal(tokenIDData, &tokenID); err != nil {
		return nil, fmt.Errorf("TokenID反序列化失败: %w", err)
	}

	return r.FindByTokenID(ctx, tokenID)
}

// FindByUserID 通过用户ID查找黑名单条目列表 (简化实现)
func (r *BlacklistRepository) FindByUserID(ctx context.Context, userID string) ([]*entity.TokenBlacklistEntry, error) {
	return []*entity.TokenBlacklistEntry{}, nil
}

// FindBySessionID 通过会话ID查找黑名单条目列表 (简化实现)
func (r *BlacklistRepository) FindBySessionID(ctx context.Context, sessionID string) ([]*entity.TokenBlacklistEntry, error) {
	return []*entity.TokenBlacklistEntry{}, nil
}

// === 批量操作 ===

// BatchCreate 批量创建黑名单条目
func (r *BlacklistRepository) BatchCreate(ctx context.Context, entries []*entity.TokenBlacklistEntry) error {
	for _, entry := range entries {
		if err := r.Save(ctx, entry); err != nil {
			return err
		}
	}
	return nil
}

// BatchDelete 批量删除黑名单条目
func (r *BlacklistRepository) BatchDelete(ctx context.Context, tokenIDs []string) error {
	for _, tokenID := range tokenIDs {
		r.Delete(ctx, tokenID)
	}
	return nil
}

// BlacklistTokensByUser 将用户的所有Token加入黑名单 (简化实现)
func (r *BlacklistRepository) BlacklistTokensByUser(ctx context.Context, userID string, reason string) error {
	// 设置用户级别黑名单标记
	key := fmt.Sprintf("user_blacklist:%s", userID)
	return r.cache.Set(ctx, key, []byte(reason), 24*time.Hour)
}

// BlacklistTokensBySession 将会话的所有Token加入黑名单 (简化实现)
func (r *BlacklistRepository) BlacklistTokensBySession(ctx context.Context, sessionID string, reason string) error {
	// 设置会话级别黑名单标记
	key := fmt.Sprintf("session_blacklist:%s", sessionID)
	return r.cache.Set(ctx, key, []byte(reason), 24*time.Hour)
}

// === 过期清理 ===

// DeleteExpiredEntries Redis TTL自动处理过期
func (r *BlacklistRepository) DeleteExpiredEntries(ctx context.Context, before time.Time) (int64, error) {
	return 0, nil
}

// FindExpiredEntries Redis TTL自动处理过期
func (r *BlacklistRepository) FindExpiredEntries(ctx context.Context, before time.Time, limit int) ([]*entity.TokenBlacklistEntry, error) {
	return []*entity.TokenBlacklistEntry{}, nil
}

// === 验证操作 ===

// IsTokenBlacklisted 检查Token是否在黑名单中
func (r *BlacklistRepository) IsTokenBlacklisted(ctx context.Context, tokenID string) (bool, error) {
	key := fmt.Sprintf("blacklist:%s", tokenID)
	exists, err := r.cache.Exists(ctx, key)
	if err != nil {
		return false, apperrors.NewInternal(codes.CacheNotFound, "缓存获取失败").Wrap(err).Build()
	}
	return exists, nil
}

// IsTokenHashBlacklisted 检查Token哈希是否在黑名单中
func (r *BlacklistRepository) IsTokenHashBlacklisted(ctx context.Context, tokenHash string) (bool, error) {
	hashKey := fmt.Sprintf("blacklist_hash:%s", tokenHash)
	exists, err := r.cache.Exists(ctx, hashKey)
	if err != nil {
		return false, apperrors.NewInternal(codes.CacheNotFound, "缓存获取失败").Wrap(err).Build()
	}
	return exists, nil
}

// ExistsByTokenID 检查黑名单条目是否存在
func (r *BlacklistRepository) ExistsByTokenID(ctx context.Context, tokenID string) (bool, error) {
	return r.IsTokenBlacklisted(ctx, tokenID)
}

// === 统计查询 ===

// CountByUser 统计用户的黑名单条目数量 (简化实现)
func (r *BlacklistRepository) CountByUser(ctx context.Context, userID string) (int64, error) {
	return 0, nil
}

// CountByTokenType 统计指定Token类型的黑名单条目数量 (简化实现)
func (r *BlacklistRepository) CountByTokenType(ctx context.Context, tokenType entity.TokenType) (int64, error) {
	return 0, nil
}

// CountActive 统计活跃的黑名单条目数量 (简化实现)
func (r *BlacklistRepository) CountActive(ctx context.Context) (int64, error) {
	return 0, nil
}
