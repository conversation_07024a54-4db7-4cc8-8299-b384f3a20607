package redis

import (
	"context"
	"encoding/json"
	"fmt"
	"time"

	"gorm.io/gorm"

	"backend/internal/domain/auth/entity"
	"backend/internal/domain/auth/repository"
	apperrors "backend/pkg/common/errors"
	"backend/pkg/common/errors/codes"
	"backend/pkg/infrastructure/cache"
)

// TokenRepository Redis实现的Token存储仓储 - 简化版本
type TokenRepository struct {
	cache cache.AdvancedCache
}

// NewTokenRepository 创建Redis Token仓储
func NewTokenRepository(cache cache.AdvancedCache) repository.TokenRepository {
	return &TokenRepository{cache: cache}
}

// === 基础CRUD操作 ===

// Save 保存Token到Redis
func (r *TokenRepository) Save(ctx context.Context, token *entity.TokenStorage) error {
	key := fmt.Sprintf("token:%s", token.TokenID)

	data, err := json.Marshal(token)
	if err != nil {
		return fmt.Errorf("Token序列化失败: %w", err)
	}

	ttl := time.Until(token.ExpiresAt)
	if ttl <= 0 {
		ttl = 24 * time.Hour // 默认24小时
	}

	return r.cache.Set(ctx, key, data, ttl)
}

// Update 更新Token
func (r *TokenRepository) Update(ctx context.Context, token *entity.TokenStorage) error {
	return r.Save(ctx, token)
}

// Delete 删除Token
func (r *TokenRepository) Delete(ctx context.Context, tokenID string) error {
	key := fmt.Sprintf("token:%s", tokenID)
	return r.cache.Del(ctx, key)
}

// SaveWithTx Redis不支持GORM事务，直接调用Save
func (r *TokenRepository) SaveWithTx(ctx context.Context, tx *gorm.DB, token *entity.TokenStorage) error {
	return r.Save(ctx, token)
}

// === 查询操作 ===

// FindByTokenID 通过Token ID查找Token
func (r *TokenRepository) FindByTokenID(ctx context.Context, tokenID string) (*entity.TokenStorage, error) {
	key := fmt.Sprintf("token:%s", tokenID)

	data, err := r.cache.Get(ctx, key)
	if err != nil {
		return nil, apperrors.NewInternal(codes.CacheNotFound, "缓存获取失败").Wrap(err).Build()
	}
	if len(data) == 0 {
		return nil, apperrors.NewInternal(codes.CacheNotFound, "无法获取Token").Wrap(fmt.Errorf("Token ID: %s", tokenID)).Build()
	}

	var token entity.TokenStorage
	if err := json.Unmarshal(data, &token); err != nil {
		return nil, apperrors.NewInternal(codes.Serialization, "Token反序列化失败").Wrap(err).Build()
	}

	return &token, nil
}

// FindByTokenHash 通过Token哈希查找Token (简化实现)
func (r *TokenRepository) FindByTokenHash(ctx context.Context, tokenHash string) (*entity.TokenStorage, error) {
	// 在Redis中需要维护hash到ID的映射，这里简化实现
	key := fmt.Sprintf("token_hash:%s", tokenHash)

	data, err := r.cache.Get(ctx, key)
	if err != nil {
		return nil, apperrors.NewInternal(codes.CacheNotFound, "缓存获取失败").Wrap(err).Build()
	}
	if len(data) == 0 {
		return nil, apperrors.NewInternal(codes.CacheNotFound, "无法获取Token").Wrap(fmt.Errorf("Token哈希: %s", tokenHash)).Build()
	}

	// 获取tokenID
	tokenID := string(data)
	return r.FindByTokenID(ctx, tokenID)
}

// FindByUserID 通过用户ID查找Token列表 (简化实现)
func (r *TokenRepository) FindByUserID(ctx context.Context, userID string, tokenType entity.TokenType) ([]*entity.TokenStorage, error) {
	return []*entity.TokenStorage{}, nil
}

// FindBySessionID 通过会话ID查找Token列表 (简化实现)
func (r *TokenRepository) FindBySessionID(ctx context.Context, sessionID string) ([]*entity.TokenStorage, error) {
	return []*entity.TokenStorage{}, nil
}

// FindByDeviceID 通过设备ID查找Token列表 (简化实现)
func (r *TokenRepository) FindByDeviceID(ctx context.Context, deviceID string) ([]*entity.TokenStorage, error) {
	return []*entity.TokenStorage{}, nil
}

// === 批量操作 ===

// BatchCreate 批量创建Token
func (r *TokenRepository) BatchCreate(ctx context.Context, tokens []*entity.TokenStorage) error {
	for _, token := range tokens {
		if err := r.Save(ctx, token); err != nil {
			return err
		}
	}
	return nil
}

// BatchUpdateStatus 批量更新Token状态 (简化实现)
func (r *TokenRepository) BatchUpdateStatus(ctx context.Context, tokenIDs []string, status entity.TokenStatus) error {
	for _, tokenID := range tokenIDs {
		token, err := r.FindByTokenID(ctx, tokenID)
		if err != nil {
			continue
		}
		token.Status = status
		r.Update(ctx, token)
	}
	return nil
}

// BatchDelete 批量删除Token
func (r *TokenRepository) BatchDelete(ctx context.Context, tokenIDs []string) error {
	for _, tokenID := range tokenIDs {
		r.Delete(ctx, tokenID)
	}
	return nil
}

// === 状态管理 ===

// UpdateStatus 更新Token状态
func (r *TokenRepository) UpdateStatus(ctx context.Context, tokenID string, status entity.TokenStatus) error {
	token, err := r.FindByTokenID(ctx, tokenID)
	if err != nil {
		return err
	}

	token.Status = status
	return r.Update(ctx, token)
}

// UpdateLastUsed 更新最后使用时间
func (r *TokenRepository) UpdateLastUsed(ctx context.Context, tokenID string) error {
	token, err := r.FindByTokenID(ctx, tokenID)
	if err != nil {
		return err
	}

	now := time.Now()
	token.LastUsedAt = &now
	return r.Update(ctx, token)
}

// RevokeTokensByUser 撤销用户的所有Token (简化实现)
func (r *TokenRepository) RevokeTokensByUser(ctx context.Context, userID string, tokenType entity.TokenType) error {
	// 简化实现：设置用户级别撤销标记
	key := fmt.Sprintf("user_tokens_revoked:%s:%s", userID, tokenType)
	return r.cache.Set(ctx, key, []byte("revoked"), 24*time.Hour)
}

// RevokeTokensBySession 撤销会话的所有Token (简化实现)
func (r *TokenRepository) RevokeTokensBySession(ctx context.Context, sessionID string) error {
	// 简化实现：设置会话级别撤销标记
	key := fmt.Sprintf("session_tokens_revoked:%s", sessionID)
	return r.cache.Set(ctx, key, []byte("revoked"), 24*time.Hour)
}

// RevokeTokensByDevice 撤销设备的所有Token (简化实现)
func (r *TokenRepository) RevokeTokensByDevice(ctx context.Context, deviceID string) error {
	// 简化实现：设置设备级别撤销标记
	key := fmt.Sprintf("device_tokens_revoked:%s", deviceID)
	return r.cache.Set(ctx, key, []byte("revoked"), 24*time.Hour)
}

// === 过期清理 ===

// DeleteExpiredTokens Redis TTL自动处理过期
func (r *TokenRepository) DeleteExpiredTokens(ctx context.Context, before time.Time) (int64, error) {
	return 0, nil
}

// FindExpiredTokens Redis TTL自动处理过期
func (r *TokenRepository) FindExpiredTokens(ctx context.Context, before time.Time, limit int) ([]*entity.TokenStorage, error) {
	return []*entity.TokenStorage{}, nil
}

// === 统计查询 ===

// CountByUser 统计用户的Token数量 (简化实现)
func (r *TokenRepository) CountByUser(ctx context.Context, userID string, tokenType entity.TokenType) (int64, error) {
	return 0, nil
}

// CountByUserAndStatus 统计用户指定状态的Token数量 (简化实现)
func (r *TokenRepository) CountByUserAndStatus(ctx context.Context, userID string, tokenType entity.TokenType, status entity.TokenStatus) (int64, error) {
	return 0, nil
}

// CountActiveTokensByDevice 统计设备的活跃Token数量 (简化实现)
func (r *TokenRepository) CountActiveTokensByDevice(ctx context.Context, deviceID string) (int64, error) {
	return 0, nil
}

// === 验证操作 ===

// ExistsByTokenID 检查Token是否存在
func (r *TokenRepository) ExistsByTokenID(ctx context.Context, tokenID string) (bool, error) {
	key := fmt.Sprintf("token:%s", tokenID)
	exists, err := r.cache.Exists(ctx, key)
	if err != nil {
		return false, apperrors.NewInternal(codes.CacheNotFound, "缓存获取失败").Wrap(err).Build()
	}
	return exists, nil
}

// IsTokenValid 检查Token是否有效
func (r *TokenRepository) IsTokenValid(ctx context.Context, tokenID string) (bool, error) {
	token, err := r.FindByTokenID(ctx, tokenID)
	if err != nil {
		return false, err
	}

	// 检查状态和过期时间
	if token.Status != entity.TokenStatusActive {
		return false, nil
	}

	if token.ExpiresAt.Before(time.Now()) {
		return false, nil
	}

	return true, nil
}
