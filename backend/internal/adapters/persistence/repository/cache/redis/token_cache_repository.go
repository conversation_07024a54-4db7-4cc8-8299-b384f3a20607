package redis

import (
	"context"
	"encoding/json"
	"fmt"
	"strconv"
	"time"

	"backend/internal/domain/auth/repository"
	"backend/pkg/infrastructure/cache"
)

// TokenCacheRepository Redis实现的Token缓存仓储
type TokenCacheRepository struct {
	cache cache.AdvancedCache
}

// NewTokenCacheRepository 创建Token缓存仓储
func NewTokenCacheRepository(cache cache.AdvancedCache) repository.TokenCacheRepository {
	return &TokenCacheRepository{
		cache: cache,
	}
}

// Redis键名常量
const (
	TokenInfoPrefix      = "token:info:"      // token:info:{jti}
	TokenBlacklistPrefix = "token:blacklist:" // token:blacklist:{jti}
	UserTokensPrefix     = "user:tokens:"     // user:tokens:{user_id}:{tenant_id}
	DeviceTokensPrefix   = "device:tokens:"   // device:tokens:{device_id}
)

// === Token信息管理 ===

// SaveTokenInfo 保存Token信息到Redis
func (r *TokenCacheRepository) SaveTokenInfo(ctx context.Context, tokenInfo *repository.TokenInfo) error {
	key := TokenInfoPrefix + tokenInfo.JTI

	// 序列化Token信息
	data, err := json.Marshal(tokenInfo)
	if err != nil {
		return fmt.Errorf("序列化Token信息失败: %w", err)
	}

	// 计算TTL
	ttl := time.Until(tokenInfo.ExpiresAt)
	if ttl <= 0 {
		return fmt.Errorf("Token已过期")
	}

	// 保存到Redis
	return r.cache.Set(ctx, key, data, ttl)
}

// GetTokenInfo 获取Token信息
func (r *TokenCacheRepository) GetTokenInfo(ctx context.Context, jti string) (*repository.TokenInfo, error) {
	key := TokenInfoPrefix + jti

	data, err := r.cache.Get(ctx, key)
	if err != nil {
		return nil, err
	}

	var tokenInfo repository.TokenInfo
	if err := json.Unmarshal(data, &tokenInfo); err != nil {
		return nil, fmt.Errorf("反序列化Token信息失败: %w", err)
	}

	return &tokenInfo, nil
}

// DeleteTokenInfo 删除Token信息
func (r *TokenCacheRepository) DeleteTokenInfo(ctx context.Context, jti string) error {
	key := TokenInfoPrefix + jti
	return r.cache.Del(ctx, key)
}

// === 黑名单管理 ===

// AddToBlacklist 将Token添加到黑名单
func (r *TokenCacheRepository) AddToBlacklist(ctx context.Context, jti string, expiresAt time.Time) error {
	key := TokenBlacklistPrefix + jti

	// 计算TTL，黑名单记录应该在Token过期后自动删除
	ttl := time.Until(expiresAt)
	if ttl <= 0 {
		// 如果Token已经过期，不需要加入黑名单
		return nil
	}

	// 添加到黑名单，值为撤销时间戳
	revokedAt := time.Now().Unix()
	value := []byte(strconv.FormatInt(revokedAt, 10))
	return r.cache.Set(ctx, key, value, ttl)
}

// IsTokenBlacklisted 检查Token是否在黑名单中
func (r *TokenCacheRepository) IsTokenBlacklisted(ctx context.Context, jti string) (bool, error) {
	key := TokenBlacklistPrefix + jti

	_, err := r.cache.Get(ctx, key)
	if err != nil {
		if err == cache.ErrKeyNotFound {
			return false, nil // 不在黑名单中
		}
		return false, err
	}

	return true, nil // 在黑名单中
}

// RemoveFromBlacklist 从黑名单中移除Token
func (r *TokenCacheRepository) RemoveFromBlacklist(ctx context.Context, jti string) error {
	key := TokenBlacklistPrefix + jti
	return r.cache.Del(ctx, key)
}

// === 用户Token管理 ===

// AddUserToken 为用户添加Token记录
func (r *TokenCacheRepository) AddUserToken(ctx context.Context, userID, tenantID, jti string) error {
	key := fmt.Sprintf("%s%s:%s", UserTokensPrefix, userID, tenantID)

	// 使用Redis Set添加JTI
	return r.cache.SAdd(ctx, key, jti)
}

// RemoveUserToken 移除用户的Token记录
func (r *TokenCacheRepository) RemoveUserToken(ctx context.Context, userID, tenantID, jti string) error {
	key := fmt.Sprintf("%s%s:%s", UserTokensPrefix, userID, tenantID)

	// 从Redis Set中移除JTI
	return r.cache.SRem(ctx, key, jti)
}

// GetUserTokens 获取用户的所有活跃Token
func (r *TokenCacheRepository) GetUserTokens(ctx context.Context, userID, tenantID string) ([]string, error) {
	key := fmt.Sprintf("%s%s:%s", UserTokensPrefix, userID, tenantID)

	// 获取Set中的所有成员
	return r.cache.SMembers(ctx, key)
}

// RevokeAllUserTokens 撤销用户的所有Token
func (r *TokenCacheRepository) RevokeAllUserTokens(ctx context.Context, userID, tenantID string) error {
	// 1. 获取用户的所有Token
	jtis, err := r.GetUserTokens(ctx, userID, tenantID)
	if err != nil {
		return err
	}

	// 2. 批量撤销Token
	if len(jtis) > 0 {
		if err := r.BatchRevoke(ctx, jtis); err != nil {
			return err
		}
	}

	// 3. 清空用户Token集合
	key := fmt.Sprintf("%s%s:%s", UserTokensPrefix, userID, tenantID)
	return r.cache.Del(ctx, key)
}

// === 设备Token管理 ===

// GetDeviceTokens 获取设备的所有Token
func (r *TokenCacheRepository) GetDeviceTokens(ctx context.Context, deviceID string) ([]string, error) {
	key := DeviceTokensPrefix + deviceID
	return r.cache.SMembers(ctx, key)
}

// RevokeDeviceTokens 撤销设备的所有Token
func (r *TokenCacheRepository) RevokeDeviceTokens(ctx context.Context, deviceID string) error {
	// 1. 获取设备的所有Token
	jtis, err := r.GetDeviceTokens(ctx, deviceID)
	if err != nil {
		return err
	}

	// 2. 批量撤销Token
	if len(jtis) > 0 {
		if err := r.BatchRevoke(ctx, jtis); err != nil {
			return err
		}
	}

	// 3. 清空设备Token集合
	key := DeviceTokensPrefix + deviceID
	return r.cache.Del(ctx, key)
}

// === 批量操作 ===

// BatchRevoke 批量撤销Token
func (r *TokenCacheRepository) BatchRevoke(ctx context.Context, jtis []string) error {
	for _, jti := range jtis {
		// 获取Token信息以确定过期时间
		tokenInfo, err := r.GetTokenInfo(ctx, jti)
		if err != nil {
			// 如果Token信息不存在，跳过
			continue
		}

		// 添加到黑名单
		if err := r.AddToBlacklist(ctx, jti, tokenInfo.ExpiresAt); err != nil {
			return fmt.Errorf("撤销Token %s 失败: %w", jti, err)
		}
	}

	return nil
}

// CleanupExpiredTokens 清理过期的Token信息和黑名单记录
func (r *TokenCacheRepository) CleanupExpiredTokens(ctx context.Context) error {
	// Redis的TTL机制会自动清理过期的键，这里可以实现额外的清理逻辑
	// 例如清理用户Token集合中的过期Token引用

	// TODO: 实现更复杂的清理逻辑，如扫描用户Token集合并移除已过期的JTI
	return nil
}

// === 统计信息 ===

// GetActiveTokenCount 获取活跃Token数量
func (r *TokenCacheRepository) GetActiveTokenCount(ctx context.Context, userID, tenantID string) (int64, error) {
	key := fmt.Sprintf("%s%s:%s", UserTokensPrefix, userID, tenantID)
	return r.cache.SCard(ctx, key)
}

// GetBlacklistSize 获取黑名单大小
func (r *TokenCacheRepository) GetBlacklistSize(ctx context.Context) (int64, error) {
	// 使用SCAN命令统计黑名单键的数量
	pattern := TokenBlacklistPrefix + "*"
	keys, err := r.cache.Keys(ctx, pattern)
	if err != nil {
		return 0, err
	}
	return int64(len(keys)), nil
}

// === 辅助方法 ===

// addDeviceToken 为设备添加Token记录（内部使用）
func (r *TokenCacheRepository) addDeviceToken(ctx context.Context, deviceID, jti string) error {
	if deviceID == "" {
		return nil // 如果没有设备ID，跳过
	}

	key := DeviceTokensPrefix + deviceID
	return r.cache.SAdd(ctx, key, jti)
}

// removeDeviceToken 移除设备的Token记录（内部使用）
func (r *TokenCacheRepository) removeDeviceToken(ctx context.Context, deviceID, jti string) error {
	if deviceID == "" {
		return nil
	}

	key := DeviceTokensPrefix + deviceID
	return r.cache.SRem(ctx, key, jti)
}

// === 预登录Token管理 ===

// MarkTokenAsUsed 标记Token为已使用（用于一次性token）
func (r *TokenCacheRepository) MarkTokenAsUsed(ctx context.Context, jti string) error {
	// 获取现有的Token信息
	tokenInfo, err := r.GetTokenInfo(ctx, jti)
	if err != nil {
		return fmt.Errorf("获取Token信息失败: %w", err)
	}

	// 标记为已使用
	tokenInfo.IsUsed = true

	// 重新保存
	return r.SaveTokenInfo(ctx, tokenInfo)
}

// IsTokenUsed 检查Token是否已使用
func (r *TokenCacheRepository) IsTokenUsed(ctx context.Context, jti string) (bool, error) {
	tokenInfo, err := r.GetTokenInfo(ctx, jti)
	if err != nil {
		return false, fmt.Errorf("获取Token信息失败: %w", err)
	}

	return tokenInfo.IsUsed, nil
}

// RevokeUserPreAuthTokens 撤销用户的所有预登录Token
func (r *TokenCacheRepository) RevokeUserPreAuthTokens(ctx context.Context, userID string) error {
	// 使用模式匹配查找用户的所有预登录token
	pattern := TokenInfoPrefix + "*"
	keys, err := r.cache.Keys(ctx, pattern)
	if err != nil {
		return fmt.Errorf("查找Token失败: %w", err)
	}

	for _, key := range keys {
		// 获取Token信息
		data, err := r.cache.Get(ctx, key)
		if err != nil {
			continue // 跳过获取失败的key
		}

		var tokenInfo repository.TokenInfo
		if err := json.Unmarshal([]byte(data), &tokenInfo); err != nil {
			continue // 跳过解析失败的数据
		}

		// 检查是否是该用户的预登录token
		if tokenInfo.UserID == userID && tokenInfo.TokenType == "pre-auth" {
			jti := tokenInfo.JTI
			// 添加到黑名单
			if err := r.AddToBlacklist(ctx, jti, tokenInfo.ExpiresAt); err != nil {
				return fmt.Errorf("撤销预登录Token失败: %w", err)
			}
		}
	}

	return nil
}

// ValidatePreAuthToken 验证预登录Token（检查设备和IP匹配）
func (r *TokenCacheRepository) ValidatePreAuthToken(ctx context.Context, jti, deviceID, ipAddress string) error {
	tokenInfo, err := r.GetTokenInfo(ctx, jti)
	if err != nil {
		return fmt.Errorf("获取Token信息失败: %w", err)
	}

	// 检查Token类型
	if tokenInfo.TokenType != "pre-auth" {
		return fmt.Errorf("不是预登录Token")
	}

	// 检查是否已使用
	if tokenInfo.IsUsed {
		return fmt.Errorf("预登录Token已被使用")
	}

	// 检查设备ID匹配（如果提供）
	if deviceID != "" && tokenInfo.DeviceID != "" && tokenInfo.DeviceID != deviceID {
		return fmt.Errorf("设备ID不匹配")
	}

	// 检查IP地址匹配（可选，考虑移动网络的IP变化）
	// 这里可以根据安全策略配置是否严格验证IP
	if ipAddress != "" && tokenInfo.IPAddress != "" && tokenInfo.IPAddress != ipAddress {
		// 记录警告但不阻止（可配置）
		// 在生产环境中可能需要更严格的验证
	}

	return nil
}
