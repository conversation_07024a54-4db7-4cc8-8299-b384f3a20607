package redis

import (
	"context"
	"encoding/json"
	"fmt"
	"time"

	"gorm.io/gorm"

	"backend/internal/domain/auth/entity"
	"backend/internal/domain/auth/repository"
	apperrors "backend/pkg/common/errors"
	"backend/pkg/common/errors/codes"
	"backend/pkg/infrastructure/cache"
)

// PreAuthRepository Redis实现的前置认证仓储 - 简化版本
type PreAuthRepository struct {
	cache cache.AdvancedCache
}

// NewPreAuthRepository 创建Redis前置认证仓储
func NewPreAuthRepository(cache cache.AdvancedCache) repository.PreAuthRepository {
	return &PreAuthRepository{cache: cache}
}

// === 基础CRUD操作 ===

// Save 保存前置认证上下文到Redis
func (r *PreAuthRepository) Save(ctx context.Context, context *entity.PreAuthContext) error {
	key := fmt.Sprintf("preauth:%s", context.ContextID)

	data, err := json.Marshal(context)
	if err != nil {
		return fmt.Errorf("前置认证上下文序列化失败: %w", err)
	}

	ttl := time.Until(context.ExpiresAt)
	if ttl <= 0 {
		ttl = 15 * time.Minute // 默认15分钟
	}

	return r.cache.Set(ctx, key, data, ttl)
}

// Update 更新前置认证上下文
func (r *PreAuthRepository) Update(ctx context.Context, context *entity.PreAuthContext) error {
	return r.Save(ctx, context)
}

// Delete 删除前置认证上下文
func (r *PreAuthRepository) Delete(ctx context.Context, contextID string) error {
	key := fmt.Sprintf("preauth:%s", contextID)
	return r.cache.Del(ctx, key)
}

// SaveWithTx Redis不支持GORM事务，直接调用Save
func (r *PreAuthRepository) SaveWithTx(ctx context.Context, tx *gorm.DB, context *entity.PreAuthContext) error {
	return r.Save(ctx, context)
}

// === 查询操作 ===

// FindByContextID 通过上下文ID查找前置认证上下文
func (r *PreAuthRepository) FindByContextID(ctx context.Context, contextID string) (*entity.PreAuthContext, error) {
	key := fmt.Sprintf("preauth:%s", contextID)

	data, err := r.cache.Get(ctx, key)
	if err != nil {
		return nil, apperrors.NewInternal(codes.CacheNotFound, "缓存获取失败").Wrap(err).Build()
	}
	if len(data) == 0 {
		return nil, apperrors.NewInternal(codes.CacheNotFound, "无法获取前置认证上下文").Wrap(err).Build()
	}

	var preAuthContext entity.PreAuthContext
	if err := json.Unmarshal(data, &preAuthContext); err != nil {
		return nil, apperrors.NewInternal(codes.Serialization, "前置认证上下文反序列化失败").Wrap(err).Build()
	}

	return &preAuthContext, nil
}

// FindByUserID 通过用户ID查找前置认证上下文列表 (简化实现)
func (r *PreAuthRepository) FindByUserID(ctx context.Context, userID string) ([]*entity.PreAuthContext, error) {
	return []*entity.PreAuthContext{}, nil
}

// FindActiveByUser 通过用户ID查找活跃的前置认证上下文 (简化实现)
func (r *PreAuthRepository) FindActiveByUser(ctx context.Context, userID string) (*entity.PreAuthContext, error) {
	// 简化实现：查找用户当前活跃的上下文
	activeKey := fmt.Sprintf("preauth_active:%s", userID)

	contextIDData, err := r.cache.Get(ctx, activeKey)
	if err != nil {
		return nil, apperrors.NewInternal(codes.CacheNotFound, "缓存获取失败").Wrap(err).Build()
	}
	if len(contextIDData) == 0 {
		return nil, apperrors.NewInternal(codes.CacheNotFound, "无法获取活跃前置认证上下文").Wrap(err).Build()
	}

	var contextID string
	if err := json.Unmarshal(contextIDData, &contextID); err != nil {
		return nil, apperrors.NewInternal(codes.Serialization, "ContextID反序列化失败").Wrap(err).Build()
	}

	return r.FindByContextID(ctx, contextID)
}

// FindByDeviceID 通过设备ID查找前置认证上下文列表 (简化实现)
func (r *PreAuthRepository) FindByDeviceID(ctx context.Context, deviceID string) ([]*entity.PreAuthContext, error) {
	return []*entity.PreAuthContext{}, nil
}

// === 状态管理 ===

// CompleteStep 完成认证步骤
func (r *PreAuthRepository) CompleteStep(ctx context.Context, contextID string, step entity.AuthStep) error {
	context, err := r.FindByContextID(ctx, contextID)
	if err != nil {
		return err
	}

	// 更新步骤状态
	context.CurrentStep = step
	return r.Update(ctx, context)
}

// IncrementAttempt 增加尝试次数
func (r *PreAuthRepository) IncrementAttempt(ctx context.Context, contextID string) error {
	context, err := r.FindByContextID(ctx, contextID)
	if err != nil {
		return err
	}

	context.AttemptCount++
	return r.Update(ctx, context)
}

// MarkCompleted 标记为已完成
func (r *PreAuthRepository) MarkCompleted(ctx context.Context, contextID string) error {
	context, err := r.FindByContextID(ctx, contextID)
	if err != nil {
		return err
	}

	now := time.Now()
	context.CurrentStep = entity.AuthStepCompleted
	context.CompletedAt = &now
	return r.Update(ctx, context)
}

// UpdateVerificationData 更新验证数据
func (r *PreAuthRepository) UpdateVerificationData(ctx context.Context, contextID string, data string) error {
	context, err := r.FindByContextID(ctx, contextID)
	if err != nil {
		return err
	}

	context.VerificationData = data
	return r.Update(ctx, context)
}

// === 批量操作 ===

// BatchCreate 批量创建前置认证上下文
func (r *PreAuthRepository) BatchCreate(ctx context.Context, contexts []*entity.PreAuthContext) error {
	for _, context := range contexts {
		if err := r.Save(ctx, context); err != nil {
			return err
		}
	}
	return nil
}

// BatchDelete 批量删除前置认证上下文
func (r *PreAuthRepository) BatchDelete(ctx context.Context, contextIDs []string) error {
	for _, contextID := range contextIDs {
		r.Delete(ctx, contextID)
	}
	return nil
}

// === 过期清理 ===

// DeleteExpiredContexts Redis TTL自动处理过期
func (r *PreAuthRepository) DeleteExpiredContexts(ctx context.Context, before time.Time) (int64, error) {
	return 0, nil
}

// FindExpiredContexts Redis TTL自动处理过期
func (r *PreAuthRepository) FindExpiredContexts(ctx context.Context, before time.Time, limit int) ([]*entity.PreAuthContext, error) {
	return []*entity.PreAuthContext{}, nil
}

// CleanupCompletedContexts 清理已完成的前置认证上下文
func (r *PreAuthRepository) CleanupCompletedContexts(ctx context.Context, completedBefore time.Time) (int64, error) {
	return 0, nil
}

// === 统计查询 ===

// CountByUser 统计用户的前置认证上下文数量 (简化实现)
func (r *PreAuthRepository) CountByUser(ctx context.Context, userID string) (int64, error) {
	return 0, nil
}

// CountActiveByUser 统计用户的活跃前置认证上下文数量 (简化实现)
func (r *PreAuthRepository) CountActiveByUser(ctx context.Context, userID string) (int64, error) {
	return 0, nil
}

// CountByStep 统计指定步骤的前置认证上下文数量 (简化实现)
func (r *PreAuthRepository) CountByStep(ctx context.Context, step entity.AuthStep) (int64, error) {
	return 0, nil
}

// === 验证操作 ===

// ExistsByContextID 检查前置认证上下文是否存在
func (r *PreAuthRepository) ExistsByContextID(ctx context.Context, contextID string) (bool, error) {
	key := fmt.Sprintf("preauth:%s", contextID)
	exists, err := r.cache.Exists(ctx, key)
	if err != nil {
		return false, apperrors.NewInternal(codes.CacheNotFound, "缓存获取失败").Wrap(err).Build()
	}
	return exists, nil
}

// IsContextValid 检查前置认证上下文是否有效
func (r *PreAuthRepository) IsContextValid(ctx context.Context, contextID string) (bool, error) {
	context, err := r.FindByContextID(ctx, contextID)
	if err != nil {
		return false, nil
	}

	// 检查是否过期
	if time.Now().After(context.ExpiresAt) {
		return false, nil
	}

	// 检查是否已完成
	if context.CurrentStep == entity.AuthStepCompleted {
		return false, nil
	}

	return true, nil
}

// CanUserAttempt 检查用户是否可以尝试认证
func (r *PreAuthRepository) CanUserAttempt(ctx context.Context, userID string) (bool, error) {
	context, err := r.FindActiveByUser(ctx, userID)
	if err != nil {
		return true, nil // 没有活跃的上下文，可以尝试
	}

	// 检查尝试次数是否超限
	return context.AttemptCount < context.MaxAttempts, nil
}
