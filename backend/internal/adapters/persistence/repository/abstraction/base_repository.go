package abstraction

import (
	"context"

	database "backend/pkg/infrastructure/database/abstraction"
)

// BaseRepository 抽象仓储基类
type BaseRepository struct {
	manager database.Manager
	context database.RepositoryContext
}

// NewBaseRepository 创建抽象仓储基类
func NewBaseRepository(manager database.Manager) *BaseRepository {
	return &BaseRepository{
		manager: manager,
		context: nil, // 默认不在事务中
	}
}

// NewBaseRepositoryWithContext 创建带上下文的抽象仓储基类
func NewBaseRepositoryWithContext(manager database.Manager, ctx database.RepositoryContext) *BaseRepository {
	return &BaseRepository{
		manager: manager,
		context: ctx,
	}
}

// GetDataAccess 获取数据访问接口
func (r *BaseRepository) GetDataAccess() database.DataAccess {
	if r.context != nil {
		return r.context.GetDataAccess()
	}
	return r.manager.GetDataAccess()
}

// GetQueryBuilder 获取查询构建器
func (r *BaseRepository) GetQueryBuilder() database.QueryBuilder {
	if r.context != nil {
		return r.context.GetQueryBuilder()
	}
	return r.manager.GetQueryBuilder()
}

// GetTransactionManager 获取事务管理器
func (r *BaseRepository) GetTransactionManager() database.TransactionManager {
	return r.manager.GetTransactionManager()
}

// WithTransaction 在事务中执行操作
func (r *BaseRepository) WithTransaction(ctx context.Context, fn func(tx database.Transaction) error) error {
	return r.GetTransactionManager().WithTransaction(ctx, fn)
}

// IsInTransaction 检查是否在事务中
func (r *BaseRepository) IsInTransaction() bool {
	return r.context != nil && r.context.IsInTransaction()
}

// GetManager 获取数据库管理器（用于特殊情况）
func (r *BaseRepository) GetManager() database.Manager {
	return r.manager
}

// SetContext 设置仓储上下文
func (r *BaseRepository) SetContext(ctx database.RepositoryContext) {
	r.context = ctx
}
