package auth

import (
	"context"
	"errors"
	"time"

	"gorm.io/gorm"

	"backend/internal/domain/auth/entity"
	"backend/internal/domain/auth/repository"
	apperrors "backend/pkg/common/errors"
	"backend/pkg/common/errors/codes"
)

// PreAuthRepository 前置认证仓储的数据库实现
type PreAuthRepository struct {
	db *gorm.DB
}

// NewPreAuthRepository 创建新的前置认证仓储实例
func NewPreAuthRepository(db *gorm.DB) repository.PreAuthRepository {
	return &PreAuthRepository{db: db}
}

// === 基础CRUD操作 ===

// Save 保存前置认证上下文
func (r *PreAuthRepository) Save(ctx context.Context, context *entity.PreAuthContext) error {
	if err := r.db.WithContext(ctx).Create(context).Error; err != nil {
		return apperrors.NewInternal(codes.DatabaseQuery, "保存前置认证上下文失败").Wrap(err).Build()
	}
	return nil
}

// Update 更新前置认证上下文
func (r *PreAuthRepository) Update(ctx context.Context, context *entity.PreAuthContext) error {
	result := r.db.WithContext(ctx).Save(context)
	if result.Error != nil {
		return apperrors.NewInternal(codes.DatabaseQuery, "更新前置认证上下文失败").Wrap(result.Error).Build()
	}
	if result.RowsAffected == 0 {
		return apperrors.NewNotFound(codes.DatabaseQuery, "前置认证上下文无变更").Build()
	}
	return nil
}

// Delete 删除前置认证上下文
func (r *PreAuthRepository) Delete(ctx context.Context, contextID string) error {
	result := r.db.WithContext(ctx).Where("context_id = ?", contextID).Delete(&entity.PreAuthContext{})
	if result.Error != nil {
		return apperrors.NewInternal(codes.DatabaseConnection, "删除前置认证上下文失败").Wrap(result.Error).Build()
	}
	if result.RowsAffected == 0 {
		return apperrors.NewNotFound(codes.DatabaseQuery, "前置认证上下文无变更").Build()
	}
	return nil
}

// SaveWithTx 在事务中保存前置认证上下文
func (r *PreAuthRepository) SaveWithTx(ctx context.Context, tx *gorm.DB, context *entity.PreAuthContext) error {
	if err := tx.WithContext(ctx).Create(context).Error; err != nil {
		return apperrors.NewInternal(codes.DatabaseTransaction, "在事务中保存前置认证上下文失败").Wrap(err).Build()
	}
	return nil
}

// === 查询操作 ===

// FindByContextID 通过上下文ID查找前置认证上下文
func (r *PreAuthRepository) FindByContextID(ctx context.Context, contextID string) (*entity.PreAuthContext, error) {
	var preAuthContext entity.PreAuthContext
	err := r.db.WithContext(ctx).Where("context_id = ?", contextID).First(&preAuthContext).Error
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, apperrors.NewNotFound(codes.DatabaseQuery, "前置认证上下文不存在").Build()
		}
		return nil, apperrors.NewInternal(codes.DatabaseQuery, "查询前置认证上下文失败").Wrap(err).Build()
	}
	return &preAuthContext, nil
}

// FindByUserID 通过用户ID查找前置认证上下文列表
func (r *PreAuthRepository) FindByUserID(ctx context.Context, userID string) ([]*entity.PreAuthContext, error) {
	var contexts []*entity.PreAuthContext
	err := r.db.WithContext(ctx).Where("user_id = ?", userID).Order("created_at DESC").Find(&contexts).Error
	if err != nil {
		return nil, apperrors.NewInternal(codes.DatabaseQuery, "查询前置认证上下文失败").Wrap(err).Build()
	}
	return contexts, nil
}

// FindActiveByUser 通过用户ID查找活跃的前置认证上下文
func (r *PreAuthRepository) FindActiveByUser(ctx context.Context, userID string) (*entity.PreAuthContext, error) {
	var context entity.PreAuthContext
	err := r.db.WithContext(ctx).
		Where("user_id = ? AND current_step != ? AND expires_at > ?",
			userID, entity.AuthStepCompleted, time.Now()).
		Order("created_at DESC").
		First(&context).Error

	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, apperrors.NewNotFound(codes.DatabaseQuery, "活跃前置认证上下文不存在").Build()
		}
		return nil, apperrors.NewInternal(codes.DatabaseQuery, "查询前置认证上下文失败").Wrap(err).Build()
	}
	return &context, nil
}

// FindByDeviceID 通过设备ID查找前置认证上下文列表
func (r *PreAuthRepository) FindByDeviceID(ctx context.Context, deviceID string) ([]*entity.PreAuthContext, error) {
	var contexts []*entity.PreAuthContext
	err := r.db.WithContext(ctx).Where("device_id = ?", deviceID).Order("created_at DESC").Find(&contexts).Error
	if err != nil {
		return nil, apperrors.NewInternal(codes.DatabaseQuery, "查询前置认证上下文失败").Wrap(err).Build()
	}
	return contexts, nil
}

// === 状态管理 ===

// CompleteStep 完成认证步骤
func (r *PreAuthRepository) CompleteStep(ctx context.Context, contextID string, step entity.AuthStep) error {
	// 这里需要更新已完成的步骤，具体实现取决于如何存储步骤状态
	// 假设使用JSON字段存储已完成的步骤
	result := r.db.WithContext(ctx).Model(&entity.PreAuthContext{}).
		Where("context_id = ?", contextID).
		Update("updated_at", time.Now())

	if result.Error != nil {
		return apperrors.NewInternal(codes.DatabaseQuery, "完成认证步骤失败").Wrap(result.Error).Build()
	}
	if result.RowsAffected == 0 {
		return apperrors.NewNotFound(codes.DatabaseQuery, "前置认证上下文无变更").Build()
	}
	return nil
}

// IncrementAttempt 增加尝试次数
func (r *PreAuthRepository) IncrementAttempt(ctx context.Context, contextID string) error {
	result := r.db.WithContext(ctx).Model(&entity.PreAuthContext{}).
		Where("context_id = ?", contextID).
		Update("attempt_count", gorm.Expr("attempt_count + 1"))

	if result.Error != nil {
		return apperrors.NewInternal(codes.DatabaseQuery, "增加尝试次数失败").Wrap(result.Error).Build()
	}
	if result.RowsAffected == 0 {
		return apperrors.NewNotFound(codes.DatabaseQuery, "前置认证上下文无变更").Build()
	}
	return nil
}

// MarkCompleted 标记为已完成
func (r *PreAuthRepository) MarkCompleted(ctx context.Context, contextID string) error {
	now := time.Now()
	result := r.db.WithContext(ctx).Model(&entity.PreAuthContext{}).
		Where("context_id = ?", contextID).
		Updates(map[string]interface{}{
			"current_step": entity.AuthStepCompleted,
			"completed_at": &now,
			"updated_at":   now,
		})

	if result.Error != nil {
		return apperrors.NewInternal(codes.DatabaseQuery, "标记为已完成失败").Wrap(result.Error).Build()
	}
	if result.RowsAffected == 0 {
		return apperrors.NewNotFound(codes.DatabaseQuery, "前置认证上下文无变更").Build()
	}
	return nil
}

// UpdateVerificationData 更新验证数据
func (r *PreAuthRepository) UpdateVerificationData(ctx context.Context, contextID string, data string) error {
	result := r.db.WithContext(ctx).Model(&entity.PreAuthContext{}).
		Where("context_id = ?", contextID).
		Update("verification_data", data)

	if result.Error != nil {
		return apperrors.NewInternal(codes.DatabaseQuery, "更新验证数据失败").Wrap(result.Error).Build()
	}
	if result.RowsAffected == 0 {
		return apperrors.NewNotFound(codes.DatabaseQuery, "前置认证上下文无变更").Build()
	}
	return nil
}

// === 批量操作 ===

// BatchCreate 批量创建前置认证上下文
func (r *PreAuthRepository) BatchCreate(ctx context.Context, contexts []*entity.PreAuthContext) error {
	if len(contexts) == 0 {
		return nil
	}

	if err := r.db.WithContext(ctx).CreateInBatches(contexts, 100).Error; err != nil {
		return apperrors.NewInternal(codes.DatabaseQuery, "批量创建前置认证上下文失败").Wrap(err).Build()
	}
	return nil
}

// BatchDelete 批量删除前置认证上下文
func (r *PreAuthRepository) BatchDelete(ctx context.Context, contextIDs []string) error {
	if len(contextIDs) == 0 {
		return nil
	}

	result := r.db.WithContext(ctx).Where("context_id IN ?", contextIDs).Delete(&entity.PreAuthContext{})
	if result.Error != nil {
		return apperrors.NewInternal(codes.DatabaseQuery, "批量删除前置认证上下文失败").Wrap(result.Error).Build()
	}
	return nil
}

// === 过期清理 ===

// DeleteExpiredContexts 删除过期的前置认证上下文
func (r *PreAuthRepository) DeleteExpiredContexts(ctx context.Context, before time.Time) (int64, error) {
	result := r.db.WithContext(ctx).
		Where("expires_at < ? OR current_step = ?", before, entity.AuthStepCompleted).
		Delete(&entity.PreAuthContext{})

	if result.Error != nil {
		return 0, apperrors.NewInternal(codes.DatabaseQuery, "删除过期的前置认证上下文失败").Wrap(result.Error).Build()
	}
	return result.RowsAffected, nil
}

// FindExpiredContexts 查找过期的前置认证上下文
func (r *PreAuthRepository) FindExpiredContexts(ctx context.Context, before time.Time, limit int) ([]*entity.PreAuthContext, error) {
	var contexts []*entity.PreAuthContext
	err := r.db.WithContext(ctx).
		Where("expires_at < ? OR current_step = ?", before, entity.AuthStepCompleted).
		Limit(limit).
		Find(&contexts).Error

	if err != nil {
		return nil, apperrors.NewInternal(codes.DatabaseQuery, "查找过期的前置认证上下文失败").Wrap(err).Build()
	}
	return contexts, nil
}

// CleanupCompletedContexts 清理已完成的前置认证上下文
func (r *PreAuthRepository) CleanupCompletedContexts(ctx context.Context, completedBefore time.Time) (int64, error) {
	result := r.db.WithContext(ctx).
		Where("completed_at < ? AND current_step = ?", completedBefore, entity.AuthStepCompleted).
		Delete(&entity.PreAuthContext{})

	if result.Error != nil {
		return 0, apperrors.NewInternal(codes.DatabaseQuery, "清理已完成的前置认证上下文失败").Wrap(result.Error).Build()
	}
	return result.RowsAffected, nil
}

// === 统计查询 ===

// CountByUser 统计用户的前置认证上下文数量
func (r *PreAuthRepository) CountByUser(ctx context.Context, userID string) (int64, error) {
	var count int64
	err := r.db.WithContext(ctx).Model(&entity.PreAuthContext{}).Where("user_id = ?", userID).Count(&count).Error
	if err != nil {
		return 0, apperrors.NewInternal(codes.DatabaseQuery, "统计用户的前置认证上下文数量失败").Wrap(err).Build()
	}
	return count, nil
}

// CountActiveByUser 统计用户的活跃前置认证上下文数量
func (r *PreAuthRepository) CountActiveByUser(ctx context.Context, userID string) (int64, error) {
	var count int64
	err := r.db.WithContext(ctx).Model(&entity.PreAuthContext{}).
		Where("user_id = ? AND current_step != ? AND expires_at > ?",
			userID, entity.AuthStepCompleted, time.Now()).
		Count(&count).Error

	if err != nil {
		return 0, apperrors.NewInternal(codes.DatabaseQuery, "统计用户的活跃前置认证上下文数量失败").Wrap(err).Build()
	}
	return count, nil
}

// CountByStep 统计指定步骤的前置认证上下文数量
func (r *PreAuthRepository) CountByStep(ctx context.Context, step entity.AuthStep) (int64, error) {
	var count int64
	// 这里需要根据实际的步骤存储方式来实现查询
	// 假设使用JSON字段存储当前步骤
	err := r.db.WithContext(ctx).Model(&entity.PreAuthContext{}).
		Where("current_step = ?", step).
		Count(&count).Error

	if err != nil {
		return 0, apperrors.NewInternal(codes.DatabaseQuery, "统计指定步骤的前置认证上下文数量失败").Wrap(err).Build()
	}
	return count, nil
}

// === 验证操作 ===

// ExistsByContextID 检查前置认证上下文是否存在
func (r *PreAuthRepository) ExistsByContextID(ctx context.Context, contextID string) (bool, error) {
	var count int64
	err := r.db.WithContext(ctx).Model(&entity.PreAuthContext{}).
		Where("context_id = ?", contextID).
		Count(&count).Error

	if err != nil {
		return false, apperrors.NewInternal(codes.DatabaseQuery, "检查前置认证上下文是否存在失败").Wrap(err).Build()
	}
	return count > 0, nil
}

// IsContextValid 检查前置认证上下文是否有效
func (r *PreAuthRepository) IsContextValid(ctx context.Context, contextID string) (bool, error) {
	var count int64
	err := r.db.WithContext(ctx).Model(&entity.PreAuthContext{}).
		Where("context_id = ? AND current_step != ? AND expires_at > ?",
			contextID, entity.AuthStepCompleted, time.Now()).
		Count(&count).Error

	if err != nil {
		return false, apperrors.NewInternal(codes.DatabaseQuery, "检查前置认证上下文是否有效失败").Wrap(err).Build()
	}
	return count > 0, nil
}

// CanUserAttempt 检查用户是否可以尝试认证
func (r *PreAuthRepository) CanUserAttempt(ctx context.Context, userID string) (bool, error) {
	var count int64
	// 检查是否有过多的失败尝试
	err := r.db.WithContext(ctx).Model(&entity.PreAuthContext{}).
		Where("user_id = ? AND current_step != ? AND attempt_count < ?",
			userID, entity.AuthStepCompleted, 5). // 假设最大尝试次数为5
		Count(&count).Error

	if err != nil {
		return false, apperrors.NewInternal(codes.DatabaseQuery, "检查用户是否可以尝试认证失败").Wrap(err).Build()
	}
	return count > 0, nil
}
