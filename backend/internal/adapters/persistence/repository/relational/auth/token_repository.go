package auth

import (
	"context"
	"time"

	"backend/internal/adapters/persistence/repository/abstraction"
	"backend/internal/domain/auth/entity"
	"backend/internal/domain/auth/repository"
	"backend/pkg/adapters/database"
	apperrors "backend/pkg/common/errors"
	"backend/pkg/common/errors/codes"
	dbAbstraction "backend/pkg/infrastructure/database/abstraction"

	"gorm.io/gorm"
)

// TokenRepository Token存储仓储的数据库实现
type TokenRepository struct {
	*abstraction.BaseRepository
}

// NewTokenRepository 创建新的Token仓储实例
func NewTokenRepository(dbManager dbAbstraction.Manager) repository.TokenRepository {
	return &TokenRepository{
		BaseRepository: abstraction.NewBaseRepository(dbManager),
	}
}

// === 基础CRUD操作 ===

// Save 保存Token
func (r *TokenRepository) Save(ctx context.Context, token *entity.TokenStorage) error {
	dataAccess := r.GetDataAccess()
	if err := dataAccess.Create(ctx, token); err != nil {
		return database.TranslateDBError(err)
	}
	return nil
}

// Update 更新Token
func (r *TokenRepository) Update(ctx context.Context, token *entity.TokenStorage) error {
	dataAccess := r.GetDataAccess()
	if err := dataAccess.Update(ctx, token); err != nil {
		if database.IsNotFoundError(err) {
			return apperrors.NewNotFound(codes.ResourceNotFound, "Token不存在").
				WithDetail("token_id", token.TokenID).
				Wrap(err).Build()
		}
		return database.TranslateDBError(err)
	}
	return nil
}

// Delete 删除Token
func (r *TokenRepository) Delete(ctx context.Context, tokenID string) error {
	queryBuilder := r.GetQueryBuilder()
	err := queryBuilder.
		Where("token_id = ?", tokenID).
		Exec("DELETE FROM token_storages WHERE token_id = ?", tokenID)

	if err != nil {
		if database.IsNotFoundError(err) {
			return apperrors.NewNotFound(codes.ResourceNotFound, "Token不存在").
				WithDetail("token_id", tokenID).
				Wrap(err).Build()
		}
		return database.TranslateDBError(err)
	}
	return nil
}

// SaveWithTx 在事务中保存Token
func (r *TokenRepository) SaveWithTx(ctx context.Context, tx *gorm.DB, token *entity.TokenStorage) error {
	// TODO: 实现事务支持
	return r.Save(ctx, token)
}

// === 查询操作 ===

// FindByTokenID 通过TokenID查找Token
func (r *TokenRepository) FindByTokenID(ctx context.Context, tokenID string) (*entity.TokenStorage, error) {
	var token entity.TokenStorage
	dataAccess := r.GetDataAccess()

	err := dataAccess.First(ctx, &token, "token_id = ?", tokenID)
	if err != nil {
		if database.IsNotFoundError(err) {
			return nil, apperrors.NewNotFound(codes.ResourceNotFound, "Token不存在").
				WithDetail("token_id", tokenID).
				Wrap(err).Build()
		}
		return nil, database.TranslateDBError(err)
	}
	return &token, nil
}

// FindByTokenHash 通过Token哈希查找Token
func (r *TokenRepository) FindByTokenHash(ctx context.Context, tokenHash string) (*entity.TokenStorage, error) {
	var token entity.TokenStorage
	dataAccess := r.GetDataAccess()

	err := dataAccess.First(ctx, &token, "token_hash = ?", tokenHash)
	if err != nil {
		if database.IsNotFoundError(err) {
			return nil, apperrors.NewNotFound(codes.ResourceNotFound, "Token不存在").
				WithDetail("token_hash", tokenHash).
				Wrap(err).Build()
		}
		return nil, database.TranslateDBError(err)
	}
	return &token, nil
}

// TODO: 以下方法需要重新实现以使用database.Manager的抽象接口

// FindByUserID 通过用户ID查找Token列表
func (r *TokenRepository) FindByUserID(ctx context.Context, userID string, tokenType entity.TokenType) ([]*entity.TokenStorage, error) {
	// TODO: 实现用户Token查询
	return []*entity.TokenStorage{}, nil
}

// FindBySessionID 通过会话ID查找Token列表
func (r *TokenRepository) FindBySessionID(ctx context.Context, sessionID string) ([]*entity.TokenStorage, error) {
	// TODO: 实现会话Token查询
	return []*entity.TokenStorage{}, nil
}

// FindByDeviceID 通过设备ID查找Token列表
func (r *TokenRepository) FindByDeviceID(ctx context.Context, deviceID string) ([]*entity.TokenStorage, error) {
	// TODO: 实现设备Token查询
	return []*entity.TokenStorage{}, nil
}

// === 批量操作 ===

// BatchCreate 批量创建Token
func (r *TokenRepository) BatchCreate(ctx context.Context, tokens []*entity.TokenStorage) error {
	// TODO: 实现批量创建
	return nil
}

// BatchUpdateStatus 批量更新Token状态
func (r *TokenRepository) BatchUpdateStatus(ctx context.Context, tokenIDs []string, status entity.TokenStatus) error {
	// TODO: 实现批量状态更新
	return nil
}

// BatchDelete 批量删除Token
func (r *TokenRepository) BatchDelete(ctx context.Context, tokenIDs []string) error {
	// TODO: 实现批量删除
	return nil
}

// === 状态管理 ===

// UpdateStatus 更新Token状态
func (r *TokenRepository) UpdateStatus(ctx context.Context, tokenID string, status entity.TokenStatus) error {
	// TODO: 实现状态更新
	return nil
}

// UpdateLastUsed 更新Token最后使用时间
func (r *TokenRepository) UpdateLastUsed(ctx context.Context, tokenID string) error {
	// TODO: 实现最后使用时间更新
	return nil
}

// RevokeTokensByUser 撤销用户的所有指定类型Token
func (r *TokenRepository) RevokeTokensByUser(ctx context.Context, userID string, tokenType entity.TokenType) error {
	// TODO: 实现用户Token撤销
	return nil
}

// RevokeTokensBySession 撤销会话的所有Token
func (r *TokenRepository) RevokeTokensBySession(ctx context.Context, sessionID string) error {
	// TODO: 实现会话Token撤销
	return nil
}

// RevokeTokensByDevice 撤销设备的所有Token
func (r *TokenRepository) RevokeTokensByDevice(ctx context.Context, deviceID string) error {
	// TODO: 实现设备Token撤销
	return nil
}

// === 过期清理 ===

// DeleteExpiredTokens 删除过期Token
func (r *TokenRepository) DeleteExpiredTokens(ctx context.Context, before time.Time) (int64, error) {
	// TODO: 实现过期Token删除
	return 0, nil
}

// FindExpiredTokens 查找过期Token
func (r *TokenRepository) FindExpiredTokens(ctx context.Context, before time.Time, limit int) ([]*entity.TokenStorage, error) {
	// TODO: 实现过期Token查询
	return []*entity.TokenStorage{}, nil
}

// === 统计查询 ===

// CountByUser 统计用户的Token数量
func (r *TokenRepository) CountByUser(ctx context.Context, userID string, tokenType entity.TokenType) (int64, error) {
	// TODO: 实现用户Token统计
	return 0, nil
}

// CountByUserAndStatus 统计用户指定状态的Token数量
func (r *TokenRepository) CountByUserAndStatus(ctx context.Context, userID string, tokenType entity.TokenType, status entity.TokenStatus) (int64, error) {
	// TODO: 实现用户状态Token统计
	return 0, nil
}

// CountActiveTokensByDevice 统计设备的活跃Token数量
func (r *TokenRepository) CountActiveTokensByDevice(ctx context.Context, deviceID string) (int64, error) {
	// TODO: 实现设备活跃Token统计
	return 0, nil
}

// === 验证操作 ===

// ExistsByTokenID 检查Token是否存在
func (r *TokenRepository) ExistsByTokenID(ctx context.Context, tokenID string) (bool, error) {
	// TODO: 实现Token存在性检查
	return false, nil
}

// IsTokenValid 检查Token是否有效
func (r *TokenRepository) IsTokenValid(ctx context.Context, tokenID string) (bool, error) {
	// TODO: 实现Token有效性检查
	return false, nil
}
