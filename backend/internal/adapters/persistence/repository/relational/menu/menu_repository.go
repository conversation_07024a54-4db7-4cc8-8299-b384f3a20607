package menu

import (
	"context"
	"strings"

	"backend/internal/adapters/persistence/repository/abstraction"
	"backend/internal/domain/menu/entity"
	"backend/internal/domain/menu/repository"
	"backend/internal/domain/menu/valueobject"
	"backend/pkg/adapters/database"
	apperrors "backend/pkg/common/errors"
	"backend/pkg/common/errors/codes"
	dbAbstraction "backend/pkg/infrastructure/database/abstraction"

	"github.com/google/uuid"
)

// MenuRepository PostgreSQL菜单仓储实现
type MenuRepository struct {
	*abstraction.BaseRepository
}

// NewMenuRepository 创建菜单仓储实例
func NewMenuRepository(dbManager dbAbstraction.Manager) repository.MenuRepository {
	return &MenuRepository{
		BaseRepository: abstraction.NewBaseRepository(dbManager),
	}
}

// Create 创建菜单
func (r *MenuRepository) Create(ctx context.Context, menu *entity.Menu) error {
	if err := menu.Validate(); err != nil {
		return apperrors.NewValidation(codes.Validation, "菜单验证失败").
			WithDetail("validation_error", err.Error()).
			Wrap(err).Build()
	}

	// 生成ID（如果尚未生成）
	if menu.BusinessID == "" {
		menu.BusinessID = uuid.New().String()
	}

	dataAccess := r.GetDataAccess()
	if err := dataAccess.Create(ctx, menu); err != nil {
		// 检查是否是重复键错误
		if database.IsConstraintError(err) {
			return apperrors.NewConflict(codes.ResourceAlreadyExists, "菜单已存在").
				WithDetail("business_id", menu.BusinessID).
				WithDetail("name", menu.Name).
				Wrap(err).Build()
		}
		return database.TranslateDBError(err)
	}

	return nil
}

// Update 更新菜单
func (r *MenuRepository) Update(ctx context.Context, menu *entity.Menu) error {
	if err := menu.Validate(); err != nil {
		return apperrors.NewValidation(codes.Validation, "菜单验证失败").
			WithDetail("validation_error", err.Error()).
			Wrap(err).Build()
	}

	menu.Version++ // 乐观锁版本控制

	queryBuilder := r.GetQueryBuilder()
	err := queryBuilder.
		Where("business_id = ?", menu.BusinessID).
		Where("version = ?", menu.Version-1).
		Where("deleted_at IS NULL").
		Exec("UPDATE menus SET name = ?, title = ?, path = ?, menu_type = ?, status = ?, is_visible = ?, is_external = ?, keep_alive = ?, level = ?, sort = ?, parent_id = ?, permission = ?, resource = ?, action = ?, icon = ?, component = ?, meta = ?, version = ?, updated_at = NOW() WHERE business_id = ? AND version = ? AND deleted_at IS NULL",
			menu.Name, menu.Title, menu.Path, menu.MenuType, menu.Status,
			menu.IsVisible, menu.IsExternal, menu.KeepAlive, menu.Level, menu.Sort,
			menu.ParentID, menu.Permission, menu.Resource, menu.Action,
			menu.Icon, menu.Component, menu.Meta,
			menu.Version, menu.BusinessID, menu.Version-1)

	if err != nil {
		if database.IsNotFoundError(err) {
			return apperrors.NewNotFound(codes.ResourceNotFound, "菜单不存在或版本冲突").
				WithDetail("business_id", menu.BusinessID).
				WithDetail("version", menu.Version-1).
				Wrap(err).Build()
		}
		return database.TranslateDBError(err)
	}

	return nil
}

// Delete 删除菜单（软删除）
func (r *MenuRepository) Delete(ctx context.Context, id string) error {
	queryBuilder := r.GetQueryBuilder()
	err := queryBuilder.
		Where("business_id = ?", id).
		Where("deleted_at IS NULL").
		Exec("UPDATE menus SET deleted_at = NOW() WHERE business_id = ? AND deleted_at IS NULL", id)

	if err != nil {
		if database.IsNotFoundError(err) {
			return apperrors.NewNotFound(codes.ResourceNotFound, "菜单不存在").
				WithDetail("business_id", id).
				Wrap(err).Build()
		}
		return database.TranslateDBError(err)
	}

	return nil
}

// FindByID 根据技术ID查找菜单
func (r *MenuRepository) FindByID(ctx context.Context, id string) (*entity.Menu, error) {
	var menu entity.Menu

	queryBuilder := r.GetQueryBuilder()
	err := queryBuilder.
		Where("id = ?", id).
		Where("deleted_at IS NULL").
		First(&menu)

	if err != nil {
		if database.IsNotFoundError(err) {
			return nil, apperrors.NewNotFound(codes.ResourceNotFound, "菜单不存在").
				WithDetail("id", id).
				Wrap(err).Build()
		}
		return nil, database.TranslateDBError(err)
	}
	return &menu, nil
}

// FindByBusinessID 根据业务ID查找菜单
func (r *MenuRepository) FindByBusinessID(ctx context.Context, businessID string) (*entity.Menu, error) {
	var menu entity.Menu

	queryBuilder := r.GetQueryBuilder()
	err := queryBuilder.
		Where("business_id = ?", businessID).
		Where("deleted_at IS NULL").
		First(&menu)

	if err != nil {
		if database.IsNotFoundError(err) {
			return nil, apperrors.NewNotFound(codes.ResourceNotFound, "菜单不存在").
				WithDetail("business_id", businessID).
				Wrap(err).Build()
		}
		return nil, database.TranslateDBError(err)
	}
	return &menu, nil
}

// FindByTenantID 根据租户ID查找所有菜单
func (r *MenuRepository) FindByTenantID(ctx context.Context, tenantID string) ([]*entity.Menu, error) {
	var menus []*entity.Menu

	queryBuilder := r.GetQueryBuilder()
	err := queryBuilder.
		Where("tenant_id = ?", tenantID).
		Where("deleted_at IS NULL").
		Order("level ASC, sort ASC, name ASC").
		Find(&menus)

	if err != nil {
		return nil, database.TranslateDBError(err)
	}
	return menus, nil
}

// FindByParentID 根据父菜单ID查找子菜单
func (r *MenuRepository) FindByParentID(ctx context.Context, tenantID, parentID string) ([]*entity.Menu, error) {
	var menus []*entity.Menu

	queryBuilder := r.GetQueryBuilder()
	query := queryBuilder.Where("tenant_id = ?", tenantID).Where("deleted_at IS NULL")

	if parentID == "" {
		query = query.Where("parent_id IS NULL OR parent_id = ''")
	} else {
		query = query.Where("parent_id = ?", parentID)
	}

	err := query.Order("sort ASC, name ASC").Find(&menus)
	if err != nil {
		return nil, database.TranslateDBError(err)
	}
	return menus, nil
}

// FindRootMenus 查找根菜单
func (r *MenuRepository) FindRootMenus(ctx context.Context, tenantID string) ([]*entity.Menu, error) {
	return r.FindByParentID(ctx, tenantID, "")
}

// FindByLevel 根据层级查找菜单
func (r *MenuRepository) FindByLevel(ctx context.Context, tenantID string, level int) ([]*entity.Menu, error) {
	var menus []*entity.Menu

	queryBuilder := r.GetQueryBuilder()
	err := queryBuilder.
		Where("tenant_id = ? AND level = ?", tenantID, level).
		Where("deleted_at IS NULL").
		Order("sort ASC, name ASC").
		Find(&menus)

	if err != nil {
		return nil, database.TranslateDBError(err)
	}
	return menus, nil
}

// FindByPermissions 根据权限列表查找菜单
func (r *MenuRepository) FindByPermissions(ctx context.Context, tenantID string, permissions []string) ([]*entity.Menu, error) {
	if len(permissions) == 0 {
		return []*entity.Menu{}, nil
	}

	var menus []*entity.Menu
	queryBuilder := r.GetQueryBuilder()
	err := queryBuilder.
		Where("tenant_id = ? AND (permission IN ? OR permission IS NULL OR permission = '')", tenantID, permissions).
		Where("deleted_at IS NULL").
		Order("level ASC, sort ASC, name ASC").
		Find(&menus)

	if err != nil {
		return nil, database.TranslateDBError(err)
	}
	return menus, nil
}

// FindByResourceAction 根据资源和操作查找菜单
func (r *MenuRepository) FindByResourceAction(ctx context.Context, tenantID, resource, action string) ([]*entity.Menu, error) {
	var menus []*entity.Menu
	queryBuilder := r.GetQueryBuilder()
	err := queryBuilder.
		Where("tenant_id = ? AND resource = ? AND action = ?", tenantID, resource, action).
		Where("deleted_at IS NULL").
		Order("level ASC, sort ASC, name ASC").
		Find(&menus)

	if err != nil {
		return nil, database.TranslateDBError(err)
	}
	return menus, nil
}

// FindVisibleMenus 查找可见菜单
func (r *MenuRepository) FindVisibleMenus(ctx context.Context, tenantID string) ([]*entity.Menu, error) {
	var menus []*entity.Menu
	queryBuilder := r.GetQueryBuilder()
	err := queryBuilder.
		Where("tenant_id = ? AND is_visible = ?", tenantID, true).
		Where("deleted_at IS NULL").
		Order("level ASC, sort ASC, name ASC").
		Find(&menus)

	if err != nil {
		return nil, database.TranslateDBError(err)
	}
	return menus, nil
}

// FindActiveMenus 查找激活菜单
func (r *MenuRepository) FindActiveMenus(ctx context.Context, tenantID string) ([]*entity.Menu, error) {
	var menus []*entity.Menu
	queryBuilder := r.GetQueryBuilder()
	err := queryBuilder.
		Where("tenant_id = ? AND status = ?", tenantID, valueobject.MenuStatusActive).
		Where("deleted_at IS NULL").
		Order("level ASC, sort ASC, name ASC").
		Find(&menus)

	if err != nil {
		return nil, database.TranslateDBError(err)
	}
	return menus, nil
}

// FindByType 根据菜单类型查找菜单
func (r *MenuRepository) FindByType(ctx context.Context, tenantID string, menuType valueobject.MenuType) ([]*entity.Menu, error) {
	var menus []*entity.Menu
	queryBuilder := r.GetQueryBuilder()
	err := queryBuilder.
		Where("tenant_id = ? AND menu_type = ?", tenantID, menuType).
		Where("deleted_at IS NULL").
		Order("level ASC, sort ASC, name ASC").
		Find(&menus)

	if err != nil {
		return nil, database.TranslateDBError(err)
	}
	return menus, nil
}

// FindDirectories 查找目录类型菜单
func (r *MenuRepository) FindDirectories(ctx context.Context, tenantID string) ([]*entity.Menu, error) {
	return r.FindByType(ctx, tenantID, valueobject.MenuTypeDirectory)
}

// FindMenuItems 查找菜单类型菜单
func (r *MenuRepository) FindMenuItems(ctx context.Context, tenantID string) ([]*entity.Menu, error) {
	return r.FindByType(ctx, tenantID, valueobject.MenuTypeMenu)
}

// FindButtons 查找按钮类型菜单
func (r *MenuRepository) FindButtons(ctx context.Context, tenantID string) ([]*entity.Menu, error) {
	return r.FindByType(ctx, tenantID, valueobject.MenuTypeButton)
}

// FindMenuTree 查找菜单树
func (r *MenuRepository) FindMenuTree(ctx context.Context, tenantID string) ([]*entity.Menu, error) {
	return r.FindByTenantID(ctx, tenantID)
}

// FindMenuTreeByParent 根据父菜单查找菜单树
func (r *MenuRepository) FindMenuTreeByParent(ctx context.Context, tenantID, parentID string) ([]*entity.Menu, error) {
	var menus []*entity.Menu

	// 使用递归CTE查询子菜单树
	query := `
		WITH RECURSIVE menu_tree AS (
			-- 基础查询：查找指定父菜单的直接子菜单
			SELECT * FROM menus
			WHERE tenant_id = ? AND parent_id = ? AND deleted_at IS NULL

			UNION ALL

			-- 递归查询：查找子菜单的子菜单
			SELECT m.* FROM menus m
			INNER JOIN menu_tree mt ON m.parent_id = mt.business_id
			WHERE m.tenant_id = ? AND m.deleted_at IS NULL
		)
		SELECT * FROM menu_tree ORDER BY level ASC, sort ASC, name ASC
	`

	queryBuilder := r.GetQueryBuilder()
	err := queryBuilder.Raw(query, tenantID, parentID, tenantID).Find(&menus)
	if err != nil {
		return nil, database.TranslateDBError(err)
	}

	return menus, nil
}

// FindChildrenRecursive 递归查找所有子菜单
func (r *MenuRepository) FindChildrenRecursive(ctx context.Context, tenantID, parentID string) ([]*entity.Menu, error) {
	return r.FindMenuTreeByParent(ctx, tenantID, parentID)
}

// FindWithSort 带排序的查找菜单
func (r *MenuRepository) FindWithSort(ctx context.Context, tenantID string, orderBy string, limit, offset int) ([]*entity.Menu, error) {
	var menus []*entity.Menu

	// 验证排序字段
	validOrderFields := map[string]bool{
		"sort":       true,
		"level":      true,
		"name":       true,
		"created_at": true,
		"updated_at": true,
	}

	if !validOrderFields[strings.ToLower(orderBy)] {
		orderBy = "sort ASC, name ASC"
	}

	queryBuilder := r.GetQueryBuilder()
	query := queryBuilder.Where("tenant_id = ?", tenantID).Where("deleted_at IS NULL").Order(orderBy)

	if limit > 0 {
		query = query.Limit(limit)
	}
	if offset > 0 {
		query = query.Offset(offset)
	}

	err := query.Find(&menus)
	if err != nil {
		return nil, database.TranslateDBError(err)
	}

	return menus, nil
}

// FindByNamePattern 根据名称模式查找菜单
func (r *MenuRepository) FindByNamePattern(ctx context.Context, tenantID, pattern string) ([]*entity.Menu, error) {
	var menus []*entity.Menu
	queryBuilder := r.GetQueryBuilder()
	err := queryBuilder.
		Where("tenant_id = ? AND name ILIKE ?", tenantID, "%"+pattern+"%").
		Where("deleted_at IS NULL").
		Order("level ASC, sort ASC, name ASC").
		Find(&menus)

	if err != nil {
		return nil, database.TranslateDBError(err)
	}
	return menus, nil
}

// FindByPathPattern 根据路径模式查找菜单
func (r *MenuRepository) FindByPathPattern(ctx context.Context, tenantID, pattern string) ([]*entity.Menu, error) {
	var menus []*entity.Menu
	queryBuilder := r.GetQueryBuilder()
	err := queryBuilder.
		Where("tenant_id = ? AND path ILIKE ?", tenantID, "%"+pattern+"%").
		Where("deleted_at IS NULL").
		Order("level ASC, sort ASC, name ASC").
		Find(&menus)

	if err != nil {
		return nil, database.TranslateDBError(err)
	}
	return menus, nil
}

// CreateBatch 批量创建菜单
func (r *MenuRepository) CreateBatch(ctx context.Context, menus []*entity.Menu) error {
	if len(menus) == 0 {
		return nil
	}

	// 验证所有菜单并生成ID
	for _, menu := range menus {
		if err := menu.Validate(); err != nil {
			return apperrors.NewValidation(codes.Validation, "菜单验证失败").
				WithDetail("validation_error", err.Error()).
				Wrap(err).Build()
		}
		// 生成ID（如果尚未生成）
		if menu.BusinessID == "" {
			menu.BusinessID = uuid.New().String()
		}
	}

	dataAccess := r.GetDataAccess()
	err := dataAccess.CreateInBatches(ctx, menus, 100)
	if err != nil {
		return database.TranslateDBError(err)
	}

	return nil
}

// UpdateBatch 批量更新菜单
func (r *MenuRepository) UpdateBatch(ctx context.Context, menus []*entity.Menu) error {
	if len(menus) == 0 {
		return nil
	}

	// 验证所有菜单
	for _, menu := range menus {
		if err := menu.Validate(); err != nil {
			return apperrors.NewValidation(codes.Validation, "菜单验证失败").
				WithDetail("validation_error", err.Error()).
				Wrap(err).Build()
		}
	}

	// 使用事务批量更新
	transactionManager := r.GetTransactionManager()
	return transactionManager.WithTransaction(ctx, func(tx dbAbstraction.Transaction) error {
		for _, menu := range menus {
			if err := tx.Save(ctx, menu); err != nil {
				return database.TranslateDBError(err)
			}
		}
		return nil
	})
}

// DeleteBatch 批量删除菜单
func (r *MenuRepository) DeleteBatch(ctx context.Context, ids []string) error {
	if len(ids) == 0 {
		return nil
	}

	queryBuilder := r.GetQueryBuilder()
	err := queryBuilder.Exec("UPDATE menus SET deleted_at = NOW() WHERE business_id IN ? AND deleted_at IS NULL", ids)
	if err != nil {
		return database.TranslateDBError(err)
	}

	return nil
}

// CountByTenant 统计租户菜单数量
func (r *MenuRepository) CountByTenant(ctx context.Context, tenantID string) (int64, error) {
	var count int64
	queryBuilder := r.GetQueryBuilder()
	err := queryBuilder.Where("tenant_id = ?", tenantID).Where("deleted_at IS NULL").Count(&count)
	if err != nil {
		return 0, database.TranslateDBError(err)
	}
	return count, nil
}

// CountByParent 统计父菜单下的子菜单数量
func (r *MenuRepository) CountByParent(ctx context.Context, tenantID, parentID string) (int64, error) {
	var count int64
	queryBuilder := r.GetQueryBuilder()
	query := queryBuilder.Where("tenant_id = ?", tenantID).Where("deleted_at IS NULL")

	if parentID == "" {
		query = query.Where("parent_id IS NULL OR parent_id = ''")
	} else {
		query = query.Where("parent_id = ?", parentID)
	}

	err := query.Count(&count)
	if err != nil {
		return 0, database.TranslateDBError(err)
	}
	return count, nil
}

// CountByType 统计指定类型的菜单数量
func (r *MenuRepository) CountByType(ctx context.Context, tenantID string, menuType valueobject.MenuType) (int64, error) {
	var count int64
	queryBuilder := r.GetQueryBuilder()
	err := queryBuilder.
		Where("tenant_id = ? AND menu_type = ?", tenantID, menuType).
		Where("deleted_at IS NULL").
		Count(&count)

	if err != nil {
		return 0, database.TranslateDBError(err)
	}
	return count, nil
}

// CountByStatus 统计指定状态的菜单数量
func (r *MenuRepository) CountByStatus(ctx context.Context, tenantID string, status valueobject.MenuStatus) (int64, error) {
	var count int64
	queryBuilder := r.GetQueryBuilder()
	err := queryBuilder.
		Where("tenant_id = ? AND status = ?", tenantID, status).
		Where("deleted_at IS NULL").
		Count(&count)

	if err != nil {
		return 0, database.TranslateDBError(err)
	}
	return count, nil
}

// ExistsByName 检查菜单名称是否存在
func (r *MenuRepository) ExistsByName(ctx context.Context, tenantID, name string) (bool, error) {
	var count int64
	queryBuilder := r.GetQueryBuilder()
	err := queryBuilder.
		Where("tenant_id = ? AND name = ?", tenantID, name).
		Where("deleted_at IS NULL").
		Count(&count)

	if err != nil {
		return false, database.TranslateDBError(err)
	}
	return count > 0, nil
}

// ExistsByPath 检查菜单路径是否存在
func (r *MenuRepository) ExistsByPath(ctx context.Context, tenantID, path string) (bool, error) {
	var count int64
	queryBuilder := r.GetQueryBuilder()
	err := queryBuilder.
		Where("tenant_id = ? AND path = ?", tenantID, path).
		Where("deleted_at IS NULL").
		Count(&count)

	if err != nil {
		return false, database.TranslateDBError(err)
	}
	return count > 0, nil
}

// ExistsByNameExcludeID 检查菜单名称是否存在（排除指定ID）
func (r *MenuRepository) ExistsByNameExcludeID(ctx context.Context, tenantID, name, excludeID string) (bool, error) {
	var count int64
	queryBuilder := r.GetQueryBuilder()
	err := queryBuilder.
		Where("tenant_id = ? AND name = ? AND business_id != ?", tenantID, name, excludeID).
		Where("deleted_at IS NULL").
		Count(&count)

	if err != nil {
		return false, database.TranslateDBError(err)
	}
	return count > 0, nil
}

// ExistsByPathExcludeID 检查菜单路径是否存在（排除指定ID）
func (r *MenuRepository) ExistsByPathExcludeID(ctx context.Context, tenantID, path, excludeID string) (bool, error) {
	var count int64
	queryBuilder := r.GetQueryBuilder()
	err := queryBuilder.
		Where("tenant_id = ? AND path = ? AND business_id != ?", tenantID, path, excludeID).
		Where("deleted_at IS NULL").
		Count(&count)

	if err != nil {
		return false, database.TranslateDBError(err)
	}
	return count > 0, nil
}

// FindByExactPath 根据精确路径查找菜单
func (r *MenuRepository) FindByExactPath(ctx context.Context, tenantID, path string) (*entity.Menu, error) {
	var menu entity.Menu
	queryBuilder := r.GetQueryBuilder()
	err := queryBuilder.
		Where("tenant_id = ? AND path = ?", tenantID, path).
		Where("deleted_at IS NULL").
		First(&menu)

	if err != nil {
		if database.IsNotFoundError(err) {
			return nil, apperrors.NewNotFound(codes.DatabaseNotFound, "菜单未找到").Build()
		}
		return nil, database.TranslateDBError(err)
	}
	return &menu, nil
}

// FindByPathPrefix 根据路径前缀查找菜单
func (r *MenuRepository) FindByPathPrefix(ctx context.Context, tenantID, pathPrefix string) ([]*entity.Menu, error) {
	var menus []*entity.Menu
	queryBuilder := r.GetQueryBuilder()
	err := queryBuilder.
		Where("tenant_id = ? AND path LIKE ?", tenantID, pathPrefix+"%").
		Where("deleted_at IS NULL").
		Order("path ASC").
		Find(&menus)

	if err != nil {
		return nil, database.TranslateDBError(err)
	}
	return menus, nil
}

// UpdateStatus 更新菜单状态
func (r *MenuRepository) UpdateStatus(ctx context.Context, id string, status valueobject.MenuStatus) error {
	queryBuilder := r.GetQueryBuilder()
	err := queryBuilder.Exec("UPDATE menus SET status = ?, updated_at = NOW() WHERE business_id = ? AND deleted_at IS NULL", status, id)
	if err != nil {
		return database.TranslateDBError(err)
	}
	return nil
}

// UpdateVisibility 更新菜单可见性
func (r *MenuRepository) UpdateVisibility(ctx context.Context, id string, visible bool) error {
	queryBuilder := r.GetQueryBuilder()
	err := queryBuilder.Exec("UPDATE menus SET is_visible = ?, updated_at = NOW() WHERE business_id = ? AND deleted_at IS NULL", visible, id)
	if err != nil {
		return database.TranslateDBError(err)
	}
	return nil
}

// BatchUpdateStatus 批量更新菜单状态
func (r *MenuRepository) BatchUpdateStatus(ctx context.Context, ids []string, status valueobject.MenuStatus) error {
	if len(ids) == 0 {
		return nil
	}

	queryBuilder := r.GetQueryBuilder()
	err := queryBuilder.Exec("UPDATE menus SET status = ?, updated_at = NOW() WHERE business_id IN ? AND deleted_at IS NULL", status, ids)
	if err != nil {
		return database.TranslateDBError(err)
	}

	return nil
}

// UpdateSort 更新菜单排序
func (r *MenuRepository) UpdateSort(ctx context.Context, id string, sort int) error {
	queryBuilder := r.GetQueryBuilder()
	err := queryBuilder.Exec("UPDATE menus SET sort = ?, updated_at = NOW() WHERE business_id = ? AND deleted_at IS NULL", sort, id)
	if err != nil {
		return database.TranslateDBError(err)
	}
	return nil
}

// BatchUpdateSort 批量更新菜单排序
func (r *MenuRepository) BatchUpdateSort(ctx context.Context, sortUpdates map[string]int) error {
	if len(sortUpdates) == 0 {
		return nil
	}

	transactionManager := r.GetTransactionManager()
	return transactionManager.WithTransaction(ctx, func(tx dbAbstraction.Transaction) error {
		for id, sort := range sortUpdates {
			queryBuilder := r.GetQueryBuilder()
			err := queryBuilder.Exec("UPDATE menus SET sort = ?, updated_at = NOW() WHERE business_id = ? AND deleted_at IS NULL", sort, id)
			if err != nil {
				return database.TranslateDBError(err)
			}
		}
		return nil
	})
}

// GetMaxSortInParent 获取父菜单下的最大排序号
func (r *MenuRepository) GetMaxSortInParent(ctx context.Context, tenantID, parentID string) (int, error) {
	var result struct {
		MaxSort int `json:"max_sort"`
	}

	queryBuilder := r.GetQueryBuilder()
	query := queryBuilder.Where("tenant_id = ?", tenantID).Where("deleted_at IS NULL")

	if parentID == "" {
		query = query.Where("parent_id IS NULL OR parent_id = ''")
	} else {
		query = query.Where("parent_id = ?", parentID)
	}

	err := query.Raw("SELECT COALESCE(MAX(sort), 0) as max_sort FROM menus WHERE tenant_id = ? AND deleted_at IS NULL AND (parent_id = ? OR (? = '' AND (parent_id IS NULL OR parent_id = '')))", tenantID, parentID, parentID).Find(&result)
	if err != nil {
		return 0, database.TranslateDBError(err)
	}

	return result.MaxSort, nil
}

// UpdateParent 更新菜单父级和层级
func (r *MenuRepository) UpdateParent(ctx context.Context, id, newParentID string, newLevel int) error {
	queryBuilder := r.GetQueryBuilder()
	err := queryBuilder.Exec("UPDATE menus SET parent_id = ?, level = ?, updated_at = NOW() WHERE business_id = ? AND deleted_at IS NULL", newParentID, newLevel, id)
	if err != nil {
		return database.TranslateDBError(err)
	}
	return nil
}

// MoveToParent 移动菜单到新父级
func (r *MenuRepository) MoveToParent(ctx context.Context, id, newParentID string) error {
	// 计算新层级
	var newLevel int
	if newParentID == "" {
		newLevel = 1
	} else {
		var parent entity.Menu
		queryBuilder := r.GetQueryBuilder()
		err := queryBuilder.
			Where("business_id = ?", newParentID).
			Where("deleted_at IS NULL").
			First(&parent)

		if err != nil {
			if database.IsNotFoundError(err) {
				return apperrors.NewNotFound(codes.DatabaseNotFound, "父菜单未找到").Build()
			}
			return database.TranslateDBError(err)
		}
		newLevel = parent.Level + 1
	}

	return r.UpdateParent(ctx, id, newParentID, newLevel)
}

// UpdateLevel 更新菜单层级
func (r *MenuRepository) UpdateLevel(ctx context.Context, id string, level int) error {
	queryBuilder := r.GetQueryBuilder()
	err := queryBuilder.Exec("UPDATE menus SET level = ?, updated_at = NOW() WHERE business_id = ? AND deleted_at IS NULL", level, id)
	if err != nil {
		return database.TranslateDBError(err)
	}
	return nil
}

// UpdateLevelRecursive 递归更新菜单层级
func (r *MenuRepository) UpdateLevelRecursive(ctx context.Context, parentID string, startLevel int) error {
	// 使用递归CTE更新所有子菜单的层级
	query := `
		WITH RECURSIVE menu_levels AS (
			-- 基础查询：查找指定父菜单的直接子菜单
			SELECT business_id, ? as new_level FROM menus
			WHERE parent_id = ? AND deleted_at IS NULL

			UNION ALL

			-- 递归查询：查找子菜单的子菜单
			SELECT m.business_id, ml.new_level + 1 as new_level
			FROM menus m
			INNER JOIN menu_levels ml ON m.parent_id = ml.business_id
			WHERE m.deleted_at IS NULL
		)
		UPDATE menus SET level = menu_levels.new_level, updated_at = NOW()
		FROM menu_levels
		WHERE menus.business_id = menu_levels.business_id
	`

	queryBuilder := r.GetQueryBuilder()
	err := queryBuilder.Exec(query, startLevel, parentID)
	if err != nil {
		return database.TranslateDBError(err)
	}

	return nil
}
