package user

import (
	"context"
	"errors"

	"gorm.io/gorm"

	"backend/internal/adapters/persistence/repository/abstraction"
	"backend/internal/domain/user/entity"
	"backend/internal/domain/user/repository"
	apperrors "backend/pkg/common/errors"
	"backend/pkg/common/errors/codes"
	database "backend/pkg/infrastructure/database/abstraction"
)

// Repository PostgreSQL用户仓储实现
type Repository struct {
	*abstraction.BaseRepository
}

// NewRepository 创建用户仓储实例
func NewRepository(dbManager database.Manager) repository.UserRepository {
	return &Repository{
		BaseRepository: abstraction.NewBaseRepository(dbManager),
	}
}

// Save 创建用户
func (r *Repository) Save(ctx context.Context, user *entity.User) error {
	dataAccess := r.GetDataAccess()
	if err := dataAccess.Create(ctx, user); err != nil {
		// 检查是否是重复键错误
		if isDuplicateKeyError(err) {
			return apperrors.NewConflict(codes.UserAlreadyExists, "用户已存在").
				WithDetail("email", user.Email).
				WithDetail("phone", user.Phone).
				Wrap(err).Build()
		}
		return apperrors.NewInternal(codes.DatabaseQuery, "创建用户失败").
			WithDetail("user_id", user.BusinessID).
			Wrap(err).Build()
	}
	return nil
}

// Update 更新用户
func (r *Repository) Update(ctx context.Context, user *entity.User) error {
	user.Version++ // 乐观锁版本控制

	queryBuilder := r.GetQueryBuilder()
	err := queryBuilder.
		Where("business_id = ?", user.BusinessID).
		Where("version = ?", user.Version-1).
		Where("deleted_at IS NULL").
		Exec("UPDATE users SET username = ?, email = ?, phone = ?, status = ?, nickname = ?, avatar = ?, first_name = ?, last_name = ?, language = ?, timezone = ?, version = ?, updated_at = NOW() WHERE business_id = ? AND version = ? AND deleted_at IS NULL",
			user.Username, user.Email, user.Phone, user.Status,
			user.Profile.Nickname, user.Profile.Avatar, user.Profile.FirstName,
			user.Profile.LastName, user.Profile.Language, user.Profile.Timezone,
			user.Version, user.BusinessID, user.Version-1)

	if err != nil {
		if isRecordNotFoundError(err) {
			return apperrors.NewNotFound(codes.UserNotFound, "用户不存在或版本冲突").
				WithDetail("user_id", user.BusinessID).
				WithDetail("version", user.Version-1).
				Wrap(err).Build()
		}
		return apperrors.NewInternal(codes.DatabaseQuery, "更新用户失败").
			WithDetail("user_id", user.BusinessID).
			Wrap(err).Build()
	}
	return nil
}

// Delete 逻辑删除用户
func (r *Repository) Delete(ctx context.Context, businessID string) error {
	queryBuilder := r.GetQueryBuilder()
	err := queryBuilder.
		Where("business_id = ?", businessID).
		Where("deleted_at IS NULL").
		Exec("UPDATE users SET deleted_at = NOW() WHERE business_id = ? AND deleted_at IS NULL", businessID)

	if err != nil {
		if isRecordNotFoundError(err) {
			return apperrors.NewNotFound(codes.UserNotFound, "用户不存在").
				WithDetail("user_id", businessID).
				Wrap(err).Build()
		}
		return apperrors.NewInternal(codes.DatabaseQuery, "删除用户失败").
			WithDetail("user_id", businessID).
			Wrap(err).Build()
	}
	return nil
}

// FindByBusinessID 通过业务ID查询用户
func (r *Repository) FindByBusinessID(ctx context.Context, businessID string) (*entity.User, error) {
	var user entity.User
	dataAccess := r.GetDataAccess()

	err := dataAccess.First(ctx, &user, "business_id = ? AND deleted_at IS NULL", businessID)
	if err != nil {
		if isRecordNotFoundError(err) {
			return nil, apperrors.NewNotFound(codes.UserNotFound, "用户不存在").
				WithDetail("user_id", businessID).
				Wrap(err).Build()
		}
		return nil, apperrors.NewInternal(codes.DatabaseQuery, "查询用户失败").
			WithDetail("user_id", businessID).
			Wrap(err).Build()
	}
	return &user, nil
}

// FindByTechID 通过技术ID查询用户
func (r *Repository) FindByTechID(ctx context.Context, techID int64) (*entity.User, error) {
	var user entity.User
	dataAccess := r.GetDataAccess()

	err := dataAccess.First(ctx, &user, "id = ? AND deleted_at IS NULL", techID)
	if err != nil {
		if isRecordNotFoundError(err) {
			return nil, apperrors.NewNotFound(codes.UserNotFound, "用户不存在").
				WithDetail("tech_id", techID).
				Wrap(err).Build()
		}
		return nil, apperrors.NewInternal(codes.DatabaseQuery, "查询用户失败").
			WithDetail("tech_id", techID).
			Wrap(err).Build()
	}
	return &user, nil
}

// FindByBusinessIDs 通过业务ID批量查询用户
func (r *Repository) FindByBusinessIDs(ctx context.Context, businessIDs []string) ([]*entity.User, error) {
	if len(businessIDs) == 0 {
		return []*entity.User{}, nil
	}

	var users []*entity.User
	dataAccess := r.GetDataAccess()

	err := dataAccess.Find(ctx, &users, "business_id IN ? AND deleted_at IS NULL", businessIDs)
	if err != nil {
		return nil, apperrors.NewInternal(codes.DatabaseQuery, "批量查询用户失败").
			WithDetail("user_ids", businessIDs).
			Wrap(err).Build()
	}
	return users, nil
}

// FindByTenantID 通过租户ID查询用户列表
func (r *Repository) FindByTenantID(ctx context.Context, tenantID string, limit, offset int) ([]*entity.User, int64, error) {
	var users []*entity.User

	// 先获取总数
	dataAccess := r.GetDataAccess()
	total, err := dataAccess.Count(ctx, &entity.User{}, "tenant_id = ? AND deleted_at IS NULL", tenantID)
	if err != nil {
		return nil, 0, apperrors.NewInternal(codes.DatabaseQuery, "统计用户数量失败").
			WithDetail("tenant_id", tenantID).
			Wrap(err).Build()
	}

	// 查询分页数据
	queryBuilder := r.GetQueryBuilder()
	err = queryBuilder.
		Where("tenant_id = ?", tenantID).
		Where("deleted_at IS NULL").
		Order("created_at DESC").
		Limit(limit).
		Offset(offset).
		Find(&users)

	if err != nil {
		return nil, 0, apperrors.NewInternal(codes.DatabaseQuery, "查询用户列表失败").
			WithDetail("tenant_id", tenantID).
			WithDetail("limit", limit).
			WithDetail("offset", offset).
			Wrap(err).Build()
	}

	return users, total, nil
}

// SearchUsers 搜索用户 (可按用户名, 昵称, 邮箱模糊搜索)
func (r *Repository) SearchUsers(ctx context.Context, tenantID, keyword string, limit, offset int) ([]*entity.User, int64, error) {
	var users []*entity.User

	likeKeyword := "%" + keyword + "%"

	// 先获取总数
	dataAccess := r.GetDataAccess()
	total, err := dataAccess.Count(ctx, &entity.User{}, "tenant_id = ? AND deleted_at IS NULL AND (username LIKE ? OR nickname LIKE ? OR email LIKE ?)", tenantID, likeKeyword, likeKeyword, likeKeyword)
	if err != nil {
		return nil, 0, apperrors.NewInternal(codes.DatabaseQuery, "统计搜索用户数量失败").
			WithDetail("tenant_id", tenantID).
			WithDetail("keyword", keyword).
			Wrap(err).Build()
	}

	// 查询分页数据
	queryBuilder := r.GetQueryBuilder()
	err = queryBuilder.
		Where("tenant_id = ?", tenantID).
		Where("deleted_at IS NULL").
		Where("(username LIKE ? OR nickname LIKE ? OR email LIKE ?)", likeKeyword, likeKeyword, likeKeyword).
		Order("created_at DESC").
		Limit(limit).
		Offset(offset).
		Find(&users)

	if err != nil {
		return nil, 0, apperrors.NewInternal(codes.DatabaseQuery, "搜索用户失败").
			WithDetail("tenant_id", tenantID).
			WithDetail("keyword", keyword).
			WithDetail("limit", limit).
			WithDetail("offset", offset).
			Wrap(err).Build()
	}

	return users, total, nil
}

// FindUsersByRoleID 通过角色ID查询用户
func (r *Repository) FindUsersByRoleID(ctx context.Context, tenantID, roleBusinessID string, limit, offset int) ([]*entity.User, int64, error) {
	var users []*entity.User
	var total int64

	joinCondition := "JOIN user_roles ur ON users.business_id = ur.user_business_id"
	whereCondition := "users.tenant_id = ? AND ur.role_business_id = ? AND users.deleted_at IS NULL"

	// 先获取总数
	queryBuilder := r.GetQueryBuilder()
	if err := queryBuilder.
		Joins(joinCondition).
		Where(whereCondition, tenantID, roleBusinessID).
		Count(&total); err != nil {
		return nil, 0, apperrors.NewInternal(codes.DatabaseQuery, "统计角色用户数量失败").
			WithDetail("tenant_id", tenantID).
			WithDetail("role_id", roleBusinessID).
			Wrap(err).Build()
	}

	// 查询分页数据
	err := queryBuilder.
		Joins(joinCondition).
		Where(whereCondition, tenantID, roleBusinessID).
		Limit(limit).
		Offset(offset).
		Find(&users)

	if err != nil {
		return nil, 0, apperrors.NewInternal(codes.DatabaseQuery, "查询角色用户失败").
			WithDetail("tenant_id", tenantID).
			WithDetail("role_id", roleBusinessID).
			WithDetail("limit", limit).
			WithDetail("offset", offset).
			Wrap(err).Build()
	}

	return users, total, nil
}

// BatchCreate 批量创建用户
func (r *Repository) BatchCreate(ctx context.Context, users []*entity.User) error {
	if len(users) == 0 {
		return nil
	}

	dataAccess := r.GetDataAccess()
	if err := dataAccess.CreateInBatches(ctx, users, 100); err != nil {
		// 检查是否是重复键错误
		if isDuplicateKeyError(err) {
			return apperrors.NewConflict(codes.UserAlreadyExists, "批量创建用户时存在重复用户").
				WithDetail("batch_size", len(users)).
				Wrap(err).Build()
		}
		return apperrors.NewInternal(codes.DatabaseQuery, "批量创建用户失败").
			WithDetail("batch_size", len(users)).
			Wrap(err).Build()
	}
	return nil
}

// ExistsByBusinessID 检查业务ID是否存在
func (r *Repository) ExistsByBusinessID(ctx context.Context, businessID string) (bool, error) {
	dataAccess := r.GetDataAccess()
	exists, err := dataAccess.Exists(ctx, &entity.User{}, "business_id = ? AND deleted_at IS NULL", businessID)
	if err != nil {
		return false, apperrors.NewInternal(codes.DatabaseQuery, "检查用户是否存在失败").
			WithDetail("user_id", businessID).
			Wrap(err).Build()
	}
	return exists, nil
}

// FindByEmail 通过邮箱查找用户
func (r *Repository) FindByEmail(ctx context.Context, email string) (*entity.User, error) {
	var user entity.User
	dataAccess := r.GetDataAccess()

	err := dataAccess.First(ctx, &user, "email = ? AND deleted_at IS NULL", email)
	if err != nil {
		if isRecordNotFoundError(err) {
			return nil, apperrors.NewNotFound(codes.UserNotFound, "用户不存在").
				WithDetail("email", email).
				Wrap(err).Build()
		}
		return nil, apperrors.NewInternal(codes.DatabaseQuery, "通过邮箱查询用户失败").
			WithDetail("email", email).
			Wrap(err).Build()
	}
	return &user, nil
}

// FindByPhone 通过手机号查找用户
func (r *Repository) FindByPhone(ctx context.Context, phone string) (*entity.User, error) {
	var user entity.User
	dataAccess := r.GetDataAccess()

	err := dataAccess.First(ctx, &user, "phone = ? AND deleted_at IS NULL", phone)
	if err != nil {
		if isRecordNotFoundError(err) {
			return nil, apperrors.NewNotFound(codes.UserNotFound, "用户不存在").
				WithDetail("phone", phone).
				Wrap(err).Build()
		}
		return nil, apperrors.NewInternal(codes.DatabaseQuery, "通过手机号查询用户失败").
			WithDetail("phone", phone).
			Wrap(err).Build()
	}
	return &user, nil
}

// ExistsByEmail 检查邮箱是否已存在
func (r *Repository) ExistsByEmail(ctx context.Context, email string) (bool, error) {
	dataAccess := r.GetDataAccess()
	exists, err := dataAccess.Exists(ctx, &entity.User{}, "email = ? AND deleted_at IS NULL", email)
	if err != nil {
		return false, apperrors.NewInternal(codes.DatabaseQuery, "检查邮箱是否存在失败").
			WithDetail("email", email).
			Wrap(err).Build()
	}
	return exists, nil
}

// ExistsByPhone 检查手机号是否已存在
func (r *Repository) ExistsByPhone(ctx context.Context, phone string) (bool, error) {
	dataAccess := r.GetDataAccess()
	exists, err := dataAccess.Exists(ctx, &entity.User{}, "phone = ? AND deleted_at IS NULL", phone)
	if err != nil {
		return false, apperrors.NewInternal(codes.DatabaseQuery, "检查手机号是否存在失败").
			WithDetail("phone", phone).
			Wrap(err).Build()
	}
	return exists, nil
}

// 错误处理辅助函数

// isDuplicateKeyError 检查是否是重复键错误
func isDuplicateKeyError(err error) bool {
	if err == nil {
		return false
	}
	// 检查PostgreSQL重复键错误
	return errors.Is(err, gorm.ErrDuplicatedKey) ||
		containsString(err.Error(), "duplicate key") ||
		containsString(err.Error(), "UNIQUE constraint failed")
}

// isRecordNotFoundError 检查是否是记录未找到错误
func isRecordNotFoundError(err error) bool {
	if err == nil {
		return false
	}
	return errors.Is(err, gorm.ErrRecordNotFound)
}

// containsString 检查字符串是否包含子字符串（不区分大小写）
func containsString(s, substr string) bool {
	return len(s) >= len(substr) &&
		(s == substr ||
			len(s) > len(substr) &&
				(s[:len(substr)] == substr ||
					s[len(s)-len(substr):] == substr ||
					indexString(s, substr) >= 0))
}

// indexString 简单的字符串查找
func indexString(s, substr string) int {
	n := len(substr)
	if n == 0 {
		return 0
	}
	if n > len(s) {
		return -1
	}
	for i := 0; i <= len(s)-n; i++ {
		if s[i:i+n] == substr {
			return i
		}
	}
	return -1
}
