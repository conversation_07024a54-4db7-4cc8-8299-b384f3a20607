package user

import (
	"context"

	"backend/internal/adapters/persistence/repository/abstraction"
	"backend/internal/domain/user/entity"
	"backend/internal/domain/user/repository"
	apperrors "backend/pkg/common/errors"
	"backend/pkg/common/errors/codes"
	database "backend/pkg/infrastructure/database/abstraction"
)

// UserTenantRepository 用户租户关联关系仓储实现
type UserTenantRepository struct {
	*abstraction.BaseRepository
}

// NewUserTenantRepository 创建用户租户关联关系仓储实例
func NewUserTenantRepository(dbManager database.Manager) repository.UserTenantRepository {
	return &UserTenantRepository{
		BaseRepository: abstraction.NewBaseRepository(dbManager),
	}
}

// FindByUserBusinessID 根据用户业务ID查询所有租户关联关系
func (r *UserTenantRepository) FindByUserBusinessID(ctx context.Context, userBusinessID string) ([]*entity.UserTenant, error) {
	var userTenants []*entity.UserTenant
	dataAccess := r.GetDataAccess()

	err := dataAccess.Find(ctx, &userTenants, "user_business_id = ? AND deleted_at IS NULL", userBusinessID)
	if err != nil {
		return nil, apperrors.NewInternal(codes.DatabaseQuery, "查询用户租户关联关系失败").
			WithDetail("user_business_id", userBusinessID).
			Wrap(err).Build()
	}
	return userTenants, nil
}

// FindByTenantBusinessID 根据租户业务ID查询所有用户关联关系
func (r *UserTenantRepository) FindByTenantBusinessID(ctx context.Context, tenantBusinessID string) ([]*entity.UserTenant, error) {
	var userTenants []*entity.UserTenant
	dataAccess := r.GetDataAccess()

	err := dataAccess.Find(ctx, &userTenants, "tenant_business_id = ? AND deleted_at IS NULL", tenantBusinessID)
	if err != nil {
		return nil, apperrors.NewInternal(codes.DatabaseQuery, "查询租户用户关联关系失败").
			WithDetail("tenant_business_id", tenantBusinessID).
			Wrap(err).Build()
	}
	return userTenants, nil
}

// FindByUserAndTenant 根据用户业务ID和租户业务ID查询特定用户租户关联关系
func (r *UserTenantRepository) FindByUserAndTenant(ctx context.Context, userBusinessID, tenantBusinessID string) (*entity.UserTenant, error) {
	var userTenant entity.UserTenant
	dataAccess := r.GetDataAccess()

	err := dataAccess.First(ctx, &userTenant, "user_business_id = ? AND tenant_business_id = ? AND deleted_at IS NULL", userBusinessID, tenantBusinessID)
	if err != nil {
		if isRecordNotFoundError(err) {
			return nil, apperrors.NewNotFound(codes.UserNotFound, "用户租户关联关系不存在").
				WithDetail("user_business_id", userBusinessID).
				WithDetail("tenant_business_id", tenantBusinessID).
				Wrap(err).Build()
		}
		return nil, apperrors.NewInternal(codes.DatabaseQuery, "查询用户租户关联关系失败").
			WithDetail("user_business_id", userBusinessID).
			WithDetail("tenant_business_id", tenantBusinessID).
			Wrap(err).Build()
	}
	return &userTenant, nil
}

// Create 创建用户租户关联关系
func (r *UserTenantRepository) Create(ctx context.Context, userTenant *entity.UserTenant) error {
	dataAccess := r.GetDataAccess()

	if err := dataAccess.Create(ctx, userTenant); err != nil {
		// 检查是否是重复键错误
		if isDuplicateKeyError(err) {
			return apperrors.NewConflict(codes.UserAlreadyExists, "用户租户关联关系已存在").
				WithDetail("user_business_id", userTenant.UserBusinessID).
				WithDetail("tenant_business_id", userTenant.TenantBusinessID).
				Wrap(err).Build()
		}
		return apperrors.NewInternal(codes.DatabaseQuery, "创建用户租户关联关系失败").
			WithDetail("user_business_id", userTenant.UserBusinessID).
			WithDetail("tenant_business_id", userTenant.TenantBusinessID).
			Wrap(err).Build()
	}
	return nil
}

// Delete 根据业务ID删除用户租户关联关系
func (r *UserTenantRepository) Delete(ctx context.Context, businessID string) error {
	queryBuilder := r.GetQueryBuilder()

	err := queryBuilder.
		Where("business_id = ?", businessID).
		Where("deleted_at IS NULL").
		Exec("UPDATE user_tenants SET deleted_at = NOW() WHERE business_id = ? AND deleted_at IS NULL", businessID)

	if err != nil {
		if isRecordNotFoundError(err) {
			return apperrors.NewNotFound(codes.UserNotFound, "用户租户关联关系不存在").
				WithDetail("business_id", businessID).
				Wrap(err).Build()
		}
		return apperrors.NewInternal(codes.DatabaseQuery, "删除用户租户关联关系失败").
			WithDetail("business_id", businessID).
			Wrap(err).Build()
	}
	return nil
}

// 注意：错误处理辅助函数已在user_repository.go中定义，这里复用
