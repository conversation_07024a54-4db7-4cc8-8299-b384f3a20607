package tenant

import (
	"context"
	"time"

	"backend/internal/adapters/persistence/repository/abstraction"
	"backend/internal/domain/tenant/entity"
	"backend/internal/domain/tenant/repository"
	"backend/pkg/adapters/database"
	apperrors "backend/pkg/common/errors"
	"backend/pkg/common/errors/codes"
	dbAbstraction "backend/pkg/infrastructure/database/abstraction"
)

// QuotaRepository 配额仓储实现
type QuotaRepository struct {
	*abstraction.BaseRepository
}

// NewQuotaRepository 创建新的配额仓储实例
func NewQuotaRepository(dbManager dbAbstraction.Manager) repository.TenantQuotaRepository {
	return &QuotaRepository{
		BaseRepository: abstraction.NewBaseRepository(dbManager),
	}
}

// Create 创建配额记录
func (r *QuotaRepository) Create(ctx context.Context, quota *entity.TenantQuota) error {
	dataAccess := r.GetDataAccess()
	if err := dataAccess.Create(ctx, quota); err != nil {
		return database.TranslateDBError(err)
	}
	return nil
}

// GetByTenantID 根据租户ID获取配额记录
func (r *QuotaRepository) GetByTenantID(ctx context.Context, tenantID string) (*entity.TenantQuota, error) {
	var quota entity.TenantQuota
	dataAccess := r.GetDataAccess()

	err := dataAccess.First(ctx, &quota, "tenant_id = ? AND deleted_at IS NULL", tenantID)
	if err != nil {
		if database.IsNotFoundError(err) {
			return nil, apperrors.NewNotFound(codes.ResourceNotFound, "租户配额不存在").
				WithDetail("tenant_id", tenantID).
				Wrap(err).Build()
		}
		return nil, database.TranslateDBError(err)
	}

	return &quota, nil
}

// Update 更新配额记录
func (r *QuotaRepository) Update(ctx context.Context, quota *entity.TenantQuota) error {
	quota.UpdatedAt = time.Now()
	dataAccess := r.GetDataAccess()
	err := dataAccess.Update(ctx, quota)
	if err != nil {
		if database.IsNotFoundError(err) {
			return apperrors.NewNotFound(codes.ResourceNotFound, "租户配额不存在").
				WithDetail("tenant_id", quota.TenantID).
				Wrap(err).Build()
		}
		return database.TranslateDBError(err)
	}
	return nil
}

// Delete 删除配额记录
func (r *QuotaRepository) Delete(ctx context.Context, tenantID string) error {
	queryBuilder := r.GetQueryBuilder()
	err := queryBuilder.
		Where("tenant_id = ?", tenantID).
		Where("deleted_at IS NULL").
		Exec("UPDATE tenant_quotas SET deleted_at = NOW() WHERE tenant_id = ? AND deleted_at IS NULL", tenantID)

	if err != nil {
		if database.IsNotFoundError(err) {
			return apperrors.NewNotFound(codes.ResourceNotFound, "租户配额不存在").
				WithDetail("tenant_id", tenantID).
				Wrap(err).Build()
		}
		return database.TranslateDBError(err)
	}
	return nil
}

// IncrementUserQuota 增加用户配额使用量
func (r *QuotaRepository) IncrementUserQuota(ctx context.Context, tenantID string, count int) error {
	// TODO: 实现配额增减操作
	return nil
}

// DecrementUserQuota 减少用户配额使用量
func (r *QuotaRepository) DecrementUserQuota(ctx context.Context, tenantID string, count int) error {
	// TODO: 实现配额减少操作
	return nil
}

// IncrementStorageQuota 增加存储配额使用量
func (r *QuotaRepository) IncrementStorageQuota(ctx context.Context, tenantID string, sizeInMB int64) error {
	// TODO: 实现存储配额增加操作
	return nil
}

// DecrementStorageQuota 减少存储配额使用量
func (r *QuotaRepository) DecrementStorageQuota(ctx context.Context, tenantID string, sizeInMB int64) error {
	// TODO: 实现存储配额减少操作
	return nil
}

// IncrementAPIQuota 增加API配额使用量
func (r *QuotaRepository) IncrementAPIQuota(ctx context.Context, tenantID string, count int) error {
	// TODO: 实现API配额增加操作
	return nil
}

// GetQuotaUtilization 获取配额使用率
func (r *QuotaRepository) GetQuotaUtilization(ctx context.Context, tenantID string) (*repository.QuotaUtilization, error) {
	// TODO: 实现配额使用率计算
	return &repository.QuotaUtilization{
		TenantID:    tenantID,
		IsOverQuota: false,
		Alerts:      []*entity.QuotaAlert{},
	}, nil
}

// GetOverQuotaTenants 获取超限租户列表
func (r *QuotaRepository) GetOverQuotaTenants(ctx context.Context) ([]string, error) {
	// TODO: 实现超限租户查询
	return []string{}, nil
}

// GetQuotaAlerts 获取配额告警
func (r *QuotaRepository) GetQuotaAlerts(ctx context.Context, tenantID string) ([]*entity.QuotaAlert, error) {
	// TODO: 实现配额告警功能
	return []*entity.QuotaAlert{}, nil
}

// BatchResetMonthlyQuotas 批量重置月度配额
func (r *QuotaRepository) BatchResetMonthlyQuotas(ctx context.Context) error {
	// TODO: 实现批量重置月度配额
	return nil
}

// BatchUpdateQuotaLimits 批量更新配额限制
func (r *QuotaRepository) BatchUpdateQuotaLimits(ctx context.Context, tenantIDs []string, limits repository.QuotaLimits) error {
	// TODO: 实现批量更新配额限制
	return nil
}
