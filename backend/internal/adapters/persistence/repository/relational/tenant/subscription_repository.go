package tenant

import (
	"context"
	"time"

	"backend/internal/adapters/persistence/repository/abstraction"
	"backend/internal/domain/tenant/entity"
	"backend/internal/domain/tenant/repository"
	"backend/pkg/adapters/database"
	apperrors "backend/pkg/common/errors"
	"backend/pkg/common/errors/codes"
	dbAbstraction "backend/pkg/infrastructure/database/abstraction"
)

// SubscriptionRepository 租户订阅仓储实现
type SubscriptionRepository struct {
	*abstraction.BaseRepository
}

// NewSubscriptionRepository 创建租户订阅仓储实例
func NewSubscriptionRepository(dbManager dbAbstraction.Manager) repository.TenantSubscriptionRepository {
	return &SubscriptionRepository{
		BaseRepository: abstraction.NewBaseRepository(dbManager),
	}
}

// Create 创建订阅
func (r *SubscriptionRepository) Create(ctx context.Context, subscription *entity.TenantSubscription) error {
	dataAccess := r.GetDataAccess()
	if err := dataAccess.Create(ctx, subscription); err != nil {
		return database.TranslateDBError(err)
	}
	return nil
}

// GetByID 根据ID获取订阅
func (r *SubscriptionRepository) GetByID(ctx context.Context, id string) (*entity.TenantSubscription, error) {
	var subscription entity.TenantSubscription
	dataAccess := r.GetDataAccess()

	err := dataAccess.First(ctx, &subscription, "business_id = ? AND deleted_at IS NULL", id)
	if err != nil {
		if database.IsNotFoundError(err) {
			return nil, apperrors.NewNotFound(codes.ResourceNotFound, "订阅不存在").
				WithDetail("business_id", id).
				Wrap(err).Build()
		}
		return nil, database.TranslateDBError(err)
	}
	return &subscription, nil
}

// GetByTenantID 根据租户ID获取订阅
func (r *SubscriptionRepository) GetByTenantID(ctx context.Context, tenantID string) (*entity.TenantSubscription, error) {
	var subscription entity.TenantSubscription
	dataAccess := r.GetDataAccess()

	err := dataAccess.First(ctx, &subscription, "tenant_id = ? AND deleted_at IS NULL", tenantID)
	if err != nil {
		if database.IsNotFoundError(err) {
			return nil, apperrors.NewNotFound(codes.ResourceNotFound, "订阅不存在").
				WithDetail("tenant_id", tenantID).
				Wrap(err).Build()
		}
		return nil, database.TranslateDBError(err)
	}
	return &subscription, nil
}

// Update 更新订阅
func (r *SubscriptionRepository) Update(ctx context.Context, subscription *entity.TenantSubscription) error {
	subscription.UpdatedAt = time.Now()
	dataAccess := r.GetDataAccess()
	err := dataAccess.Update(ctx, subscription)
	if err != nil {
		if database.IsNotFoundError(err) {
			return apperrors.NewNotFound(codes.ResourceNotFound, "订阅不存在").
				WithDetail("business_id", subscription.BusinessID).
				Wrap(err).Build()
		}
		return database.TranslateDBError(err)
	}
	return nil
}

// Delete 删除订阅
func (r *SubscriptionRepository) Delete(ctx context.Context, id string) error {
	queryBuilder := r.GetQueryBuilder()
	err := queryBuilder.
		Where("business_id = ?", id).
		Where("deleted_at IS NULL").
		Exec("UPDATE tenant_subscriptions SET deleted_at = NOW() WHERE business_id = ? AND deleted_at IS NULL", id)

	if err != nil {
		if database.IsNotFoundError(err) {
			return apperrors.NewNotFound(codes.ResourceNotFound, "订阅不存在").
				WithDetail("business_id", id).
				Wrap(err).Build()
		}
		return database.TranslateDBError(err)
	}
	return nil
}

// List 获取订阅列表
func (r *SubscriptionRepository) List(ctx context.Context, filter repository.SubscriptionFilter, pagination repository.TenantPagination) ([]*entity.TenantSubscription, *repository.TenantListResult, error) {
	// TODO: 实现订阅列表查询
	return []*entity.TenantSubscription{}, &repository.TenantListResult{}, nil
}

// GetActiveSubscriptions 获取活跃订阅
func (r *SubscriptionRepository) GetActiveSubscriptions(ctx context.Context) ([]*entity.TenantSubscription, error) {
	// TODO: 实现活跃订阅查询
	return []*entity.TenantSubscription{}, nil
}

// GetExpiringSubscriptions 获取即将过期的订阅
func (r *SubscriptionRepository) GetExpiringSubscriptions(ctx context.Context, days int) ([]*entity.TenantSubscription, error) {
	// TODO: 实现即将过期订阅查询
	return []*entity.TenantSubscription{}, nil
}

// GetExpiredSubscriptions 获取已过期的订阅
func (r *SubscriptionRepository) GetExpiredSubscriptions(ctx context.Context) ([]*entity.TenantSubscription, error) {
	// TODO: 实现已过期订阅查询
	return []*entity.TenantSubscription{}, nil
}

// GetTrialSubscriptions 获取试用订阅
func (r *SubscriptionRepository) GetTrialSubscriptions(ctx context.Context) ([]*entity.TenantSubscription, error) {
	// TODO: 实现试用订阅查询
	return []*entity.TenantSubscription{}, nil
}

// GetByPlanID 根据计划ID获取订阅
func (r *SubscriptionRepository) GetByPlanID(ctx context.Context, planID string) ([]*entity.TenantSubscription, error) {
	// TODO: 实现按计划ID查询订阅
	return []*entity.TenantSubscription{}, nil
}

// GetRenewableSubscriptions 获取可续费的订阅
func (r *SubscriptionRepository) GetRenewableSubscriptions(ctx context.Context, days int) ([]*entity.TenantSubscription, error) {
	// TODO: 实现可续费订阅查询
	return []*entity.TenantSubscription{}, nil
}

// MarkAsRenewed 标记为已续费
func (r *SubscriptionRepository) MarkAsRenewed(ctx context.Context, subscriptionID string, newEndDate time.Time) error {
	// TODO: 实现标记为已续费
	return nil
}

// GetSubscriptionStatistics 获取订阅统计
func (r *SubscriptionRepository) GetSubscriptionStatistics(ctx context.Context) (*repository.SubscriptionStatistics, error) {
	// TODO: 实现订阅统计
	return &repository.SubscriptionStatistics{
		StatusCounts:       make(map[entity.SubscriptionStatus]int64),
		PlanCounts:         make(map[string]int64),
		CurrencyCounts:     make(map[string]int64),
		BillingCycleCounts: make(map[string]int64),
	}, nil
}

// GetRevenueStatistics 获取收入统计
func (r *SubscriptionRepository) GetRevenueStatistics(ctx context.Context, startDate, endDate time.Time) (*repository.RevenueStatistics, error) {
	// TODO: 实现收入统计
	return &repository.RevenueStatistics{
		CurrencyRevenue: make(map[string]float64),
		PlanRevenue:     make(map[string]float64),
	}, nil
}

// TODO: 实现过滤和分页逻辑
// 这些方法需要重新实现以使用database.Manager的抽象接口
