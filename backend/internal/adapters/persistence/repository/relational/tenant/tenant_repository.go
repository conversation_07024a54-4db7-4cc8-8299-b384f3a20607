package tenant

import (
	"context"
	"time"

	"backend/internal/adapters/persistence/repository/abstraction"
	"backend/internal/domain/tenant/entity"
	"backend/internal/domain/tenant/repository"
	"backend/internal/domain/tenant/valueobject"
	"backend/pkg/adapters/database"
	apperrors "backend/pkg/common/errors"
	"backend/pkg/common/errors/codes"
	dbAbstraction "backend/pkg/infrastructure/database/abstraction"
)

// TenantRepository 租户仓储实现
type TenantRepository struct {
	*abstraction.BaseRepository
}

// NewTenantRepository 创建租户仓储实例
func NewTenantRepository(dbManager dbAbstraction.Manager) repository.TenantRepository {
	return &TenantRepository{
		BaseRepository: abstraction.NewBaseRepository(dbManager),
	}
}

// Create 创建租户
func (r *TenantRepository) Create(ctx context.Context, tenant *entity.Tenant) error {
	dataAccess := r.GetDataAccess()
	if err := dataAccess.Create(ctx, tenant); err != nil {
		// 检查是否是重复键错误
		if database.IsConstraintError(err) {
			return apperrors.NewConflict(codes.ResourceAlreadyExists, "租户已存在").
				WithDetail("business_id", tenant.BusinessID).
				WithDetail("domain", tenant.Domain).
				Wrap(err).Build()
		}
		return database.TranslateDBError(err)
	}
	return nil
}

// GetByID 根据业务ID获取租户
func (r *TenantRepository) GetByID(ctx context.Context, id string) (*entity.Tenant, error) {
	var tenant entity.Tenant
	dataAccess := r.GetDataAccess()

	err := dataAccess.First(ctx, &tenant, "business_id = ? AND deleted_at IS NULL", id)
	if err != nil {
		if database.IsNotFoundError(err) {
			return nil, apperrors.NewNotFound(codes.TenantNotFound, "租户不存在").
				WithDetail("business_id", id).
				Wrap(err).Build()
		}
		return nil, database.TranslateDBError(err)
	}
	return &tenant, nil
}

// GetByDomain 根据域名获取租户
func (r *TenantRepository) GetByDomain(ctx context.Context, domain string) (*entity.Tenant, error) {
	var tenant entity.Tenant
	dataAccess := r.GetDataAccess()

	err := dataAccess.First(ctx, &tenant, "domain = ? AND deleted_at IS NULL", domain)
	if err != nil {
		if database.IsNotFoundError(err) {
			return nil, apperrors.NewNotFound(codes.TenantNotFound, "租户不存在").
				WithDetail("domain", domain).
				Wrap(err).Build()
		}
		return nil, database.TranslateDBError(err)
	}
	return &tenant, nil
}

// Update 更新租户
func (r *TenantRepository) Update(ctx context.Context, tenant *entity.Tenant) error {
	tenant.UpdatedAt = time.Now()

	dataAccess := r.GetDataAccess()
	err := dataAccess.Update(ctx, tenant)

	if err != nil {
		if database.IsNotFoundError(err) {
			return apperrors.NewNotFound(codes.TenantNotFound, "租户不存在").
				WithDetail("business_id", tenant.BusinessID).
				Wrap(err).Build()
		}
		return database.TranslateDBError(err)
	}

	return nil
}

// Delete 删除租户（软删除）
func (r *TenantRepository) Delete(ctx context.Context, id string) error {
	queryBuilder := r.GetQueryBuilder()
	err := queryBuilder.
		Where("business_id = ?", id).
		Where("deleted_at IS NULL").
		Exec("UPDATE tenants SET deleted_at = NOW() WHERE business_id = ? AND deleted_at IS NULL", id)

	if err != nil {
		if database.IsNotFoundError(err) {
			return apperrors.NewNotFound(codes.TenantNotFound, "租户不存在").
				WithDetail("business_id", id).
				Wrap(err).Build()
		}
		return database.TranslateDBError(err)
	}

	return nil
}

// List 获取租户列表
func (r *TenantRepository) List(ctx context.Context, filter repository.TenantFilter, pagination repository.TenantPagination) ([]*entity.Tenant, *repository.TenantListResult, error) {
	var tenants []*entity.Tenant

	// 简化实现：直接查询所有租户
	dataAccess := r.GetDataAccess()
	err := dataAccess.Find(ctx, &tenants, "deleted_at IS NULL")
	if err != nil {
		return nil, nil, database.TranslateDBError(err)
	}

	// 获取总数
	total, err := dataAccess.Count(ctx, &entity.Tenant{}, "deleted_at IS NULL")
	if err != nil {
		return nil, nil, database.TranslateDBError(err)
	}

	// 构建分页结果
	result := &repository.TenantListResult{
		Total:    total,
		Page:     pagination.Page,
		PageSize: pagination.PageSize,
	}

	if pagination.PageSize > 0 {
		result.TotalPages = int((total + int64(pagination.PageSize) - 1) / int64(pagination.PageSize))
		result.HasNext = pagination.Page < result.TotalPages
		result.HasPrevious = pagination.Page > 1
	}

	return tenants, result, nil
}

// Count 统计租户数量
func (r *TenantRepository) Count(ctx context.Context, filter repository.TenantFilter) (int64, error) {
	dataAccess := r.GetDataAccess()
	count, err := dataAccess.Count(ctx, &entity.Tenant{}, "deleted_at IS NULL")
	if err != nil {
		return 0, database.TranslateDBError(err)
	}
	return count, nil
}

// Exists 检查租户是否存在
func (r *TenantRepository) Exists(ctx context.Context, id string) (bool, error) {
	dataAccess := r.GetDataAccess()
	exists, err := dataAccess.Exists(ctx, &entity.Tenant{}, "business_id = ? AND deleted_at IS NULL", id)
	if err != nil {
		return false, database.TranslateDBError(err)
	}
	return exists, nil
}

// ExistsByDomain 检查域名是否存在
func (r *TenantRepository) ExistsByDomain(ctx context.Context, domain string) (bool, error) {
	dataAccess := r.GetDataAccess()
	exists, err := dataAccess.Exists(ctx, &entity.Tenant{}, "domain = ? AND deleted_at IS NULL", domain)
	if err != nil {
		return false, database.TranslateDBError(err)
	}
	return exists, nil
}

// GetByStatus 根据状态获取租户列表
func (r *TenantRepository) GetByStatus(ctx context.Context, status valueobject.TenantStatus, pagination repository.TenantPagination) ([]*entity.Tenant, error) {
	var tenants []*entity.Tenant

	queryBuilder := r.GetQueryBuilder()
	err := queryBuilder.
		Where("status = ?", status).
		Where("deleted_at IS NULL").
		Order("created_at DESC").
		Find(&tenants)

	if err != nil {
		return nil, database.TranslateDBError(err)
	}

	return tenants, nil
}

// GetByType 根据类型获取租户列表
func (r *TenantRepository) GetByType(ctx context.Context, tenantType valueobject.TenantType, pagination repository.TenantPagination) ([]*entity.Tenant, error) {
	var tenants []*entity.Tenant

	queryBuilder := r.GetQueryBuilder()
	err := queryBuilder.
		Where("type = ?", tenantType).
		Where("deleted_at IS NULL").
		Order("created_at DESC").
		Find(&tenants)

	if err != nil {
		return nil, database.TranslateDBError(err)
	}

	return tenants, nil
}

// GetExpiring 获取即将过期的租户
func (r *TenantRepository) GetExpiring(ctx context.Context, days int) ([]*entity.Tenant, error) {
	var tenants []*entity.Tenant

	expiringDate := time.Now().AddDate(0, 0, days)

	dataAccess := r.GetDataAccess()
	err := dataAccess.Find(ctx, &tenants,
		"subscription_end IS NOT NULL AND subscription_end <= ? AND subscription_end > ? AND deleted_at IS NULL",
		expiringDate, time.Now())

	if err != nil {
		return nil, database.TranslateDBError(err)
	}

	return tenants, nil
}

// GetExpired 获取已过期的租户
func (r *TenantRepository) GetExpired(ctx context.Context) ([]*entity.Tenant, error) {
	var tenants []*entity.Tenant

	dataAccess := r.GetDataAccess()
	err := dataAccess.Find(ctx, &tenants,
		"subscription_end IS NOT NULL AND subscription_end < ? AND deleted_at IS NULL", time.Now())

	if err != nil {
		return nil, database.TranslateDBError(err)
	}

	return tenants, nil
}

// BatchUpdateStatus 批量更新租户状态
func (r *TenantRepository) BatchUpdateStatus(ctx context.Context, tenantIDs []string, status valueobject.TenantStatus) error {
	queryBuilder := r.GetQueryBuilder()
	err := queryBuilder.
		Where("business_id IN ?", tenantIDs).
		Where("deleted_at IS NULL").
		Exec("UPDATE tenants SET status = ?, updated_at = NOW() WHERE business_id IN ? AND deleted_at IS NULL",
			status, tenantIDs)

	if err != nil {
		return database.TranslateDBError(err)
	}

	return nil
}

// BatchDelete 批量删除租户
func (r *TenantRepository) BatchDelete(ctx context.Context, tenantIDs []string) error {
	queryBuilder := r.GetQueryBuilder()
	err := queryBuilder.
		Where("business_id IN ?", tenantIDs).
		Where("deleted_at IS NULL").
		Exec("UPDATE tenants SET deleted_at = NOW() WHERE business_id IN ? AND deleted_at IS NULL", tenantIDs)

	if err != nil {
		return database.TranslateDBError(err)
	}

	return nil
}

// GetStatistics 获取租户统计信息
func (r *TenantRepository) GetStatistics(ctx context.Context) (*repository.TenantStatistics, error) {
	stats := &repository.TenantStatistics{
		StatusCounts: make(map[valueobject.TenantStatus]int64),
		TypeCounts:   make(map[valueobject.TenantType]int64),
	}

	dataAccess := r.GetDataAccess()

	// 获取总数
	total, err := dataAccess.Count(ctx, &entity.Tenant{}, "deleted_at IS NULL")
	if err != nil {
		return nil, database.TranslateDBError(err)
	}
	stats.TotalTenants = total

	// 获取活跃租户数
	active, err := dataAccess.Count(ctx, &entity.Tenant{},
		"status = ? AND deleted_at IS NULL", valueobject.TenantStatusActive)
	if err != nil {
		return nil, database.TranslateDBError(err)
	}
	stats.ActiveTenants = active

	return stats, nil
}

// GetStatusDistribution 获取状态分布
func (r *TenantRepository) GetStatusDistribution(ctx context.Context) (map[valueobject.TenantStatus]int64, error) {
	// TODO: 实现复杂的聚合查询，暂时返回空结果
	distribution := make(map[valueobject.TenantStatus]int64)
	return distribution, nil
}

// GetTypeDistribution 获取类型分布
func (r *TenantRepository) GetTypeDistribution(ctx context.Context) (map[valueobject.TenantType]int64, error) {
	// TODO: 实现复杂的聚合查询，暂时返回空结果
	distribution := make(map[valueobject.TenantType]int64)
	return distribution, nil
}

// FindWithPagination 新分页接口实现
func (r *TenantRepository) FindWithPagination(ctx context.Context, req *repository.PaginationRequest, filter repository.TenantFilter) ([]*entity.Tenant, error) {
	var tenants []*entity.Tenant

	// 简化实现：直接查询所有租户
	dataAccess := r.GetDataAccess()
	err := dataAccess.Find(ctx, &tenants, "deleted_at IS NULL")
	if err != nil {
		return nil, database.TranslateDBError(err)
	}

	return tenants, nil
}

// CountWithFilter 新计数接口实现
func (r *TenantRepository) CountWithFilter(ctx context.Context, filter repository.TenantFilter) (int64, error) {
	dataAccess := r.GetDataAccess()
	count, err := dataAccess.Count(ctx, &entity.Tenant{}, "deleted_at IS NULL")
	if err != nil {
		return 0, database.TranslateDBError(err)
	}
	return count, nil
}

// TODO: 实现分页和排序逻辑
// 这些方法需要重新实现以使用database.Manager的抽象接口
