package redis

import (
	"context"
	"fmt"
	"strings"
	"time"

	"github.com/redis/go-redis/v9"

	"backend/pkg/infrastructure/cache"
	redisClient "backend/pkg/infrastructure/cache/redis"
	"backend/pkg/infrastructure/config"
	"backend/pkg/infrastructure/logger"
)

// CacheAdapter Redis缓存适配器
// 实现所有缓存接口，提供完整的缓存功能
type CacheAdapter struct {
	client      *redisClient.Client
	redisClient *redis.Client
	config      *config.CacheRedisConfig
	logger      logger.Logger
	serializer  cache.Serializer
	compressor  cache.Compressor
	keyBuilder  cache.KeyBuilder
	metrics     cache.MetricsCollector
}

// NewCacheAdapter 创建Redis缓存适配器
func NewCacheAdapter(
	client *redisClient.Client,
	redisClient *redis.Client,
	config *config.CacheRedisConfig,
	logger logger.Logger,
	serializer cache.Serializer,
	compressor cache.Compressor,
	keyBuilder cache.KeyBuilder,
	metrics cache.MetricsCollector,
) *CacheAdapter {
	return &CacheAdapter{
		client:      client,
		redisClient: redisClient,
		config:      config,
		logger:      logger,
		serializer:  serializer,
		compressor:  compressor,
		keyBuilder:  keyBuilder,
		metrics:     metrics,
	}
}

// Get 获取缓存值
func (ca *CacheAdapter) Get(ctx context.Context, key string) ([]byte, error) {
	// 构建完整的缓存键
	fullKey := ca.keyBuilder.Build(key)

	// 从Redis获取数据
	data, err := ca.client.Get(ctx, fullKey)
	if err != nil {
		return nil, err
	}

	// 解压缩数据（如果启用）
	if ca.config.Compression {
		return ca.decompressData(data)
	}

	return data, nil
}

// Set 设置缓存值
func (ca *CacheAdapter) Set(ctx context.Context, key string, value []byte, ttl time.Duration) error {
	// 构建完整的缓存键
	fullKey := ca.keyBuilder.Build(key)

	// 压缩数据（如果启用）
	data := value
	if ca.config.Compression {
		compressed, err := ca.compressData(value)
		if err != nil {
			ca.logger.Error(ctx, "数据压缩失败", "key", key, "error", err)
			return fmt.Errorf("数据压缩失败: %w", err)
		}
		data = compressed
	}

	// 设置到Redis
	return ca.client.Set(ctx, fullKey, data, ttl)
}

// Del 删除缓存
func (ca *CacheAdapter) Del(ctx context.Context, keys ...string) error {
	if len(keys) == 0 {
		return nil
	}

	// 构建完整的缓存键
	fullKeys := make([]string, len(keys))
	for i, key := range keys {
		fullKeys[i] = ca.keyBuilder.Build(key)
	}

	return ca.client.Del(ctx, fullKeys...)
}

// Exists 检查键是否存在
func (ca *CacheAdapter) Exists(ctx context.Context, key string) (bool, error) {
	fullKey := ca.keyBuilder.Build(key)
	return ca.client.Exists(ctx, fullKey)
}

// TTL 获取键的剩余生存时间
func (ca *CacheAdapter) TTL(ctx context.Context, key string) (time.Duration, error) {
	fullKey := ca.keyBuilder.Build(key)
	return ca.client.TTL(ctx, fullKey)
}

// SetTTL 设置键的生存时间
func (ca *CacheAdapter) SetTTL(ctx context.Context, key string, ttl time.Duration) error {
	fullKey := ca.keyBuilder.Build(key)
	return ca.client.SetTTL(ctx, fullKey, ttl)
}

// Close 关闭缓存连接
func (ca *CacheAdapter) Close() error {
	return ca.client.Close()
}

// BatchGet 批量获取缓存值
func (ca *CacheAdapter) BatchGet(ctx context.Context, keys []string) (map[string][]byte, error) {
	if len(keys) == 0 {
		return make(map[string][]byte), nil
	}

	// 构建完整的缓存键
	fullKeys := make([]string, len(keys))
	keyMap := make(map[string]string) // fullKey -> originalKey
	for i, key := range keys {
		fullKey := ca.keyBuilder.Build(key)
		fullKeys[i] = fullKey
		keyMap[fullKey] = key
	}

	// 批量获取数据
	results, err := ca.client.BatchGet(ctx, fullKeys)
	if err != nil {
		return nil, err
	}

	// 转换键名并解压缩数据
	finalResults := make(map[string][]byte)
	for fullKey, data := range results {
		originalKey := keyMap[fullKey]

		// 解压缩数据（如果启用）
		if ca.config.Compression {
			decompressed, err := ca.decompressData(data)
			if err != nil {
				ca.logger.Error(ctx, "数据解压缩失败", "key", originalKey, "error", err)
				continue
			}
			finalResults[originalKey] = decompressed
		} else {
			finalResults[originalKey] = data
		}
	}

	return finalResults, nil
}

// BatchSet 批量设置缓存值
func (ca *CacheAdapter) BatchSet(ctx context.Context, items map[string]cache.CacheItem) error {
	if len(items) == 0 {
		return nil
	}

	// 转换为完整键名并处理压缩
	fullItems := make(map[string]cache.CacheItem)
	for key, item := range items {
		fullKey := ca.keyBuilder.Build(key)

		// 压缩数据（如果启用）
		data := item.Value
		if ca.config.Compression {
			compressed, err := ca.compressData(item.Value)
			if err != nil {
				ca.logger.Error(ctx, "数据压缩失败", "key", key, "error", err)
				return fmt.Errorf("数据压缩失败: %w", err)
			}
			data = compressed
		}

		fullItems[fullKey] = cache.CacheItem{
			Key:   fullKey,
			Value: data,
			TTL:   item.TTL,
		}
	}

	return ca.client.BatchSet(ctx, fullItems)
}

// BatchDel 批量删除缓存
func (ca *CacheAdapter) BatchDel(ctx context.Context, keys []string) error {
	return ca.Del(ctx, keys...)
}

// Keys 根据模式获取键列表
func (ca *CacheAdapter) Keys(ctx context.Context, pattern string) ([]string, error) {
	// 构建完整的模式
	fullPattern := ca.keyBuilder.Build(pattern)

	fullKeys, err := ca.client.Keys(ctx, fullPattern)
	if err != nil {
		return nil, err
	}

	// 移除键前缀，返回原始键名
	originalKeys := make([]string, 0, len(fullKeys))
	for _, fullKey := range fullKeys {
		if originalKey := ca.extractOriginalKey(fullKey); originalKey != "" {
			originalKeys = append(originalKeys, originalKey)
		}
	}

	return originalKeys, nil
}

// Scan 扫描键，支持游标和分页
func (ca *CacheAdapter) Scan(ctx context.Context, cursor uint64, pattern string, count int64) ([]string, uint64, error) {
	// 构建完整的模式
	fullPattern := ca.keyBuilder.Build(pattern)

	fullKeys, newCursor, err := ca.client.Scan(ctx, cursor, fullPattern, count)
	if err != nil {
		return nil, 0, err
	}

	// 移除键前缀，返回原始键名
	originalKeys := make([]string, 0, len(fullKeys))
	for _, fullKey := range fullKeys {
		if originalKey := ca.extractOriginalKey(fullKey); originalKey != "" {
			originalKeys = append(originalKeys, originalKey)
		}
	}

	return originalKeys, newCursor, nil
}

// SAdd 添加元素到集合
func (ca *CacheAdapter) SAdd(ctx context.Context, key string, members ...string) error {
	fullKey := ca.keyBuilder.Build(key)
	return ca.client.SAdd(ctx, fullKey, members...)
}

// SRem 从集合中移除元素
func (ca *CacheAdapter) SRem(ctx context.Context, key string, members ...string) error {
	fullKey := ca.keyBuilder.Build(key)
	return ca.client.SRem(ctx, fullKey, members...)
}

// SMembers 获取集合中的所有元素
func (ca *CacheAdapter) SMembers(ctx context.Context, key string) ([]string, error) {
	fullKey := ca.keyBuilder.Build(key)
	return ca.client.SMembers(ctx, fullKey)
}

// SCard 获取集合中的元素数量
func (ca *CacheAdapter) SCard(ctx context.Context, key string) (int64, error) {
	fullKey := ca.keyBuilder.Build(key)
	return ca.client.SCard(ctx, fullKey)
}

// FlushDB 清空当前数据库
func (ca *CacheAdapter) FlushDB(ctx context.Context) error {
	return ca.client.FlushDB(ctx)
}

// Info 获取缓存统计信息
func (ca *CacheAdapter) Info(ctx context.Context) (*cache.CacheInfo, error) {
	return ca.client.Info(ctx)
}

// Ping 测试连接
func (ca *CacheAdapter) Ping(ctx context.Context) error {
	return ca.client.Ping(ctx)
}

// GetObject 获取对象并反序列化到目标类型
func (ca *CacheAdapter) GetObject(ctx context.Context, key string, dest interface{}) error {
	// 获取原始数据
	data, err := ca.Get(ctx, key)
	if err != nil {
		return err
	}

	// 反序列化
	if err := ca.serializer.Deserialize(data, dest); err != nil {
		ca.logger.Error(ctx, "反序列化失败", "key", key, "error", err)
		return cache.ErrDeserializeFailed
	}

	return nil
}

// SetObject 序列化对象并设置到缓存
func (ca *CacheAdapter) SetObject(ctx context.Context, key string, obj interface{}, ttl time.Duration) error {
	// 序列化对象
	data, err := ca.serializer.Serialize(obj)
	if err != nil {
		ca.logger.Error(ctx, "序列化失败", "key", key, "error", err)
		return cache.ErrSerializeFailed
	}

	// 设置到缓存
	return ca.Set(ctx, key, data, ttl)
}

// Lock 获取分布式锁
func (ca *CacheAdapter) Lock(ctx context.Context, key string, ttl time.Duration) (*cache.Lock, error) {
	return ca.lock(ctx, key, ttl, true)
}

// TryLock 尝试获取分布式锁
func (ca *CacheAdapter) TryLock(ctx context.Context, key string, ttl time.Duration) (*cache.Lock, error) {
	return ca.lock(ctx, key, ttl, false)
}

// lock 内部锁实现
func (ca *CacheAdapter) lock(ctx context.Context, key string, ttl time.Duration, blocking bool) (*cache.Lock, error) {
	lockKey := ca.keyBuilder.BuildWithPrefix("lock", key)
	lockValue := ca.generateLockValue()

	// 尝试获取锁
	for {
		success, err := ca.redisClient.SetNX(ctx, lockKey, lockValue, ttl).Result()
		if err != nil {
			ca.logger.Error(ctx, "获取分布式锁失败", "key", key, "error", err)
			return nil, fmt.Errorf("获取分布式锁失败: %w", err)
		}

		if success {
			// 成功获取锁
			return &cache.Lock{
				Key:   lockKey,
				Value: lockValue,
				TTL:   ttl,
			}, nil
		}

		if !blocking {
			// 非阻塞模式，直接返回失败
			return nil, fmt.Errorf("锁已被占用: %s", key)
		}

		// 阻塞模式，等待一段时间后重试
		select {
		case <-ctx.Done():
			return nil, ctx.Err()
		case <-time.After(50 * time.Millisecond):
			// 继续重试
		}
	}
}

// Publish 发布消息
func (ca *CacheAdapter) Publish(ctx context.Context, channel string, message []byte) error {
	err := ca.redisClient.Publish(ctx, channel, message).Err()
	if err != nil {
		ca.logger.Error(ctx, "发布消息失败", "channel", channel, "error", err)
		return fmt.Errorf("发布消息失败: %w", err)
	}

	return nil
}

// Subscribe 订阅消息
func (ca *CacheAdapter) Subscribe(ctx context.Context, channel string) (<-chan *cache.Message, error) {
	pubsub := ca.redisClient.Subscribe(ctx, channel)

	// 创建消息通道
	msgChan := make(chan *cache.Message, 100)

	// 启动goroutine处理消息
	go func() {
		defer close(msgChan)
		defer pubsub.Close()

		ch := pubsub.Channel()
		for {
			select {
			case <-ctx.Done():
				return
			case msg, ok := <-ch:
				if !ok {
					return
				}

				cacheMsg := &cache.Message{
					Channel: msg.Channel,
					Pattern: msg.Pattern,
					Payload: []byte(msg.Payload),
				}

				select {
				case msgChan <- cacheMsg:
				case <-ctx.Done():
					return
				}
			}
		}
	}()

	return msgChan, nil
}

// compressData 压缩数据
func (ca *CacheAdapter) compressData(data []byte) ([]byte, error) {
	if ca.compressor == nil {
		return data, nil
	}
	return ca.compressor.Compress(data)
}

// decompressData 解压缩数据
func (ca *CacheAdapter) decompressData(data []byte) ([]byte, error) {
	if ca.compressor == nil {
		return data, nil
	}
	return ca.compressor.Decompress(data)
}

// extractOriginalKey 从完整键名中提取原始键名
func (ca *CacheAdapter) extractOriginalKey(fullKey string) string {
	prefix := ca.config.KeyPrefix + ca.config.KeySeparator
	if strings.HasPrefix(fullKey, prefix) {
		return strings.TrimPrefix(fullKey, prefix)
	}
	return fullKey
}

// generateLockValue 生成锁值
func (ca *CacheAdapter) generateLockValue() string {
	return fmt.Sprintf("%d", time.Now().UnixNano())
}
