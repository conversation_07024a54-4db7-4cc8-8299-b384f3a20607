package redis

import (
	"encoding/json"
	"fmt"

	"backend/pkg/infrastructure/cache"
)

// JSONSerializer JSON序列化器
type JSONSerializer struct{}

// NewJSONSerializer 创建JSON序列化器
func NewJSONSerializer() cache.Serializer {
	return &JSONSerializer{}
}

// Serialize 序列化对象
func (s *JSONSerializer) Serialize(obj interface{}) ([]byte, error) {
	data, err := json.Marshal(obj)
	if err != nil {
		return nil, fmt.Errorf("JSON序列化失败: %w", err)
	}
	return data, nil
}

// Deserialize 反序列化对象
func (s *JSONSerializer) Deserialize(data []byte, obj interface{}) error {
	if err := json.Unmarshal(data, obj); err != nil {
		return fmt.Errorf("JSON反序列化失败: %w", err)
	}
	return nil
}

// ContentType 获取内容类型
func (s *JSONSerializer) ContentType() string {
	return "application/json"
}

// ByteSerializer 字节序列化器（无序列化）
type ByteSerializer struct{}

// NewByteSerializer 创建字节序列化器
func NewByteSerializer() cache.Serializer {
	return &ByteSerializer{}
}

// Serialize 序列化对象（直接返回字节）
func (s *ByteSerializer) Serialize(obj interface{}) ([]byte, error) {
	switch v := obj.(type) {
	case []byte:
		return v, nil
	case string:
		return []byte(v), nil
	default:
		return nil, fmt.Errorf("ByteSerializer只支持[]byte和string类型，得到: %T", obj)
	}
}

// Deserialize 反序列化对象（直接返回字节）
func (s *ByteSerializer) Deserialize(data []byte, obj interface{}) error {
	switch v := obj.(type) {
	case *[]byte:
		*v = data
		return nil
	case *string:
		*v = string(data)
		return nil
	default:
		return fmt.Errorf("ByteSerializer只支持*[]byte和*string类型，得到: %T", obj)
	}
}

// ContentType 获取内容类型
func (s *ByteSerializer) ContentType() string {
	return "application/octet-stream"
}

// SerializerFactory 序列化器工厂
type SerializerFactory struct{}

// NewSerializerFactory 创建序列化器工厂
func NewSerializerFactory() *SerializerFactory {
	return &SerializerFactory{}
}

// CreateSerializer 创建序列化器
func (f *SerializerFactory) CreateSerializer(serializationType string) (cache.Serializer, error) {
	switch serializationType {
	case "json":
		return NewJSONSerializer(), nil
	case "byte", "bytes":
		return NewByteSerializer(), nil
	default:
		return nil, fmt.Errorf("不支持的序列化类型: %s", serializationType)
	}
}

// GetSupportedTypes 获取支持的序列化类型列表
func (f *SerializerFactory) GetSupportedTypes() []string {
	return []string{"json", "byte", "bytes"}
}
