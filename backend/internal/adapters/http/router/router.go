package router

import (
	"backend/internal/adapters/http/handler"
	"backend/internal/adapters/http/middleware"
	"backend/pkg/infrastructure/logger"
	"backend/pkg/infrastructure/monitoring"

	"github.com/gin-gonic/gin"
	swaggerFiles "github.com/swaggo/files"
	ginSwagger "github.com/swaggo/gin-swagger"
)

// Router 路由器
type Router struct {
	engine              *gin.Engine
	logger              logger.Logger
	tracingManager      *monitoring.TracingManager
	metricsManager      *monitoring.MetricsManager
	healthHandler       *handler.HealthHandler
	metricsHandler      *handler.MetricsHandler
	debugHandler        *handler.DebugHandler
	monitoringHandler   *handler.MonitoringHandler
	demoHandler         *handler.DemoHandler
	authHandler         *handler.AuthHandler
	authMiddleware      *middleware.AuthMiddleware
	userHandler         *handler.UserHandler
	tenantHandler       *handler.TenantHandler
	subscriptionHandler *handler.SubscriptionHandler
}

// NewRouter 创建新的路由器
func NewRouter(
	logger logger.Logger,
	tracingManager *monitoring.TracingManager,
	metricsManager *monitoring.MetricsManager,
	healthHandler *handler.HealthHandler,
	metricsHandler *handler.MetricsHandler,
	debugHandler *handler.DebugHandler,
	monitoringHandler *handler.MonitoringHandler,
	demoHandler *handler.DemoHandler,
	authHandler *handler.AuthHandler,
	authMiddleware *middleware.AuthMiddleware,
	userHandler *handler.UserHandler,
	tenantHandler *handler.TenantHandler,
	subscriptionHandler *handler.SubscriptionHandler,
) *Router {
	// 设置Gin模式
	gin.SetMode(gin.ReleaseMode)

	engine := gin.New()

	// 添加中间件 - 错误处理中间件应该是第一个
	engine.Use(middleware.ErrorMiddleware(logger))
	engine.Use(middleware.RequestIDMiddleware(logger))
	engine.Use(middleware.TracingMiddleware(tracingManager))
	engine.Use(middleware.MetricsMiddleware(metricsManager))
	engine.Use(middleware.LoggingMiddleware(logger))

	return &Router{
		engine:              engine,
		logger:              logger,
		tracingManager:      tracingManager,
		metricsManager:      metricsManager,
		healthHandler:       healthHandler,
		metricsHandler:      metricsHandler,
		debugHandler:        debugHandler,
		monitoringHandler:   monitoringHandler,
		demoHandler:         demoHandler,
		authHandler:         authHandler,
		authMiddleware:      authMiddleware,
		userHandler:         userHandler,
		tenantHandler:       tenantHandler,
		subscriptionHandler: subscriptionHandler,
	}
}

// SetupRoutes 设置路由
func (r *Router) SetupRoutes() {
	// 健康检查路由
	r.engine.GET("/health", r.healthHandler.Health)
	r.engine.GET("/ready", r.healthHandler.Ready)

	// 指标路由
	r.engine.GET("/metrics", r.metricsHandler.Metrics)
	r.engine.GET("/health/metrics", r.metricsHandler.HealthMetrics)

	// 调试路由（仅在开发环境启用）
	debugGroup := r.engine.Group("/debug")
	{
		debugGroup.GET("/info", r.debugHandler.DebugInfo)
		debugGroup.GET("/context", r.debugHandler.ContextInfo)
		debugGroup.GET("/test/logging", r.debugHandler.TestLogging)
		debugGroup.GET("/test/metrics", r.debugHandler.TestMetrics)
		debugGroup.GET("/test/tracing", r.debugHandler.TestTracing)
		debugGroup.GET("/test/error", r.debugHandler.TestError)
		debugGroup.GET("/test/performance", r.debugHandler.TestPerformance)
	}

	// 监控路由
	monitoringGroup := r.engine.Group("/monitoring")
	{
		monitoringGroup.GET("/status", r.monitoringHandler.GetMonitoringStatus)
		monitoringGroup.GET("/health", r.monitoringHandler.GetSystemHealth)
		monitoringGroup.GET("/performance", r.monitoringHandler.GetPerformanceMetrics)
		monitoringGroup.GET("/config", r.monitoringHandler.GetMonitoringConfig)

		// 告警相关路由
		alertsGroup := monitoringGroup.Group("/alerts")
		{
			alertsGroup.GET("/active", r.monitoringHandler.GetActiveAlerts)
			alertsGroup.GET("/history", r.monitoringHandler.GetAlertHistory)
			alertsGroup.POST("/test", r.monitoringHandler.TriggerTestAlert)
			alertsGroup.PUT("/:id/resolve", r.monitoringHandler.ResolveAlert)
		}
	}

	// Swagger文档路由
	r.engine.GET("/swagger/*any", ginSwagger.WrapHandler(swaggerFiles.Handler))

	// API路由组
	api := r.engine.Group("/api/v1")
	{
		// 安全认证路由（无需认证）
		auth := api.Group("/auth")
		{
			auth.POST("/register", r.userHandler.Register)                                             // 用户注册
			auth.POST("/login", r.authHandler.Login)                                                   // 用户登录
			auth.POST("/select-tenant", r.authMiddleware.RequirePreAuth(), r.authHandler.SelectTenant) // 选择租户
			auth.POST("/refresh", r.authHandler.RefreshToken)                                          // 刷新令牌
			auth.POST("/reset-password", r.authHandler.ResetPassword)                                  // 重置密码
			auth.POST("/validate-token", r.authHandler.ValidateToken)                                  // 验证令牌
		}

		// 需要认证的路由
		secureRequired := api.Group("")
		secureRequired.Use(r.authMiddleware.RequireAuth())
		secureRequired.Use(r.authMiddleware.RequireTenantID())
		{
			// 安全认证相关（需要认证）
			authSecure := secureRequired.Group("/auth")
			{
				authSecure.POST("/logout", r.authHandler.Logout)                    // 用户注销
				authSecure.POST("/change-password", r.authHandler.ChangePassword)   // 修改密码
				authSecure.GET("/profile", r.authHandler.GetProfile)                // 获取用户信息
				authSecure.POST("/check-permission", r.authHandler.CheckPermission) // 权限检查
				authSecure.GET("/roles/:tenant_id", r.authHandler.GetUserRoles)     // 获取用户角色
				authSecure.GET("/roles/list/:tenant_id", r.authHandler.ListRoles)   // 角色列表
			}

			// 用户管理路由
			users := secureRequired.Group("/users")
			{
				users.GET("", r.userHandler.ListUsers)             // 获取用户列表
				users.GET("/profile", r.userHandler.GetProfile)    // 获取用户资料
				users.PUT("/profile", r.userHandler.UpdateProfile) // 更新用户资料
			}

			// 租户管理路由
			tenants := secureRequired.Group("/tenants")
			{
				// 租户CRUD操作
				tenants.POST("", r.tenantHandler.CreateTenant)       // 创建租户
				tenants.GET("", r.tenantHandler.ListTenants)         // 获取租户列表
				tenants.GET("/:id", r.tenantHandler.GetTenant)       // 获取租户详情
				tenants.PUT("/:id", r.tenantHandler.UpdateTenant)    // 更新租户信息
				tenants.DELETE("/:id", r.tenantHandler.DeleteTenant) // 删除租户

				// 租户状态管理
				tenants.POST("/:id/activate", r.tenantHandler.ActivateTenant)   // 激活租户
				tenants.POST("/:id/suspend", r.tenantHandler.SuspendTenant)     // 暂停租户
				tenants.POST("/:id/terminate", r.tenantHandler.TerminateTenant) // 终止租户

				// 租户搜索和批量操作
				tenants.GET("/search", r.tenantHandler.SearchTenants)            // 搜索租户
				tenants.POST("/batch-status", r.tenantHandler.BatchUpdateStatus) // 批量更新状态

				// 租户配额管理
				tenants.GET("/:id/quota", r.tenantHandler.GetTenantQuota)    // 获取租户配额
				tenants.PUT("/:id/quota", r.tenantHandler.UpdateTenantQuota) // 更新租户配额

				// 租户健康检查和验证
				tenants.GET("/:id/health", r.tenantHandler.GetTenantHealth)     // 获取租户健康状态
				tenants.GET("/validate-domain", r.tenantHandler.ValidateDomain) // 验证域名可用性
			}

			// 订阅管理路由
			subscriptions := secureRequired.Group("/subscriptions")
			{
				// 订阅CRUD操作
				subscriptions.POST("", r.subscriptionHandler.CreateSubscription)                     // 创建订阅
				subscriptions.GET("", r.subscriptionHandler.ListSubscriptions)                       // 获取订阅列表
				subscriptions.GET("/:id", r.subscriptionHandler.GetSubscription)                     // 获取订阅详情
				subscriptions.GET("/tenant/:tenant_id", r.subscriptionHandler.GetTenantSubscription) // 获取租户订阅

				// 订阅生命周期管理
				subscriptions.POST("/:id/renew", r.subscriptionHandler.RenewSubscription)   // 续费订阅
				subscriptions.POST("/:id/cancel", r.subscriptionHandler.CancelSubscription) // 取消订阅

				// 试用期管理
				subscriptions.POST("/:id/extend-trial", r.subscriptionHandler.ExtendTrialPeriod) // 延长试用期

				// 订阅配额管理
				subscriptions.PUT("/:id/limits", r.subscriptionHandler.UpdateSubscriptionLimits) // 更新订阅配额

				// 订阅查询和统计
				subscriptions.GET("/expiring", r.subscriptionHandler.GetExpiringSubscriptions)    // 获取即将过期的订阅
				subscriptions.GET("/expired", r.subscriptionHandler.GetExpiredSubscriptions)      // 获取已过期的订阅
				subscriptions.GET("/trial", r.subscriptionHandler.GetTrialSubscriptions)          // 获取试用订阅
				subscriptions.GET("/statistics", r.subscriptionHandler.GetSubscriptionStatistics) // 获取订阅统计
			}
		}

		// 演示路由
		demo := api.Group("/demo")
		{
			demo.GET("/hello", r.demoHandler.Hello)
			demo.POST("/process", r.demoHandler.Process)
		}
	}
}

// GetEngine 获取Gin引擎
func (r *Router) GetEngine() *gin.Engine {
	return r.engine
}
