package middleware

import (
	"fmt"

	"backend/pkg/infrastructure/monitoring"

	"github.com/gin-gonic/gin"
	"go.opentelemetry.io/otel"
	"go.opentelemetry.io/otel/attribute"
	"go.opentelemetry.io/otel/codes"
	"go.opentelemetry.io/otel/propagation"
	"go.opentelemetry.io/otel/trace"
)

// TracingMiddleware 追踪中间件
func TracingMiddleware(tracingManager *monitoring.TracingManager) gin.HandlerFunc {
	return func(c *gin.Context) {
		if !tracingManager.IsEnabled() {
			c.Next()
			return
		}

		// 从HTTP头中提取追踪上下文
		ctx := otel.GetTextMapPropagator().Extract(c.Request.Context(), propagation.HeaderCarrier(c.Request.Header))

		// 创建span名称
		spanName := fmt.Sprintf("%s %s", c.Request.Method, c.FullPath())
		if spanName == " " {
			spanName = fmt.Sprintf("%s %s", c.Request.Method, c.Request.URL.Path)
		}

		// 开始新的span
		ctx, span := tracingManager.StartSpan(ctx, spanName,
			trace.WithAttributes(
				attribute.String("http.method", c.Request.Method),
				attribute.String("http.url", c.Request.URL.String()),
				attribute.String("http.scheme", c.Request.URL.Scheme),
				attribute.String("http.host", c.Request.Host),
				attribute.String("http.target", c.Request.URL.Path),
				attribute.String("http.user_agent", c.Request.UserAgent()),
				attribute.String("http.remote_addr", c.ClientIP()),
			),
			trace.WithSpanKind(trace.SpanKindServer),
		)

		// 将带有span的context设置到gin.Context中
		c.Request = c.Request.WithContext(ctx)

		// 继续处理请求
		c.Next()

		// 请求完成后，添加响应信息到span
		span.SetAttributes(
			attribute.Int("http.status_code", c.Writer.Status()),
			attribute.Int("http.response_size", c.Writer.Size()),
		)

		// 添加业务上下文到span（如果在处理过程中设置了）
		if userID := c.GetString("user_id"); userID != "" {
			span.SetAttributes(attribute.String("user.id", userID))
		}
		if tenantID := c.GetString("tenant_id"); tenantID != "" {
			span.SetAttributes(attribute.String("tenant.id", tenantID))
		}
		if sessionID := c.GetString("session_id"); sessionID != "" {
			span.SetAttributes(attribute.String("session.id", sessionID))
		}
		if requestID := c.GetString("request_id"); requestID != "" {
			span.SetAttributes(attribute.String("request.id", requestID))
		}
		if correlationID := c.GetString("correlation_id"); correlationID != "" {
			span.SetAttributes(attribute.String("correlation.id", correlationID))
		}

		// 如果有错误，记录错误信息
		if len(c.Errors) > 0 {
			span.SetAttributes(attribute.String("http.error", c.Errors.String()))
			span.RecordError(c.Errors.Last())
		}

		// 根据状态码设置span状态
		if c.Writer.Status() >= 400 {
			span.SetStatus(codes.Error, fmt.Sprintf("HTTP %d", c.Writer.Status()))
		} else {
			span.SetStatus(codes.Ok, "")
		}

		// 结束span
		span.End()
	}
}
