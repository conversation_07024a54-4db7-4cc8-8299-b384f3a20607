package middleware

import (
	"time"

	"backend/pkg/infrastructure/logger"

	"github.com/gin-gonic/gin"
)

// LoggingMiddleware 日志中间件
func LoggingMiddleware(log logger.Logger) gin.HandlerFunc {
	return func(c *gin.Context) {
		// 记录开始时间
		start := time.Now()

		// 处理请求
		c.Next()

		// 计算处理时间
		duration := time.Since(start)

		// 获取请求ID和关联ID
		requestID := ""
		correlationID := ""
		if id, exists := c.Get("request_id"); exists {
			if reqID, ok := id.(string); ok {
				requestID = reqID
			}
		}
		if id, exists := c.Get("correlation_id"); exists {
			if corrID, ok := id.(string); ok {
				correlationID = corrID
			}
		}

		// 记录请求日志
		fields := []any{
			"method", c.Request.Method,
			"path", c.Request.URL.Path,
			"status", c.Writer.Status(),
			"duration", duration.String(),
			"client_ip", c.<PERSON>(),
			"user_agent", c.Request.UserAgent(),
			"request_size", c.Request.ContentLength,
			"response_size", c.<PERSON>.Size(),
			"request_id", requestID,
			"correlation_id", correlationID,
		}

		// 添加查询参数（如果有）
		if c.Request.URL.RawQuery != "" {
			fields = append(fields, "query", c.Request.URL.RawQuery)
		}

		// 根据状态码选择日志级别
		switch {
		case c.Writer.Status() >= 500:
			log.Error(c.Request.Context(), "HTTP request completed with server error", fields...)
		case c.Writer.Status() >= 400:
			log.Warn(c.Request.Context(), "HTTP request completed with client error", fields...)
		default:
			log.Info(c.Request.Context(), "HTTP request completed", fields...)
		}

		// 如果有错误，记录错误详情
		if len(c.Errors) > 0 {
			for _, err := range c.Errors {
				log.Error(c.Request.Context(), "Request error occurred",
					"error", err.Error(),
					"type", err.Type,
				)
			}
		}
	}
}
