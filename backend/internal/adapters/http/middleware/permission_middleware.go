package middleware

import (
	"net/http"
	"strings"

	"backend/pkg/common/response"
	"backend/pkg/infrastructure/auth/casbin"

	"github.com/gin-gonic/gin"
	"go.uber.org/zap"
)

// PermissionMiddleware 权限中间件
type PermissionMiddleware struct {
	casbinManager *casbin.Manager
	logger        *zap.Logger
}

// NewPermissionMiddleware 创建权限中间件
func NewPermissionMiddleware(casbinManager *casbin.Manager, logger *zap.Logger) *PermissionMiddleware {
	return &PermissionMiddleware{
		casbinManager: casbinManager,
		logger:        logger,
	}
}

// RequirePermission 需要权限的中间件
func (m *PermissionMiddleware) RequirePermission(resource, action string) gin.HandlerFunc {
	return func(c *gin.Context) {
		// 获取用户信息
		userID := GetUserID(c)
		tenantID := GetTenantID(c)
		roles := GetRoles(c)

		if userID == "" || tenantID == "" {
			response.Error(c, http.StatusUnauthorized, "需要认证")
			c.Abort()
			return
		}

		// 检查权限
		hasPermission := false

		// 检查每个角色的权限
		for _, role := range roles {
			allowed, err := m.casbinManager.CheckPermission(c.Request.Context(), role, tenantID, resource, action)
			if err != nil {
				m.logger.Error("Failed to check permission",
					zap.Error(err),
					zap.String("user_id", userID),
					zap.String("tenant_id", tenantID),
					zap.String("role", role),
					zap.String("resource", resource),
					zap.String("action", action),
				)
				continue
			}

			if allowed {
				hasPermission = true
				break
			}
		}

		if !hasPermission {
			m.logger.Warn("Permission denied",
				zap.String("user_id", userID),
				zap.String("tenant_id", tenantID),
				zap.Strings("roles", roles),
				zap.String("resource", resource),
				zap.String("action", action),
			)
			response.Error(c, http.StatusForbidden, "权限不足")
			c.Abort()
			return
		}

		c.Next()
	}
}

// RequireRole 需要角色的中间件
func (m *PermissionMiddleware) RequireRole(requiredRoles ...string) gin.HandlerFunc {
	return func(c *gin.Context) {
		userID := GetUserID(c)
		if userID == "" {
			response.Error(c, http.StatusUnauthorized, "需要认证")
			c.Abort()
			return
		}

		if !HasAnyRole(c, requiredRoles) {
			m.logger.Warn("Role check failed",
				zap.String("user_id", userID),
				zap.Strings("user_roles", GetRoles(c)),
				zap.Strings("required_roles", requiredRoles),
			)
			response.Error(c, http.StatusForbidden, "角色权限不足")
			c.Abort()
			return
		}

		c.Next()
	}
}

// RequireAnyPermission 需要任意一个权限的中间件
func (m *PermissionMiddleware) RequireAnyPermission(permissions []Permission) gin.HandlerFunc {
	return func(c *gin.Context) {
		userID := GetUserID(c)
		tenantID := GetTenantID(c)
		roles := GetRoles(c)

		if userID == "" || tenantID == "" {
			response.Error(c, http.StatusUnauthorized, "需要认证")
			c.Abort()
			return
		}

		hasPermission := false

		// 检查每个权限
		for _, permission := range permissions {
			// 检查每个角色的权限
			for _, role := range roles {
				allowed, err := m.casbinManager.CheckPermission(c.Request.Context(), role, tenantID, permission.Resource, permission.Action)
				if err != nil {
					m.logger.Error("Failed to check permission", zap.Error(err))
					continue
				}

				if allowed {
					hasPermission = true
					break
				}
			}

			if hasPermission {
				break
			}
		}

		if !hasPermission {
			response.Error(c, http.StatusForbidden, "权限不足")
			c.Abort()
			return
		}

		c.Next()
	}
}

// RequireAllPermissions 需要所有权限的中间件
func (m *PermissionMiddleware) RequireAllPermissions(permissions []Permission) gin.HandlerFunc {
	return func(c *gin.Context) {
		userID := GetUserID(c)
		tenantID := GetTenantID(c)
		roles := GetRoles(c)

		if userID == "" || tenantID == "" {
			response.Error(c, http.StatusUnauthorized, "需要认证")
			c.Abort()
			return
		}

		// 检查每个权限
		for _, permission := range permissions {
			hasPermission := false

			// 检查每个角色的权限
			for _, role := range roles {
				allowed, err := m.casbinManager.CheckPermission(c.Request.Context(), role, tenantID, permission.Resource, permission.Action)
				if err != nil {
					m.logger.Error("Failed to check permission", zap.Error(err))
					continue
				}

				if allowed {
					hasPermission = true
					break
				}
			}

			if !hasPermission {
				response.Error(c, http.StatusForbidden, "权限不足")
				c.Abort()
				return
			}
		}

		c.Next()
	}
}

// CheckResourcePermission 检查资源权限的中间件（动态资源）
func (m *PermissionMiddleware) CheckResourcePermission(resourceExtractor func(*gin.Context) string, action string) gin.HandlerFunc {
	return func(c *gin.Context) {
		userID := GetUserID(c)
		tenantID := GetTenantID(c)
		roles := GetRoles(c)

		if userID == "" || tenantID == "" {
			response.Error(c, http.StatusUnauthorized, "需要认证")
			c.Abort()
			return
		}

		// 提取资源
		resource := resourceExtractor(c)
		if resource == "" {
			response.Error(c, http.StatusBadRequest, "无效的资源")
			c.Abort()
			return
		}

		// 检查权限
		hasPermission := false

		for _, role := range roles {
			allowed, err := m.casbinManager.CheckPermission(c.Request.Context(), role, tenantID, resource, action)
			if err != nil {
				m.logger.Error("Failed to check permission", zap.Error(err))
				continue
			}

			if allowed {
				hasPermission = true
				break
			}
		}

		if !hasPermission {
			response.Error(c, http.StatusForbidden, "权限不足")
			c.Abort()
			return
		}

		c.Next()
	}
}

// Permission 权限定义
type Permission struct {
	Resource string
	Action   string
}

// 常用权限定义
var (
	// 产品权限
	ProductRead   = Permission{Resource: "/api/products/*", Action: "read"}
	ProductWrite  = Permission{Resource: "/api/products/*", Action: "write"}
	ProductDelete = Permission{Resource: "/api/products/*", Action: "delete"}

	// 订单权限
	OrderRead   = Permission{Resource: "/api/orders/*", Action: "read"}
	OrderWrite  = Permission{Resource: "/api/orders/*", Action: "write"}
	OrderDelete = Permission{Resource: "/api/orders/*", Action: "delete"}

	// 用户权限
	UserRead   = Permission{Resource: "/api/users/*", Action: "read"}
	UserWrite  = Permission{Resource: "/api/users/*", Action: "write"}
	UserDelete = Permission{Resource: "/api/users/*", Action: "delete"}

	// 租户权限
	TenantRead   = Permission{Resource: "/api/tenants/*", Action: "read"}
	TenantWrite  = Permission{Resource: "/api/tenants/*", Action: "write"}
	TenantDelete = Permission{Resource: "/api/tenants/*", Action: "delete"}
)

// 资源提取器函数
func ExtractProductResource(c *gin.Context) string {
	productID := c.Param("id")
	if productID == "" {
		return "/api/products"
	}
	return "/api/products/" + productID
}

func ExtractOrderResource(c *gin.Context) string {
	orderID := c.Param("id")
	if orderID == "" {
		return "/api/orders"
	}
	return "/api/orders/" + orderID
}

// MapHTTPMethodToAction 将HTTP方法映射为权限动作
func MapHTTPMethodToAction(method string) string {
	switch strings.ToUpper(method) {
	case "GET":
		return "read"
	case "POST":
		return "write"
	case "PUT", "PATCH":
		return "write"
	case "DELETE":
		return "delete"
	default:
		return "read"
	}
}
