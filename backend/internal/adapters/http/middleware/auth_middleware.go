package middleware

import (
	"context"
	"net/http"
	"strings"

	"backend/internal/domain/auth/valueobject"
	"backend/pkg/common/response"
	"backend/pkg/infrastructure/auth/jwt"
	"backend/pkg/infrastructure/logger"

	"github.com/gin-gonic/gin"
)

// AuthMiddleware JWT安全认证中间件
type AuthMiddleware struct {
	jwtManager *jwt.Manager
	logger     logger.Logger
}

// NewAuthMiddleware 创建安全认证中间件
func NewAuthMiddleware(jwtManager *jwt.Manager, logger logger.Logger) *AuthMiddleware {
	return &AuthMiddleware{
		jwtManager: jwtManager,
		logger:     logger,
	}
}

// RequireAuth 需要认证的中间件
func (m *AuthMiddleware) RequireAuth() gin.HandlerFunc {
	return func(c *gin.Context) {
		// 提取令牌
		token := m.extractToken(c)
		if token == "" {
			response.Error(c, http.StatusUnauthorized, "缺少认证令牌")
			c.Abort()
			return
		}

		// 验证令牌
		claims, err := m.jwtManager.ValidateToken(token)
		if err != nil {
			m.logger.Warn(context.Background(), "无效的令牌", logger.Error(err))
			response.Error(c, http.StatusUnauthorized, "无效的令牌")
			c.Abort()
			return
		}

		// 将用户信息注入上下文
		m.setUserContext(c, claims)
		c.Next()
	}
}

// OptionalAuth 可选认证的中间件
func (m *AuthMiddleware) OptionalAuth() gin.HandlerFunc {
	return func(c *gin.Context) {
		// 提取令牌
		token := m.extractToken(c)
		if token == "" {
			c.Next()
			return
		}

		// 验证令牌
		claims, err := m.jwtManager.ValidateToken(token)
		if err != nil {
			m.logger.Warn(context.Background(), "Invalid token in optional auth", logger.Error(err))
			c.Next()
			return
		}

		// 将用户信息注入上下文
		m.setUserContext(c, claims)
		c.Next()
	}
}

// extractToken 从请求中提取令牌
func (m *AuthMiddleware) extractToken(c *gin.Context) string {
	// 从Authorization头提取
	authHeader := c.GetHeader("Authorization")
	if authHeader != "" {
		// Bearer token格式
		if strings.HasPrefix(authHeader, "Bearer ") {
			return strings.TrimPrefix(authHeader, "Bearer ")
		}
	}

	// 从查询参数提取（用于WebSocket等场景）
	token := c.Query("token")
	if token != "" {
		return token
	}

	return ""
}

// setUserContext 设置用户上下文
func (m *AuthMiddleware) setUserContext(c *gin.Context, claims *valueobject.JWTClaims) {
	// 设置到Gin上下文
	c.Set("user_id", claims.UserID)
	c.Set("tenant_id", claims.TenantID)
	c.Set("roles", claims.Roles)
	c.Set("claims", claims)

	// 设置到Request上下文
	ctx := context.WithValue(c.Request.Context(), "user_id", claims.UserID)
	ctx = context.WithValue(ctx, "tenant_id", claims.TenantID)
	ctx = context.WithValue(ctx, "roles", claims.Roles)
	ctx = context.WithValue(ctx, "claims", claims)

	c.Request = c.Request.WithContext(ctx)
}

// GetUserID 从上下文获取用户ID
func GetUserID(c *gin.Context) string {
	if userID, exists := c.Get("user_id"); exists {
		if id, ok := userID.(string); ok {
			return id
		}
	}
	return ""
}

// GetTenantID 从上下文获取租户ID
func GetTenantID(c *gin.Context) string {
	if tenantID, exists := c.Get("tenant_id"); exists {
		if id, ok := tenantID.(string); ok {
			return id
		}
	}
	return ""
}

// GetRoles 从上下文获取用户角色
func GetRoles(c *gin.Context) []string {
	if roles, exists := c.Get("roles"); exists {
		if r, ok := roles.([]string); ok {
			return r
		}
	}
	return []string{}
}

// GetClaims 从上下文获取JWT声明
func GetClaims(c *gin.Context) *valueobject.JWTClaims {
	if claims, exists := c.Get("claims"); exists {
		if c, ok := claims.(*valueobject.JWTClaims); ok {
			return c
		}
	}
	return nil
}

// HasRole 检查用户是否有指定角色
func HasRole(c *gin.Context, role string) bool {
	roles := GetRoles(c)
	for _, r := range roles {
		if r == role {
			return true
		}
	}
	return false
}

// HasAnyRole 检查用户是否有任意一个指定角色
func HasAnyRole(c *gin.Context, roles []string) bool {
	userRoles := GetRoles(c)
	for _, role := range roles {
		for _, userRole := range userRoles {
			if userRole == role {
				return true
			}
		}
	}
	return false
}

// RequireTenantID 必须提供租户ID的中间件
func (m *AuthMiddleware) RequireTenantID() gin.HandlerFunc {
	return func(c *gin.Context) {
		// 从上下文中获取JWT Claims
		claims, exists := c.Get("claims")
		if !exists {
			response.Error(c, http.StatusUnauthorized, "缺少认证声明")
			c.Abort()
			return
		}

		jwtClaims, ok := claims.(*valueobject.JWTClaims)
		if !ok {
			response.Error(c, http.StatusUnauthorized, "无效的认证声明")
			c.Abort()
			return
		}

		// 从请求头中提取租户ID
		headerTenantID := c.GetHeader("X-Tenant-ID")
		if headerTenantID == "" {
			response.Error(c, http.StatusBadRequest, "缺少 X-Tenant-ID 请求头")
			c.Abort()
			return
		}

		// 校验请求头中的租户ID与JWT中的租户ID是否一致
		if headerTenantID != jwtClaims.TenantID {
			m.logger.Warn(c, "租户ID不匹配",
				"header_tenant_id", headerTenantID,
				"jwt_tenant_id", jwtClaims.TenantID,
				"user_id", jwtClaims.UserID,
			)
			response.Error(c, http.StatusForbidden, "租户ID不匹配")
			c.Abort()
			return
		}

		c.Next()
	}
}

// RequirePreAuth 需要预认证的中间件
func (m *AuthMiddleware) RequirePreAuth() gin.HandlerFunc {
	return func(c *gin.Context) {
		tokenString := m.extractToken(c)
		if tokenString == "" {
			response.Error(c, http.StatusUnauthorized, "缺少预认证令牌")
			c.Abort()
			return
		}

		claims, err := m.jwtManager.ValidateToken(tokenString)
		if err != nil {
			m.logger.Warn(context.Background(), "Invalid pre-auth token", logger.Error(err))
			response.Error(c, http.StatusUnauthorized, "无效的预认证令牌")
			c.Abort()
			return
		}

		// 检查是否是预认证令牌（使用TokenType而不是roles）
		if !claims.IsPreAuthToken() || claims.TenantID != "" {
			m.logger.Warn(context.Background(), "Invalid pre-auth token type",
				"token_type", claims.GetTokenTypeString(),
				"tenant_id", claims.TenantID,
				"user_id", claims.UserID)
			response.Error(c, http.StatusForbidden, "需要预认证令牌")
			c.Abort()
			return
		}

		// TODO: 这里可以添加额外的安全验证
		// 例如：验证设备指纹、IP地址等
		// 但这些验证更适合在SelectTenant处理器中进行，因为需要访问TokenCache

		c.Set("user_id", claims.UserID)
		c.Set("jti", claims.JTI)
		c.Next()
	}
}
