package middleware

import (
	"strconv"
	"time"

	"backend/pkg/infrastructure/monitoring"

	"github.com/gin-gonic/gin"
)

// MetricsMiddleware 指标收集中间件
func MetricsMiddleware(metricsManager *monitoring.MetricsManager) gin.HandlerFunc {
	return func(c *gin.Context) {
		// 记录开始时间
		start := time.Now()

		// 获取请求大小
		requestSize := c.Request.ContentLength

		// 处理请求
		c.Next()

		// 计算处理时间
		duration := time.Since(start)

		// 获取响应信息
		statusCode := strconv.Itoa(c.Writer.Status())
		responseSize := int64(c.Writer.Size())

		// 获取租户ID（从上下文中提取）
		tenantID := getTenantIDFromContext(c)

		// 获取端点路径（使用路由模式而不是实际路径）
		endpoint := getEndpointPattern(c)

		// 记录 HTTP 请求指标
		metricsManager.RecordHTTPRequest(
			c.Request.Method,
			endpoint,
			statusCode,
			tenantID,
			duration,
			requestSize,
			responseSize,
		)

		// 如果有错误，记录错误指标
		if len(c.Errors) > 0 {
			for range c.Errors {
				metricsManager.RecordError("http_error", "request_error", tenantID)
			}
		}
	}
}

// getTenantIDFromContext 从上下文中获取租户ID
func getTenantIDFromContext(c *gin.Context) string {
	if tenantID, exists := c.Get("tenant_id"); exists {
		if id, ok := tenantID.(string); ok {
			return id
		}
	}
	return "unknown"
}

// getRequestIDFromContext 从上下文中获取请求ID
func getRequestIDFromContext(c *gin.Context) string {
	if requestID, exists := c.Get("request_id"); exists {
		if id, ok := requestID.(string); ok {
			return id
		}
	}
	return ""
}

// getEndpointPattern 获取端点模式
func getEndpointPattern(c *gin.Context) string {
	// 优先使用路由的完整路径
	if c.FullPath() != "" {
		return c.FullPath()
	}

	// 如果没有路由模式，使用实际路径
	return c.Request.URL.Path
}

// BusinessMetricsHelper 业务指标辅助器
type BusinessMetricsHelper struct {
	metricsManager *monitoring.MetricsManager
}

// NewBusinessMetricsHelper 创建业务指标辅助器
func NewBusinessMetricsHelper(metricsManager *monitoring.MetricsManager) *BusinessMetricsHelper {
	return &BusinessMetricsHelper{
		metricsManager: metricsManager,
	}
}

// RecordUserOperation 记录用户操作
func (h *BusinessMetricsHelper) RecordUserOperation(c *gin.Context, operation string, start time.Time, success bool) {
	duration := time.Since(start)
	status := "success"
	if !success {
		status = "failure"
	}

	tenantID := getTenantIDFromContext(c)
	userID := getUserIDFromContext(c)

	h.metricsManager.RecordBusinessOperation(operation, status, tenantID, userID, duration)
}

// RecordDatabaseOperation 记录数据库操作
func (h *BusinessMetricsHelper) RecordDatabaseOperation(operation, table, tenantID string, start time.Time, err error) {
	duration := time.Since(start)
	status := "success"
	if err != nil {
		status = "error"
	}

	h.metricsManager.RecordDatabaseQuery(operation, table, status, tenantID, duration)
}

// RecordCacheOperation 记录缓存操作
func (h *BusinessMetricsHelper) RecordCacheOperation(cacheName, operation, tenantID string, start time.Time, hit bool) {
	duration := time.Since(start)
	h.metricsManager.RecordCacheOperation(cacheName, operation, tenantID, hit, duration)
}

// getUserIDFromContext 从上下文中获取用户ID
func getUserIDFromContext(c *gin.Context) string {
	if userID, exists := c.Get("user_id"); exists {
		if id, ok := userID.(string); ok {
			return id
		}
	}
	return "unknown"
}

// MetricsCollector 指标收集器接口
type MetricsCollector interface {
	RecordHTTPRequest(method, endpoint, statusCode, tenantID string, duration time.Duration, requestSize, responseSize int64)
	RecordDatabaseQuery(operation, table, status, tenantID string, duration time.Duration)
	RecordCacheOperation(cacheName, operation, tenantID string, hit bool, duration time.Duration)
	RecordBusinessOperation(operation, status, tenantID, userID string, duration time.Duration)
	RecordError(errorType, errorCode, tenantID string)
	RecordPanic()
}

// NoOpMetricsCollector 空操作指标收集器（用于测试）
type NoOpMetricsCollector struct{}

func (n *NoOpMetricsCollector) RecordHTTPRequest(method, endpoint, statusCode, tenantID string, duration time.Duration, requestSize, responseSize int64) {
}
func (n *NoOpMetricsCollector) RecordDatabaseQuery(operation, table, status, tenantID string, duration time.Duration) {
}
func (n *NoOpMetricsCollector) RecordCacheOperation(cacheName, operation, tenantID string, hit bool, duration time.Duration) {
}
func (n *NoOpMetricsCollector) RecordBusinessOperation(operation, status, tenantID, userID string, duration time.Duration) {
}
func (n *NoOpMetricsCollector) RecordError(errorType, errorCode, tenantID string) {}
func (n *NoOpMetricsCollector) RecordPanic()                                      {}

// NewNoOpMetricsCollector 创建空操作指标收集器
func NewNoOpMetricsCollector() MetricsCollector {
	return &NoOpMetricsCollector{}
}
