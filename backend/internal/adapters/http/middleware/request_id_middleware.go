package middleware

import (
	"context"

	"backend/pkg/infrastructure/logger"

	"github.com/gin-gonic/gin"
	"github.com/google/uuid"
)

const (
	// RequestIDHeader HTTP头中的请求ID字段名
	RequestIDHeader = "X-Request-ID"
	// RequestIDKey 上下文中的请求ID键名
	RequestIDKey = "request_id"
	// CorrelationIDHeader 关联ID头字段名
	CorrelationIDHeader = "X-Correlation-ID"
	// CorrelationIDKey 上下文中的关联ID键名
	CorrelationIDKey = "correlation_id"
)

// RequestIDMiddleware 请求ID中间件
func RequestIDMiddleware(log logger.Logger) gin.HandlerFunc {
	return func(c *gin.Context) {
		// 1. 生成或获取请求ID
		requestID := getOrGenerateRequestID(c)
		
		// 2. 生成或获取关联ID
		correlationID := getOrGenerateCorrelationID(c)
		
		// 3. 设置到上下文中
		c.Set(RequestIDKey, requestID)
		c.Set(CorrelationIDKey, correlationID)
		
		// 4. 设置到HTTP响应头中
		c.Header(RequestIDHeader, requestID)
		c.Header(CorrelationIDHeader, correlationID)
		
		// 5. 创建带有请求ID的新上下文
		ctx := context.WithValue(c.Request.Context(), RequestIDKey, requestID)
		ctx = context.WithValue(ctx, CorrelationIDKey, correlationID)
		c.Request = c.Request.WithContext(ctx)
		
		// 6. 记录请求开始日志
		log.Info(ctx, "Request started",
			"request_id", requestID,
			"correlation_id", correlationID,
			"method", c.Request.Method,
			"path", c.Request.URL.Path,
			"user_agent", c.Request.UserAgent(),
			"client_ip", c.ClientIP(),
		)
		
		c.Next()
		
		// 7. 记录请求完成日志
		log.Info(ctx, "Request completed",
			"request_id", requestID,
			"correlation_id", correlationID,
			"status_code", c.Writer.Status(),
			"response_size", c.Writer.Size(),
		)
	}
}

// getOrGenerateRequestID 获取或生成请求ID
func getOrGenerateRequestID(c *gin.Context) string {
	// 首先尝试从请求头获取
	if requestID := c.GetHeader(RequestIDHeader); requestID != "" {
		return requestID
	}
	
	// 如果没有，生成新的UUID
	return uuid.New().String()
}

// getOrGenerateCorrelationID 获取或获取关联ID
func getOrGenerateCorrelationID(c *gin.Context) string {
	// 首先尝试从请求头获取
	if correlationID := c.GetHeader(CorrelationIDHeader); correlationID != "" {
		return correlationID
	}
	
	// 如果没有，生成新的UUID
	return uuid.New().String()
}

// GetRequestID 从上下文获取请求ID
func GetRequestID(c *gin.Context) string {
	if requestID, exists := c.Get(RequestIDKey); exists {
		if id, ok := requestID.(string); ok {
			return id
		}
	}
	return ""
}

// GetCorrelationID 从上下文获取关联ID
func GetCorrelationID(c *gin.Context) string {
	if correlationID, exists := c.Get(CorrelationIDKey); exists {
		if id, ok := correlationID.(string); ok {
			return id
		}
	}
	return ""
}

// GetRequestIDFromContext 从context获取请求ID
func GetRequestIDFromContext(ctx context.Context) string {
	if requestID := ctx.Value(RequestIDKey); requestID != nil {
		if id, ok := requestID.(string); ok {
			return id
		}
	}
	return ""
}

// GetCorrelationIDFromContext 从context获取关联ID
func GetCorrelationIDFromContext(ctx context.Context) string {
	if correlationID := ctx.Value(CorrelationIDKey); correlationID != nil {
		if id, ok := correlationID.(string); ok {
			return id
		}
	}
	return ""
}

// WithRequestID 为context添加请求ID
func WithRequestID(ctx context.Context, requestID string) context.Context {
	return context.WithValue(ctx, RequestIDKey, requestID)
}

// WithCorrelationID 为context添加关联ID
func WithCorrelationID(ctx context.Context, correlationID string) context.Context {
	return context.WithValue(ctx, CorrelationIDKey, correlationID)
}

// RequestTracker 请求追踪器
type RequestTracker struct {
	logger logger.Logger
}

// NewRequestTracker 创建请求追踪器
func NewRequestTracker(logger logger.Logger) *RequestTracker {
	return &RequestTracker{
		logger: logger,
	}
}

// TrackOperation 追踪业务操作
func (rt *RequestTracker) TrackOperation(ctx context.Context, operation string, details map[string]interface{}) {
	requestID := GetRequestIDFromContext(ctx)
	correlationID := GetCorrelationIDFromContext(ctx)
	
	fields := []interface{}{
		"request_id", requestID,
		"correlation_id", correlationID,
		"operation", operation,
	}
	
	// 添加详细信息
	for key, value := range details {
		fields = append(fields, key, value)
	}
	
	rt.logger.Info(ctx, "Business operation tracked", fields...)
}

// TrackError 追踪错误
func (rt *RequestTracker) TrackError(ctx context.Context, operation string, err error, details map[string]interface{}) {
	requestID := GetRequestIDFromContext(ctx)
	correlationID := GetCorrelationIDFromContext(ctx)
	
	fields := []interface{}{
		"request_id", requestID,
		"correlation_id", correlationID,
		"operation", operation,
		"error", err.Error(),
	}
	
	// 添加详细信息
	for key, value := range details {
		fields = append(fields, key, value)
	}
	
	rt.logger.Error(ctx, "Operation error tracked", fields...)
}

// TrackDatabaseOperation 追踪数据库操作
func (rt *RequestTracker) TrackDatabaseOperation(ctx context.Context, operation, table string, duration interface{}, err error) {
	requestID := GetRequestIDFromContext(ctx)
	correlationID := GetCorrelationIDFromContext(ctx)
	
	fields := []interface{}{
		"request_id", requestID,
		"correlation_id", correlationID,
		"db_operation", operation,
		"table", table,
		"duration", duration,
	}
	
	if err != nil {
		fields = append(fields, "error", err.Error())
		rt.logger.Error(ctx, "Database operation failed", fields...)
	} else {
		rt.logger.Debug(ctx, "Database operation completed", fields...)
	}
}

// TrackCacheOperation 追踪缓存操作
func (rt *RequestTracker) TrackCacheOperation(ctx context.Context, operation, key string, hit bool, duration interface{}) {
	requestID := GetRequestIDFromContext(ctx)
	correlationID := GetCorrelationIDFromContext(ctx)
	
	rt.logger.Debug(ctx, "Cache operation tracked",
		"request_id", requestID,
		"correlation_id", correlationID,
		"cache_operation", operation,
		"cache_key", key,
		"cache_hit", hit,
		"duration", duration,
	)
}

// TrackExternalCall 追踪外部调用
func (rt *RequestTracker) TrackExternalCall(ctx context.Context, service, endpoint string, duration interface{}, statusCode int, err error) {
	requestID := GetRequestIDFromContext(ctx)
	correlationID := GetCorrelationIDFromContext(ctx)
	
	fields := []interface{}{
		"request_id", requestID,
		"correlation_id", correlationID,
		"external_service", service,
		"endpoint", endpoint,
		"duration", duration,
		"status_code", statusCode,
	}
	
	if err != nil {
		fields = append(fields, "error", err.Error())
		rt.logger.Error(ctx, "External call failed", fields...)
	} else {
		rt.logger.Info(ctx, "External call completed", fields...)
	}
}
