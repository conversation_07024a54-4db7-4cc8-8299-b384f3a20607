package handler

import (
	"net/http"

	"backend/pkg/infrastructure/monitoring"

	"github.com/gin-gonic/gin"
)

// MetricsHandler 指标处理器
type MetricsHandler struct {
	metricsManager *monitoring.MetricsManager
}

// NewMetricsHandler 创建指标处理器
func NewMetricsHandler(metricsManager *monitoring.MetricsManager) *MetricsHandler {
	return &MetricsHandler{
		metricsManager: metricsManager,
	}
}

// Metrics 指标端点
func (h *MetricsHandler) Metrics(c *gin.Context) {
	handler := h.metricsManager.GetHandler()
	handler.ServeHTTP(c.Writer, c.Request)
}

// HealthMetrics 健康检查指标端点
func (h *MetricsHandler) HealthMetrics(c *gin.Context) {
	// 这里可以返回一些基本的健康指标
	c.JSON(http.StatusOK, gin.H{
		"status": "ok",
		"metrics": gin.H{
			"prometheus_endpoint": "/metrics",
			"health_endpoint":     "/health",
		},
	})
}
