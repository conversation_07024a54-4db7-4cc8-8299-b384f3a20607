package handler

import (
	"net/http"
	"strconv"

	"github.com/gin-gonic/gin"

	"backend/internal/application/dto"
	"backend/internal/application/usecase/tenant"
	"backend/internal/shared/errors"
	"backend/pkg/common/response"
)

// SubscriptionHandler 订阅HTTP处理器
type SubscriptionHandler struct {
	subscriptionUseCase *tenant.SubscriptionUseCase
}

// NewSubscriptionHandler 创建订阅处理器
func NewSubscriptionHandler(subscriptionUseCase *tenant.SubscriptionUseCase) *SubscriptionHandler {
	return &SubscriptionHandler{
		subscriptionUseCase: subscriptionUseCase,
	}
}

// CreateSubscription 创建订阅
// @Summary 创建订阅
// @Description 为租户创建新的订阅计划
// @Tags 订阅管理
// @Accept json
// @Produce json
// @Param request body dto.CreateSubscriptionRequestDTO true "创建订阅请求"
// @Success 201 {object} response.APIResponse{data=dto.TenantSubscriptionDTO} "创建成功"
// @Failure 400 {object} response.APIResponse "请求参数错误"
// @Failure 409 {object} response.APIResponse "订阅已存在"
// @Failure 500 {object} response.APIResponse "服务器内部错误"
// @Router /subscriptions [post]
func (h *SubscriptionHandler) CreateSubscription(c *gin.Context) {
	var req dto.CreateSubscriptionRequestDTO
	if err := c.ShouldBindJSON(&req); err != nil {
		response.Error(c, http.StatusBadRequest, "请求参数错误: "+err.Error())
		return
	}

	result, err := h.subscriptionUseCase.CreateSubscription(c.Request.Context(), &req)
	if err != nil {
		errors.HandleError(c, err)
		return
	}

	response.SuccessWithStatus(c, http.StatusCreated, result)
}

// GetSubscription 获取订阅详情
// @Summary 获取订阅详情
// @Description 根据订阅ID获取订阅详细信息
// @Tags 订阅管理
// @Accept json
// @Produce json
// @Param id path string true "订阅ID"
// @Success 200 {object} response.APIResponse{data=dto.TenantSubscriptionDTO} "获取成功"
// @Failure 404 {object} response.APIResponse "订阅不存在"
// @Failure 500 {object} response.APIResponse "服务器内部错误"
// @Router /subscriptions/{id} [get]
func (h *SubscriptionHandler) GetSubscription(c *gin.Context) {
	subscriptionID := c.Param("id")
	if subscriptionID == "" {
		response.Error(c, http.StatusBadRequest, "订阅ID不能为空")
		return
	}

	result, err := h.subscriptionUseCase.GetSubscription(c.Request.Context(), subscriptionID)
	if err != nil {
		errors.HandleError(c, err)
		return
	}

	response.Success(c, result)
}

// GetTenantSubscription 获取租户订阅
// @Summary 获取租户订阅
// @Description 获取指定租户的当前订阅信息
// @Tags 订阅管理
// @Accept json
// @Produce json
// @Param tenant_id path string true "租户ID"
// @Success 200 {object} response.APIResponse{data=dto.TenantSubscriptionDTO} "获取成功"
// @Failure 404 {object} response.APIResponse "订阅不存在"
// @Failure 500 {object} response.APIResponse "服务器内部错误"
// @Router /tenants/{tenant_id}/subscription [get]
func (h *SubscriptionHandler) GetTenantSubscription(c *gin.Context) {
	tenantID := c.Param("tenant_id")
	if tenantID == "" {
		response.Error(c, http.StatusBadRequest, "租户ID不能为空")
		return
	}

	result, err := h.subscriptionUseCase.GetTenantSubscription(c.Request.Context(), tenantID)
	if err != nil {
		errors.HandleError(c, err)
		return
	}

	response.Success(c, result)
}

// RenewSubscription 续费订阅
// @Summary 续费订阅
// @Description 为订阅续费
// @Tags 订阅管理
// @Accept json
// @Produce json
// @Param id path string true "订阅ID"
// @Param request body dto.RenewSubscriptionRequestDTO true "续费订阅请求"
// @Success 200 {object} response.APIResponse{data=dto.TenantSubscriptionDTO} "续费成功"
// @Failure 400 {object} response.APIResponse "请求参数错误"
// @Failure 404 {object} response.APIResponse "订阅不存在"
// @Failure 500 {object} response.APIResponse "服务器内部错误"
// @Router /subscriptions/{id}/renew [put]
func (h *SubscriptionHandler) RenewSubscription(c *gin.Context) {
	subscriptionID := c.Param("id")
	if subscriptionID == "" {
		response.Error(c, http.StatusBadRequest, "订阅ID不能为空")
		return
	}

	var req dto.RenewSubscriptionRequestDTO
	if err := c.ShouldBindJSON(&req); err != nil {
		response.Error(c, http.StatusBadRequest, "请求参数错误: "+err.Error())
		return
	}

	result, err := h.subscriptionUseCase.RenewSubscription(c.Request.Context(), subscriptionID, &req)
	if err != nil {
		errors.HandleError(c, err)
		return
	}

	response.Success(c, result)
}

// CancelSubscription 取消订阅
// @Summary 取消订阅
// @Description 取消指定的订阅
// @Tags 订阅管理
// @Accept json
// @Produce json
// @Param id path string true "订阅ID"
// @Param request body dto.CancelSubscriptionRequestDTO true "取消订阅请求"
// @Success 200 {object} response.APIResponse{data=dto.TenantSubscriptionDTO} "取消成功"
// @Failure 400 {object} response.APIResponse "请求参数错误"
// @Failure 404 {object} response.APIResponse "订阅不存在"
// @Failure 500 {object} response.APIResponse "服务器内部错误"
// @Router /subscriptions/{id}/cancel [put]
func (h *SubscriptionHandler) CancelSubscription(c *gin.Context) {
	subscriptionID := c.Param("id")
	if subscriptionID == "" {
		response.Error(c, http.StatusBadRequest, "订阅ID不能为空")
		return
	}

	var req dto.CancelSubscriptionRequestDTO
	if err := c.ShouldBindJSON(&req); err != nil {
		response.Error(c, http.StatusBadRequest, "请求参数错误: "+err.Error())
		return
	}

	err := h.subscriptionUseCase.CancelSubscription(c.Request.Context(), subscriptionID, &req)
	if err != nil {
		errors.HandleError(c, err)
		return
	}

	response.Success(c, nil)
}

// ListSubscriptions 获取订阅列表
// @Summary 获取订阅列表
// @Description 分页获取订阅列表，支持过滤和排序
// @Tags 订阅管理
// @Accept json
// @Produce json
// @Param page query int false "页码" default(1)
// @Param page_size query int false "每页数量" default(10)
// @Param sort_by query string false "排序字段" default(created_at)
// @Param sort_desc query bool false "是否降序" default(true)
// @Param tenant_id query string false "租户ID"
// @Param plan_type query string false "计划类型"
// @Param status query string false "订阅状态"
// @Param billing_cycle query string false "计费周期"
// @Success 200 {object} response.APIResponse{data=dto.SubscriptionListDTO} "获取成功"
// @Failure 500 {object} response.APIResponse "服务器内部错误"
// @Router /subscriptions [get]
func (h *SubscriptionHandler) ListSubscriptions(c *gin.Context) {
	// 解析分页参数
	page, _ := strconv.Atoi(c.DefaultQuery("page", "1"))
	pageSize, _ := strconv.Atoi(c.DefaultQuery("page_size", "10"))
	sortBy := c.DefaultQuery("sort_by", "created_at")
	sortDesc, _ := strconv.ParseBool(c.DefaultQuery("sort_desc", "true"))

	pagination := &dto.PaginationRequestDTO{
		Page:     page,
		PageSize: pageSize,
		SortBy:   sortBy,
		SortDesc: sortDesc,
	}

	// 解析过滤参数
	filter := &dto.SubscriptionFilterRequestDTO{
		TenantID:     c.Query("tenant_id"),
		PlanName:     c.Query("plan_name"),
		Status:       c.Query("status"),
		BillingCycle: c.Query("billing_cycle"),
	}

	result, err := h.subscriptionUseCase.ListSubscriptions(c.Request.Context(), filter, pagination)
	if err != nil {
		errors.HandleError(c, err)
		return
	}

	response.Success(c, result)
}

// GetExpiringSubscriptions 获取即将过期的订阅
// @Summary 获取即将过期的订阅
// @Description 获取指定天数内即将过期的订阅列表
// @Tags 订阅管理
// @Accept json
// @Produce json
// @Param days query int false "天数" default(30)
// @Param page query int false "页码" default(1)
// @Param page_size query int false "每页数量" default(10)
// @Success 200 {object} response.APIResponse{data=dto.SubscriptionListDTO} "获取成功"
// @Failure 500 {object} response.APIResponse "服务器内部错误"
// @Router /subscriptions/expiring [get]
func (h *SubscriptionHandler) GetExpiringSubscriptions(c *gin.Context) {
	days, _ := strconv.Atoi(c.DefaultQuery("days", "30"))

	result, err := h.subscriptionUseCase.GetExpiringSubscriptions(c.Request.Context(), days)
	if err != nil {
		errors.HandleError(c, err)
		return
	}

	response.Success(c, result)
}

// GetExpiredSubscriptions 获取已过期的订阅
// @Summary 获取已过期的订阅
// @Description 获取已过期的订阅列表
// @Tags 订阅管理
// @Accept json
// @Produce json
// @Param page query int false "页码" default(1)
// @Param page_size query int false "每页数量" default(10)
// @Success 200 {object} response.APIResponse{data=dto.SubscriptionListDTO} "获取成功"
// @Failure 500 {object} response.APIResponse "服务器内部错误"
// @Router /subscriptions/expired [get]
func (h *SubscriptionHandler) GetExpiredSubscriptions(c *gin.Context) {
	result, err := h.subscriptionUseCase.GetExpiredSubscriptions(c.Request.Context())
	if err != nil {
		errors.HandleError(c, err)
		return
	}

	response.Success(c, result)
}

// GetTrialSubscriptions 获取试用订阅
// @Summary 获取试用订阅
// @Description 获取试用期订阅列表
// @Tags 订阅管理
// @Accept json
// @Produce json
// @Param page query int false "页码" default(1)
// @Param page_size query int false "每页数量" default(10)
// @Success 200 {object} response.APIResponse{data=dto.SubscriptionListDTO} "获取成功"
// @Failure 500 {object} response.APIResponse "服务器内部错误"
// @Router /subscriptions/trial [get]
func (h *SubscriptionHandler) GetTrialSubscriptions(c *gin.Context) {
	result, err := h.subscriptionUseCase.GetTrialSubscriptions(c.Request.Context())
	if err != nil {
		errors.HandleError(c, err)
		return
	}

	response.Success(c, result)
}

// ExtendTrialPeriod 延长试用期
// @Summary 延长试用期
// @Description 延长指定订阅的试用期
// @Tags 订阅管理
// @Accept json
// @Produce json
// @Param id path string true "订阅ID"
// @Param request body dto.ExtendTrialRequestDTO true "延长试用期请求"
// @Success 200 {object} response.APIResponse{data=dto.TenantSubscriptionDTO} "延长成功"
// @Failure 400 {object} response.APIResponse "请求参数错误"
// @Failure 404 {object} response.APIResponse "订阅不存在"
// @Failure 500 {object} response.APIResponse "服务器内部错误"
// @Router /subscriptions/{id}/extend-trial [put]
func (h *SubscriptionHandler) ExtendTrialPeriod(c *gin.Context) {
	subscriptionID := c.Param("id")
	if subscriptionID == "" {
		response.Error(c, http.StatusBadRequest, "订阅ID不能为空")
		return
	}

	var req dto.ExtendTrialRequestDTO
	if err := c.ShouldBindJSON(&req); err != nil {
		response.Error(c, http.StatusBadRequest, "请求参数错误: "+err.Error())
		return
	}

	result, err := h.subscriptionUseCase.ExtendTrialPeriod(c.Request.Context(), subscriptionID, &req)
	if err != nil {
		errors.HandleError(c, err)
		return
	}

	response.Success(c, result)
}

// UpdateSubscriptionLimits 更新订阅限制
// @Summary 更新订阅限制
// @Description 更新指定订阅的使用限制
// @Tags 订阅管理
// @Accept json
// @Produce json
// @Param id path string true "订阅ID"
// @Param request body dto.UpdateQuotaLimitsRequestDTO true "更新订阅限制请求"
// @Success 200 {object} response.APIResponse{data=dto.TenantSubscriptionDTO} "更新成功"
// @Failure 400 {object} response.APIResponse "请求参数错误"
// @Failure 404 {object} response.APIResponse "订阅不存在"
// @Failure 500 {object} response.APIResponse "服务器内部错误"
// @Router /subscriptions/{id}/limits [put]
func (h *SubscriptionHandler) UpdateSubscriptionLimits(c *gin.Context) {
	subscriptionID := c.Param("id")
	if subscriptionID == "" {
		response.Error(c, http.StatusBadRequest, "订阅ID不能为空")
		return
	}

	var req dto.UpdateQuotaLimitsRequestDTO
	if err := c.ShouldBindJSON(&req); err != nil {
		response.Error(c, http.StatusBadRequest, "请求参数错误: "+err.Error())
		return
	}

	result, err := h.subscriptionUseCase.UpdateSubscriptionLimits(c.Request.Context(), subscriptionID, &req)
	if err != nil {
		errors.HandleError(c, err)
		return
	}

	response.Success(c, result)
}

// GetSubscriptionStatistics 获取订阅统计
// @Summary 获取订阅统计
// @Description 获取订阅相关的统计信息
// @Tags 订阅管理
// @Accept json
// @Produce json
// @Param tenant_id query string false "租户ID"
// @Param start_date query string false "开始日期 (YYYY-MM-DD)"
// @Param end_date query string false "结束日期 (YYYY-MM-DD)"
// @Success 200 {object} response.APIResponse{data=dto.SubscriptionStatisticsDTO} "获取成功"
// @Failure 500 {object} response.APIResponse "服务器内部错误"
// @Router /subscriptions/statistics [get]
func (h *SubscriptionHandler) GetSubscriptionStatistics(c *gin.Context) {
	result, err := h.subscriptionUseCase.GetSubscriptionStatistics(c.Request.Context())
	if err != nil {
		errors.HandleError(c, err)
		return
	}

	response.Success(c, result)
}
