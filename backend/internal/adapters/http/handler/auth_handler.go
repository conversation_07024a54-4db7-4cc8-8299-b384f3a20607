package handler

import (
	"net/http"
	"strconv"

	"github.com/gin-gonic/gin"
	"github.com/go-playground/validator/v10"

	commandHandler "backend/internal/application/command/handler"
	commandModel "backend/internal/application/command/model"
	queryHandler "backend/internal/application/query/handler"
	queryModel "backend/internal/application/query/model"
	sharedErrors "backend/internal/shared/errors"
	"backend/pkg/common/response"
	"backend/pkg/infrastructure/logger"
)

// AuthHandler 基于CQRS架构的安全认证处理器
type AuthHandler struct {
	commandHandler *commandHandler.AuthCommandHandler
	queryHandler   *queryHandler.AuthQueryHandler
	logger         logger.Logger
	validator      *validator.Validate
}

// NewAuthHandler 创建新的Auth Handler
func NewAuthHandler(
	cmdHandler *commandHandler.AuthCommandHandler,
	qryHandler *queryHandler.AuthQueryHandler,
	logger logger.Logger,
) *AuthHandler {
	return &AuthHandler{
		commandHandler: cmdHandler,
		queryHandler:   qryHandler,
		logger:         logger,
		validator:      validator.New(),
	}
}

// ==================== 认证相关API ====================

// Login 处理登录请求 - 阶段一：用户认证
// @Summary 用户登录 - 阶段一
// @Description 认证用户并返回可访问的租户列表
// @Tags 认证
// @Accept json
// @Produce json
// @Param request body commandModel.LoginCommand true "登录凭据"
// @Success 200 {object} response.APIResponse{data=commandModel.LoginResult} "登录成功，返回租户列表"
// @Failure 400 {object} response.APIResponse "无效请求"
// @Failure 401 {object} response.APIResponse "无效凭据"
// @Router /auth/login [post]
func (h *AuthHandler) Login(c *gin.Context) {
	var cmd commandModel.LoginCommand
	if err := c.ShouldBindJSON(&cmd); err != nil {
		response.Error(c, http.StatusBadRequest, "无效的请求体: "+err.Error())
		return
	}

	// 执行登录命令
	result, err := h.commandHandler.Login(c.Request.Context(), &cmd)
	if err != nil {
		sharedErrors.HandleError(c, err)
		return
	}

	response.Success(c, result)
}

// SelectTenant 处理选择租户请求 - 阶段二：选择租户并生成令牌
// @Summary 用户登录 - 阶段二
// @Description 选择一个租户并返回JWT令牌
// @Tags 认证
// @Accept json
// @Produce json
// @Param request body commandModel.SelectTenantCommand true "租户选择"
// @Success 200 {object} response.APIResponse{data=commandModel.AuthResult} "租户已选择，令牌已生成"
// @Failure 400 {object} response.APIResponse "无效请求"
// @Failure 403 {object} response.APIResponse "租户访问被拒绝"
// @Security ApiKeyAuth
// @Router /auth/select-tenant [post]
func (h *AuthHandler) SelectTenant(c *gin.Context) {
	userID, exists := c.Get("user_id")
	if !exists {
		response.Error(c, http.StatusUnauthorized, "用户未认证，无法选择租户")
		return
	}

	userIDStr, ok := userID.(string)
	if !ok {
		response.Error(c, http.StatusUnauthorized, "无效的用户ID")
		return
	}

	// 获取JTI用于删除preauth token
	jti, jtiExists := c.Get("jti")
	if !jtiExists {
		response.Error(c, http.StatusUnauthorized, "缺少令牌标识符")
		return
	}

	jtiStr, ok := jti.(string)
	if !ok {
		response.Error(c, http.StatusUnauthorized, "无效的令牌标识符")
		return
	}

	var cmd commandModel.SelectTenantCommand
	if err := c.ShouldBindJSON(&cmd); err != nil {
		response.Error(c, http.StatusBadRequest, "无效的请求体: "+err.Error())
		return
	}

	// 设置用户ID和JTI
	cmd.UserID = userIDStr
	cmd.PreAuthTokenJTI = jtiStr

	// 执行选择租户命令
	result, err := h.commandHandler.SelectTenant(c.Request.Context(), &cmd)
	if err != nil {
		sharedErrors.HandleError(c, err)
		return
	}

	response.Success(c, result)
}

// Logout 处理用户注销
// @Summary 用户注销
// @Description 用户注销登录，撤销令牌
// @Tags 认证
// @Accept json
// @Produce json
// @Param request body commandModel.LogoutCommand true "注销请求"
// @Success 200 {object} response.APIResponse{data=commandModel.OperationResult} "注销成功"
// @Security ApiKeyAuth
// @Router /auth/logout [post]
func (h *AuthHandler) Logout(c *gin.Context) {
	userID, exists := c.Get("user_id")
	if !exists {
		response.Error(c, http.StatusUnauthorized, "用户未认证")
		return
	}

	tenantID, exists := c.Get("tenant_id")
	if !exists {
		response.Error(c, http.StatusUnauthorized, "租户信息缺失")
		return
	}

	var cmd commandModel.LogoutCommand
	if err := c.ShouldBindJSON(&cmd); err != nil {
		// 如果没有请求体，使用默认值
		cmd = commandModel.LogoutCommand{
			UserID:     userID.(string),
			TenantID:   tenantID.(string),
			LogoutType: "current",
		}
	} else {
		// 确保用户ID和租户ID正确
		cmd.UserID = userID.(string)
		cmd.TenantID = tenantID.(string)
	}

	// 执行注销命令
	result, err := h.commandHandler.Logout(c.Request.Context(), &cmd)
	if err != nil {
		sharedErrors.HandleError(c, err)
		return
	}

	response.Success(c, result)
}

// RefreshToken 处理令牌刷新
// @Summary 刷新访问令牌
// @Description 使用刷新令牌获取新的访问令牌
// @Tags 认证
// @Accept json
// @Produce json
// @Param request body commandModel.RefreshTokenCommand true "刷新令牌请求"
// @Success 200 {object} response.APIResponse{data=commandModel.AuthResult} "令牌刷新成功"
// @Failure 400 {object} response.APIResponse "无效请求"
// @Failure 401 {object} response.APIResponse "无效的刷新令牌"
// @Router /auth/refresh [post]
func (h *AuthHandler) RefreshToken(c *gin.Context) {
	var cmd commandModel.RefreshTokenCommand
	if err := c.ShouldBindJSON(&cmd); err != nil {
		response.Error(c, http.StatusBadRequest, "无效的请求体: "+err.Error())
		return
	}

	// 执行刷新令牌命令
	result, err := h.commandHandler.RefreshToken(c.Request.Context(), &cmd)
	if err != nil {
		sharedErrors.HandleError(c, err)
		return
	}

	response.Success(c, result)
}

// ==================== 密码管理API ====================

// ChangePassword 处理修改密码
// @Summary 修改密码
// @Description 允许已认证的用户修改其密码
// @Tags 认证
// @Accept json
// @Produce json
// @Param request body commandModel.ChangePasswordCommand true "修改密码请求"
// @Success 200 {object} response.APIResponse{data=commandModel.OperationResult} "密码修改成功"
// @Failure 400 {object} response.APIResponse "无效请求"
// @Failure 401 {object} response.APIResponse "无效的旧密码"
// @Security ApiKeyAuth
// @Router /auth/change-password [post]
func (h *AuthHandler) ChangePassword(c *gin.Context) {
	userID, exists := c.Get("user_id")
	if !exists {
		response.Error(c, http.StatusUnauthorized, "令牌中未找到用户ID")
		return
	}

	userIDStr, ok := userID.(string)
	if !ok {
		response.Error(c, http.StatusUnauthorized, "无效的用户ID")
		return
	}

	var cmd commandModel.ChangePasswordCommand
	if err := c.ShouldBindJSON(&cmd); err != nil {
		response.Error(c, http.StatusBadRequest, "无效的请求体: "+err.Error())
		return
	}
	cmd.UserID = userIDStr

	// 执行修改密码命令
	result, err := h.commandHandler.ChangePassword(c.Request.Context(), &cmd)
	if err != nil {
		sharedErrors.HandleError(c, err)
		return
	}

	response.Success(c, result)
}

// ResetPassword 处理密码重置
// @Summary 重置密码
// @Description 通过验证码重置用户密码
// @Tags 认证
// @Accept json
// @Produce json
// @Param request body commandModel.ResetPasswordCommand true "重置密码请求"
// @Success 200 {object} response.APIResponse{data=commandModel.OperationResult} "密码重置成功"
// @Failure 400 {object} response.APIResponse "无效请求"
// @Failure 401 {object} response.APIResponse "无效的验证码"
// @Router /auth/reset-password [post]
func (h *AuthHandler) ResetPassword(c *gin.Context) {
	var cmd commandModel.ResetPasswordCommand
	if err := c.ShouldBindJSON(&cmd); err != nil {
		response.Error(c, http.StatusBadRequest, "无效的请求体: "+err.Error())
		return
	}

	// 执行重置密码命令
	result, err := h.commandHandler.ResetPassword(c.Request.Context(), &cmd)
	if err != nil {
		sharedErrors.HandleError(c, err)
		return
	}

	response.Success(c, result)
}

// ==================== 查询相关API ====================

// ValidateToken 验证令牌
// @Summary 验证令牌
// @Description 验证JWT令牌的有效性
// @Tags 认证
// @Accept json
// @Produce json
// @Param request body queryModel.ValidateTokenQuery true "令牌验证请求"
// @Success 200 {object} response.APIResponse{data=queryModel.TokenValidationResult} "令牌验证结果"
// @Failure 400 {object} response.APIResponse "无效请求"
// @Router /auth/validate-token [post]
func (h *AuthHandler) ValidateToken(c *gin.Context) {
	var query queryModel.ValidateTokenQuery
	if err := c.ShouldBindJSON(&query); err != nil {
		response.Error(c, http.StatusBadRequest, "无效的请求体: "+err.Error())
		return
	}

	// 执行令牌验证查询
	result, err := h.queryHandler.ValidateToken(c.Request.Context(), &query)
	if err != nil {
		sharedErrors.HandleError(c, err)
		return
	}

	response.Success(c, result)
}

// GetProfile 获取用户个人资料
// @Summary 获取用户个人资料
// @Description 获取当前认证用户的个人资料信息
// @Tags 认证
// @Produce json
// @Success 200 {object} response.APIResponse{data=queryModel.UserAuthResult} "成功返回个人资料"
// @Failure 401 {object} response.APIResponse "用户未认证"
// @Security ApiKeyAuth
// @Router /auth/profile [get]
func (h *AuthHandler) GetProfile(c *gin.Context) {
	userID, exists := c.Get("user_id")
	if !exists {
		response.Error(c, http.StatusUnauthorized, "用户未认证")
		return
	}

	userIDStr, ok := userID.(string)
	if !ok {
		response.Error(c, http.StatusUnauthorized, "无效的用户ID")
		return
	}

	// 构建查询
	query := &queryModel.GetUserAuthQuery{
		UserID: userIDStr,
	}

	// 执行获取用户认证信息查询
	result, err := h.queryHandler.GetUserAuth(c.Request.Context(), query)
	if err != nil {
		sharedErrors.HandleError(c, err)
		return
	}

	response.Success(c, result)
}

// CheckPermission 检查权限
// @Summary 检查用户权限
// @Description 检查用户是否有特定资源的操作权限
// @Tags 权限
// @Accept json
// @Produce json
// @Param request body queryModel.CheckPermissionQuery true "权限检查请求"
// @Success 200 {object} response.APIResponse{data=queryModel.PermissionCheckResult} "权限检查结果"
// @Failure 400 {object} response.APIResponse "无效请求"
// @Security ApiKeyAuth
// @Router /auth/check-permission [post]
func (h *AuthHandler) CheckPermission(c *gin.Context) {
	var query queryModel.CheckPermissionQuery
	if err := c.ShouldBindJSON(&query); err != nil {
		response.Error(c, http.StatusBadRequest, "无效的请求体: "+err.Error())
		return
	}

	// 执行权限检查查询
	result, err := h.queryHandler.CheckPermission(c.Request.Context(), &query)
	if err != nil {
		sharedErrors.HandleError(c, err)
		return
	}

	response.Success(c, result)
}

// GetUserRoles 获取用户角色
// @Summary 获取用户角色
// @Description 获取用户在指定租户下的角色列表
// @Tags 角色
// @Produce json
// @Param tenant_id path string true "租户ID"
// @Success 200 {object} response.APIResponse{data=queryModel.UserRolesResult} "用户角色列表"
// @Failure 400 {object} response.APIResponse "无效请求"
// @Security ApiKeyAuth
// @Router /auth/roles/{tenant_id} [get]
func (h *AuthHandler) GetUserRoles(c *gin.Context) {
	userID, exists := c.Get("user_id")
	if !exists {
		response.Error(c, http.StatusUnauthorized, "用户未认证")
		return
	}

	userIDStr, ok := userID.(string)
	if !ok {
		response.Error(c, http.StatusUnauthorized, "无效的用户ID")
		return
	}

	tenantID := c.Param("tenant_id")
	if tenantID == "" {
		response.Error(c, http.StatusBadRequest, "租户ID不能为空")
		return
	}

	// 构建查询
	query := &queryModel.GetUserRolesQuery{
		UserID:   userIDStr,
		TenantID: tenantID,
	}

	// 执行获取用户角色查询
	result, err := h.queryHandler.GetUserRoles(c.Request.Context(), query)
	if err != nil {
		sharedErrors.HandleError(c, err)
		return
	}

	response.Success(c, result)
}

// ListRoles 获取角色列表
// @Summary 获取角色列表
// @Description 获取指定租户下的角色列表
// @Tags 角色
// @Produce json
// @Param tenant_id path string true "租户ID"
// @Param page query int false "页码" default(1)
// @Param page_size query int false "每页大小" default(20)
// @Param sort_by query string false "排序字段" Enums(name,created_at,updated_at)
// @Param sort_desc query bool false "是否降序" default(false)
// @Success 200 {object} response.APIResponse{data=queryModel.RoleListResult} "角色列表"
// @Failure 400 {object} response.APIResponse "无效请求"
// @Security ApiKeyAuth
// @Router /auth/roles/list/{tenant_id} [get]
func (h *AuthHandler) ListRoles(c *gin.Context) {
	tenantID := c.Param("tenant_id")
	if tenantID == "" {
		response.Error(c, http.StatusBadRequest, "租户ID不能为空")
		return
	}

	// 解析查询参数
	page, _ := strconv.Atoi(c.DefaultQuery("page", "1"))
	pageSize, _ := strconv.Atoi(c.DefaultQuery("page_size", "20"))
	sortBy := c.DefaultQuery("sort_by", "created_at")
	sortDesc, _ := strconv.ParseBool(c.DefaultQuery("sort_desc", "false"))

	// 构建查询
	query := &queryModel.ListRolesQuery{
		TenantID: tenantID,
		Page:     page,
		PageSize: pageSize,
		SortBy:   sortBy,
		SortDesc: sortDesc,
	}

	// 执行角色列表查询
	result, err := h.queryHandler.ListRoles(c.Request.Context(), query)
	if err != nil {
		sharedErrors.HandleError(c, err)
		return
	}

	response.Success(c, result)
}
