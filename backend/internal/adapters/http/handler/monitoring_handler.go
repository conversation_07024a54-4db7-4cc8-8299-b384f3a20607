package handler

import (
	"net/http"
	"strconv"

	"backend/pkg/infrastructure/monitoring"

	"github.com/gin-gonic/gin"
)

// MonitoringHandler 监控处理器
type MonitoringHandler struct {
	performanceMonitor *monitoring.PerformanceMonitor
	alertManager       *monitoring.AlertManager
	metricsManager     *monitoring.MetricsManager
}

// NewMonitoringHandler 创建监控处理器
func NewMonitoringHandler(
	performanceMonitor *monitoring.PerformanceMonitor,
	alertManager *monitoring.AlertManager,
	metricsManager *monitoring.MetricsManager,
) *MonitoringHandler {
	return &MonitoringHandler{
		performanceMonitor: performanceMonitor,
		alertManager:       alertManager,
		metricsManager:     metricsManager,
	}
}

// GetPerformanceMetrics 获取性能指标
func (h *MonitoringHandler) GetPerformanceMetrics(c *gin.Context) {
	metrics := h.performanceMonitor.GetCurrentMetrics()
	
	c.<PERSON>SO<PERSON>(http.StatusOK, gin.H{
		"status": "success",
		"data":   metrics,
	})
}

// GetActiveAlerts 获取活跃告警
func (h *MonitoringHandler) GetActiveAlerts(c *gin.Context) {
	alerts := h.alertManager.GetActiveAlerts()
	
	c.JSON(http.StatusOK, gin.H{
		"status": "success",
		"data": gin.H{
			"active_alerts": alerts,
			"count":         len(alerts),
		},
	})
}

// GetAlertHistory 获取告警历史
func (h *MonitoringHandler) GetAlertHistory(c *gin.Context) {
	limitStr := c.DefaultQuery("limit", "100")
	limit, err := strconv.Atoi(limitStr)
	if err != nil {
		limit = 100
	}
	
	alerts := h.alertManager.GetAlertHistory(limit)
	
	c.JSON(http.StatusOK, gin.H{
		"status": "success",
		"data": gin.H{
			"alert_history": alerts,
			"count":         len(alerts),
			"limit":         limit,
		},
	})
}

// ResolveAlert 解决告警
func (h *MonitoringHandler) ResolveAlert(c *gin.Context) {
	alertID := c.Param("id")
	if alertID == "" {
		c.JSON(http.StatusBadRequest, gin.H{
			"status":  "error",
			"message": "Alert ID is required",
		})
		return
	}
	
	h.alertManager.ResolveAlert(c.Request.Context(), alertID)
	
	c.JSON(http.StatusOK, gin.H{
		"status":  "success",
		"message": "Alert resolved successfully",
		"data": gin.H{
			"alert_id": alertID,
		},
	})
}

// TriggerTestAlert 触发测试告警
func (h *MonitoringHandler) TriggerTestAlert(c *gin.Context) {
	var request struct {
		Type     string                 `json:"type" binding:"required"`
		Severity string                 `json:"severity" binding:"required"`
		Data     map[string]interface{} `json:"data"`
	}
	
	if err := c.ShouldBindJSON(&request); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"status":  "error",
			"message": "Invalid request format",
			"error":   err.Error(),
		})
		return
	}
	
	// 验证告警级别
	validSeverities := map[string]bool{
		"info":     true,
		"warning":  true,
		"critical": true,
	}
	
	if !validSeverities[request.Severity] {
		c.JSON(http.StatusBadRequest, gin.H{
			"status":  "error",
			"message": "Invalid severity level",
			"valid_severities": []string{"info", "warning", "critical"},
		})
		return
	}
	
	// 添加测试标识
	if request.Data == nil {
		request.Data = make(map[string]interface{})
	}
	request.Data["test_alert"] = true
	request.Data["triggered_by"] = "api"
	
	// 触发告警
	h.alertManager.TriggerAlert(c.Request.Context(), request.Type, request.Severity, request.Data)
	
	c.JSON(http.StatusOK, gin.H{
		"status":  "success",
		"message": "Test alert triggered successfully",
		"data": gin.H{
			"type":     request.Type,
			"severity": request.Severity,
			"data":     request.Data,
		},
	})
}

// GetMonitoringStatus 获取监控状态
func (h *MonitoringHandler) GetMonitoringStatus(c *gin.Context) {
	activeAlerts := h.alertManager.GetActiveAlerts()
	currentMetrics := h.performanceMonitor.GetCurrentMetrics()
	
	// 统计告警级别
	alertCounts := map[string]int{
		"critical": 0,
		"warning":  0,
		"info":     0,
	}
	
	for _, alert := range activeAlerts {
		if count, exists := alertCounts[alert.Severity]; exists {
			alertCounts[alert.Severity] = count + 1
		}
	}
	
	// 计算系统健康状态
	healthStatus := "healthy"
	if alertCounts["critical"] > 0 {
		healthStatus = "critical"
	} else if alertCounts["warning"] > 0 {
		healthStatus = "warning"
	}
	
	c.JSON(http.StatusOK, gin.H{
		"status": "success",
		"data": gin.H{
			"health_status": healthStatus,
			"alerts": gin.H{
				"active_count": len(activeAlerts),
				"by_severity":  alertCounts,
			},
			"performance": gin.H{
				"cpu_usage":      currentMetrics.CPUUsage,
				"memory_usage":   currentMetrics.MemoryUsage.UsageRate,
				"goroutines":     currentMetrics.Goroutines,
				"gc_runs":        currentMetrics.GCRuns,
				"last_updated":   currentMetrics.Timestamp,
			},
			"system_info": gin.H{
				"memory_alloc":  currentMetrics.MemoryUsage.Alloc,
				"memory_sys":    currentMetrics.MemoryUsage.Sys,
				"memory_total":  currentMetrics.MemoryUsage.TotalAlloc,
			},
		},
	})
}

// GetMonitoringConfig 获取监控配置
func (h *MonitoringHandler) GetMonitoringConfig(c *gin.Context) {
	// 这里应该返回当前的监控配置
	// 为了简化，返回一些基本信息
	c.JSON(http.StatusOK, gin.H{
		"status": "success",
		"data": gin.H{
			"performance_monitoring": gin.H{
				"enabled":  true,
				"interval": "30s",
			},
			"alerting": gin.H{
				"enabled": true,
				"channels": []string{"log", "email"},
			},
			"metrics": gin.H{
				"prometheus_enabled": true,
				"endpoint":          "/metrics",
			},
		},
	})
}

// GetSystemHealth 获取系统健康状态
func (h *MonitoringHandler) GetSystemHealth(c *gin.Context) {
	currentMetrics := h.performanceMonitor.GetCurrentMetrics()
	activeAlerts := h.alertManager.GetActiveAlerts()
	
	// 计算健康分数 (0-100)
	healthScore := 100.0
	
	// 根据CPU使用率扣分
	if currentMetrics.CPUUsage > 90 {
		healthScore -= 30
	} else if currentMetrics.CPUUsage > 70 {
		healthScore -= 15
	}
	
	// 根据内存使用率扣分
	if currentMetrics.MemoryUsage.UsageRate > 95 {
		healthScore -= 25
	} else if currentMetrics.MemoryUsage.UsageRate > 80 {
		healthScore -= 10
	}
	
	// 根据Goroutine数量扣分
	if currentMetrics.Goroutines > 5000 {
		healthScore -= 20
	} else if currentMetrics.Goroutines > 1000 {
		healthScore -= 10
	}
	
	// 根据活跃告警扣分
	for _, alert := range activeAlerts {
		switch alert.Severity {
		case "critical":
			healthScore -= 15
		case "warning":
			healthScore -= 5
		case "info":
			healthScore -= 1
		}
	}
	
	if healthScore < 0 {
		healthScore = 0
	}
	
	// 确定健康状态
	var status string
	var statusColor string
	
	switch {
	case healthScore >= 90:
		status = "excellent"
		statusColor = "green"
	case healthScore >= 70:
		status = "good"
		statusColor = "green"
	case healthScore >= 50:
		status = "warning"
		statusColor = "yellow"
	case healthScore >= 30:
		status = "poor"
		statusColor = "orange"
	default:
		status = "critical"
		statusColor = "red"
	}
	
	c.JSON(http.StatusOK, gin.H{
		"status": "success",
		"data": gin.H{
			"health_score":  healthScore,
			"status":        status,
			"status_color":  statusColor,
			"timestamp":     currentMetrics.Timestamp,
			"active_alerts": len(activeAlerts),
			"components": gin.H{
				"cpu": gin.H{
					"status": func() string {
						if currentMetrics.CPUUsage > 90 {
							return "critical"
						} else if currentMetrics.CPUUsage > 70 {
							return "warning"
						}
						return "healthy"
					}(),
					"usage": currentMetrics.CPUUsage,
				},
				"memory": gin.H{
					"status": func() string {
						if currentMetrics.MemoryUsage.UsageRate > 95 {
							return "critical"
						} else if currentMetrics.MemoryUsage.UsageRate > 80 {
							return "warning"
						}
						return "healthy"
					}(),
					"usage_rate": currentMetrics.MemoryUsage.UsageRate,
					"alloc":      currentMetrics.MemoryUsage.Alloc,
				},
				"goroutines": gin.H{
					"status": func() string {
						if currentMetrics.Goroutines > 5000 {
							return "critical"
						} else if currentMetrics.Goroutines > 1000 {
							return "warning"
						}
						return "healthy"
					}(),
					"count": currentMetrics.Goroutines,
				},
			},
		},
	})
}
