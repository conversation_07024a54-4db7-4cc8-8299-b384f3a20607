package handler

import (
	"net/http"
	"strconv"

	"github.com/gin-gonic/gin"

	commandHandler "backend/internal/application/command/handler"
	commandModel "backend/internal/application/command/model"
	queryHandler "backend/internal/application/query/handler"
	queryModel "backend/internal/application/query/model"
	"backend/internal/application/usecase/user"
	"backend/internal/shared/errors"
	"backend/pkg/common/response"
	"backend/pkg/infrastructure/logger"
)

// UserHandler 用户处理器 - 使用CQRS架构
type UserHandler struct {
	// CQRS handlers
	userCommandHandler *commandHandler.UserCommandHandler
	userQueryHandler   queryHandler.UserQueryHandler

	// 保留UseCase用于复杂业务流程（过渡期）
	userRegistrationUseCase *user.UserRegistrationUseCase
	userManagementUseCase   *user.UserManagementUseCase

	logger logger.Logger
}

// NewUserHandler 创建用户处理器
func NewUserHandler(
	userCommandHandler *commandHandler.UserCommandHandler,
	userQueryHandler queryHandler.UserQueryHandler,
	registrationUseCase *user.UserRegistrationUseCase,
	managementUseCase *user.UserManagementUseCase,
	logger logger.Logger,
) *UserHandler {
	return &UserHandler{
		userCommandHandler:      userCommandHandler,
		userQueryHandler:        userQueryHandler,
		userRegistrationUseCase: registrationUseCase,
		userManagementUseCase:   managementUseCase,
		logger:                  logger,
	}
}

// Register 用户注册 - 过渡期使用UseCase（复杂业务流程）
// @Summary 用户注册
// @Description 注册新用户账户
// @Tags 用户管理
// @Accept json
// @Produce json
// @Param request body user.RegisterUserRequest true "注册请求"
// @Success 201 {object} response.APIResponse{data=user.RegisterUserResponse} "注册成功"
// @Failure 400 {object} response.APIResponse "无效的请求体"
// @Failure 409 {object} response.APIResponse "用户已存在"
// @Failure 500 {object} response.APIResponse "服务器内部错误"
// @Router /users/register [post]
func (h *UserHandler) Register(c *gin.Context) {
	var req user.RegisterUserRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		response.Error(c, http.StatusBadRequest, "无效的请求体: "+err.Error())
		return
	}

	h.logger.Info(c.Request.Context(), "Processing user registration (UseCase - complex flow)", map[string]interface{}{
		"username": req.Username,
		"email":    req.Email,
	})

	// 暂时使用UseCase处理复杂的用户注册流程
	// TODO: 在阶段二中拆分为多个CQRS Command组合
	resp, err := h.userRegistrationUseCase.Execute(c.Request.Context(), &req)
	if err != nil {
		h.logger.Error(c.Request.Context(), "User registration failed", err, map[string]interface{}{
			"username": req.Username,
			"email":    req.Email,
		})
		errors.HandleError(c, err)
		return
	}

	h.logger.Info(c.Request.Context(), "User registration successful", map[string]interface{}{
		"user_id":  resp.UserID,
		"username": resp.Username,
	})

	response.SuccessWithStatus(c, http.StatusCreated, resp)
}

// ListUsers 获取用户列表 - 使用CQRS Query
// @Summary 获取用户列表
// @Description 获取用户列表
// @Tags 用户管理
// @Accept json
// @Produce json
// @Param tenant_id query string true "租户ID"
// @Param page query int false "页码" default(1)
// @Param page_size query int false "每页数量" default(10)
// @Param keyword query string false "搜索关键词"
// @Param status query string false "用户状态筛选"
// @Success 200 {object} response.APIResponse{data=queryModel.UserListResult} "获取成功"
// @Failure 400 {object} response.APIResponse "请求参数错误"
// @Failure 500 {object} response.APIResponse "服务器内部错误"
// @Security ApiKeyAuth
// @Router /users [get]
func (h *UserHandler) ListUsers(c *gin.Context) {
	// 解析请求参数
	tenantID := c.Query("tenant_id")
	if tenantID == "" {
		errors.HandleError(c, errors.ValidationFailed("租户ID不能为空"))
		return
	}

	page, _ := strconv.Atoi(c.DefaultQuery("page", "1"))
	pageSize, _ := strconv.Atoi(c.DefaultQuery("page_size", "10"))
	keyword := c.Query("keyword")
	status := c.Query("status")

	h.logger.Info(c.Request.Context(), "Processing list users via CQRS Query", map[string]interface{}{
		"tenant_id": tenantID,
		"page":      page,
		"page_size": pageSize,
		"keyword":   keyword,
		"status":    status,
	})

	// 构建CQRS Query
	query := &queryModel.ListUsersQuery{
		TenantID: tenantID,
		Keyword:  keyword,
		Status:   status,
		Page:     page,
		PageSize: pageSize,
	}

	// 使用CQRS Query Handler处理
	result, err := h.userQueryHandler.ListUsers(c.Request.Context(), query)
	if err != nil {
		h.logger.Error(c.Request.Context(), "List users failed via CQRS", err, map[string]interface{}{
			"tenant_id": tenantID,
			"page":      page,
			"page_size": pageSize,
		})
		errors.HandleError(c, err)
		return
	}

	h.logger.Info(c.Request.Context(), "List users successful via CQRS", map[string]interface{}{
		"tenant_id":    tenantID,
		"total_count":  result.Total,
		"result_count": len(result.Users),
	})

	response.Success(c, result)
}

// GetProfile 获取用户资料 - 使用CQRS Query
// @Summary 获取用户资料
// @Description 获取指定用户的详细资料信息
// @Tags 用户管理
// @Accept json
// @Produce json
// @Param user_id path string true "用户ID"
// @Success 200 {object} response.APIResponse{data=queryModel.UserQueryResult} "获取成功"
// @Failure 400 {object} response.APIResponse "无效的请求参数"
// @Failure 404 {object} response.APIResponse "用户不存在"
// @Failure 500 {object} response.APIResponse "服务器内部错误"
// @Security ApiKeyAuth
// @Router /users/{user_id}/profile [get]
func (h *UserHandler) GetProfile(c *gin.Context) {
	userID := c.Param("user_id")
	if userID == "" {
		response.Error(c, http.StatusBadRequest, "用户ID不能为空")
		return
	}

	h.logger.Info(c.Request.Context(), "Processing get user profile via CQRS Query", map[string]interface{}{
		"user_id": userID,
	})

	// 构建CQRS Query
	query := &queryModel.GetUserByIDQuery{
		UserID: userID,
	}

	// 使用CQRS Query Handler处理
	result, err := h.userQueryHandler.GetUserByID(c.Request.Context(), query)
	if err != nil {
		h.logger.Error(c.Request.Context(), "Get user profile failed via CQRS", err, map[string]interface{}{
			"user_id": userID,
		})
		errors.HandleError(c, err)
		return
	}

	h.logger.Info(c.Request.Context(), "Get user profile successful via CQRS", map[string]interface{}{
		"user_id":  userID,
		"username": result.Username,
	})

	response.Success(c, result)
}

// UpdateProfile 更新用户资料 - 使用CQRS Command
// @Summary 更新用户资料
// @Description 更新用户的个人资料信息
// @Tags 用户管理
// @Accept json
// @Produce json
// @Param user_id path string true "用户ID"
// @Param request body user.UpdateUserProfileRequest true "更新请求"
// @Success 200 {object} response.APIResponse{data=commandModel.UpdateUserResult} "更新成功"
// @Failure 400 {object} response.APIResponse "无效的请求体"
// @Failure 404 {object} response.APIResponse "用户不存在"
// @Failure 500 {object} response.APIResponse "服务器内部错误"
// @Security ApiKeyAuth
// @Router /users/{user_id}/profile [put]
func (h *UserHandler) UpdateProfile(c *gin.Context) {
	userID := c.Param("user_id")
	if userID == "" {
		response.Error(c, http.StatusBadRequest, "用户ID不能为空")
		return
	}

	var req user.UpdateUserProfileRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		response.Error(c, http.StatusBadRequest, "无效的请求体: "+err.Error())
		return
	}

	h.logger.Info(c.Request.Context(), "Processing update user profile via CQRS Command", map[string]interface{}{
		"user_id": userID,
	})

	// 构建CQRS Command
	cmd := &commandModel.UpdateUserCommand{
		UserID: userID,
		Profile: &commandModel.UpdateProfileData{
			Nickname:  req.Nickname,
			FirstName: req.FirstName,
			LastName:  req.LastName,
			Avatar:    req.Avatar,
		},
		// ContactInfo 在当前的UpdateUserProfileRequest中不包含Email和Phone
		// 这些字段需要单独的API端点来更新
		Preferences: &commandModel.UpdatePreferencesData{
			Language: req.Language,
			Timezone: req.Timezone,
		},
	}

	// 使用CQRS Command Handler处理
	result, err := h.userCommandHandler.UpdateUser(c.Request.Context(), cmd)
	if err != nil {
		h.logger.Error(c.Request.Context(), "Update user profile failed via CQRS", err, map[string]interface{}{
			"user_id": userID,
		})
		errors.HandleError(c, err)
		return
	}

	h.logger.Info(c.Request.Context(), "Update user profile successful via CQRS", map[string]interface{}{
		"user_id": userID,
	})

	response.Success(c, result)
}

// ActivateUser 激活用户 - 使用CQRS Command
// @Summary 激活用户
// @Description 激活指定的用户账户
// @Tags 用户管理
// @Accept json
// @Produce json
// @Param user_id path string true "用户ID"
// @Success 200 {object} response.APIResponse{data=commandModel.ActivateUserResult} "激活成功"
// @Failure 400 {object} response.APIResponse "无效的请求参数"
// @Failure 404 {object} response.APIResponse "用户不存在"
// @Failure 500 {object} response.APIResponse "服务器内部错误"
// @Security ApiKeyAuth
// @Router /users/{user_id}/activate [post]
func (h *UserHandler) ActivateUser(c *gin.Context) {
	userID := c.Param("user_id")
	if userID == "" {
		response.Error(c, http.StatusBadRequest, "用户ID不能为空")
		return
	}

	h.logger.Info(c.Request.Context(), "Processing activate user via CQRS Command", map[string]interface{}{
		"user_id": userID,
	})

	// 构建CQRS Command
	cmd := &commandModel.ActivateUserCommand{
		UserID: userID,
	}

	// 使用CQRS Command Handler处理
	result, err := h.userCommandHandler.ActivateUser(c.Request.Context(), cmd)
	if err != nil {
		h.logger.Error(c.Request.Context(), "Activate user failed via CQRS", err, map[string]interface{}{
			"user_id": userID,
		})
		errors.HandleError(c, err)
		return
	}

	h.logger.Info(c.Request.Context(), "Activate user successful via CQRS", map[string]interface{}{
		"user_id": userID,
		"status":  result.Status,
	})

	response.Success(c, result)
}

// DeactivateUser 禁用用户 - 使用CQRS Command
// @Summary 禁用用户
// @Description 禁用指定的用户账户
// @Tags 用户管理
// @Accept json
// @Produce json
// @Param user_id path string true "用户ID"
// @Param request body user.DeactivateUserRequest true "禁用请求"
// @Success 200 {object} response.APIResponse{data=commandModel.DeactivateUserResult} "禁用成功"
// @Failure 400 {object} response.APIResponse "无效的请求体"
// @Failure 404 {object} response.APIResponse "用户不存在"
// @Failure 500 {object} response.APIResponse "服务器内部错误"
// @Security ApiKeyAuth
// @Router /users/{user_id}/deactivate [post]
func (h *UserHandler) DeactivateUser(c *gin.Context) {
	userID := c.Param("user_id")
	if userID == "" {
		response.Error(c, http.StatusBadRequest, "用户ID不能为空")
		return
	}

	var req user.DeactivateUserRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		response.Error(c, http.StatusBadRequest, "无效的请求体: "+err.Error())
		return
	}

	h.logger.Info(c.Request.Context(), "Processing deactivate user via CQRS Command", map[string]interface{}{
		"user_id": userID,
		"reason":  req.Reason,
	})

	// 构建CQRS Command
	cmd := &commandModel.DeactivateUserCommand{
		UserID: userID,
		Reason: req.Reason,
	}

	// 使用CQRS Command Handler处理
	result, err := h.userCommandHandler.DeactivateUser(c.Request.Context(), cmd)
	if err != nil {
		h.logger.Error(c.Request.Context(), "Deactivate user failed via CQRS", err, map[string]interface{}{
			"user_id": userID,
			"reason":  req.Reason,
		})
		errors.HandleError(c, err)
		return
	}

	h.logger.Info(c.Request.Context(), "Deactivate user successful via CQRS", map[string]interface{}{
		"user_id": userID,
		"status":  result.Status,
	})

	response.Success(c, result)
}
