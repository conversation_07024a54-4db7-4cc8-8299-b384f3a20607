package handler

import (
	"net/http"
	"runtime"
	"time"

	"backend/internal/adapters/http/middleware"
	"backend/pkg/infrastructure/logger"
	"backend/pkg/infrastructure/monitoring"

	"github.com/gin-gonic/gin"
)

// DebugHandler 调试处理器
type DebugHandler struct {
	logger           logger.Logger
	structuredLogger *logger.StructuredLogger
	operationTracker *monitoring.OperationTracker
	metricsManager   *monitoring.MetricsManager
}

// NewDebugHandler 创建调试处理器
func NewDebugHandler(
	log logger.Logger,
	operationTracker *monitoring.OperationTracker,
	metricsManager *monitoring.MetricsManager,
) *DebugHandler {
	return &DebugHandler{
		logger:           log,
		structuredLogger: logger.NewStructuredLogger(log),
		operationTracker: operationTracker,
		metricsManager:   metricsManager,
	}
}

// DebugInfo 调试信息端点
func (h *DebugHandler) DebugInfo(c *gin.Context) {
	requestID := middleware.GetRequestID(c)
	correlationID := middleware.GetCorrelationID(c)

	// 获取运行时信息
	var m runtime.MemStats
	runtime.ReadMemStats(&m)

	debugInfo := gin.H{
		"request_info": gin.H{
			"request_id":     requestID,
			"correlation_id": correlationID,
			"method":         c.Request.Method,
			"path":           c.Request.URL.Path,
			"user_agent":     c.Request.UserAgent(),
			"client_ip":      c.ClientIP(),
			"headers":        c.Request.Header,
		},
		"runtime_info": gin.H{
			"goroutines":   runtime.NumGoroutine(),
			"memory_alloc": m.Alloc,
			"memory_total": m.TotalAlloc,
			"memory_sys":   m.Sys,
			"gc_runs":      m.NumGC,
			"go_version":   runtime.Version(),
		},
		"server_info": gin.H{
			"timestamp": time.Now().UTC(),
			"uptime":    time.Since(time.Now().Add(-time.Hour)), // 示例，实际应该记录启动时间
		},
	}

	h.logger.Info(c.Request.Context(), "Debug info requested",
		"request_id", requestID,
		"correlation_id", correlationID,
		"client_ip", c.ClientIP(),
	)

	c.JSON(http.StatusOK, debugInfo)
}

// TestLogging 测试日志记录端点
func (h *DebugHandler) TestLogging(c *gin.Context) {
	requestID := middleware.GetRequestID(c)
	correlationID := middleware.GetCorrelationID(c)
	ctx := c.Request.Context()

	// 测试不同级别的日志
	h.logger.Debug(ctx, "Debug log test", "request_id", requestID)
	h.logger.Info(ctx, "Info log test", "request_id", requestID)
	h.logger.Warn(ctx, "Warn log test", "request_id", requestID)

	// 测试结构化日志
	h.structuredLogger.LogBusinessOperation(ctx, logger.BusinessOperation{
		Operation: "test_operation",
		TenantID:  "test-tenant",
		UserID:    "test-user",
		Action:    "test_logging",
		Status:    "success",
		Duration:  100 * time.Millisecond,
		RequestID: requestID,
		Details: map[string]interface{}{
			"test_field": "test_value",
		},
	})

	// 测试操作追踪
	start := time.Now()
	h.operationTracker.TrackBusinessOperation(ctx, "test_operation", start, true, map[string]interface{}{
		"test_detail": "test_value",
	})

	c.JSON(http.StatusOK, gin.H{
		"message":        "Logging test completed",
		"request_id":     requestID,
		"correlation_id": correlationID,
		"logs_generated": []string{
			"debug", "info", "warn", "business_operation", "operation_tracking",
		},
	})
}

// TestMetrics 测试指标记录端点
func (h *DebugHandler) TestMetrics(c *gin.Context) {
	requestID := middleware.GetRequestID(c)
	tenantID := "test-tenant"
	userID := "test-user"

	// 测试各种指标
	h.metricsManager.RecordHTTPRequest("GET", "/debug/test-metrics", "200", tenantID, 150*time.Millisecond, 1024, 2048)
	h.metricsManager.RecordDatabaseQuery("SELECT", "test_table", "success", tenantID, 50*time.Millisecond)
	h.metricsManager.RecordCacheOperation("test-cache", "get", tenantID, true, 10*time.Millisecond)
	h.metricsManager.RecordBusinessOperation("test_operation", "success", tenantID, userID, 200*time.Millisecond)
	h.metricsManager.RecordError("test_error", "test_code", tenantID)

	h.logger.Info(c.Request.Context(), "Metrics test completed",
		"request_id", requestID,
		"tenant_id", tenantID,
		"user_id", userID,
	)

	c.JSON(http.StatusOK, gin.H{
		"message":    "Metrics test completed",
		"request_id": requestID,
		"metrics_recorded": []string{
			"http_request", "database_query", "cache_operation", "business_operation", "error",
		},
	})
}

// TestTracing 测试链路追踪端点
func (h *DebugHandler) TestTracing(c *gin.Context) {
	requestID := middleware.GetRequestID(c)
	correlationID := middleware.GetCorrelationID(c)
	ctx := c.Request.Context()

	// 模拟一系列操作
	start := time.Now()

	// 模拟数据库操作
	h.operationTracker.TrackDatabaseOperation(ctx, "SELECT", "users", start, 1, nil)

	// 模拟缓存操作
	h.operationTracker.TrackCacheOperation(ctx, "get", "user:123", start, true, 256, nil)

	// 模拟外部调用
	h.operationTracker.TrackExternalCall(ctx, "payment-service", "/api/v1/payments", "POST", start, 200, 512, 1024, 0, nil)

	// 模拟用户行为
	h.operationTracker.TrackUserAction(ctx, "view_profile", start, true, map[string]interface{}{
		"profile_id": "123",
		"section":    "personal_info",
	})

	// 模拟安全事件
	h.operationTracker.TrackSecurityEvent(ctx, "login_attempt", "success", "low", map[string]interface{}{
		"method": "password",
		"mfa":    true,
	})

	c.JSON(http.StatusOK, gin.H{
		"message":        "Tracing test completed",
		"request_id":     requestID,
		"correlation_id": correlationID,
		"operations_tracked": []string{
			"database_operation", "cache_operation", "external_call", "user_action", "security_event",
		},
	})
}

// TestError 测试错误处理端点
func (h *DebugHandler) TestError(c *gin.Context) {
	requestID := middleware.GetRequestID(c)
	ctx := c.Request.Context()

	// 测试不同类型的错误
	errorType := c.Query("type")

	switch errorType {
	case "panic":
		h.logger.Error(ctx, "About to trigger panic", "request_id", requestID)
		panic("Test panic for debugging")

	case "validation":
		h.logger.Warn(ctx, "Validation error test", "request_id", requestID)
		c.JSON(http.StatusBadRequest, gin.H{
			"error":      "validation_error",
			"message":    "Test validation error",
			"request_id": requestID,
		})

	case "internal":
		h.logger.Error(ctx, "Internal error test", "request_id", requestID)
		c.JSON(http.StatusInternalServerError, gin.H{
			"error":      "internal_error",
			"message":    "Test internal error",
			"request_id": requestID,
		})

	default:
		c.JSON(http.StatusOK, gin.H{
			"message":         "Error test endpoint",
			"request_id":      requestID,
			"available_types": []string{"panic", "validation", "internal"},
			"usage":           "Add ?type=<error_type> to test specific error types",
		})
	}
}

// TestPerformance 测试性能监控端点
func (h *DebugHandler) TestPerformance(c *gin.Context) {
	requestID := middleware.GetRequestID(c)
	ctx := c.Request.Context()

	// 模拟一些性能指标
	start := time.Now()

	// 模拟CPU密集型操作
	for i := 0; i < 1000000; i++ {
		_ = i * i
	}
	cpuTime := time.Since(start)

	// 模拟内存分配
	start = time.Now()
	data := make([]byte, 1024*1024) // 1MB
	_ = data
	memTime := time.Since(start)

	// 记录性能指标
	h.operationTracker.TrackPerformanceMetric(ctx, "cpu_intensive_operation", cpuTime.Seconds(), "seconds", map[string]interface{}{
		"iterations": 1000000,
	})

	h.operationTracker.TrackPerformanceMetric(ctx, "memory_allocation", float64(len(data)), "bytes", map[string]interface{}{
		"allocation_time": memTime.String(),
	})

	c.JSON(http.StatusOK, gin.H{
		"message":     "Performance test completed",
		"request_id":  requestID,
		"cpu_time":    cpuTime.String(),
		"memory_time": memTime.String(),
		"memory_size": len(data),
	})
}

// ContextInfo 上下文信息端点
func (h *DebugHandler) ContextInfo(c *gin.Context) {
	requestID := middleware.GetRequestID(c)
	correlationID := middleware.GetCorrelationID(c)

	// 获取所有上下文信息
	contextInfo := gin.H{
		"request_id":     requestID,
		"correlation_id": correlationID,
		"gin_context": gin.H{
			"method":       c.Request.Method,
			"path":         c.Request.URL.Path,
			"query":        c.Request.URL.RawQuery,
			"client_ip":    c.ClientIP(),
			"user_agent":   c.Request.UserAgent(),
			"content_type": c.ContentType(),
		},
	}

	// 添加所有Gin上下文中的键值对
	ginKeys := gin.H{}
	for key, value := range c.Keys {
		ginKeys[key] = value
	}
	contextInfo["gin_keys"] = ginKeys

	// 添加HTTP头信息
	contextInfo["headers"] = c.Request.Header

	c.JSON(http.StatusOK, contextInfo)
}
