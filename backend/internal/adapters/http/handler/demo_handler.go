package handler

import (
	"net/http"
	"strconv"

	"backend/pkg/common/response"
	"backend/pkg/infrastructure/logger"
	"backend/pkg/infrastructure/monitoring"

	"github.com/gin-gonic/gin"
	"go.opentelemetry.io/otel/attribute"
)

// DemoHandler 演示处理器
type DemoHandler struct {
	logger         logger.Logger
	tracingManager *monitoring.TracingManager
}

// NewDemoHandler 创建演示处理器
func NewDemoHandler(logger logger.Logger, tracingManager *monitoring.TracingManager) *DemoHandler {
	return &DemoHandler{
		logger:         logger,
		tracingManager: tracingManager,
	}
}

// DemoRequest 演示请求结构
type DemoRequest struct {
	// 用户名
	Name string `json:"name" validate:"required,min=1,max=50" example:"张三"`
	// 年龄
	Age int `json:"age" validate:"required,min=1,max=120" example:"25"`
	// 邮箱地址
	Email string `json:"email" validate:"required,email" example:"<EMAIL>"`
	// 用户类型: admin=管理员, user=普通用户, guest=访客
	Type string `json:"type" validate:"required,oneof=admin user guest" enums:"admin,user,guest" example:"user"`
} // @name DemoRequest

// DemoResponse 演示响应结构
type DemoResponse struct {
	// 处理结果ID
	ID string `json:"id" example:"demo_123456"`
	// 处理时间戳
	ProcessedAt string `json:"processed_at" example:"2023-01-01T00:00:00Z"`
	// 处理状态
	Status string `json:"status" example:"success"`
	// 用户信息
	UserInfo DemoUserInfo `json:"user_info"`
} // @name DemoResponse

// DemoUserInfo 演示用户信息
type DemoUserInfo struct {
	// 显示名称
	DisplayName string `json:"display_name" example:"张三"`
	// 用户级别
	Level string `json:"level" example:"standard"`
	// 是否为VIP用户
	IsVIP bool `json:"is_vip" example:"false"`
} // @name DemoUserInfo

// Hello 演示GET接口
// @Summary 演示问候接口
// @Description 这是一个演示接口，用于展示基本的GET请求处理
// @Tags 系统管理
// @Accept json
// @Produce json
// @Param name query string false "用户名称" example:"张三"
// @Param age query int false "用户年龄" minimum(1) maximum(120) example:"25"
// @Param format query string false "返回格式" enums(json,xml) default(json) example:"json"
// @Success 200 {object} response.APIResponse{data=map[string]interface{}} "请求成功"
// @Failure 400 {object} response.APIResponse "请求参数错误"
// @Failure 500 {object} response.APIResponse "服务器内部错误"
// @Router /demo/hello [get]
func (h *DemoHandler) Hello(c *gin.Context) {
	ctx, span := h.tracingManager.StartSpan(c.Request.Context(), "demo.hello")
	defer span.End()

	name := c.DefaultQuery("name", "匿名用户")
	ageStr := c.DefaultQuery("age", "0")
	format := c.DefaultQuery("format", "json")

	age, err := strconv.Atoi(ageStr)
	if err != nil {
		h.logger.Error(c.Request.Context(), "年龄参数转换失败", "error", err.Error())
		response.Error(c, http.StatusBadRequest, "年龄参数格式错误")
		return
	}

	span.SetAttributes(
		attribute.String("demo.name", name),
		attribute.Int("demo.age", age),
		attribute.String("demo.format", format),
	)

	h.logger.Info(ctx, "处理问候请求",
		"name", name,
		"age", age,
		"format", format,
	)

	// 模拟业务逻辑
	result := map[string]interface{}{
		"greeting":    "你好，" + name,
		"age":         age,
		"format":      format,
		"server_time": "2023-01-01T00:00:00Z",
		"version":     "1.0.0",
	}

	response.Success(c, result)
}

// Process 演示POST接口
// @Summary 演示数据处理接口
// @Description 这是一个演示接口，用于展示POST请求处理和数据验证
// @Tags 系统管理
// @Accept json
// @Produce json
// @Param request body DemoRequest true "处理请求数据"
// @Success 200 {object} response.APIResponse{data=DemoResponse} "处理成功"
// @Success 201 {object} response.APIResponse{data=DemoResponse} "创建成功"
// @Failure 400 {object} response.APIResponse "请求参数错误"
// @Failure 422 {object} response.APIResponse "数据验证失败"
// @Failure 500 {object} response.APIResponse "服务器内部错误"
// @Router /demo/process [post]
func (h *DemoHandler) Process(c *gin.Context) {
	ctx, span := h.tracingManager.StartSpan(c.Request.Context(), "demo.process")
	defer span.End()

	var req DemoRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		h.logger.Error(c.Request.Context(), "请求数据绑定失败", "error", err.Error())
		response.Error(c, http.StatusBadRequest, "请求数据格式错误: "+err.Error())
		return
	}

	// 模拟数据验证
	if req.Age < 1 || req.Age > 120 {
		response.Error(c, http.StatusUnprocessableEntity, "年龄必须在1-120之间")
		return
	}

	// 模拟业务处理
	resp := DemoResponse{
		ID:          "demo_" + strconv.Itoa(int(c.GetInt64("timestamp"))),
		ProcessedAt: "2023-01-01T00:00:00Z",
		Status:      "success",
		UserInfo: DemoUserInfo{
			DisplayName: req.Name,
			Level:       h.getUserLevel(req.Type),
			IsVIP:       req.Type == "admin",
		},
	}

	h.logger.Info(ctx, "处理数据请求",
		"name", req.Name,
		"age", req.Age,
		"email", req.Email,
		"type", req.Type,
	)

	// 根据业务逻辑返回不同状态码
	if req.Type == "admin" {
		response.SuccessWithStatus(c, http.StatusCreated, resp)
	} else {
		response.Success(c, resp)
	}
}

// getUserLevel 获取用户级别（内部方法）
func (h *DemoHandler) getUserLevel(userType string) string {
	switch userType {
	case "admin":
		return "premium"
	case "user":
		return "standard"
	case "guest":
		return "basic"
	default:
		return "unknown"
	}
}
