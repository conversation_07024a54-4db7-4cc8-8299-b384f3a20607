package handler

import (
	"net/http"

	"backend/pkg/infrastructure/logger"

	"github.com/gin-gonic/gin"
)

// HealthHandler 健康检查处理器
type HealthHandler struct {
	logger logger.Logger
}

// NewHealthHandler 创建健康检查处理器
func NewHealthHandler(logger logger.Logger) *HealthHandler {
	return &HealthHandler{
		logger: logger,
	}
}

// Health 健康检查端点
func (h *HealthHandler) Health(c *gin.Context) {
	h.logger.Info(c.Request.Context(), "Health check requested")

	c.JSON(http.StatusOK, gin.H{
		"status":  "ok",
		"service": "nine-wings-erp",
		"version": "1.0.0",
	})
}

// Ready 就绪检查端点
func (h *HealthHandler) Ready(c *gin.Context) {
	h.logger.Info(c.Request.Context(), "Ready check requested")

	c.<PERSON><PERSON>(http.StatusOK, gin.H{
		"status": "ready",
	})
}
