package handler

import (
	"net/http"
	"strconv"

	"github.com/gin-gonic/gin"

	"backend/internal/application/dto"
	paginationDTO "backend/internal/application/pagination/dto"
	"backend/internal/application/usecase/tenant"
	"backend/internal/shared/errors"
	"backend/pkg/common/response"
)

// TenantHandler 租户HTTP处理器
type TenantHandler struct {
	tenantUseCase *tenant.TenantUseCase
}

// NewTenantHandler 创建租户处理器
func NewTenantHandler(tenantUseCase *tenant.TenantUseCase) *TenantHandler {
	return &TenantHandler{
		tenantUseCase: tenantUseCase,
	}
}

// CreateTenant 创建租户
// @Summary 创建租户
// @Description 创建新的租户
// @Tags 租户管理
// @Accept json
// @Produce json
// @Param request body dto.CreateTenantRequestDTO true "创建租户请求"
// @Success 201 {object} response.APIResponse{data=dto.TenantDTO} "创建成功"
// @Failure 400 {object} response.APIResponse "请求参数错误"
// @Failure 409 {object} response.APIResponse "域名已存在"
// @Failure 500 {object} response.APIResponse "服务器内部错误"
// @Router /tenants [post]
func (h *TenantHandler) CreateTenant(c *gin.Context) {
	var req dto.CreateTenantRequestDTO
	if err := c.ShouldBindJSON(&req); err != nil {
		response.Error(c, http.StatusBadRequest, "请求参数错误: "+err.Error())
		return
	}

	result, err := h.tenantUseCase.CreateTenant(c.Request.Context(), &req)
	if err != nil {
		errors.HandleError(c, err)
		return
	}

	response.SuccessWithStatus(c, http.StatusCreated, result)
}

// GetTenant 获取租户详情
// @Summary 获取租户详情
// @Description 根据租户ID获取租户详细信息
// @Tags 租户管理
// @Accept json
// @Produce json
// @Param id path string true "租户ID"
// @Success 200 {object} response.APIResponse{data=dto.TenantDTO} "获取成功"
// @Failure 404 {object} response.APIResponse "租户不存在"
// @Failure 500 {object} response.APIResponse "服务器内部错误"
// @Router /tenants/{id} [get]
func (h *TenantHandler) GetTenant(c *gin.Context) {
	tenantID := c.Param("id")
	if tenantID == "" {
		response.Error(c, http.StatusBadRequest, "租户ID不能为空")
		return
	}

	result, err := h.tenantUseCase.GetTenant(c.Request.Context(), tenantID)
	if err != nil {
		errors.HandleError(c, err)
		return
	}

	response.Success(c, result)
}

// UpdateTenant 更新租户信息
// @Summary 更新租户信息
// @Description 更新租户的基本信息
// @Tags 租户管理
// @Accept json
// @Produce json
// @Param id path string true "租户ID"
// @Param request body dto.UpdateTenantRequestDTO true "更新租户请求"
// @Success 200 {object} response.APIResponse{data=dto.TenantDTO} "更新成功"
// @Failure 400 {object} response.APIResponse "请求参数错误"
// @Failure 404 {object} response.APIResponse "租户不存在"
// @Failure 500 {object} response.APIResponse "服务器内部错误"
// @Router /tenants/{id} [put]
func (h *TenantHandler) UpdateTenant(c *gin.Context) {
	tenantID := c.Param("id")
	if tenantID == "" {
		response.Error(c, http.StatusBadRequest, "租户ID不能为空")
		return
	}

	var req dto.UpdateTenantRequestDTO
	if err := c.ShouldBindJSON(&req); err != nil {
		response.Error(c, http.StatusBadRequest, "请求参数错误: "+err.Error())
		return
	}

	result, err := h.tenantUseCase.UpdateTenant(c.Request.Context(), tenantID, &req)
	if err != nil {
		errors.HandleError(c, err)
		return
	}

	response.Success(c, result)
}

// DeleteTenant 删除租户
// @Summary 删除租户
// @Description 软删除租户
// @Tags 租户管理
// @Accept json
// @Produce json
// @Param id path string true "租户ID"
// @Success 200 {object} response.APIResponse "删除成功"
// @Failure 404 {object} response.APIResponse "租户不存在"
// @Failure 500 {object} response.APIResponse "服务器内部错误"
// @Router /tenants/{id} [delete]
func (h *TenantHandler) DeleteTenant(c *gin.Context) {
	tenantID := c.Param("id")
	if tenantID == "" {
		response.Error(c, http.StatusBadRequest, "租户ID不能为空")
		return
	}

	err := h.tenantUseCase.DeleteTenant(c.Request.Context(), tenantID)
	if err != nil {
		errors.HandleError(c, err)
		return
	}

	response.Success(c, nil)
}

// // ListTenants 获取租户列表
// // @Summary 获取租户列表
// // @Description 分页获取租户列表，支持过滤和排序
// // @Tags 租户管理
// // @Accept json
// // @Produce json
// // @Param page query int false "页码" default(1)
// // @Param page_size query int false "每页数量" default(10)
// // @Param sort_by query string false "排序字段" default(created_at)
// // @Param sort_desc query bool false "是否降序" default(true)
// // @Param name query string false "租户名称"
// // @Param domain query string false "域名"
// // @Param type query string false "租户类型"
// // @Param status query string false "租户状态"
// // @Param industry query string false "行业"
// // @Param country query string false "国家"
// // @Param keywords query string false "关键词搜索"
// // @Success 200 {object} response.APIResponse{data=dto.TenantListDTO} "获取成功"
// // @Failure 500 {object} response.APIResponse "服务器内部错误"
// // @Router /tenants [get]
// func (h *TenantHandler) ListTenants(c *gin.Context) {
// 	// 解析分页参数
// 	page, _ := strconv.Atoi(c.DefaultQuery("page", "1"))
// 	pageSize, _ := strconv.Atoi(c.DefaultQuery("page_size", "10"))
// 	sortBy := c.DefaultQuery("sort_by", "created_at")
// 	sortDesc, _ := strconv.ParseBool(c.DefaultQuery("sort_desc", "true"))

// 	pagination := &dto.PaginationRequestDTO{
// 		Page:     page,
// 		PageSize: pageSize,
// 		SortBy:   sortBy,
// 		SortDesc: sortDesc,
// 	}

// 	// 解析过滤参数
// 	filter := &dto.TenantFilterRequestDTO{
// 		Name:     c.Query("name"),
// 		Domain:   c.Query("domain"),
// 		Type:     c.Query("type"),
// 		Status:   c.Query("status"),
// 		Industry: c.Query("industry"),
// 		Country:  c.Query("country"),
// 		Province: c.Query("province"),
// 		City:     c.Query("city"),
// 		Keywords: c.Query("keywords"),
// 	}

// 	result, err := h.tenantUseCase.ListTenants(c.Request.Context(), filter, pagination)
// 	if err != nil {
// 		errors.HandleError(c, err)
// 		return
// 	}

// 	response.Success(c, result)
// }

// // SearchTenants 搜索租户
// // @Summary 搜索租户
// // @Description 根据关键词搜索租户
// // @Tags 租户管理
// // @Accept json
// // @Produce json
// // @Param request body dto.SearchTenantsRequestDTO true "搜索请求"
// // @Success 200 {object} response.APIResponse{data=dto.TenantListDTO} "搜索成功"
// // @Failure 400 {object} response.APIResponse "请求参数错误"
// // @Failure 500 {object} response.APIResponse "服务器内部错误"
// // @Router /tenants/search [post]
// func (h *TenantHandler) SearchTenants(c *gin.Context) {
// 	var req dto.SearchTenantsRequestDTO
// 	if err := c.ShouldBindJSON(&req); err != nil {
// 		response.Error(c, http.StatusBadRequest, "请求参数错误: "+err.Error())
// 		return
// 	}

// 	result, err := h.tenantUseCase.SearchTenants(c.Request.Context(), &req)
// 	if err != nil {
// 		errors.HandleError(c, err)
// 		return
// 	}

// 	response.Success(c, result)
// }

// ListTenants 获取租户列表（新分页实现）
// @Summary 获取租户列表（新分页）
// @Description 使用新分页框架获取租户列表，支持高级过滤和排序
// @Tags 租户管理
// @Accept json
// @Produce json
// @Param page query int false "页码" default(1)
// @Param page_size query int false "每页数量" default(10)
// @Param sort_by query string false "排序字段" default(created_at)
// @Param sort_desc query bool false "是否降序" default(true)
// @Param search query string false "搜索关键词"
// @Success 200 {object} response.APIResponse{data=paginationDTO.PaginationResponseDTO[dto.TenantDTO]} "获取成功"
// @Failure 500 {object} response.APIResponse "服务器内部错误"
// @Router /tenants [get]
func (h *TenantHandler) ListTenants(c *gin.Context) {
	// 解析分页参数
	page, _ := strconv.Atoi(c.DefaultQuery("page", "1"))
	pageSize, _ := strconv.Atoi(c.DefaultQuery("page_size", "10"))
	sortBy := c.DefaultQuery("sort_by", "created_at")
	sortDesc, _ := strconv.ParseBool(c.DefaultQuery("sort_desc", "true"))
	search := c.Query("search")

	req := &paginationDTO.PaginationRequestDTO{
		Page:     page,
		PageSize: pageSize,
		SortBy:   sortBy,
		SortDesc: sortDesc,
		Search:   search,
	}

	// 设置默认值
	req.SetDefaults()

	result, err := h.tenantUseCase.ListTenants(c.Request.Context(), req)
	if err != nil {
		errors.HandleError(c, err)
		return
	}

	response.Success(c, result)
}

// SearchTenants 搜索租户（新分页实现）
// @Summary 搜索租户（新分页）
// @Description 使用新分页框架搜索租户，支持高级过滤条件
// @Tags 租户管理
// @Accept json
// @Produce json
// @Param page query int false "页码" default(1)
// @Param page_size query int false "每页数量" default(10)
// @Param sort_by query string false "排序字段" default(created_at)
// @Param sort_desc query bool false "是否降序" default(true)
// @Param search query string false "搜索关键词"
// @Param status query string false "租户状态" Enums(active,inactive,suspended,expired,terminated)
// @Param type query string false "租户类型" Enums(system,enterprise,professional,basic,trial)
// @Param industry query string false "行业"
// @Param country query string false "国家"
// @Param province query string false "省份"
// @Param city query string false "城市"
// @Param created_from query string false "创建时间起始" format(date-time)
// @Param created_to query string false "创建时间结束" format(date-time)
// @Success 200 {object} response.APIResponse{data=paginationDTO.PaginationResponseDTO[dto.TenantDTO]} "搜索成功"
// @Failure 400 {object} response.APIResponse "请求参数错误"
// @Failure 500 {object} response.APIResponse "服务器内部错误"
// @Router /tenants/search [get]
func (h *TenantHandler) SearchTenants(c *gin.Context) {
	// 解析分页参数
	page, _ := strconv.Atoi(c.DefaultQuery("page", "1"))
	pageSize, _ := strconv.Atoi(c.DefaultQuery("page_size", "10"))
	sortBy := c.DefaultQuery("sort_by", "created_at")
	sortDesc, _ := strconv.ParseBool(c.DefaultQuery("sort_desc", "true"))
	search := c.Query("search")

	req := &paginationDTO.PaginationRequestDTO{
		Page:     page,
		PageSize: pageSize,
		SortBy:   sortBy,
		SortDesc: sortDesc,
		Search:   search,
	}

	// 设置默认值
	req.SetDefaults()

	// 构建过滤条件
	filters := make(map[string]interface{})

	if status := c.Query("status"); status != "" {
		filters["status"] = status
	}

	if tenantType := c.Query("type"); tenantType != "" {
		filters["type"] = tenantType
	}

	if industry := c.Query("industry"); industry != "" {
		filters["industry"] = industry
	}

	if country := c.Query("country"); country != "" {
		filters["country"] = country
	}

	if province := c.Query("province"); province != "" {
		filters["province"] = province
	}

	if city := c.Query("city"); city != "" {
		filters["city"] = city
	}

	if createdFrom := c.Query("created_from"); createdFrom != "" {
		filters["created_from"] = createdFrom
	}

	if createdTo := c.Query("created_to"); createdTo != "" {
		filters["created_to"] = createdTo
	}

	result, err := h.tenantUseCase.SearchTenants(c.Request.Context(), req, filters)
	if err != nil {
		errors.HandleError(c, err)
		return
	}

	response.Success(c, result)
}

// ActivateTenant 激活租户
// @Summary 激活租户
// @Description 激活指定的租户
// @Tags 租户管理
// @Accept json
// @Produce json
// @Param id path string true "租户ID"
// @Success 200 {object} response.APIResponse "激活成功"
// @Failure 404 {object} response.APIResponse "租户不存在"
// @Failure 500 {object} response.APIResponse "服务器内部错误"
// @Router /tenants/{id}/activate [put]
func (h *TenantHandler) ActivateTenant(c *gin.Context) {
	tenantID := c.Param("id")
	if tenantID == "" {
		response.Error(c, http.StatusBadRequest, "租户ID不能为空")
		return
	}

	err := h.tenantUseCase.ActivateTenant(c.Request.Context(), tenantID)
	if err != nil {
		errors.HandleError(c, err)
		return
	}

	response.Success(c, nil)
}

// SuspendTenant 暂停租户
// @Summary 暂停租户
// @Description 暂停指定的租户
// @Tags 租户管理
// @Accept json
// @Produce json
// @Param id path string true "租户ID"
// @Param request body dto.UpdateTenantStatusRequestDTO true "暂停租户请求"
// @Success 200 {object} response.APIResponse "暂停成功"
// @Failure 400 {object} response.APIResponse "请求参数错误"
// @Failure 404 {object} response.APIResponse "租户不存在"
// @Failure 500 {object} response.APIResponse "服务器内部错误"
// @Router /tenants/{id}/suspend [put]
func (h *TenantHandler) SuspendTenant(c *gin.Context) {
	tenantID := c.Param("id")
	if tenantID == "" {
		response.Error(c, http.StatusBadRequest, "租户ID不能为空")
		return
	}

	var req dto.UpdateTenantStatusRequestDTO
	if err := c.ShouldBindJSON(&req); err != nil {
		response.Error(c, http.StatusBadRequest, "请求参数错误: "+err.Error())
		return
	}

	err := h.tenantUseCase.SuspendTenant(c.Request.Context(), tenantID, req.Reason)
	if err != nil {
		errors.HandleError(c, err)
		return
	}

	response.Success(c, nil)
}

// TerminateTenant 终止租户
// @Summary 终止租户
// @Description 终止指定的租户
// @Tags 租户管理
// @Accept json
// @Produce json
// @Param id path string true "租户ID"
// @Param request body dto.UpdateTenantStatusRequestDTO true "终止租户请求"
// @Success 200 {object} response.APIResponse "终止成功"
// @Failure 400 {object} response.APIResponse "请求参数错误"
// @Failure 404 {object} response.APIResponse "租户不存在"
// @Failure 500 {object} response.APIResponse "服务器内部错误"
// @Router /tenants/{id}/terminate [put]
func (h *TenantHandler) TerminateTenant(c *gin.Context) {
	tenantID := c.Param("id")
	if tenantID == "" {
		response.Error(c, http.StatusBadRequest, "租户ID不能为空")
		return
	}

	var req dto.UpdateTenantStatusRequestDTO
	if err := c.ShouldBindJSON(&req); err != nil {
		response.Error(c, http.StatusBadRequest, "请求参数错误: "+err.Error())
		return
	}

	err := h.tenantUseCase.TerminateTenant(c.Request.Context(), tenantID, req.Reason)
	if err != nil {
		errors.HandleError(c, err)
		return
	}

	response.Success(c, nil)
}

// BatchUpdateStatus 批量更新租户状态
// @Summary 批量更新租户状态
// @Description 批量更新多个租户的状态
// @Tags 租户管理
// @Accept json
// @Produce json
// @Param request body dto.BatchUpdateStatusRequestDTO true "批量更新状态请求"
// @Success 200 {object} response.APIResponse "更新成功"
// @Failure 400 {object} response.APIResponse "请求参数错误"
// @Failure 500 {object} response.APIResponse "服务器内部错误"
// @Router /tenants/batch-status [put]
func (h *TenantHandler) BatchUpdateStatus(c *gin.Context) {
	var req dto.BatchUpdateStatusRequestDTO
	if err := c.ShouldBindJSON(&req); err != nil {
		response.Error(c, http.StatusBadRequest, "请求参数错误: "+err.Error())
		return
	}

	err := h.tenantUseCase.BatchUpdateStatus(c.Request.Context(), &req)
	if err != nil {
		errors.HandleError(c, err)
		return
	}

	response.Success(c, nil)
}

// GetTenantQuota 获取租户配额信息
// @Summary 获取租户配额信息
// @Description 获取指定租户的配额使用情况
// @Tags 租户管理
// @Accept json
// @Produce json
// @Param id path string true "租户ID"
// @Success 200 {object} response.APIResponse{data=dto.TenantQuotaDTO} "获取成功"
// @Failure 404 {object} response.APIResponse "租户不存在"
// @Failure 500 {object} response.APIResponse "服务器内部错误"
// @Router /tenants/{id}/quota [get]
func (h *TenantHandler) GetTenantQuota(c *gin.Context) {
	tenantID := c.Param("id")
	if tenantID == "" {
		response.Error(c, http.StatusBadRequest, "租户ID不能为空")
		return
	}

	result, err := h.tenantUseCase.GetTenantQuota(c.Request.Context(), tenantID)
	if err != nil {
		errors.HandleError(c, err)
		return
	}

	response.Success(c, result)
}

// UpdateTenantQuota 更新租户配额
// @Summary 更新租户配额
// @Description 更新指定租户的配额限制
// @Tags 租户管理
// @Accept json
// @Produce json
// @Param id path string true "租户ID"
// @Param request body dto.UpdateQuotaLimitsRequestDTO true "更新配额请求"
// @Success 200 {object} response.APIResponse{data=dto.TenantQuotaDTO} "更新成功"
// @Failure 400 {object} response.APIResponse "请求参数错误"
// @Failure 404 {object} response.APIResponse "租户不存在"
// @Failure 500 {object} response.APIResponse "服务器内部错误"
// @Router /tenants/{id}/quota [put]
func (h *TenantHandler) UpdateTenantQuota(c *gin.Context) {
	tenantID := c.Param("id")
	if tenantID == "" {
		response.Error(c, http.StatusBadRequest, "租户ID不能为空")
		return
	}

	var req dto.UpdateQuotaLimitsRequestDTO
	if err := c.ShouldBindJSON(&req); err != nil {
		response.Error(c, http.StatusBadRequest, "请求参数错误: "+err.Error())
		return
	}

	result, err := h.tenantUseCase.UpdateTenantQuota(c.Request.Context(), tenantID, &req)
	if err != nil {
		errors.HandleError(c, err)
		return
	}

	response.Success(c, result)
}

// GetTenantHealth 获取租户健康状态
// @Summary 获取租户健康状态
// @Description 获取指定租户的健康状态检查结果
// @Tags 租户管理
// @Accept json
// @Produce json
// @Param id path string true "租户ID"
// @Success 200 {object} response.APIResponse{data=dto.TenantHealthDTO} "获取成功"
// @Failure 404 {object} response.APIResponse "租户不存在"
// @Failure 500 {object} response.APIResponse "服务器内部错误"
// @Router /tenants/{id}/health [get]
func (h *TenantHandler) GetTenantHealth(c *gin.Context) {
	tenantID := c.Param("id")
	if tenantID == "" {
		response.Error(c, http.StatusBadRequest, "租户ID不能为空")
		return
	}

	result, err := h.tenantUseCase.GetTenantHealth(c.Request.Context(), tenantID)
	if err != nil {
		errors.HandleError(c, err)
		return
	}

	response.Success(c, result)
}

// ValidateDomain 验证域名可用性
// @Summary 验证域名可用性
// @Description 检查域名是否可以使用
// @Tags 租户管理
// @Accept json
// @Produce json
// @Param request body dto.ValidateDomainRequestDTO true "验证域名请求"
// @Success 200 {object} response.APIResponse{data=map[string]bool} "验证成功"
// @Failure 400 {object} response.APIResponse "请求参数错误"
// @Failure 500 {object} response.APIResponse "服务器内部错误"
// @Router /tenants/validate-domain [post]
func (h *TenantHandler) ValidateDomain(c *gin.Context) {
	var req dto.ValidateDomainRequestDTO
	if err := c.ShouldBindJSON(&req); err != nil {
		response.Error(c, http.StatusBadRequest, "请求参数错误: "+err.Error())
		return
	}

	available, err := h.tenantUseCase.ValidateDomain(c.Request.Context(), req.Domain)
	if err != nil {
		errors.HandleError(c, err)
		return
	}

	result := map[string]bool{
		"available": available,
	}
	response.Success(c, result)
}
