package entity

import (
	"fmt"

	"backend/internal/domain/menu/valueobject"
	"backend/internal/shared/types"
)

// Menu 菜单实体 - 租户范围内的菜单管理
type Menu struct {
	types.TenantScopedEntity

	// 菜单基本信息
	Name      string `gorm:"size:100;not null" json:"name"`
	Title     string `gorm:"size:100;not null" json:"title"` // 显示标题
	Icon      string `gorm:"size:100" json:"icon"`           // 图标
	Path      string `gorm:"size:200;not null" json:"path"`  // 路由路径
	Component string `gorm:"size:200" json:"component"`      // 前端组件路径

	// 层级关系
	ParentID string `gorm:"size:36;index" json:"parent_id"` // 父菜单ID
	Level    int    `gorm:"default:1" json:"level"`         // 菜单层级
	Sort     int    `gorm:"default:0" json:"sort"`          // 排序

	// 权限控制
	Permission string `gorm:"size:200" json:"permission"` // 关联权限标识
	Resource   string `gorm:"size:100" json:"resource"`   // 资源标识
	Action     string `gorm:"size:100" json:"action"`     // 操作标识

	// 菜单属性
	MenuType   valueobject.MenuType   `gorm:"default:1" json:"menu_type"`       // 菜单类型
	Status     valueobject.MenuStatus `gorm:"default:1" json:"status"`          // 菜单状态
	IsVisible  bool                   `gorm:"default:true" json:"is_visible"`   // 是否可见
	IsExternal bool                   `gorm:"default:false" json:"is_external"` // 是否外部链接
	KeepAlive  bool                   `gorm:"default:false" json:"keep_alive"`  // 是否缓存

	// 元数据
	Meta valueobject.MenuMeta `gorm:"type:jsonb" json:"meta"` // 扩展元数据

	// 关联关系（不持久化）
	Children []*Menu `gorm:"-" json:"children,omitempty"` // 子菜单
	Parent   *Menu   `gorm:"-" json:"parent,omitempty"`   // 父菜单
}

// TableName 指定表名
func (m *Menu) TableName() string {
	return "menus"
}

// 注意：ID生成应该在仓储层处理，而不是在实体层使用GORM钩子

// NewMenu 创建新菜单
// 注意：ID生成应该在仓储层处理，这里只创建业务实体
func NewMenu(tenantID, name, title, path string, menuType valueobject.MenuType) (*Menu, error) {
	// 验证必填字段
	if tenantID == "" {
		return nil, fmt.Errorf("租户ID不能为空")
	}
	if name == "" {
		return nil, fmt.Errorf("菜单名称不能为空")
	}
	if title == "" {
		return nil, fmt.Errorf("菜单标题不能为空")
	}
	if path == "" {
		return nil, fmt.Errorf("菜单路径不能为空")
	}

	// 验证菜单类型
	if !menuType.IsValid() {
		return nil, fmt.Errorf("无效的菜单类型: %v", menuType)
	}

	// 创建菜单路径值对象
	menuPath, err := valueobject.NewMenuPath(path)
	if err != nil {
		return nil, fmt.Errorf("创建菜单路径失败: %w", err)
	}

	// 创建菜单元数据
	meta := valueobject.NewMenuMeta(title)

	// 创建空的租户范围实体，ID将在仓储层生成
	tenantEntity := types.NewEmptyTenantScopedEntity()
	tenantEntity.SetTenantID(tenantID)

	menu := &Menu{
		TenantScopedEntity: tenantEntity,
		Name:               name,
		Title:              title,
		Path:               menuPath.Value(),
		MenuType:           menuType,
		Status:             valueobject.MenuStatusActive,
		IsVisible:          true,
		IsExternal:         false,
		KeepAlive:          false,
		Level:              1,
		Sort:               0,
		Meta:               *meta,
	}

	return menu, nil
}

// SetParent 设置父菜单
func (m *Menu) SetParent(parentID string, level int) error {
	if level < 1 {
		return fmt.Errorf("菜单层级必须大于0")
	}
	if level > 5 { // 限制最大层级
		return fmt.Errorf("菜单层级不能超过5级")
	}

	m.ParentID = parentID
	m.Level = level
	return nil
}

// SetIcon 设置图标
func (m *Menu) SetIcon(icon string) {
	m.Icon = icon
}

// SetComponent 设置组件路径
func (m *Menu) SetComponent(component string) {
	m.Component = component
}

// SetSort 设置排序
func (m *Menu) SetSort(sort int) {
	m.Sort = sort
}

// SetPermission 设置权限（使用权限码）
func (m *Menu) SetPermission(permission string) error {
	menuPermission, err := valueobject.NewMenuPermissionWithCode(permission)
	if err != nil {
		return fmt.Errorf("设置菜单权限失败: %w", err)
	}

	m.Permission = menuPermission.Permission()
	m.Resource = ""
	m.Action = ""
	return nil
}

// SetResourceAction 设置权限（使用资源和操作）
func (m *Menu) SetResourceAction(resource, action string) error {
	menuPermission, err := valueobject.NewMenuPermission(resource, action)
	if err != nil {
		return fmt.Errorf("设置菜单权限失败: %w", err)
	}

	m.Resource = menuPermission.Resource()
	m.Action = menuPermission.Action()
	m.Permission = ""
	return nil
}

// SetVisible 设置可见性
func (m *Menu) SetVisible(visible bool) {
	m.IsVisible = visible
	if visible {
		m.Status = valueobject.MenuStatusActive
	} else {
		m.Status = valueobject.MenuStatusHidden
	}
}

// SetExternal 设置外部链接
func (m *Menu) SetExternal(external bool) {
	m.IsExternal = external
}

// SetKeepAlive 设置缓存
func (m *Menu) SetKeepAlive(keepAlive bool) {
	m.KeepAlive = keepAlive
}

// UpdateMeta 更新元数据
func (m *Menu) UpdateMeta(meta valueobject.MenuMeta) error {
	if err := meta.Validate(); err != nil {
		return fmt.Errorf("菜单元数据验证失败: %w", err)
	}

	m.Meta = meta
	return nil
}

// Activate 激活菜单
func (m *Menu) Activate() {
	m.Status = valueobject.MenuStatusActive
	m.IsVisible = true
}

// Deactivate 停用菜单
func (m *Menu) Deactivate() {
	m.Status = valueobject.MenuStatusInactive
	m.IsVisible = false
}

// Hide 隐藏菜单
func (m *Menu) Hide() {
	m.Status = valueobject.MenuStatusHidden
	m.IsVisible = false
}

// Disable 禁用菜单
func (m *Menu) Disable() {
	m.Status = valueobject.MenuStatusDisabled
	m.IsVisible = false
}

// IsActive 检查菜单是否激活
func (m *Menu) IsActive() bool {
	return m.Status == valueobject.MenuStatusActive
}

// IsRoot 检查是否为根菜单
func (m *Menu) IsRoot() bool {
	return m.ParentID == ""
}

// IsDirectory 检查是否为目录类型
func (m *Menu) IsDirectory() bool {
	return m.MenuType == valueobject.MenuTypeDirectory
}

// IsMenuType 检查是否为菜单类型
func (m *Menu) IsMenuType() bool {
	return m.MenuType == valueobject.MenuTypeMenu
}

// IsButton 检查是否为按钮类型
func (m *Menu) IsButton() bool {
	return m.MenuType == valueobject.MenuTypeButton
}

// HasPermission 检查是否有权限设置
func (m *Menu) HasPermission() bool {
	return m.Permission != "" || (m.Resource != "" && m.Action != "")
}

// GetPermissionString 获取权限字符串表示
func (m *Menu) GetPermissionString() string {
	if m.Permission != "" {
		return m.Permission
	}
	if m.Resource != "" && m.Action != "" {
		return fmt.Sprintf("%s:%s", m.Resource, m.Action)
	}
	return ""
}

// AddChild 添加子菜单
func (m *Menu) AddChild(child *Menu) error {
	if child == nil {
		return fmt.Errorf("子菜单不能为空")
	}

	// 设置父子关系
	child.ParentID = m.BusinessID
	child.Level = m.Level + 1
	child.TenantID = m.TenantID

	// 添加到子菜单列表
	if m.Children == nil {
		m.Children = make([]*Menu, 0)
	}
	m.Children = append(m.Children, child)

	return nil
}

// RemoveChild 移除子菜单
func (m *Menu) RemoveChild(childID string) {
	if m.Children == nil {
		return
	}

	for i, child := range m.Children {
		if child.BusinessID == childID {
			m.Children = append(m.Children[:i], m.Children[i+1:]...)
			break
		}
	}
}

// GetChildrenCount 获取子菜单数量
func (m *Menu) GetChildrenCount() int {
	if m.Children == nil {
		return 0
	}
	return len(m.Children)
}

// HasChildren 检查是否有子菜单
func (m *Menu) HasChildren() bool {
	return m.GetChildrenCount() > 0
}

// Validate 验证菜单数据
func (m *Menu) Validate() error {
	// 验证基础字段
	if m.TenantID == "" {
		return fmt.Errorf("租户ID不能为空")
	}
	if m.Name == "" {
		return fmt.Errorf("菜单名称不能为空")
	}
	if m.Title == "" {
		return fmt.Errorf("菜单标题不能为空")
	}
	if m.Path == "" {
		return fmt.Errorf("菜单路径不能为空")
	}

	// 验证菜单类型
	if !m.MenuType.IsValid() {
		return fmt.Errorf("无效的菜单类型: %v", m.MenuType)
	}

	// 验证菜单状态
	if !m.Status.IsValid() {
		return fmt.Errorf("无效的菜单状态: %v", m.Status)
	}

	// 验证层级
	if m.Level < 1 || m.Level > 5 {
		return fmt.Errorf("菜单层级必须在1-5之间")
	}

	// 验证元数据
	if err := m.Meta.Validate(); err != nil {
		return fmt.Errorf("菜单元数据验证失败: %w", err)
	}

	return nil
}
