package repository

import (
	"context"

	"backend/internal/domain/menu/entity"
	"backend/internal/domain/menu/valueobject"
)

// MenuRepository 菜单仓储接口
type MenuRepository interface {
	// 基础CRUD操作
	Create(ctx context.Context, menu *entity.Menu) error
	Update(ctx context.Context, menu *entity.Menu) error
	Delete(ctx context.Context, id string) error
	FindByID(ctx context.Context, id string) (*entity.Menu, error)
	FindByBusinessID(ctx context.Context, businessID string) (*entity.Menu, error)

	// 查询方法
	FindByTenantID(ctx context.Context, tenantID string) ([]*entity.Menu, error)
	FindByParentID(ctx context.Context, tenantID, parentID string) ([]*entity.Menu, error)
	FindRootMenus(ctx context.Context, tenantID string) ([]*entity.Menu, error)
	FindByLevel(ctx context.Context, tenantID string, level int) ([]*entity.Menu, error)

	// 权限相关查询
	FindByPermissions(ctx context.Context, tenantID string, permissions []string) ([]*entity.Menu, error)
	FindByResourceAction(ctx context.Context, tenantID, resource, action string) ([]*entity.Menu, error)
	FindVisibleMenus(ctx context.Context, tenantID string) ([]*entity.Menu, error)
	FindActiveMenus(ctx context.Context, tenantID string) ([]*entity.Menu, error)

	// 菜单类型查询
	FindByType(ctx context.Context, tenantID string, menuType valueobject.MenuType) ([]*entity.Menu, error)
	FindDirectories(ctx context.Context, tenantID string) ([]*entity.Menu, error)
	FindMenuItems(ctx context.Context, tenantID string) ([]*entity.Menu, error)
	FindButtons(ctx context.Context, tenantID string) ([]*entity.Menu, error)

	// 层级查询
	FindMenuTree(ctx context.Context, tenantID string) ([]*entity.Menu, error)
	FindMenuTreeByParent(ctx context.Context, tenantID, parentID string) ([]*entity.Menu, error)
	FindChildrenRecursive(ctx context.Context, tenantID, parentID string) ([]*entity.Menu, error)

	// 排序和分页
	FindWithSort(ctx context.Context, tenantID string, orderBy string, limit, offset int) ([]*entity.Menu, error)
	FindByNamePattern(ctx context.Context, tenantID, pattern string) ([]*entity.Menu, error)
	FindByPathPattern(ctx context.Context, tenantID, pattern string) ([]*entity.Menu, error)

	// 批量操作
	CreateBatch(ctx context.Context, menus []*entity.Menu) error
	UpdateBatch(ctx context.Context, menus []*entity.Menu) error
	DeleteBatch(ctx context.Context, ids []string) error

	// 统计查询
	CountByTenant(ctx context.Context, tenantID string) (int64, error)
	CountByParent(ctx context.Context, tenantID, parentID string) (int64, error)
	CountByType(ctx context.Context, tenantID string, menuType valueobject.MenuType) (int64, error)
	CountByStatus(ctx context.Context, tenantID string, status valueobject.MenuStatus) (int64, error)

	// 存在性检查
	ExistsByName(ctx context.Context, tenantID, name string) (bool, error)
	ExistsByPath(ctx context.Context, tenantID, path string) (bool, error)
	ExistsByNameExcludeID(ctx context.Context, tenantID, name, excludeID string) (bool, error)
	ExistsByPathExcludeID(ctx context.Context, tenantID, path, excludeID string) (bool, error)

	// 菜单路径相关
	FindByExactPath(ctx context.Context, tenantID, path string) (*entity.Menu, error)
	FindByPathPrefix(ctx context.Context, tenantID, pathPrefix string) ([]*entity.Menu, error)

	// 菜单状态操作
	UpdateStatus(ctx context.Context, id string, status valueobject.MenuStatus) error
	UpdateVisibility(ctx context.Context, id string, visible bool) error
	BatchUpdateStatus(ctx context.Context, ids []string, status valueobject.MenuStatus) error

	// 菜单排序操作
	UpdateSort(ctx context.Context, id string, sort int) error
	BatchUpdateSort(ctx context.Context, sortUpdates map[string]int) error
	GetMaxSortInParent(ctx context.Context, tenantID, parentID string) (int, error)

	// 菜单层级操作
	UpdateParent(ctx context.Context, id, newParentID string, newLevel int) error
	MoveToParent(ctx context.Context, id, newParentID string) error
	UpdateLevel(ctx context.Context, id string, level int) error
	UpdateLevelRecursive(ctx context.Context, parentID string, startLevel int) error
}

// MenuFilter 菜单过滤条件
type MenuFilter struct {
	TenantID    string                    `json:"tenant_id"`
	ParentID    *string                   `json:"parent_id,omitempty"`
	MenuType    *valueobject.MenuType     `json:"menu_type,omitempty"`
	Status      *valueobject.MenuStatus   `json:"status,omitempty"`
	IsVisible   *bool                     `json:"is_visible,omitempty"`
	IsExternal  *bool                     `json:"is_external,omitempty"`
	Level       *int                      `json:"level,omitempty"`
	Name        string                    `json:"name,omitempty"`
	Path        string                    `json:"path,omitempty"`
	Permission  string                    `json:"permission,omitempty"`
	Resource    string                    `json:"resource,omitempty"`
	Action      string                    `json:"action,omitempty"`
	Permissions []string                  `json:"permissions,omitempty"`
}

// MenuSort 菜单排序选项
type MenuSort struct {
	Field string `json:"field"` // sort, level, name, created_at, updated_at
	Order string `json:"order"` // asc, desc
}

// MenuPagination 菜单分页选项
type MenuPagination struct {
	Page     int `json:"page"`
	PageSize int `json:"page_size"`
	Offset   int `json:"offset"`
	Limit    int `json:"limit"`
}

// MenuQueryOptions 菜单查询选项
type MenuQueryOptions struct {
	Filter     *MenuFilter     `json:"filter,omitempty"`
	Sort       *MenuSort       `json:"sort,omitempty"`
	Pagination *MenuPagination `json:"pagination,omitempty"`
	LoadParent bool            `json:"load_parent"`
	LoadChildren bool          `json:"load_children"`
	LoadChildrenRecursive bool `json:"load_children_recursive"`
}

// MenuRepositoryExtended 扩展的菜单仓储接口
type MenuRepositoryExtended interface {
	MenuRepository

	// 高级查询
	FindWithOptions(ctx context.Context, options *MenuQueryOptions) ([]*entity.Menu, error)
	FindWithFilter(ctx context.Context, filter *MenuFilter) ([]*entity.Menu, error)
	CountWithFilter(ctx context.Context, filter *MenuFilter) (int64, error)

	// 菜单树构建
	BuildMenuTree(ctx context.Context, menus []*entity.Menu) []*entity.Menu
	LoadMenuChildren(ctx context.Context, menu *entity.Menu, recursive bool) error
	LoadMenuParent(ctx context.Context, menu *entity.Menu) error

	// 菜单路径操作
	GetMenuPath(ctx context.Context, menuID string) ([]string, error)
	GetMenuBreadcrumb(ctx context.Context, menuID string) ([]*entity.Menu, error)
	ValidateMenuPath(ctx context.Context, tenantID, path string, excludeID string) error

	// 菜单权限验证
	ValidateMenuPermission(ctx context.Context, menu *entity.Menu, userPermissions []string) bool
	GetMenusByUserPermissions(ctx context.Context, tenantID string, userPermissions []string) ([]*entity.Menu, error)

	// 菜单缓存相关
	InvalidateCache(ctx context.Context, tenantID string) error
	GetCacheKey(tenantID string, key string) string
}

// MenuStatistics 菜单统计信息
type MenuStatistics struct {
	TotalMenus      int64                                    `json:"total_menus"`
	MenusByType     map[valueobject.MenuType]int64           `json:"menus_by_type"`
	MenusByStatus   map[valueobject.MenuStatus]int64         `json:"menus_by_status"`
	MenusByLevel    map[int]int64                            `json:"menus_by_level"`
	VisibleMenus    int64                                    `json:"visible_menus"`
	HiddenMenus     int64                                    `json:"hidden_menus"`
	ExternalMenus   int64                                    `json:"external_menus"`
	MenusWithPermission int64                                `json:"menus_with_permission"`
	RootMenus       int64                                    `json:"root_menus"`
	MaxLevel        int                                      `json:"max_level"`
	AvgChildrenCount float64                                 `json:"avg_children_count"`
}

// MenuRepositoryStats 菜单仓储统计接口
type MenuRepositoryStats interface {
	GetMenuStatistics(ctx context.Context, tenantID string) (*MenuStatistics, error)
	GetMenuCountByDateRange(ctx context.Context, tenantID string, startDate, endDate string) (map[string]int64, error)
	GetPopularMenus(ctx context.Context, tenantID string, limit int) ([]*entity.Menu, error)
	GetRecentlyUpdatedMenus(ctx context.Context, tenantID string, limit int) ([]*entity.Menu, error)
}
