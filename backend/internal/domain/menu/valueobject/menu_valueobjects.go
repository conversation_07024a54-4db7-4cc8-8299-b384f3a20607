package valueobject

import (
	"encoding/json"
	"fmt"
	"strings"
)

// MenuType 菜单类型枚举
type MenuType int

const (
	MenuTypeDirectory MenuType = iota + 1 // 目录
	MenuTypeMenu                          // 菜单
	MenuTypeButton                        // 按钮
)

// String 返回菜单类型的字符串表示
func (mt MenuType) String() string {
	switch mt {
	case MenuTypeDirectory:
		return "directory"
	case MenuTypeMenu:
		return "menu"
	case MenuTypeButton:
		return "button"
	default:
		return "unknown"
	}
}

// IsValid 检查菜单类型是否有效
func (mt MenuType) IsValid() bool {
	return mt >= MenuTypeDirectory && mt <= MenuTypeButton
}

// MenuStatus 菜单状态枚举
type MenuStatus int

const (
	MenuStatusInactive MenuStatus = iota // 未激活
	MenuStatusActive                     // 激活
	MenuStatusHidden                     // 隐藏
	MenuStatusDisabled                   // 禁用
)

// String 返回菜单状态的字符串表示
func (ms MenuStatus) String() string {
	switch ms {
	case MenuStatusInactive:
		return "inactive"
	case MenuStatusActive:
		return "active"
	case MenuStatusHidden:
		return "hidden"
	case MenuStatusDisabled:
		return "disabled"
	default:
		return "unknown"
	}
}

// IsValid 检查菜单状态是否有效
func (ms MenuStatus) IsValid() bool {
	return ms >= MenuStatusInactive && ms <= MenuStatusDisabled
}

// IsVisible 检查菜单是否可见
func (ms MenuStatus) IsVisible() bool {
	return ms == MenuStatusActive
}

// MenuMeta 菜单元数据值对象
type MenuMeta struct {
	Title       string   `json:"title"`                    // 页面标题
	Roles       []string `json:"roles,omitempty"`          // 角色限制
	NoCache     bool     `json:"noCache"`                  // 不缓存
	Breadcrumb  bool     `json:"breadcrumb"`               // 显示面包屑
	ActiveMenu  string   `json:"activeMenu,omitempty"`     // 激活菜单
	Hidden      bool     `json:"hidden"`                   // 隐藏菜单
	KeepAlive   bool     `json:"keepAlive"`                // 保持活跃
	IsExternal  bool     `json:"isExternal"`               // 外部链接
	Badge       string   `json:"badge,omitempty"`          // 徽章
	BadgeType   string   `json:"badgeType,omitempty"`      // 徽章类型
	Target      string   `json:"target,omitempty"`         // 链接目标
}

// NewMenuMeta 创建新的菜单元数据
func NewMenuMeta(title string) *MenuMeta {
	return &MenuMeta{
		Title:      title,
		Breadcrumb: true,
		KeepAlive:  false,
		NoCache:    false,
		Hidden:     false,
		IsExternal: false,
	}
}

// WithRoles 设置角色限制
func (mm *MenuMeta) WithRoles(roles []string) *MenuMeta {
	mm.Roles = roles
	return mm
}

// WithCache 设置缓存策略
func (mm *MenuMeta) WithCache(cache bool) *MenuMeta {
	mm.NoCache = !cache
	return mm
}

// WithBreadcrumb 设置面包屑显示
func (mm *MenuMeta) WithBreadcrumb(show bool) *MenuMeta {
	mm.Breadcrumb = show
	return mm
}

// WithKeepAlive 设置保持活跃
func (mm *MenuMeta) WithKeepAlive(keepAlive bool) *MenuMeta {
	mm.KeepAlive = keepAlive
	return mm
}

// WithHidden 设置隐藏状态
func (mm *MenuMeta) WithHidden(hidden bool) *MenuMeta {
	mm.Hidden = hidden
	return mm
}

// WithExternal 设置外部链接
func (mm *MenuMeta) WithExternal(external bool) *MenuMeta {
	mm.IsExternal = external
	return mm
}

// WithBadge 设置徽章
func (mm *MenuMeta) WithBadge(badge, badgeType string) *MenuMeta {
	mm.Badge = badge
	mm.BadgeType = badgeType
	return mm
}

// WithTarget 设置链接目标
func (mm *MenuMeta) WithTarget(target string) *MenuMeta {
	mm.Target = target
	return mm
}

// Validate 验证菜单元数据
func (mm *MenuMeta) Validate() error {
	if strings.TrimSpace(mm.Title) == "" {
		return fmt.Errorf("菜单标题不能为空")
	}
	
	// 验证徽章类型
	if mm.Badge != "" && mm.BadgeType != "" {
		validBadgeTypes := []string{"primary", "secondary", "success", "danger", "warning", "info", "light", "dark"}
		isValid := false
		for _, validType := range validBadgeTypes {
			if mm.BadgeType == validType {
				isValid = true
				break
			}
		}
		if !isValid {
			return fmt.Errorf("无效的徽章类型: %s", mm.BadgeType)
		}
	}
	
	// 验证链接目标
	if mm.Target != "" {
		validTargets := []string{"_blank", "_self", "_parent", "_top"}
		isValid := false
		for _, validTarget := range validTargets {
			if mm.Target == validTarget {
				isValid = true
				break
			}
		}
		if !isValid {
			return fmt.Errorf("无效的链接目标: %s", mm.Target)
		}
	}
	
	return nil
}

// ToJSON 转换为JSON字符串
func (mm *MenuMeta) ToJSON() (string, error) {
	data, err := json.Marshal(mm)
	if err != nil {
		return "", fmt.Errorf("菜单元数据序列化失败: %w", err)
	}
	return string(data), nil
}

// FromJSON 从JSON字符串创建菜单元数据
func MenuMetaFromJSON(jsonStr string) (*MenuMeta, error) {
	var meta MenuMeta
	if err := json.Unmarshal([]byte(jsonStr), &meta); err != nil {
		return nil, fmt.Errorf("菜单元数据反序列化失败: %w", err)
	}
	
	if err := meta.Validate(); err != nil {
		return nil, err
	}
	
	return &meta, nil
}

// MenuPath 菜单路径值对象
type MenuPath struct {
	value string
}

// NewMenuPath 创建新的菜单路径
func NewMenuPath(path string) (*MenuPath, error) {
	path = strings.TrimSpace(path)
	if path == "" {
		return nil, fmt.Errorf("菜单路径不能为空")
	}
	
	// 路径必须以/开头
	if !strings.HasPrefix(path, "/") {
		path = "/" + path
	}
	
	// 验证路径格式
	if strings.Contains(path, "//") {
		return nil, fmt.Errorf("菜单路径格式无效: %s", path)
	}
	
	return &MenuPath{value: path}, nil
}

// Value 获取路径值
func (mp *MenuPath) Value() string {
	return mp.value
}

// String 字符串表示
func (mp *MenuPath) String() string {
	return mp.value
}

// IsRoot 检查是否为根路径
func (mp *MenuPath) IsRoot() bool {
	return mp.value == "/"
}

// Equals 比较两个路径是否相等
func (mp *MenuPath) Equals(other *MenuPath) bool {
	if other == nil {
		return false
	}
	return mp.value == other.value
}

// MenuPermission 菜单权限值对象
type MenuPermission struct {
	resource   string
	action     string
	permission string
}

// NewMenuPermission 创建新的菜单权限
func NewMenuPermission(resource, action string) (*MenuPermission, error) {
	resource = strings.TrimSpace(resource)
	action = strings.TrimSpace(action)
	
	if resource == "" {
		return nil, fmt.Errorf("权限资源不能为空")
	}
	
	if action == "" {
		return nil, fmt.Errorf("权限操作不能为空")
	}
	
	return &MenuPermission{
		resource: resource,
		action:   action,
	}, nil
}

// NewMenuPermissionWithCode 使用权限码创建菜单权限
func NewMenuPermissionWithCode(permission string) (*MenuPermission, error) {
	permission = strings.TrimSpace(permission)
	if permission == "" {
		return nil, fmt.Errorf("权限码不能为空")
	}
	
	return &MenuPermission{
		permission: permission,
	}, nil
}

// Resource 获取资源
func (mp *MenuPermission) Resource() string {
	return mp.resource
}

// Action 获取操作
func (mp *MenuPermission) Action() string {
	return mp.action
}

// Permission 获取权限码
func (mp *MenuPermission) Permission() string {
	return mp.permission
}

// HasResourceAction 检查是否有资源和操作
func (mp *MenuPermission) HasResourceAction() bool {
	return mp.resource != "" && mp.action != ""
}

// HasPermissionCode 检查是否有权限码
func (mp *MenuPermission) HasPermissionCode() bool {
	return mp.permission != ""
}

// String 字符串表示
func (mp *MenuPermission) String() string {
	if mp.HasPermissionCode() {
		return mp.permission
	}
	if mp.HasResourceAction() {
		return fmt.Sprintf("%s:%s", mp.resource, mp.action)
	}
	return ""
}

// Equals 比较两个权限是否相等
func (mp *MenuPermission) Equals(other *MenuPermission) bool {
	if other == nil {
		return false
	}
	return mp.resource == other.resource && 
		   mp.action == other.action && 
		   mp.permission == other.permission
}
