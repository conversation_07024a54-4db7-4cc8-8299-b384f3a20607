package service

import (
	"context"
	"fmt"
	"sort"

	"backend/internal/domain/menu/entity"
	"backend/internal/domain/menu/repository"
	"backend/internal/domain/menu/valueobject"
	"backend/pkg/infrastructure/logger"
)

// MenuDomainService 菜单领域服务接口
type MenuDomainService interface {
	// 菜单创建和验证
	CreateMenu(ctx context.Context, tenantID, name, title, path string, menuType valueobject.MenuType) (*entity.Menu, error)
	ValidateMenuCreation(ctx context.Context, tenantID, name, path string) error
	ValidateMenuUpdate(ctx context.Context, menuID, name, path string) error

	// 菜单层级管理
	SetMenuParent(ctx context.Context, menuID, parentID string) error
	MoveMenu(ctx context.Context, menuID, newParentID string) error
	ValidateMenuHierarchy(ctx context.Context, menuID, parentID string) error
	CalculateMenuLevel(ctx context.Context, parentID string) (int, error)

	// 菜单排序管理
	ReorderMenus(ctx context.Context, tenantID, parentID string, menuIDs []string) error
	GetNextSortOrder(ctx context.Context, tenantID, parentID string) (int, error)
	UpdateMenuSort(ctx context.Context, menuID string, sort int) error

	// 菜单权限管理
	SetMenuPermission(ctx context.Context, menuID, permission string) error
	SetMenuResourceAction(ctx context.Context, menuID, resource, action string) error
	ValidateMenuPermission(ctx context.Context, menu *entity.Menu) error

	// 菜单状态管理
	ActivateMenu(ctx context.Context, menuID string) error
	DeactivateMenu(ctx context.Context, menuID string) error
	HideMenu(ctx context.Context, menuID string) error
	ShowMenu(ctx context.Context, menuID string) error

	// 菜单树构建
	BuildMenuTree(ctx context.Context, tenantID string) ([]*entity.Menu, error)
	BuildMenuTreeFromList(menus []*entity.Menu) []*entity.Menu
	LoadMenuChildren(ctx context.Context, menu *entity.Menu, recursive bool) error

	// 菜单查询辅助
	FindMenusByPermissions(ctx context.Context, tenantID string, permissions []string) ([]*entity.Menu, error)
	FindAccessibleMenus(ctx context.Context, tenantID string, userPermissions []string) ([]*entity.Menu, error)
	GetMenuBreadcrumb(ctx context.Context, menuID string) ([]*entity.Menu, error)

	// 菜单验证
	ValidateMenuPath(ctx context.Context, tenantID, path, excludeID string) error
	ValidateMenuName(ctx context.Context, tenantID, name, excludeID string) error
	ValidateMenuCircularReference(ctx context.Context, menuID, parentID string) error
}

// menuDomainService 菜单领域服务实现
type menuDomainService struct {
	menuRepo repository.MenuRepository
	logger   logger.Logger
}

// NewMenuDomainService 创建菜单领域服务
func NewMenuDomainService(menuRepo repository.MenuRepository, logger logger.Logger) MenuDomainService {
	return &menuDomainService{
		menuRepo: menuRepo,
		logger:   logger,
	}
}

// CreateMenu 创建菜单
func (s *menuDomainService) CreateMenu(ctx context.Context, tenantID, name, title, path string, menuType valueobject.MenuType) (*entity.Menu, error) {
	s.logger.Info(ctx, "Creating menu", map[string]interface{}{
		"tenant_id":  tenantID,
		"name":       name,
		"title":      title,
		"path":       path,
		"menu_type":  menuType,
	})

	// 验证菜单创建
	if err := s.ValidateMenuCreation(ctx, tenantID, name, path); err != nil {
		return nil, err
	}

	// 创建菜单实体
	menu, err := entity.NewMenu(tenantID, name, title, path, menuType)
	if err != nil {
		return nil, fmt.Errorf("创建菜单实体失败: %w", err)
	}

	return menu, nil
}

// ValidateMenuCreation 验证菜单创建
func (s *menuDomainService) ValidateMenuCreation(ctx context.Context, tenantID, name, path string) error {
	// 检查菜单名称是否已存在
	exists, err := s.menuRepo.ExistsByName(ctx, tenantID, name)
	if err != nil {
		return fmt.Errorf("检查菜单名称失败: %w", err)
	}
	if exists {
		return fmt.Errorf("菜单名称 '%s' 已存在", name)
	}

	// 检查菜单路径是否已存在
	exists, err = s.menuRepo.ExistsByPath(ctx, tenantID, path)
	if err != nil {
		return fmt.Errorf("检查菜单路径失败: %w", err)
	}
	if exists {
		return fmt.Errorf("菜单路径 '%s' 已存在", path)
	}

	return nil
}

// ValidateMenuUpdate 验证菜单更新
func (s *menuDomainService) ValidateMenuUpdate(ctx context.Context, menuID, name, path string) error {
	// 获取菜单信息
	menu, err := s.menuRepo.FindByBusinessID(ctx, menuID)
	if err != nil {
		return fmt.Errorf("查找菜单失败: %w", err)
	}

	// 检查菜单名称是否已存在（排除当前菜单）
	exists, err := s.menuRepo.ExistsByNameExcludeID(ctx, menu.TenantID, name, menuID)
	if err != nil {
		return fmt.Errorf("检查菜单名称失败: %w", err)
	}
	if exists {
		return fmt.Errorf("菜单名称 '%s' 已存在", name)
	}

	// 检查菜单路径是否已存在（排除当前菜单）
	exists, err = s.menuRepo.ExistsByPathExcludeID(ctx, menu.TenantID, path, menuID)
	if err != nil {
		return fmt.Errorf("检查菜单路径失败: %w", err)
	}
	if exists {
		return fmt.Errorf("菜单路径 '%s' 已存在", path)
	}

	return nil
}

// SetMenuParent 设置菜单父级
func (s *menuDomainService) SetMenuParent(ctx context.Context, menuID, parentID string) error {
	// 验证层级关系
	if err := s.ValidateMenuHierarchy(ctx, menuID, parentID); err != nil {
		return err
	}

	// 计算新的层级
	level, err := s.CalculateMenuLevel(ctx, parentID)
	if err != nil {
		return err
	}

	// 更新菜单父级和层级
	return s.menuRepo.UpdateParent(ctx, menuID, parentID, level)
}

// MoveMenu 移动菜单
func (s *menuDomainService) MoveMenu(ctx context.Context, menuID, newParentID string) error {
	return s.SetMenuParent(ctx, menuID, newParentID)
}

// ValidateMenuHierarchy 验证菜单层级关系
func (s *menuDomainService) ValidateMenuHierarchy(ctx context.Context, menuID, parentID string) error {
	if menuID == parentID {
		return fmt.Errorf("菜单不能设置自己为父菜单")
	}

	// 检查循环引用
	return s.ValidateMenuCircularReference(ctx, menuID, parentID)
}

// CalculateMenuLevel 计算菜单层级
func (s *menuDomainService) CalculateMenuLevel(ctx context.Context, parentID string) (int, error) {
	if parentID == "" {
		return 1, nil // 根菜单层级为1
	}

	parent, err := s.menuRepo.FindByBusinessID(ctx, parentID)
	if err != nil {
		return 0, fmt.Errorf("查找父菜单失败: %w", err)
	}

	level := parent.Level + 1
	if level > 5 {
		return 0, fmt.Errorf("菜单层级不能超过5级")
	}

	return level, nil
}

// ReorderMenus 重新排序菜单
func (s *menuDomainService) ReorderMenus(ctx context.Context, tenantID, parentID string, menuIDs []string) error {
	sortUpdates := make(map[string]int)
	for i, menuID := range menuIDs {
		sortUpdates[menuID] = i + 1
	}

	return s.menuRepo.BatchUpdateSort(ctx, sortUpdates)
}

// GetNextSortOrder 获取下一个排序号
func (s *menuDomainService) GetNextSortOrder(ctx context.Context, tenantID, parentID string) (int, error) {
	maxSort, err := s.menuRepo.GetMaxSortInParent(ctx, tenantID, parentID)
	if err != nil {
		return 0, err
	}
	return maxSort + 1, nil
}

// UpdateMenuSort 更新菜单排序
func (s *menuDomainService) UpdateMenuSort(ctx context.Context, menuID string, sort int) error {
	return s.menuRepo.UpdateSort(ctx, menuID, sort)
}

// SetMenuPermission 设置菜单权限
func (s *menuDomainService) SetMenuPermission(ctx context.Context, menuID, permission string) error {
	menu, err := s.menuRepo.FindByBusinessID(ctx, menuID)
	if err != nil {
		return fmt.Errorf("查找菜单失败: %w", err)
	}

	if err := menu.SetPermission(permission); err != nil {
		return err
	}

	return s.menuRepo.Update(ctx, menu)
}

// SetMenuResourceAction 设置菜单资源权限
func (s *menuDomainService) SetMenuResourceAction(ctx context.Context, menuID, resource, action string) error {
	menu, err := s.menuRepo.FindByBusinessID(ctx, menuID)
	if err != nil {
		return fmt.Errorf("查找菜单失败: %w", err)
	}

	if err := menu.SetResourceAction(resource, action); err != nil {
		return err
	}

	return s.menuRepo.Update(ctx, menu)
}

// ValidateMenuPermission 验证菜单权限
func (s *menuDomainService) ValidateMenuPermission(ctx context.Context, menu *entity.Menu) error {
	if !menu.HasPermission() {
		return nil // 没有权限要求的菜单总是有效的
	}

	// 这里可以添加更复杂的权限验证逻辑
	return nil
}

// ActivateMenu 激活菜单
func (s *menuDomainService) ActivateMenu(ctx context.Context, menuID string) error {
	return s.menuRepo.UpdateStatus(ctx, menuID, valueobject.MenuStatusActive)
}

// DeactivateMenu 停用菜单
func (s *menuDomainService) DeactivateMenu(ctx context.Context, menuID string) error {
	return s.menuRepo.UpdateStatus(ctx, menuID, valueobject.MenuStatusInactive)
}

// HideMenu 隐藏菜单
func (s *menuDomainService) HideMenu(ctx context.Context, menuID string) error {
	return s.menuRepo.UpdateStatus(ctx, menuID, valueobject.MenuStatusHidden)
}

// ShowMenu 显示菜单
func (s *menuDomainService) ShowMenu(ctx context.Context, menuID string) error {
	return s.menuRepo.UpdateStatus(ctx, menuID, valueobject.MenuStatusActive)
}

// BuildMenuTree 构建菜单树
func (s *menuDomainService) BuildMenuTree(ctx context.Context, tenantID string) ([]*entity.Menu, error) {
	menus, err := s.menuRepo.FindByTenantID(ctx, tenantID)
	if err != nil {
		return nil, fmt.Errorf("查询菜单列表失败: %w", err)
	}

	return s.BuildMenuTreeFromList(menus), nil
}

// BuildMenuTreeFromList 从菜单列表构建菜单树
func (s *menuDomainService) BuildMenuTreeFromList(menus []*entity.Menu) []*entity.Menu {
	// 创建菜单映射
	menuMap := make(map[string]*entity.Menu)
	for _, menu := range menus {
		menuMap[menu.BusinessID] = menu
		menu.Children = nil // 清空子菜单列表
	}

	// 构建树形结构
	var rootMenus []*entity.Menu
	for _, menu := range menus {
		if menu.IsRoot() {
			rootMenus = append(rootMenus, menu)
		} else if parent, exists := menuMap[menu.ParentID]; exists {
			if parent.Children == nil {
				parent.Children = make([]*entity.Menu, 0)
			}
			parent.Children = append(parent.Children, menu)
		}
	}

	// 排序
	s.sortMenus(rootMenus)
	for _, menu := range menus {
		if menu.Children != nil {
			s.sortMenus(menu.Children)
		}
	}

	return rootMenus
}

// sortMenus 排序菜单
func (s *menuDomainService) sortMenus(menus []*entity.Menu) {
	sort.Slice(menus, func(i, j int) bool {
		if menus[i].Sort != menus[j].Sort {
			return menus[i].Sort < menus[j].Sort
		}
		return menus[i].Name < menus[j].Name
	})
}

// LoadMenuChildren 加载菜单子项
func (s *menuDomainService) LoadMenuChildren(ctx context.Context, menu *entity.Menu, recursive bool) error {
	children, err := s.menuRepo.FindByParentID(ctx, menu.TenantID, menu.BusinessID)
	if err != nil {
		return fmt.Errorf("加载菜单子项失败: %w", err)
	}

	menu.Children = children

	if recursive {
		for _, child := range children {
			if err := s.LoadMenuChildren(ctx, child, true); err != nil {
				return err
			}
		}
	}

	return nil
}

// FindMenusByPermissions 根据权限查找菜单
func (s *menuDomainService) FindMenusByPermissions(ctx context.Context, tenantID string, permissions []string) ([]*entity.Menu, error) {
	return s.menuRepo.FindByPermissions(ctx, tenantID, permissions)
}

// FindAccessibleMenus 查找用户可访问的菜单
func (s *menuDomainService) FindAccessibleMenus(ctx context.Context, tenantID string, userPermissions []string) ([]*entity.Menu, error) {
	// 获取所有可见菜单
	allMenus, err := s.menuRepo.FindVisibleMenus(ctx, tenantID)
	if err != nil {
		return nil, fmt.Errorf("查询可见菜单失败: %w", err)
	}

	// 过滤用户可访问的菜单
	var accessibleMenus []*entity.Menu
	for _, menu := range allMenus {
		if s.isMenuAccessible(menu, userPermissions) {
			accessibleMenus = append(accessibleMenus, menu)
		}
	}

	return accessibleMenus, nil
}

// isMenuAccessible 检查菜单是否可访问
func (s *menuDomainService) isMenuAccessible(menu *entity.Menu, userPermissions []string) bool {
	// 如果菜单没有权限要求，默认可访问
	if !menu.HasPermission() {
		return true
	}

	permissionStr := menu.GetPermissionString()
	for _, userPerm := range userPermissions {
		if userPerm == permissionStr {
			return true
		}
	}

	return false
}

// GetMenuBreadcrumb 获取菜单面包屑
func (s *menuDomainService) GetMenuBreadcrumb(ctx context.Context, menuID string) ([]*entity.Menu, error) {
	var breadcrumb []*entity.Menu

	currentMenu, err := s.menuRepo.FindByBusinessID(ctx, menuID)
	if err != nil {
		return nil, fmt.Errorf("查找菜单失败: %w", err)
	}

	// 向上遍历父菜单
	for currentMenu != nil {
		breadcrumb = append([]*entity.Menu{currentMenu}, breadcrumb...)
		
		if currentMenu.IsRoot() {
			break
		}

		currentMenu, err = s.menuRepo.FindByBusinessID(ctx, currentMenu.ParentID)
		if err != nil {
			break
		}
	}

	return breadcrumb, nil
}

// ValidateMenuPath 验证菜单路径
func (s *menuDomainService) ValidateMenuPath(ctx context.Context, tenantID, path, excludeID string) error {
	exists, err := s.menuRepo.ExistsByPathExcludeID(ctx, tenantID, path, excludeID)
	if err != nil {
		return fmt.Errorf("检查菜单路径失败: %w", err)
	}
	if exists {
		return fmt.Errorf("菜单路径 '%s' 已存在", path)
	}
	return nil
}

// ValidateMenuName 验证菜单名称
func (s *menuDomainService) ValidateMenuName(ctx context.Context, tenantID, name, excludeID string) error {
	exists, err := s.menuRepo.ExistsByNameExcludeID(ctx, tenantID, name, excludeID)
	if err != nil {
		return fmt.Errorf("检查菜单名称失败: %w", err)
	}
	if exists {
		return fmt.Errorf("菜单名称 '%s' 已存在", name)
	}
	return nil
}

// ValidateMenuCircularReference 验证菜单循环引用
func (s *menuDomainService) ValidateMenuCircularReference(ctx context.Context, menuID, parentID string) error {
	if parentID == "" {
		return nil
	}

	// 检查是否会形成循环引用
	currentParentID := parentID
	for currentParentID != "" {
		if currentParentID == menuID {
			return fmt.Errorf("设置父菜单会形成循环引用")
		}

		parent, err := s.menuRepo.FindByBusinessID(ctx, currentParentID)
		if err != nil {
			break
		}

		currentParentID = parent.ParentID
	}

	return nil
}
