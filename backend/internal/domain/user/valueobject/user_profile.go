package valueobject

import (
	"database/sql/driver"
	"encoding/json"
	"errors"
	"fmt"
	"regexp"
	"strings"
)

// ==================== 用户ID值对象 ====================

// UserID 用户ID值对象
type UserID struct {
	value string
}

// NewUserID 创建用户ID值对象
func NewUserID(value string) (UserID, error) {
	if value == "" {
		return UserID{}, errors.New("用户ID不能为空")
	}
	if len(value) > 36 {
		return UserID{}, errors.New("用户ID长度不能超过36个字符")
	}
	return UserID{value: value}, nil
}

// String 返回字符串表示
func (u UserID) String() string {
	return u.value
}

// Equals 比较两个用户ID是否相等
func (u UserID) Equals(other UserID) bool {
	return u.value == other.value
}

// IsEmpty 检查是否为空
func (u UserID) IsEmpty() bool {
	return u.value == ""
}

// ==================== 用户状态值对象 ====================

// UserStatus 用户状态值对象
type UserStatus struct {
	value string
}

// 用户状态常量
const (
	UserStatusPending   = "pending"
	UserStatusActive    = "active"
	UserStatusInactive  = "inactive"
	UserStatusSuspended = "suspended"
	UserStatusBanned    = "banned"
)

// NewUserStatus 创建用户状态值对象
func NewUserStatus(value string) (UserStatus, error) {
	switch value {
	case UserStatusPending, UserStatusActive, UserStatusInactive, UserStatusSuspended, UserStatusBanned:
		return UserStatus{value: value}, nil
	default:
		return UserStatus{}, fmt.Errorf("无效的用户状态: %s", value)
	}
}

// String 返回字符串表示
func (s UserStatus) String() string {
	return s.value
}

// Equals 比较两个用户状态是否相等
func (s UserStatus) Equals(other UserStatus) bool {
	return s.value == other.value
}

// IsActive 检查是否为激活状态
func (s UserStatus) IsActive() bool {
	return s.value == UserStatusActive
}

// CanLogin 检查是否可以登录
func (s UserStatus) CanLogin() bool {
	return s.value == UserStatusActive
}

// ==================== 邮箱值对象 ====================

// Email 邮箱值对象
type Email struct {
	value    string
	verified bool
}

// NewEmail 创建邮箱值对象
func NewEmail(value string) (Email, error) {
	if value == "" {
		return Email{}, errors.New("邮箱不能为空")
	}

	emailRegex := regexp.MustCompile(`^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$`)
	if !emailRegex.MatchString(value) {
		return Email{}, errors.New("邮箱格式无效")
	}

	return Email{value: strings.ToLower(value), verified: false}, nil
}

// String 返回字符串表示
func (e Email) String() string {
	return e.value
}

// IsVerified 检查是否已验证
func (e Email) IsVerified() bool {
	return e.verified
}

// Verify 标记为已验证
func (e Email) Verify() Email {
	return Email{value: e.value, verified: true}
}

// Equals 比较两个邮箱是否相等
func (e Email) Equals(other Email) bool {
	return e.value == other.value
}

// ==================== 手机号值对象 ====================

// Phone 手机号值对象
type Phone struct {
	countryCode string
	number      string
	verified    bool
}

// NewPhone 创建手机号值对象
func NewPhone(countryCode, number string) (Phone, error) {
	if countryCode == "" {
		countryCode = "+86" // 默认中国
	}

	if number == "" {
		return Phone{}, errors.New("手机号不能为空")
	}

	// 简单的手机号验证（可根据需要扩展）
	phoneRegex := regexp.MustCompile(`^\d{10,15}$`)
	if !phoneRegex.MatchString(number) {
		return Phone{}, errors.New("手机号格式无效")
	}

	return Phone{
		countryCode: countryCode,
		number:      number,
		verified:    false,
	}, nil
}

// String 返回完整的手机号字符串
func (p Phone) String() string {
	return p.countryCode + p.number
}

// GetNumber 获取号码部分
func (p Phone) GetNumber() string {
	return p.number
}

// GetCountryCode 获取国家代码
func (p Phone) GetCountryCode() string {
	return p.countryCode
}

// IsVerified 检查是否已验证
func (p Phone) IsVerified() bool {
	return p.verified
}

// Verify 标记为已验证
func (p Phone) Verify() Phone {
	return Phone{
		countryCode: p.countryCode,
		number:      p.number,
		verified:    true,
	}
}

// Equals 比较两个手机号是否相等
func (p Phone) Equals(other Phone) bool {
	return p.countryCode == other.countryCode && p.number == other.number
}

// ==================== 用户联系方式值对象 ====================

// UserContact 用户联系方式值对象
type UserContact struct {
	email  Email
	phone  Phone
	wechat string
}

// NewUserContact 创建用户联系方式值对象
func NewUserContact(email Email, phone Phone, wechat string) UserContact {
	return UserContact{
		email:  email,
		phone:  phone,
		wechat: wechat,
	}
}

// GetEmail 获取邮箱
func (c UserContact) GetEmail() Email {
	return c.email
}

// GetPhone 获取手机号
func (c UserContact) GetPhone() Phone {
	return c.phone
}

// GetWechat 获取微信号
func (c UserContact) GetWechat() string {
	return c.wechat
}

// UpdateEmail 更新邮箱
func (c UserContact) UpdateEmail(email Email) UserContact {
	return UserContact{
		email:  email,
		phone:  c.phone,
		wechat: c.wechat,
	}
}

// UpdatePhone 更新手机号
func (c UserContact) UpdatePhone(phone Phone) UserContact {
	return UserContact{
		email:  c.email,
		phone:  phone,
		wechat: c.wechat,
	}
}

// UpdateWechat 更新微信号
func (c UserContact) UpdateWechat(wechat string) UserContact {
	return UserContact{
		email:  c.email,
		phone:  c.phone,
		wechat: wechat,
	}
}

// IsContactVerified 检查联系方式是否已验证
func (c UserContact) IsContactVerified() bool {
	return c.email.IsVerified() || c.phone.IsVerified()
}

// ==================== 用户资料值对象 ====================

// UserProfile 用户资料值对象
type UserProfile struct {
	Avatar    string      `json:"avatar" gorm:"size:512"`
	Nickname  string      `json:"nickname" gorm:"size:100"`
	FirstName string      `json:"first_name" gorm:"size:50"`
	LastName  string      `json:"last_name" gorm:"size:50"`
	Language  string      `json:"language" gorm:"size:10;default:'zh-CN'"`
	Timezone  string      `json:"timezone" gorm:"size:50;default:'Asia/Shanghai'"`
	Metadata  MetadataMap `json:"metadata,omitempty" gorm:"type:jsonb"` // 扩展字段
}

// MetadataMap 自定义类型用于 GORM JSON 处理
type MetadataMap map[string]any

// Value 实现 driver.Valuer 接口
func (m MetadataMap) Value() (driver.Value, error) {
	if m == nil {
		return nil, nil
	}
	return json.Marshal(m)
}

// Scan 实现 sql.Scanner 接口
func (m *MetadataMap) Scan(value any) error {
	if value == nil {
		*m = make(map[string]any)
		return nil
	}

	switch v := value.(type) {
	case []byte:
		return json.Unmarshal(v, m)
	case string:
		return json.Unmarshal([]byte(v), m)
	default:
		return fmt.Errorf("cannot scan %T into MetadataMap", value)
	}
}

// NewUserProfile 创建用户资料
func NewUserProfile(nickname, firstName, lastName string) *UserProfile {
	return &UserProfile{
		Nickname:  strings.TrimSpace(nickname),
		FirstName: strings.TrimSpace(firstName),
		LastName:  strings.TrimSpace(lastName),
		Language:  "zh-CN",
		Timezone:  "Asia/Shanghai",
		Metadata:  make(MetadataMap),
	}
}

// GetFullName 获取全名
func (p *UserProfile) GetFullName() string {
	fullName := strings.TrimSpace(p.FirstName + " " + p.LastName)
	if fullName == "" {
		return p.Nickname
	}
	return fullName
}

// GetDisplayName 获取显示名称
func (p *UserProfile) GetDisplayName() string {
	if p.Nickname != "" {
		return p.Nickname
	}
	return p.GetFullName()
}

// Validate 验证用户资料
func (p *UserProfile) Validate() error {
	if p.Nickname != "" && len(p.Nickname) > 50 {
		return fmt.Errorf("昵称过长：最多50个字符")
	}
	if p.FirstName != "" && len(p.FirstName) > 30 {
		return fmt.Errorf("名字过长：最多30个字符")
	}
	if p.LastName != "" && len(p.LastName) > 30 {
		return fmt.Errorf("姓氏过长：最多30个字符")
	}
	if p.Language != "" && !isValidLanguageCode(p.Language) {
		return fmt.Errorf("无效的语言代码: %s", p.Language)
	}
	if p.Timezone != "" && !isValidTimezone(p.Timezone) {
		return fmt.Errorf("无效的时区: %s", p.Timezone)
	}
	return nil
}

// SetMetadata 设置元数据
func (p *UserProfile) SetMetadata(key string, value any) {
	if p.Metadata == nil {
		p.Metadata = make(MetadataMap)
	}
	p.Metadata[key] = value
}

// SetMetadataString 设置字符串类型的元数据
func (p *UserProfile) SetMetadataString(key, value string) {
	if p.Metadata == nil {
		p.Metadata = make(MetadataMap)
	}
	p.Metadata[key] = value
}

// GetMetadata 获取元数据
func (p *UserProfile) GetMetadata(key string) any {
	if p.Metadata == nil {
		return nil
	}
	return p.Metadata[key]
}

// GetMetadataString 获取字符串类型的元数据
func (p *UserProfile) GetMetadataString(key string) string {
	if p.Metadata == nil {
		return ""
	}
	if value, ok := p.Metadata[key].(string); ok {
		return value
	}
	return ""
}

// ToJSON 转换为JSON字符串
func (p *UserProfile) ToJSON() (string, error) {
	data, err := json.Marshal(p)
	if err != nil {
		return "", fmt.Errorf("序列化用户资料失败: %w", err)
	}
	return string(data), nil
}

// FromJSON 从JSON字符串创建用户资料
func FromJSON(jsonStr string) (*UserProfile, error) {
	var profile UserProfile
	if err := json.Unmarshal([]byte(jsonStr), &profile); err != nil {
		return nil, fmt.Errorf("反序列化用户资料失败: %w", err)
	}
	return &profile, nil
}

// Credentials 认证凭据值对象
type Credentials struct {
	Username string `json:"username"`
	Email    string `json:"email"`
	Phone    string `json:"phone"`
}

// NewCredentials 创建认证凭据
func NewCredentials(username, email, phone string) *Credentials {
	return &Credentials{
		Username: strings.TrimSpace(strings.ToLower(username)),
		Email:    strings.TrimSpace(strings.ToLower(email)),
		Phone:    strings.TrimSpace(phone),
	}
}

// Validate 验证认证凭据
func (c *Credentials) Validate() error {
	if c.Username == "" && c.Email == "" && c.Phone == "" {
		return fmt.Errorf("至少需要一个凭证（用户名、邮箱或手机号）")
	}

	if c.Username != "" {
		if err := validateUsername(c.Username); err != nil {
			return err
		}
	}

	if c.Email != "" {
		if err := validateEmail(c.Email); err != nil {
			return err
		}
	}

	if c.Phone != "" {
		if err := validatePhone(c.Phone); err != nil {
			return err
		}
	}

	return nil
}

// AccessToken 访问令牌值对象
type AccessToken struct {
	Token     string `json:"token"`
	TokenType string `json:"token_type"`
	ExpiresIn int64  `json:"expires_in"`
	Scope     string `json:"scope,omitempty"`
}

// NewAccessToken 创建访问令牌
func NewAccessToken(token string, expiresIn int64) *AccessToken {
	return &AccessToken{
		Token:     token,
		TokenType: "Bearer",
		ExpiresIn: expiresIn,
	}
}

// LoginAttempt 登录尝试值对象
type LoginAttempt struct {
	IPAddress string `json:"ip_address"`
	UserAgent string `json:"user_agent"`
	Success   bool   `json:"success"`
	Reason    string `json:"reason,omitempty"`
}

// NewLoginAttempt 创建登录尝试记录
func NewLoginAttempt(ipAddress, userAgent string, success bool, reason string) *LoginAttempt {
	return &LoginAttempt{
		IPAddress: ipAddress,
		UserAgent: userAgent,
		Success:   success,
		Reason:    reason,
	}
}

// 辅助验证函数

// validateUsername 验证用户名
func validateUsername(username string) error {
	if len(username) < 3 {
		return fmt.Errorf("用户名太短：最少3个字符")
	}
	if len(username) > 30 {
		return fmt.Errorf("用户名太长：最多30个字符")
	}

	// 用户名只能包含字母、数字、下划线和连字符
	matched, _ := regexp.MatchString(`^[a-zA-Z0-9_-]+$`, username)
	if !matched {
		return fmt.Errorf("用户名包含无效字符：只允许字母、数字、下划线和连字符")
	}

	return nil
}

// validateEmail 验证邮箱
func validateEmail(email string) error {
	if len(email) > 100 {
		return fmt.Errorf("邮箱太长：最多100个字符")
	}

	// 简单的邮箱格式验证
	emailRegex := `^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$`
	matched, _ := regexp.MatchString(emailRegex, email)
	if !matched {
		return fmt.Errorf("无效的邮箱格式")
	}

	return nil
}

// validatePhone 验证手机号
func validatePhone(phone string) error {
	if len(phone) > 20 {
		return fmt.Errorf("手机号太长：最多20个字符")
	}

	// 简单的手机号验证（支持国际格式）
	phoneRegex := `^[\+]?[1-9][\d]{0,15}$`
	matched, _ := regexp.MatchString(phoneRegex, phone)
	if !matched {
		return fmt.Errorf("无效的手机号格式")
	}

	return nil
}

// isValidLanguageCode 验证语言代码
func isValidLanguageCode(code string) bool {
	// 简单的语言代码验证（ISO 639-1 + 国家代码）
	validCodes := []string{
		"zh-CN", "zh-TW", "en-US", "en-GB", "ja-JP", "ko-KR",
		"fr-FR", "de-DE", "es-ES", "it-IT", "pt-BR", "ru-RU",
	}
	for _, valid := range validCodes {
		if code == valid {
			return true
		}
	}
	return false
}

// isValidTimezone 验证时区
func isValidTimezone(timezone string) bool {
	// 简单的时区验证
	validTimezones := []string{
		"Asia/Shanghai", "Asia/Tokyo", "Asia/Seoul",
		"Europe/London", "Europe/Paris", "Europe/Berlin",
		"America/New_York", "America/Los_Angeles", "America/Chicago",
		"UTC", "GMT",
	}
	for _, valid := range validTimezones {
		if timezone == valid {
			return true
		}
	}
	return false
}
