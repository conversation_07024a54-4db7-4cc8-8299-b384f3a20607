package entity

import (
	"backend/internal/shared/types"
)

// UserTenant 是用户和租户关联的实体
// 关联关系属于特定租户范围
type UserTenant struct {
	types.TenantScopedEntity

	UserBusinessID   string `gorm:"size:36;not null;uniqueIndex:uq_user_tenants_user_tenant"`
	TenantBusinessID string `gorm:"size:36;not null;uniqueIndex:uq_user_tenants_user_tenant"`
	RoleBusinessID   string `gorm:"size:36;not null"`
	Status           int    `gorm:"type:integer;not null;default:1"`
}

// TableName 指定表名
func (UserTenant) TableName() string {
	return "user_tenants"
}

// NewUserTenant 创建一个新的用户租户关联
func NewUserTenant(userBusinessID, tenantBusinessID, roleBusinessID string) *UserTenant {
	userTenant := &UserTenant{
		TenantScopedEntity: types.NewEmptyTenantScopedEntity(),
		UserBusinessID:     userBusinessID,
		TenantBusinessID:   tenantBusinessID,
		RoleBusinessID:     roleBusinessID,
		Status:             1, // 默认为激活状态
	}

	// 设置租户ID，因为这个关联属于特定租户
	userTenant.SetTenantID(tenantBusinessID)

	return userTenant
}
