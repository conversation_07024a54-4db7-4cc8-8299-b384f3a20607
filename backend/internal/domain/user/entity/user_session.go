package entity

import (
	"backend/internal/shared/types"
	"time"

	"github.com/google/uuid"
	"gorm.io/gorm"
)

// UserSession 用户会话实体
type UserSession struct {
	types.TenantScopedEntity

	// 用户关联（技术ID，用于性能优化）
	UserTechID int64 `gorm:"index;column:user_id" json:"-"`

	// 业务ID（对外暴露）
	UserBusinessID string `gorm:"size:36;column:user_business_id" json:"user_id"`

	// 会话信息
	SessionToken string `gorm:"uniqueIndex;size:255" json:"-"` // 会话令牌（不对外暴露）
	RefreshToken string `gorm:"uniqueIndex;size:255" json:"-"` // 刷新令牌（不对外暴露）
	DeviceID     string `gorm:"size:100" json:"device_id"`     // 设备ID
	DeviceType   string `gorm:"size:50" json:"device_type"`    // 设备类型
	DeviceName   string `gorm:"size:100" json:"device_name"`   // 设备名称
	UserAgent    string `gorm:"size:500" json:"user_agent"`    // 用户代理
	IPAddress    string `gorm:"size:45" json:"ip_address"`     // IP地址
	Location     string `gorm:"size:100" json:"location"`      // 登录地点

	// 会话状态
	Status       SessionStatus `gorm:"default:1" json:"status"`
	IsActive     bool          `gorm:"default:true" json:"is_active"`
	LastActivity time.Time     `gorm:"index" json:"last_activity"`

	// 时间管理
	ExpiresAt time.Time  `gorm:"index" json:"expires_at"` // 过期时间
	RevokedAt *time.Time `json:"revoked_at"`              // 吊销时间

	// 关联关系
	User *User `gorm:"foreignKey:UserTechID" json:"user,omitempty"`
}

// SessionStatus 会话状态枚举
type SessionStatus int

const (
	SessionStatusActive   SessionStatus = 1 // 活跃
	SessionStatusExpired  SessionStatus = 2 // 过期
	SessionStatusRevoked  SessionStatus = 3 // 吊销
	SessionStatusInactive SessionStatus = 4 // 未激活
)

// TableName 指定表名
func (s *UserSession) TableName() string {
	return "user_sessions"
}

// BeforeCreate GORM钩子 - 创建前生成ID
func (s *UserSession) BeforeCreate(tx *gorm.DB) error {
	if s.BusinessID == "" {
		s.BusinessID = uuid.New().String()
	}
	if s.LastActivity.IsZero() {
		s.LastActivity = time.Now()
	}
	return nil
}

// IsExpired 检查会话是否过期
func (s *UserSession) IsExpired() bool {
	return time.Now().After(s.ExpiresAt)
}

// IsRevoked 检查会话是否被吊销
func (s *UserSession) IsRevoked() bool {
	return s.RevokedAt != nil
}

// IsValid 检查会话是否有效
func (s *UserSession) IsValid() bool {
	return s.Status == SessionStatusActive &&
		s.IsActive &&
		!s.IsExpired() &&
		!s.IsRevoked()
}

// Revoke 吊销会话
func (s *UserSession) Revoke() {
	now := time.Now()
	s.RevokedAt = &now
	s.Status = SessionStatusRevoked
	s.IsActive = false
}

// UpdateActivity 更新最后活动时间
func (s *UserSession) UpdateActivity() {
	s.LastActivity = time.Now()
}

// Extend 延长会话有效期
func (s *UserSession) Extend(duration time.Duration) {
	s.ExpiresAt = time.Now().Add(duration)
}

// GetRemainingTime 获取剩余有效时间
func (s *UserSession) GetRemainingTime() time.Duration {
	if s.IsExpired() {
		return 0
	}
	return time.Until(s.ExpiresAt)
}
