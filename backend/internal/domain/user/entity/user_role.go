package entity

import (
	"backend/internal/shared/types"
	"time"

	"github.com/google/uuid"
	"gorm.io/gorm"
)

// UserRole 用户角色关联实体
type UserRole struct {
	types.TenantScopedEntity

	// 关联ID（技术ID，用于性能优化）
	UserTechID int64 `gorm:"index;column:user_id" json:"-"`
	RoleTechID int64 `gorm:"index;column:role_id" json:"-"`

	// 业务ID（对外暴露）
	UserBusinessID string `gorm:"size:36;column:user_business_id" json:"user_id"`
	RoleBusinessID string `gorm:"size:36;column:role_business_id" json:"role_id"`

	// 分配信息
	AssignedBy string     `gorm:"size:36" json:"assigned_by"`    // 分配者ID
	AssignedAt time.Time  `json:"assigned_at"`                   // 分配时间
	ExpiresAt  *time.Time `json:"expires_at"`                    // 过期时间（可选）
	IsActive   bool       `gorm:"default:true" json:"is_active"` // 是否激活

	// 关联关系
	User *User `gorm:"foreignKey:UserTechID" json:"user,omitempty"`
	Role *Role `gorm:"foreignKey:RoleTechID" json:"role,omitempty"`
}

// TableName 指定表名
func (ur *UserRole) TableName() string {
	return "user_roles"
}

// BeforeCreate GORM钩子 - 创建前生成ID
func (ur *UserRole) BeforeCreate(tx *gorm.DB) error {
	if ur.BusinessID == "" {
		ur.BusinessID = uuid.New().String()
	}
	if ur.AssignedAt.IsZero() {
		ur.AssignedAt = time.Now()
	}
	return nil
}

// IsExpired 检查角色分配是否过期
func (ur *UserRole) IsExpired() bool {
	if ur.ExpiresAt == nil {
		return false
	}
	return time.Now().After(*ur.ExpiresAt)
}

// IsValid 检查角色分配是否有效
func (ur *UserRole) IsValid() bool {
	return ur.IsActive && !ur.IsExpired() && !ur.IsDeleted()
}

// IsDeleted 检查角色分配是否被逻辑删除
func (ur *UserRole) IsDeleted() bool {
	return ur.DeletedAt.Valid
}

// RolePermission 角色权限关联实体
type RolePermission struct {
	types.TenantScopedEntity

	// 关联ID（技术ID，用于性能优化）
	RoleTechID       int64 `gorm:"index;column:role_id" json:"-"`
	PermissionTechID int64 `gorm:"index;column:permission_id" json:"-"`

	// 业务ID（对外暴露）
	RoleBusinessID       string `gorm:"size:36;column:role_business_id" json:"role_id"`
	PermissionBusinessID string `gorm:"size:36;column:permission_business_id" json:"permission_id"`

	// 分配信息
	AssignedBy string    `gorm:"size:36" json:"assigned_by"`    // 分配者ID
	AssignedAt time.Time `json:"assigned_at"`                   // 分配时间
	IsActive   bool      `gorm:"default:true" json:"is_active"` // 是否激活

	// 关联关系
	Role       *Role       `gorm:"foreignKey:RoleTechID" json:"role,omitempty"`
	Permission *Permission `gorm:"foreignKey:PermissionTechID" json:"permission,omitempty"`
}

// TableName 指定表名
func (rp *RolePermission) TableName() string {
	return "role_permissions"
}

// BeforeCreate GORM钩子 - 创建前生成ID
func (rp *RolePermission) BeforeCreate(tx *gorm.DB) error {
	if rp.BusinessID == "" {
		rp.BusinessID = uuid.New().String()
	}
	if rp.AssignedAt.IsZero() {
		rp.AssignedAt = time.Now()
	}
	return nil
}

// IsValid 检查权限分配是否有效
func (rp *RolePermission) IsValid() bool {
	return rp.IsActive && !rp.IsDeleted()
}

// IsDeleted 检查权限分配是否被逻辑删除
func (rp *RolePermission) IsDeleted() bool {
	return rp.DeletedAt.Valid
}
