package entity

import (
	"backend/internal/shared/types"

	"github.com/google/uuid"
	"gorm.io/gorm"
)

// Role 角色实体
type Role struct {
	types.TenantScopedEntity

	// 角色信息
	Name        string `gorm:"size:100" json:"name"`
	DisplayName string `gorm:"size:100" json:"display_name"`
	Description string `gorm:"size:500" json:"description"`

	// 角色状态
	Status   RoleStatus `gorm:"default:1" json:"status"`
	IsSystem bool       `gorm:"default:false" json:"is_system"` // 系统角色不可删除

	// 关联关系
	UserRoles       []UserRole       `gorm:"foreignKey:RoleTechID" json:"-"`
	RolePermissions []RolePermission `gorm:"foreignKey:RoleTechID" json:"-"`
}

// RoleStatus 角色状态枚举
type RoleStatus int

const (
	RoleStatusInactive RoleStatus = iota // 未激活
	RoleStatusActive                     // 激活
	RoleStatusArchived                   // 归档
)

// TableName 指定表名
func (r *Role) TableName() string {
	return "roles"
}

// BeforeCreate GORM钩子 - 创建前生成ID
func (r *Role) BeforeCreate(tx *gorm.DB) error {
	if r.BusinessID == "" {
		r.BusinessID = uuid.New().String()
	}
	return nil
}

// IsActive 检查角色是否激活
func (r *Role) IsActive() bool {
	return r.Status == RoleStatusActive
}

// IsDeleted 检查角色是否被逻辑删除
func (r *Role) IsDeleted() bool {
	return r.DeletedAt.Valid
}

// CanDelete 检查角色是否可以删除
func (r *Role) CanDelete() bool {
	return !r.IsSystem && !r.IsDeleted()
}

// Permission 权限实体
type Permission struct {
	types.GlobalEntity

	// 权限信息
	Resource    string `gorm:"size:100" json:"resource"`     // 资源
	Action      string `gorm:"size:100" json:"action"`       // 操作
	Name        string `gorm:"size:100" json:"name"`         // 权限名称
	DisplayName string `gorm:"size:100" json:"display_name"` // 显示名称
	Description string `gorm:"size:500" json:"description"`  // 描述

	// 权限状态
	Status   PermissionStatus `gorm:"default:1" json:"status"`
	IsSystem bool             `gorm:"default:false" json:"is_system"` // 系统权限不可删除

	// 关联关系
	RolePermissions []RolePermission `gorm:"foreignKey:PermissionTechID" json:"-"`
}

// PermissionStatus 权限状态枚举
type PermissionStatus int

const (
	PermissionStatusInactive PermissionStatus = iota // 未激活
	PermissionStatusActive                           // 激活
	PermissionStatusArchived                         // 归档
)

// TableName 指定表名
func (p *Permission) TableName() string {
	return "permissions"
}

// BeforeCreate GORM钩子 - 创建前生成ID
func (p *Permission) BeforeCreate(tx *gorm.DB) error {
	if p.BusinessID == "" {
		p.BusinessID = uuid.New().String()
	}
	return nil
}

// IsActive 检查权限是否激活
func (p *Permission) IsActive() bool {
	return p.Status == PermissionStatusActive
}

// IsDeleted 检查权限是否被逻辑删除
func (p *Permission) IsDeleted() bool {
	return p.DeletedAt.Valid
}

// CanDelete 检查权限是否可以删除
func (p *Permission) CanDelete() bool {
	return !p.IsSystem && !p.IsDeleted()
}

// GetResourceAction 获取资源操作标识符
func (p *Permission) GetResourceAction() string {
	return p.Resource + ":" + p.Action
}
