package entity

import (
	"errors"
	"time"

	security "backend/internal/domain/auth/entity"
	"backend/internal/domain/user/valueobject"
	"backend/internal/shared/types"
)

// User 用户实体, 存储用户画像信息
// 用户是全局唯一的实体，不属于特定租户
type User struct {
	types.GlobalEntity

	// 用户基本信息
	Username string                  `gorm:"size:100;not null;uniqueIndex"`
	Email    string                  `gorm:"size:255;uniqueIndex"`
	Phone    string                  `gorm:"size:50;not null;uniqueIndex"`
	Status   UserStatus              `gorm:"type:smallint;default:1"`
	Profile  valueobject.UserProfile `gorm:"embedded"`

	// 关联关系
	Auths   []*security.UserAuth `gorm:"foreignKey:UserBusinessID;references:BusinessID"`
	Tenants []*UserTenant        `gorm:"foreignKey:UserBusinessID;references:BusinessID"`

	// 领域事件 (不持久化)
	domainEvents []interface{} `gorm:"-" json:"-"`
}

// UserStatus 用户状态枚举
type UserStatus int

const (
	UserStatusPending   UserStatus = 0 // 待激活
	UserStatusActive    UserStatus = 1 // 激活
	UserStatusInactive  UserStatus = 2 // 未激活
	UserStatusSuspended UserStatus = 3 // 暂停
	UserStatusBanned    UserStatus = 4 // 封禁
)

// String 状态字符串表示
func (s UserStatus) String() string {
	switch s {
	case UserStatusPending:
		return "pending"
	case UserStatusActive:
		return "active"
	case UserStatusInactive:
		return "inactive"
	case UserStatusSuspended:
		return "suspended"
	case UserStatusBanned:
		return "banned"
	default:
		return "unknown"
	}
}

// TableName 指定表名
func (u *User) TableName() string {
	return "users"
}

// NewUser 创建一个新用户
func NewUser(profile valueobject.UserProfile, username, email, phone string) (*User, error) {
	user := &User{
		GlobalEntity: types.NewEmptyGlobalEntity(),
		Username:     username,
		Email:        email,
		Phone:        phone,
		Status:       UserStatusActive,
		Profile:      profile,
	}

	if err := user.validate(); err != nil {
		return nil, err
	}
	return user, nil
}

func (u *User) validate() error {
	if u.Username == "" {
		return errors.New("用户名是必需的")
	}
	if u.Phone == "" {
		return errors.New("手机号是必需的")
	}
	return nil
}

// Activate 激活用户
func (u *User) Activate() error {
	if u.IsDeleted() {
		return errors.New("不能激活已删除的用户")
	}
	if u.Status == UserStatusActive {
		return errors.New("用户已经是激活状态")
	}
	u.Status = UserStatusActive
	u.UpdatedAt = time.Now()
	// u.addDomainEvent(...)
	return nil
}

// Deactivate 禁用用户
func (u *User) Deactivate() error {
	if u.IsDeleted() {
		return errors.New("不能停用已删除的用户")
	}
	if u.Status == UserStatusInactive {
		return errors.New("用户已经是未激活状态")
	}
	u.Status = UserStatusInactive
	u.UpdatedAt = time.Now()
	// u.addDomainEvent(...)
	return nil
}

// UpdateProfile 更新用户资料
func (u *User) UpdateProfile(profile valueobject.UserProfile) {
	u.Profile = profile
	u.UpdatedAt = time.Now()
}

// CanLogin 检查用户是否可以登录 (只检查状态，不检查密码)
func (u *User) CanLogin() bool {
	return u.Status == UserStatusActive && !u.IsDeleted()
}

// IsDeleted 检查用户是否被逻辑删除
func (u *User) IsDeleted() bool {
	return u.DeletedAt.Valid
}

// GetDomainEvents 获取领域事件
func (u *User) GetDomainEvents() []interface{} {
	return u.domainEvents
}

// ClearDomainEvents 清空领域事件
func (u *User) ClearDomainEvents() {
	u.domainEvents = []interface{}{}
}

// addDomainEvent 添加领域事件 (私有方法)
func (u *User) addDomainEvent(event interface{}) {
	u.domainEvents = append(u.domainEvents, event)
}
