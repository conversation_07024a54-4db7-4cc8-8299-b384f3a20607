package repository

import (
	"context"
	"time"

	"backend/internal/domain/user/entity"

	"gorm.io/gorm"
)

// UserSessionRepository 用户会话仓储接口
type UserSessionRepository interface {
	// 双ID查询支持
	FindByBusinessID(ctx context.Context, businessID string) (*entity.UserSession, error)
	FindByTechID(ctx context.Context, techID int64) (*entity.UserSession, error)
	FindByBusinessIDs(ctx context.Context, businessIDs []string) ([]*entity.UserSession, error)

	// 保存操作
	Save(ctx context.Context, session *entity.UserSession) error
	SaveWithTx(ctx context.Context, tx *gorm.DB, session *entity.UserSession) error
	Update(ctx context.Context, session *entity.UserSession) error
	UpdateWithTx(ctx context.Context, tx *gorm.DB, session *entity.UserSession) error
	Delete(ctx context.Context, businessID string) error
	DeleteWithTx(ctx context.Context, tx *gorm.DB, businessID string) error

	// 令牌查询
	FindBySessionToken(ctx context.Context, sessionToken string) (*entity.UserSession, error)
	FindByRefreshToken(ctx context.Context, refreshToken string) (*entity.UserSession, error)

	// 用户会话查询
	FindByUserID(ctx context.Context, tenantID, userBusinessID string) ([]*entity.UserSession, error)
	FindActiveSessionsByUserID(ctx context.Context, tenantID, userBusinessID string) ([]*entity.UserSession, error)
	FindLatestSessionByUserID(ctx context.Context, tenantID, userBusinessID string) (*entity.UserSession, error)

	// 设备查询
	FindByDeviceID(ctx context.Context, tenantID, deviceID string) ([]*entity.UserSession, error)
	FindByUserAndDevice(ctx context.Context, tenantID, userBusinessID, deviceID string) (*entity.UserSession, error)

	// 租户查询
	FindByTenantID(ctx context.Context, tenantID string, limit, offset int) ([]*entity.UserSession, int64, error)

	// 状态查询
	FindActiveSessions(ctx context.Context, tenantID string, limit, offset int) ([]*entity.UserSession, int64, error)
	FindExpiredSessions(ctx context.Context, tenantID string, limit, offset int) ([]*entity.UserSession, int64, error)
	FindRevokedSessions(ctx context.Context, tenantID string, limit, offset int) ([]*entity.UserSession, int64, error)

	// 时间范围查询
	FindSessionsByTimeRange(ctx context.Context, tenantID string, startTime, endTime time.Time) ([]*entity.UserSession, error)
	FindSessionsByLastActivity(ctx context.Context, tenantID string, since time.Time) ([]*entity.UserSession, error)

	// 统计查询
	CountActiveSessionsByUserID(ctx context.Context, tenantID, userBusinessID string) (int64, error)
	CountSessionsByTenantID(ctx context.Context, tenantID string) (int64, error)
	GetSessionStats(ctx context.Context, tenantID string, days int) (map[string]int64, error)

	// 验证查询
	ExistsBySessionToken(ctx context.Context, sessionToken string) (bool, error)
	ExistsByRefreshToken(ctx context.Context, refreshToken string) (bool, error)

	// 批量操作
	BatchRevokeSessions(ctx context.Context, businessIDs []string) error
	BatchRevokeSessionsByUserID(ctx context.Context, tenantID, userBusinessID string) error
	BatchRevokeSessionsByDeviceID(ctx context.Context, tenantID, deviceID string) error
	BatchUpdateActivity(ctx context.Context, businessIDs []string, lastActivity time.Time) error

	// 清理操作
	CleanupExpiredSessions(ctx context.Context, tenantID string) error
	CleanupRevokedSessions(ctx context.Context, tenantID string, olderThan time.Time) error
	CleanupInactiveSessions(ctx context.Context, tenantID string, inactiveDays int) error
}
