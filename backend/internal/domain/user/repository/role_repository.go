package repository

import (
	"context"

	"backend/internal/domain/user/entity"

	"gorm.io/gorm"
)

// RoleRepository 角色仓储接口
type RoleRepository interface {
	// 双ID查询支持
	FindByBusinessID(ctx context.Context, businessID string) (*entity.Role, error)
	FindByTechID(ctx context.Context, techID int64) (*entity.Role, error)
	FindByBusinessIDs(ctx context.Context, businessIDs []string) ([]*entity.Role, error)
	FindByTechIDs(ctx context.Context, techIDs []int64) ([]*entity.Role, error)

	// 保存操作
	Save(ctx context.Context, role *entity.Role) error
	SaveWithTx(ctx context.Context, tx *gorm.DB, role *entity.Role) error
	Update(ctx context.Context, role *entity.Role) error
	UpdateWithTx(ctx context.Context, tx *gorm.DB, role *entity.Role) error
	Delete(ctx context.Context, businessID string) error
	DeleteWithTx(ctx context.Context, tx *gorm.DB, businessID string) error

	// 业务查询
	FindByName(ctx context.Context, tenantID, name string) (*entity.Role, error)
	FindSystemRoles(ctx context.Context, tenantID string) ([]*entity.Role, error)
	FindCustomRoles(ctx context.Context, tenantID string) ([]*entity.Role, error)

	// 租户查询
	FindByTenantID(ctx context.Context, tenantID string, limit, offset int) ([]*entity.Role, int64, error)
	CountByTenantID(ctx context.Context, tenantID string) (int64, error)

	// 状态查询
	FindActiveRoles(ctx context.Context, tenantID string) ([]*entity.Role, error)
	FindInactiveRoles(ctx context.Context, tenantID string) ([]*entity.Role, error)

	// 搜索查询
	SearchRoles(ctx context.Context, tenantID, keyword string, limit, offset int) ([]*entity.Role, int64, error)

	// 关联查询
	FindRolesWithPermissions(ctx context.Context, tenantID string) ([]*entity.Role, error)
	FindRolesByUserID(ctx context.Context, tenantID, userBusinessID string) ([]*entity.Role, error)

	// 验证查询
	ExistsByName(ctx context.Context, tenantID, name string) (bool, error)

	// 批量操作
	BatchCreate(ctx context.Context, roles []*entity.Role) error
	BatchUpdate(ctx context.Context, roles []*entity.Role) error
}

// PermissionRepository 权限仓储接口
type PermissionRepository interface {
	// 双ID查询支持
	FindByBusinessID(ctx context.Context, businessID string) (*entity.Permission, error)
	FindByTechID(ctx context.Context, techID int64) (*entity.Permission, error)
	FindByBusinessIDs(ctx context.Context, businessIDs []string) ([]*entity.Permission, error)
	FindByTechIDs(ctx context.Context, techIDs []int64) ([]*entity.Permission, error)

	// 保存操作
	Save(ctx context.Context, permission *entity.Permission) error
	SaveWithTx(ctx context.Context, tx *gorm.DB, permission *entity.Permission) error
	Update(ctx context.Context, permission *entity.Permission) error
	UpdateWithTx(ctx context.Context, tx *gorm.DB, permission *entity.Permission) error
	Delete(ctx context.Context, businessID string) error
	DeleteWithTx(ctx context.Context, tx *gorm.DB, businessID string) error

	// 业务查询
	FindByResourceAction(ctx context.Context, resource, action string) (*entity.Permission, error)
	FindByResource(ctx context.Context, resource string) ([]*entity.Permission, error)
	FindSystemPermissions(ctx context.Context) ([]*entity.Permission, error)
	FindCustomPermissions(ctx context.Context) ([]*entity.Permission, error)

	// 状态查询
	FindActivePermissions(ctx context.Context) ([]*entity.Permission, error)
	FindInactivePermissions(ctx context.Context) ([]*entity.Permission, error)

	// 搜索查询
	SearchPermissions(ctx context.Context, keyword string, limit, offset int) ([]*entity.Permission, int64, error)

	// 关联查询
	FindPermissionsByRoleID(ctx context.Context, roleBusinessID string) ([]*entity.Permission, error)
	FindPermissionsByUserID(ctx context.Context, tenantID, userBusinessID string) ([]*entity.Permission, error)

	// 验证查询
	ExistsByResourceAction(ctx context.Context, resource, action string) (bool, error)

	// 批量操作
	BatchCreate(ctx context.Context, permissions []*entity.Permission) error
	BatchUpdate(ctx context.Context, permissions []*entity.Permission) error
}

// UserRoleRepository 用户角色关联仓储接口
type UserRoleRepository interface {
	// 双ID查询支持
	FindByBusinessID(ctx context.Context, businessID string) (*entity.UserRole, error)
	FindByTechID(ctx context.Context, techID int64) (*entity.UserRole, error)
	FindByBusinessIDs(ctx context.Context, businessIDs []string) ([]*entity.UserRole, error)

	// 保存操作
	Save(ctx context.Context, userRole *entity.UserRole) error
	SaveWithTx(ctx context.Context, tx *gorm.DB, userRole *entity.UserRole) error
	Update(ctx context.Context, userRole *entity.UserRole) error
	UpdateWithTx(ctx context.Context, tx *gorm.DB, userRole *entity.UserRole) error
	Delete(ctx context.Context, businessID string) error
	DeleteWithTx(ctx context.Context, tx *gorm.DB, businessID string) error

	// 关联查询
	FindByUserID(ctx context.Context, tenantID, userBusinessID string) ([]*entity.UserRole, error)
	FindByRoleID(ctx context.Context, tenantID, roleBusinessID string) ([]*entity.UserRole, error)
	FindByUserAndRole(ctx context.Context, tenantID, userBusinessID, roleBusinessID string) (*entity.UserRole, error)

	// 租户查询
	FindByTenantID(ctx context.Context, tenantID string, limit, offset int) ([]*entity.UserRole, int64, error)

	// 状态查询
	FindActiveAssignments(ctx context.Context, tenantID string) ([]*entity.UserRole, error)
	FindExpiredAssignments(ctx context.Context, tenantID string) ([]*entity.UserRole, error)

	// 验证查询
	ExistsByUserAndRole(ctx context.Context, tenantID, userBusinessID, roleBusinessID string) (bool, error)

	// 批量操作
	BatchAssignRoles(ctx context.Context, userRoles []*entity.UserRole) error
	BatchRevokeRoles(ctx context.Context, businessIDs []string) error
	BatchUpdateStatus(ctx context.Context, businessIDs []string, isActive bool) error

	// 清理操作
	CleanupExpiredAssignments(ctx context.Context, tenantID string) error
}

// RolePermissionRepository 角色权限关联仓储接口
type RolePermissionRepository interface {
	// 双ID查询支持
	FindByBusinessID(ctx context.Context, businessID string) (*entity.RolePermission, error)
	FindByTechID(ctx context.Context, techID int64) (*entity.RolePermission, error)
	FindByBusinessIDs(ctx context.Context, businessIDs []string) ([]*entity.RolePermission, error)

	// 保存操作
	Save(ctx context.Context, rolePermission *entity.RolePermission) error
	SaveWithTx(ctx context.Context, tx *gorm.DB, rolePermission *entity.RolePermission) error
	Update(ctx context.Context, rolePermission *entity.RolePermission) error
	UpdateWithTx(ctx context.Context, tx *gorm.DB, rolePermission *entity.RolePermission) error
	Delete(ctx context.Context, businessID string) error
	DeleteWithTx(ctx context.Context, tx *gorm.DB, businessID string) error

	// 关联查询
	FindByRoleID(ctx context.Context, tenantID, roleBusinessID string) ([]*entity.RolePermission, error)
	FindByPermissionID(ctx context.Context, permissionBusinessID string) ([]*entity.RolePermission, error)
	FindByRoleAndPermission(ctx context.Context, tenantID, roleBusinessID, permissionBusinessID string) (*entity.RolePermission, error)

	// 租户查询
	FindByTenantID(ctx context.Context, tenantID string, limit, offset int) ([]*entity.RolePermission, int64, error)

	// 状态查询
	FindActiveAssignments(ctx context.Context, tenantID string) ([]*entity.RolePermission, error)

	// 验证查询
	ExistsByRoleAndPermission(ctx context.Context, tenantID, roleBusinessID, permissionBusinessID string) (bool, error)

	// 批量操作
	BatchAssignPermissions(ctx context.Context, rolePermissions []*entity.RolePermission) error
	BatchRevokePermissions(ctx context.Context, businessIDs []string) error
	BatchRevokePermissionsByRole(ctx context.Context, tenantID, roleBusinessID string) error
	BatchRevokePermissionsByPermission(ctx context.Context, permissionBusinessID string) error
}
