package repository

import (
	"context"

	"backend/internal/domain/user/entity"
)

// UserRepository 用户画像仓储接口 (Profile Repository)
type UserRepository interface {
	// === 基本CRUD操作 ===
	Save(ctx context.Context, user *entity.User) error
	Update(ctx context.Context, user *entity.User) error
	Delete(ctx context.Context, businessID string) error

	// === 查询操作 ===
	// 双ID查询
	FindByBusinessID(ctx context.Context, businessID string) (*entity.User, error)
	FindByTechID(ctx context.Context, techID int64) (*entity.User, error)
	FindByBusinessIDs(ctx context.Context, businessIDs []string) ([]*entity.User, error)

	// 列表与分页查询
	FindByTenantID(ctx context.Context, tenantID string, limit, offset int) ([]*entity.User, int64, error)
	SearchUsers(ctx context.Context, tenantID, keyword string, limit, offset int) ([]*entity.User, int64, error)

	// === 存在性检查 (用于业务验证) ===
	ExistsByBusinessID(ctx context.Context, businessID string) (bool, error)

	// === 关联查询 ===
	FindUsersByRoleID(ctx context.Context, tenantID, roleBusinessID string, limit, offset int) ([]*entity.User, int64, error)

	// === 批量操作 ===
	BatchCreate(ctx context.Context, users []*entity.User) error

	// FindByEmail finds a user by its email address.
	FindByEmail(ctx context.Context, email string) (*entity.User, error)
	// FindByPhone finds a user by its phone number.
	FindByPhone(ctx context.Context, phone string) (*entity.User, error)
	// ExistsByEmail checks if a user with the given email exists.
	ExistsByEmail(ctx context.Context, email string) (bool, error)
	// ExistsByPhone checks if a user with the given phone number exists.
	ExistsByPhone(ctx context.Context, phone string) (bool, error)
}
