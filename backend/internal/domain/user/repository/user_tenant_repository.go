package repository

import (
	"backend/internal/domain/user/entity"
	"context"
)

// UserTenantRepository 定义用户租户关联的仓储接口
type UserTenantRepository interface {
	// FindByUserBusinessID 查询指定用户的所有租户关联
	FindByUserBusinessID(ctx context.Context, userBusinessID string) ([]*entity.UserTenant, error)

	// FindByTenantBusinessID 查询指定租户的所有用户关联
	FindByTenantBusinessID(ctx context.Context, tenantBusinessID string) ([]*entity.UserTenant, error)

	// FindByUserAndTenant 查询指定用户和租户的关联关系
	FindByUserAndTenant(ctx context.Context, userBusinessID, tenantBusinessID string) (*entity.UserTenant, error)

	// Create 创建新的用户租户关联
	Create(ctx context.Context, userTenant *entity.UserTenant) error

	// Delete 删除用户租户关联
	Delete(ctx context.Context, businessID string) error
}
