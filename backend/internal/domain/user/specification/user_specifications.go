package specification

import (
	"strings"

	"backend/internal/domain/user/entity"
)

// ==================== 用户状态相关规范 ====================

// UserCanBeActivated 用户可以被激活的业务规则
type UserCanBeActivated struct{}

// IsSatisfiedBy 检查用户是否可以被激活
func (s UserCanBeActivated) IsSatisfiedBy(user *entity.User) bool {
	// 用户必须处于待激活状态
	if user.Status != entity.UserStatusPending {
		return false
	}

	// 用户不能被删除
	if user.IsDeleted() {
		return false
	}

	// 用户必须有有效的联系方式
	return user.Email != "" || user.Phone != ""
}

// GetFailureReason 获取不满足条件的原因
func (s UserCanBeActivated) GetFailureReason(user *entity.User) string {
	if user.Status != entity.UserStatusPending {
		return "用户不是待激活状态"
	}
	if user.IsDeleted() {
		return "用户已被删除"
	}
	if user.Email == "" && user.Phone == "" {
		return "用户缺少有效的联系方式"
	}
	return ""
}

// UserCanBeDeactivated 用户可以被停用的业务规则
type UserCanBeDeactivated struct{}

// IsSatisfiedBy 检查用户是否可以被停用
func (s UserCanBeDeactivated) IsSatisfiedBy(user *entity.User) bool {
	// 用户必须处于激活状态
	if user.Status != entity.UserStatusActive {
		return false
	}

	// 用户不能被删除
	return !user.IsDeleted()
}

// GetFailureReason 获取不满足条件的原因
func (s UserCanBeDeactivated) GetFailureReason(user *entity.User) string {
	if user.Status != entity.UserStatusActive {
		return "用户不是激活状态"
	}
	if user.IsDeleted() {
		return "用户已被删除"
	}
	return ""
}

// UserCanLogin 用户可以登录的业务规则
type UserCanLogin struct{}

// IsSatisfiedBy 检查用户是否可以登录
func (s UserCanLogin) IsSatisfiedBy(user *entity.User) bool {
	// 用户必须处于激活状态
	if user.Status != entity.UserStatusActive {
		return false
	}

	// 用户不能被删除
	if user.IsDeleted() {
		return false
	}

	// 用户不能被封禁
	if user.Status == entity.UserStatusBanned {
		return false
	}

	return true
}

// GetFailureReason 获取不满足条件的原因
func (s UserCanLogin) GetFailureReason(user *entity.User) string {
	if user.Status != entity.UserStatusActive {
		return "用户未激活"
	}
	if user.IsDeleted() {
		return "用户已被删除"
	}
	if user.Status == entity.UserStatusBanned {
		return "用户已被封禁"
	}
	return ""
}

// ==================== 租户访问相关规范 ====================

// UserCanAccessTenant 用户可以访问租户的业务规则
type UserCanAccessTenant struct {
	tenantID string
}

// NewUserCanAccessTenant 创建租户访问规范
func NewUserCanAccessTenant(tenantID string) *UserCanAccessTenant {
	return &UserCanAccessTenant{tenantID: tenantID}
}

// IsSatisfiedBy 检查用户是否可以访问指定租户
func (s *UserCanAccessTenant) IsSatisfiedBy(user *entity.User) bool {
	// 用户必须能够登录
	loginSpec := UserCanLogin{}
	if !loginSpec.IsSatisfiedBy(user) {
		return false
	}

	// 检查用户是否有该租户的关联关系
	for _, tenant := range user.Tenants {
		if tenant.TenantBusinessID == s.tenantID &&
			tenant.Status == 1 { // 激活状态
			return true
		}
	}

	return false
}

// GetFailureReason 获取不满足条件的原因
func (s *UserCanAccessTenant) GetFailureReason(user *entity.User) string {
	loginSpec := UserCanLogin{}
	if !loginSpec.IsSatisfiedBy(user) {
		return loginSpec.GetFailureReason(user)
	}

	// 检查是否有租户关联
	for _, tenant := range user.Tenants {
		if tenant.TenantBusinessID == s.tenantID {
			if tenant.Status != 1 {
				return "用户在该租户中的状态未激活"
			}
		}
	}

	return "用户未关联到该租户"
}

// ==================== 用户资料相关规范 ====================

// UserProfileIsComplete 用户资料完整性规范
type UserProfileIsComplete struct{}

// IsSatisfiedBy 检查用户资料是否完整
func (s UserProfileIsComplete) IsSatisfiedBy(user *entity.User) bool {
	profile := user.Profile

	// 基本信息必须完整
	if user.Username == "" {
		return false
	}

	// 至少要有一种联系方式
	if user.Email == "" && user.Phone == "" {
		return false
	}

	// 显示名称必须存在
	if profile.Nickname == "" && profile.FirstName == "" && profile.LastName == "" {
		return false
	}

	return true
}

// GetFailureReason 获取不满足条件的原因
func (s UserProfileIsComplete) GetFailureReason(user *entity.User) string {
	if user.Username == "" {
		return "缺少用户名"
	}
	if user.Email == "" && user.Phone == "" {
		return "缺少联系方式"
	}

	profile := user.Profile
	if profile.Nickname == "" && profile.FirstName == "" && profile.LastName == "" {
		return "缺少显示名称"
	}

	return ""
}

// ==================== 用户唯一性相关规范 ====================

// UserIdentifierIsUnique 用户标识符唯一性规范
type UserIdentifierIsUnique struct {
	checkUsername bool
	checkEmail    bool
	checkPhone    bool
	excludeUserID string // 排除的用户ID（用于更新时检查）
}

// NewUserIdentifierIsUnique 创建用户标识符唯一性规范
func NewUserIdentifierIsUnique(checkUsername, checkEmail, checkPhone bool, excludeUserID string) *UserIdentifierIsUnique {
	return &UserIdentifierIsUnique{
		checkUsername: checkUsername,
		checkEmail:    checkEmail,
		checkPhone:    checkPhone,
		excludeUserID: excludeUserID,
	}
}

// IsSatisfiedBy 检查用户标识符是否唯一（需要配合仓储层使用）
func (s *UserIdentifierIsUnique) IsSatisfiedBy(user *entity.User) bool {
	// 这个规范需要配合仓储层的查询来实现
	// 在实际使用时，应该通过仓储层查询数据库来验证唯一性
	return true
}

// GetValidationFields 获取需要验证的字段
func (s *UserIdentifierIsUnique) GetValidationFields(user *entity.User) map[string]string {
	fields := make(map[string]string)

	if s.checkUsername && user.Username != "" {
		fields["username"] = user.Username
	}
	if s.checkEmail && user.Email != "" {
		fields["email"] = user.Email
	}
	if s.checkPhone && user.Phone != "" {
		fields["phone"] = user.Phone
	}

	return fields
}

// GetExcludeUserID 获取排除的用户ID
func (s *UserIdentifierIsUnique) GetExcludeUserID() string {
	return s.excludeUserID
}

// ==================== 复合规范 ====================

// UserIsValidForCreation 用户创建有效性复合规范
type UserIsValidForCreation struct{}

// IsSatisfiedBy 检查用户是否可以被创建
func (s UserIsValidForCreation) IsSatisfiedBy(user *entity.User) bool {
	// 检查资料完整性
	profileSpec := UserProfileIsComplete{}
	if !profileSpec.IsSatisfiedBy(user) {
		return false
	}

	// 用户名格式验证
	if !s.isValidUsername(user.Username) {
		return false
	}

	// 邮箱格式验证（如果提供）
	if user.Email != "" && !s.isValidEmail(user.Email) {
		return false
	}

	// 手机号格式验证（如果提供）
	if user.Phone != "" && !s.isValidPhone(user.Phone) {
		return false
	}

	return true
}

// GetFailureReason 获取不满足条件的原因
func (s UserIsValidForCreation) GetFailureReason(user *entity.User) string {
	profileSpec := UserProfileIsComplete{}
	if !profileSpec.IsSatisfiedBy(user) {
		return profileSpec.GetFailureReason(user)
	}

	if !s.isValidUsername(user.Username) {
		return "用户名格式无效"
	}

	if user.Email != "" && !s.isValidEmail(user.Email) {
		return "邮箱格式无效"
	}

	if user.Phone != "" && !s.isValidPhone(user.Phone) {
		return "手机号格式无效"
	}

	return ""
}

// 辅助验证方法
func (s UserIsValidForCreation) isValidUsername(username string) bool {
	if len(username) < 3 || len(username) > 30 {
		return false
	}
	// 用户名只能包含字母、数字、下划线和连字符
	for _, char := range username {
		if !((char >= 'a' && char <= 'z') ||
			(char >= 'A' && char <= 'Z') ||
			(char >= '0' && char <= '9') ||
			char == '_' || char == '-') {
			return false
		}
	}
	return true
}

func (s UserIsValidForCreation) isValidEmail(email string) bool {
	// 简单的邮箱格式验证
	return strings.Contains(email, "@") && len(email) > 5 && len(email) <= 100
}

func (s UserIsValidForCreation) isValidPhone(phone string) bool {
	// 简单的手机号验证
	return len(phone) >= 10 && len(phone) <= 20
}

// ==================== 规范接口定义 ====================

// Specification 规范接口
type Specification[T any] interface {
	IsSatisfiedBy(entity T) bool
	GetFailureReason(entity T) string
}

// CompositeSpecification 复合规范接口
type CompositeSpecification[T any] interface {
	Specification[T]
	And(other Specification[T]) Specification[T]
	Or(other Specification[T]) Specification[T]
	Not() Specification[T]
}

// ==================== 规范工厂 ====================

// UserSpecificationFactory 用户规范工厂
type UserSpecificationFactory struct{}

// NewUserSpecificationFactory 创建用户规范工厂
func NewUserSpecificationFactory() *UserSpecificationFactory {
	return &UserSpecificationFactory{}
}

// CanActivate 创建用户激活规范
func (f *UserSpecificationFactory) CanActivate() Specification[*entity.User] {
	return UserCanBeActivated{}
}

// CanDeactivate 创建用户停用规范
func (f *UserSpecificationFactory) CanDeactivate() Specification[*entity.User] {
	return UserCanBeDeactivated{}
}

// CanLogin 创建用户登录规范
func (f *UserSpecificationFactory) CanLogin() Specification[*entity.User] {
	return UserCanLogin{}
}

// CanAccessTenant 创建租户访问规范
func (f *UserSpecificationFactory) CanAccessTenant(tenantID string) Specification[*entity.User] {
	return NewUserCanAccessTenant(tenantID)
}

// IsValidForCreation 创建用户创建有效性规范
func (f *UserSpecificationFactory) IsValidForCreation() Specification[*entity.User] {
	return UserIsValidForCreation{}
}

// ProfileIsComplete 创建用户资料完整性规范
func (f *UserSpecificationFactory) ProfileIsComplete() Specification[*entity.User] {
	return UserProfileIsComplete{}
}
