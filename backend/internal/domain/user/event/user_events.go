package event

import (
	"time"

	"backend/internal/domain/user/entity"
)

// UserEvent 用户事件基础结构
type UserEvent struct {
	EventType  string                 `json:"event_type"`
	EventID    string                 `json:"event_id"`
	TenantID   string                 `json:"tenant_id"`
	UserID     string                 `json:"user_id"`
	OccurredAt time.Time              `json:"occurred_at"`
	Version    int                    `json:"version"`
	Metadata   map[string]interface{} `json:"metadata,omitempty"`
}

// UserRegisteredEvent 用户注册事件
type UserRegisteredEvent struct {
	UserEvent
	User           *entity.User `json:"user"`
	RegistrationIP string       `json:"registration_ip"`
	UserAgent      string       `json:"user_agent"`
	Source         string       `json:"source"` // web, mobile, api, etc.
}

// UserLoggedInEvent 用户登录事件
type UserLoggedInEvent struct {
	UserEvent
	User       *entity.User `json:"user"`
	SessionID  string       `json:"session_id"`
	DeviceID   string       `json:"device_id"`
	DeviceType string       `json:"device_type"`
	IPAddress  string       `json:"ip_address"`
	UserAgent  string       `json:"user_agent"`
	Location   string       `json:"location"`
	LoginTime  time.Time    `json:"login_time"`
}

// UserLoggedOutEvent 用户注销事件
type UserLoggedOutEvent struct {
	UserEvent
	SessionID  string    `json:"session_id"`
	DeviceID   string    `json:"device_id"`
	IPAddress  string    `json:"ip_address"`
	LogoutTime time.Time `json:"logout_time"`
	Reason     string    `json:"reason"` // manual, expired, forced, etc.
}

// UserProfileUpdatedEvent 用户资料更新事件
type UserProfileUpdatedEvent struct {
	UserEvent
	UpdatedFields map[string]interface{} `json:"updated_fields"`
	OldValues     map[string]interface{} `json:"old_values"`
	NewValues     map[string]interface{} `json:"new_values"`
	UpdatedBy     string                 `json:"updated_by"`
}

// UserPasswordChangedEvent 用户密码变更事件
type UserPasswordChangedEvent struct {
	UserEvent
	ChangedBy string    `json:"changed_by"`
	IPAddress string    `json:"ip_address"`
	UserAgent string    `json:"user_agent"`
	ChangedAt time.Time `json:"changed_at"`
}

// UserStatusChangedEvent 用户状态变更事件
type UserStatusChangedEvent struct {
	UserEvent
	OldStatus entity.UserStatus `json:"old_status"`
	NewStatus entity.UserStatus `json:"new_status"`
	Reason    string            `json:"reason"`
	ChangedBy string            `json:"changed_by"`
}

// UserEmailVerifiedEvent 用户邮箱验证事件
type UserEmailVerifiedEvent struct {
	UserEvent
	Email       string    `json:"email"`
	VerifiedAt  time.Time `json:"verified_at"`
	VerifyToken string    `json:"verify_token"`
}

// UserPhoneVerifiedEvent 用户手机验证事件
type UserPhoneVerifiedEvent struct {
	UserEvent
	Phone      string    `json:"phone"`
	VerifiedAt time.Time `json:"verified_at"`
	VerifyCode string    `json:"verify_code"`
}

// UserTwoFactorEnabledEvent 用户两步验证启用事件
type UserTwoFactorEnabledEvent struct {
	UserEvent
	Method    string    `json:"method"` // totp, sms, email
	EnabledAt time.Time `json:"enabled_at"`
}

// UserTwoFactorDisabledEvent 用户两步验证禁用事件
type UserTwoFactorDisabledEvent struct {
	UserEvent
	Method     string    `json:"method"`
	DisabledAt time.Time `json:"disabled_at"`
	DisabledBy string    `json:"disabled_by"`
}

// UserRoleAssignedEvent 用户角色分配事件
type UserRoleAssignedEvent struct {
	UserEvent
	RoleID     string     `json:"role_id"`
	RoleName   string     `json:"role_name"`
	AssignedBy string     `json:"assigned_by"`
	AssignedAt time.Time  `json:"assigned_at"`
	ExpiresAt  *time.Time `json:"expires_at,omitempty"`
}

// UserRoleRevokedEvent 用户角色撤销事件
type UserRoleRevokedEvent struct {
	UserEvent
	RoleID    string    `json:"role_id"`
	RoleName  string    `json:"role_name"`
	RevokedBy string    `json:"revoked_by"`
	RevokedAt time.Time `json:"revoked_at"`
	Reason    string    `json:"reason"`
}

// UserPermissionGrantedEvent 用户权限授予事件
type UserPermissionGrantedEvent struct {
	UserEvent
	PermissionID string    `json:"permission_id"`
	Resource     string    `json:"resource"`
	Action       string    `json:"action"`
	GrantedBy    string    `json:"granted_by"`
	GrantedAt    time.Time `json:"granted_at"`
	GrantType    string    `json:"grant_type"` // direct, role-based
}

// UserPermissionRevokedEvent 用户权限撤销事件
type UserPermissionRevokedEvent struct {
	UserEvent
	PermissionID string    `json:"permission_id"`
	Resource     string    `json:"resource"`
	Action       string    `json:"action"`
	RevokedBy    string    `json:"revoked_by"`
	RevokedAt    time.Time `json:"revoked_at"`
	Reason       string    `json:"reason"`
}

// UserSessionCreatedEvent 用户会话创建事件
type UserSessionCreatedEvent struct {
	UserEvent
	SessionID  string    `json:"session_id"`
	DeviceID   string    `json:"device_id"`
	DeviceType string    `json:"device_type"`
	IPAddress  string    `json:"ip_address"`
	UserAgent  string    `json:"user_agent"`
	Location   string    `json:"location"`
	CreatedAt  time.Time `json:"created_at"`
	ExpiresAt  time.Time `json:"expires_at"`
}

// UserSessionExpiredEvent 用户会话过期事件
type UserSessionExpiredEvent struct {
	UserEvent
	SessionID string    `json:"session_id"`
	DeviceID  string    `json:"device_id"`
	ExpiredAt time.Time `json:"expired_at"`
}

// UserSessionRevokedEvent 用户会话撤销事件
type UserSessionRevokedEvent struct {
	UserEvent
	SessionID string    `json:"session_id"`
	DeviceID  string    `json:"device_id"`
	RevokedBy string    `json:"revoked_by"`
	RevokedAt time.Time `json:"revoked_at"`
	Reason    string    `json:"reason"`
}

// UserDeletedEvent 用户删除事件（逻辑删除）
type UserDeletedEvent struct {
	UserEvent
	DeletedBy string    `json:"deleted_by"`
	DeletedAt time.Time `json:"deleted_at"`
	Reason    string    `json:"reason"`
}

// UserRestoredEvent 用户恢复事件
type UserRestoredEvent struct {
	UserEvent
	RestoredBy string    `json:"restored_by"`
	RestoredAt time.Time `json:"restored_at"`
	Reason     string    `json:"reason"`
}

// UserSecurityEvent 用户安全事件
type UserSecurityEvent struct {
	UserEvent
	SecurityEventType string                 `json:"security_event_type"` // suspicious_login, password_breach, etc.
	RiskLevel         string                 `json:"risk_level"`          // low, medium, high, critical
	IPAddress         string                 `json:"ip_address"`
	UserAgent         string                 `json:"user_agent"`
	Location          string                 `json:"location"`
	Details           map[string]interface{} `json:"details"`
	ActionTaken       string                 `json:"action_taken"` // none, blocked, locked, notified
}

// EventType 常量定义
const (
	// 用户生命周期事件
	EventTypeUserRegistered = "user.registered"
	EventTypeUserLoggedIn   = "user.logged_in"
	EventTypeUserLoggedOut  = "user.logged_out"
	EventTypeUserDeleted    = "user.deleted"
	EventTypeUserRestored   = "user.restored"

	// 用户资料事件
	EventTypeUserProfileUpdated  = "user.profile.updated"
	EventTypeUserPasswordChanged = "user.password.changed"
	EventTypeUserStatusChanged   = "user.status.changed"
	EventTypeUserEmailVerified   = "user.email.verified"
	EventTypeUserPhoneVerified   = "user.phone.verified"

	// 两步验证事件
	EventTypeUserTwoFactorEnabled  = "user.two_factor.enabled"
	EventTypeUserTwoFactorDisabled = "user.two_factor.disabled"

	// 角色权限事件
	EventTypeUserRoleAssigned      = "user.role.assigned"
	EventTypeUserRoleRevoked       = "user.role.revoked"
	EventTypeUserPermissionGranted = "user.permission.granted"
	EventTypeUserPermissionRevoked = "user.permission.revoked"

	// 会话事件
	EventTypeUserSessionCreated = "user.session.created"
	EventTypeUserSessionExpired = "user.session.expired"
	EventTypeUserSessionRevoked = "user.session.revoked"

	// 安全事件
	EventTypeUserSecurity = "user.security"
)

// NewUserEvent 创建基础用户事件
func NewUserEvent(eventType, tenantID, userID string) UserEvent {
	return UserEvent{
		EventType:  eventType,
		EventID:    generateEventID(),
		TenantID:   tenantID,
		UserID:     userID,
		OccurredAt: time.Now(),
		Version:    1,
		Metadata:   make(map[string]interface{}),
	}
}

// generateEventID 生成事件ID
func generateEventID() string {
	// 使用 UUID 或雪花ID生成唯一事件ID
	// 这里简化为时间戳，实际应该使用更robust的ID生成器
	return time.Now().Format("20060102150405000000")
}
