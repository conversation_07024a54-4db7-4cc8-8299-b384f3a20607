package service

import (
	"context"

	"backend/internal/domain/user/entity"
)

// UserAuthorizationService 用户授权服务接口
type UserAuthorizationService interface {
	// 权限检查
	CheckPermission(ctx context.Context, req *CheckPermissionRequest) (*PermissionResult, error)
	CheckMultiplePermissions(ctx context.Context, req *CheckMultiplePermissionsRequest) (*MultiplePermissionResult, error)

	// 角色管理
	AssignRole(ctx context.Context, req *AssignRoleRequest) error
	RevokeRole(ctx context.Context, req *RevokeRoleRequest) error
	AssignMultipleRoles(ctx context.Context, req *AssignMultipleRolesRequest) error
	RevokeMultipleRoles(ctx context.Context, req *RevokeMultipleRolesRequest) error

	// 权限管理
	GrantPermission(ctx context.Context, req *GrantPermissionRequest) error
	RevokePermission(ctx context.Context, req *RevokePermissionRequest) error
	GrantMultiplePermissions(ctx context.Context, req *GrantMultiplePermissionsRequest) error
	RevokeMultiplePermissions(ctx context.Context, req *RevokeMultiplePermissionsRequest) error

	// 用户权限查询
	GetUserRoles(ctx context.Context, tenantID, userID string) ([]*entity.Role, error)
	GetUserPermissions(ctx context.Context, tenantID, userID string) ([]*entity.Permission, error)
	GetUserEffectivePermissions(ctx context.Context, tenantID, userID string) ([]*entity.Permission, error)

	// 角色权限查询
	GetRolePermissions(ctx context.Context, tenantID, roleID string) ([]*entity.Permission, error)
	GetRoleUsers(ctx context.Context, tenantID, roleID string) ([]*entity.User, error)

	// 权限继承和计算
	CalculateEffectivePermissions(ctx context.Context, tenantID, userID string) ([]*entity.Permission, error)
	RefreshUserPermissions(ctx context.Context, tenantID, userID string) error

	// Casbin策略管理
	SyncUserToCasbin(ctx context.Context, tenantID, userID string) error
	SyncRoleToCasbin(ctx context.Context, tenantID, roleID string) error
	SyncAllToCasbin(ctx context.Context, tenantID string) error

	// 策略验证
	ValidatePolicy(ctx context.Context, req *ValidatePolicyRequest) (*PolicyValidationResult, error)
}

// 请求和响应结构

// CheckPermissionRequest 权限检查请求
type CheckPermissionRequest struct {
	TenantID string `json:"tenant_id" validate:"required"`
	UserID   string `json:"user_id" validate:"required"`
	Resource string `json:"resource" validate:"required"`
	Action   string `json:"action" validate:"required"`
}

// PermissionResult 权限检查结果
type PermissionResult struct {
	Allowed     bool     `json:"allowed"`
	UserID      string   `json:"user_id"`
	Resource    string   `json:"resource"`
	Action      string   `json:"action"`
	MatchedRole string   `json:"matched_role,omitempty"`
	Reason      string   `json:"reason,omitempty"`
	Roles       []string `json:"roles,omitempty"`
}

// CheckMultiplePermissionsRequest 多权限检查请求
type CheckMultiplePermissionsRequest struct {
	TenantID    string            `json:"tenant_id" validate:"required"`
	UserID      string            `json:"user_id" validate:"required"`
	Permissions []PermissionCheck `json:"permissions" validate:"required,min=1"`
}

// PermissionCheck 权限检查项
type PermissionCheck struct {
	Resource string `json:"resource" validate:"required"`
	Action   string `json:"action" validate:"required"`
}

// MultiplePermissionResult 多权限检查结果
type MultiplePermissionResult struct {
	UserID  string             `json:"user_id"`
	Results []PermissionResult `json:"results"`
	Summary PermissionSummary  `json:"summary"`
}

// PermissionSummary 权限检查摘要
type PermissionSummary struct {
	Total   int `json:"total"`
	Allowed int `json:"allowed"`
	Denied  int `json:"denied"`
}

// AssignRoleRequest 分配角色请求
type AssignRoleRequest struct {
	TenantID   string  `json:"tenant_id" validate:"required"`
	UserID     string  `json:"user_id" validate:"required"`
	RoleID     string  `json:"role_id" validate:"required"`
	AssignedBy string  `json:"assigned_by" validate:"required"`
	ExpiresAt  *string `json:"expires_at,omitempty"` // ISO8601格式时间字符串
}

// RevokeRoleRequest 撤销角色请求
type RevokeRoleRequest struct {
	TenantID string `json:"tenant_id" validate:"required"`
	UserID   string `json:"user_id" validate:"required"`
	RoleID   string `json:"role_id" validate:"required"`
}

// AssignMultipleRolesRequest 分配多个角色请求
type AssignMultipleRolesRequest struct {
	TenantID   string   `json:"tenant_id" validate:"required"`
	UserID     string   `json:"user_id" validate:"required"`
	RoleIDs    []string `json:"role_ids" validate:"required,min=1"`
	AssignedBy string   `json:"assigned_by" validate:"required"`
	ExpiresAt  *string  `json:"expires_at,omitempty"`
}

// RevokeMultipleRolesRequest 撤销多个角色请求
type RevokeMultipleRolesRequest struct {
	TenantID string   `json:"tenant_id" validate:"required"`
	UserID   string   `json:"user_id" validate:"required"`
	RoleIDs  []string `json:"role_ids" validate:"required,min=1"`
}

// GrantPermissionRequest 授予权限请求
type GrantPermissionRequest struct {
	TenantID     string `json:"tenant_id" validate:"required"`
	RoleID       string `json:"role_id" validate:"required"`
	PermissionID string `json:"permission_id" validate:"required"`
	AssignedBy   string `json:"assigned_by" validate:"required"`
}

// RevokePermissionRequest 撤销权限请求
type RevokePermissionRequest struct {
	TenantID     string `json:"tenant_id" validate:"required"`
	RoleID       string `json:"role_id" validate:"required"`
	PermissionID string `json:"permission_id" validate:"required"`
}

// GrantMultiplePermissionsRequest 授予多个权限请求
type GrantMultiplePermissionsRequest struct {
	TenantID      string   `json:"tenant_id" validate:"required"`
	RoleID        string   `json:"role_id" validate:"required"`
	PermissionIDs []string `json:"permission_ids" validate:"required,min=1"`
	AssignedBy    string   `json:"assigned_by" validate:"required"`
}

// RevokeMultiplePermissionsRequest 撤销多个权限请求
type RevokeMultiplePermissionsRequest struct {
	TenantID      string   `json:"tenant_id" validate:"required"`
	RoleID        string   `json:"role_id" validate:"required"`
	PermissionIDs []string `json:"permission_ids" validate:"required,min=1"`
}

// ValidatePolicyRequest 策略验证请求
type ValidatePolicyRequest struct {
	TenantID string `json:"tenant_id" validate:"required"`
	Subject  string `json:"subject" validate:"required"`
	Domain   string `json:"domain" validate:"required"`
	Object   string `json:"object" validate:"required"`
	Action   string `json:"action" validate:"required"`
}

// PolicyValidationResult 策略验证结果
type PolicyValidationResult struct {
	Valid           bool     `json:"valid"`
	Subject         string   `json:"subject"`
	Domain          string   `json:"domain"`
	Object          string   `json:"object"`
	Action          string   `json:"action"`
	MatchedPolicies []string `json:"matched_policies,omitempty"`
	Reason          string   `json:"reason,omitempty"`
}

// RoleManagementService 角色管理服务接口
type RoleManagementService interface {
	// 创建角色
	CreateRole(ctx context.Context, req *CreateRoleRequest) (*entity.Role, error)

	// 更新角色
	UpdateRole(ctx context.Context, req *UpdateRoleRequest) (*entity.Role, error)

	// 删除角色
	DeleteRole(ctx context.Context, tenantID, roleID string) error

	// 查询角色
	GetRole(ctx context.Context, tenantID, roleID string) (*entity.Role, error)
	GetRoleByName(ctx context.Context, tenantID, name string) (*entity.Role, error)
	ListRoles(ctx context.Context, req *ListRolesRequest) (*ListRolesResponse, error)

	// 角色复制
	CloneRole(ctx context.Context, req *CloneRoleRequest) (*entity.Role, error)

	// 角色权限管理
	SetRolePermissions(ctx context.Context, req *SetRolePermissionsRequest) error
	AddRolePermissions(ctx context.Context, req *AddRolePermissionsRequest) error
	RemoveRolePermissions(ctx context.Context, req *RemoveRolePermissionsRequest) error
}

// CreateRoleRequest 创建角色请求
type CreateRoleRequest struct {
	TenantID    string   `json:"tenant_id" validate:"required"`
	Name        string   `json:"name" validate:"required,min=2,max=50"`
	DisplayName string   `json:"display_name" validate:"required,min=2,max=100"`
	Description string   `json:"description,omitempty"`
	Permissions []string `json:"permissions,omitempty"` // 权限ID列表
}

// UpdateRoleRequest 更新角色请求
type UpdateRoleRequest struct {
	TenantID    string  `json:"tenant_id" validate:"required"`
	RoleID      string  `json:"role_id" validate:"required"`
	Name        *string `json:"name,omitempty"`
	DisplayName *string `json:"display_name,omitempty"`
	Description *string `json:"description,omitempty"`
	Status      *int    `json:"status,omitempty"`
}

// ListRolesRequest 角色列表请求
type ListRolesRequest struct {
	TenantID string `json:"tenant_id" validate:"required"`
	Keyword  string `json:"keyword,omitempty"`
	Status   *int   `json:"status,omitempty"`
	IsSystem *bool  `json:"is_system,omitempty"`
	Page     int    `json:"page,omitempty" validate:"min=1"`
	PageSize int    `json:"page_size,omitempty" validate:"min=1,max=100"`
}

// ListRolesResponse 角色列表响应
type ListRolesResponse struct {
	Roles      []*entity.Role `json:"roles"`
	Total      int64          `json:"total"`
	Page       int            `json:"page"`
	PageSize   int            `json:"page_size"`
	TotalPages int            `json:"total_pages"`
}

// CloneRoleRequest 复制角色请求
type CloneRoleRequest struct {
	TenantID        string `json:"tenant_id" validate:"required"`
	SourceRoleID    string `json:"source_role_id" validate:"required"`
	NewName         string `json:"new_name" validate:"required,min=2,max=50"`
	NewDisplayName  string `json:"new_display_name" validate:"required,min=2,max=100"`
	NewDescription  string `json:"new_description,omitempty"`
	CopyPermissions bool   `json:"copy_permissions,omitempty"`
}

// SetRolePermissionsRequest 设置角色权限请求
type SetRolePermissionsRequest struct {
	TenantID      string   `json:"tenant_id" validate:"required"`
	RoleID        string   `json:"role_id" validate:"required"`
	PermissionIDs []string `json:"permission_ids" validate:"required"`
}

// AddRolePermissionsRequest 添加角色权限请求
type AddRolePermissionsRequest struct {
	TenantID      string   `json:"tenant_id" validate:"required"`
	RoleID        string   `json:"role_id" validate:"required"`
	PermissionIDs []string `json:"permission_ids" validate:"required,min=1"`
}

// RemoveRolePermissionsRequest 移除角色权限请求
type RemoveRolePermissionsRequest struct {
	TenantID      string   `json:"tenant_id" validate:"required"`
	RoleID        string   `json:"role_id" validate:"required"`
	PermissionIDs []string `json:"permission_ids" validate:"required,min=1"`
}
