package factory

import (
	"time"

	"backend/internal/domain/event"
	"backend/internal/domain/user/entity"
)

// UserEventFactory 用户事件工厂
type UserEventFactory struct{}

// NewUserEventFactory 创建用户事件工厂
func NewUserEventFactory() *UserEventFactory {
	return &UserEventFactory{}
}

// CreateUserCreatedEvent 创建用户创建事件
func (f *UserEventFactory) CreateUserCreatedEvent(user *entity.User, tenantID string) *event.UserCreatedEvent {
	data := &event.UserCreatedEventData{
		UserID:    user.BusinessID,
		TenantID:  tenantID,
		Username:  user.Username,
		Email:     user.Email,
		Phone:     user.Phone,
		FirstName: user.Profile.FirstName,
		LastName:  user.Profile.LastName,
		Status:    user.Status.String(),
		CreatedAt: user.CreatedAt,
	}
	
	return event.NewUserCreatedEvent(user.BusinessID, data)
}

// CreateUserActivatedEvent 创建用户激活事件
func (f *UserEventFactory) CreateUserActivatedEvent(user *entity.User, activatedBy string, reason string) *event.UserActivatedEvent {
	data := &event.UserActivatedEventData{
		UserID:      user.BusinessID,
		ActivatedBy: activatedBy,
		ActivatedAt: time.Now(),
		Reason:      reason,
	}
	
	return event.NewUserActivatedEvent(user.BusinessID, data)
}

// CreateUserDeactivatedEvent 创建用户停用事件
func (f *UserEventFactory) CreateUserDeactivatedEvent(user *entity.User, deactivatedBy string, reason string) *event.UserDeactivatedEvent {
	data := &event.UserDeactivatedEventData{
		UserID:        user.BusinessID,
		DeactivatedBy: deactivatedBy,
		DeactivatedAt: time.Now(),
		Reason:        reason,
	}
	
	return event.NewUserDeactivatedEvent(user.BusinessID, data)
}

// CreateUserUpdatedEvent 创建用户更新事件
func (f *UserEventFactory) CreateUserUpdatedEvent(user *entity.User, updatedBy string, changes map[string]interface{}) *event.UserUpdatedEvent {
	data := &event.UserUpdatedEventData{
		UserID:    user.BusinessID,
		UpdatedBy: updatedBy,
		UpdatedAt: time.Now(),
		Changes:   changes,
	}
	
	return event.NewUserUpdatedEvent(user.BusinessID, data)
}

// CreateUserProfileUpdatedEvent 创建用户资料更新事件
func (f *UserEventFactory) CreateUserProfileUpdatedEvent(user *entity.User, updatedBy string, changes map[string]interface{}) *event.UserProfileUpdatedEvent {
	profile := map[string]interface{}{
		"first_name": user.Profile.FirstName,
		"last_name":  user.Profile.LastName,
		"nickname":   user.Profile.Nickname,
		"avatar":     user.Profile.Avatar,
		"language":   user.Profile.Language,
		"timezone":   user.Profile.Timezone,
	}
	
	data := &event.UserProfileUpdatedEventData{
		UserID:    user.BusinessID,
		UpdatedBy: updatedBy,
		UpdatedAt: time.Now(),
		Profile:   profile,
		Changes:   changes,
	}
	
	return event.NewUserProfileUpdatedEvent(user.BusinessID, data)
}

// CreateUserContactUpdatedEvent 创建用户联系信息更新事件
func (f *UserEventFactory) CreateUserContactUpdatedEvent(user *entity.User, updatedBy string, changes map[string]interface{}) *event.UserContactUpdatedEvent {
	contactInfo := map[string]interface{}{
		"email": user.Email,
		"phone": user.Phone,
	}
	
	data := &event.UserContactUpdatedEventData{
		UserID:      user.BusinessID,
		UpdatedBy:   updatedBy,
		UpdatedAt:   time.Now(),
		ContactInfo: contactInfo,
		Changes:     changes,
	}
	
	return event.NewUserContactUpdatedEvent(user.BusinessID, data)
}

// CreateUserPreferencesUpdatedEvent 创建用户偏好设置更新事件
func (f *UserEventFactory) CreateUserPreferencesUpdatedEvent(user *entity.User, updatedBy string, changes map[string]interface{}) *event.UserPreferencesUpdatedEvent {
	preferences := map[string]interface{}{
		"language": user.Profile.Language,
		"timezone": user.Profile.Timezone,
	}
	
	data := &event.UserPreferencesUpdatedEventData{
		UserID:      user.BusinessID,
		UpdatedBy:   updatedBy,
		UpdatedAt:   time.Now(),
		Preferences: preferences,
		Changes:     changes,
	}
	
	return event.NewUserPreferencesUpdatedEvent(user.BusinessID, data)
}
