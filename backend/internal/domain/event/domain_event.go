package event

import (
	"time"

	"github.com/google/uuid"
)

// DomainEvent 领域事件接口
// 所有领域事件都必须实现此接口
type DomainEvent interface {
	// EventID 事件唯一标识
	EventID() string

	// EventType 事件类型
	EventType() string

	// Topic 事件主题（兼容现有MQ系统）
	Topic() string

	// AggregateID 聚合根ID
	AggregateID() string

	// AggregateType 聚合根类型
	AggregateType() string

	// EventVersion 事件版本
	EventVersion() int

	// OccurredAt 事件发生时间
	OccurredAt() time.Time

	// EventData 事件数据
	EventData() interface{}

	// Metadata 事件元数据
	Metadata() map[string]interface{}
}

// BaseDomainEvent 基础领域事件
// 提供DomainEvent接口的默认实现
type BaseDomainEvent struct {
	eventID       string
	eventType     string
	aggregateID   string
	aggregateType string
	eventVersion  int
	occurredAt    time.Time
	eventData     interface{}
	metadata      map[string]interface{}
}

// NewBaseDomainEvent 创建基础领域事件
func NewBaseDomainEvent(
	eventType string,
	aggregateID string,
	aggregateType string,
	eventVersion int,
	eventData interface{},
) *BaseDomainEvent {
	return &BaseDomainEvent{
		eventID:       uuid.New().String(),
		eventType:     eventType,
		aggregateID:   aggregateID,
		aggregateType: aggregateType,
		eventVersion:  eventVersion,
		occurredAt:    time.Now(),
		eventData:     eventData,
		metadata:      make(map[string]interface{}),
	}
}

// EventID 实现DomainEvent接口
func (e *BaseDomainEvent) EventID() string {
	return e.eventID
}

// EventType 实现DomainEvent接口
func (e *BaseDomainEvent) EventType() string {
	return e.eventType
}

// Topic 实现DomainEvent接口（兼容现有MQ系统）
func (e *BaseDomainEvent) Topic() string {
	// 使用事件类型作为Topic，符合现有系统的约定
	return e.eventType
}

// AggregateID 实现DomainEvent接口
func (e *BaseDomainEvent) AggregateID() string {
	return e.aggregateID
}

// AggregateType 实现DomainEvent接口
func (e *BaseDomainEvent) AggregateType() string {
	return e.aggregateType
}

// EventVersion 实现DomainEvent接口
func (e *BaseDomainEvent) EventVersion() int {
	return e.eventVersion
}

// OccurredAt 实现DomainEvent接口
func (e *BaseDomainEvent) OccurredAt() time.Time {
	return e.occurredAt
}

// EventData 实现DomainEvent接口
func (e *BaseDomainEvent) EventData() interface{} {
	return e.eventData
}

// Metadata 实现DomainEvent接口
func (e *BaseDomainEvent) Metadata() map[string]interface{} {
	return e.metadata
}

// SetMetadata 设置元数据
func (e *BaseDomainEvent) SetMetadata(key string, value interface{}) {
	if e.metadata == nil {
		e.metadata = make(map[string]interface{})
	}
	e.metadata[key] = value
}

// GetMetadata 获取元数据
func (e *BaseDomainEvent) GetMetadata(key string) (interface{}, bool) {
	if e.metadata == nil {
		return nil, false
	}
	value, exists := e.metadata[key]
	return value, exists
}

// EventEnvelope 事件信封
// 用于事件的传输和序列化
type EventEnvelope struct {
	EventID       string                 `json:"event_id"`
	EventType     string                 `json:"event_type"`
	AggregateID   string                 `json:"aggregate_id"`
	AggregateType string                 `json:"aggregate_type"`
	EventVersion  int                    `json:"event_version"`
	OccurredAt    time.Time              `json:"occurred_at"`
	EventData     interface{}            `json:"event_data"`
	Metadata      map[string]interface{} `json:"metadata"`
}

// ToEnvelope 将领域事件转换为事件信封
func ToEnvelope(event DomainEvent) *EventEnvelope {
	return &EventEnvelope{
		EventID:       event.EventID(),
		EventType:     event.EventType(),
		AggregateID:   event.AggregateID(),
		AggregateType: event.AggregateType(),
		EventVersion:  event.EventVersion(),
		OccurredAt:    event.OccurredAt(),
		EventData:     event.EventData(),
		Metadata:      event.Metadata(),
	}
}

// FromEnvelope 从事件信封创建基础领域事件
func FromEnvelope(envelope *EventEnvelope) *BaseDomainEvent {
	event := &BaseDomainEvent{
		eventID:       envelope.EventID,
		eventType:     envelope.EventType,
		aggregateID:   envelope.AggregateID,
		aggregateType: envelope.AggregateType,
		eventVersion:  envelope.EventVersion,
		occurredAt:    envelope.OccurredAt,
		eventData:     envelope.EventData,
		metadata:      envelope.Metadata,
	}

	if event.metadata == nil {
		event.metadata = make(map[string]interface{})
	}

	return event
}

// EventRegistry 事件注册表
// 用于事件类型的注册和反序列化
type EventRegistry struct {
	eventTypes map[string]func() DomainEvent
}

// NewEventRegistry 创建事件注册表
func NewEventRegistry() *EventRegistry {
	return &EventRegistry{
		eventTypes: make(map[string]func() DomainEvent),
	}
}

// Register 注册事件类型
func (r *EventRegistry) Register(eventType string, factory func() DomainEvent) {
	r.eventTypes[eventType] = factory
}

// Create 根据事件类型创建事件实例
func (r *EventRegistry) Create(eventType string) (DomainEvent, bool) {
	factory, exists := r.eventTypes[eventType]
	if !exists {
		return nil, false
	}
	return factory(), true
}

// GetRegisteredTypes 获取所有注册的事件类型
func (r *EventRegistry) GetRegisteredTypes() []string {
	types := make([]string, 0, len(r.eventTypes))
	for eventType := range r.eventTypes {
		types = append(types, eventType)
	}
	return types
}

// 全局事件注册表
var GlobalEventRegistry = NewEventRegistry()
