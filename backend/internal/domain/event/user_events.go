package event

import (
	"time"
)

// 用户聚合事件类型常量
const (
	UserCreatedEventType     = "user.created"
	UserActivatedEventType   = "user.activated"
	UserDeactivatedEventType = "user.deactivated"
	UserUpdatedEventType     = "user.updated"
	UserDeletedEventType     = "user.deleted"
	
	// 用户认证相关事件
	UserPasswordChangedEventType = "user.password_changed"
	UserEmailVerifiedEventType   = "user.email_verified"
	UserPhoneVerifiedEventType   = "user.phone_verified"
	
	// 用户资料相关事件
	UserProfileUpdatedEventType = "user.profile_updated"
	UserContactUpdatedEventType = "user.contact_updated"
	UserPreferencesUpdatedEventType = "user.preferences_updated"
)

// 聚合类型常量
const (
	UserAggregateType = "User"
)

// ==================== 用户创建事件 ====================

// UserCreatedEventData 用户创建事件数据
type UserCreatedEventData struct {
	UserID    string    `json:"user_id"`
	TenantID  string    `json:"tenant_id"`
	Username  string    `json:"username"`
	Email     string    `json:"email"`
	Phone     string    `json:"phone"`
	FirstName string    `json:"first_name"`
	LastName  string    `json:"last_name"`
	Status    string    `json:"status"`
	CreatedAt time.Time `json:"created_at"`
}

// UserCreatedEvent 用户创建事件
type UserCreatedEvent struct {
	*BaseDomainEvent
}

// NewUserCreatedEvent 创建用户创建事件
func NewUserCreatedEvent(userID string, data *UserCreatedEventData) *UserCreatedEvent {
	baseEvent := NewBaseDomainEvent(
		UserCreatedEventType,
		userID,
		UserAggregateType,
		1, // 创建事件版本为1
		data,
	)
	
	return &UserCreatedEvent{
		BaseDomainEvent: baseEvent,
	}
}

// ==================== 用户激活事件 ====================

// UserActivatedEventData 用户激活事件数据
type UserActivatedEventData struct {
	UserID      string    `json:"user_id"`
	ActivatedBy string    `json:"activated_by"`
	ActivatedAt time.Time `json:"activated_at"`
	Reason      string    `json:"reason,omitempty"`
}

// UserActivatedEvent 用户激活事件
type UserActivatedEvent struct {
	*BaseDomainEvent
}

// NewUserActivatedEvent 创建用户激活事件
func NewUserActivatedEvent(userID string, data *UserActivatedEventData) *UserActivatedEvent {
	baseEvent := NewBaseDomainEvent(
		UserActivatedEventType,
		userID,
		UserAggregateType,
		1,
		data,
	)
	
	return &UserActivatedEvent{
		BaseDomainEvent: baseEvent,
	}
}

// ==================== 用户停用事件 ====================

// UserDeactivatedEventData 用户停用事件数据
type UserDeactivatedEventData struct {
	UserID        string    `json:"user_id"`
	DeactivatedBy string    `json:"deactivated_by"`
	DeactivatedAt time.Time `json:"deactivated_at"`
	Reason        string    `json:"reason"`
}

// UserDeactivatedEvent 用户停用事件
type UserDeactivatedEvent struct {
	*BaseDomainEvent
}

// NewUserDeactivatedEvent 创建用户停用事件
func NewUserDeactivatedEvent(userID string, data *UserDeactivatedEventData) *UserDeactivatedEvent {
	baseEvent := NewBaseDomainEvent(
		UserDeactivatedEventType,
		userID,
		UserAggregateType,
		1,
		data,
	)
	
	return &UserDeactivatedEvent{
		BaseDomainEvent: baseEvent,
	}
}

// ==================== 用户更新事件 ====================

// UserUpdatedEventData 用户更新事件数据
type UserUpdatedEventData struct {
	UserID    string                 `json:"user_id"`
	UpdatedBy string                 `json:"updated_by"`
	UpdatedAt time.Time              `json:"updated_at"`
	Changes   map[string]interface{} `json:"changes"` // 记录具体的变更字段
}

// UserUpdatedEvent 用户更新事件
type UserUpdatedEvent struct {
	*BaseDomainEvent
}

// NewUserUpdatedEvent 创建用户更新事件
func NewUserUpdatedEvent(userID string, data *UserUpdatedEventData) *UserUpdatedEvent {
	baseEvent := NewBaseDomainEvent(
		UserUpdatedEventType,
		userID,
		UserAggregateType,
		1,
		data,
	)
	
	return &UserUpdatedEvent{
		BaseDomainEvent: baseEvent,
	}
}

// ==================== 用户资料更新事件 ====================

// UserProfileUpdatedEventData 用户资料更新事件数据
type UserProfileUpdatedEventData struct {
	UserID    string                 `json:"user_id"`
	UpdatedBy string                 `json:"updated_by"`
	UpdatedAt time.Time              `json:"updated_at"`
	Profile   map[string]interface{} `json:"profile"` // 新的资料信息
	Changes   map[string]interface{} `json:"changes"` // 变更的字段
}

// UserProfileUpdatedEvent 用户资料更新事件
type UserProfileUpdatedEvent struct {
	*BaseDomainEvent
}

// NewUserProfileUpdatedEvent 创建用户资料更新事件
func NewUserProfileUpdatedEvent(userID string, data *UserProfileUpdatedEventData) *UserProfileUpdatedEvent {
	baseEvent := NewBaseDomainEvent(
		UserProfileUpdatedEventType,
		userID,
		UserAggregateType,
		1,
		data,
	)
	
	return &UserProfileUpdatedEvent{
		BaseDomainEvent: baseEvent,
	}
}

// ==================== 用户联系信息更新事件 ====================

// UserContactUpdatedEventData 用户联系信息更新事件数据
type UserContactUpdatedEventData struct {
	UserID      string                 `json:"user_id"`
	UpdatedBy   string                 `json:"updated_by"`
	UpdatedAt   time.Time              `json:"updated_at"`
	ContactInfo map[string]interface{} `json:"contact_info"` // 新的联系信息
	Changes     map[string]interface{} `json:"changes"`      // 变更的字段
}

// UserContactUpdatedEvent 用户联系信息更新事件
type UserContactUpdatedEvent struct {
	*BaseDomainEvent
}

// NewUserContactUpdatedEvent 创建用户联系信息更新事件
func NewUserContactUpdatedEvent(userID string, data *UserContactUpdatedEventData) *UserContactUpdatedEvent {
	baseEvent := NewBaseDomainEvent(
		UserContactUpdatedEventType,
		userID,
		UserAggregateType,
		1,
		data,
	)
	
	return &UserContactUpdatedEvent{
		BaseDomainEvent: baseEvent,
	}
}

// ==================== 用户偏好设置更新事件 ====================

// UserPreferencesUpdatedEventData 用户偏好设置更新事件数据
type UserPreferencesUpdatedEventData struct {
	UserID      string                 `json:"user_id"`
	UpdatedBy   string                 `json:"updated_by"`
	UpdatedAt   time.Time              `json:"updated_at"`
	Preferences map[string]interface{} `json:"preferences"` // 新的偏好设置
	Changes     map[string]interface{} `json:"changes"`     // 变更的字段
}

// UserPreferencesUpdatedEvent 用户偏好设置更新事件
type UserPreferencesUpdatedEvent struct {
	*BaseDomainEvent
}

// NewUserPreferencesUpdatedEvent 创建用户偏好设置更新事件
func NewUserPreferencesUpdatedEvent(userID string, data *UserPreferencesUpdatedEventData) *UserPreferencesUpdatedEvent {
	baseEvent := NewBaseDomainEvent(
		UserPreferencesUpdatedEventType,
		userID,
		UserAggregateType,
		1,
		data,
	)
	
	return &UserPreferencesUpdatedEvent{
		BaseDomainEvent: baseEvent,
	}
}

// ==================== 事件注册 ====================

// RegisterUserEvents 注册用户相关事件到全局注册表
func RegisterUserEvents() {
	GlobalEventRegistry.Register(UserCreatedEventType, func() DomainEvent {
		return &UserCreatedEvent{}
	})
	
	GlobalEventRegistry.Register(UserActivatedEventType, func() DomainEvent {
		return &UserActivatedEvent{}
	})
	
	GlobalEventRegistry.Register(UserDeactivatedEventType, func() DomainEvent {
		return &UserDeactivatedEvent{}
	})
	
	GlobalEventRegistry.Register(UserUpdatedEventType, func() DomainEvent {
		return &UserUpdatedEvent{}
	})
	
	GlobalEventRegistry.Register(UserProfileUpdatedEventType, func() DomainEvent {
		return &UserProfileUpdatedEvent{}
	})
	
	GlobalEventRegistry.Register(UserContactUpdatedEventType, func() DomainEvent {
		return &UserContactUpdatedEvent{}
	})
	
	GlobalEventRegistry.Register(UserPreferencesUpdatedEventType, func() DomainEvent {
		return &UserPreferencesUpdatedEvent{}
	})
}

// init 自动注册用户事件
func init() {
	RegisterUserEvents()
}
