package repository

import (
	"context"
	"time"

	"backend/internal/domain/auth/entity"

	"gorm.io/gorm"
)

// TokenRepository Token存储仓储接口
type TokenRepository interface {
	// === 基础CRUD操作 ===
	Save(ctx context.Context, token *entity.TokenStorage) error
	Update(ctx context.Context, token *entity.TokenStorage) error
	Delete(ctx context.Context, tokenID string) error
	SaveWithTx(ctx context.Context, tx *gorm.DB, token *entity.TokenStorage) error

	// === 查询操作 ===
	FindByTokenID(ctx context.Context, tokenID string) (*entity.TokenStorage, error)
	FindByTokenHash(ctx context.Context, tokenHash string) (*entity.TokenStorage, error)
	FindByUserID(ctx context.Context, userID string, tokenType entity.TokenType) ([]*entity.TokenStorage, error)
	FindBySessionID(ctx context.Context, sessionID string) ([]*entity.TokenStorage, error)
	FindByDeviceID(ctx context.Context, deviceID string) ([]*entity.TokenStorage, error)

	// === 批量操作 ===
	BatchCreate(ctx context.Context, tokens []*entity.TokenStorage) error
	BatchUpdateStatus(ctx context.Context, tokenIDs []string, status entity.TokenStatus) error
	BatchDelete(ctx context.Context, tokenIDs []string) error

	// === 状态管理 ===
	UpdateStatus(ctx context.Context, tokenID string, status entity.TokenStatus) error
	UpdateLastUsed(ctx context.Context, tokenID string) error
	RevokeTokensByUser(ctx context.Context, userID string, tokenType entity.TokenType) error
	RevokeTokensBySession(ctx context.Context, sessionID string) error
	RevokeTokensByDevice(ctx context.Context, deviceID string) error

	// === 过期清理 ===
	DeleteExpiredTokens(ctx context.Context, before time.Time) (int64, error)
	FindExpiredTokens(ctx context.Context, before time.Time, limit int) ([]*entity.TokenStorage, error)

	// === 统计查询 ===
	CountByUser(ctx context.Context, userID string, tokenType entity.TokenType) (int64, error)
	CountByUserAndStatus(ctx context.Context, userID string, tokenType entity.TokenType, status entity.TokenStatus) (int64, error)
	CountActiveTokensByDevice(ctx context.Context, deviceID string) (int64, error)

	// === 验证操作 ===
	ExistsByTokenID(ctx context.Context, tokenID string) (bool, error)
	IsTokenValid(ctx context.Context, tokenID string) (bool, error)
}

// SessionRepository 用户会话仓储接口
type SessionRepository interface {
	// === 基础CRUD操作 ===
	Save(ctx context.Context, session *entity.UserSession) error
	Update(ctx context.Context, session *entity.UserSession) error
	Delete(ctx context.Context, sessionID string) error
	SaveWithTx(ctx context.Context, tx *gorm.DB, session *entity.UserSession) error

	// === 查询操作 ===
	FindBySessionID(ctx context.Context, sessionID string) (*entity.UserSession, error)
	FindByUserID(ctx context.Context, userID string) ([]*entity.UserSession, error)
	FindByDeviceID(ctx context.Context, deviceID string) (*entity.UserSession, error)
	FindActiveSessionsByUser(ctx context.Context, userID string) ([]*entity.UserSession, error)

	// === 状态管理 ===
	UpdateStatus(ctx context.Context, sessionID string, status entity.SessionStatus) error
	UpdateLastActive(ctx context.Context, sessionID string) error
	TerminateSession(ctx context.Context, sessionID string) error
	TerminateSessionsByUser(ctx context.Context, userID string) error
	TerminateSessionsByDevice(ctx context.Context, deviceID string) error

	// === 批量操作 ===
	BatchCreate(ctx context.Context, sessions []*entity.UserSession) error
	BatchUpdateStatus(ctx context.Context, sessionIDs []string, status entity.SessionStatus) error
	BatchTerminate(ctx context.Context, sessionIDs []string) error

	// === 过期清理 ===
	DeleteExpiredSessions(ctx context.Context, before time.Time) (int64, error)
	FindExpiredSessions(ctx context.Context, before time.Time, limit int) ([]*entity.UserSession, error)
	CleanupInactiveSessions(ctx context.Context, inactiveBefore time.Time) (int64, error)

	// === 统计查询 ===
	CountByUser(ctx context.Context, userID string) (int64, error)
	CountActiveByUser(ctx context.Context, userID string) (int64, error)
	CountByUserAndStatus(ctx context.Context, userID string, status entity.SessionStatus) (int64, error)
	CountConcurrentSessions(ctx context.Context, userID string) (int64, error)

	// === 验证操作 ===
	ExistsBySessionID(ctx context.Context, sessionID string) (bool, error)
	IsSessionValid(ctx context.Context, sessionID string) (bool, error)
	CheckConcurrentLimit(ctx context.Context, userID string, limit int) (bool, error)
}

// PreAuthRepository 前置认证仓储接口
type PreAuthRepository interface {
	// === 基础CRUD操作 ===
	Save(ctx context.Context, context *entity.PreAuthContext) error
	Update(ctx context.Context, context *entity.PreAuthContext) error
	Delete(ctx context.Context, contextID string) error
	SaveWithTx(ctx context.Context, tx *gorm.DB, context *entity.PreAuthContext) error

	// === 查询操作 ===
	FindByContextID(ctx context.Context, contextID string) (*entity.PreAuthContext, error)
	FindByUserID(ctx context.Context, userID string) ([]*entity.PreAuthContext, error)
	FindActiveByUser(ctx context.Context, userID string) (*entity.PreAuthContext, error)
	FindByDeviceID(ctx context.Context, deviceID string) ([]*entity.PreAuthContext, error)

	// === 状态管理 ===
	CompleteStep(ctx context.Context, contextID string, step entity.AuthStep) error
	IncrementAttempt(ctx context.Context, contextID string) error
	MarkCompleted(ctx context.Context, contextID string) error
	UpdateVerificationData(ctx context.Context, contextID string, data string) error

	// === 批量操作 ===
	BatchCreate(ctx context.Context, contexts []*entity.PreAuthContext) error
	BatchDelete(ctx context.Context, contextIDs []string) error

	// === 过期清理 ===
	DeleteExpiredContexts(ctx context.Context, before time.Time) (int64, error)
	FindExpiredContexts(ctx context.Context, before time.Time, limit int) ([]*entity.PreAuthContext, error)
	CleanupCompletedContexts(ctx context.Context, completedBefore time.Time) (int64, error)

	// === 统计查询 ===
	CountByUser(ctx context.Context, userID string) (int64, error)
	CountActiveByUser(ctx context.Context, userID string) (int64, error)
	CountByStep(ctx context.Context, step entity.AuthStep) (int64, error)

	// === 验证操作 ===
	ExistsByContextID(ctx context.Context, contextID string) (bool, error)
	IsContextValid(ctx context.Context, contextID string) (bool, error)
	CanUserAttempt(ctx context.Context, userID string) (bool, error)
}

// BlacklistRepository Token黑名单仓储接口
type BlacklistRepository interface {
	// === 基础CRUD操作 ===
	Save(ctx context.Context, entry *entity.TokenBlacklistEntry) error
	Update(ctx context.Context, entry *entity.TokenBlacklistEntry) error
	Delete(ctx context.Context, tokenID string) error
	SaveWithTx(ctx context.Context, tx *gorm.DB, entry *entity.TokenBlacklistEntry) error

	// === 查询操作 ===
	FindByTokenID(ctx context.Context, tokenID string) (*entity.TokenBlacklistEntry, error)
	FindByTokenHash(ctx context.Context, tokenHash string) (*entity.TokenBlacklistEntry, error)
	FindByUserID(ctx context.Context, userID string) ([]*entity.TokenBlacklistEntry, error)
	FindBySessionID(ctx context.Context, sessionID string) ([]*entity.TokenBlacklistEntry, error)

	// === 批量操作 ===
	BatchCreate(ctx context.Context, entries []*entity.TokenBlacklistEntry) error
	BatchDelete(ctx context.Context, tokenIDs []string) error
	BlacklistTokensByUser(ctx context.Context, userID string, reason string) error
	BlacklistTokensBySession(ctx context.Context, sessionID string, reason string) error

	// === 过期清理 ===
	DeleteExpiredEntries(ctx context.Context, before time.Time) (int64, error)
	FindExpiredEntries(ctx context.Context, before time.Time, limit int) ([]*entity.TokenBlacklistEntry, error)

	// === 验证操作 ===
	IsTokenBlacklisted(ctx context.Context, tokenID string) (bool, error)
	IsTokenHashBlacklisted(ctx context.Context, tokenHash string) (bool, error)
	ExistsByTokenID(ctx context.Context, tokenID string) (bool, error)

	// === 统计查询 ===
	CountByUser(ctx context.Context, userID string) (int64, error)
	CountByTokenType(ctx context.Context, tokenType entity.TokenType) (int64, error)
	CountActive(ctx context.Context) (int64, error)
}

// TokenManager Token管理服务接口（高级操作）
type TokenManager interface {
	// === Token生命周期管理 ===
	CreateTokenPair(ctx context.Context, userID, tenantID, sessionID, deviceID string) (*TokenPair, error)
	RefreshTokenPair(ctx context.Context, refreshTokenID string) (*TokenPair, error)
	RevokeToken(ctx context.Context, tokenID string, reason string) error
	RevokeAllUserTokens(ctx context.Context, userID string, reason string) error
	RevokeSessionTokens(ctx context.Context, sessionID string, reason string) error

	// === 前置认证管理 ===
	CreatePreAuthToken(ctx context.Context, userID, tenantID string, requiredSteps []entity.AuthStep) (*PreAuthToken, error)
	ValidatePreAuthStep(ctx context.Context, contextID string, step entity.AuthStep, data interface{}) error
	CompletePreAuth(ctx context.Context, contextID string) (*TokenPair, error)

	// === 会话管理 ===
	CreateSession(ctx context.Context, userID, tenantID, deviceID string, metadata map[string]interface{}) (*entity.UserSession, error)
	ValidateSession(ctx context.Context, sessionID string) (*entity.UserSession, error)
	UpdateSessionActivity(ctx context.Context, sessionID string) error
	TerminateSession(ctx context.Context, sessionID string, reason string) error

	// === Token验证 ===
	ValidateToken(ctx context.Context, tokenID string) (*entity.TokenStorage, error)
	ValidateTokenHash(ctx context.Context, tokenHash string) (*entity.TokenStorage, error)
	CheckTokenPermissions(ctx context.Context, tokenID string, resource, action string) (bool, error)

	// === 安全控制 ===
	CheckConcurrentSessions(ctx context.Context, userID string) error
	CheckDeviceAuthorization(ctx context.Context, userID, deviceID string) error
	CheckRateLimit(ctx context.Context, userID string, action string) error

	// === 清理维护 ===
	CleanupExpiredTokens(ctx context.Context) (int64, error)
	CleanupExpiredSessions(ctx context.Context) (int64, error)
	CleanupExpiredBlacklist(ctx context.Context) (int64, error)
	CleanupExpiredPreAuth(ctx context.Context) (int64, error)
}

// === 数据传输对象 ===

// TokenPair Token对
type TokenPair struct {
	AccessToken  *entity.TokenStorage `json:"access_token"`
	RefreshToken *entity.TokenStorage `json:"refresh_token"`
	ExpiresIn    int64                `json:"expires_in"` // 访问令牌过期时间（秒）
	TokenType    string               `json:"token_type"` // 令牌类型
}

// PreAuthToken 前置认证Token
type PreAuthToken struct {
	ContextID     string               `json:"context_id"`
	Token         *entity.TokenStorage `json:"token"`
	RequiredSteps []entity.AuthStep    `json:"required_steps"`
	ExpiresIn     int64                `json:"expires_in"`
}

// TokenQuery Token查询条件
type TokenQuery struct {
	UserID    string             `json:"user_id,omitempty"`
	TenantID  string             `json:"tenant_id,omitempty"`
	SessionID string             `json:"session_id,omitempty"`
	DeviceID  string             `json:"device_id,omitempty"`
	TokenType entity.TokenType   `json:"token_type,omitempty"`
	Status    entity.TokenStatus `json:"status,omitempty"`
	StartTime *time.Time         `json:"start_time,omitempty"`
	EndTime   *time.Time         `json:"end_time,omitempty"`
	Limit     int                `json:"limit,omitempty"`
	Offset    int                `json:"offset,omitempty"`
}

// SessionQuery 会话查询条件
type SessionQuery struct {
	UserID     string               `json:"user_id,omitempty"`
	TenantID   string               `json:"tenant_id,omitempty"`
	DeviceID   string               `json:"device_id,omitempty"`
	Status     entity.SessionStatus `json:"status,omitempty"`
	DeviceType string               `json:"device_type,omitempty"`
	StartTime  *time.Time           `json:"start_time,omitempty"`
	EndTime    *time.Time           `json:"end_time,omitempty"`
	Limit      int                  `json:"limit,omitempty"`
	Offset     int                  `json:"offset,omitempty"`
}

// TokenStatistics Token统计信息
type TokenStatistics struct {
	TotalTokens    int64            `json:"total_tokens"`
	ActiveTokens   int64            `json:"active_tokens"`
	ExpiredTokens  int64            `json:"expired_tokens"`
	RevokedTokens  int64            `json:"revoked_tokens"`
	TokensByType   map[string]int64 `json:"tokens_by_type"`
	TokensByStatus map[string]int64 `json:"tokens_by_status"`
}

// SessionStatistics 会话统计信息
type SessionStatistics struct {
	TotalSessions    int64            `json:"total_sessions"`
	ActiveSessions   int64            `json:"active_sessions"`
	ExpiredSessions  int64            `json:"expired_sessions"`
	SessionsByStatus map[string]int64 `json:"sessions_by_status"`
	SessionsByDevice map[string]int64 `json:"sessions_by_device"`
}
