package repository

import (
	"context"

	"backend/internal/domain/auth/entity"
)

// UserAuthRepository defines the repository interface for user authentication data.
type UserAuthRepository interface {
	// --- Write Operations ---
	Save(ctx context.Context, auth *entity.UserAuth) error
	Update(ctx context.Context, auth *entity.UserAuth) error
	Delete(ctx context.Context, businessID string) error

	BatchCreate(ctx context.Context, auths []*entity.UserAuth) error

	// --- Read Operations ---

	// FindByIdentifier finds an auth entity by its identity type and unique identifier.
	// This is crucial for the global login process.
	FindByIdentifier(ctx context.Context, identityType, identifier string) (*entity.UserAuth, error)

	// FindByBusinessID finds an auth entity by its business ID.
	FindByBusinessID(ctx context.Context, businessID string) (*entity.UserAuth, error)

	// FindByUserBusinessID finds all auth entities associated with a user.
	FindByUserBusinessID(ctx context.Context, userBusinessID string) ([]*entity.UserAuth, error)

	// ExistsByIdentifier checks if an identifier of a certain type already exists.
	// Used during registration to prevent duplicates.
	ExistsByIdentifier(ctx context.Context, identityType, identifier string) (bool, error)
}
