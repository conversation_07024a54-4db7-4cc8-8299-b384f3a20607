package repository

import (
	"context"
	"time"
)

// TokenInfo Token信息结构
type TokenInfo struct {
	JTI       string    `json:"jti"`        // JWT唯一标识符（短UUID）
	Token     string    `json:"token"`      // 完整的JWT token字符串
	UserID    string    `json:"user_id"`    // 用户ID
	TenantID  string    `json:"tenant_id"`  // 租户ID
	DeviceID  string    `json:"device_id"`  // 设备ID
	TokenType string    `json:"token_type"` // Token类型：access, refresh, pre-auth
	CreatedAt time.Time `json:"created_at"` // 创建时间
	ExpiresAt time.Time `json:"expires_at"` // 过期时间
	IPAddress string    `json:"ip_address"` // IP地址
	UserAgent string    `json:"user_agent"` // 用户代理
	IsUsed    bool      `json:"is_used"`    // 是否已使用（用于一次性token）
}

// TokenCacheRepository Token缓存仓储接口
type TokenCacheRepository interface {
	// === Token信息管理 ===

	// SaveTokenInfo 保存Token信息到Redis
	SaveTokenInfo(ctx context.Context, tokenInfo *TokenInfo) error

	// GetTokenInfo 获取Token信息
	GetTokenInfo(ctx context.Context, jti string) (*TokenInfo, error)

	// DeleteTokenInfo 删除Token信息
	DeleteTokenInfo(ctx context.Context, jti string) error

	// === 黑名单管理 ===

	// AddToBlacklist 将Token添加到黑名单
	AddToBlacklist(ctx context.Context, jti string, expiresAt time.Time) error

	// IsTokenBlacklisted 检查Token是否在黑名单中
	IsTokenBlacklisted(ctx context.Context, jti string) (bool, error)

	// RemoveFromBlacklist 从黑名单中移除Token（用于测试或管理）
	RemoveFromBlacklist(ctx context.Context, jti string) error

	// === 用户Token管理 ===

	// AddUserToken 为用户添加Token记录
	AddUserToken(ctx context.Context, userID, tenantID, jti string) error

	// RemoveUserToken 移除用户的Token记录
	RemoveUserToken(ctx context.Context, userID, tenantID, jti string) error

	// GetUserTokens 获取用户的所有活跃Token
	GetUserTokens(ctx context.Context, userID, tenantID string) ([]string, error)

	// RevokeAllUserTokens 撤销用户的所有Token
	RevokeAllUserTokens(ctx context.Context, userID, tenantID string) error

	// === 设备Token管理 ===

	// GetDeviceTokens 获取设备的所有Token
	GetDeviceTokens(ctx context.Context, deviceID string) ([]string, error)

	// RevokeDeviceTokens 撤销设备的所有Token
	RevokeDeviceTokens(ctx context.Context, deviceID string) error

	// === 批量操作 ===

	// BatchRevoke 批量撤销Token
	BatchRevoke(ctx context.Context, jtis []string) error

	// CleanupExpiredTokens 清理过期的Token信息和黑名单记录
	CleanupExpiredTokens(ctx context.Context) error

	// === 统计信息 ===

	// GetActiveTokenCount 获取活跃Token数量
	GetActiveTokenCount(ctx context.Context, userID, tenantID string) (int64, error)

	// GetBlacklistSize 获取黑名单大小
	GetBlacklistSize(ctx context.Context) (int64, error)

	// === 预登录Token管理 ===

	// MarkTokenAsUsed 标记Token为已使用（用于一次性token）
	MarkTokenAsUsed(ctx context.Context, jti string) error

	// IsTokenUsed 检查Token是否已使用
	IsTokenUsed(ctx context.Context, jti string) (bool, error)

	// RevokeUserPreAuthTokens 撤销用户的所有预登录Token
	RevokeUserPreAuthTokens(ctx context.Context, userID string) error

	// ValidatePreAuthToken 验证预登录Token（检查设备和IP匹配）
	ValidatePreAuthToken(ctx context.Context, jti, deviceID, ipAddress string) error
}
