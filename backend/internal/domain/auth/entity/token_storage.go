package entity

import (
	"time"

	"backend/internal/shared/types"
)

// TokenType Token类型枚举
type TokenType string

const (
	TokenTypeAccess  TokenType = "access"   // 访问令牌
	TokenTypeRefresh TokenType = "refresh"  // 刷新令牌
	TokenTypePreAuth TokenType = "pre_auth" // 前置认证令牌
)

// TokenStatus Token状态枚举
type TokenStatus string

const (
	TokenStatusActive      TokenStatus = "active"      // 活跃状态
	TokenStatusExpired     TokenStatus = "expired"     // 已过期
	TokenStatusRevoked     TokenStatus = "revoked"     // 已撤销
	TokenStatusBlacklisted TokenStatus = "blacklisted" // 已拉黑
)

// AuthStep 认证步骤枚举
type AuthStep string

const (
	AuthStepCredentials AuthStep = "credentials" // 用户名密码验证
	AuthStepSMS         AuthStep = "sms"         // 短信验证
	AuthStepEmail       AuthStep = "email"       // 邮箱验证
	AuthStepTOTP        AuthStep = "totp"        // TOTP验证
	AuthStepCompleted   AuthStep = "completed"   // 认证完成
)

// SessionStatus 会话状态枚举
type SessionStatus string

const (
	SessionStatusActive     SessionStatus = "active"     // 活跃状态
	SessionStatusInactive   SessionStatus = "inactive"   // 非活跃状态
	SessionStatusExpired    SessionStatus = "expired"    // 已过期
	SessionStatusTerminated SessionStatus = "terminated" // 已终止
)

// TokenStorage Token存储实体
type TokenStorage struct {
	types.TenantScopedEntity

	// 基础信息
	TokenID   string      `gorm:"uniqueIndex;size:64"` // Token唯一标识
	TokenHash string      `gorm:"index;size:128"`      // Token哈希值
	TokenType TokenType   `gorm:"index;size:20"`       // Token类型
	Status    TokenStatus `gorm:"index;size:20"`       // Token状态

	// 关联信息
	UserID    string `gorm:"index;size:36"`  // 用户业务ID
	TenantID  string `gorm:"index;size:36"`  // 租户业务ID
	SessionID string `gorm:"index;size:64"`  // 会话ID
	DeviceID  string `gorm:"index;size:128"` // 设备ID

	// 时间信息
	IssuedAt   time.Time  `gorm:"index"` // 签发时间
	ExpiresAt  time.Time  `gorm:"index"` // 过期时间
	LastUsedAt *time.Time `gorm:"index"` // 最后使用时间

	// 元数据
	ClientIP  string `gorm:"size:45"`   // 客户端IP
	UserAgent string `gorm:"size:500"`  // 用户代理
	Scope     string `gorm:"size:200"`  // 权限范围
	Metadata  string `gorm:"type:text"` // 元数据JSON
}

// TableName 指定表名
func (TokenStorage) TableName() string {
	return "token_storages"
}

// NewTokenStorage 创建新的Token存储实体
func NewTokenStorage(tokenID, tokenHash string, tokenType TokenType, userID, tenantID, sessionID, deviceID string, expiresAt time.Time) *TokenStorage {
	return &TokenStorage{
		TenantScopedEntity: types.NewEmptyTenantScopedEntity(),
		TokenID:            tokenID,
		TokenHash:          tokenHash,
		TokenType:          tokenType,
		Status:             TokenStatusActive,
		UserID:             userID,
		TenantID:           tenantID,
		SessionID:          sessionID,
		DeviceID:           deviceID,
		IssuedAt:           time.Now(),
		ExpiresAt:          expiresAt,
	}
}

// IsValid 检查Token是否有效
func (t *TokenStorage) IsValid() bool {
	if t.Status != TokenStatusActive {
		return false
	}
	return time.Now().Before(t.ExpiresAt)
}

// IsExpired 检查Token是否过期
func (t *TokenStorage) IsExpired() bool {
	return time.Now().After(t.ExpiresAt)
}

// Revoke 撤销Token
func (t *TokenStorage) Revoke() {
	t.Status = TokenStatusRevoked
}

// Blacklist 拉黑Token
func (t *TokenStorage) Blacklist() {
	t.Status = TokenStatusBlacklisted
}

// UpdateLastUsed 更新最后使用时间
func (t *TokenStorage) UpdateLastUsed() {
	now := time.Now()
	t.LastUsedAt = &now
}

// SetMetadata 设置元数据
func (t *TokenStorage) SetMetadata(metadata string) {
	t.Metadata = metadata
}

// PreAuthContext 前置认证上下文实体
type PreAuthContext struct {
	types.TenantScopedEntity

	// 基础信息
	ContextID string `gorm:"uniqueIndex;size:64"` // 上下文唯一标识
	UserID    string `gorm:"index;size:36"`       // 用户业务ID
	TenantID  string `gorm:"index;size:36"`       // 租户业务ID

	// 认证状态
	CurrentStep    AuthStep `gorm:"index;size:20"` // 当前认证步骤
	CompletedSteps string   `gorm:"size:200"`      // 已完成步骤（JSON数组）
	RequiredSteps  string   `gorm:"size:200"`      // 需要完成的步骤（JSON数组）

	// 时间信息
	CreatedAt   time.Time  `gorm:"index"` // 创建时间
	ExpiresAt   time.Time  `gorm:"index"` // 过期时间
	CompletedAt *time.Time // 完成时间

	// 验证信息
	AttemptCount     int    `gorm:"default:0"` // 尝试次数
	MaxAttempts      int    `gorm:"default:5"` // 最大尝试次数
	VerificationData string `gorm:"type:text"` // 验证数据（JSON）

	// 元数据
	ClientIP  string `gorm:"size:45"`        // 客户端IP
	UserAgent string `gorm:"size:500"`       // 用户代理
	DeviceID  string `gorm:"index;size:128"` // 设备ID
	Metadata  string `gorm:"type:text"`      // 元数据JSON
}

// TableName 指定表名
func (PreAuthContext) TableName() string {
	return "pre_auth_contexts"
}

// NewPreAuthContext 创建新的前置认证上下文
func NewPreAuthContext(contextID, userID, tenantID string, requiredSteps []AuthStep, expiresAt time.Time) *PreAuthContext {
	return &PreAuthContext{
		TenantScopedEntity: types.NewEmptyTenantScopedEntity(),
		ContextID:          contextID,
		UserID:             userID,
		TenantID:           tenantID,
		CurrentStep:        AuthStepCredentials,
		RequiredSteps:      marshalSteps(requiredSteps),
		CreatedAt:          time.Now(),
		ExpiresAt:          expiresAt,
		MaxAttempts:        5,
	}
}

// IsValid 检查上下文是否有效
func (p *PreAuthContext) IsValid() bool {
	return time.Now().Before(p.ExpiresAt) && p.CompletedAt == nil
}

// IsExpired 检查上下文是否过期
func (p *PreAuthContext) IsExpired() bool {
	return time.Now().After(p.ExpiresAt)
}

// IsCompleted 检查认证是否完成
func (p *PreAuthContext) IsCompleted() bool {
	return p.CompletedAt != nil || p.CurrentStep == AuthStepCompleted
}

// CanAttempt 检查是否可以尝试
func (p *PreAuthContext) CanAttempt() bool {
	return p.AttemptCount < p.MaxAttempts
}

// IncrementAttempt 增加尝试次数
func (p *PreAuthContext) IncrementAttempt() {
	p.AttemptCount++
}

// CompleteStep 完成认证步骤
func (p *PreAuthContext) CompleteStep(step AuthStep) {
	completedSteps := unmarshalSteps(p.CompletedSteps)
	completedSteps = append(completedSteps, step)
	p.CompletedSteps = marshalSteps(completedSteps)

	// 检查是否所有步骤都完成
	requiredSteps := unmarshalSteps(p.RequiredSteps)
	if len(completedSteps) >= len(requiredSteps) {
		p.CurrentStep = AuthStepCompleted
		now := time.Now()
		p.CompletedAt = &now
	}
}

// UserSession 用户会话实体
type UserSession struct {
	types.TenantScopedEntity

	// 基础信息
	SessionID string        `gorm:"uniqueIndex;size:64"` // 会话唯一标识
	UserID    string        `gorm:"index;size:36"`       // 用户业务ID
	TenantID  string        `gorm:"index;size:36"`       // 租户业务ID
	DeviceID  string        `gorm:"index;size:128"`      // 设备ID
	Status    SessionStatus `gorm:"index;size:20"`       // 会话状态

	// 时间信息
	CreatedAt    time.Time  `gorm:"index"` // 创建时间
	ExpiresAt    time.Time  `gorm:"index"` // 过期时间
	LastActiveAt time.Time  `gorm:"index"` // 最后活跃时间
	TerminatedAt *time.Time // 终止时间

	// 设备信息
	ClientIP   string `gorm:"size:45"`  // 客户端IP
	UserAgent  string `gorm:"size:500"` // 用户代理
	DeviceType string `gorm:"size:50"`  // 设备类型
	DeviceName string `gorm:"size:200"` // 设备名称

	// 安全信息
	LoginMethod string `gorm:"size:50"`       // 登录方式
	IsSecure    bool   `gorm:"default:false"` // 是否安全连接

	// 元数据
	Metadata string `gorm:"type:text"` // 元数据JSON
}

// TableName 指定表名
func (UserSession) TableName() string {
	return "user_sessions"
}

// NewUserSession 创建新的用户会话
func NewUserSession(sessionID, userID, tenantID, deviceID string, expiresAt time.Time) *UserSession {
	now := time.Now()
	return &UserSession{
		TenantScopedEntity: types.NewEmptyTenantScopedEntity(),
		SessionID:          sessionID,
		UserID:             userID,
		TenantID:           tenantID,
		DeviceID:           deviceID,
		Status:             SessionStatusActive,
		CreatedAt:          now,
		ExpiresAt:          expiresAt,
		LastActiveAt:       now,
	}
}

// IsValid 检查会话是否有效
func (s *UserSession) IsValid() bool {
	return s.Status == SessionStatusActive && time.Now().Before(s.ExpiresAt)
}

// IsExpired 检查会话是否过期
func (s *UserSession) IsExpired() bool {
	return time.Now().After(s.ExpiresAt)
}

// UpdateLastActive 更新最后活跃时间
func (s *UserSession) UpdateLastActive() {
	s.LastActiveAt = time.Now()
}

// Terminate 终止会话
func (s *UserSession) Terminate() {
	s.Status = SessionStatusTerminated
	now := time.Now()
	s.TerminatedAt = &now
}

// Expire 使会话过期
func (s *UserSession) Expire() {
	s.Status = SessionStatusExpired
}

// TokenBlacklistEntry Token黑名单条目实体
type TokenBlacklistEntry struct {
	types.TenantScopedEntity

	// 基础信息
	TokenID   string    `gorm:"uniqueIndex;size:64"` // Token唯一标识
	TokenHash string    `gorm:"index;size:128"`      // Token哈希值
	TokenType TokenType `gorm:"index;size:20"`       // Token类型

	// 关联信息
	UserID    string `gorm:"index;size:36"` // 用户业务ID
	TenantID  string `gorm:"index;size:36"` // 租户业务ID
	SessionID string `gorm:"index;size:64"` // 会话ID

	// 拉黑信息
	BlacklistedAt time.Time `gorm:"index"`    // 拉黑时间
	ExpiresAt     time.Time `gorm:"index"`    // 过期时间（黑名单过期）
	Reason        string    `gorm:"size:200"` // 拉黑原因

	// 元数据
	Metadata string `gorm:"type:text"` // 元数据JSON
}

// TableName 指定表名
func (TokenBlacklistEntry) TableName() string {
	return "token_blacklist_entries"
}

// NewTokenBlacklistEntry 创建新的Token黑名单条目
func NewTokenBlacklistEntry(tokenID, tokenHash string, tokenType TokenType, userID, tenantID, sessionID string, expiresAt time.Time, reason string) *TokenBlacklistEntry {
	return &TokenBlacklistEntry{
		TenantScopedEntity: types.NewEmptyTenantScopedEntity(),
		TokenID:            tokenID,
		TokenHash:          tokenHash,
		TokenType:          tokenType,
		UserID:             userID,
		TenantID:           tenantID,
		SessionID:          sessionID,
		BlacklistedAt:      time.Now(),
		ExpiresAt:          expiresAt,
		Reason:             reason,
	}
}

// IsValid 检查黑名单条目是否有效
func (b *TokenBlacklistEntry) IsValid() bool {
	return time.Now().Before(b.ExpiresAt)
}

// IsExpired 检查黑名单条目是否过期
func (b *TokenBlacklistEntry) IsExpired() bool {
	return time.Now().After(b.ExpiresAt)
}

// 辅助函数
func marshalSteps(steps []AuthStep) string {
	// 简单的JSON序列化，实际项目中应使用json包
	result := "["
	for i, step := range steps {
		if i > 0 {
			result += ","
		}
		result += `"` + string(step) + `"`
	}
	result += "]"
	return result
}

func unmarshalSteps(data string) []AuthStep {
	// 简单的JSON反序列化，实际项目中应使用json包
	var steps []AuthStep
	// 这里简化处理，实际应该正确解析JSON
	return steps
}
