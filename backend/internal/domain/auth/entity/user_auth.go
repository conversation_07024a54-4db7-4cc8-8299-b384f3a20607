package entity

import (
	"errors"

	"backend/internal/shared/types"

	"golang.org/x/crypto/bcrypt"
)

// UserAuth 用户认证实体
// 认证信息可以跨租户复用（如邮箱登录）
type UserAuth struct {
	types.MultiTenantEntity

	UserBusinessID string `gorm:"index;size:36"`
	IdentityType   string `gorm:"size:20;uniqueIndex:uq_user_auths_identity"`
	Identifier     string `gorm:"size:255;uniqueIndex:uq_user_auths_identity"`
	Credential     string `gorm:"size:255"`
	Status         int    `gorm:"type:smallint;default:1"`
}

// TableName 指定表名
func (UserAuth) TableName() string {
	return "user_auths"
}

// NewUserAuth 创建一个新的用户认证实例
func NewUserAuth(userBusinessID, identityType, identifier, credential string) (*UserAuth, error) {
	auth := &UserAuth{
		MultiTenantEntity: types.NewEmptyMultiTenantEntity(),
		UserBusinessID:    userBusinessID,
		IdentityType:      identityType,
		Identifier:        identifier,
		Status:            1, // 默认为激活状态
	}

	if err := auth.SetCredential(credential); err != nil {
		return nil, err
	}
	return auth, nil
}

// SetCredential 加密并设置凭证
func (a *UserAuth) SetCredential(credential string) error {
	if credential == "" {
		return errors.New("凭证不能为空")
	}
	hashed, err := bcrypt.GenerateFromPassword([]byte(credential), bcrypt.DefaultCost)
	if err != nil {
		return err
	}
	a.Credential = string(hashed)
	return nil
}

// CheckCredential 验证凭证
func (a *UserAuth) CheckCredential(credential string) bool {
	err := bcrypt.CompareHashAndPassword([]byte(a.Credential), []byte(credential))
	return err == nil
}
