package service

import (
	"context"
	"time"

	"backend/internal/domain/auth/valueobject"
)

// AuthService 认证服务接口
type AuthService interface {
	// 认证相关
	Authenticate(ctx context.Context, tenantID, username, password, clientIP string) (*AuthResult, error)
	RefreshToken(ctx context.Context, refreshToken string) (*AuthResult, error)
	Logout(ctx context.Context, token string) error

	// Token管理
	GenerateTokens(ctx context.Context, userID, tenantID string, roles []string) (*TokenPair, error)
	ValidateToken(ctx context.Context, token string) (*valueobject.JWTClaims, error)
	RevokeToken(ctx context.Context, token string) error

	// 密码管理
	ChangePassword(ctx context.Context, userID, oldPassword, newPassword string) error
	ResetPassword(ctx context.Context, userID, newPassword string) error
}

// PermissionService 权限服务接口
type PermissionService interface {
	// 权限检查
	CheckPermission(ctx context.Context, userID, tenantID, resource, action string) (bool, error)
	CheckRolePermission(ctx context.Context, role, tenantID, resource, action string) (bool, error)

	// 角色管理
	GetUserRoles(ctx context.Context, userID, tenantID string) ([]string, error)
	AssignRole(ctx context.Context, userID, tenantID, role string) error
	RemoveRole(ctx context.Context, userID, tenantID, role string) error

	// 策略管理
	AddPolicy(ctx context.Context, role, tenantID, resource, action string) error
	RemovePolicy(ctx context.Context, role, tenantID, resource, action string) error
	GetPolicies(ctx context.Context, tenantID string) ([]PolicyRule, error)
}

// AuthResult 认证结果
type AuthResult struct {
	UserID       string    `json:"user_id"`
	TenantID     string    `json:"tenant_id"`
	Username     string    `json:"username"`
	Roles        []string  `json:"roles"`
	AccessToken  string    `json:"access_token"`
	RefreshToken string    `json:"refresh_token"`
	ExpiresAt    time.Time `json:"expires_at"`
	TokenType    string    `json:"token_type"`
}

// TokenPair Token对
type TokenPair struct {
	AccessToken  string    `json:"access_token"`
	RefreshToken string    `json:"refresh_token"`
	ExpiresAt    time.Time `json:"expires_at"`
	TokenType    string    `json:"token_type"`
}

// PolicyRule 策略规则
type PolicyRule struct {
	Role     string `json:"role"`
	TenantID string `json:"tenant_id"`
	Resource string `json:"resource"`
	Action   string `json:"action"`
}
