package service

import (
	"context"
	"time"

	"backend/internal/domain/auth/entity"
	"backend/internal/domain/auth/repository"
	"backend/internal/domain/auth/valueobject"
)

// TokenService Token管理服务接口
type TokenService interface {
	// === Token生命周期管理 ===
	GenerateTokenPair(ctx context.Context, userID, tenantID, sessionID, deviceID string, claims *valueobject.JWTClaims) (*repository.TokenPair, error)
	RefreshToken(ctx context.Context, refreshTokenID string) (*repository.TokenPair, error)
	RevokeToken(ctx context.Context, tokenID string, reason string) error
	RevokeAllUserTokens(ctx context.Context, userID string, excludeTokenID string, reason string) error
	RevokeSessionTokens(ctx context.Context, sessionID string, reason string) error
	RevokeDeviceTokens(ctx context.Context, deviceID string, reason string) error

	// === Token验证 ===
	ValidateToken(ctx context.Context, tokenID string) (*entity.TokenStorage, error)
	ValidateTokenHash(ctx context.Context, tokenHash string) (*entity.TokenStorage, error)
	ParseAndValidateJWT(ctx context.Context, tokenString string) (*valueobject.JWTClaims, error)
	CheckTokenBlacklist(ctx context.Context, tokenID string) error

	// === Token查询 ===
	GetUserTokens(ctx context.Context, userID string, tokenType entity.TokenType) ([]*entity.TokenStorage, error)
	GetSessionTokens(ctx context.Context, sessionID string) ([]*entity.TokenStorage, error)
	GetDeviceTokens(ctx context.Context, deviceID string) ([]*entity.TokenStorage, error)
	GetTokenStatistics(ctx context.Context, userID string) (*repository.TokenStatistics, error)

	// === Token维护 ===
	CleanupExpiredTokens(ctx context.Context) (int64, error)
	UpdateTokenLastUsed(ctx context.Context, tokenID string) error
	ExtendTokenExpiry(ctx context.Context, tokenID string, duration time.Duration) error
}

// SessionService 会话管理服务接口
type SessionService interface {
	// === 会话生命周期管理 ===
	CreateSession(ctx context.Context, userID, tenantID, deviceID string, clientInfo *ClientInfo) (*entity.UserSession, error)
	ValidateSession(ctx context.Context, sessionID string) (*entity.UserSession, error)
	UpdateSessionActivity(ctx context.Context, sessionID string, clientInfo *ClientInfo) error
	TerminateSession(ctx context.Context, sessionID string, reason string) error
	TerminateAllUserSessions(ctx context.Context, userID string, excludeSessionID string, reason string) error
	TerminateDeviceSessions(ctx context.Context, deviceID string, reason string) error

	// === 会话查询 ===
	GetUserSessions(ctx context.Context, userID string, activeOnly bool) ([]*entity.UserSession, error)
	GetDeviceSession(ctx context.Context, deviceID string) (*entity.UserSession, error)
	GetSessionStatistics(ctx context.Context, userID string) (*repository.SessionStatistics, error)

	// === 会话控制 ===
	CheckConcurrentSessionLimit(ctx context.Context, userID string) error
	CheckDeviceAuthorization(ctx context.Context, userID, deviceID string) error
	ForceSessionTermination(ctx context.Context, sessionID string, reason string) error

	// === 会话维护 ===
	CleanupExpiredSessions(ctx context.Context) (int64, error)
	CleanupInactiveSessions(ctx context.Context, inactiveDuration time.Duration) (int64, error)
	ExtendSessionExpiry(ctx context.Context, sessionID string, duration time.Duration) error
}

// PreAuthService 前置认证服务接口
type PreAuthService interface {
	// === 前置认证生命周期 ===
	InitiatePreAuth(ctx context.Context, userID, tenantID string, requiredSteps []entity.AuthStep, clientInfo *ClientInfo) (*repository.PreAuthToken, error)
	ValidatePreAuthContext(ctx context.Context, contextID string) (*entity.PreAuthContext, error)
	CompleteAuthStep(ctx context.Context, contextID string, step entity.AuthStep, verificationData interface{}) error
	FinalizePreAuth(ctx context.Context, contextID string) (*repository.TokenPair, error)

	// === 步骤验证 ===
	ValidateCredentialsStep(ctx context.Context, contextID string, username, password string) error
	ValidateSMSStep(ctx context.Context, contextID string, phoneNumber, code string) error
	ValidateEmailStep(ctx context.Context, contextID string, email, code string) error
	ValidateTOTPStep(ctx context.Context, contextID string, totpCode string) error

	// === 前置认证查询 ===
	GetUserPreAuthContext(ctx context.Context, userID string) (*entity.PreAuthContext, error)
	GetPreAuthProgress(ctx context.Context, contextID string) (*PreAuthProgress, error)
	CheckPreAuthExpiry(ctx context.Context, contextID string) error

	// === 前置认证控制 ===
	CheckAttemptLimit(ctx context.Context, contextID string) error
	IncrementAttemptCount(ctx context.Context, contextID string) error
	ResetPreAuthContext(ctx context.Context, contextID string) error

	// === 前置认证维护 ===
	CleanupExpiredPreAuth(ctx context.Context) (int64, error)
	CleanupCompletedPreAuth(ctx context.Context, completedBefore time.Duration) (int64, error)
}

// BlacklistService Token黑名单服务接口
type BlacklistService interface {
	// === 黑名单管理 ===
	AddToBlacklist(ctx context.Context, tokenID string, reason string, duration time.Duration) error
	RemoveFromBlacklist(ctx context.Context, tokenID string) error
	CheckBlacklist(ctx context.Context, tokenID string) (bool, error)
	CheckTokenHashBlacklist(ctx context.Context, tokenHash string) (bool, error)

	// === 批量黑名单操作 ===
	BlacklistUserTokens(ctx context.Context, userID string, reason string) error
	BlacklistSessionTokens(ctx context.Context, sessionID string, reason string) error
	BlacklistDeviceTokens(ctx context.Context, deviceID string, reason string) error

	// === 黑名单查询 ===
	GetUserBlacklistEntries(ctx context.Context, userID string) ([]*entity.TokenBlacklistEntry, error)
	GetBlacklistStatistics(ctx context.Context) (map[string]int64, error)

	// === 黑名单维护 ===
	CleanupExpiredBlacklist(ctx context.Context) (int64, error)
	PurgeBlacklistHistory(ctx context.Context, before time.Time) (int64, error)
}

// SecurityService 安全控制服务接口
type SecurityService interface {
	// === 访问控制 ===
	CheckTokenPermissions(ctx context.Context, tokenID string, resource, action string) (bool, error)
	ValidateTokenScope(ctx context.Context, tokenID string, requiredScope string) error
	CheckResourceAccess(ctx context.Context, userID, tenantID, resource string) error

	// === 限流控制 ===
	CheckRateLimit(ctx context.Context, userID string, action string) error
	CheckLoginAttempts(ctx context.Context, userID string) error
	IncrementLoginAttempts(ctx context.Context, userID string) error
	ResetLoginAttempts(ctx context.Context, userID string) error

	// === 设备管理 ===
	RegisterDevice(ctx context.Context, userID, deviceID string, deviceInfo *DeviceInfo) error
	AuthorizeDevice(ctx context.Context, userID, deviceID string) error
	RevokeDeviceAccess(ctx context.Context, userID, deviceID string) error
	GetUserDevices(ctx context.Context, userID string) ([]*DeviceInfo, error)

	// === 安全审计 ===
	LogSecurityEvent(ctx context.Context, event *SecurityEvent) error
	GetSecurityEvents(ctx context.Context, userID string, eventType string, limit int) ([]*SecurityEvent, error)
	DetectSuspiciousActivity(ctx context.Context, userID string) ([]*SecurityAlert, error)

	// === 安全策略 ===
	ApplySecurityPolicy(ctx context.Context, userID, tenantID string, policy *SecurityPolicy) error
	CheckPasswordPolicy(ctx context.Context, password string) error
	CheckSessionPolicy(ctx context.Context, sessionID string) error
}

// === 数据传输对象 ===

// ClientInfo 客户端信息
type ClientInfo struct {
	IP         string `json:"ip"`
	UserAgent  string `json:"user_agent"`
	DeviceID   string `json:"device_id"`
	DeviceType string `json:"device_type"`
	DeviceName string `json:"device_name"`
	IsSecure   bool   `json:"is_secure"`
}

// PreAuthProgress 前置认证进度
type PreAuthProgress struct {
	ContextID      string            `json:"context_id"`
	CurrentStep    entity.AuthStep   `json:"current_step"`
	CompletedSteps []entity.AuthStep `json:"completed_steps"`
	RequiredSteps  []entity.AuthStep `json:"required_steps"`
	RemainingSteps []entity.AuthStep `json:"remaining_steps"`
	Progress       float64           `json:"progress"` // 完成百分比
	ExpiresAt      time.Time         `json:"expires_at"`
	AttemptCount   int               `json:"attempt_count"`
	MaxAttempts    int               `json:"max_attempts"`
}

// DeviceInfo 设备信息
type DeviceInfo struct {
	DeviceID     string    `json:"device_id"`
	DeviceType   string    `json:"device_type"`
	DeviceName   string    `json:"device_name"`
	OS           string    `json:"os"`
	Browser      string    `json:"browser"`
	IsAuthorized bool      `json:"is_authorized"`
	FirstSeen    time.Time `json:"first_seen"`
	LastSeen     time.Time `json:"last_seen"`
	TrustLevel   int       `json:"trust_level"` // 信任级别 1-10
}

// SecurityEvent 安全事件
type SecurityEvent struct {
	EventID     string                 `json:"event_id"`
	UserID      string                 `json:"user_id"`
	TenantID    string                 `json:"tenant_id"`
	EventType   string                 `json:"event_type"` // login, logout, token_refresh, suspicious_activity等
	Severity    string                 `json:"severity"`   // low, medium, high, critical
	Description string                 `json:"description"`
	ClientInfo  *ClientInfo            `json:"client_info"`
	Metadata    map[string]interface{} `json:"metadata"`
	OccurredAt  time.Time              `json:"occurred_at"`
}

// SecurityAlert 安全警报
type SecurityAlert struct {
	AlertID     string                 `json:"alert_id"`
	UserID      string                 `json:"user_id"`
	TenantID    string                 `json:"tenant_id"`
	AlertType   string                 `json:"alert_type"` // multiple_failed_logins, unusual_location等
	Risk        string                 `json:"risk"`       // low, medium, high, critical
	Message     string                 `json:"message"`
	Evidence    []string               `json:"evidence"` // 相关证据
	Metadata    map[string]interface{} `json:"metadata"`
	TriggeredAt time.Time              `json:"triggered_at"`
	Status      string                 `json:"status"` // active, resolved, dismissed
}

// SecurityPolicy 安全策略
type SecurityPolicy struct {
	PolicyID              string        `json:"policy_id"`
	TenantID              string        `json:"tenant_id"`
	MaxConcurrentSessions int           `json:"max_concurrent_sessions"`
	SessionTimeout        time.Duration `json:"session_timeout"`
	TokenExpiry           time.Duration `json:"token_expiry"`
	RequireMFA            bool          `json:"require_mfa"`
	AllowedDeviceTypes    []string      `json:"allowed_device_types"`
	IPWhitelist           []string      `json:"ip_whitelist"`
	MaxLoginAttempts      int           `json:"max_login_attempts"`
	LockoutDuration       time.Duration `json:"lockout_duration"`
}

// TokenValidationResult Token验证结果
type TokenValidationResult struct {
	IsValid     bool                   `json:"is_valid"`
	Token       *entity.TokenStorage   `json:"token,omitempty"`
	Claims      *valueobject.JWTClaims `json:"claims,omitempty"`
	Session     *entity.UserSession    `json:"session,omitempty"`
	Error       string                 `json:"error,omitempty"`
	ErrorCode   string                 `json:"error_code,omitempty"`
	ExpiresAt   *time.Time             `json:"expires_at,omitempty"`
	Permissions []string               `json:"permissions,omitempty"`
}

// SessionValidationResult 会话验证结果
type SessionValidationResult struct {
	IsValid   bool                `json:"is_valid"`
	Session   *entity.UserSession `json:"session,omitempty"`
	Error     string              `json:"error,omitempty"`
	ErrorCode string              `json:"error_code,omitempty"`
	ExpiresAt *time.Time          `json:"expires_at,omitempty"`
}
