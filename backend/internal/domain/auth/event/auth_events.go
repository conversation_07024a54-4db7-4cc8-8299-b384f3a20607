package event

import (
	"time"
)

// ==================== 基础事件结构 ====================

// BaseEvent 基础事件结构
type BaseEvent struct {
	EventID     string                 `json:"event_id"`
	EventType   string                 `json:"event_type"`
	AggregateID string                 `json:"aggregate_id"`
	Version     int                    `json:"version"`
	Timestamp   time.Time              `json:"timestamp"`
	Metadata    map[string]interface{} `json:"metadata,omitempty"`
}

// GetEventID 获取事件ID
func (e BaseEvent) GetEventID() string {
	return e.EventID
}

// GetEventType 获取事件类型
func (e BaseEvent) GetEventType() string {
	return e.EventType
}

// GetAggregateID 获取聚合ID
func (e BaseEvent) GetAggregateID() string {
	return e.AggregateID
}

// GetVersion 获取版本
func (e BaseEvent) GetVersion() int {
	return e.Version
}

// GetTimestamp 获取时间戳
func (e BaseEvent) GetTimestamp() time.Time {
	return e.Timestamp
}

// GetMetadata 获取元数据
func (e BaseEvent) GetMetadata() map[string]interface{} {
	return e.Metadata
}

// ==================== 用户认证事件 ====================

// UserLoggedIn 用户登录事件
type UserLoggedIn struct {
	BaseEvent
	UserID       string `json:"user_id"`
	TenantID     string `json:"tenant_id,omitempty"`
	SessionID    string `json:"session_id"`
	IP           string `json:"ip_address"`
	UserAgent    string `json:"user_agent"`
	LoginMethod  string `json:"login_method"` // password, oauth, sso
	DeviceID     string `json:"device_id,omitempty"`
	DeviceInfo   string `json:"device_info,omitempty"`
	Location     string `json:"location,omitempty"`
	IsSuccessful bool   `json:"is_successful"`
}

// NewUserLoggedIn 创建用户登录事件
func NewUserLoggedIn(eventID, userID, tenantID, sessionID, ip, userAgent, loginMethod string) *UserLoggedIn {
	return &UserLoggedIn{
		BaseEvent: BaseEvent{
			EventID:     eventID,
			EventType:   "user.logged_in",
			AggregateID: userID,
			Version:     1,
			Timestamp:   time.Now(),
			Metadata:    make(map[string]interface{}),
		},
		UserID:       userID,
		TenantID:     tenantID,
		SessionID:    sessionID,
		IP:           ip,
		UserAgent:    userAgent,
		LoginMethod:  loginMethod,
		IsSuccessful: true,
	}
}

// UserLoggedOut 用户登出事件
type UserLoggedOut struct {
	BaseEvent
	UserID    string `json:"user_id"`
	TenantID  string `json:"tenant_id,omitempty"`
	SessionID string `json:"session_id"`
	Reason    string `json:"reason"` // manual, timeout, forced
	IP        string `json:"ip_address"`
}

// NewUserLoggedOut 创建用户登出事件
func NewUserLoggedOut(eventID, userID, tenantID, sessionID, reason, ip string) *UserLoggedOut {
	return &UserLoggedOut{
		BaseEvent: BaseEvent{
			EventID:     eventID,
			EventType:   "user.logged_out",
			AggregateID: userID,
			Version:     1,
			Timestamp:   time.Now(),
			Metadata:    make(map[string]interface{}),
		},
		UserID:    userID,
		TenantID:  tenantID,
		SessionID: sessionID,
		Reason:    reason,
		IP:        ip,
	}
}

// AuthenticationFailed 认证失败事件
type AuthenticationFailed struct {
	BaseEvent
	Identifier   string `json:"identifier"`    // 用户标识符（用户名、邮箱、手机号）
	IdentityType string `json:"identity_type"` // username, email, phone
	Reason       string `json:"reason"`        // invalid_credentials, account_locked, account_disabled
	IP           string `json:"ip_address"`
	UserAgent    string `json:"user_agent"`
	AttemptCount int    `json:"attempt_count"` // 连续失败次数
	TenantID     string `json:"tenant_id,omitempty"`
}

// NewAuthenticationFailed 创建认证失败事件
func NewAuthenticationFailed(eventID, identifier, identityType, reason, ip, userAgent string, attemptCount int) *AuthenticationFailed {
	return &AuthenticationFailed{
		BaseEvent: BaseEvent{
			EventID:     eventID,
			EventType:   "authentication.failed",
			AggregateID: identifier,
			Version:     1,
			Timestamp:   time.Now(),
			Metadata:    make(map[string]interface{}),
		},
		Identifier:   identifier,
		IdentityType: identityType,
		Reason:       reason,
		IP:           ip,
		UserAgent:    userAgent,
		AttemptCount: attemptCount,
	}
}

// ==================== 会话管理事件 ====================

// SessionCreated 会话创建事件
type SessionCreated struct {
	BaseEvent
	UserID       string    `json:"user_id"`
	TenantID     string    `json:"tenant_id,omitempty"`
	SessionID    string    `json:"session_id"`
	SessionToken string    `json:"session_token"`
	ExpiresAt    time.Time `json:"expires_at"`
	IP           string    `json:"ip_address"`
	UserAgent    string    `json:"user_agent"`
	DeviceID     string    `json:"device_id,omitempty"`
}

// NewSessionCreated 创建会话创建事件
func NewSessionCreated(eventID, userID, tenantID, sessionID, sessionToken, ip, userAgent string, expiresAt time.Time) *SessionCreated {
	return &SessionCreated{
		BaseEvent: BaseEvent{
			EventID:     eventID,
			EventType:   "session.created",
			AggregateID: sessionID,
			Version:     1,
			Timestamp:   time.Now(),
			Metadata:    make(map[string]interface{}),
		},
		UserID:       userID,
		TenantID:     tenantID,
		SessionID:    sessionID,
		SessionToken: sessionToken,
		ExpiresAt:    expiresAt,
		IP:           ip,
		UserAgent:    userAgent,
	}
}

// SessionExpired 会话过期事件
type SessionExpired struct {
	BaseEvent
	UserID    string `json:"user_id"`
	TenantID  string `json:"tenant_id,omitempty"`
	SessionID string `json:"session_id"`
	Reason    string `json:"reason"` // timeout, manual_revoke, security_policy
}

// NewSessionExpired 创建会话过期事件
func NewSessionExpired(eventID, userID, tenantID, sessionID, reason string) *SessionExpired {
	return &SessionExpired{
		BaseEvent: BaseEvent{
			EventID:     eventID,
			EventType:   "session.expired",
			AggregateID: sessionID,
			Version:     1,
			Timestamp:   time.Now(),
			Metadata:    make(map[string]interface{}),
		},
		UserID:    userID,
		TenantID:  tenantID,
		SessionID: sessionID,
		Reason:    reason,
	}
}

// SessionRefreshed 会话刷新事件
type SessionRefreshed struct {
	BaseEvent
	UserID       string    `json:"user_id"`
	TenantID     string    `json:"tenant_id,omitempty"`
	SessionID    string    `json:"session_id"`
	NewToken     string    `json:"new_token"`
	OldToken     string    `json:"old_token"`
	NewExpiresAt time.Time `json:"new_expires_at"`
	IP           string    `json:"ip_address"`
}

// NewSessionRefreshed 创建会话刷新事件
func NewSessionRefreshed(eventID, userID, tenantID, sessionID, newToken, oldToken, ip string, newExpiresAt time.Time) *SessionRefreshed {
	return &SessionRefreshed{
		BaseEvent: BaseEvent{
			EventID:     eventID,
			EventType:   "session.refreshed",
			AggregateID: sessionID,
			Version:     1,
			Timestamp:   time.Now(),
			Metadata:    make(map[string]interface{}),
		},
		UserID:       userID,
		TenantID:     tenantID,
		SessionID:    sessionID,
		NewToken:     newToken,
		OldToken:     oldToken,
		NewExpiresAt: newExpiresAt,
		IP:           ip,
	}
}

// ==================== 安全事件 ====================

// SuspiciousActivityDetected 可疑活动检测事件
type SuspiciousActivityDetected struct {
	BaseEvent
	UserID       string `json:"user_id,omitempty"`
	TenantID     string `json:"tenant_id,omitempty"`
	ActivityType string `json:"activity_type"` // multiple_failed_logins, unusual_location, brute_force
	Description  string `json:"description"`
	IP           string `json:"ip_address"`
	UserAgent    string `json:"user_agent"`
	RiskLevel    string `json:"risk_level"` // low, medium, high, critical
	ActionTaken  string `json:"action_taken,omitempty"`
}

// NewSuspiciousActivityDetected 创建可疑活动检测事件
func NewSuspiciousActivityDetected(eventID, userID, tenantID, activityType, description, ip, userAgent, riskLevel string) *SuspiciousActivityDetected {
	return &SuspiciousActivityDetected{
		BaseEvent: BaseEvent{
			EventID:     eventID,
			EventType:   "security.suspicious_activity_detected",
			AggregateID: userID,
			Version:     1,
			Timestamp:   time.Now(),
			Metadata:    make(map[string]interface{}),
		},
		UserID:       userID,
		TenantID:     tenantID,
		ActivityType: activityType,
		Description:  description,
		IP:           ip,
		UserAgent:    userAgent,
		RiskLevel:    riskLevel,
	}
}

// AccountLocked 账户锁定事件
type AccountLocked struct {
	BaseEvent
	UserID       string    `json:"user_id"`
	TenantID     string    `json:"tenant_id,omitempty"`
	Reason       string    `json:"reason"`       // too_many_failed_attempts, security_policy, manual
	LockedUntil  time.Time `json:"locked_until"` // 锁定到什么时候
	LockedBy     string    `json:"locked_by,omitempty"`
	FailedCount  int       `json:"failed_count,omitempty"`
	LastFailedIP string    `json:"last_failed_ip,omitempty"`
}

// NewAccountLocked 创建账户锁定事件
func NewAccountLocked(eventID, userID, tenantID, reason string, lockedUntil time.Time, failedCount int) *AccountLocked {
	return &AccountLocked{
		BaseEvent: BaseEvent{
			EventID:     eventID,
			EventType:   "security.account_locked",
			AggregateID: userID,
			Version:     1,
			Timestamp:   time.Now(),
			Metadata:    make(map[string]interface{}),
		},
		UserID:      userID,
		TenantID:    tenantID,
		Reason:      reason,
		LockedUntil: lockedUntil,
		FailedCount: failedCount,
	}
}

// AccountUnlocked 账户解锁事件
type AccountUnlocked struct {
	BaseEvent
	UserID     string `json:"user_id"`
	TenantID   string `json:"tenant_id,omitempty"`
	UnlockedBy string `json:"unlocked_by"` // system, admin, user
	Reason     string `json:"reason"`      // time_expired, manual_unlock, password_reset
}

// NewAccountUnlocked 创建账户解锁事件
func NewAccountUnlocked(eventID, userID, tenantID, unlockedBy, reason string) *AccountUnlocked {
	return &AccountUnlocked{
		BaseEvent: BaseEvent{
			EventID:     eventID,
			EventType:   "security.account_unlocked",
			AggregateID: userID,
			Version:     1,
			Timestamp:   time.Now(),
			Metadata:    make(map[string]interface{}),
		},
		UserID:     userID,
		TenantID:   tenantID,
		UnlockedBy: unlockedBy,
		Reason:     reason,
	}
}

// ==================== 密码管理事件 ====================

// PasswordChanged 密码更改事件
type PasswordChanged struct {
	BaseEvent
	UserID    string `json:"user_id"`
	TenantID  string `json:"tenant_id,omitempty"`
	ChangedBy string `json:"changed_by"` // user, admin, system
	Method    string `json:"method"`     // manual, reset, forced
	IP        string `json:"ip_address"`
	UserAgent string `json:"user_agent"`
}

// NewPasswordChanged 创建密码更改事件
func NewPasswordChanged(eventID, userID, tenantID, changedBy, method, ip, userAgent string) *PasswordChanged {
	return &PasswordChanged{
		BaseEvent: BaseEvent{
			EventID:     eventID,
			EventType:   "security.password_changed",
			AggregateID: userID,
			Version:     1,
			Timestamp:   time.Now(),
			Metadata:    make(map[string]interface{}),
		},
		UserID:    userID,
		TenantID:  tenantID,
		ChangedBy: changedBy,
		Method:    method,
		IP:        ip,
		UserAgent: userAgent,
	}
}

// PasswordResetRequested 密码重置请求事件
type PasswordResetRequested struct {
	BaseEvent
	UserID     string    `json:"user_id"`
	TenantID   string    `json:"tenant_id,omitempty"`
	Email      string    `json:"email"`
	ResetToken string    `json:"reset_token"`
	ExpiresAt  time.Time `json:"expires_at"`
	IP         string    `json:"ip_address"`
	UserAgent  string    `json:"user_agent"`
}

// NewPasswordResetRequested 创建密码重置请求事件
func NewPasswordResetRequested(eventID, userID, tenantID, email, resetToken, ip, userAgent string, expiresAt time.Time) *PasswordResetRequested {
	return &PasswordResetRequested{
		BaseEvent: BaseEvent{
			EventID:     eventID,
			EventType:   "security.password_reset_requested",
			AggregateID: userID,
			Version:     1,
			Timestamp:   time.Now(),
			Metadata:    make(map[string]interface{}),
		},
		UserID:     userID,
		TenantID:   tenantID,
		Email:      email,
		ResetToken: resetToken,
		ExpiresAt:  expiresAt,
		IP:         ip,
		UserAgent:  userAgent,
	}
}

// ==================== 事件工厂 ====================

// SecurityEventFactory 安全事件工厂
type SecurityEventFactory struct{}

// NewSecurityEventFactory 创建安全事件工厂
func NewSecurityEventFactory() *SecurityEventFactory {
	return &SecurityEventFactory{}
}

// CreateUserLoggedInEvent 创建用户登录事件
func (f *SecurityEventFactory) CreateUserLoggedInEvent(userID, tenantID, sessionID, ip, userAgent, loginMethod string) *UserLoggedIn {
	return NewUserLoggedIn(f.generateEventID(), userID, tenantID, sessionID, ip, userAgent, loginMethod)
}

// CreateUserLoggedOutEvent 创建用户登出事件
func (f *SecurityEventFactory) CreateUserLoggedOutEvent(userID, tenantID, sessionID, reason, ip string) *UserLoggedOut {
	return NewUserLoggedOut(f.generateEventID(), userID, tenantID, sessionID, reason, ip)
}

// CreateAuthenticationFailedEvent 创建认证失败事件
func (f *SecurityEventFactory) CreateAuthenticationFailedEvent(identifier, identityType, reason, ip, userAgent string, attemptCount int) *AuthenticationFailed {
	return NewAuthenticationFailed(f.generateEventID(), identifier, identityType, reason, ip, userAgent, attemptCount)
}

// CreateSessionCreatedEvent 创建会话创建事件
func (f *SecurityEventFactory) CreateSessionCreatedEvent(userID, tenantID, sessionID, sessionToken, ip, userAgent string, expiresAt time.Time) *SessionCreated {
	return NewSessionCreated(f.generateEventID(), userID, tenantID, sessionID, sessionToken, ip, userAgent, expiresAt)
}

// CreateSuspiciousActivityEvent 创建可疑活动事件
func (f *SecurityEventFactory) CreateSuspiciousActivityEvent(userID, tenantID, activityType, description, ip, userAgent, riskLevel string) *SuspiciousActivityDetected {
	return NewSuspiciousActivityDetected(f.generateEventID(), userID, tenantID, activityType, description, ip, userAgent, riskLevel)
}

// CreateAccountLockedEvent 创建账户锁定事件
func (f *SecurityEventFactory) CreateAccountLockedEvent(userID, tenantID, reason string, lockedUntil time.Time, failedCount int) *AccountLocked {
	return NewAccountLocked(f.generateEventID(), userID, tenantID, reason, lockedUntil, failedCount)
}

// CreatePasswordChangedEvent 创建密码更改事件
func (f *SecurityEventFactory) CreatePasswordChangedEvent(userID, tenantID, changedBy, method, ip, userAgent string) *PasswordChanged {
	return NewPasswordChanged(f.generateEventID(), userID, tenantID, changedBy, method, ip, userAgent)
}

// generateEventID 生成事件ID（简化实现，实际应该使用UUID生成器）
func (f *SecurityEventFactory) generateEventID() string {
	return time.Now().Format("**************") + "-" + "event"
}
