package valueobject

import (
	"time"

	"github.com/golang-jwt/jwt/v5"
)

// TokenType token类型枚举
type TokenType string

const (
	TokenTypeAccess  TokenType = "access"   // 访问令牌
	TokenTypePreAuth TokenType = "pre_auth" // 前置认证令牌
	TokenTypeRefresh TokenType = "refresh"  // 刷新令牌
)

// IsAccessToken 检查是否为访问令牌
func (t TokenType) IsAccessToken() bool {
	return t == TokenTypeAccess
}

// IsPreAuthToken 检查是否为前置认证令牌
func (t TokenType) IsPreAuthToken() bool {
	return t == TokenTypePreAuth
}

// IsRefreshToken 检查是否为刷新令牌
func (t TokenType) IsRefreshToken() bool {
	return t == TokenTypeRefresh
}

// String 返回字符串表示
func (t TokenType) String() string {
	return string(t)
}

// JWTClaims JWT声明值对象
type JWTClaims struct {
	UserID    string    `json:"user_id"`    // 用户业务ID
	TenantID  string    `json:"tenant_id"`  // 租户业务ID (pre_auth_token 中可为空)
	Roles     []string  `json:"roles"`      // 用户角色列表
	JTI       string    `json:"jti"`        // JWT唯一标识符，用于撤销管理
	TokenType TokenType `json:"token_type"` // Token类型
	jwt.RegisteredClaims
}

// NewJWTClaims 创建新的JWT声明
func NewJWTClaims(userID, tenantID string, roles []string, expiration time.Duration, jti string, tokenType TokenType) *JWTClaims {
	now := time.Now()
	return &JWTClaims{
		UserID:    userID,
		TenantID:  tenantID,
		Roles:     roles,
		JTI:       jti,
		TokenType: tokenType,
		RegisteredClaims: jwt.RegisteredClaims{
			ID:        jti,
			ExpiresAt: jwt.NewNumericDate(now.Add(expiration)),
			IssuedAt:  jwt.NewNumericDate(now),
			NotBefore: jwt.NewNumericDate(now),
			Issuer:    "9-wings-erp",
			Subject:   userID,
		},
	}
}

// NewAccessTokenClaims 创建访问令牌声明的便利方法
func NewAccessTokenClaims(userID, tenantID string, roles []string, expiration time.Duration, jti string) *JWTClaims {
	return NewJWTClaims(userID, tenantID, roles, expiration, jti, TokenTypeAccess)
}

// NewPreAuthTokenClaims 创建前置认证令牌声明的便利方法
func NewPreAuthTokenClaims(userID string, expiration time.Duration, jti string) *JWTClaims {
	return NewJWTClaims(userID, "", nil, expiration, jti, TokenTypePreAuth)
}

// NewRefreshTokenClaims 创建刷新令牌声明的便利方法
func NewRefreshTokenClaims(userID, tenantID string, expiration time.Duration, jti string) *JWTClaims {
	return NewJWTClaims(userID, tenantID, nil, expiration, jti, TokenTypeRefresh)
}

// IsValid 验证声明是否有效
func (c *JWTClaims) IsValid() bool {
	// 基础字段验证
	if c.UserID == "" {
		return false
	}

	// 根据token类型进行不同的验证
	switch c.TokenType {
	case TokenTypeAccess:
		// 访问令牌需要完整的租户信息
		if c.TenantID == "" {
			return false
		}
	case TokenTypePreAuth:
		// 前置认证令牌不需要租户信息
		// UserID已在上面验证
	case TokenTypeRefresh:
		// 刷新令牌需要租户信息
		if c.TenantID == "" {
			return false
		}
	default:
		// 未知的token类型
		return false
	}

	now := time.Now()

	// 检查过期时间
	if c.ExpiresAt != nil && now.After(c.ExpiresAt.Time) {
		return false
	}

	// 检查生效时间
	if c.NotBefore != nil && now.Before(c.NotBefore.Time) {
		return false
	}

	return true
}

// IsValidForTokenType 根据指定的token类型验证声明
func (c *JWTClaims) IsValidForTokenType(expectedType TokenType) bool {
	return c.IsValid() && c.TokenType == expectedType
}

// IsAccessToken 检查是否为访问令牌
func (c *JWTClaims) IsAccessToken() bool {
	return c.TokenType.IsAccessToken()
}

// IsPreAuthToken 检查是否为前置认证令牌
func (c *JWTClaims) IsPreAuthToken() bool {
	return c.TokenType.IsPreAuthToken()
}

// IsRefreshToken 检查是否为刷新令牌
func (c *JWTClaims) IsRefreshToken() bool {
	return c.TokenType.IsRefreshToken()
}

// CanAccessTenant 检查是否可以访问指定租户
func (c *JWTClaims) CanAccessTenant(tenantID string) bool {
	return c.IsAccessToken() && c.TenantID == tenantID
}

// HasRole 检查是否包含指定角色
func (c *JWTClaims) HasRole(role string) bool {
	for _, r := range c.Roles {
		if r == role {
			return true
		}
	}
	return false
}

// HasAnyRole 检查是否包含任意一个指定角色
func (c *JWTClaims) HasAnyRole(roles []string) bool {
	for _, role := range roles {
		if c.HasRole(role) {
			return true
		}
	}
	return false
}

// GetTokenTypeString 获取token类型的字符串表示
func (c *JWTClaims) GetTokenTypeString() string {
	return c.TokenType.String()
}

// RequiresTenantID 检查当前token类型是否需要租户ID
func (c *JWTClaims) RequiresTenantID() bool {
	return c.TokenType == TokenTypeAccess || c.TokenType == TokenTypeRefresh
}
