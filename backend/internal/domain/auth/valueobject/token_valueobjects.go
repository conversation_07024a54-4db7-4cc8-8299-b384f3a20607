package valueobject

import (
	"crypto/sha256"
	"encoding/hex"
	"fmt"
	"regexp"
	"strings"
	"time"

	"backend/pkg/infrastructure/snowflake"
)

// TokenID Token唯一标识值对象
type TokenID struct {
	value string
}

// NewTokenID 创建新的TokenID
func NewTokenID() *TokenID {
	// 使用雪花算法生成唯一ID
	generator := snowflake.NewGenerator(1) // 使用节点ID 1
	id := generator.Generate()
	return &TokenID{value: fmt.Sprintf("tok_%d", id)}
}

// NewTokenIDFromString 从字符串创建TokenID
func NewTokenIDFromString(value string) (*TokenID, error) {
	if value == "" {
		return nil, fmt.Errorf("TokenID不能为空")
	}

	// 验证TokenID格式 (tok_数字)
	matched, _ := regexp.MatchString(`^tok_\d+$`, value)
	if !matched {
		return nil, fmt.Errorf("TokenID格式无效: %s", value)
	}

	return &TokenID{value: value}, nil
}

// Value 获取TokenID值
func (t *TokenID) Value() string {
	return t.value
}

// String 字符串表示
func (t *TokenID) String() string {
	return t.value
}

// Equals 比较两个TokenID是否相等
func (t *TokenID) Equals(other *TokenID) bool {
	if other == nil {
		return false
	}
	return t.value == other.value
}

// SessionID 会话唯一标识值对象
type SessionID struct {
	value string
}

// NewSessionID 创建新的SessionID
func NewSessionID() *SessionID {
	generator := snowflake.NewGenerator(2) // 使用节点ID 2
	id := generator.Generate()
	return &SessionID{value: fmt.Sprintf("sess_%d", id)}
}

// NewSessionIDFromString 从字符串创建SessionID
func NewSessionIDFromString(value string) (*SessionID, error) {
	if value == "" {
		return nil, fmt.Errorf("SessionID不能为空")
	}

	// 验证SessionID格式 (sess_数字)
	matched, _ := regexp.MatchString(`^sess_\d+$`, value)
	if !matched {
		return nil, fmt.Errorf("SessionID格式无效: %s", value)
	}

	return &SessionID{value: value}, nil
}

// Value 获取SessionID值
func (s *SessionID) Value() string {
	return s.value
}

// String 字符串表示
func (s *SessionID) String() string {
	return s.value
}

// Equals 比较两个SessionID是否相等
func (s *SessionID) Equals(other *SessionID) bool {
	if other == nil {
		return false
	}
	return s.value == other.value
}

// ContextID 前置认证上下文标识值对象
type ContextID struct {
	value string
}

// NewContextID 创建新的ContextID
func NewContextID() *ContextID {
	generator := snowflake.NewGenerator(3) // 使用节点ID 3
	id := generator.Generate()
	return &ContextID{value: fmt.Sprintf("ctx_%d", id)}
}

// NewContextIDFromString 从字符串创建ContextID
func NewContextIDFromString(value string) (*ContextID, error) {
	if value == "" {
		return nil, fmt.Errorf("ContextID不能为空")
	}

	// 验证ContextID格式 (ctx_数字)
	matched, _ := regexp.MatchString(`^ctx_\d+$`, value)
	if !matched {
		return nil, fmt.Errorf("ContextID格式无效: %s", value)
	}

	return &ContextID{value: value}, nil
}

// Value 获取ContextID值
func (c *ContextID) Value() string {
	return c.value
}

// String 字符串表示
func (c *ContextID) String() string {
	return c.value
}

// Equals 比较两个ContextID是否相等
func (c *ContextID) Equals(other *ContextID) bool {
	if other == nil {
		return false
	}
	return c.value == other.value
}

// DeviceFingerprint 设备指纹值对象
type DeviceFingerprint struct {
	deviceID    string
	userAgent   string
	fingerprint string
}

// NewDeviceFingerprint 创建设备指纹
func NewDeviceFingerprint(deviceID, userAgent, clientIP string, additionalInfo map[string]string) *DeviceFingerprint {
	// 构建指纹字符串
	fingerprintData := fmt.Sprintf("%s|%s|%s", deviceID, userAgent, clientIP)

	// 添加额外信息
	if additionalInfo != nil {
		for key, value := range additionalInfo {
			fingerprintData += fmt.Sprintf("|%s:%s", key, value)
		}
	}

	// 计算SHA256哈希
	hash := sha256.Sum256([]byte(fingerprintData))
	fingerprint := hex.EncodeToString(hash[:])

	return &DeviceFingerprint{
		deviceID:    deviceID,
		userAgent:   userAgent,
		fingerprint: fingerprint,
	}
}

// DeviceID 获取设备ID
func (d *DeviceFingerprint) DeviceID() string {
	return d.deviceID
}

// UserAgent 获取用户代理
func (d *DeviceFingerprint) UserAgent() string {
	return d.userAgent
}

// Fingerprint 获取指纹值
func (d *DeviceFingerprint) Fingerprint() string {
	return d.fingerprint
}

// Equals 比较两个设备指纹是否相等
func (d *DeviceFingerprint) Equals(other *DeviceFingerprint) bool {
	if other == nil {
		return false
	}
	return d.fingerprint == other.fingerprint
}

// TokenHash Token哈希值对象
type TokenHash struct {
	originalToken string
	hashValue     string
}

// NewTokenHash 创建Token哈希
func NewTokenHash(token string) *TokenHash {
	hash := sha256.Sum256([]byte(token))
	hashValue := hex.EncodeToString(hash[:])

	return &TokenHash{
		originalToken: token,
		hashValue:     hashValue,
	}
}

// NewTokenHashFromHash 从哈希值创建TokenHash
func NewTokenHashFromHash(hashValue string) (*TokenHash, error) {
	if hashValue == "" {
		return nil, fmt.Errorf("Token哈希值不能为空")
	}

	// 验证哈希值格式（64位十六进制字符）
	matched, _ := regexp.MatchString(`^[a-fA-F0-9]{64}$`, hashValue)
	if !matched {
		return nil, fmt.Errorf("Token哈希值格式无效: %s", hashValue)
	}

	return &TokenHash{
		hashValue: strings.ToLower(hashValue),
	}, nil
}

// Hash 获取哈希值
func (t *TokenHash) Hash() string {
	return t.hashValue
}

// OriginalToken 获取原始Token（如果有）
func (t *TokenHash) OriginalToken() string {
	return t.originalToken
}

// Equals 比较两个TokenHash是否相等
func (t *TokenHash) Equals(other *TokenHash) bool {
	if other == nil {
		return false
	}
	return t.hashValue == other.hashValue
}

// TokenScope Token权限范围值对象
type TokenScope struct {
	scopes []string
}

// NewTokenScope 创建Token权限范围
func NewTokenScope(scopes ...string) *TokenScope {
	// 去重和排序
	uniqueScopes := make(map[string]bool)
	var result []string

	for _, scope := range scopes {
		scope = strings.TrimSpace(scope)
		if scope != "" && !uniqueScopes[scope] {
			uniqueScopes[scope] = true
			result = append(result, scope)
		}
	}

	return &TokenScope{scopes: result}
}

// NewTokenScopeFromString 从字符串创建Token权限范围
func NewTokenScopeFromString(scopeString string) *TokenScope {
	if scopeString == "" {
		return &TokenScope{scopes: []string{}}
	}

	scopes := strings.Split(scopeString, " ")
	return NewTokenScope(scopes...)
}

// Scopes 获取权限范围列表
func (t *TokenScope) Scopes() []string {
	return append([]string{}, t.scopes...) // 返回副本
}

// String 字符串表示
func (t *TokenScope) String() string {
	return strings.Join(t.scopes, " ")
}

// HasScope 检查是否包含指定权限
func (t *TokenScope) HasScope(scope string) bool {
	for _, s := range t.scopes {
		if s == scope {
			return true
		}
	}
	return false
}

// HasAnyScope 检查是否包含任意一个指定权限
func (t *TokenScope) HasAnyScope(scopes ...string) bool {
	for _, scope := range scopes {
		if t.HasScope(scope) {
			return true
		}
	}
	return false
}

// HasAllScopes 检查是否包含所有指定权限
func (t *TokenScope) HasAllScopes(scopes ...string) bool {
	for _, scope := range scopes {
		if !t.HasScope(scope) {
			return false
		}
	}
	return true
}

// AddScope 添加权限范围
func (t *TokenScope) AddScope(scope string) *TokenScope {
	if t.HasScope(scope) {
		return t
	}

	newScopes := append(t.scopes, scope)
	return &TokenScope{scopes: newScopes}
}

// RemoveScope 移除权限范围
func (t *TokenScope) RemoveScope(scope string) *TokenScope {
	var newScopes []string
	for _, s := range t.scopes {
		if s != scope {
			newScopes = append(newScopes, s)
		}
	}
	return &TokenScope{scopes: newScopes}
}

// IsEmpty 检查权限范围是否为空
func (t *TokenScope) IsEmpty() bool {
	return len(t.scopes) == 0
}

// Count 获取权限数量
func (t *TokenScope) Count() int {
	return len(t.scopes)
}

// TokenExpiry Token过期时间值对象
type TokenExpiry struct {
	expiresAt time.Time
	duration  time.Duration
}

// NewTokenExpiry 创建Token过期时间
func NewTokenExpiry(duration time.Duration) *TokenExpiry {
	return &TokenExpiry{
		expiresAt: time.Now().Add(duration),
		duration:  duration,
	}
}

// NewTokenExpiryFromTime 从指定时间创建Token过期时间
func NewTokenExpiryFromTime(expiresAt time.Time) *TokenExpiry {
	duration := time.Until(expiresAt)
	return &TokenExpiry{
		expiresAt: expiresAt,
		duration:  duration,
	}
}

// ExpiresAt 获取过期时间
func (t *TokenExpiry) ExpiresAt() time.Time {
	return t.expiresAt
}

// Duration 获取持续时间
func (t *TokenExpiry) Duration() time.Duration {
	return t.duration
}

// IsExpired 检查是否已过期
func (t *TokenExpiry) IsExpired() bool {
	return time.Now().After(t.expiresAt)
}

// TimeUntilExpiry 获取距离过期的时间
func (t *TokenExpiry) TimeUntilExpiry() time.Duration {
	return time.Until(t.expiresAt)
}

// IsValid 检查是否有效（未过期）
func (t *TokenExpiry) IsValid() bool {
	return !t.IsExpired()
}

// Extend 延长过期时间
func (t *TokenExpiry) Extend(duration time.Duration) *TokenExpiry {
	newExpiresAt := t.expiresAt.Add(duration)
	return &TokenExpiry{
		expiresAt: newExpiresAt,
		duration:  t.duration + duration,
	}
}

// Refresh 刷新过期时间（重新设置为原始持续时间）
func (t *TokenExpiry) Refresh() *TokenExpiry {
	return NewTokenExpiry(t.duration)
}
