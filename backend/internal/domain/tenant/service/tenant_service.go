package service

import (
	"backend/internal/domain/tenant/entity"
	"backend/internal/domain/tenant/repository"
	"backend/internal/domain/tenant/valueobject"
	"context"
	"errors"
	"time"

	"github.com/shopspring/decimal"
)

// TenantService 租户领域服务接口
type TenantService interface {
	// 租户管理
	CreateTenant(ctx context.Context, req CreateTenantRequest) (*entity.Tenant, error)
	ValidateTenantDomain(ctx context.Context, domain string) error
	ActivateTenant(ctx context.Context, tenantID string) error
	SuspendTenant(ctx context.Context, tenantID string, reason string) error
	TerminateTenant(ctx context.Context, tenantID string, reason string) error

	// 配额管理
	CreateTenantQuota(ctx context.Context, tenantID string, limits QuotaLimits) (*entity.TenantQuota, error)
	CheckUserQuota(ctx context.Context, tenantID string) error
	CheckStorageQuota(ctx context.Context, tenantID string, sizeInMB int64) error
	CheckAPIQuota(ctx context.Context, tenantID string) error

	// 订阅管理
	CreateSubscription(ctx context.Context, req CreateSubscriptionRequest) (*entity.TenantSubscription, error)
	RenewSubscription(ctx context.Context, subscriptionID string, req RenewSubscriptionRequest) error
	CancelSubscription(ctx context.Context, subscriptionID string, reason, userID string) error
	ExtendTrialPeriod(ctx context.Context, subscriptionID string, days int) error

	// 状态检查
	CheckTenantHealth(ctx context.Context, tenantID string) (*TenantHealthStatus, error)
	IsFeatureEnabled(ctx context.Context, tenantID string, feature string) (bool, error)
	GetTenantLimits(ctx context.Context, tenantID string) (*TenantLimits, error)
}

// tenantService 租户领域服务实现
type tenantService struct {
	tenantRepo       repository.TenantRepository
	quotaRepo        repository.TenantQuotaRepository
	subscriptionRepo repository.TenantSubscriptionRepository
}

// NewTenantService 创建租户领域服务
func NewTenantService(
	tenantRepo repository.TenantRepository,
	quotaRepo repository.TenantQuotaRepository,
	subscriptionRepo repository.TenantSubscriptionRepository,
) TenantService {
	return &tenantService{
		tenantRepo:       tenantRepo,
		quotaRepo:        quotaRepo,
		subscriptionRepo: subscriptionRepo,
	}
}

// CreateTenant 创建租户
func (s *tenantService) CreateTenant(ctx context.Context, req CreateTenantRequest) (*entity.Tenant, error) {
	// 验证域名唯一性
	if err := s.ValidateTenantDomain(ctx, req.Domain); err != nil {
		return nil, err
	}

	// 创建租户实体
	tenant, err := entity.NewTenant(req.Name, req.Domain, req.DisplayName, req.Type)
	if err != nil {
		return nil, err
	}

	// 设置可选信息
	if req.Description != "" {
		tenant.Description = req.Description
	}
	if req.Industry != "" {
		tenant.Industry = req.Industry
	}
	if req.Country != "" {
		tenant.Country = req.Country
	}
	if req.Province != "" {
		tenant.Province = req.Province
	}
	if req.City != "" {
		tenant.City = req.City
	}
	if req.Address != "" {
		tenant.Address = req.Address
	}

	// 更新联系信息
	if req.ContactEmail != "" || req.ContactPhone != "" {
		if err := tenant.UpdateContactInfo(req.ContactEmail, req.ContactPhone); err != nil {
			return nil, err
		}
	}

	// 保存租户
	if err := s.tenantRepo.Create(ctx, tenant); err != nil {
		return nil, err
	}

	// 创建默认配额
	quota, err := entity.NewTenantQuota(
		tenant.BusinessID,
		tenant.MaxUsers,
		tenant.MaxStorage,
		tenant.MaxAPIQuota,
	)
	if err != nil {
		return nil, err
	}

	if err := s.quotaRepo.Create(ctx, quota); err != nil {
		// 如果配额创建失败，需要回滚租户创建
		// 这里应该在事务中处理，简化实现
		return nil, err
	}

	return tenant, nil
}

// ValidateTenantDomain 验证租户域名
func (s *tenantService) ValidateTenantDomain(ctx context.Context, domain string) error {
	// 验证域名格式
	tenantDomain, err := valueobject.NewTenantDomain(domain)
	if err != nil {
		return err
	}

	// 检查域名唯一性
	exists, err := s.tenantRepo.ExistsByDomain(ctx, tenantDomain.String())
	if err != nil {
		return err
	}
	if exists {
		return errors.New("域名已被使用")
	}

	// 检查保留域名
	reservedDomains := []string{"admin", "api", "www", "app", "dashboard", "system"}
	subdomain := tenantDomain.GetSubdomain()
	for _, reserved := range reservedDomains {
		if subdomain == reserved {
			return errors.New("域名为系统保留域名，无法使用")
		}
	}

	return nil
}

// ActivateTenant 激活租户
func (s *tenantService) ActivateTenant(ctx context.Context, tenantID string) error {
	tenant, err := s.tenantRepo.GetByID(ctx, tenantID)
	if err != nil {
		return err
	}

	if err := tenant.Activate(); err != nil {
		return err
	}

	return s.tenantRepo.Update(ctx, tenant)
}

// SuspendTenant 暂停租户
func (s *tenantService) SuspendTenant(ctx context.Context, tenantID string, reason string) error {
	tenant, err := s.tenantRepo.GetByID(ctx, tenantID)
	if err != nil {
		return err
	}

	if err := tenant.Suspend(reason); err != nil {
		return err
	}

	return s.tenantRepo.Update(ctx, tenant)
}

// TerminateTenant 终止租户
func (s *tenantService) TerminateTenant(ctx context.Context, tenantID string, reason string) error {
	tenant, err := s.tenantRepo.GetByID(ctx, tenantID)
	if err != nil {
		return err
	}

	if err := tenant.Terminate(reason); err != nil {
		return err
	}

	return s.tenantRepo.Update(ctx, tenant)
}

// CreateTenantQuota 创建租户配额
func (s *tenantService) CreateTenantQuota(ctx context.Context, tenantID string, limits QuotaLimits) (*entity.TenantQuota, error) {
	// 检查租户是否存在
	exists, err := s.tenantRepo.Exists(ctx, tenantID)
	if err != nil {
		return nil, err
	}
	if !exists {
		return nil, errors.New("租户不存在")
	}

	// 创建配额实体
	quota, err := entity.NewTenantQuota(tenantID, limits.UserLimit, limits.StorageLimit, limits.APILimit)
	if err != nil {
		return nil, err
	}

	// 更新其他限制
	if err := quota.UpdateLimits(
		limits.UserLimit,
		limits.StorageLimit,
		limits.APILimit,
		limits.ProductLimit,
		limits.OrderLimit,
		limits.FileUploadLimit,
		limits.EmailLimit,
	); err != nil {
		return nil, err
	}

	if err := s.quotaRepo.Create(ctx, quota); err != nil {
		return nil, err
	}

	return quota, nil
}

// CheckUserQuota 检查用户配额
func (s *tenantService) CheckUserQuota(ctx context.Context, tenantID string) error {
	quota, err := s.quotaRepo.GetByTenantID(ctx, tenantID)
	if err != nil {
		return err
	}

	if !quota.CanCreateUser() {
		return errors.New("用户配额已达上限")
	}

	return nil
}

// CheckStorageQuota 检查存储配额
func (s *tenantService) CheckStorageQuota(ctx context.Context, tenantID string, sizeInMB int64) error {
	quota, err := s.quotaRepo.GetByTenantID(ctx, tenantID)
	if err != nil {
		return err
	}

	if !quota.CanUseStorage(sizeInMB) {
		return errors.New("存储配额不足")
	}

	return nil
}

// CheckAPIQuota 检查API配额
func (s *tenantService) CheckAPIQuota(ctx context.Context, tenantID string) error {
	quota, err := s.quotaRepo.GetByTenantID(ctx, tenantID)
	if err != nil {
		return err
	}

	if !quota.CanMakeAPICall() {
		return errors.New("API调用配额已达上限")
	}

	return nil
}

// CreateSubscription 创建订阅
func (s *tenantService) CreateSubscription(ctx context.Context, req CreateSubscriptionRequest) (*entity.TenantSubscription, error) {
	// 验证租户存在
	tenant, err := s.tenantRepo.GetByID(ctx, req.TenantID)
	if err != nil {
		return nil, err
	}

	// 创建订阅周期值对象
	period, err := valueobject.NewTenantSubscriptionPeriod(req.StartDate, req.EndDate)
	if err != nil {
		return nil, err
	}

	// 创建定价值对象
	pricing, err := valueobject.NewTenantPricing(req.Price, req.Currency, req.BillingCycle)
	if err != nil {
		return nil, err
	}

	// 创建订阅实体
	subscription, err := entity.NewTenantSubscription(
		req.TenantID,
		req.PlanID,
		req.PlanName,
		period,
		pricing,
	)
	if err != nil {
		return nil, err
	}

	// 设置配额限制
	if err := subscription.UpdateQuotaLimits(
		req.UserLimit,
		req.StorageLimit,
		req.APILimit,
		req.ProductLimit,
		req.OrderLimit,
	); err != nil {
		return nil, err
	}

	// 保存订阅
	if err := s.subscriptionRepo.Create(ctx, subscription); err != nil {
		return nil, err
	}

	// 更新租户订阅信息
	if err := tenant.UpdateSubscription(req.PlanName, &req.StartDate, &req.EndDate); err != nil {
		return nil, err
	}

	// 更新租户配额
	if err := tenant.UpdateQuotaLimits(req.UserLimit, req.StorageLimit, req.APILimit); err != nil {
		return nil, err
	}

	if err := s.tenantRepo.Update(ctx, tenant); err != nil {
		return nil, err
	}

	return subscription, nil
}

// RenewSubscription 续费订阅
func (s *tenantService) RenewSubscription(ctx context.Context, subscriptionID string, req RenewSubscriptionRequest) error {
	subscription, err := s.subscriptionRepo.GetByID(ctx, subscriptionID)
	if err != nil {
		return err
	}

	// 创建新的订阅周期
	period, err := valueobject.NewTenantSubscriptionPeriod(req.StartDate, req.EndDate)
	if err != nil {
		return err
	}

	// 创建新的定价
	pricing, err := valueobject.NewTenantPricing(req.Price, req.Currency, req.BillingCycle)
	if err != nil {
		return err
	}

	// 续费订阅
	if err := subscription.Renew(period, pricing); err != nil {
		return err
	}

	return s.subscriptionRepo.Update(ctx, subscription)
}

// CancelSubscription 取消订阅
func (s *tenantService) CancelSubscription(ctx context.Context, subscriptionID string, reason, userID string) error {
	subscription, err := s.subscriptionRepo.GetByID(ctx, subscriptionID)
	if err != nil {
		return err
	}

	if err := subscription.Cancel(reason, userID); err != nil {
		return err
	}

	return s.subscriptionRepo.Update(ctx, subscription)
}

// ExtendTrialPeriod 延长试用期
func (s *tenantService) ExtendTrialPeriod(ctx context.Context, subscriptionID string, days int) error {
	subscription, err := s.subscriptionRepo.GetByID(ctx, subscriptionID)
	if err != nil {
		return err
	}

	if err := subscription.ExtendTrial(days, 3); err != nil { // 最多延期3次
		return err
	}

	return s.subscriptionRepo.Update(ctx, subscription)
}

// CheckTenantHealth 检查租户健康状态
func (s *tenantService) CheckTenantHealth(ctx context.Context, tenantID string) (*TenantHealthStatus, error) {
	// 获取租户信息
	tenant, err := s.tenantRepo.GetByID(ctx, tenantID)
	if err != nil {
		return nil, err
	}

	// 获取配额信息
	quota, err := s.quotaRepo.GetByTenantID(ctx, tenantID)
	if err != nil {
		return nil, err
	}

	// 获取订阅信息
	subscription, err := s.subscriptionRepo.GetByTenantID(ctx, tenantID)
	if err != nil {
		return nil, err
	}

	health := &TenantHealthStatus{
		TenantID:  tenantID,
		IsHealthy: true,
		Alerts:    []HealthAlert{},
		CheckedAt: time.Now(),
	}

	// 检查租户状态
	if !tenant.IsActive() {
		health.IsHealthy = false
		health.Alerts = append(health.Alerts, HealthAlert{
			Type:     "tenant_status",
			Level:    "critical",
			Message:  "租户未激活",
			Severity: "high",
		})
	}

	// 检查订阅状态
	if subscription != nil {
		if subscription.IsExpired() {
			health.IsHealthy = false
			health.Alerts = append(health.Alerts, HealthAlert{
				Type:     "subscription_expired",
				Level:    "critical",
				Message:  "订阅已过期",
				Severity: "high",
			})
		} else if subscription.DaysUntilExpiry() <= 7 {
			health.Alerts = append(health.Alerts, HealthAlert{
				Type:     "subscription_expiring",
				Level:    "warning",
				Message:  "订阅即将过期",
				Severity: "medium",
			})
		}
	}

	// 检查配额状态
	if quota != nil {
		quotaAlerts := quota.GetQuotaAlerts()
		for _, alert := range quotaAlerts {
			health.Alerts = append(health.Alerts, HealthAlert{
				Type:     "quota_" + alert.Type,
				Level:    alert.Level,
				Message:  alert.Message,
				Severity: getSeverityFromLevel(alert.Level),
			})
		}

		if quota.IsOverQuota() {
			health.IsHealthy = false
		}
	}

	return health, nil
}

// IsFeatureEnabled 检查功能是否启用
func (s *tenantService) IsFeatureEnabled(ctx context.Context, tenantID string, feature string) (bool, error) {
	// 首先检查租户级别的功能
	tenant, err := s.tenantRepo.GetByID(ctx, tenantID)
	if err != nil {
		return false, err
	}

	if tenant.HasFeature(feature) {
		return true, nil
	}

	// 然后检查订阅级别的功能
	subscription, err := s.subscriptionRepo.GetByTenantID(ctx, tenantID)
	if err != nil {
		return false, err
	}

	if subscription != nil && subscription.HasFeature(feature) {
		return true, nil
	}

	return false, nil
}

// GetTenantLimits 获取租户限制
func (s *tenantService) GetTenantLimits(ctx context.Context, tenantID string) (*TenantLimits, error) {
	tenant, err := s.tenantRepo.GetByID(ctx, tenantID)
	if err != nil {
		return nil, err
	}

	quota, err := s.quotaRepo.GetByTenantID(ctx, tenantID)
	if err != nil {
		return nil, err
	}

	subscription, err := s.subscriptionRepo.GetByTenantID(ctx, tenantID)
	if err != nil {
		return nil, err
	}

	limits := &TenantLimits{
		TenantID:     tenantID,
		UserLimit:    tenant.MaxUsers,
		StorageLimit: tenant.MaxStorage,
		APILimit:     tenant.MaxAPIQuota,
	}

	if quota != nil {
		limits.UserUsed = quota.UserUsed
		limits.StorageUsed = quota.StorageUsed
		limits.APIUsed = quota.APIUsedMonth
		limits.ProductLimit = quota.ProductLimit
		limits.ProductUsed = quota.ProductUsed
	}

	if subscription != nil {
		limits.Features = subscription.GetFeatures()
		limits.SubscriptionPlan = subscription.PlanName
		limits.ExpiryDate = &subscription.EndDate
	}

	return limits, nil
}

// 辅助函数：根据告警级别获取严重程度
func getSeverityFromLevel(level string) string {
	switch level {
	case "critical":
		return "high"
	case "warning":
		return "medium"
	default:
		return "low"
	}
}

// 请求和响应结构体定义

// CreateTenantRequest 创建租户请求
type CreateTenantRequest struct {
	Name         string                 `json:"name" validate:"required,min=2,max=100"`
	Domain       string                 `json:"domain" validate:"required,min=3,max=100"`
	DisplayName  string                 `json:"display_name" validate:"max=100"`
	Description  string                 `json:"description" validate:"max=500"`
	Type         valueobject.TenantType `json:"type" validate:"required"`
	Industry     string                 `json:"industry" validate:"max=100"`
	Country      string                 `json:"country" validate:"max=50"`
	Province     string                 `json:"province" validate:"max=50"`
	City         string                 `json:"city" validate:"max=50"`
	Address      string                 `json:"address" validate:"max=255"`
	ContactEmail string                 `json:"contact_email" validate:"omitempty,email"`
	ContactPhone string                 `json:"contact_phone" validate:"omitempty,len=11"`
}

// QuotaLimits 配额限制
type QuotaLimits struct {
	UserLimit       int   `json:"user_limit"`
	StorageLimit    int64 `json:"storage_limit"`
	APILimit        int   `json:"api_limit"`
	ProductLimit    int   `json:"product_limit"`
	OrderLimit      int   `json:"order_limit"`
	FileUploadLimit int   `json:"file_upload_limit"`
	EmailLimit      int   `json:"email_limit"`
}

// CreateSubscriptionRequest 创建订阅请求
type CreateSubscriptionRequest struct {
	TenantID     string          `json:"tenant_id" validate:"required"`
	PlanID       string          `json:"plan_id" validate:"required"`
	PlanName     string          `json:"plan_name" validate:"required"`
	StartDate    time.Time       `json:"start_date" validate:"required"`
	EndDate      time.Time       `json:"end_date" validate:"required"`
	Price        decimal.Decimal `json:"price" validate:"required"`
	Currency     string          `json:"currency" validate:"required,len=3"`
	BillingCycle string          `json:"billing_cycle" validate:"required,oneof=monthly yearly lifetime"`
	UserLimit    int             `json:"user_limit" validate:"min=1"`
	StorageLimit int64           `json:"storage_limit" validate:"min=1"`
	APILimit     int             `json:"api_limit" validate:"min=1"`
	ProductLimit int             `json:"product_limit" validate:"min=1"`
	OrderLimit   int             `json:"order_limit" validate:"min=1"`
}

// RenewSubscriptionRequest 续费订阅请求
type RenewSubscriptionRequest struct {
	StartDate    time.Time       `json:"start_date" validate:"required"`
	EndDate      time.Time       `json:"end_date" validate:"required"`
	Price        decimal.Decimal `json:"price" validate:"required"`
	Currency     string          `json:"currency" validate:"required,len=3"`
	BillingCycle string          `json:"billing_cycle" validate:"required,oneof=monthly yearly lifetime"`
}

// TenantHealthStatus 租户健康状态
type TenantHealthStatus struct {
	TenantID  string        `json:"tenant_id"`
	IsHealthy bool          `json:"is_healthy"`
	Alerts    []HealthAlert `json:"alerts"`
	CheckedAt time.Time     `json:"checked_at"`
}

// HealthAlert 健康告警
type HealthAlert struct {
	Type     string `json:"type"`     // 告警类型
	Level    string `json:"level"`    // 告警级别: info, warning, critical
	Message  string `json:"message"`  // 告警消息
	Severity string `json:"severity"` // 严重程度: low, medium, high
}

// TenantLimits 租户限制信息
type TenantLimits struct {
	TenantID         string     `json:"tenant_id"`
	UserLimit        int        `json:"user_limit"`
	UserUsed         int        `json:"user_used"`
	StorageLimit     int64      `json:"storage_limit"`
	StorageUsed      int64      `json:"storage_used"`
	APILimit         int        `json:"api_limit"`
	APIUsed          int        `json:"api_used"`
	ProductLimit     int        `json:"product_limit"`
	ProductUsed      int        `json:"product_used"`
	Features         []string   `json:"features"`
	SubscriptionPlan string     `json:"subscription_plan"`
	ExpiryDate       *time.Time `json:"expiry_date,omitempty"`
}
