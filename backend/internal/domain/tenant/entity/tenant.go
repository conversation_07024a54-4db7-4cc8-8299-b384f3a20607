package entity

import (
	"backend/internal/domain/tenant/valueobject"
	"backend/internal/shared/types"
	"database/sql/driver"
	"encoding/json"
	"errors"
	"fmt"
	"time"
)

// TenantSettings 租户设置类型
type TenantSettings map[string]interface{}

// Value 实现 driver.Valuer 接口
func (s TenantSettings) Value() (driver.Value, error) {
	if s == nil {
		return nil, nil
	}
	return json.Marshal(s)
}

// Scan 实现 sql.Scanner 接口
func (s *TenantSettings) Scan(value interface{}) error {
	if value == nil {
		*s = make(map[string]interface{})
		return nil
	}

	switch v := value.(type) {
	case []byte:
		return json.Unmarshal(v, s)
	case string:
		return json.Unmarshal([]byte(v), s)
	default:
		return fmt.Errorf("cannot scan %T into TenantSettings", value)
	}
}

// Tenant 租户聚合根
type Tenant struct {
	types.GlobalEntity

	// 基本信息
	Name        string `gorm:"size:255;not null"`
	Domain      string `gorm:"size:100;uniqueIndex"`
	DisplayName string `gorm:"size:255;not null"`
	Description string `gorm:"type:text"`

	// 租户分类
	Type   valueobject.TenantType   `gorm:"size:20;not null;default:'basic'"`
	Status valueobject.TenantStatus `gorm:"type:integer;not null;default:0"`

	// 订阅信息
	SubscriptionPlan  string `gorm:"size:50"`
	SubscriptionStart *time.Time
	SubscriptionEnd   *time.Time

	// 配置信息
	Settings TenantSettings `gorm:"type:jsonb;default:'{}'"`

	// 联系信息
	ContactEmail string `gorm:"size:255"`
	ContactPhone string `gorm:"size:50"`

	// 业务信息
	BusinessLicense string `gorm:"size:100"`
	TaxID           string `gorm:"size:50"`
	Industry        string `gorm:"size:100"`

	// 地址信息
	Country  string `gorm:"size:50"`
	Province string `gorm:"size:50"`
	City     string `gorm:"size:50"`
	Address  string `gorm:"size:255"`

	// 限制配置
	MaxUsers    int   `gorm:"default:10"`
	MaxStorage  int64 `gorm:"default:1024"` // MB
	MaxAPIQuota int   `gorm:"default:10000"`
}

// TableName 指定表名
func (Tenant) TableName() string {
	return "tenants"
}

// NewTenant 创建新租户
func NewTenant(name, domain, displayName string, tenantType valueobject.TenantType) (*Tenant, error) {
	if name == "" {
		return nil, errors.New("租户名称不能为空")
	}
	if domain == "" {
		return nil, errors.New("租户域名不能为空")
	}
	if displayName == "" {
		displayName = name
	}
	if !tenantType.IsValid() {
		return nil, errors.New("无效的租户类型")
	}

	return &Tenant{
		GlobalEntity: types.NewEmptyGlobalEntity(),
		Name:         name,
		Domain:       domain,
		DisplayName:  displayName,
		Type:         tenantType,
		Status:       valueobject.TenantStatusInactive, // 默认未激活
		Settings:     make(TenantSettings),
		MaxUsers:     getDefaultUserLimit(tenantType),
		MaxStorage:   getDefaultStorageLimit(tenantType),
		MaxAPIQuota:  getDefaultAPILimit(tenantType),
	}, nil
}

// IsActive 检查租户是否处于活跃状态
func (t *Tenant) IsActive() bool {
	return t.Status == valueobject.TenantStatusActive
}

// IsExpired 检查订阅是否已过期
func (t *Tenant) IsExpired() bool {
	return t.SubscriptionEnd != nil && time.Now().After(*t.SubscriptionEnd)
}

// CanCreateUser 检查是否还能创建新用户
func (t *Tenant) CanCreateUser() bool {
	return t.IsActive() && !t.IsExpired()
}

// Activate 激活租户
func (t *Tenant) Activate() error {
	if t.Status == valueobject.TenantStatusTerminated {
		return errors.New("已终止的租户无法激活")
	}

	if !t.Status.CanTransitionTo(valueobject.TenantStatusActive) {
		return fmt.Errorf("无法从状态 %s 转换到 active", t.Status.String())
	}

	t.Status = valueobject.TenantStatusActive
	return nil
}

// Suspend 暂停租户
func (t *Tenant) Suspend(reason string) error {
	if !t.Status.CanTransitionTo(valueobject.TenantStatusSuspended) {
		return fmt.Errorf("无法从状态 %s 转换到 suspended", t.Status.String())
	}

	t.Status = valueobject.TenantStatusSuspended

	// 记录暂停原因到Settings中
	if t.Settings == nil {
		t.Settings = make(TenantSettings)
	}
	t.Settings["suspend_reason"] = reason
	t.Settings["suspend_time"] = time.Now()

	return nil
}

// MarkExpired 标记为过期
func (t *Tenant) MarkExpired() error {
	if !t.Status.CanTransitionTo(valueobject.TenantStatusExpired) {
		return fmt.Errorf("无法从状态 %s 转换到 expired", t.Status.String())
	}

	t.Status = valueobject.TenantStatusExpired
	return nil
}

// Terminate 终止租户
func (t *Tenant) Terminate(reason string) error {
	if !t.Status.CanTransitionTo(valueobject.TenantStatusTerminated) {
		return fmt.Errorf("无法从状态 %s 转换到 terminated", t.Status.String())
	}

	t.Status = valueobject.TenantStatusTerminated

	// 记录终止原因
	if t.Settings == nil {
		t.Settings = make(TenantSettings)
	}
	t.Settings["terminate_reason"] = reason
	t.Settings["terminate_time"] = time.Now()

	return nil
}

// UpdateSubscription 更新订阅信息
func (t *Tenant) UpdateSubscription(plan string, startDate, endDate *time.Time) error {
	if plan == "" {
		return errors.New("订阅计划不能为空")
	}

	if startDate != nil && endDate != nil && endDate.Before(*startDate) {
		return errors.New("结束日期不能早于开始日期")
	}

	t.SubscriptionPlan = plan
	t.SubscriptionStart = startDate
	t.SubscriptionEnd = endDate

	return nil
}

// UpdateQuotaLimits 更新配额限制
func (t *Tenant) UpdateQuotaLimits(userLimit int, storageLimit int64, apiLimit int) error {
	if userLimit < 0 {
		return errors.New("用户限制不能为负数")
	}
	if storageLimit < 0 {
		return errors.New("存储限制不能为负数")
	}
	if apiLimit < 0 {
		return errors.New("API限制不能为负数")
	}

	t.MaxUsers = userLimit
	t.MaxStorage = storageLimit
	t.MaxAPIQuota = apiLimit

	return nil
}

// UpdateContactInfo 更新联系信息
func (t *Tenant) UpdateContactInfo(email, phone string) error {
	contactInfo, err := valueobject.NewTenantContactInfo(email, phone)
	if err != nil {
		return err
	}

	t.ContactEmail = contactInfo.Email()
	t.ContactPhone = contactInfo.Phone()

	return nil
}

// GetDomainInfo 获取域名信息
func (t *Tenant) GetDomainInfo() (valueobject.TenantDomain, error) {
	return valueobject.NewTenantDomain(t.Domain)
}

// HasFeature 检查是否具有特定功能
func (t *Tenant) HasFeature(feature string) bool {
	if t.Settings == nil {
		return false
	}

	features, ok := t.Settings["features"].([]interface{})
	if !ok {
		return false
	}

	for _, f := range features {
		if featureStr, ok := f.(string); ok && featureStr == feature {
			return true
		}
	}

	return false
}

// AddFeature 添加功能
func (t *Tenant) AddFeature(feature string) {
	if t.Settings == nil {
		t.Settings = make(TenantSettings)
	}

	features, ok := t.Settings["features"].([]interface{})
	if !ok {
		features = []interface{}{}
	}

	// 检查是否已存在
	for _, f := range features {
		if featureStr, ok := f.(string); ok && featureStr == feature {
			return // 已存在，不重复添加
		}
	}

	features = append(features, feature)
	t.Settings["features"] = features
}

// RemoveFeature 移除功能
func (t *Tenant) RemoveFeature(feature string) {
	if t.Settings == nil {
		return
	}

	features, ok := t.Settings["features"].([]interface{})
	if !ok {
		return
	}

	newFeatures := []interface{}{}
	for _, f := range features {
		if featureStr, ok := f.(string); ok && featureStr != feature {
			newFeatures = append(newFeatures, f)
		}
	}

	t.Settings["features"] = newFeatures
}

// getDefaultUserLimit 根据租户类型获取默认用户限制
func getDefaultUserLimit(tenantType valueobject.TenantType) int {
	switch tenantType {
	case valueobject.TenantTypeSystem:
		return -1 // 无限制
	case valueobject.TenantTypeEnterprise:
		return 500
	case valueobject.TenantTypeProfessional:
		return 100
	case valueobject.TenantTypeBasic:
		return 10
	case valueobject.TenantTypeTrial:
		return 3
	default:
		return 10
	}
}

// getDefaultStorageLimit 根据租户类型获取默认存储限制 (MB)
func getDefaultStorageLimit(tenantType valueobject.TenantType) int64 {
	switch tenantType {
	case valueobject.TenantTypeSystem:
		return -1 // 无限制
	case valueobject.TenantTypeEnterprise:
		return 100 * 1024 // 100GB
	case valueobject.TenantTypeProfessional:
		return 10 * 1024 // 10GB
	case valueobject.TenantTypeBasic:
		return 1024 // 1GB
	case valueobject.TenantTypeTrial:
		return 512 // 512MB
	default:
		return 1024
	}
}

// getDefaultAPILimit 根据租户类型获取默认API限制
func getDefaultAPILimit(tenantType valueobject.TenantType) int {
	switch tenantType {
	case valueobject.TenantTypeSystem:
		return -1 // 无限制
	case valueobject.TenantTypeEnterprise:
		return 1000000 // 100万次/月
	case valueobject.TenantTypeProfessional:
		return 100000 // 10万次/月
	case valueobject.TenantTypeBasic:
		return 10000 // 1万次/月
	case valueobject.TenantTypeTrial:
		return 1000 // 1千次/月
	default:
		return 10000
	}
}
