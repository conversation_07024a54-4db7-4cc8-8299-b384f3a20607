package entity

import (
	"backend/internal/domain/tenant/valueobject"
	"backend/internal/shared/types"
	"errors"
	"time"

	"github.com/shopspring/decimal"
)

// SubscriptionStatus 订阅状态枚举
type SubscriptionStatus int

const (
	SubscriptionStatusPending   SubscriptionStatus = 0 // 待激活
	SubscriptionStatusActive    SubscriptionStatus = 1 // 有效
	SubscriptionStatusSuspended SubscriptionStatus = 2 // 暂停
	SubscriptionStatusCanceled  SubscriptionStatus = 3 // 已取消
	SubscriptionStatusExpired   SubscriptionStatus = 4 // 已过期
)

// String 返回字符串表示
func (s SubscriptionStatus) String() string {
	switch s {
	case SubscriptionStatusPending:
		return "pending"
	case SubscriptionStatusActive:
		return "active"
	case SubscriptionStatusSuspended:
		return "suspended"
	case SubscriptionStatusCanceled:
		return "canceled"
	case SubscriptionStatusExpired:
		return "expired"
	default:
		return "unknown"
	}
}

// GetDisplayName 获取显示名称
func (s SubscriptionStatus) GetDisplayName() string {
	switch s {
	case SubscriptionStatusPending:
		return "待激活"
	case SubscriptionStatusActive:
		return "有效"
	case SubscriptionStatusSuspended:
		return "暂停"
	case SubscriptionStatusCanceled:
		return "已取消"
	case SubscriptionStatusExpired:
		return "已过期"
	default:
		return "未知状态"
	}
}

// TenantSubscription 租户订阅实体
type TenantSubscription struct {
	types.TenantScopedEntity

	PlanID   string `gorm:"size:36;not null;index;comment:订阅计划ID"`
	PlanName string `gorm:"size:100;not null;comment:订阅计划名称"`

	// 订阅周期
	StartDate   time.Time `gorm:"not null;comment:开始日期"`
	EndDate     time.Time `gorm:"not null;comment:结束日期"`
	RenewalDate time.Time `gorm:"not null;comment:续费日期"`

	// 计费信息
	Price        decimal.Decimal `gorm:"type:decimal(10,2);comment:价格"`
	Currency     string          `gorm:"size:3;default:'CNY';comment:货币"`
	BillingCycle string          `gorm:"size:20;comment:计费周期"` // monthly, yearly, lifetime

	// 状态
	Status      SubscriptionStatus `gorm:"default:0;comment:订阅状态"`
	AutoRenewal bool               `gorm:"default:true;comment:自动续费"`

	// 使用限制
	UserLimit    int   `gorm:"default:10;comment:用户数限制"`
	StorageLimit int64 `gorm:"default:1024;comment:存储空间限制(MB)"`
	APILimit     int   `gorm:"default:10000;comment:API调用限制"`
	ProductLimit int   `gorm:"default:100;comment:商品数限制"`
	OrderLimit   int   `gorm:"default:1000;comment:订单数限制"`

	// 功能权限
	Features string `gorm:"type:text;comment:功能列表JSON"`

	// 试用信息
	IsTrialPeriod   bool       `gorm:"default:false;comment:是否试用期"`
	TrialEndDate    *time.Time `gorm:"comment:试用结束日期"`
	TrialExtensions int        `gorm:"default:0;comment:试用延期次数"`

	// 取消信息
	CanceledAt     *time.Time `gorm:"comment:取消时间"`
	CancelReason   string     `gorm:"size:500;comment:取消原因"`
	CanceledByUser string     `gorm:"size:36;comment:取消操作用户"`
}

// TableName 指定表名
func (TenantSubscription) TableName() string {
	return "tenant_subscriptions"
}

// NewTenantSubscription 创建新的租户订阅
func NewTenantSubscription(tenantID, planID, planName string, period valueobject.TenantSubscriptionPeriod, pricing valueobject.TenantPricing) (*TenantSubscription, error) {
	if tenantID == "" {
		return nil, errors.New("租户ID不能为空")
	}
	if planID == "" {
		return nil, errors.New("订阅计划ID不能为空")
	}
	if planName == "" {
		return nil, errors.New("订阅计划名称不能为空")
	}

	now := time.Now()

	subscription := &TenantSubscription{
		TenantScopedEntity: types.NewEmptyTenantScopedEntity(),
		PlanID:             planID,
		PlanName:           planName,
		StartDate:          period.StartDate(),
		EndDate:            period.EndDate(),
		RenewalDate:        period.EndDate(),
		Price:              pricing.Price(),
		Currency:           pricing.Currency(),
		BillingCycle:       pricing.Cycle(),
		Status:             SubscriptionStatusPending,
		AutoRenewal:        true,
		UserLimit:          10,
		StorageLimit:       1024,
		APILimit:           10000,
		ProductLimit:       100,
		OrderLimit:         1000,
	}

	// 如果是试用计划，设置试用信息
	if planID == "trial" || planName == "试用版" {
		subscription.IsTrialPeriod = true
		trialEnd := now.AddDate(0, 0, 30) // 30天试用期
		subscription.TrialEndDate = &trialEnd
	}

	return subscription, nil
}

// IsActive 检查订阅是否有效
func (s *TenantSubscription) IsActive() bool {
	return s.Status == SubscriptionStatusActive && !s.IsExpired()
}

// IsExpired 检查订阅是否已过期
func (s *TenantSubscription) IsExpired() bool {
	now := time.Now()
	if s.IsTrialPeriod && s.TrialEndDate != nil {
		return now.After(*s.TrialEndDate)
	}
	return now.After(s.EndDate)
}

// CanRenew 检查是否可以续费
func (s *TenantSubscription) CanRenew() bool {
	return s.Status == SubscriptionStatusActive || s.Status == SubscriptionStatusExpired
}

// DaysUntilExpiry 获取到期剩余天数
func (s *TenantSubscription) DaysUntilExpiry() int {
	now := time.Now()
	var expiryDate time.Time

	if s.IsTrialPeriod && s.TrialEndDate != nil {
		expiryDate = *s.TrialEndDate
	} else {
		expiryDate = s.EndDate
	}

	if now.After(expiryDate) {
		return 0
	}

	return int(expiryDate.Sub(now).Hours() / 24)
}

// Activate 激活订阅
func (s *TenantSubscription) Activate() error {
	if s.Status == SubscriptionStatusCanceled {
		return errors.New("已取消的订阅无法激活")
	}

	s.Status = SubscriptionStatusActive
	return nil
}

// Suspend 暂停订阅
func (s *TenantSubscription) Suspend(reason string) error {
	if s.Status != SubscriptionStatusActive {
		return errors.New("只有有效订阅才能暂停")
	}

	s.Status = SubscriptionStatusSuspended
	s.CancelReason = reason
	now := time.Now()
	s.CanceledAt = &now

	return nil
}

// Resume 恢复订阅
func (s *TenantSubscription) Resume() error {
	if s.Status != SubscriptionStatusSuspended {
		return errors.New("只有暂停的订阅才能恢复")
	}

	s.Status = SubscriptionStatusActive
	s.CanceledAt = nil
	s.CancelReason = ""

	return nil
}

// Cancel 取消订阅
func (s *TenantSubscription) Cancel(reason, canceledByUser string) error {
	if s.Status == SubscriptionStatusCanceled {
		return errors.New("订阅已被取消")
	}

	s.Status = SubscriptionStatusCanceled
	s.AutoRenewal = false
	s.CancelReason = reason
	s.CanceledByUser = canceledByUser
	now := time.Now()
	s.CanceledAt = &now

	return nil
}

// MarkExpired 标记为过期
func (s *TenantSubscription) MarkExpired() error {
	if !s.IsExpired() {
		return errors.New("订阅尚未到期")
	}

	s.Status = SubscriptionStatusExpired
	return nil
}

// Renew 续费订阅
func (s *TenantSubscription) Renew(newPeriod valueobject.TenantSubscriptionPeriod, newPricing valueobject.TenantPricing) error {
	if !s.CanRenew() {
		return errors.New("当前订阅状态不允许续费")
	}

	s.StartDate = newPeriod.StartDate()
	s.EndDate = newPeriod.EndDate()
	s.RenewalDate = newPeriod.EndDate()
	s.Price = newPricing.Price()
	s.Currency = newPricing.Currency()
	s.BillingCycle = newPricing.Cycle()
	s.Status = SubscriptionStatusActive

	// 如果是从试用转正式订阅，清除试用标记
	if s.IsTrialPeriod {
		s.IsTrialPeriod = false
		s.TrialEndDate = nil
	}

	return nil
}

// ExtendTrial 延长试用期
func (s *TenantSubscription) ExtendTrial(days int, maxExtensions int) error {
	if !s.IsTrialPeriod {
		return errors.New("非试用订阅无法延长试用期")
	}

	if s.TrialExtensions >= maxExtensions {
		return errors.New("试用延期次数已达上限")
	}

	if s.TrialEndDate == nil {
		now := time.Now()
		newEndDate := now.AddDate(0, 0, days)
		s.TrialEndDate = &newEndDate
	} else {
		newEndDate := s.TrialEndDate.AddDate(0, 0, days)
		s.TrialEndDate = &newEndDate
	}

	s.TrialExtensions++
	return nil
}

// UpdateQuotaLimits 更新配额限制
func (s *TenantSubscription) UpdateQuotaLimits(userLimit int, storageLimit int64, apiLimit, productLimit, orderLimit int) error {
	if userLimit < 0 {
		return errors.New("用户限制不能为负数")
	}
	if storageLimit < 0 {
		return errors.New("存储限制不能为负数")
	}
	if apiLimit < 0 {
		return errors.New("API限制不能为负数")
	}

	s.UserLimit = userLimit
	s.StorageLimit = storageLimit
	s.APILimit = apiLimit
	s.ProductLimit = productLimit
	s.OrderLimit = orderLimit

	return nil
}

// SetAutoRenewal 设置自动续费
func (s *TenantSubscription) SetAutoRenewal(autoRenewal bool) {
	s.AutoRenewal = autoRenewal
}

// GetFeatures 获取功能列表
func (s *TenantSubscription) GetFeatures() []string {
	if s.Features == "" {
		return []string{}
	}

	// 这里可以实现JSON解析逻辑
	// 简化实现，返回默认功能
	features := []string{}

	switch s.PlanName {
	case "企业版":
		features = []string{"advanced_analytics", "api_access", "priority_support", "custom_integrations"}
	case "专业版":
		features = []string{"analytics", "api_access", "email_support"}
	case "基础版":
		features = []string{"basic_features", "email_support"}
	case "试用版":
		features = []string{"trial_features"}
	}

	return features
}

// HasFeature 检查是否具有特定功能
func (s *TenantSubscription) HasFeature(feature string) bool {
	features := s.GetFeatures()
	for _, f := range features {
		if f == feature {
			return true
		}
	}
	return false
}

// GetExpiryInfo 获取到期信息
func (s *TenantSubscription) GetExpiryInfo() ExpiryInfo {
	daysRemaining := s.DaysUntilExpiry()
	isExpiringSoon := daysRemaining <= 7 && daysRemaining > 0

	var expiryDate time.Time
	if s.IsTrialPeriod && s.TrialEndDate != nil {
		expiryDate = *s.TrialEndDate
	} else {
		expiryDate = s.EndDate
	}

	return ExpiryInfo{
		ExpiryDate:     expiryDate,
		DaysRemaining:  daysRemaining,
		IsExpired:      s.IsExpired(),
		IsExpiringSoon: isExpiringSoon,
		IsTrialPeriod:  s.IsTrialPeriod,
		CanExtendTrial: s.IsTrialPeriod && s.TrialExtensions < 3,
	}
}

// GetBillingInfo 获取计费信息
func (s *TenantSubscription) GetBillingInfo() BillingInfo {
	pricing, _ := valueobject.NewTenantPricing(s.Price, s.Currency, s.BillingCycle)

	return BillingInfo{
		Price:          s.Price,
		Currency:       s.Currency,
		BillingCycle:   s.BillingCycle,
		FormattedPrice: pricing.FormatPrice(),
		NextBillDate:   s.RenewalDate,
		AutoRenewal:    s.AutoRenewal,
	}
}

// ExpiryInfo 到期信息
type ExpiryInfo struct {
	ExpiryDate     time.Time `json:"expiry_date"`
	DaysRemaining  int       `json:"days_remaining"`
	IsExpired      bool      `json:"is_expired"`
	IsExpiringSoon bool      `json:"is_expiring_soon"`
	IsTrialPeriod  bool      `json:"is_trial_period"`
	CanExtendTrial bool      `json:"can_extend_trial"`
}

// BillingInfo 计费信息
type BillingInfo struct {
	Price          decimal.Decimal `json:"price"`
	Currency       string          `json:"currency"`
	BillingCycle   string          `json:"billing_cycle"`
	FormattedPrice string          `json:"formatted_price"`
	NextBillDate   time.Time       `json:"next_bill_date"`
	AutoRenewal    bool            `json:"auto_renewal"`
}
