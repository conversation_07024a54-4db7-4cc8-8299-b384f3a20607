package entity

import (
	"backend/internal/shared/types"
	"errors"
	"time"
)

// TenantQuota 租户配额实体
type TenantQuota struct {
	types.TenantScopedEntity

	// 用户配额
	UserUsed  int `gorm:"default:0;comment:已使用用户数"`
	UserLimit int `gorm:"default:10;comment:用户限制数"`

	// 存储配额 (MB)
	StorageUsed  int64 `gorm:"default:0;comment:已使用存储空间"`
	StorageLimit int64 `gorm:"default:1024;comment:存储空间限制"`

	// API配额 (每月)
	APIUsedMonth  int       `gorm:"default:0;comment:本月已使用API次数"`
	APILimitMonth int       `gorm:"default:10000;comment:月API调用限制"`
	APIResetDate  time.Time `gorm:"comment:API配额重置日期"`

	// 商品配额
	ProductUsed  int `gorm:"default:0;comment:已使用商品数"`
	ProductLimit int `gorm:"default:100;comment:商品限制数"`

	// 订单配额 (每月)
	OrderUsedMonth  int `gorm:"default:0;comment:本月已处理订单数"`
	OrderLimitMonth int `gorm:"default:1000;comment:月订单处理限制"`

	// 文件上传配额 (每月)
	FileUploadUsedMonth  int `gorm:"default:0;comment:本月已上传文件数"`
	FileUploadLimitMonth int `gorm:"default:1000;comment:月文件上传限制"`

	// 邮件发送配额 (每月)
	EmailSentMonth  int `gorm:"default:0;comment:本月已发送邮件数"`
	EmailLimitMonth int `gorm:"default:500;comment:月邮件发送限制"`
}

// TableName 指定表名
func (TenantQuota) TableName() string {
	return "tenant_quotas"
}

// NewTenantQuota 创建新的租户配额
func NewTenantQuota(tenantID string, userLimit int, storageLimit int64, apiLimit int) (*TenantQuota, error) {
	if tenantID == "" {
		return nil, errors.New("租户ID不能为空")
	}
	if userLimit < 0 {
		return nil, errors.New("用户限制不能为负数")
	}
	if storageLimit < 0 {
		return nil, errors.New("存储限制不能为负数")
	}
	if apiLimit < 0 {
		return nil, errors.New("API限制不能为负数")
	}

	now := time.Now()
	// API配额重置日期设置为下个月的1号
	nextMonth := now.AddDate(0, 1, 0)
	resetDate := time.Date(nextMonth.Year(), nextMonth.Month(), 1, 0, 0, 0, 0, nextMonth.Location())

	return &TenantQuota{
		TenantScopedEntity:   types.NewEmptyTenantScopedEntity(),
		UserLimit:            userLimit,
		StorageLimit:         storageLimit,
		APILimitMonth:        apiLimit,
		APIResetDate:         resetDate,
		ProductLimit:         100,  // 默认商品限制
		OrderLimitMonth:      1000, // 默认月订单限制
		FileUploadLimitMonth: 1000, // 默认月文件上传限制
		EmailLimitMonth:      500,  // 默认月邮件限制
	}, nil
}

// CanCreateUser 检查是否可以创建新用户
func (q *TenantQuota) CanCreateUser() bool {
	return q.UserLimit == -1 || q.UserUsed < q.UserLimit
}

// CanCreateProduct 检查是否可以创建新商品
func (q *TenantQuota) CanCreateProduct() bool {
	return q.ProductLimit == -1 || q.ProductUsed < q.ProductLimit
}

// CanMakeAPICall 检查是否可以进行API调用
func (q *TenantQuota) CanMakeAPICall() bool {
	// 检查是否需要重置月度配额
	if q.needResetMonthlyQuota() {
		q.resetMonthlyQuota()
	}
	return q.APILimitMonth == -1 || q.APIUsedMonth < q.APILimitMonth
}

// CanProcessOrder 检查是否可以处理新订单
func (q *TenantQuota) CanProcessOrder() bool {
	if q.needResetMonthlyQuota() {
		q.resetMonthlyQuota()
	}
	return q.OrderLimitMonth == -1 || q.OrderUsedMonth < q.OrderLimitMonth
}

// CanUploadFile 检查是否可以上传文件
func (q *TenantQuota) CanUploadFile() bool {
	if q.needResetMonthlyQuota() {
		q.resetMonthlyQuota()
	}
	return q.FileUploadLimitMonth == -1 || q.FileUploadUsedMonth < q.FileUploadLimitMonth
}

// CanSendEmail 检查是否可以发送邮件
func (q *TenantQuota) CanSendEmail() bool {
	if q.needResetMonthlyQuota() {
		q.resetMonthlyQuota()
	}
	return q.EmailLimitMonth == -1 || q.EmailSentMonth < q.EmailLimitMonth
}

// CanUseStorage 检查是否可以使用指定大小的存储空间
func (q *TenantQuota) CanUseStorage(sizeInMB int64) bool {
	if q.StorageLimit == -1 {
		return true
	}
	return q.StorageUsed+sizeInMB <= q.StorageLimit
}

// ConsumeUserQuota 消耗用户配额
func (q *TenantQuota) ConsumeUserQuota() error {
	if !q.CanCreateUser() {
		return errors.New("用户配额已达上限")
	}
	q.UserUsed++
	return nil
}

// ReleaseUserQuota 释放用户配额
func (q *TenantQuota) ReleaseUserQuota() error {
	if q.UserUsed <= 0 {
		return errors.New("没有可释放的用户配额")
	}
	q.UserUsed--
	return nil
}

// ConsumeProductQuota 消耗商品配额
func (q *TenantQuota) ConsumeProductQuota() error {
	if !q.CanCreateProduct() {
		return errors.New("商品配额已达上限")
	}
	q.ProductUsed++
	return nil
}

// ReleaseProductQuota 释放商品配额
func (q *TenantQuota) ReleaseProductQuota() error {
	if q.ProductUsed <= 0 {
		return errors.New("没有可释放的商品配额")
	}
	q.ProductUsed--
	return nil
}

// ConsumeAPIQuota 消耗API配额
func (q *TenantQuota) ConsumeAPIQuota() error {
	if !q.CanMakeAPICall() {
		return errors.New("API调用配额已达上限")
	}
	q.APIUsedMonth++
	return nil
}

// ConsumeOrderQuota 消耗订单配额
func (q *TenantQuota) ConsumeOrderQuota() error {
	if !q.CanProcessOrder() {
		return errors.New("订单处理配额已达上限")
	}
	q.OrderUsedMonth++
	return nil
}

// ConsumeFileUploadQuota 消耗文件上传配额
func (q *TenantQuota) ConsumeFileUploadQuota() error {
	if !q.CanUploadFile() {
		return errors.New("文件上传配额已达上限")
	}
	q.FileUploadUsedMonth++
	return nil
}

// ConsumeEmailQuota 消耗邮件配额
func (q *TenantQuota) ConsumeEmailQuota() error {
	if !q.CanSendEmail() {
		return errors.New("邮件发送配额已达上限")
	}
	q.EmailSentMonth++
	return nil
}

// ConsumeStorageQuota 消耗存储配额
func (q *TenantQuota) ConsumeStorageQuota(sizeInMB int64) error {
	if !q.CanUseStorage(sizeInMB) {
		return errors.New("存储配额不足")
	}
	q.StorageUsed += sizeInMB
	return nil
}

// ReleaseStorageQuota 释放存储配额
func (q *TenantQuota) ReleaseStorageQuota(sizeInMB int64) error {
	if q.StorageUsed < sizeInMB {
		return errors.New("释放的存储配额超过已使用量")
	}
	q.StorageUsed -= sizeInMB
	return nil
}

// UpdateLimits 更新配额限制
func (q *TenantQuota) UpdateLimits(userLimit int, storageLimit int64, apiLimit, productLimit, orderLimit, fileUploadLimit, emailLimit int) error {
	if userLimit < 0 && userLimit != -1 {
		return errors.New("用户限制必须为正数或-1(无限制)")
	}
	if storageLimit < 0 && storageLimit != -1 {
		return errors.New("存储限制必须为正数或-1(无限制)")
	}
	if apiLimit < 0 && apiLimit != -1 {
		return errors.New("API限制必须为正数或-1(无限制)")
	}

	q.UserLimit = userLimit
	q.StorageLimit = storageLimit
	q.APILimitMonth = apiLimit
	q.ProductLimit = productLimit
	q.OrderLimitMonth = orderLimit
	q.FileUploadLimitMonth = fileUploadLimit
	q.EmailLimitMonth = emailLimit

	return nil
}

// GetUserUtilization 获取用户配额使用率
func (q *TenantQuota) GetUserUtilization() float64 {
	if q.UserLimit == -1 || q.UserLimit == 0 {
		return 0
	}
	return float64(q.UserUsed) / float64(q.UserLimit) * 100
}

// GetStorageUtilization 获取存储配额使用率
func (q *TenantQuota) GetStorageUtilization() float64 {
	if q.StorageLimit == -1 || q.StorageLimit == 0 {
		return 0
	}
	return float64(q.StorageUsed) / float64(q.StorageLimit) * 100
}

// GetAPIUtilization 获取API配额使用率
func (q *TenantQuota) GetAPIUtilization() float64 {
	if q.APILimitMonth == -1 || q.APILimitMonth == 0 {
		return 0
	}
	return float64(q.APIUsedMonth) / float64(q.APILimitMonth) * 100
}

// GetProductUtilization 获取商品配额使用率
func (q *TenantQuota) GetProductUtilization() float64 {
	if q.ProductLimit == -1 || q.ProductLimit == 0 {
		return 0
	}
	return float64(q.ProductUsed) / float64(q.ProductLimit) * 100
}

// IsOverQuota 检查是否超出配额
func (q *TenantQuota) IsOverQuota() bool {
	return !q.CanCreateUser() || !q.CanMakeAPICall() || !q.CanCreateProduct()
}

// GetQuotaAlerts 获取配额告警信息
func (q *TenantQuota) GetQuotaAlerts() []QuotaAlert {
	var alerts []QuotaAlert

	// 用户配额告警
	if userUtilization := q.GetUserUtilization(); userUtilization >= 80 {
		alerts = append(alerts, QuotaAlert{
			Type:        "user",
			Utilization: userUtilization,
			Message:     "用户配额使用已达80%",
			Level:       getAlertLevel(userUtilization),
		})
	}

	// 存储配额告警
	if storageUtilization := q.GetStorageUtilization(); storageUtilization >= 80 {
		alerts = append(alerts, QuotaAlert{
			Type:        "storage",
			Utilization: storageUtilization,
			Message:     "存储配额使用已达80%",
			Level:       getAlertLevel(storageUtilization),
		})
	}

	// API配额告警
	if apiUtilization := q.GetAPIUtilization(); apiUtilization >= 80 {
		alerts = append(alerts, QuotaAlert{
			Type:        "api",
			Utilization: apiUtilization,
			Message:     "API调用配额使用已达80%",
			Level:       getAlertLevel(apiUtilization),
		})
	}

	// 商品配额告警
	if productUtilization := q.GetProductUtilization(); productUtilization >= 80 {
		alerts = append(alerts, QuotaAlert{
			Type:        "product",
			Utilization: productUtilization,
			Message:     "商品配额使用已达80%",
			Level:       getAlertLevel(productUtilization),
		})
	}

	return alerts
}

// needResetMonthlyQuota 检查是否需要重置月度配额
func (q *TenantQuota) needResetMonthlyQuota() bool {
	return time.Now().After(q.APIResetDate)
}

// resetMonthlyQuota 重置月度配额
func (q *TenantQuota) resetMonthlyQuota() {
	q.APIUsedMonth = 0
	q.OrderUsedMonth = 0
	q.FileUploadUsedMonth = 0
	q.EmailSentMonth = 0

	// 设置下个月的重置日期
	now := time.Now()
	nextMonth := now.AddDate(0, 1, 0)
	q.APIResetDate = time.Date(nextMonth.Year(), nextMonth.Month(), 1, 0, 0, 0, 0, nextMonth.Location())
}

// QuotaAlert 配额告警信息
type QuotaAlert struct {
	Type        string  `json:"type"`        // user, storage, api, product
	Utilization float64 `json:"utilization"` // 使用率百分比
	Message     string  `json:"message"`     // 告警消息
	Level       string  `json:"level"`       // warning, critical
}

// getAlertLevel 根据使用率获取告警级别
func getAlertLevel(utilization float64) string {
	if utilization >= 95 {
		return "critical"
	} else if utilization >= 80 {
		return "warning"
	}
	return "info"
}
