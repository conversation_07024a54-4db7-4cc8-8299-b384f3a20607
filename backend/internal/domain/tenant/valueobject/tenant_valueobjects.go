package valueobject

import (
	"errors"
	"fmt"
	"regexp"
	"strings"
	"time"

	"github.com/shopspring/decimal"
)

// TenantID 租户ID值对象
type TenantID struct {
	value string
}

// NewTenantID 创建租户ID值对象
func NewTenantID(value string) (TenantID, error) {
	if value == "" {
		return TenantID{}, errors.New("租户ID不能为空")
	}
	if len(value) > 36 {
		return TenantID{}, errors.New("租户ID长度不能超过36个字符")
	}
	return TenantID{value: value}, nil
}

// String 返回字符串表示
func (t TenantID) String() string {
	return t.value
}

// Equals 比较两个租户ID是否相等
func (t TenantID) Equals(other TenantID) bool {
	return t.value == other.value
}

// IsEmpty 检查是否为空
func (t TenantID) IsEmpty() bool {
	return t.value == ""
}

// TenantType 租户类型枚举
type TenantType string

const (
	TenantTypeSystem       TenantType = "system"       // 系统租户
	TenantTypeEnterprise   TenantType = "enterprise"   // 企业版
	TenantTypeProfessional TenantType = "professional" // 专业版
	TenantTypeBasic        TenantType = "basic"        // 基础版
	TenantTypeTrial        TenantType = "trial"        // 试用版
)

// IsValid 检查租户类型是否有效
func (t TenantType) IsValid() bool {
	switch t {
	case TenantTypeSystem, TenantTypeEnterprise, TenantTypeProfessional, TenantTypeBasic, TenantTypeTrial:
		return true
	default:
		return false
	}
}

// String 返回字符串表示
func (t TenantType) String() string {
	return string(t)
}

// GetDisplayName 获取显示名称
func (t TenantType) GetDisplayName() string {
	switch t {
	case TenantTypeSystem:
		return "系统租户"
	case TenantTypeEnterprise:
		return "企业版"
	case TenantTypeProfessional:
		return "专业版"
	case TenantTypeBasic:
		return "基础版"
	case TenantTypeTrial:
		return "试用版"
	default:
		return "未知"
	}
}

// TenantStatus 租户状态枚举
type TenantStatus int

const (
	TenantStatusInactive   TenantStatus = 0 // 未激活
	TenantStatusActive     TenantStatus = 1 // 正常使用
	TenantStatusSuspended  TenantStatus = 2 // 暂停使用
	TenantStatusExpired    TenantStatus = 3 // 已过期
	TenantStatusTerminated TenantStatus = 4 // 已终止
)

// IsValid 检查租户状态是否有效
func (s TenantStatus) IsValid() bool {
	return s >= TenantStatusInactive && s <= TenantStatusTerminated
}

// String 返回字符串表示
func (s TenantStatus) String() string {
	switch s {
	case TenantStatusInactive:
		return "inactive"
	case TenantStatusActive:
		return "active"
	case TenantStatusSuspended:
		return "suspended"
	case TenantStatusExpired:
		return "expired"
	case TenantStatusTerminated:
		return "terminated"
	default:
		return "unknown"
	}
}

// GetDisplayName 获取显示名称
func (s TenantStatus) GetDisplayName() string {
	switch s {
	case TenantStatusInactive:
		return "未激活"
	case TenantStatusActive:
		return "正常使用"
	case TenantStatusSuspended:
		return "暂停使用"
	case TenantStatusExpired:
		return "已过期"
	case TenantStatusTerminated:
		return "已终止"
	default:
		return "未知状态"
	}
}

// CanTransitionTo 检查是否可以转换到目标状态
func (s TenantStatus) CanTransitionTo(target TenantStatus) bool {
	switch s {
	case TenantStatusInactive:
		return target == TenantStatusActive
	case TenantStatusActive:
		return target == TenantStatusSuspended || target == TenantStatusExpired
	case TenantStatusSuspended:
		return target == TenantStatusActive || target == TenantStatusTerminated
	case TenantStatusExpired:
		return target == TenantStatusActive || target == TenantStatusTerminated
	case TenantStatusTerminated:
		return false // 终止状态不能转换到其他状态
	default:
		return false
	}
}

// TenantDomain 租户域名值对象
type TenantDomain struct {
	domain string
}

// domainRegex 域名正则表达式
var domainRegex = regexp.MustCompile(`^[a-zA-Z0-9]([a-zA-Z0-9\-]{0,61}[a-zA-Z0-9])?(\.[a-zA-Z0-9]([a-zA-Z0-9\-]{0,61}[a-zA-Z0-9])?)*$`)

// NewTenantDomain 创建租户域名值对象
func NewTenantDomain(domain string) (TenantDomain, error) {
	if domain == "" {
		return TenantDomain{}, errors.New("域名不能为空")
	}

	domain = strings.ToLower(strings.TrimSpace(domain))

	if len(domain) > 100 {
		return TenantDomain{}, errors.New("域名长度不能超过100个字符")
	}

	if !domainRegex.MatchString(domain) {
		return TenantDomain{}, errors.New("无效的域名格式")
	}

	return TenantDomain{domain: domain}, nil
}

// String 返回字符串表示
func (d TenantDomain) String() string {
	return d.domain
}

// Equals 比较两个域名是否相等
func (d TenantDomain) Equals(other TenantDomain) bool {
	return d.domain == other.domain
}

// IsEmpty 检查是否为空
func (d TenantDomain) IsEmpty() bool {
	return d.domain == ""
}

// GetSubdomain 获取子域名部分
func (d TenantDomain) GetSubdomain() string {
	parts := strings.Split(d.domain, ".")
	if len(parts) > 0 {
		return parts[0]
	}
	return ""
}

// TenantQuotaLimit 租户配额限制值对象
type TenantQuotaLimit struct {
	userLimit    int
	storageLimit int64 // MB
	apiLimit     int   // 每月
}

// NewTenantQuotaLimit 创建租户配额限制值对象
func NewTenantQuotaLimit(userLimit int, storageLimit int64, apiLimit int) (TenantQuotaLimit, error) {
	if userLimit < 0 {
		return TenantQuotaLimit{}, errors.New("用户限制不能为负数")
	}
	if storageLimit < 0 {
		return TenantQuotaLimit{}, errors.New("存储限制不能为负数")
	}
	if apiLimit < 0 {
		return TenantQuotaLimit{}, errors.New("API限制不能为负数")
	}

	return TenantQuotaLimit{
		userLimit:    userLimit,
		storageLimit: storageLimit,
		apiLimit:     apiLimit,
	}, nil
}

// UserLimit 获取用户限制
func (q TenantQuotaLimit) UserLimit() int {
	return q.userLimit
}

// StorageLimit 获取存储限制
func (q TenantQuotaLimit) StorageLimit() int64 {
	return q.storageLimit
}

// APILimit 获取API限制
func (q TenantQuotaLimit) APILimit() int {
	return q.apiLimit
}

// IsUnlimited 检查是否为无限制
func (q TenantQuotaLimit) IsUnlimited() bool {
	return q.userLimit == -1 || q.storageLimit == -1 || q.apiLimit == -1
}

// TenantContactInfo 租户联系信息值对象
type TenantContactInfo struct {
	email string
	phone string
}

// emailRegex 邮箱正则表达式
var emailRegex = regexp.MustCompile(`^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$`)

// phoneRegex 电话号码正则表达式（支持中国大陆手机号）
var phoneRegex = regexp.MustCompile(`^1[3-9]\d{9}$`)

// NewTenantContactInfo 创建租户联系信息值对象
func NewTenantContactInfo(email, phone string) (TenantContactInfo, error) {
	if email != "" && !emailRegex.MatchString(email) {
		return TenantContactInfo{}, errors.New("无效的邮箱格式")
	}

	if phone != "" && !phoneRegex.MatchString(phone) {
		return TenantContactInfo{}, errors.New("无效的电话号码格式")
	}

	return TenantContactInfo{
		email: email,
		phone: phone,
	}, nil
}

// Email 获取邮箱
func (c TenantContactInfo) Email() string {
	return c.email
}

// Phone 获取电话
func (c TenantContactInfo) Phone() string {
	return c.phone
}

// IsEmpty 检查联系信息是否为空
func (c TenantContactInfo) IsEmpty() bool {
	return c.email == "" && c.phone == ""
}

// TenantSubscriptionPeriod 租户订阅周期值对象
type TenantSubscriptionPeriod struct {
	startDate time.Time
	endDate   time.Time
}

// NewTenantSubscriptionPeriod 创建租户订阅周期值对象
func NewTenantSubscriptionPeriod(startDate, endDate time.Time) (TenantSubscriptionPeriod, error) {
	if startDate.IsZero() {
		return TenantSubscriptionPeriod{}, errors.New("开始日期不能为空")
	}
	if endDate.IsZero() {
		return TenantSubscriptionPeriod{}, errors.New("结束日期不能为空")
	}
	if endDate.Before(startDate) {
		return TenantSubscriptionPeriod{}, errors.New("结束日期不能早于开始日期")
	}

	return TenantSubscriptionPeriod{
		startDate: startDate,
		endDate:   endDate,
	}, nil
}

// StartDate 获取开始日期
func (p TenantSubscriptionPeriod) StartDate() time.Time {
	return p.startDate
}

// EndDate 获取结束日期
func (p TenantSubscriptionPeriod) EndDate() time.Time {
	return p.endDate
}

// IsActive 检查订阅是否在有效期内
func (p TenantSubscriptionPeriod) IsActive() bool {
	now := time.Now()
	return now.After(p.startDate) && now.Before(p.endDate)
}

// IsExpired 检查订阅是否已过期
func (p TenantSubscriptionPeriod) IsExpired() bool {
	return time.Now().After(p.endDate)
}

// DaysRemaining 获取剩余天数
func (p TenantSubscriptionPeriod) DaysRemaining() int {
	if p.IsExpired() {
		return 0
	}
	return int(p.endDate.Sub(time.Now()).Hours() / 24)
}

// Duration 获取订阅持续时间
func (p TenantSubscriptionPeriod) Duration() time.Duration {
	return p.endDate.Sub(p.startDate)
}

// TenantPricing 租户定价值对象
type TenantPricing struct {
	price    decimal.Decimal
	currency string
	cycle    string // monthly, yearly
}

// NewTenantPricing 创建租户定价值对象
func NewTenantPricing(price decimal.Decimal, currency, cycle string) (TenantPricing, error) {
	if price.IsNegative() {
		return TenantPricing{}, errors.New("价格不能为负数")
	}

	if currency == "" {
		currency = "CNY"
	}

	validCurrencies := []string{"CNY", "USD", "EUR", "GBP", "JPY"}
	isValidCurrency := false
	for _, c := range validCurrencies {
		if currency == c {
			isValidCurrency = true
			break
		}
	}
	if !isValidCurrency {
		return TenantPricing{}, fmt.Errorf("不支持的货币类型: %s", currency)
	}

	validCycles := []string{"monthly", "yearly", "lifetime"}
	isValidCycle := false
	for _, c := range validCycles {
		if cycle == c {
			isValidCycle = true
			break
		}
	}
	if !isValidCycle {
		return TenantPricing{}, fmt.Errorf("不支持的计费周期: %s", cycle)
	}

	return TenantPricing{
		price:    price,
		currency: currency,
		cycle:    cycle,
	}, nil
}

// Price 获取价格
func (p TenantPricing) Price() decimal.Decimal {
	return p.price
}

// Currency 获取货币
func (p TenantPricing) Currency() string {
	return p.currency
}

// Cycle 获取计费周期
func (p TenantPricing) Cycle() string {
	return p.cycle
}

// IsMonthly 是否为月付
func (p TenantPricing) IsMonthly() bool {
	return p.cycle == "monthly"
}

// IsYearly 是否为年付
func (p TenantPricing) IsYearly() bool {
	return p.cycle == "yearly"
}

// IsLifetime 是否为终身付费
func (p TenantPricing) IsLifetime() bool {
	return p.cycle == "lifetime"
}

// FormatPrice 格式化价格显示
func (p TenantPricing) FormatPrice() string {
	switch p.currency {
	case "CNY":
		return fmt.Sprintf("¥%s", p.price.StringFixed(2))
	case "USD":
		return fmt.Sprintf("$%s", p.price.StringFixed(2))
	case "EUR":
		return fmt.Sprintf("€%s", p.price.StringFixed(2))
	case "GBP":
		return fmt.Sprintf("£%s", p.price.StringFixed(2))
	case "JPY":
		return fmt.Sprintf("¥%s", p.price.StringFixed(0))
	default:
		return fmt.Sprintf("%s %s", p.price.StringFixed(2), p.currency)
	}
}
