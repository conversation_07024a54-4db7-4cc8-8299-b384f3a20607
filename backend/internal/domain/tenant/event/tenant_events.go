package event

import (
	"backend/internal/domain/tenant/valueobject"
	"time"
)

// TenantEvent 租户领域事件基础接口
type TenantEvent interface {
	GetEventType() string
	GetTenantID() string
	GetTimestamp() time.Time
	GetEventID() string
}

// baseEvent 基础事件结构
type baseEvent struct {
	EventID   string    `json:"event_id"`
	TenantID  string    `json:"tenant_id"`
	Timestamp time.Time `json:"timestamp"`
}

// GetEventID 获取事件ID
func (e baseEvent) GetEventID() string {
	return e.EventID
}

// GetTenantID 获取租户ID
func (e baseEvent) GetTenantID() string {
	return e.TenantID
}

// GetTimestamp 获取事件时间戳
func (e baseEvent) GetTimestamp() time.Time {
	return e.Timestamp
}

// TenantCreatedEvent 租户创建事件
type TenantCreatedEvent struct {
	baseEvent
	TenantName   string                 `json:"tenant_name"`
	Domain       string                 `json:"domain"`
	DisplayName  string                 `json:"display_name"`
	Type         valueobject.TenantType `json:"type"`
	ContactEmail string                 `json:"contact_email"`
	ContactPhone string                 `json:"contact_phone"`
	Industry     string                 `json:"industry"`
	Country      string                 `json:"country"`
	Province     string                 `json:"province"`
	City         string                 `json:"city"`
}

// GetEventType 获取事件类型
func (e TenantCreatedEvent) GetEventType() string {
	return "tenant.created"
}

// NewTenantCreatedEvent 新建租户创建事件
func NewTenantCreatedEvent(tenantID, tenantName, domain, displayName string,
	tenantType valueobject.TenantType, contactEmail, contactPhone, industry, country, province, city string) TenantCreatedEvent {
	return TenantCreatedEvent{
		baseEvent: baseEvent{
			EventID:   generateEventID(),
			TenantID:  tenantID,
			Timestamp: time.Now(),
		},
		TenantName:   tenantName,
		Domain:       domain,
		DisplayName:  displayName,
		Type:         tenantType,
		ContactEmail: contactEmail,
		ContactPhone: contactPhone,
		Industry:     industry,
		Country:      country,
		Province:     province,
		City:         city,
	}
}

// TenantActivatedEvent 租户激活事件
type TenantActivatedEvent struct {
	baseEvent
	TenantName      string    `json:"tenant_name"`
	Domain          string    `json:"domain"`
	PreviousStatus  string    `json:"previous_status"`
	ActivatedAt     time.Time `json:"activated_at"`
	ActivatedByUser string    `json:"activated_by_user,omitempty"`
}

// GetEventType 获取事件类型
func (e TenantActivatedEvent) GetEventType() string {
	return "tenant.activated"
}

// NewTenantActivatedEvent 新建租户激活事件
func NewTenantActivatedEvent(tenantID, tenantName, domain, previousStatus, activatedByUser string) TenantActivatedEvent {
	return TenantActivatedEvent{
		baseEvent: baseEvent{
			EventID:   generateEventID(),
			TenantID:  tenantID,
			Timestamp: time.Now(),
		},
		TenantName:      tenantName,
		Domain:          domain,
		PreviousStatus:  previousStatus,
		ActivatedAt:     time.Now(),
		ActivatedByUser: activatedByUser,
	}
}

// TenantSuspendedEvent 租户暂停事件
type TenantSuspendedEvent struct {
	baseEvent
	TenantName      string    `json:"tenant_name"`
	Domain          string    `json:"domain"`
	Reason          string    `json:"reason"`
	SuspendedAt     time.Time `json:"suspended_at"`
	SuspendedByUser string    `json:"suspended_by_user,omitempty"`
}

// GetEventType 获取事件类型
func (e TenantSuspendedEvent) GetEventType() string {
	return "tenant.suspended"
}

// NewTenantSuspendedEvent 新建租户暂停事件
func NewTenantSuspendedEvent(tenantID, tenantName, domain, reason, suspendedByUser string) TenantSuspendedEvent {
	return TenantSuspendedEvent{
		baseEvent: baseEvent{
			EventID:   generateEventID(),
			TenantID:  tenantID,
			Timestamp: time.Now(),
		},
		TenantName:      tenantName,
		Domain:          domain,
		Reason:          reason,
		SuspendedAt:     time.Now(),
		SuspendedByUser: suspendedByUser,
	}
}

// TenantTerminatedEvent 租户终止事件
type TenantTerminatedEvent struct {
	baseEvent
	TenantName       string    `json:"tenant_name"`
	Domain           string    `json:"domain"`
	Reason           string    `json:"reason"`
	TerminatedAt     time.Time `json:"terminated_at"`
	TerminatedByUser string    `json:"terminated_by_user,omitempty"`
}

// GetEventType 获取事件类型
func (e TenantTerminatedEvent) GetEventType() string {
	return "tenant.terminated"
}

// NewTenantTerminatedEvent 新建租户终止事件
func NewTenantTerminatedEvent(tenantID, tenantName, domain, reason, terminatedByUser string) TenantTerminatedEvent {
	return TenantTerminatedEvent{
		baseEvent: baseEvent{
			EventID:   generateEventID(),
			TenantID:  tenantID,
			Timestamp: time.Now(),
		},
		TenantName:       tenantName,
		Domain:           domain,
		Reason:           reason,
		TerminatedAt:     time.Now(),
		TerminatedByUser: terminatedByUser,
	}
}

// TenantQuotaExceededEvent 租户配额超限事件
type TenantQuotaExceededEvent struct {
	baseEvent
	QuotaType   string  `json:"quota_type"` // user, storage, api, product
	Limit       int64   `json:"limit"`
	Current     int64   `json:"current"`
	Utilization float64 `json:"utilization"`           // 使用率百分比
	AlertLevel  string  `json:"alert_level"`           // warning, critical
	AutoAction  string  `json:"auto_action,omitempty"` // 自动执行的动作
}

// GetEventType 获取事件类型
func (e TenantQuotaExceededEvent) GetEventType() string {
	return "tenant.quota.exceeded"
}

// NewTenantQuotaExceededEvent 新建配额超限事件
func NewTenantQuotaExceededEvent(tenantID, quotaType string, limit, current int64,
	utilization float64, alertLevel, autoAction string) TenantQuotaExceededEvent {
	return TenantQuotaExceededEvent{
		baseEvent: baseEvent{
			EventID:   generateEventID(),
			TenantID:  tenantID,
			Timestamp: time.Now(),
		},
		QuotaType:   quotaType,
		Limit:       limit,
		Current:     current,
		Utilization: utilization,
		AlertLevel:  alertLevel,
		AutoAction:  autoAction,
	}
}

// TenantSubscriptionCreatedEvent 租户订阅创建事件
type TenantSubscriptionCreatedEvent struct {
	baseEvent
	SubscriptionID string    `json:"subscription_id"`
	PlanID         string    `json:"plan_id"`
	PlanName       string    `json:"plan_name"`
	StartDate      time.Time `json:"start_date"`
	EndDate        time.Time `json:"end_date"`
	Price          float64   `json:"price"`
	Currency       string    `json:"currency"`
	BillingCycle   string    `json:"billing_cycle"`
	IsTrialPeriod  bool      `json:"is_trial_period"`
}

// GetEventType 获取事件类型
func (e TenantSubscriptionCreatedEvent) GetEventType() string {
	return "tenant.subscription.created"
}

// NewTenantSubscriptionCreatedEvent 新建订阅创建事件
func NewTenantSubscriptionCreatedEvent(tenantID, subscriptionID, planID, planName string,
	startDate, endDate time.Time, price float64, currency, billingCycle string, isTrialPeriod bool) TenantSubscriptionCreatedEvent {
	return TenantSubscriptionCreatedEvent{
		baseEvent: baseEvent{
			EventID:   generateEventID(),
			TenantID:  tenantID,
			Timestamp: time.Now(),
		},
		SubscriptionID: subscriptionID,
		PlanID:         planID,
		PlanName:       planName,
		StartDate:      startDate,
		EndDate:        endDate,
		Price:          price,
		Currency:       currency,
		BillingCycle:   billingCycle,
		IsTrialPeriod:  isTrialPeriod,
	}
}

// TenantSubscriptionRenewedEvent 租户订阅续费事件
type TenantSubscriptionRenewedEvent struct {
	baseEvent
	SubscriptionID string    `json:"subscription_id"`
	PlanName       string    `json:"plan_name"`
	OldEndDate     time.Time `json:"old_end_date"`
	NewStartDate   time.Time `json:"new_start_date"`
	NewEndDate     time.Time `json:"new_end_date"`
	Price          float64   `json:"price"`
	Currency       string    `json:"currency"`
	BillingCycle   string    `json:"billing_cycle"`
	RenewedByUser  string    `json:"renewed_by_user,omitempty"`
	AutoRenewal    bool      `json:"auto_renewal"`
}

// GetEventType 获取事件类型
func (e TenantSubscriptionRenewedEvent) GetEventType() string {
	return "tenant.subscription.renewed"
}

// NewTenantSubscriptionRenewedEvent 新建订阅续费事件
func NewTenantSubscriptionRenewedEvent(tenantID, subscriptionID, planName string,
	oldEndDate, newStartDate, newEndDate time.Time, price float64, currency, billingCycle, renewedByUser string, autoRenewal bool) TenantSubscriptionRenewedEvent {
	return TenantSubscriptionRenewedEvent{
		baseEvent: baseEvent{
			EventID:   generateEventID(),
			TenantID:  tenantID,
			Timestamp: time.Now(),
		},
		SubscriptionID: subscriptionID,
		PlanName:       planName,
		OldEndDate:     oldEndDate,
		NewStartDate:   newStartDate,
		NewEndDate:     newEndDate,
		Price:          price,
		Currency:       currency,
		BillingCycle:   billingCycle,
		RenewedByUser:  renewedByUser,
		AutoRenewal:    autoRenewal,
	}
}

// TenantSubscriptionCanceledEvent 租户订阅取消事件
type TenantSubscriptionCanceledEvent struct {
	baseEvent
	SubscriptionID string    `json:"subscription_id"`
	PlanName       string    `json:"plan_name"`
	CancelReason   string    `json:"cancel_reason"`
	CanceledAt     time.Time `json:"canceled_at"`
	CanceledByUser string    `json:"canceled_by_user,omitempty"`
	RefundAmount   float64   `json:"refund_amount,omitempty"`
	RefundCurrency string    `json:"refund_currency,omitempty"`
}

// GetEventType 获取事件类型
func (e TenantSubscriptionCanceledEvent) GetEventType() string {
	return "tenant.subscription.canceled"
}

// NewTenantSubscriptionCanceledEvent 新建订阅取消事件
func NewTenantSubscriptionCanceledEvent(tenantID, subscriptionID, planName, cancelReason, canceledByUser string,
	refundAmount float64, refundCurrency string) TenantSubscriptionCanceledEvent {
	return TenantSubscriptionCanceledEvent{
		baseEvent: baseEvent{
			EventID:   generateEventID(),
			TenantID:  tenantID,
			Timestamp: time.Now(),
		},
		SubscriptionID: subscriptionID,
		PlanName:       planName,
		CancelReason:   cancelReason,
		CanceledAt:     time.Now(),
		CanceledByUser: canceledByUser,
		RefundAmount:   refundAmount,
		RefundCurrency: refundCurrency,
	}
}

// TenantSubscriptionExpiredEvent 租户订阅过期事件
type TenantSubscriptionExpiredEvent struct {
	baseEvent
	SubscriptionID string    `json:"subscription_id"`
	PlanName       string    `json:"plan_name"`
	ExpiredAt      time.Time `json:"expired_at"`
	GracePeriod    int       `json:"grace_period_days"` // 宽限期天数
	AutoDowngrade  bool      `json:"auto_downgrade"`    // 是否自动降级
}

// GetEventType 获取事件类型
func (e TenantSubscriptionExpiredEvent) GetEventType() string {
	return "tenant.subscription.expired"
}

// NewTenantSubscriptionExpiredEvent 新建订阅过期事件
func NewTenantSubscriptionExpiredEvent(tenantID, subscriptionID, planName string,
	expiredAt time.Time, gracePeriod int, autoDowngrade bool) TenantSubscriptionExpiredEvent {
	return TenantSubscriptionExpiredEvent{
		baseEvent: baseEvent{
			EventID:   generateEventID(),
			TenantID:  tenantID,
			Timestamp: time.Now(),
		},
		SubscriptionID: subscriptionID,
		PlanName:       planName,
		ExpiredAt:      expiredAt,
		GracePeriod:    gracePeriod,
		AutoDowngrade:  autoDowngrade,
	}
}

// TenantTrialExtendedEvent 租户试用期延长事件
type TenantTrialExtendedEvent struct {
	baseEvent
	SubscriptionID  string    `json:"subscription_id"`
	PreviousEndDate time.Time `json:"previous_end_date"`
	NewEndDate      time.Time `json:"new_end_date"`
	ExtensionDays   int       `json:"extension_days"`
	ExtensionReason string    `json:"extension_reason"`
	ExtendedByUser  string    `json:"extended_by_user,omitempty"`
	ExtensionCount  int       `json:"extension_count"` // 延期次数
	MaxExtensions   int       `json:"max_extensions"`  // 最大延期次数
}

// GetEventType 获取事件类型
func (e TenantTrialExtendedEvent) GetEventType() string {
	return "tenant.trial.extended"
}

// NewTenantTrialExtendedEvent 新建试用期延长事件
func NewTenantTrialExtendedEvent(tenantID, subscriptionID string, previousEndDate, newEndDate time.Time,
	extensionDays int, extensionReason, extendedByUser string, extensionCount, maxExtensions int) TenantTrialExtendedEvent {
	return TenantTrialExtendedEvent{
		baseEvent: baseEvent{
			EventID:   generateEventID(),
			TenantID:  tenantID,
			Timestamp: time.Now(),
		},
		SubscriptionID:  subscriptionID,
		PreviousEndDate: previousEndDate,
		NewEndDate:      newEndDate,
		ExtensionDays:   extensionDays,
		ExtensionReason: extensionReason,
		ExtendedByUser:  extendedByUser,
		ExtensionCount:  extensionCount,
		MaxExtensions:   maxExtensions,
	}
}

// TenantHealthCheckFailedEvent 租户健康检查失败事件
type TenantHealthCheckFailedEvent struct {
	baseEvent
	TenantName     string        `json:"tenant_name"`
	Domain         string        `json:"domain"`
	FailureReasons []string      `json:"failure_reasons"`
	Alerts         []HealthAlert `json:"alerts"`
	LastHealthyAt  *time.Time    `json:"last_healthy_at,omitempty"`
	CheckedAt      time.Time     `json:"checked_at"`
}

// HealthAlert 健康告警
type HealthAlert struct {
	Type     string `json:"type"`     // 告警类型
	Level    string `json:"level"`    // 告警级别
	Message  string `json:"message"`  // 告警消息
	Severity string `json:"severity"` // 严重程度
}

// GetEventType 获取事件类型
func (e TenantHealthCheckFailedEvent) GetEventType() string {
	return "tenant.health.failed"
}

// NewTenantHealthCheckFailedEvent 新建健康检查失败事件
func NewTenantHealthCheckFailedEvent(tenantID, tenantName, domain string, failureReasons []string,
	alerts []HealthAlert, lastHealthyAt *time.Time) TenantHealthCheckFailedEvent {
	return TenantHealthCheckFailedEvent{
		baseEvent: baseEvent{
			EventID:   generateEventID(),
			TenantID:  tenantID,
			Timestamp: time.Now(),
		},
		TenantName:     tenantName,
		Domain:         domain,
		FailureReasons: failureReasons,
		Alerts:         alerts,
		LastHealthyAt:  lastHealthyAt,
		CheckedAt:      time.Now(),
	}
}

// TenantEventFactory 租户事件工厂接口
type TenantEventFactory interface {
	CreateTenantCreatedEvent(tenantID, tenantName, domain, displayName string,
		tenantType valueobject.TenantType, contactEmail, contactPhone, industry, country, province, city string) TenantEvent
	CreateTenantActivatedEvent(tenantID, tenantName, domain, previousStatus, activatedByUser string) TenantEvent
	CreateTenantSuspendedEvent(tenantID, tenantName, domain, reason, suspendedByUser string) TenantEvent
	CreateTenantTerminatedEvent(tenantID, tenantName, domain, reason, terminatedByUser string) TenantEvent
	CreateQuotaExceededEvent(tenantID, quotaType string, limit, current int64,
		utilization float64, alertLevel, autoAction string) TenantEvent
	CreateSubscriptionCreatedEvent(tenantID, subscriptionID, planID, planName string,
		startDate, endDate time.Time, price float64, currency, billingCycle string, isTrialPeriod bool) TenantEvent
	CreateSubscriptionRenewedEvent(tenantID, subscriptionID, planName string,
		oldEndDate, newStartDate, newEndDate time.Time, price float64, currency, billingCycle, renewedByUser string, autoRenewal bool) TenantEvent
	CreateSubscriptionCanceledEvent(tenantID, subscriptionID, planName, cancelReason, canceledByUser string,
		refundAmount float64, refundCurrency string) TenantEvent
	CreateSubscriptionExpiredEvent(tenantID, subscriptionID, planName string,
		expiredAt time.Time, gracePeriod int, autoDowngrade bool) TenantEvent
	CreateTrialExtendedEvent(tenantID, subscriptionID string, previousEndDate, newEndDate time.Time,
		extensionDays int, extensionReason, extendedByUser string, extensionCount, maxExtensions int) TenantEvent
	CreateHealthCheckFailedEvent(tenantID, tenantName, domain string, failureReasons []string,
		alerts []HealthAlert, lastHealthyAt *time.Time) TenantEvent
}

// TenantEventFactoryImpl 租户事件工厂实现
type TenantEventFactoryImpl struct{}

// NewTenantEventFactory 新建租户事件工厂
func NewTenantEventFactory() TenantEventFactory {
	return &TenantEventFactoryImpl{}
}

// CreateTenantCreatedEvent 创建租户创建事件
func (f *TenantEventFactoryImpl) CreateTenantCreatedEvent(tenantID, tenantName, domain, displayName string,
	tenantType valueobject.TenantType, contactEmail, contactPhone, industry, country, province, city string) TenantEvent {
	return NewTenantCreatedEvent(tenantID, tenantName, domain, displayName, tenantType,
		contactEmail, contactPhone, industry, country, province, city)
}

// CreateTenantActivatedEvent 新建租户激活事件
func (f *TenantEventFactoryImpl) CreateTenantActivatedEvent(tenantID, tenantName, domain, previousStatus, activatedByUser string) TenantEvent {
	return NewTenantActivatedEvent(tenantID, tenantName, domain, previousStatus, activatedByUser)
}

// CreateTenantSuspendedEvent 新建租户暂停事件
func (f *TenantEventFactoryImpl) CreateTenantSuspendedEvent(tenantID, tenantName, domain, reason, suspendedByUser string) TenantEvent {
	return NewTenantSuspendedEvent(tenantID, tenantName, domain, reason, suspendedByUser)
}

// CreateTenantTerminatedEvent 新建租户终止事件
func (f *TenantEventFactoryImpl) CreateTenantTerminatedEvent(tenantID, tenantName, domain, reason, terminatedByUser string) TenantEvent {
	return NewTenantTerminatedEvent(tenantID, tenantName, domain, reason, terminatedByUser)
}

// CreateQuotaExceededEvent 新建配额超限事件
func (f *TenantEventFactoryImpl) CreateQuotaExceededEvent(tenantID, quotaType string, limit, current int64,
	utilization float64, alertLevel, autoAction string) TenantEvent {
	return NewTenantQuotaExceededEvent(tenantID, quotaType, limit, current, utilization, alertLevel, autoAction)
}

// CreateSubscriptionCreatedEvent 新建订阅创建事件
func (f *TenantEventFactoryImpl) CreateSubscriptionCreatedEvent(tenantID, subscriptionID, planID, planName string,
	startDate, endDate time.Time, price float64, currency, billingCycle string, isTrialPeriod bool) TenantEvent {
	return NewTenantSubscriptionCreatedEvent(tenantID, subscriptionID, planID, planName,
		startDate, endDate, price, currency, billingCycle, isTrialPeriod)
}

// CreateSubscriptionRenewedEvent 新建订阅续费事件
func (f *TenantEventFactoryImpl) CreateSubscriptionRenewedEvent(tenantID, subscriptionID, planName string,
	oldEndDate, newStartDate, newEndDate time.Time, price float64, currency, billingCycle, renewedByUser string, autoRenewal bool) TenantEvent {
	return NewTenantSubscriptionRenewedEvent(tenantID, subscriptionID, planName,
		oldEndDate, newStartDate, newEndDate, price, currency, billingCycle, renewedByUser, autoRenewal)
}

// CreateSubscriptionCanceledEvent 新建订阅取消事件
func (f *TenantEventFactoryImpl) CreateSubscriptionCanceledEvent(tenantID, subscriptionID, planName, cancelReason, canceledByUser string,
	refundAmount float64, refundCurrency string) TenantEvent {
	return NewTenantSubscriptionCanceledEvent(tenantID, subscriptionID, planName, cancelReason, canceledByUser,
		refundAmount, refundCurrency)
}

// CreateSubscriptionExpiredEvent 新建订阅过期事件
func (f *TenantEventFactoryImpl) CreateSubscriptionExpiredEvent(tenantID, subscriptionID, planName string,
	expiredAt time.Time, gracePeriod int, autoDowngrade bool) TenantEvent {
	return NewTenantSubscriptionExpiredEvent(tenantID, subscriptionID, planName, expiredAt, gracePeriod, autoDowngrade)
}

// CreateTrialExtendedEvent 新建试用期延长事件
func (f *TenantEventFactoryImpl) CreateTrialExtendedEvent(tenantID, subscriptionID string, previousEndDate, newEndDate time.Time,
	extensionDays int, extensionReason, extendedByUser string, extensionCount, maxExtensions int) TenantEvent {
	return NewTenantTrialExtendedEvent(tenantID, subscriptionID, previousEndDate, newEndDate,
		extensionDays, extensionReason, extendedByUser, extensionCount, maxExtensions)
}

// CreateHealthCheckFailedEvent 新建健康检查失败事件
func (f *TenantEventFactoryImpl) CreateHealthCheckFailedEvent(tenantID, tenantName, domain string, failureReasons []string,
	alerts []HealthAlert, lastHealthyAt *time.Time) TenantEvent {
	return NewTenantHealthCheckFailedEvent(tenantID, tenantName, domain, failureReasons, alerts, lastHealthyAt)
}

// 辅助函数

// generateEventID 生成事件ID
func generateEventID() string {
	// 简化实现，实际项目中可以使用UUID或雪花算法
	return time.Now().Format("20060102150405") + "_" + randomString(8)
}

// randomString 生成随机字符串
func randomString(length int) string {
	const charset = "abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789"
	b := make([]byte, length)
	for i := range b {
		b[i] = charset[time.Now().UnixNano()%int64(len(charset))]
	}
	return string(b)
}
