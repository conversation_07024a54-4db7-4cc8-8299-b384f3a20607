package repository

import (
	"context"
	"time"

	"backend/internal/domain/tenant/entity"
	"backend/internal/domain/tenant/valueobject"
)

// TenantRepository 租户仓储接口
type TenantRepository interface {
	// 基础CRUD操作
	Create(ctx context.Context, tenant *entity.Tenant) error
	GetByID(ctx context.Context, id string) (*entity.Tenant, error)
	GetByDomain(ctx context.Context, domain string) (*entity.Tenant, error)
	Update(ctx context.Context, tenant *entity.Tenant) error
	Delete(ctx context.Context, id string) error

	// 查询操作
	List(ctx context.Context, filter TenantFilter, pagination TenantPagination) ([]*entity.Tenant, *TenantListResult, error)
	Count(ctx context.Context, filter TenantFilter) (int64, error)
	Exists(ctx context.Context, id string) (bool, error)
	ExistsByDomain(ctx context.Context, domain string) (bool, error)

	// 新分页接口实现
	FindWithPagination(ctx context.Context, req *PaginationRequest, filter TenantFilter) ([]*entity.Tenant, error)
	CountWithFilter(ctx context.Context, filter TenantFilter) (int64, error)

	// 状态查询
	GetByStatus(ctx context.Context, status valueobject.TenantStatus, pagination TenantPagination) ([]*entity.Tenant, error)
	GetByType(ctx context.Context, tenantType valueobject.TenantType, pagination TenantPagination) ([]*entity.Tenant, error)
	GetExpiring(ctx context.Context, days int) ([]*entity.Tenant, error)
	GetExpired(ctx context.Context) ([]*entity.Tenant, error)

	// 批量操作
	BatchUpdateStatus(ctx context.Context, tenantIDs []string, status valueobject.TenantStatus) error
	BatchDelete(ctx context.Context, tenantIDs []string) error

	// 统计分析
	GetStatistics(ctx context.Context) (*TenantStatistics, error)
	GetStatusDistribution(ctx context.Context) (map[valueobject.TenantStatus]int64, error)
	GetTypeDistribution(ctx context.Context) (map[valueobject.TenantType]int64, error)
}

// TenantQuotaRepository 租户配额仓储接口
type TenantQuotaRepository interface {
	// 基础CRUD操作
	Create(ctx context.Context, quota *entity.TenantQuota) error
	GetByTenantID(ctx context.Context, tenantID string) (*entity.TenantQuota, error)
	Update(ctx context.Context, quota *entity.TenantQuota) error
	Delete(ctx context.Context, tenantID string) error

	// 配额管理
	IncrementUserQuota(ctx context.Context, tenantID string, count int) error
	DecrementUserQuota(ctx context.Context, tenantID string, count int) error
	IncrementStorageQuota(ctx context.Context, tenantID string, sizeInMB int64) error
	DecrementStorageQuota(ctx context.Context, tenantID string, sizeInMB int64) error
	IncrementAPIQuota(ctx context.Context, tenantID string, count int) error

	// 配额查询
	GetQuotaUtilization(ctx context.Context, tenantID string) (*QuotaUtilization, error)
	GetOverQuotaTenants(ctx context.Context) ([]string, error)
	GetQuotaAlerts(ctx context.Context, tenantID string) ([]*entity.QuotaAlert, error)

	// 批量操作
	BatchResetMonthlyQuotas(ctx context.Context) error
	BatchUpdateQuotaLimits(ctx context.Context, tenantIDs []string, limits QuotaLimits) error
}

// TenantSubscriptionRepository 租户订阅仓储接口
type TenantSubscriptionRepository interface {
	// 基础CRUD操作
	Create(ctx context.Context, subscription *entity.TenantSubscription) error
	GetByID(ctx context.Context, id string) (*entity.TenantSubscription, error)
	GetByTenantID(ctx context.Context, tenantID string) (*entity.TenantSubscription, error)
	Update(ctx context.Context, subscription *entity.TenantSubscription) error
	Delete(ctx context.Context, id string) error

	// 订阅查询
	List(ctx context.Context, filter SubscriptionFilter, pagination TenantPagination) ([]*entity.TenantSubscription, *TenantListResult, error)
	GetActiveSubscriptions(ctx context.Context) ([]*entity.TenantSubscription, error)
	GetExpiringSubscriptions(ctx context.Context, days int) ([]*entity.TenantSubscription, error)
	GetExpiredSubscriptions(ctx context.Context) ([]*entity.TenantSubscription, error)
	GetTrialSubscriptions(ctx context.Context) ([]*entity.TenantSubscription, error)
	GetByPlanID(ctx context.Context, planID string) ([]*entity.TenantSubscription, error)

	// 续费管理
	GetRenewableSubscriptions(ctx context.Context, days int) ([]*entity.TenantSubscription, error)
	MarkAsRenewed(ctx context.Context, subscriptionID string, newEndDate time.Time) error

	// 统计分析
	GetSubscriptionStatistics(ctx context.Context) (*SubscriptionStatistics, error)
	GetRevenueStatistics(ctx context.Context, startDate, endDate time.Time) (*RevenueStatistics, error)
}

// TenantFilter 租户过滤条件
type TenantFilter struct {
	Name        string                   `json:"name,omitempty"`
	Domain      string                   `json:"domain,omitempty"`
	Type        valueobject.TenantType   `json:"type,omitempty"`
	Status      valueobject.TenantStatus `json:"status,omitempty"`
	Industry    string                   `json:"industry,omitempty"`
	Country     string                   `json:"country,omitempty"`
	Province    string                   `json:"province,omitempty"`
	City        string                   `json:"city,omitempty"`
	CreatedFrom *time.Time               `json:"created_from,omitempty"`
	CreatedTo   *time.Time               `json:"created_to,omitempty"`
	UpdatedFrom *time.Time               `json:"updated_from,omitempty"`
	UpdatedTo   *time.Time               `json:"updated_to,omitempty"`
	Keywords    string                   `json:"keywords,omitempty"` // 关键词搜索
}

// SubscriptionFilter 订阅过滤条件
type SubscriptionFilter struct {
	TenantID       string                    `json:"tenant_id,omitempty"`
	PlanID         string                    `json:"plan_id,omitempty"`
	PlanName       string                    `json:"plan_name,omitempty"`
	Status         entity.SubscriptionStatus `json:"status,omitempty"`
	IsTrialPeriod  *bool                     `json:"is_trial_period,omitempty"`
	AutoRenewal    *bool                     `json:"auto_renewal,omitempty"`
	Currency       string                    `json:"currency,omitempty"`
	BillingCycle   string                    `json:"billing_cycle,omitempty"`
	ExpiringInDays int                       `json:"expiring_in_days,omitempty"`
	CreatedFrom    *time.Time                `json:"created_from,omitempty"`
	CreatedTo      *time.Time                `json:"created_to,omitempty"`
}

// TenantPagination 分页参数
type TenantPagination struct {
	Page     int    `json:"page" validate:"min=1"`
	PageSize int    `json:"page_size" validate:"min=1,max=100"`
	SortBy   string `json:"sort_by,omitempty"`
	SortDesc bool   `json:"sort_desc,omitempty"`
}

// TenantListResult 列表查询结果
type TenantListResult struct {
	Total       int64 `json:"total"`
	Page        int   `json:"page"`
	PageSize    int   `json:"page_size"`
	TotalPages  int   `json:"total_pages"`
	HasNext     bool  `json:"has_next"`
	HasPrevious bool  `json:"has_previous"`
}

// TenantStatistics 租户统计信息
type TenantStatistics struct {
	TotalTenants     int64                              `json:"total_tenants"`
	ActiveTenants    int64                              `json:"active_tenants"`
	TrialTenants     int64                              `json:"trial_tenants"`
	ExpiredTenants   int64                              `json:"expired_tenants"`
	StatusCounts     map[valueobject.TenantStatus]int64 `json:"status_counts"`
	TypeCounts       map[valueobject.TenantType]int64   `json:"type_counts"`
	GrowthStats      *TenantGrowthStats                 `json:"growth_stats"`
	GeographicStats  map[string]int64                   `json:"geographic_stats"` // 按国家/地区统计
	IndustryStats    map[string]int64                   `json:"industry_stats"`   // 按行业统计
	CreatedThisMonth int64                              `json:"created_this_month"`
	CreatedToday     int64                              `json:"created_today"`
}

// TenantGrowthStats 租户增长统计
type TenantGrowthStats struct {
	Daily   []DailyGrowth   `json:"daily"`   // 日增长
	Monthly []MonthlyGrowth `json:"monthly"` // 月增长
}

// DailyGrowth 日增长统计
type DailyGrowth struct {
	Date       time.Time `json:"date"`
	NewCount   int64     `json:"new_count"`
	TotalCount int64     `json:"total_count"`
}

// MonthlyGrowth 月增长统计
type MonthlyGrowth struct {
	Year       int   `json:"year"`
	Month      int   `json:"month"`
	NewCount   int64 `json:"new_count"`
	TotalCount int64 `json:"total_count"`
}

// QuotaUtilization 配额使用率
type QuotaUtilization struct {
	TenantID           string               `json:"tenant_id"`
	UserUtilization    float64              `json:"user_utilization"`
	StorageUtilization float64              `json:"storage_utilization"`
	APIUtilization     float64              `json:"api_utilization"`
	ProductUtilization float64              `json:"product_utilization"`
	IsOverQuota        bool                 `json:"is_over_quota"`
	Alerts             []*entity.QuotaAlert `json:"alerts"`
}

// QuotaLimits 配额限制设置
type QuotaLimits struct {
	UserLimit       int   `json:"user_limit"`
	StorageLimit    int64 `json:"storage_limit"`
	APILimit        int   `json:"api_limit"`
	ProductLimit    int   `json:"product_limit"`
	OrderLimit      int   `json:"order_limit"`
	FileUploadLimit int   `json:"file_upload_limit"`
	EmailLimit      int   `json:"email_limit"`
}

// SubscriptionStatistics 订阅统计信息
type SubscriptionStatistics struct {
	TotalSubscriptions   int64                               `json:"total_subscriptions"`
	ActiveSubscriptions  int64                               `json:"active_subscriptions"`
	TrialSubscriptions   int64                               `json:"trial_subscriptions"`
	ExpiredSubscriptions int64                               `json:"expired_subscriptions"`
	StatusCounts         map[entity.SubscriptionStatus]int64 `json:"status_counts"`
	PlanCounts           map[string]int64                    `json:"plan_counts"`
	CurrencyCounts       map[string]int64                    `json:"currency_counts"`
	BillingCycleCounts   map[string]int64                    `json:"billing_cycle_counts"`
	ChurnRate            float64                             `json:"churn_rate"`      // 流失率
	RenewalRate          float64                             `json:"renewal_rate"`    // 续费率
	AverageRevenue       float64                             `json:"average_revenue"` // 平均收入
	MRR                  float64                             `json:"mrr"`             // 月经常性收入
	ARR                  float64                             `json:"arr"`             // 年经常性收入
}

// RevenueStatistics 收入统计信息
type RevenueStatistics struct {
	TotalRevenue    float64            `json:"total_revenue"`
	CurrencyRevenue map[string]float64 `json:"currency_revenue"`
	PlanRevenue     map[string]float64 `json:"plan_revenue"`
	MonthlyRevenue  []MonthlyRevenue   `json:"monthly_revenue"`
	DailyRevenue    []DailyRevenue     `json:"daily_revenue"`
	NewRevenue      float64            `json:"new_revenue"`     // 新增收入
	RenewalRevenue  float64            `json:"renewal_revenue"` // 续费收入
	ChurnRevenue    float64            `json:"churn_revenue"`   // 流失收入
}

// MonthlyRevenue 月度收入
type MonthlyRevenue struct {
	Year    int     `json:"year"`
	Month   int     `json:"month"`
	Revenue float64 `json:"revenue"`
	Count   int64   `json:"count"`
}

// DailyRevenue 日收入
type DailyRevenue struct {
	Date    time.Time `json:"date"`
	Revenue float64   `json:"revenue"`
	Count   int64     `json:"count"`
}

// PaginationRequest 新分页请求（临时定义，将来会使用 pagination.Request）
type PaginationRequest struct {
	Page     int    `json:"page"`
	PageSize int    `json:"page_size"`
	SortBy   string `json:"sort_by,omitempty"`
	SortDesc bool   `json:"sort_desc,omitempty"`
}
