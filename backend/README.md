# 九翼跨境电商ERP系统 (Nine Wings Cross-Border E-Commerce ERP)

<div align="center">

![Go Version](https://img.shields.io/badge/Go-1.21+-00ADD8?style=for-the-badge&logo=go)
![PostgreSQL](https://img.shields.io/badge/PostgreSQL-17+-336791?style=for-the-badge&logo=postgresql)
![Redis](https://img.shields.io/badge/Redis-7+-DC382D?style=for-the-badge&logo=redis)
![Docker](https://img.shields.io/badge/Docker-20+-2496ED?style=for-the-badge&logo=docker)
![License](https://img.shields.io/badge/License-MIT-green?style=for-the-badge)

**基于DDD六边形架构的下一代跨境电商ERP解决方案**

[功能特性](#-核心功能) • [快速开始](#-快速开始) • [架构设计](#-架构设计) • [API文档](#-api文档) • [贡献指南](#-贡献指南)

</div>

---

## 📋 项目概述

九翼跨境电商ERP系统是一个现代化的企业资源规划平台，专为跨境电商业务设计。系统采用**领域驱动设计(DDD)**和**六边形架构**，提供高度模块化、可扩展的微服务解决方案。

### 🎯 设计理念

- **领域驱动**：以业务领域为核心，清晰的边界上下文
- **六边形架构**：适配器模式实现技术与业务的完全解耦
- **CQRS分离**：命令查询职责分离，提升系统性能
- **多租户支持**：原生多租户架构，数据完全隔离
- **微服务就绪**：模块化设计，支持渐进式微服务拆分

## 🚀 核心功能

### 👥 用户与权限管理
- 🔐 **多阶段认证**：支持用户名密码 + 短信验证的分段式登录
- 🏢 **多租户架构**：租户级别的数据隔离和权限控制
- 🎭 **RBAC权限模型**：基于Casbin的细粒度权限管理
- 🔄 **JWT Token管理**：Redis缓存 + 黑名单机制的Token管理

### 📦 商品与库存管理
- 🛍️ **商品信息管理**：SKU、变体、分类、属性管理
- 📊 **库存跟踪**：实时库存、安全库存、库存预警
- 🔄 **库存调拨**：多仓库间的库存流转管理
- 📈 **库存分析**：库存周转率、滞销商品分析

### 📋 订单与物流管理
- 🛒 **订单生命周期**：从下单到配送的全流程管理
- 🌍 **跨境物流**：多物流商集成，运费计算
- 📦 **包裹追踪**：实时物流状态更新
- 💰 **订单财务**：订单成本核算、利润分析

### 💼 供应商与采购管理
- 🤝 **供应商管理**：供应商档案、评级、合作历史
- 📝 **采购订单**：自动补货、采购审批流程
- 📊 **采购分析**：供应商表现、采购成本分析
- 🔄 **供应链优化**：库存与采购的智能匹配

### 🌐 跨境电商平台集成
- 🛒 **多平台支持**：Amazon、eBay、Shopify等主流平台
- 📤 **商品同步**：批量上传、价格同步、库存同步
- 📥 **订单导入**：自动获取订单、状态同步
- 📊 **销售分析**：跨平台销售数据统一分析

### 💰 财务与报表管理
- 📊 **财务核算**：收入、成本、利润的精确计算
- 💳 **多币种支持**：汇率管理、多币种结算
- 📈 **报表系统**：销售报表、财务报表、库存报表
- 🔍 **数据分析**：商业智能BI、趋势分析

## 🏗️ 架构设计

### 系统架构图

```mermaid
graph TB
    subgraph "表现层 (Presentation Layer)"
        A[REST API] --> B[GraphQL API]
        B --> C[gRPC API]
    end
    
    subgraph "应用层 (Application Layer)"
        D[用例编排 UseCase] --> E[命令处理 Command]
        E --> F[查询处理 Query]
        F --> G[DTO组装 Assembler]
    end
    
    subgraph "领域层 (Domain Layer)"
        H[实体 Entity] --> I[值对象 ValueObject]
        I --> J[聚合根 AggregateRoot]
        J --> K[领域服务 DomainService]
        K --> L[领域事件 DomainEvent]
    end
    
    subgraph "基础设施层 (Infrastructure Layer)"
        M[数据库 PostgreSQL] --> N[缓存 Redis]
        N --> O[消息队列 Redis Streams]
        O --> P[外部API]
    end
    
    A --> D
    D --> H
    H --> M
```

### 🔧 技术栈

| 层次 | 技术选型 | 用途 |
|------|----------|------|
| **Web框架** | Gin | HTTP路由和中间件 |
| **数据库** | PostgreSQL 17+ | 主数据存储 |
| **缓存** | Redis 7+ | 会话缓存、业务缓存 |
| **消息队列** | Redis Streams | 异步任务处理 |
| **认证授权** | JWT + Casbin | 身份认证和权限控制 |
| **ORM** | GORM | 数据库操作抽象 |
| **日志** | Zap | 结构化日志记录 |
| **配置管理** | Viper | 配置文件和环境变量 |
| **依赖注入** | Wire | 编译时依赖注入 |
| **容器化** | Docker + Docker Compose | 容器化部署 |

### 📁 项目结构

```
backend/
├── cmd/                    # 应用程序入口点
│   ├── api/               # API服务入口
│   ├── migrate/           # 数据库迁移工具
│   └── worker/            # 后台任务处理器
├── internal/              # 内部应用代码
│   ├── domain/            # 🏛️ 领域层：业务核心
│   │   ├── user/          # 用户聚合
│   │   ├── product/       # 商品聚合
│   │   ├── order/         # 订单聚合
│   │   ├── inventory/     # 库存聚合
│   │   ├── finance/       # 财务聚合
│   │   └── security/      # 安全聚合
│   ├── application/       # 🎯 应用层：用例编排
│   │   ├── usecase/       # 业务用例
│   │   ├── command/       # CQRS命令
│   │   ├── query/         # CQRS查询
│   │   └── dto/           # 数据传输对象
│   ├── adapters/          # 🔌 适配器层：外部集成
│   │   ├── http/          # HTTP适配器
│   │   ├── persistence/   # 数据持久化
│   │   ├── external/      # 外部服务集成
│   │   └── messaging/     # 消息队列
│   ├── infrastructure/    # ⚙️ 基础设施层
│   │   └── di/            # 依赖注入配置
│   └── shared/            # 📦 共享代码
│       ├── errors/        # 错误处理
│       ├── types/         # 通用类型
│       └── context/       # 上下文管理
├── pkg/                   # 🧰 公共基础设施包
│   ├── infrastructure/    # 基础设施组件
│   ├── common/            # 通用工具
│   └── types/             # 基础类型定义
├── configs/               # 📋 配置文件
├── migrations/            # 🗃️ 数据库迁移
├── api/                   # 📊 API文档
├── docker/               # 🐳 Docker配置
└── docs/                 # 📚 项目文档
```

## ⚡ 快速开始

### 📋 系统要求

- **Go**: 1.21+
- **PostgreSQL**: 17+
- **Redis**: 7+
- **Docker**: 20+
- **Docker Compose**: 2.0+

### 🛠️ 本地开发环境搭建

#### 1. 克隆项目
```bash
git clone https://github.com/your-org/nine-wings-erp.git
cd nine-wings-erp/backend
```

#### 2. 环境配置
```bash
# 复制环境变量模板
cp .env.example .env.development

# 编辑配置文件
vim .env.development
```

#### 3. 启动基础服务
```bash
# 启动PostgreSQL和Redis
docker-compose -f docker/docker-compose.development.yml up -d postgres redis

# 等待服务启动
make wait-for-services
```

#### 4. 数据库初始化
```bash
# 运行数据库迁移
make migrate-up

# 导入测试数据
make seed-data
```

#### 5. 启动应用
```bash
# 安装依赖
go mod download

# 启动开发服务器
make dev

# 或者使用热重载
make dev-watch
```

#### 6. 验证安装
```bash
# 健康检查
curl http://localhost:8080/health

# 查看API文档
open http://localhost:8080/swagger/index.html
```

### 🚀 生产环境部署

#### Docker部署
```bash
# 构建生产镜像
make docker-build

# 启动生产环境
docker-compose -f docker/docker-compose.production.yml up -d

# 检查服务状态
make docker-status
```

#### Kubernetes部署
```bash
# 应用Kubernetes配置
kubectl apply -f k8s/

# 检查部署状态
kubectl get pods -l app=nine-wings-erp
```

## 🔧 开发指南

### 📝 代码规范

系统遵循严格的DDD开发规范，详见 [开发规则文档](.cursor/rules/九翼跨境电商ERP系统开发规则.mdc)

#### 核心原则
- **Domain层禁止依赖外部技术**：保持业务逻辑的纯净性
- **接口在Domain层定义**：依赖倒置原则
- **统一错误处理**：分层错误码管理
- **测试驱动开发**：80%+单元测试覆盖率

### 🧪 测试

```bash
# 运行所有测试
make test

# 单元测试
make test-unit

# 集成测试
make test-integration

# 生成覆盖率报告
make test-coverage

# 查看测试报告
open coverage.html
```

### 🔍 代码质量检查

```bash
# 静态代码分析
make lint

# 安全漏洞扫描
make security-check

# 代码格式化
make fmt

# 完整质量检查
make quality-check
```

## 📊 API文档

### 🌐 在线文档
- **Swagger UI**: [http://localhost:8080/swagger/index.html](http://localhost:8080/swagger/index.html)
- **Redoc**: [http://localhost:8080/redoc](http://localhost:8080/redoc)

### 🔑 认证方式

```bash
# 1. 第一阶段登录（用户名密码）
POST /api/v1/auth/login
{
  "identity_type": "email",
  "identifier": "<EMAIL>",
  "credential": "password123"
}

# 响应包含预认证Token和可用租户列表
{
  "pre_auth_token": "eyJ...",
  "tenants": [
    {
      "tenant_id": "tenant_123",
      "name": "测试租户"
    }
  ]
}

# 2. 第二阶段选择租户
POST /api/v1/auth/select-tenant
Authorization: Bearer <pre_auth_token>
{
  "tenant_id": "tenant_123"
}

# 获得正式访问Token
{
  "access_token": "eyJ...",
  "refresh_token": "eyJ...",
  "expires_in": 900
}

# 3. 使用访问Token调用API
GET /api/v1/users/profile
Authorization: Bearer <access_token>
```

### 📋 主要API端点

| 功能模块 | 端点 | 描述 |
|----------|------|------|
| **认证** | `POST /api/v1/auth/login` | 用户登录 |
| **认证** | `POST /api/v1/auth/select-tenant` | 选择租户 |
| **认证** | `POST /api/v1/auth/refresh` | 刷新Token |
| **用户** | `GET /api/v1/users` | 用户列表 |
| **用户** | `POST /api/v1/users` | 创建用户 |
| **商品** | `GET /api/v1/products` | 商品列表 |
| **商品** | `POST /api/v1/products` | 创建商品 |
| **订单** | `GET /api/v1/orders` | 订单列表 |
| **订单** | `POST /api/v1/orders` | 创建订单 |
| **库存** | `GET /api/v1/inventory` | 库存查询 |
| **库存** | `PUT /api/v1/inventory/{id}` | 库存调整 |

## 🧰 Makefile命令

### 🚀 开发命令
```bash
make dev                 # 启动开发服务器
make dev-watch          # 启动热重载开发服务器
make build              # 构建应用
make clean              # 清理构建产物
```

### 🗃️ 数据库命令
```bash
make migrate-up         # 执行数据库迁移
make migrate-down       # 回滚数据库迁移
make migrate-reset      # 重置数据库
make seed-data          # 导入测试数据
```

### 🧪 测试命令
```bash
make test               # 运行所有测试
make test-unit          # 运行单元测试
make test-integration   # 运行集成测试
make test-coverage      # 生成覆盖率报告
make test-race          # 运行竞态检测
```

### 🔍 质量检查
```bash
make lint               # 代码静态分析
make fmt                # 代码格式化
make sec-check          # 安全漏洞扫描
make lint-full          # 完整质量检查
```

### 🐳 Docker命令
```bash
make docker-build       # 构建Docker镜像
make docker-run         # 运行Docker容器
make docker-push        # 推送镜像到仓库
make docker-clean       # 清理Docker资源
```

### 📋 实用工具
```bash
make deps               # 安装依赖
make deps-update        # 更新依赖
make swagger            # 生成API文档
make wire               # 生成依赖注入代码
```

## 🔧 配置管理

### 📁 配置文件结构
```
configs/
├── config.yaml           # 主配置文件
├── development.yaml      # 开发环境配置
├── testing.yaml          # 测试环境配置
└── production.yaml       # 生产环境配置
```

### ⚙️ 环境变量配置

关键环境变量：

```bash
# 应用配置
APP_ENV=development
APP_PORT=8080

# 数据库配置
DATABASE_HOST=localhost
DATABASE_PORT=5432
DATABASE_NAME=wings_erp_dev
DATABASE_USERNAME=postgres
DATABASE_PASSWORD=postgres

# Redis配置
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_PASSWORD=
REDIS_DATABASE=0

# JWT配置
JWT_SECRET=your-super-secret-jwt-key
JWT_ACCESS_TOKEN_TTL=15m
JWT_REFRESH_TOKEN_TTL=7d

# 日志配置
LOG_LEVEL=info
LOG_FORMAT=json
```

### 🔐 安全配置

生产环境安全配置检查清单：

- [ ] 更换默认的JWT密钥
- [ ] 设置强密码的数据库连接
- [ ] 启用Redis认证
- [ ] 配置HTTPS证书
- [ ] 设置CORS策略
- [ ] 启用请求限流
- [ ] 配置安全头部

## 📈 监控与运维

### 🔍 健康检查

系统提供完整的健康检查端点：

```bash
# 基础健康检查
curl http://localhost:8080/health

# 详细健康检查
curl http://localhost:8080/health/detailed

# 响应示例
{
  "status": "ok",
  "timestamp": "2024-01-01T12:00:00Z",
  "version": "v1.0.0",
  "checks": {
    "database": {
      "status": "ok",
      "response_time": "2ms"
    },
    "redis": {
      "status": "ok",
      "response_time": "1ms"
    },
    "external_apis": {
      "status": "warning",
      "details": "Amazon API slow response"
    }
  }
}
```

### 📊 性能指标

系统内置Prometheus指标：

- `http_requests_total`: HTTP请求总数
- `http_request_duration_seconds`: HTTP请求耗时
- `database_connections_active`: 活跃数据库连接数
- `cache_hits_total`: 缓存命中次数
- `business_operations_total`: 业务操作计数

### 📋 日志管理

结构化日志示例：

```json
{
  "timestamp": "2024-01-01T12:00:00Z",
  "level": "info",
  "message": "用户登录成功",
  "user_id": "user_123",
  "tenant_id": "tenant_456",
  "ip_address": "*************",
  "user_agent": "Mozilla/5.0...",
  "request_id": "req_789",
  "duration": "150ms"
}
```

## 🤝 贡献指南

### 📋 贡献流程

1. **Fork项目**到你的GitHub账户
2. **创建功能分支**：`git checkout -b feature/ERP-123-new-feature`
3. **提交变更**：`git commit -am 'feat(product): 添加商品批量导入功能'`
4. **推送分支**：`git push origin feature/ERP-123-new-feature`
5. **创建Pull Request**

### 📝 提交规范

遵循[约定式提交](https://www.conventionalcommits.org/zh-hans/)规范：

```
<类型>[可选的作用域]: <描述>

[可选的正文]

[可选的脚注]
```

**类型说明**：
- `feat`: 新功能
- `fix`: 错误修复
- `docs`: 文档更新
- `style`: 代码格式化
- `refactor`: 代码重构
- `test`: 测试相关
- `chore`: 构建流程或辅助工具的变动

### 🔍 代码审查

Pull Request检查清单：

- [ ] 遵循项目代码规范
- [ ] 通过所有自动化测试
- [ ] 测试覆盖率不低于80%
- [ ] 包含必要的文档更新
- [ ] 没有安全漏洞
- [ ] 性能测试通过

## 📚 相关文档

- [API文档](docs/api.md) - 详细的API接口说明
- [架构设计](docs/architecture.md) - 系统架构深度解析
- [部署指南](docs/deployment.md) - 生产环境部署手册
- [开发指南](docs/development.md) - 开发环境搭建和最佳实践
- [测试指南](docs/testing.md) - 测试策略和规范
- [故障排查](docs/troubleshooting.md) - 常见问题和解决方案

## 📄 许可证

本项目采用 [MIT许可证](LICENSE)。

## 🆘 支持与反馈

- **问题报告**: [GitHub Issues](https://github.com/your-org/nine-wings-erp/issues)
- **功能建议**: [GitHub Discussions](https://github.com/your-org/nine-wings-erp/discussions)
- **技术支持**: [<EMAIL>](mailto:<EMAIL>)
- **商务合作**: [<EMAIL>](mailto:<EMAIL>)

---

<div align="center">

**九翼跨境电商ERP - 让跨境电商管理更简单** 

Made with ❤️ by Nine Wings Team

</div> 