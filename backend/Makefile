# 九翼跨境电商ERP系统 - 优化版Makefile
# 支持Docker部署和多环境配置管理

# 项目信息
PROJECT_NAME := nine-wings-erp
VERSION ?= $(shell git describe --tags --always --dirty 2>/dev/null || echo "dev")
BUILD_TIME := $(shell date -u '+%Y-%m-%d_%H:%M:%S')
COMMIT_HASH := $(shell git rev-parse --short HEAD 2>/dev/null || echo "unknown")

# Docker配置
DOCKER_REGISTRY ?= your-registry.com
DOCKER_NAMESPACE ?= nine-wings
DOCKER_IMAGE := $(DOCKER_REGISTRY)/$(DOCKER_NAMESPACE)/$(PROJECT_NAME)
DOCKER_TAG ?= $(VERSION)

# 环境配置
ENV ?= development
CONFIG_PATH := configs
DOCKER_COMPOSE_FILE := docker/docker-compose.yml
DOCKER_COMPOSE_OVERRIDE := docker/docker-compose.$(ENV).yml

# 测试环境快速部署配置
TEST_ENV_PORT ?= 8080
TEST_DB_PORT ?= 5433
TEST_REDIS_PORT ?= 6380

# Go构建配置
GO_MODULE := backend
GO_MAIN_PATH := cmd/api
BINARY_NAME := erp-api
BUILD_FLAGS := -ldflags="-X main.Version=$(VERSION) -X main.BuildTime=$(BUILD_TIME) -X main.CommitHash=$(COMMIT_HASH)"

# 测试配置
TEST_COVERAGE_THRESHOLD := 80
TEST_TIMEOUT := 10m

# 颜色定义
BLUE := \033[34m
GREEN := \033[32m
YELLOW := \033[33m
RED := \033[31m
NC := \033[0m

# ==========================================
# 工具路径配置
# ==========================================

# Go工具路径配置
GOPATH ?= $(shell go env GOPATH)
GOBIN ?= $(shell go env GOBIN)
ifeq ($(GOBIN),)
	GOBIN = $(GOPATH)/bin
endif

# Swagger工具路径
SWAG_BIN ?= $(GOBIN)/swag
SWAGGER_VALIDATOR_BIN ?= $(GOBIN)/swagger

# ==========================================
# 通用函数定义
# ==========================================

# Docker Compose通用执行函数
define docker-compose-exec
	@export VERSION="$(VERSION)" BUILD_TIME="$(BUILD_TIME)" COMMIT_HASH="$(COMMIT_HASH)"; \
	if [ -f "$(DOCKER_COMPOSE_OVERRIDE)" ]; then \
		docker-compose -f $(DOCKER_COMPOSE_FILE) -f $(DOCKER_COMPOSE_OVERRIDE) $(1); \
	else \
		docker-compose -f $(DOCKER_COMPOSE_FILE) $(1); \
	fi
endef

# 通用构建函数: $(1)=GOOS $(2)=GOARCH $(3)=输出文件名
define build-binary
	@echo "$(BLUE)构建 $(3) ($(1)/$(2))...$(NC)"
	@GOOS=$(1) GOARCH=$(2) go build $(BUILD_FLAGS) -o bin/$(3) $(GO_MAIN_PATH)/main.go
	@echo "$(GREEN)构建完成: bin/$(3)$(NC)"
endef

# 检查命令是否存在
define check-command
	@command -v $(1) >/dev/null 2>&1 || { echo "$(RED)❌ $(1) 未安装$(NC)"; exit 1; }
endef

# ==========================================
# 增强的检查函数
# ==========================================

# 检查Go工具是否存在，支持多种查找方式
define check-go-tool
	@if command -v $(1) >/dev/null 2>&1; then \
		echo "$(GREEN)✅ 找到$(1)工具$(NC)"; \
	elif [ -f "$(GOBIN)/$(1)" ]; then \
		echo "$(GREEN)✅ 在GOBIN中找到$(1)工具$(NC)"; \
		export PATH="$(GOBIN):$$PATH"; \
	else \
		echo "$(RED)❌ $(1) 工具未找到$(NC)"; \
		echo "$(YELLOW)💡 尝试安装: make tools$(NC)"; \
		exit 1; \
	fi
endef

# 智能执行Go工具命令
define exec-go-tool
	@if command -v $(1) >/dev/null 2>&1; then \
		$(1) $(2); \
	elif [ -f "$(GOBIN)/$(1)" ]; then \
		PATH="$(GOBIN):$$PATH" $(1) $(2); \
	else \
		echo "$(RED)❌ $(1) 工具未找到，正在尝试安装...$(NC)"; \
		$(MAKE) swagger-install; \
		PATH="$(GOBIN):$$PATH" $(1) $(2); \
	fi
endef

# ==========================================
# 帮助信息
# ==========================================

.PHONY: help help-dev help-ops help-test help-all
help: ## 显示常用命令
	@echo "$(BLUE)九翼跨境电商ERP系统 - 常用命令$(NC)"
	@echo ""
	@echo "$(GREEN)🚀 快速开始:$(NC)"
	@echo "  make dev              启动开发环境"
	@echo "  make test-unit        运行单元测试"
	@echo "  make pre-commit       提交前检查"
	@echo "  make quick-build      快速构建"
	@echo ""
	@echo "$(GREEN)🧪 测试环境部署:$(NC)"
	@echo "  make test-deploy      部署本地测试环境"
	@echo "  make test-status      查看测试环境状态"
	@echo "  make test-logs        查看测试环境日志"
	@echo "  make test-clean       清理测试环境"
	@echo ""
	@echo "$(YELLOW)📚 查看更多命令:$(NC)"
	@echo "  make help-dev         开发相关命令"
	@echo "  make help-ops         运维相关命令"
	@echo "  make help-test        测试相关命令"
	@echo "  make help-all         所有命令列表"

help-dev: ## 显示开发命令
	@echo "$(BLUE)📝 开发相关命令$(NC)"
	@echo ""
	@awk 'BEGIN {FS = ":.*?## "} /^[a-zA-Z_-]+:.*?## / && (/fmt|lint|deps|swagger|mock|tools|pre-commit/) {printf "  $(GREEN)%-20s$(NC) %s\n", $$1, $$2}' $(MAKEFILE_LIST)

help-ops: ## 显示运维命令
	@echo "$(BLUE)⚙️  运维相关命令$(NC)"
	@echo ""
	@awk 'BEGIN {FS = ":.*?## "} /^[a-zA-Z_-]+:.*?## / && (/deploy|docker|migrate|setup|up|down|build/) {printf "  $(GREEN)%-20s$(NC) %s\n", $$1, $$2}' $(MAKEFILE_LIST)

help-test: ## 显示测试命令
	@echo "$(BLUE)🧪 测试相关命令$(NC)"
	@echo ""
	@awk 'BEGIN {FS = ":.*?## "} /^[a-zA-Z_-]+:.*?## / && /test/ {printf "  $(GREEN)%-20s$(NC) %s\n", $$1, $$2}' $(MAKEFILE_LIST)

help-all: ## 显示所有命令
	@echo "$(BLUE)📋 所有可用命令$(NC)"
	@echo ""
	@awk 'BEGIN {FS = ":.*?## "} /^[a-zA-Z_-]+:.*?## / {printf "  $(GREEN)%-20s$(NC) %s\n", $$1, $$2}' $(MAKEFILE_LIST)

# ==========================================
# 环境管理 (优化后)
# ==========================================

.PHONY: setup env env-status
setup: ## 设置指定环境 (使用 ENV=环境名)
	@echo "$(BLUE)设置 $(ENV) 环境...$(NC)"
	@$(MAKE) env ENV=$(ENV)
	@echo "$(GREEN)环境 $(ENV) 设置完成$(NC)"

env: ## 设置环境变量 (使用 ENV=环境名)
	@echo "$(BLUE)配置环境: $(ENV)$(NC)"
	@if [ ! -f ".env.$(ENV)" ]; then \
		echo "$(YELLOW)警告: .env.$(ENV) 不存在，将从 .env.example 创建$(NC)"; \
		cp .env.example .env.$(ENV) 2>/dev/null || true; \
		sed -i.bak 's/^APP_ENV=.*/APP_ENV=$(ENV)/' .env.$(ENV) && rm -f .env.$(ENV).bak; \
	fi
	@cp .env.$(ENV) .env
	@echo "$(GREEN)✅ 环境 $(ENV) 已激活$(NC)"

env-status: ## 显示当前环境状态
	@echo "$(BLUE)当前环境状态:$(NC)"
	@if [ -f .env ]; then \
		grep "^APP_ENV=" .env | sed 's/^/  /' || echo "  环境: 未设置"; \
		echo "  配置文件: .env (存在)"; \
	else \
		echo "  配置文件: .env (不存在)"; \
	fi
	@echo "  版本: $(VERSION)"
	@echo "  构建时间: $(BUILD_TIME)"
	@echo "  提交哈希: $(COMMIT_HASH)"

# 便捷环境命令
.PHONY: dev test-env prod start stop
dev: ENV=development ## 启动开发环境
dev: setup
	@$(MAKE) docker-up
	@echo "$(GREEN)🎉 开发环境已启动!$(NC)"
	@echo "API服务: http://localhost:8080"
	@echo "Swagger文档: http://localhost:8080/swagger/index.html"

test-env: ENV=testing ## 启动测试环境
test-env: setup
	@$(MAKE) docker-up

prod: ENV=production ## 启动生产环境  
prod: setup
	@$(MAKE) docker-up

start: dev ## 启动服务 (dev的别名)
stop: docker-down ## 停止服务

# ==========================================
# Docker服务管理 (统一后)
# ==========================================

.PHONY: docker-up docker-down docker-restart docker-logs docker-status docker-clean
docker-up: ## 启动Docker服务
	@echo "$(BLUE)启动Docker服务...$(NC)"
	$(call docker-compose-exec,up -d --build)

docker-down: ## 停止Docker服务
	@echo "$(BLUE)停止Docker服务...$(NC)"
	$(call docker-compose-exec,down)

docker-restart: docker-down docker-up ## 重启Docker服务

docker-logs: ## 查看Docker服务日志
	$(call docker-compose-exec,logs -f)

docker-status: ## 查看Docker服务状态
	$(call docker-compose-exec,ps)

docker-clean: ## 清理Docker资源
	@echo "$(BLUE)清理Docker资源...$(NC)"
	$(call docker-compose-exec,down -v --remove-orphans)
	@docker image prune -f
	@docker volume prune -f
	@echo "$(GREEN)Docker清理完成$(NC)"

# 保持向后兼容的别名
.PHONY: up down restart logs status
up: docker-up
down: docker-down  
restart: docker-restart
logs: docker-logs
status: docker-status

# ==========================================
# 构建系统 (参数化)
# ==========================================

.PHONY: build build-linux build-darwin build-windows build-all quick-build
build: ## 构建当前平台版本
	$(call build-binary,$(shell go env GOOS),$(shell go env GOARCH),$(BINARY_NAME))

build-linux: ## 构建Linux版本
	$(call build-binary,linux,amd64,$(BINARY_NAME)-linux-amd64)

build-darwin: ## 构建macOS版本
	$(call build-binary,darwin,amd64,$(BINARY_NAME)-darwin-amd64)
	$(call build-binary,darwin,arm64,$(BINARY_NAME)-darwin-arm64)

build-windows: ## 构建Windows版本
	$(call build-binary,windows,amd64,$(BINARY_NAME)-windows-amd64.exe)

build-all: build-linux build-darwin build-windows ## 构建所有平台版本

quick-build: ## 快速构建当前平台版本
	$(call build-binary,$(shell go env GOOS),$(shell go env GOARCH),$(BINARY_NAME))

# ==========================================
# Docker镜像构建
# ==========================================

.PHONY: docker-build docker-push docker-build-multi
docker-build: ## 构建Docker镜像
	@echo "$(BLUE)构建Docker镜像...$(NC)"
	@docker build \
		--build-arg VERSION=$(VERSION) \
		--build-arg BUILD_TIME=$(BUILD_TIME) \
		--build-arg COMMIT_HASH=$(COMMIT_HASH) \
		-t $(DOCKER_IMAGE):$(DOCKER_TAG) \
		-t $(DOCKER_IMAGE):latest \
		-f docker/Dockerfile \
		.
	@echo "$(GREEN)Docker镜像构建完成: $(DOCKER_IMAGE):$(DOCKER_TAG)$(NC)"

docker-push: docker-build ## 推送Docker镜像到仓库
	@echo "$(BLUE)推送Docker镜像...$(NC)"
	@docker push $(DOCKER_IMAGE):$(DOCKER_TAG)
	@docker push $(DOCKER_IMAGE):latest
	@echo "$(GREEN)Docker镜像推送完成$(NC)"

docker-build-multi: ## 构建多架构Docker镜像
	@echo "$(BLUE)构建多架构Docker镜像...$(NC)"
	@docker buildx build \
		--platform linux/amd64,linux/arm64 \
		--build-arg VERSION=$(VERSION) \
		--build-arg BUILD_TIME=$(BUILD_TIME) \
		--build-arg COMMIT_HASH=$(COMMIT_HASH) \
		-t $(DOCKER_IMAGE):$(DOCKER_TAG) \
		-t $(DOCKER_IMAGE):latest \
		-f docker/Dockerfile \
		--push \
		.

# ==========================================
# 测试体系 (测试金字塔)
# ==========================================

.PHONY: test-unit test-integration test-e2e test-all test-local test-parallel
test-unit: ## 运行单元测试 (80%)
	@echo "$(BLUE)🧪 运行单元测试...$(NC)"
	@go test -v -short -race -timeout=$(TEST_TIMEOUT) \
		./internal/domain/... \
		./internal/application/... \
		./pkg/...
	@echo "$(GREEN)✅ 单元测试完成$(NC)"

test-integration: ## 运行集成测试 (15%)
	@echo "$(BLUE)🔧 运行集成测试...$(NC)"
	@go test -v -race -timeout=$(TEST_TIMEOUT) \
		./test/integration/...
	@echo "$(GREEN)✅ 集成测试完成$(NC)"

test-e2e: ## 运行端到端测试 (5%)
	@echo "$(BLUE)🌐 运行端到端测试...$(NC)"
	@go test -v -timeout=$(TEST_TIMEOUT) \
		./test/e2e/...
	@echo "$(GREEN)✅ 端到端测试完成$(NC)"

test-all: test-unit test-integration test-e2e ## 运行所有测试

test-local: ## 本地快速测试
	@echo "$(BLUE)🚀 运行本地快速测试...$(NC)"
	@go test -v ./...

test-parallel: ## 并行运行测试
	@echo "$(BLUE)⚡ 并行运行测试...$(NC)"
	@go test -v -race -parallel 4 -timeout=$(TEST_TIMEOUT) ./...

# ==========================================
# 测试覆盖率
# ==========================================

.PHONY: test-cover test-cover-html test-cover-check
test-cover: ## 生成测试覆盖率报告
	@echo "$(BLUE)📊 生成测试覆盖率报告...$(NC)"
	@go test -v -race -coverprofile=coverage.out ./...
	@go tool cover -func=coverage.out
	@echo "$(GREEN)覆盖率报告已生成$(NC)"

test-cover-html: test-cover ## 生成HTML覆盖率报告
	@go tool cover -html=coverage.out -o coverage.html
	@echo "$(GREEN)HTML覆盖率报告: coverage.html$(NC)"

test-cover-check: ## 检查测试覆盖率是否达标
	@echo "$(BLUE)🎯 检查测试覆盖率...$(NC)"
	@go test -race -coverprofile=coverage.out ./...
	@coverage=$$(go tool cover -func=coverage.out | grep total | awk '{print $$3}' | sed 's/%//'); \
	echo "当前覆盖率: $$coverage%"; \
	if [ $$(echo "$$coverage < $(TEST_COVERAGE_THRESHOLD)" | bc -l) -eq 1 ]; then \
		echo "$(RED)❌ 覆盖率$$coverage%低于$(TEST_COVERAGE_THRESHOLD)%标准$(NC)"; \
		exit 1; \
	else \
		echo "$(GREEN)✅ 覆盖率$$coverage%达标$(NC)"; \
	fi

# ==========================================
# Mock代码生成
# ==========================================

.PHONY: mock-gen mock-clean
mock-gen: ## 生成Mock代码
	@echo "$(BLUE)🎭 生成Mock代码...$(NC)"
	@go generate ./...
	@echo "$(GREEN)✅ Mock代码生成完成$(NC)"

mock-clean: ## 清理Mock代码
	@echo "$(BLUE)🧹 清理Mock代码...$(NC)"
	@find . -name "*_mock.go" -type f -delete
	@echo "$(GREEN)✅ Mock代码清理完成$(NC)"

# ==========================================
# 代码质量和安全
# ==========================================

.PHONY: fmt lint lint-full sec-check vuln-check pre-commit
fmt: ## 格式化Go代码
	@echo "$(BLUE)🎨 格式化Go代码...$(NC)"
	@go fmt ./...
	@echo "$(GREEN)✅ 代码格式化完成$(NC)"

lint: ## 基础代码检查
	@echo "$(BLUE)🔍 运行基础代码检查...$(NC)"
	$(call check-command,golangci-lint)
	@golangci-lint run
	@echo "$(GREEN)✅ 基础代码检查完成$(NC)"

lint-full: ## 完整代码检查 (包含复杂度分析)
	@echo "$(BLUE)🔬 运行完整代码检查...$(NC)"
	$(call check-command,golangci-lint)
	@golangci-lint run --enable-all --disable=exhaustivestruct,exhaustruct,gochecknoglobals
	@if command -v gocyclo >/dev/null 2>&1; then \
		gocyclo -over 10 .; \
	else \
		echo "$(YELLOW)⚠️  gocyclo未安装，跳过复杂度检查$(NC)"; \
	fi
	@echo "$(GREEN)✅ 完整代码检查完成$(NC)"

sec-check: ## 安全扫描
	@echo "$(BLUE)🔒 运行安全扫描...$(NC)"
	@if command -v gosec >/dev/null 2>&1; then \
		gosec ./...; \
	else \
		echo "$(YELLOW)⚠️  gosec未安装，跳过安全扫描$(NC)"; \
		echo "安装: go install github.com/securecodewarrior/gosec/v2/cmd/gosec@latest"; \
	fi
	@echo "$(GREEN)✅ 安全扫描完成$(NC)"

vuln-check: ## 依赖漏洞检查
	@echo "$(BLUE)🛡️  检查依赖漏洞...$(NC)"
	@if command -v govulncheck >/dev/null 2>&1; then \
		govulncheck ./...; \
	else \
		echo "$(YELLOW)⚠️  govulncheck未安装，跳过漏洞检查$(NC)"; \
		echo "安装: go install golang.org/x/vuln/cmd/govulncheck@latest"; \
	fi
	@echo "$(GREEN)✅ 漏洞检查完成$(NC)"

pre-commit: fmt lint test-unit ## 提交前检查
	@echo "$(GREEN)✅ 提交前检查通过，可以安全提交！$(NC)"

# ==========================================
# 开发工具管理 (更新版)
# ==========================================

.PHONY: deps tools tools-full tools-test tools-quality
deps: ## 安装Go依赖
	@echo "$(BLUE)📦 安装Go依赖...$(NC)"
	@go mod download
	@go mod tidy
	@echo "$(GREEN)✅ 依赖安装完成$(NC)"

tools: ## 安装基础开发工具
	@echo "$(BLUE)🔧 安装基础开发工具...$(NC)"
	@echo "安装到: $(GOBIN)"
	@go install github.com/golangci/golangci-lint/cmd/golangci-lint@latest
	@go install github.com/swaggo/swag/cmd/swag@latest
	@if [ -f "$(GOBIN)/golangci-lint" ] && [ -f "$(GOBIN)/swag" ]; then \
		echo "$(GREEN)✅ 基础工具安装完成$(NC)"; \
		echo "golangci-lint: $$($(GOBIN)/golangci-lint --version)"; \
		echo "swag: $$($(GOBIN)/swag --version)"; \
	else \
		echo "$(RED)❌ 工具安装可能失败$(NC)"; \
	fi

tools-test: ## 安装测试相关工具
	@echo "$(BLUE)🧪 安装测试工具...$(NC)"
	@go install github.com/golang/mock/mockgen@latest
	@go install golang.org/x/vuln/cmd/govulncheck@latest
	@echo "$(GREEN)✅ 测试工具安装完成$(NC)"

tools-quality: ## 安装代码质量工具
	@echo "$(BLUE)📊 安装代码质量工具...$(NC)"
	@go install github.com/securecodewarrior/gosec/v2/cmd/gosec@latest
	@go install github.com/fzipp/gocyclo/cmd/gocyclo@latest
	@echo "$(GREEN)✅ 质量工具安装完成$(NC)"

tools-swagger: swagger-install ## 仅安装Swagger相关工具
	@echo "$(BLUE)📚 安装Swagger相关工具...$(NC)"
	@go install github.com/go-swagger/go-swagger/cmd/swagger@latest
	@echo "$(GREEN)✅ Swagger工具安装完成$(NC)"

tools-full: tools tools-test tools-quality ## 安装所有开发工具

# ==========================================
# 便捷命令
# ==========================================

.PHONY: quick-test quick-deploy
quick-test: test-unit lint ## 快速测试(单元测试+代码检查)
	@echo "$(GREEN)🚀 快速测试完成$(NC)"

quick-deploy: pre-commit docker-build ## 快速部署(检查+构建)
	@echo "$(GREEN)🚀 快速部署准备完成$(NC)"

# ==========================================
# 数据库迁移
# ==========================================

.PHONY: migrate-create migrate-up migrate-down migrate-status migrate-version migrate-reset
migrate-create: ## 创建迁移文件 (例: make migrate-create name=add_user_email)
	@echo "$(BLUE)📝 创建迁移文件: [$(name)]$(NC)"
	@if [ -z "$(name)" ]; then \
		echo "$(RED)❌ 请提供迁移文件名称$(NC)"; \
		echo "使用方法: make migrate-create name=your_migration_name"; \
		exit 1; \
	fi
	@go run ./cmd/migrate/main.go create $(name)

migrate-up: ## 应用数据库迁移
	@echo "$(BLUE)📈 应用数据库迁移...$(NC)"
	@go run ./cmd/migrate/main.go up
	@echo "$(GREEN)✅ 数据库迁移完成$(NC)"

migrate-down: ## 回滚数据库迁移
	@echo "$(BLUE)📉 回滚数据库迁移...$(NC)"
	@go run ./cmd/migrate/main.go down
	@echo "$(GREEN)✅ 数据库迁移回滚完成$(NC)"

migrate-status: ## 检查迁移状态
	@echo "$(BLUE)📊 检查数据库迁移状态...$(NC)"
	@go run ./cmd/migrate/main.go status

migrate-version: ## 显示数据库版本
	@echo "$(BLUE)🏷️  检查数据库版本...$(NC)"
	@go run ./cmd/migrate/main.go version

migrate-reset: ## 重置数据库迁移 (危险操作)
	@echo "$(RED)⚠️  警告: 这将删除所有数据!$(NC)"
	@read -p "确认要重置数据库吗? (y/N): " confirm; \
	if [ "$$confirm" = "y" ] || [ "$$confirm" = "Y" ]; then \
		echo "$(BLUE)🔄 重置数据库...$(NC)"; \
		go run ./cmd/migrate/main.go reset; \
		echo "$(GREEN)✅ 数据库重置完成$(NC)"; \
	else \
		echo "$(YELLOW)❌ 操作已取消$(NC)"; \
	fi

# ==========================================
# Swagger文档 (优化版)
# ==========================================

.PHONY: swagger-install swagger-gen swagger-validate swagger-serve swagger-export swagger-clean swagger-check-env
swagger-check-env: ## 检查Swagger环境和工具
	@echo "$(BLUE)🔍 检查Swagger环境...$(NC)"
	@echo "GOPATH: $(GOPATH)"
	@echo "GOBIN: $(GOBIN)"
	@echo "PATH: $$PATH"
	@if command -v go >/dev/null 2>&1; then \
		echo "$(GREEN)✅ Go环境正常$(NC)"; \
		go version; \
	else \
		echo "$(RED)❌ Go环境未配置$(NC)"; \
		exit 1; \
	fi
	@if command -v swag >/dev/null 2>&1; then \
		echo "$(GREEN)✅ swag工具在PATH中$(NC)"; \
		swag --version; \
	elif [ -f "$(SWAG_BIN)" ]; then \
		echo "$(GREEN)✅ swag工具在GOBIN中: $(SWAG_BIN)$(NC)"; \
		$(SWAG_BIN) --version; \
	else \
		echo "$(YELLOW)⚠️  swag工具未安装$(NC)"; \
	fi

swagger-install: ## 安装Swagger生成工具
	@echo "$(BLUE)📚 安装Swagger生成工具...$(NC)"
	@echo "安装路径: $(GOBIN)"
	@go install github.com/swaggo/swag/cmd/swag@latest
	@if [ -f "$(SWAG_BIN)" ]; then \
		echo "$(GREEN)✅ Swagger工具安装成功: $(SWAG_BIN)$(NC)"; \
		$(SWAG_BIN) --version; \
	else \
		echo "$(RED)❌ Swagger工具安装失败$(NC)"; \
		exit 1; \
	fi

swagger-gen: ## 生成Swagger文档
	@echo "$(BLUE)📖 生成Swagger文档...$(NC)"
	@$(call check-go-tool,swag)
	@mkdir -p api/openapi
	$(call exec-go-tool,swag,init -g cmd/api/main.go -o api/openapi --parseInternal --parseDependency --parseDepth 2)
	@if [ -f "api/openapi/swagger.json" ]; then \
		echo "$(GREEN)✅ Swagger文档生成完成: api/openapi/swagger.json$(NC)"; \
		echo "📱 访问地址: http://localhost:8080/swagger/index.html"; \
	else \
		echo "$(RED)❌ Swagger文档生成失败$(NC)"; \
		exit 1; \
	fi

swagger-gen-force: swagger-install swagger-gen ## 强制重新安装工具并生成文档

swagger-validate: ## 验证Swagger文档
	@echo "$(BLUE)🔍 验证Swagger文档...$(NC)"
	@if [ ! -f "api/openapi/swagger.json" ]; then \
		echo "$(YELLOW)⚠️  文档不存在，先生成文档...$(NC)"; \
		$(MAKE) swagger-gen; \
	fi
	@if command -v swagger >/dev/null 2>&1; then \
		swagger validate api/openapi/swagger.json; \
		echo "$(GREEN)✅ Swagger文档验证通过$(NC)"; \
	elif [ -f "$(SWAGGER_VALIDATOR_BIN)" ]; then \
		$(SWAGGER_VALIDATOR_BIN) validate api/openapi/swagger.json; \
		echo "$(GREEN)✅ Swagger文档验证通过$(NC)"; \
	else \
		echo "$(YELLOW)⚠️  swagger验证工具未安装，跳过验证$(NC)"; \
		echo "可选安装: go install github.com/go-swagger/go-swagger/cmd/swagger@latest"; \
		echo "$(GREEN)✅ 跳过验证，文档可以正常使用$(NC)"; \
	fi

swagger-serve: swagger-gen ## 启动Swagger UI服务
	@echo "$(BLUE)🌐 启动Swagger UI服务...$(NC)"
	@echo "请确保应用正在运行 (make dev)"
	@echo "📱 访问地址: http://localhost:8080/swagger/index.html"
	@echo "📋 API文档: api/openapi/swagger.json"

swagger-export: swagger-gen ## 导出OpenAPI规范文件
	@echo "$(BLUE)📤 导出OpenAPI规范文件...$(NC)"
	@echo "$(GREEN)✅ OpenAPI文件已生成到 api/openapi/$(NC)"

swagger-clean: ## 清理生成的Swagger文档
	@echo "$(BLUE)🧹 清理Swagger文档...$(NC)"
	@rm -rf api/openapi/swagger.json api/openapi/swagger.yaml api/openapi/docs.go
	@echo "$(GREEN)✅ Swagger文档清理完成$(NC)"

swagger-dev: swagger-gen-force swagger-serve ## 开发模式：强制生成并启动服务

# ==========================================
# Docker Swagger支持
# ==========================================

.PHONY: docker-swagger-gen docker-swagger-validate
docker-swagger-gen: ## 在Docker容器中生成Swagger文档
	@echo "$(BLUE)🐳 在Docker容器中生成Swagger文档...$(NC)"
	@docker run --rm \
		-v $(PWD):/app \
		-w /app \
		golang:1.24.3-alpine \
		sh -c "go install github.com/swaggo/swag/cmd/swag@latest && \
		       swag init -g cmd/api/main.go -o api/openapi --parseInternal --parseDependency --parseDepth 2"
	@if [ -f "api/openapi/swagger.json" ]; then \
		echo "$(GREEN)✅ Docker中Swagger文档生成完成$(NC)"; \
	else \
		echo "$(RED)❌ Docker中Swagger文档生成失败$(NC)"; \
		exit 1; \
	fi

docker-swagger-validate: ## 在Docker容器中验证Swagger文档
	@echo "$(BLUE)🐳 在Docker容器中验证Swagger文档...$(NC)"
	@if [ ! -f "api/openapi/swagger.json" ]; then \
		$(MAKE) docker-swagger-gen; \
	fi
	@docker run --rm \
		-v $(PWD):/app \
		-w /app \
		golang:1.24.3-alpine \
		sh -c "go install github.com/go-swagger/go-swagger/cmd/swagger@latest && \
		       swagger validate api/openapi/swagger.json" || \
	echo "$(YELLOW)⚠️  验证可能有警告，但文档可以正常使用$(NC)"

# ==========================================
# 配置管理 (优化后)
# ==========================================

.PHONY: config-check-all config-check-current
config-check-all: ## 检查所有环境配置
	@echo "$(BLUE)🔧 检查所有环境配置...$(NC)"
	@for env in development testing production; do \
		echo "检查 $$env 环境..."; \
		ENV=$$env $(MAKE) --no-print-directory config-check-single; \
	done
	@echo "$(GREEN)✅ 配置检查完成$(NC)"

config-check-current: ## 检查当前环境配置
	@echo "$(BLUE)🔍 检查当前环境配置...$(NC)"
	@current_env=$$(grep "^APP_ENV=" .env 2>/dev/null | cut -d= -f2 || echo "development"); \
	echo "当前环境: $$current_env"; \
	ENV=$$current_env $(MAKE) --no-print-directory config-check-single

# 内部命令，不在help中显示
config-check-single:
	@if go run -C . -tags config_test ./cmd/config-test 2>/dev/null; then \
		echo "$(GREEN)✅ $(ENV) 环境配置正常$(NC)"; \
	else \
		echo "$(YELLOW)⚠️  $(ENV) 环境配置有警告$(NC)"; \
	fi

# ==========================================
# 部署
# ==========================================

.PHONY: deploy deploy-dev deploy-test deploy-prod
deploy: ## 部署到指定环境 (使用 ENV=环境名)
	@echo "$(BLUE)🚀 部署到 $(ENV) 环境...$(NC)"
	@$(MAKE) setup ENV=$(ENV)
	@$(MAKE) docker-build
	@if [ "$(ENV)" = "production" ]; then \
		$(MAKE) docker-push; \
	fi
	@$(MAKE) docker-up
	@echo "$(GREEN)✅ 部署到 $(ENV) 环境完成!$(NC)"

deploy-dev: ENV=development ## 部署到开发环境
deploy-dev: deploy

deploy-test: ENV=testing ## 部署到测试环境
deploy-test: deploy

deploy-prod: ENV=production ## 部署到生产环境
deploy-prod: deploy

# ==========================================
# 清理
# ==========================================

.PHONY: clean clean-all clean-build clean-test clean-cache
clean-build: ## 清理构建文件
	@echo "$(BLUE)🧹 清理构建文件...$(NC)"
	@rm -rf bin/
	@echo "$(GREEN)✅ 构建文件清理完成$(NC)"

clean-test: ## 清理测试文件
	@echo "$(BLUE)🧹 清理测试文件...$(NC)"
	@rm -f coverage.out coverage.html
	@echo "$(GREEN)✅ 测试文件清理完成$(NC)"

clean-cache: ## 清理Go缓存
	@echo "$(BLUE)🧹 清理Go缓存...$(NC)"
	@go clean -cache -modcache -testcache
	@echo "$(GREEN)✅ 缓存清理完成$(NC)"

clean: clean-build clean-test ## 清理构建和测试文件

clean-all: clean clean-cache docker-clean swagger-clean mock-clean ## 清理所有文件和资源

# ==========================================
# 监控和健康检查
# ==========================================

.PHONY: health metrics
health: ## 检查服务健康状态
	@echo "$(BLUE)💓 检查服务健康状态...$(NC)"
	@curl -f http://localhost:8080/health || echo "$(RED)❌ 服务不健康$(NC)"

metrics: ## 查看服务指标
	@echo "$(BLUE)📊 查看服务指标...$(NC)"
	@curl -s http://localhost:9100/metrics | head -20

# ==========================================
# CI/CD支持 (更新版)
# ==========================================

.PHONY: ci-test ci-build ci-lint ci-security ci-swagger
ci-test: ## CI环境测试
	@echo "$(BLUE)🤖 CI环境测试...$(NC)"
	@go test -v -race -coverprofile=coverage.out ./...
	@go tool cover -func=coverage.out

ci-build: ## CI环境构建
	@echo "$(BLUE)🤖 CI环境构建...$(NC)"
	@$(MAKE) build-all

ci-lint: ## CI环境代码检查
	@echo "$(BLUE)🤖 CI环境代码检查...$(NC)"
	@$(MAKE) lint-full

ci-security: ## CI环境安全检查
	@echo "$(BLUE)🤖 CI环境安全检查...$(NC)"
	@$(MAKE) sec-check vuln-check

ci-swagger: ## CI环境Swagger文档生成和验证
	@echo "$(BLUE)🤖 CI环境Swagger处理...$(NC)"
	@$(MAKE) swagger-install
	@$(MAKE) swagger-gen
	@$(MAKE) swagger-validate
	@$(MAKE) swagger-export
	@echo "$(GREEN)✅ CI环境Swagger处理完成$(NC)"

# ==========================================
# 测试环境快速部署
# ==========================================

.PHONY: deploy-test-env deploy-test-local test-env-status test-env-logs test-env-clean

deploy-test-env: ## 部署测试环境到远程服务器
	@echo "$(BLUE)🚀 部署测试环境到远程服务器...$(NC)"
	@if [ -z "$(ECS_SERVER_IP)" ]; then \
		echo "$(RED)❌ 请设置ECS_SERVER_IP环境变量$(NC)"; \
		exit 1; \
	fi
	@if [ -z "$(ECS_USER)" ]; then \
		echo "$(RED)❌ 请设置ECS_USER环境变量$(NC)"; \
		exit 1; \
	fi
	@echo "$(YELLOW)📦 构建测试镜像...$(NC)"
	@$(MAKE) docker-build ENV=testing
	@echo "$(YELLOW)📤 推送镜像到仓库...$(NC)"
	@$(MAKE) docker-push
	@echo "$(YELLOW)🚀 部署到服务器...$(NC)"
	@../scripts/deploy-test-env.sh
	@echo "$(GREEN)✅ 测试环境部署完成$(NC)"

deploy-test-local: ## 在本地部署测试环境
	@echo "$(BLUE)🏠 在本地部署测试环境...$(NC)"
	@echo "$(YELLOW)📦 构建测试镜像...$(NC)"
	@$(MAKE) docker-build ENV=testing
	@echo "$(YELLOW)🚀 启动本地测试环境...$(NC)"
	@export TEST_API_PORT=$(TEST_ENV_PORT) && \
	 export TEST_DB_PORT=$(TEST_DB_PORT) && \
	 export TEST_REDIS_PORT=$(TEST_REDIS_PORT) && \
	 export BACKEND_IMAGE=$(DOCKER_IMAGE):$(DOCKER_TAG) && \
	 ../scripts/deploy-test-env.sh
	@echo "$(GREEN)✅ 本地测试环境启动完成$(NC)"
	@echo "$(BLUE)📍 访问地址: http://localhost:$(TEST_ENV_PORT)$(NC)"

test-env-status: ## 查看测试环境状态
	@echo "$(BLUE)📊 测试环境状态$(NC)"
	@if [ -f "../docker-compose.test.yml" ]; then \
		docker-compose -f ../docker-compose.test.yml ps; \
	else \
		echo "$(YELLOW)⚠️  测试环境未部署$(NC)"; \
	fi

test-env-logs: ## 查看测试环境日志
	@echo "$(BLUE)📋 测试环境日志$(NC)"
	@if [ -f "../docker-compose.test.yml" ]; then \
		docker-compose -f ../docker-compose.test.yml logs -f --tail=100; \
	else \
		echo "$(YELLOW)⚠️  测试环境未部署$(NC)"; \
	fi

test-env-clean: ## 清理测试环境
	@echo "$(BLUE)🧹 清理测试环境...$(NC)"
	@if [ -f "../docker-compose.test.yml" ]; then \
		docker-compose -f ../docker-compose.test.yml down -v; \
		docker system prune -f; \
		echo "$(GREEN)✅ 测试环境清理完成$(NC)"; \
	else \
		echo "$(YELLOW)⚠️  测试环境未部署$(NC)"; \
	fi

# 快速测试环境命令别名
.PHONY: test-deploy test-status test-logs test-clean
test-deploy: deploy-test-local ## 快速部署本地测试环境（别名）
test-status: test-env-status ## 查看测试环境状态（别名）
test-logs: test-env-logs ## 查看测试环境日志（别名）
test-clean: test-env-clean ## 清理测试环境（别名）

# 默认目标
.DEFAULT_GOAL := help