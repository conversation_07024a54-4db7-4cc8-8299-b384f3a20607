# 需求分析

## 项目背景
经过调研和试用市场上的erp，包括bcs玩家国度、聚水潭、无忧、芒果店长，暂无能满足我们需求的erp。

## 目标
解决获取平台订单列表、仓库打印运单、采购信息汇总的问题

## 功能需求
- 通过平台API获取订单信息（平台货件编号、图片、颜色、尺码、物流追踪号码、物流状态、面单）
- 后台订单状态分为未接单、已接单、已采购、已打单，可以筛选显示
- 订单可填入采购价格
- 填入采购价格后，将订单状态更改为已采购
- 点击"打印运单按钮"后，将订单状态更改为已打单

## 技术栈与部署
后端：go+gin+gorm  
前端：Vue3 + TypeScript + TailwindCSS  
数据库：阿里云RDS PostgreSQL  
网关：nginx  
服务器：阿里云ECS  
域名：阿里云域名服务  

## 📚 项目文档

### 主要文档
- **README.md** - 本文档，项目概述和需求分析

### 详细文档
项目的详细技术文档已整理到 `docs/readme-files/` 文件夹中：

- **backend-README.md** - 后端服务配置和API文档
- **frontend-README.md** - 前端项目配置和构建说明
- **README_AUTH.md** - 认证机制说明和实现细节
- **README-API-PROXY.md** - API代理配置文档
- **api-README.md** - API模块组织结构说明
- **auth-components-README.md** - 认证组件使用文档
- **README-INDEX.md** - 文档索引和说明

> 💡 提示：查看 `docs/readme-files/README-INDEX.md` 获取所有文档的详细说明

---

**项目作者**: Holk  
**最后更新**: 2024年  