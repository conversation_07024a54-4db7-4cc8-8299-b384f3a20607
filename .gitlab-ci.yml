# GitLab CI/CD 配置文件 - 最小可运行版本
# 专门用于快速部署backend测试环境供前端调试

stages:
  - build
  - deploy-test

variables:
  # Docker镜像配置
  DOCKER_DRIVER: overlay2
  DOCKER_TLS_CERTDIR: "/certs"
  IMAGE_TAG: $CI_REGISTRY_IMAGE/backend:test-$CI_COMMIT_SHORT_SHA
  TEST_TAG: $CI_REGISTRY_IMAGE/backend:test-latest

  # 阿里云ECS部署配置
  DEPLOY_SERVER: $ECS_SERVER_IP
  DEPLOY_USER: $ECS_USER
  PROJECT_PATH: "/opt/erp-backend-test"

  # 测试环境端口配置
  TEST_API_PORT: 8080
  TEST_DB_PORT: 5433
  TEST_REDIS_PORT: 6380

# 快速构建阶段 - 跳过测试，专注快速部署
build_test:
  stage: build
  image: docker:24.0.5
  services:
    - docker:24.0.5-dind
  before_script:
    - echo $CI_REGISTRY_PASSWORD | docker login -u $CI_REGISTRY_USER --password-stdin $CI_REGISTRY
  script:
    - cd backend
    - |
      echo "🚀 快速构建测试环境镜像..."
      # 构建轻量级测试镜像
      docker build \
        --build-arg VERSION=test-$CI_COMMIT_SHORT_SHA \
        --build-arg BUILD_TIME=$(date '+%Y-%m-%d_%H:%M:%S') \
        --build-arg COMMIT_HASH=$CI_COMMIT_SHA \
        -f docker/Dockerfile \
        -t $IMAGE_TAG \
        -t $TEST_TAG \
        .

      echo "📤 推送镜像到仓库..."
      docker push $IMAGE_TAG
      docker push $TEST_TAG

      echo "✅ 镜像构建完成: $IMAGE_TAG"
  only:
    - develop
    - test
    - feature/*
  tags:
    - docker

# 快速部署到测试环境 - 跳过测试，专注快速部署
deploy_test:
  stage: deploy-test
  image: alpine:latest
  before_script:
    - apk add --no-cache openssh-client curl docker-compose
    - eval $(ssh-agent -s)
    - echo "$SSH_PRIVATE_KEY" | tr -d '\r' | ssh-add -
    - mkdir -p ~/.ssh
    - chmod 700 ~/.ssh
    - ssh-keyscan $DEPLOY_SERVER >> ~/.ssh/known_hosts
    - chmod 644 ~/.ssh/known_hosts
  script:
    - |
      echo "🚀 开始部署到阿里云ECS测试环境..."

      # SSH到服务器执行部署
      ssh $DEPLOY_USER@$DEPLOY_SERVER << 'EOF'
        set -e

        echo "� 准备项目目录..."
        sudo mkdir -p $PROJECT_PATH
        cd $PROJECT_PATH

        # 登录Docker仓库
        echo "$CI_REGISTRY_PASSWORD" | docker login -u "$CI_REGISTRY_USER" --password-stdin $CI_REGISTRY

        # 创建测试环境docker-compose文件
        echo "📝 创建docker-compose配置..."
        cat > docker-compose.test.yml << 'COMPOSE_EOF'
version: '3.8'

services:
  backend-test:
    image: $IMAGE_TAG
    container_name: erp-backend-test
    ports:
      - "$TEST_API_PORT:8080"
      - "9100:9100"  # metrics
    environment:
      - APP_ENV=testing
      - DATABASE_HOST=postgres-test
      - DATABASE_PORT=5432
      - DATABASE_DATABASE=erp_testing
      - DATABASE_USERNAME=erp_test
      - DATABASE_PASSWORD=test_password_123
      - REDIS_HOST=redis-test
      - REDIS_PORT=6379
      - REDIS_DB=2
      - LOGGER_LEVEL=info
      - LOGGER_FORMAT=json
      - SECURITY_CORS_ALLOWED_ORIGINS=*
    depends_on:
      - postgres-test
      - redis-test
    networks:
      - erp-test-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8080/health"]
      interval: 30s
      timeout: 10s
      retries: 3

  postgres-test:
    image: postgres:15-alpine
    container_name: erp-postgres-test
    ports:
      - "$TEST_DB_PORT:5432"
    environment:
      POSTGRES_DB: erp_testing
      POSTGRES_USER: erp_test
      POSTGRES_PASSWORD: test_password_123
    volumes:
      - postgres_test_data:/var/lib/postgresql/data
    networks:
      - erp-test-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U erp_test"]
      interval: 10s
      timeout: 5s
      retries: 5

  redis-test:
    image: redis:7-alpine
    container_name: erp-redis-test
    ports:
      - "$TEST_REDIS_PORT:6379"
    volumes:
      - redis_test_data:/data
    networks:
      - erp-test-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 10s
      timeout: 3s
      retries: 5

networks:
  erp-test-network:
    driver: bridge

volumes:
  postgres_test_data:
  redis_test_data:
COMPOSE_EOF

        # 替换环境变量
        sed -i "s/\$IMAGE_TAG/$IMAGE_TAG/g" docker-compose.test.yml
        sed -i "s/\$TEST_API_PORT/$TEST_API_PORT/g" docker-compose.test.yml
        sed -i "s/\$TEST_DB_PORT/$TEST_DB_PORT/g" docker-compose.test.yml
        sed -i "s/\$TEST_REDIS_PORT/$TEST_REDIS_PORT/g" docker-compose.test.yml

        # 拉取最新镜像
        echo "📦 拉取最新镜像..."
        docker pull $IMAGE_TAG

        # 停止旧服务
        echo "⏹️ 停止旧服务..."
        docker-compose -f docker-compose.test.yml down || true

        # 启动新服务
        echo "▶️ 启动新服务..."
        docker-compose -f docker-compose.test.yml up -d

        # 等待服务启动
        echo "⏳ 等待服务启动..."
        sleep 30

        # 健康检查
        echo "🏥 执行健康检查..."
        for i in {1..10}; do
          if curl -f http://localhost:$TEST_API_PORT/health > /dev/null 2>&1; then
            echo "✅ 服务启动成功！"
            break
          else
            echo "⏳ 等待服务启动... ($i/10)"
            sleep 10
          fi

          if [ $i -eq 10 ]; then
            echo "❌ 服务启动失败"
            docker-compose -f docker-compose.test.yml logs backend-test
            exit 1
          fi
        done

        # 清理旧镜像
        echo "🧹 清理旧镜像..."
        docker image prune -f

        echo "🎉 部署完成！"
        echo "📍 测试环境访问地址: http://$DEPLOY_SERVER:$TEST_API_PORT"
        echo "📊 健康检查: http://$DEPLOY_SERVER:$TEST_API_PORT/health"
        echo "📖 API文档: http://$DEPLOY_SERVER:$TEST_API_PORT/swagger/index.html"
      EOF
  environment:
    name: test
    url: http://$ECS_SERVER_IP:$TEST_API_PORT
  only:
    - develop
    - test
    - feature/*
  when: on_success  # 构建成功后自动部署
  dependencies:
    - build_test

notify_failure:
  stage: notify
  image: alpine:latest
  before_script:
    - apk add --no-cache curl
  script:
    - |
      # 发送失败通知
      curl -X POST "$WEBHOOK_URL" \
        -H "Content-Type: application/json" \
        -d "{
          \"msgtype\": \"text\",
          \"text\": {
            \"content\": \"❌ ERP后端测试环境部署失败！\n分支: $CI_COMMIT_REF_NAME\n提交: $CI_COMMIT_SHORT_SHA\n请检查CI/CD日志\"
          }
        }" || echo "通知发送失败"
  only:
    - main
    - develop
  when: on_failure