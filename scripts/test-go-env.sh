#!/bin/bash

# Go测试环境检查脚本
# 用于验证CI/CD环境中的Go配置

set -e

echo "🔍 Go环境检查"
echo "=============="

echo "📋 Go版本信息："
go version

echo "📋 Go环境变量："
go env | grep -E "(CGO_ENABLED|GOOS|GOARCH|GOROOT|GOPATH)"

echo "📋 CGO支持检查："
if [ "$CGO_ENABLED" = "1" ]; then
    echo "✅ CGO已启用"
    
    echo "📋 检查C编译器："
    if command -v gcc >/dev/null 2>&1; then
        echo "✅ GCC可用: $(gcc --version | head -1)"
    else
        echo "❌ GCC不可用"
    fi
    
    if command -v musl-gcc >/dev/null 2>&1; then
        echo "✅ musl-gcc可用"
    else
        echo "❌ musl-gcc不可用"
    fi
else
    echo "❌ CGO未启用"
fi

echo "📋 测试race检测支持："
cd backend 2>/dev/null || { echo "❌ backend目录不存在"; exit 1; }

# 创建一个简单的测试文件来验证race检测
cat > race_test.go << 'EOF'
package main

import (
    "testing"
    "time"
)

func TestRaceDetection(t *testing.T) {
    counter := 0
    done := make(chan bool)
    
    go func() {
        counter++
        done <- true
    }()
    
    go func() {
        counter++
        done <- true
    }()
    
    <-done
    <-done
    
    // 等待一下确保操作完成
    time.Sleep(10 * time.Millisecond)
    
    if counter != 2 {
        t.Errorf("Expected counter to be 2, got %d", counter)
    }
}
EOF

echo "🏃 测试race检测..."
if go test -race race_test.go; then
    echo "✅ race检测测试成功"
    RACE_SUPPORT=true
else
    echo "❌ race检测测试失败"
    RACE_SUPPORT=false
fi

# 清理测试文件
rm -f race_test.go

echo "🏃 测试标准测试..."
if go test ./...; then
    echo "✅ 标准测试成功"
    STANDARD_TEST=true
else
    echo "❌ 标准测试失败"
    STANDARD_TEST=false
fi

echo ""
echo "📊 测试环境总结："
echo "=================="
echo "CGO支持: $([ "$CGO_ENABLED" = "1" ] && echo "✅ 是" || echo "❌ 否")"
echo "Race检测: $([ "$RACE_SUPPORT" = "true" ] && echo "✅ 支持" || echo "❌ 不支持")"
echo "标准测试: $([ "$STANDARD_TEST" = "true" ] && echo "✅ 通过" || echo "❌ 失败")"

echo ""
echo "💡 建议的CI/CD配置："
if [ "$RACE_SUPPORT" = "true" ]; then
    echo "✅ 可以使用: go test -race -coverprofile=coverage.out ./..."
else
    echo "⚠️  建议使用: go test -coverprofile=coverage.out ./..."
    echo "   需要安装: apk add gcc musl-dev"
    echo "   设置变量: CGO_ENABLED=1"
fi
