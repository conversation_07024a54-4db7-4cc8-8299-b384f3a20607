#!/bin/bash

# 九翼跨境电商ERP系统 - 测试环境快速部署脚本
# 用于在阿里云ECS上快速部署backend测试环境供前端调试

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 配置变量
PROJECT_NAME="nine-wings-erp"
BACKEND_IMAGE="${CI_REGISTRY_IMAGE}/backend:test-${CI_COMMIT_SHORT_SHA:-latest}"
PROJECT_PATH="/opt/erp-backend-test"
COMPOSE_FILE="docker-compose.test.yml"

# 端口配置
API_PORT="${TEST_API_PORT:-8080}"
DB_PORT="${TEST_DB_PORT:-5433}"
REDIS_PORT="${TEST_REDIS_PORT:-6380}"

echo -e "${BLUE}🚀 开始部署九翼ERP后端测试环境...${NC}"

# 检查Docker是否安装
if ! command -v docker &> /dev/null; then
    echo -e "${RED}❌ Docker未安装，请先安装Docker${NC}"
    exit 1
fi

if ! command -v docker-compose &> /dev/null; then
    echo -e "${RED}❌ Docker Compose未安装，请先安装Docker Compose${NC}"
    exit 1
fi

# 创建项目目录
echo -e "${YELLOW}📂 准备项目目录...${NC}"
sudo mkdir -p $PROJECT_PATH
cd $PROJECT_PATH

# 创建docker-compose配置文件
echo -e "${YELLOW}📝 创建Docker Compose配置...${NC}"
cat > $COMPOSE_FILE << 'EOF'
version: '3.8'

services:
  backend-test:
    image: ${BACKEND_IMAGE}
    container_name: erp-backend-test
    ports:
      - "${API_PORT}:8080"
      - "9100:9100"  # metrics
    environment:
      - APP_ENV=testing
      - DATABASE_HOST=postgres-test
      - DATABASE_PORT=5432
      - DATABASE_DATABASE=erp_testing
      - DATABASE_USERNAME=erp_test
      - DATABASE_PASSWORD=test_password_123
      - REDIS_HOST=redis-test
      - REDIS_PORT=6379
      - REDIS_DB=2
      - LOGGER_LEVEL=info
      - LOGGER_FORMAT=json
      - SECURITY_CORS_ALLOWED_ORIGINS=*
      - SECURITY_CORS_ALLOWED_METHODS=GET,POST,PUT,DELETE,OPTIONS,PATCH
      - SECURITY_CORS_ALLOWED_HEADERS=*
      - SECURITY_CORS_ALLOW_CREDENTIALS=true
      - SECURITY_RATE_LIMIT_ENABLE=false
      - MONITORING_HEALTH_ENABLE=true
      - MONITORING_HEALTH_PATH=/health
    depends_on:
      postgres-test:
        condition: service_healthy
      redis-test:
        condition: service_healthy
    networks:
      - erp-test-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8080/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 60s

  postgres-test:
    image: postgres:15-alpine
    container_name: erp-postgres-test
    ports:
      - "${DB_PORT}:5432"
    environment:
      POSTGRES_DB: erp_testing
      POSTGRES_USER: erp_test
      POSTGRES_PASSWORD: test_password_123
      POSTGRES_INITDB_ARGS: "--encoding=UTF8 --locale=C"
    volumes:
      - postgres_test_data:/var/lib/postgresql/data
      - ./init-db.sql:/docker-entrypoint-initdb.d/init-db.sql:ro
    networks:
      - erp-test-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U erp_test -d erp_testing"]
      interval: 10s
      timeout: 5s
      retries: 5
      start_period: 30s

  redis-test:
    image: redis:7-alpine
    container_name: erp-redis-test
    ports:
      - "${REDIS_PORT}:6379"
    volumes:
      - redis_test_data:/data
    networks:
      - erp-test-network
    restart: unless-stopped
    command: >
      redis-server
      --appendonly yes
      --maxmemory 256mb
      --maxmemory-policy allkeys-lru
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 10s
      timeout: 3s
      retries: 5

networks:
  erp-test-network:
    driver: bridge
    name: erp-test-network

volumes:
  postgres_test_data:
    name: erp-postgres-test-data
  redis_test_data:
    name: erp-redis-test-data
EOF

# 创建数据库初始化脚本
echo -e "${YELLOW}📝 创建数据库初始化脚本...${NC}"
cat > init-db.sql << 'EOF'
-- 九翼ERP测试环境数据库初始化脚本
-- 创建基础表结构和测试数据

-- 设置时区
SET timezone = 'Asia/Shanghai';

-- 创建扩展
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";
CREATE EXTENSION IF NOT EXISTS "pg_trgm";

-- 创建测试用户表（如果不存在）
CREATE TABLE IF NOT EXISTS users (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    username VARCHAR(50) UNIQUE NOT NULL,
    email VARCHAR(100) UNIQUE NOT NULL,
    password_hash VARCHAR(255) NOT NULL,
    status VARCHAR(20) DEFAULT 'active',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 插入测试用户
INSERT INTO users (username, email, password_hash) 
VALUES ('test_user', '<EMAIL>', '$2a$10$dummy.hash.for.testing.only')
ON CONFLICT (username) DO NOTHING;

-- 创建测试数据表
CREATE TABLE IF NOT EXISTS test_data (
    id SERIAL PRIMARY KEY,
    name VARCHAR(100) NOT NULL,
    description TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 插入测试数据
INSERT INTO test_data (name, description) VALUES 
('测试商品1', '这是一个测试商品'),
('测试商品2', '这是另一个测试商品')
ON CONFLICT DO NOTHING;

COMMIT;
EOF

# 替换环境变量
echo -e "${YELLOW}🔧 配置环境变量...${NC}"
sed -i "s/\${BACKEND_IMAGE}/$BACKEND_IMAGE/g" $COMPOSE_FILE
sed -i "s/\${API_PORT}/$API_PORT/g" $COMPOSE_FILE
sed -i "s/\${DB_PORT}/$DB_PORT/g" $COMPOSE_FILE
sed -i "s/\${REDIS_PORT}/$REDIS_PORT/g" $COMPOSE_FILE

# 登录Docker仓库（如果提供了凭据）
if [ ! -z "$CI_REGISTRY_PASSWORD" ] && [ ! -z "$CI_REGISTRY_USER" ]; then
    echo -e "${YELLOW}🔐 登录Docker仓库...${NC}"
    echo "$CI_REGISTRY_PASSWORD" | docker login -u "$CI_REGISTRY_USER" --password-stdin $CI_REGISTRY
fi

# 拉取最新镜像
echo -e "${YELLOW}📦 拉取最新镜像...${NC}"
docker pull $BACKEND_IMAGE || echo "警告: 无法拉取镜像，将使用本地镜像"

# 停止旧服务
echo -e "${YELLOW}⏹️ 停止旧服务...${NC}"
docker-compose -f $COMPOSE_FILE down || true

# 清理旧容器和网络
echo -e "${YELLOW}🧹 清理旧资源...${NC}"
docker container prune -f || true
docker network prune -f || true

# 启动新服务
echo -e "${YELLOW}▶️ 启动新服务...${NC}"
docker-compose -f $COMPOSE_FILE up -d

# 等待服务启动
echo -e "${YELLOW}⏳ 等待服务启动...${NC}"
sleep 30

# 健康检查
echo -e "${YELLOW}🏥 执行健康检查...${NC}"
for i in {1..20}; do
    if curl -f http://localhost:$API_PORT/health > /dev/null 2>&1; then
        echo -e "${GREEN}✅ 服务启动成功！${NC}"
        break
    else
        echo -e "${YELLOW}⏳ 等待服务启动... ($i/20)${NC}"
        sleep 15
    fi
    
    if [ $i -eq 20 ]; then
        echo -e "${RED}❌ 服务启动失败，查看日志:${NC}"
        docker-compose -f $COMPOSE_FILE logs backend-test
        exit 1
    fi
done

# 显示服务信息
echo -e "${GREEN}🎉 部署完成！${NC}"
echo -e "${BLUE}📍 服务访问信息:${NC}"
echo -e "  API地址: http://localhost:$API_PORT"
echo -e "  健康检查: http://localhost:$API_PORT/health"
echo -e "  API文档: http://localhost:$API_PORT/swagger/index.html"
echo -e "  数据库端口: $DB_PORT"
echo -e "  Redis端口: $REDIS_PORT"
echo ""
echo -e "${BLUE}📊 服务状态:${NC}"
docker-compose -f $COMPOSE_FILE ps

echo -e "${GREEN}✅ 测试环境已就绪，前端可以开始调试接口！${NC}"
