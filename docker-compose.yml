version: '3.8'

services:
  # 前端服务
  frontend:
    build:
      context: ./frontend
      dockerfile: Dockerfile
    ports:
      - "80:80"
    depends_on:
      - backend
    restart: always
    networks:
      - erp-network
    environment:
      - NODE_ENV=production

  # 后端服务
  backend:
    build:
      context: ./backend
      dockerfile: Dockerfile
    ports:
      - "8080:8080"
    environment:
      - GIN_MODE=release
      - DB_HOST=db
      - DB_PORT=3306
      - DB_USER=erp_user
      - DB_PASSWORD=erp_password
      - DB_NAME=erp_db
    depends_on:
      - db
    restart: always
    networks:
      - erp-network

  # 数据库服务 (MySQL)
  db:
    image: mysql:8.0
    ports:
      - "3306:3306"
    environment:
      - MYSQL_ROOT_PASSWORD=root_password
      - MYSQL_DATABASE=erp_db
      - MYSQL_USER=erp_user
      - MYSQL_PASSWORD=erp_password
    volumes:
      - mysql_data:/var/lib/mysql
    restart: always
    networks:
      - erp-network

networks:
  erp-network:
    driver: bridge

volumes:
  mysql_data: 