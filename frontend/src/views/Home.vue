<script setup lang="ts">
import { ref, onMounted, onUnmounted } from "vue";
import { Line, Column, Pie, DualAxes } from "@antv/g2plot";
import { useAuth } from "@/stores/auth";
import PageContainer from "@/components/PageContainer.vue";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";

/**
 * Author: Holk
 * 首页图表展示组件 - 使用shadcn-vue组件优化版本
 * 包含折线图、条形图、饼图和双轴图的数据可视化展示
 * 使用PageContainer确保内容不会超出父组件宽度，防止横向滚动条
 * 使用shadcn-vue的Card组件提供更好的视觉效果和一致性
 */

defineProps<{
	msg?: string;
}>();

const count = ref(0);
const auth = useAuth();

// 存储图表实例的引用，用于resize时重新渲染
let charts: {
	lineChart?: Line;
	columnChart?: Column;
	pieChart?: Pie;
	dualAxesChart?: DualAxes;
} = {};

// ResizeObserver实例，用于监听容器大小变化
let resizeObserver: ResizeObserver | null = null;

// resize处理函数 - 优化flex布局下的响应式处理
const handleResize = () => {
	// 延迟执行以确保DOM已更新，增加延迟时间以适应flex布局的重排
	setTimeout(() => {
		Object.values(charts).forEach((chart) => {
			if (chart) {
				// 直接重新渲染图表以适应新的容器大小
				chart.render();
			}
		});
	}, 150);
};

// 图表初始化函数
const initCharts = () => {
	// 折线图数据
	const lineData = [
		{ year: "2019", value: 3 },
		{ year: "2020", value: 4 },
		{ year: "2021", value: 3.5 },
		{ year: "2022", value: 5 },
		{ year: "2023", value: 4.9 },
	];

	// 条形图数据
	const columnData = [
		{ type: "家具家电", value: 38 },
		{ type: "粮油副食", value: 52 },
		{ type: "生鲜水果", value: 61 },
		{ type: "美容洗护", value: 45 },
		{ type: "母婴用品", value: 48 },
		{ type: "进口食品", value: 38 },
	];

	// 饼图数据
	const pieData = [
		{ type: "分类一", value: 27 },
		{ type: "分类二", value: 25 },
		{ type: "分类三", value: 18 },
		{ type: "分类四", value: 15 },
		{ type: "分类五", value: 10 },
		{ type: "其他", value: 5 },
	];

	// 双轴图数据
	const dualAxesData = [
		{
			time: "2019-03",
			value: 350,
			count: 800,
		},
		{
			time: "2019-04",
			value: 900,
			count: 600,
		},
		{
			time: "2019-05",
			value: 300,
			count: 400,
		},
		{
			time: "2019-06",
			value: 450,
			count: 380,
		},
		{
			time: "2019-07",
			value: 470,
			count: 220,
		},
	];

	// 初始化折线图
	charts.lineChart = new Line("line-container", {
		data: lineData,
		xField: "year",
		yField: "value",
		smooth: true,
		point: {
			size: 5,
		},
		autoFit: true,
		width: undefined,
		height: undefined,
	});
	charts.lineChart.render();

	// 初始化条形图
	charts.columnChart = new Column("column-container", {
		data: columnData,
		xField: "type",
		yField: "value",
		label: {
			position: "middle",
		},
		xAxis: {
			label: {
				autoRotate: true,
			},
		},
		autoFit: true,
		width: undefined,
		height: undefined,
	});
	charts.columnChart.render();

	// 初始化饼图
	charts.pieChart = new Pie("pie-container", {
		data: pieData,
		angleField: "value",
		colorField: "type",
		radius: 0.8,
		label: {
			type: "outer",
		},
		interactions: [{ type: "element-active" }],
		autoFit: true,
		width: undefined,
		height: undefined,
	});
	charts.pieChart.render();

	// 初始化双轴图
	charts.dualAxesChart = new DualAxes("dual-axes-container", {
		data: [dualAxesData, dualAxesData],
		xField: "time",
		yField: ["value", "count"],
		geometryOptions: [
			{
				geometry: "column",
				columnWidthRatio: 0.4,
			},
			{
				geometry: "line",
				lineStyle: {
					lineWidth: 2,
				},
			},
		],
		autoFit: true,
		width: undefined,
		height: undefined,
	});
	charts.dualAxesChart.render();
};

onMounted(() => {
	initCharts();

	// 监听窗口resize事件
	window.addEventListener("resize", handleResize);

	// 使用ResizeObserver监听图表容器大小变化（更精确）
	const chartContainer = document.querySelector(".charts-grid");
	if (chartContainer && window.ResizeObserver) {
		resizeObserver = new ResizeObserver(handleResize);
		resizeObserver.observe(chartContainer);
	}

	// 监听sidebar状态变化（通过MutationObserver监听DOM变化）
	const observer = new MutationObserver(handleResize);
	const sidebarElement = document.querySelector('[data-sidebar="sidebar"]');
	if (sidebarElement) {
		observer.observe(sidebarElement, {
			attributes: true,
			attributeFilter: ["data-state", "data-collapsible"],
		});
	}
});

onUnmounted(() => {
	// 清理事件监听器
	window.removeEventListener("resize", handleResize);

	// 清理ResizeObserver
	if (resizeObserver) {
		resizeObserver.disconnect();
		resizeObserver = null;
	}

	// 销毁图表实例
	Object.values(charts).forEach((chart) => {
		if (chart) {
			chart.destroy();
		}
	});

	// 清空图表引用
	charts = {};
});
</script>

<template>
	<PageContainer>
		<div class="welcome-header">
			<h1 class="text-2xl font-bold text-gray-900 mb-4">
				{{ msg || "欢迎使用9Wings ERP系统" }}
			</h1>
			<Card v-if="auth.isAuthenticated.value" class="mb-6">
				<CardHeader>
					<CardTitle class="text-lg">用户信息</CardTitle>
				</CardHeader>
				<CardContent class="space-y-2">
					<p class="text-gray-700">
						当前用户:
						<strong class="text-gray-900">{{
							auth.user.value?.username
						}}</strong>
					</p>
					<p class="text-gray-700">
						角色:
						<strong class="text-gray-900">{{ auth.user.value?.role }}</strong>
					</p>
					<p class="text-gray-700 flex items-center gap-2">
						Token状态:
						<Badge variant="default" class="bg-green-600 text-white">
							✅ 已认证
						</Badge>
					</p>
				</CardContent>
			</Card>
		</div>

		<div class="charts-grid grid grid-cols-1 lg:grid-cols-2 gap-6 w-full">
			<Card class="chart-item">
				<CardHeader>
					<CardTitle class="text-center">折线图</CardTitle>
				</CardHeader>
				<CardContent>
					<div id="line-container" class="w-full h-[300px]"></div>
				</CardContent>
			</Card>

			<Card class="chart-item">
				<CardHeader>
					<CardTitle class="text-center">条形图</CardTitle>
				</CardHeader>
				<CardContent>
					<div id="column-container" class="w-full h-[300px]"></div>
				</CardContent>
			</Card>

			<Card class="chart-item">
				<CardHeader>
					<CardTitle class="text-center">饼图</CardTitle>
				</CardHeader>
				<CardContent>
					<div id="pie-container" class="w-full h-[300px]"></div>
				</CardContent>
			</Card>

			<Card class="chart-item">
				<CardHeader>
					<CardTitle class="text-center">双轴图</CardTitle>
				</CardHeader>
				<CardContent>
					<div id="dual-axes-container" class="w-full h-[300px]"></div>
				</CardContent>
			</Card>
		</div>
	</PageContainer>
</template>

<style scoped>
/**
 * 首页样式优化
 * <AUTHOR>
 * 使用Tailwind CSS类，确保响应式布局不会超出容器宽度
 */

/* 确保图表容器不会超出宽度 */
.charts-grid {
	max-width: 100%;
}

.chart-item {
	max-width: 100%;
	min-width: 0;
}

/* 图表容器样式 */
#line-container,
#column-container,
#pie-container,
#dual-axes-container {
	max-width: 100%;
	overflow: hidden;
}

/* 移动端优化 */
@media (max-width: 1024px) {
	.charts-grid {
		grid-template-columns: 1fr;
	}
}

@media (max-width: 768px) {
	#line-container,
	#column-container,
	#pie-container,
	#dual-axes-container {
		height: 250px;
	}
}
</style>
