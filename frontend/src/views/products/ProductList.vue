<template>
	<div class="product-list">
		<!-- 页面头部 - 固定高度 -->
		<div class="header-section">
			<div class="flex justify-between items-center">
				<div class="flex space-x-4">
					<!-- 店铺筛选 -->
					<Select
						v-model="queryParams.shop_id"
						@update:model-value="handleFilterChange"
					>
						<SelectTrigger class="w-[180px]">
							<SelectValue placeholder="全部店铺" />
						</SelectTrigger>
						<SelectContent>
							<SelectItem :value="undefined">全部店铺</SelectItem>
							<!-- 这里可以添加店铺选项 -->
						</SelectContent>
					</Select>

					<!-- 状态筛选 -->
					<Select
						v-model="queryParams.is_active"
						@update:model-value="handleFilterChange"
					>
						<SelectTrigger class="w-[180px]">
							<SelectValue placeholder="全部状态" />
						</SelectTrigger>
						<SelectContent>
							<SelectItem :value="undefined">全部状态</SelectItem>
							<SelectItem :value="true">已启用</SelectItem>
							<SelectItem :value="false">已禁用</SelectItem>
						</SelectContent>
					</Select>

					<!-- 搜索框 -->
					<Input
						v-model="queryParams.keyword"
						@input="debounceSearch"
						type="text"
						placeholder="搜索商品名称或SKU..."
						class="w-64"
					/>

					<!-- 刷新按钮 -->
					<Button @click="refreshData" :disabled="loading" variant="default">
						{{ loading ? "加载中..." : "刷新" }}
					</Button>
				</div>
			</div>
		</div>

		<!-- 表格区域 - 占据剩余空间，可滚动 -->
		<div class="table-section">
			<Table class="min-w-[1400px]">
				<TableHeader class="sticky top-0 z-10 bg-gray-50">
					<TableRow>
						<TableHead
							class="w-[320px] min-w-[320px] px-6 py-3 text-xs font-medium text-gray-500 uppercase tracking-wider"
						>
							商品信息
						</TableHead>
						<TableHead
							class="w-[180px] min-w-[180px] px-6 py-3 text-xs font-medium text-gray-500 uppercase tracking-wider"
						>
							价格信息
						</TableHead>
						<TableHead
							class="w-[120px] min-w-[120px] px-6 py-3 text-xs font-medium text-gray-500 uppercase tracking-wider"
						>
							库存信息
						</TableHead>
						<TableHead
							class="w-[150px] min-w-[150px] px-6 py-3 text-xs font-medium text-gray-500 uppercase tracking-wider"
						>
							店铺信息
						</TableHead>
						<TableHead
							class="w-[100px] min-w-[100px] px-6 py-3 text-xs font-medium text-gray-500 uppercase tracking-wider"
						>
							状态
						</TableHead>
						<TableHead
							class="w-[140px] min-w-[140px] px-6 py-3 text-xs font-medium text-gray-500 uppercase tracking-wider"
						>
							创建时间
						</TableHead>
						<TableHead
							class="w-[170px] min-w-[150px] sticky right-0 bg-gray-50 z-15 shadow-[-4px_0_8px_-2px_rgba(0,0,0,0.1)] px-6 py-3 text-xs font-medium text-gray-500 uppercase tracking-wider"
						>
							操作
						</TableHead>
					</TableRow>
				</TableHeader>
				<TableBody>
					<!-- 加载状态 -->
					<TableRow v-if="loading">
						<TableCell colspan="7" class="h-24 text-center">
							<div class="flex justify-center items-center space-x-2">
								<div
									class="animate-spin rounded-full h-6 w-6 border-b-2 border-blue-600"
								></div>
								<span class="text-gray-500">加载中...</span>
							</div>
						</TableCell>
					</TableRow>

					<!-- 空状态 -->
					<TableRow v-else-if="!products.length">
						<TableCell colspan="7" class="h-24 text-center text-gray-500">
							暂无商品数据
						</TableCell>
					</TableRow>

					<!-- 商品数据行 -->
					<TableRow
						v-else
						v-for="product in products"
						:key="product.id"
						class="hover:bg-gray-50"
					>
						<!-- 商品信息 -->
						<TableCell class="whitespace-normal px-6 py-4">
							<div class="flex items-center">
								<div class="flex-shrink-0 h-16 w-16">
									<img
										:src="
											product.image_url ||
											'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNjQiIGhlaWdodD0iNjQiIHZpZXdCb3g9IjAgMCA2NCA2NCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHJlY3Qgd2lkdGg9IjY0IiBoZWlnaHQ9IjY0IiBmaWxsPSIjRjNGNEY2Ii8+CjxwYXRoIGQ9Ik0yMCAyMkg0NFYyNkgyMFYyMloiIGZpbGw9IiM5Q0EzQUYiLz4KPHA+YXRoIGQ9Ik0yMCAzMEg0NFYzNEgyMFYzMFoiIGZpbGw9IiM5Q0EzQUYiLz4KPHA+YXRoIGQ9Ik0yMCAzOEgzNlY0MkgyMFYzOFoiIGZpbGw9IiM5Q0EzQUYiLz4KPC9zdmc+'
										"
										:alt="product.name"
										class="h-16 w-16 rounded-lg object-cover"
										@error="handleImageError"
									/>
								</div>
								<div class="ml-4">
									<div class="text-sm font-medium text-gray-900">
										{{ product.name }}
									</div>
									<div class="text-sm text-gray-500">
										SKU: {{ product.sku }}
									</div>
									<div class="text-xs text-gray-400">
										商品ID: {{ product.offer_id }}
									</div>
								</div>
							</div>
						</TableCell>

						<!-- 价格信息 -->
						<TableCell class="whitespace-normal px-6 py-4 text-sm">
							<div class="text-gray-900 font-bold">
								售价: {{ product.currency }} {{ product.price.toFixed(2) }}
							</div>
							<div
								v-if="product.old_price > 0"
								class="text-gray-500 line-through"
							>
								原价: {{ product.currency }}
								{{ product.old_price.toFixed(2) }}
							</div>
							<div
								v-if="product.market_price > 0"
								class="text-blue-600 text-xs"
							>
								市场价: {{ product.currency }}
								{{ product.market_price.toFixed(2) }}
							</div>
							<div v-if="product.min_price > 0" class="text-red-600 text-xs">
								最低价: {{ product.currency }}
								{{ product.min_price.toFixed(2) }}
							</div>
						</TableCell>

						<!-- 库存信息 -->
						<TableCell class="px-6 py-4 text-sm">
							<div :class="getStockClass(product)" class="font-bold">
								库存: {{ product.stock }}
							</div>
							<div v-if="product.stock === 0" class="text-red-600 text-xs">
								⚠️ 缺货
							</div>
							<div
								v-else-if="product.stock < 10"
								class="text-orange-600 text-xs"
							>
								⚠️ 库存较低
							</div>
						</TableCell>

						<!-- 店铺信息 -->
						<TableCell class="px-6 py-4 text-sm">
							<div class="text-gray-900 font-medium">
								{{ product.shop_name }}
							</div>
							<div class="text-gray-500 text-xs">ID: {{ product.shop_id }}</div>
						</TableCell>

						<!-- 状态 -->
						<TableCell class="px-6 py-4">
							<Badge :variant="product.is_active ? 'default' : 'secondary'">
								{{ product.is_active ? "已启用" : "已禁用" }}
							</Badge>
						</TableCell>

						<!-- 创建时间 -->
						<TableCell class="px-6 py-4 text-sm text-gray-500">
							{{ formatDate(product.created_at) }}
						</TableCell>

						<!-- 操作 -->
						<TableCell
							class="sticky right-0 bg-white z-5 shadow-[-4px_0_8px_-2px_rgba(0,0,0,0.1)] px-6 py-4"
						>
							<div class="flex space-x-2">
								<Button
									variant="ghost"
									size="sm"
									class="text-blue-600 hover:text-blue-900"
								>
									查看
								</Button>
								<Button
									variant="ghost"
									size="sm"
									class="text-green-600 hover:text-green-900"
								>
									编辑
								</Button>
								<Button
									variant="ghost"
									size="sm"
									:class="
										product.is_active
											? 'text-red-600 hover:text-red-900'
											: 'text-green-600 hover:text-green-900'
									"
								>
									{{ product.is_active ? "禁用" : "启用" }}
								</Button>
							</div>
						</TableCell>
					</TableRow>
				</TableBody>
			</Table>
		</div>

		<!-- 分页组件 - 固定在底部 -->
		<div v-if="(queryParams.total || 0) > 0" class="pagination-section">
			<div class="flex items-center justify-between">
				<div class="text-sm text-gray-700">
					显示第 {{ (queryParams.page - 1) * queryParams.page_size + 1 }} -
					{{
						Math.min(
							queryParams.page * queryParams.page_size,
							queryParams.total || 0
						)
					}}
					条， 共 {{ queryParams.total || 0 }} 条记录
				</div>
				<div class="flex items-center space-x-2">
					<!-- 首页 -->
					<Button
						@click="changePage(1)"
						:disabled="queryParams.page <= 1"
						variant="outline"
						size="sm"
					>
						首页
					</Button>

					<!-- 上一页 -->
					<Button
						@click="changePage(queryParams.page - 1)"
						:disabled="queryParams.page <= 1"
						variant="outline"
						size="sm"
					>
						上一页
					</Button>

					<!-- 页码按钮 -->
					<div class="flex space-x-1">
						<Button
							v-for="page in visiblePages"
							:key="page"
							@click="changePage(page)"
							:variant="page === queryParams.page ? 'default' : 'outline'"
							size="sm"
						>
							{{ page }}
						</Button>
					</div>

					<!-- 页码跳转 -->
					<div class="flex items-center space-x-2 ml-4">
						<span class="text-sm text-gray-500">跳转到</span>
						<Input
							v-model.number="jumpPage"
							@keydown.enter="handleJumpPage"
							type="number"
							:min="1"
							:max="totalPages"
							class="w-16 text-center"
							placeholder="页码"
						/>
						<Button @click="handleJumpPage" variant="outline" size="sm">
							跳转
						</Button>
					</div>

					<!-- 下一页 -->
					<Button
						@click="changePage(queryParams.page + 1)"
						:disabled="queryParams.page >= totalPages"
						variant="outline"
						size="sm"
					>
						下一页
					</Button>

					<!-- 尾页 -->
					<Button
						@click="changePage(totalPages)"
						:disabled="queryParams.page >= totalPages"
						variant="outline"
						size="sm"
					>
						尾页
					</Button>

					<!-- 每页条数选择 -->
					<div class="flex items-center space-x-2 ml-4">
						<span class="text-sm text-gray-500">每页</span>
						<Select
							v-model.number="queryParams.page_size"
							@update:model-value="changePageSize"
						>
							<SelectTrigger class="w-[80px]">
								<SelectValue />
							</SelectTrigger>
							<SelectContent>
								<SelectItem :value="10">10条</SelectItem>
								<SelectItem :value="20">20条</SelectItem>
								<SelectItem :value="50">50条</SelectItem>
								<SelectItem :value="100">100条</SelectItem>
							</SelectContent>
						</Select>
					</div>
				</div>
			</div>
		</div>
	</div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, computed } from "vue";
import { ProductApi } from "@/api";
import type { IProductResponse, IProductQueryParams } from "@/types/api";
import {
	Table,
	TableBody,
	TableCell,
	TableHead,
	TableHeader,
	TableRow,
} from "@/components/ui/table";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Badge } from "@/components/ui/badge";
import {
	Select,
	SelectContent,
	SelectItem,
	SelectTrigger,
	SelectValue,
} from "@/components/ui/select";

// 定义页面内部使用的查询参数类型
interface IPageQueryParams {
	page: number;
	page_size: number;
	is_active?: boolean;
	keyword?: string;
	shop_id?: number;
	sku?: number;
	total: number;
}

// 响应式数据
const loading = ref<boolean>(false);
const products = ref<IProductResponse[]>([]);
const jumpPage = ref<string>("");

// 查询参数 - 统一的分页状态管理
const queryParams = reactive<IPageQueryParams>({
	page: 1,
	page_size: 20,
	is_active: undefined,
	keyword: "",
	shop_id: undefined,
	sku: undefined,
	total: 0,
});

// 防抖搜索定时器
let searchTimer: NodeJS.Timeout | null = null;

// 是否为开发环境 - 使用条件判断避免import.meta警告
const isDev =
	typeof window !== "undefined" && window.location.hostname === "localhost";

/**
 * 计算总页数
 * <AUTHOR>
 */
const totalPages = computed(() => {
	const total = queryParams.total;
	const pageSize = queryParams.page_size;
	return Math.ceil(total / pageSize);
});

/**
 * 计算可见的页码数组
 * <AUTHOR>
 */
const visiblePages = computed(() => {
	const total = totalPages.value;
	const current = queryParams.page;
	const delta = 2; // 当前页前后显示的页数

	let start = Math.max(1, current - delta);
	let end = Math.min(total, current + delta);

	// 如果开始页离第一页太近，则从第一页开始
	if (start <= 3) {
		start = 1;
		end = Math.min(total, start + 4);
	}

	// 如果结束页离最后一页太近，则到最后一页结束
	if (end >= total - 2) {
		end = total;
		start = Math.max(1, end - 4);
	}

	const pages: number[] = [];
	for (let i = start; i <= end; i++) {
		pages.push(i);
	}

	return pages;
});

/**
 * 加载商品列表数据
 * <AUTHOR>
 */
const loadProducts = async (): Promise<void> => {
	try {
		loading.value = true;
		// 准备查询参数，移除undefined和empty值
		const params: IProductQueryParams = {};
		if (queryParams.shop_id) params.shop_id = queryParams.shop_id;
		if (queryParams.sku) params.sku = queryParams.sku;
		if (queryParams.keyword?.trim())
			params.keyword = queryParams.keyword.trim();
		if (queryParams.is_active !== undefined)
			params.is_active = queryParams.is_active;
		params.page = queryParams.page;
		params.page_size = queryParams.page_size;

		const response = await ProductApi.getProducts(params);
		console.log(`获取商品成功`, response);
		products.value = response.data;
		queryParams.total = response.total;
	} catch (error) {
		console.error("加载商品列表失败:", error);
		// 在开发环境下提供更详细的错误信息
		if (isDev) {
			console.warn("请确保后端服务正在运行在 http://localhost:8080");
			console.warn(
				"可以访问 http://localhost:8080/swagger/index.html 查看API文档"
			);
		}
		// 重置数据
		products.value = [];
		queryParams.total = 0;
	} finally {
		loading.value = false;
	}
};

/**
 * 刷新所有数据
 * <AUTHOR>
 */
const refreshData = async (): Promise<void> => {
	await loadProducts();
};

/**
 * 防抖搜索
 * <AUTHOR>
 */
const debounceSearch = (): void => {
	if (searchTimer) {
		clearTimeout(searchTimer);
	}
	searchTimer = setTimeout(() => {
		queryParams.page = 1; // 搜索时重置到第一页
		loadProducts();
	}, 500);
};

/**
 * 切换页码
 * <AUTHOR>
 */
const changePage = (page: number): void => {
	if (page < 1 || page > totalPages.value || page === queryParams.page) {
		return;
	}
	queryParams.page = page;
	loadProducts();
};

/**
 * 处理页码跳转
 * <AUTHOR>
 */
const handleJumpPage = (): void => {
	const pageNum = Number(jumpPage.value);
	if (jumpPage.value && pageNum >= 1 && pageNum <= totalPages.value) {
		changePage(pageNum);
		jumpPage.value = ""; // 清空输入框
	}
};

/**
 * 改变每页条数
 * <AUTHOR>
 */
const changePageSize = (): void => {
	queryParams.page = 1; // 重置到第一页
	loadProducts();
};

/**
 * 获取库存样式类
 * <AUTHOR>
 */
const getStockClass = (product: IProductResponse): string => {
	if (product.stock === 0) {
		return "text-red-600";
	} else if (product.stock < 10) {
		return "text-orange-600";
	}
	return "text-green-600";
};

/**
 * 格式化日期
 * <AUTHOR>
 */
const formatDate = (dateString: string): string => {
	const date = new Date(dateString);
	return date.toLocaleDateString("zh-CN", {
		year: "numeric",
		month: "2-digit",
		day: "2-digit",
	});
};

/**
 * 处理图片加载错误
 * <AUTHOR>
 */
const handleImageError = (event: Event): void => {
	const img = event.target as HTMLImageElement;
	// 使用SVG数据URL作为占位符图片
	img.src =
		"data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNjQiIGhlaWdodD0iNjQiIHZpZXdCb3g9IjAgMCA2NCA2NCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHJlY3Qgd2lkdGg9IjY0IiBoZWlnaHQ9IjY0IiBmaWxsPSIjRjNGNEY2Ii8+CjxwYXRoIGQ9Ik0yMCAyMkg0NFYyNkgyMFYyMloiIGZpbGw9IiM5Q0EzQUYiLz4KPHA+YXRoIGQ9Ik0yMCAzMEg0NFYzNEgyMFYzMFoiIGZpbGw9IiM5Q0EzQUYiLz4KPHA+YXRoIGQ9Ik0yMCAzOEgzNlY0MkgyMFYzOFoiIGZpbGw9IiM5Q0EzQUYiLz4KPC9zdmc+";
};

/**
 * 处理过滤条件变化
 * <AUTHOR>
 */
const handleFilterChange = (): void => {
	queryParams.page = 1; // 重置到第一页
	loadProducts();
};

// 组件挂载时加载数据
onMounted(() => {
	refreshData();
});
</script>

<style scoped>
/**
 * 商品列表页面样式
 * <AUTHOR>
 * 使用 shadcn-vue Table 组件的优化样式，保持响应式设计和良好的用户体验
 */

/* 主容器 - 填充全部可用空间 */
.product-list {
	display: flex;
	flex-direction: column;
	flex: 1;
	height: 100%; /* 确保占满父容器高度 */
	min-height: 0; /* 关键：确保flex子项可以收缩 */
	width: 100%;
	background-color: #ffffff;
	overflow: hidden; /* 页面本身不滚动 */
}

/* 头部区域 - 固定高度 */
.header-section {
	flex-shrink: 0;
	padding: 1.5rem;
	background-color: #ffffff;
	border-bottom: 1px solid #e5e7eb;
	box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1);
}

/* 表格区域 - 占据剩余空间，可滚动 */
.table-section {
	flex: 1;
	min-height: 0; /* 关键：确保可以收缩 */
	overflow: auto; /* 允许水平和垂直滚动 */
	background-color: #ffffff;
}

/* shadcn-vue Table 组件样式优化 */
.table-section :deep([data-slot="table-container"]) {
	height: 100%;
	overflow: auto;
}

.table-section :deep([data-slot="table"]) {
	border-collapse: collapse;
	min-width: 1400px; /* 确保表格有足够的宽度 */
}

/* 表格头部固定样式 */
.table-section :deep(thead) {
	position: sticky;
	top: 0;
	z-index: 10;
	background-color: #f9fafb;
}

/* 表格单元格基础样式 */
.table-section :deep(th),
.table-section :deep(td) {
	padding: 1rem 1.5rem;
	text-align: left;
	border-bottom: 1px solid #e5e7eb;
	vertical-align: top; /* 顶部对齐 */
}

/* 表格头部样式 */
.table-section :deep(th) {
	font-size: 0.75rem;
	font-weight: 500;
	color: #6b7280;
	text-transform: uppercase;
	letter-spacing: 0.05em;
	background-color: #f9fafb;
}

/* 表格行悬停效果 */
.table-section :deep(tbody tr:hover) {
	background-color: #f9fafb;
}

/* 操作列在行悬停时的背景色 */
.table-section :deep(tbody tr:hover td:last-child) {
	background-color: #f9fafb; /* 与行悬停背景色一致 */
}

/* 自定义滚动条样式 */
.table-section::-webkit-scrollbar {
	width: 6px;
	height: 6px; /* 水平滚动条高度 */
}

.table-section::-webkit-scrollbar-track {
	background: #f1f5f9;
	border-radius: 3px;
}

.table-section::-webkit-scrollbar-thumb {
	background: #cbd5e1;
	border-radius: 3px;
}

.table-section::-webkit-scrollbar-thumb:hover {
	background: #94a3b8;
}

/* 滚动条交叉点样式 */
.table-section::-webkit-scrollbar-corner {
	background: #f1f5f9;
}

/* 加载动画 */
@keyframes spin {
	to {
		transform: rotate(360deg);
	}
}

.animate-spin {
	animation: spin 1s linear infinite;
}

/* 分页区域 - 固定在底部 */
.pagination-section {
	flex-shrink: 0;
	padding: 1rem 1.5rem;
	background-color: #ffffff;
	border-top: 1px solid #e5e7eb;
	box-shadow: 0 -1px 3px 0 rgba(0, 0, 0, 0.1);
}

/* 响应式优化 */
@media (max-width: 768px) {
	.header-section {
		padding: 1rem;
	}

	.pagination-section {
		padding: 0.75rem 1rem;
	}

	/* 移动端表格最小宽度调整 */
	.table-section :deep([data-slot="table"]) {
		min-width: 1200px; /* 移动端稍微减少最小宽度 */
	}

	.table-section :deep(th),
	.table-section :deep(td) {
		padding: 0.75rem 1rem;
		font-size: 0.875rem;
	}
}

@media (max-width: 480px) {
	.header-section {
		padding: 0.75rem;
	}

	/* 超小屏幕表格最小宽度 */
	.table-section :deep([data-slot="table"]) {
		min-width: 1000px;
	}

	.table-section :deep(th),
	.table-section :deep(td) {
		padding: 0.5rem 0.75rem;
		font-size: 0.75rem;
	}
}

/* 状态徽章样式优化 */
.table-section :deep(.inline-flex) {
	display: inline-flex;
	align-items: center;
	justify-content: center;
	white-space: nowrap;
}

/* 图片样式优化 */
.table-section :deep(img) {
	border-radius: 0.5rem;
	object-fit: cover;
	transition: transform 0.2s ease-in-out;
}

.table-section :deep(img:hover) {
	transform: scale(1.05);
}

/* 操作按钮样式优化 */
.table-section :deep(button) {
	transition: all 0.2s ease-in-out;
	font-weight: 500;
}

.table-section :deep(button:hover) {
	text-decoration: underline;
}

/* 价格信息样式优化 */
.table-section :deep(.font-bold) {
	font-weight: 600;
}

.table-section :deep(.line-through) {
	text-decoration: line-through;
}

/* 库存警告样式 */
.table-section :deep(.text-red-600) {
	color: #dc2626;
	font-weight: 600;
}

.table-section :deep(.text-orange-600) {
	color: #ea580c;
	font-weight: 600;
}

.table-section :deep(.text-green-600) {
	color: #16a34a;
	font-weight: 600;
}
</style>
