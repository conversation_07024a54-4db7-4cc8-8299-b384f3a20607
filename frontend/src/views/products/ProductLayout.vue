<template>
	<div class="product-layout">
		<router-view class="h-full" />
	</div>
</template>

<script setup lang="ts">
/**
 * 商品管理布局组件
 * <AUTHOR>
 * 使用flex-1自适应父容器剩余高度，确保在有header的情况下正确显示
 * 采用flex布局，后续的layout组件可以直接使用而无需考虑全局布局的高度计算
 */
</script>

<style scoped>
.product-layout {
	display: flex;
	flex-direction: column;
	flex: 1;
	height: 100%; /* 确保占满父容器高度 */
	min-height: 0; /* 关键：确保flex子项可以收缩 */
	background-color: #fafafa;
	overflow: hidden; /* 防止页面滚动 */
}
</style>
