<template>
	<div class="min-h-screen bg-gray-100 py-12 px-4 sm:px-6 lg:px-8">
		<div class="max-w-4xl mx-auto">
			<!-- 标题 -->
			<div class="text-center mb-8">
				<h1 class="text-3xl font-bold text-gray-900 mb-4">
					9Wings ERP 登录系统演示
				</h1>
				<p class="text-lg text-gray-600">
					完整的JWT认证系统，包含登录、权限管理和状态持久化
				</p>
			</div>

			<div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
				<!-- 登录表单区域 -->
				<div class="bg-white rounded-lg shadow-lg p-6">
					<h2 class="text-xl font-semibold text-gray-900 mb-4">登录测试</h2>

					<!-- 当前状态显示 -->
					<div class="mb-6 p-4 rounded-lg" :class="statusBgClass">
						<div class="flex items-center space-x-2">
							<div class="h-3 w-3 rounded-full" :class="statusDotClass"></div>
							<span class="font-medium">{{ statusText }}</span>
						</div>
						<div v-if="user" class="mt-2 text-sm">
							<p>用户: {{ user.username }}</p>
							<p>角色: {{ user.role }}</p>
							<p>权限: {{ userPermissions.join(", ") }}</p>
						</div>
					</div>

					<!-- 登录表单 -->
					<div v-if="!isAuthenticated">
						<LoginForm />
					</div>

					<!-- 已登录状态 -->
					<div v-else class="space-y-4">
						<div class="p-4 bg-green-50 border border-green-200 rounded-lg">
							<h3 class="font-medium text-green-800 mb-2">登录成功!</h3>
							<p class="text-sm text-green-600">
								您已成功登录系统，token已保存到localStorage
							</p>
						</div>

						<div class="space-y-2">
							<Button @click="testApiCall" :disabled="isLoading" class="w-full">
								测试API调用
							</Button>
							<Button
								@click="logout"
								:disabled="isLoading"
								variant="outline"
								class="w-full"
							>
								登出
							</Button>
						</div>
					</div>
				</div>

				<!-- 功能说明区域 -->
				<div class="bg-white rounded-lg shadow-lg p-6">
					<h2 class="text-xl font-semibold text-gray-900 mb-4">系统特性</h2>

					<div class="space-y-4">
						<!-- JWT认证 -->
						<div class="flex items-start space-x-3">
							<div
								class="flex-shrink-0 h-8 w-8 bg-blue-100 rounded-lg flex items-center justify-center"
							>
								<svg
									class="h-5 w-5 text-blue-600"
									fill="none"
									stroke="currentColor"
									viewBox="0 0 24 24"
								>
									<path
										stroke-linecap="round"
										stroke-linejoin="round"
										stroke-width="2"
										d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"
									/>
								</svg>
							</div>
							<div>
								<h3 class="font-medium text-gray-900">JWT认证</h3>
								<p class="text-sm text-gray-600">
									使用JWT token进行用户认证，支持token自动刷新
								</p>
							</div>
						</div>

						<!-- 权限控制 -->
						<div class="flex items-start space-x-3">
							<div
								class="flex-shrink-0 h-8 w-8 bg-green-100 rounded-lg flex items-center justify-center"
							>
								<svg
									class="h-5 w-5 text-green-600"
									fill="none"
									stroke="currentColor"
									viewBox="0 0 24 24"
								>
									<path
										stroke-linecap="round"
										stroke-linejoin="round"
										stroke-width="2"
										d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z"
									/>
								</svg>
							</div>
							<div>
								<h3 class="font-medium text-gray-900">权限控制</h3>
								<p class="text-sm text-gray-600">
									基于角色的权限系统，支持路由级别的权限控制
								</p>
							</div>
						</div>

						<!-- 状态持久化 -->
						<div class="flex items-start space-x-3">
							<div
								class="flex-shrink-0 h-8 w-8 bg-purple-100 rounded-lg flex items-center justify-center"
							>
								<svg
									class="h-5 w-5 text-purple-600"
									fill="none"
									stroke="currentColor"
									viewBox="0 0 24 24"
								>
									<path
										stroke-linecap="round"
										stroke-linejoin="round"
										stroke-width="2"
										d="M7 16l-4-4m0 0l4-4m-4 4h18"
									/>
								</svg>
							</div>
							<div>
								<h3 class="font-medium text-gray-900">状态持久化</h3>
								<p class="text-sm text-gray-600">
									登录状态保存到localStorage，页面刷新后自动恢复
								</p>
							</div>
						</div>

						<!-- 路由守卫 -->
						<div class="flex items-start space-x-3">
							<div
								class="flex-shrink-0 h-8 w-8 bg-yellow-100 rounded-lg flex items-center justify-center"
							>
								<svg
									class="h-5 w-5 text-yellow-600"
									fill="none"
									stroke="currentColor"
									viewBox="0 0 24 24"
								>
									<path
										stroke-linecap="round"
										stroke-linejoin="round"
										stroke-width="2"
										d="M9 20l-5.447-2.724A1 1 0 013 16.382V5.618a1 1 0 011.447-.894L9 7m0 13l6-3m-6 3V7m6 10l4.553 2.276A1 1 0 0021 18.382V7.618a1 1 0 00-.553-.894L15 4m0 13V4m0 0L9 7"
									/>
								</svg>
							</div>
							<div>
								<h3 class="font-medium text-gray-900">路由守卫</h3>
								<p class="text-sm text-gray-600">
									自动处理认证重定向，保护需要登录的页面
								</p>
							</div>
						</div>
					</div>

					<!-- 测试账号信息 -->
					<div class="mt-6 p-4 bg-gray-50 rounded-lg">
						<h3 class="font-medium text-gray-900 mb-2">测试账号</h3>
						<div class="text-sm text-gray-600 space-y-1">
							<p><strong>管理员:</strong> admin / admin123</p>
							<p><strong>经理:</strong> manager / manager123</p>
							<p><strong>操作员:</strong> operator / operator123</p>
						</div>
					</div>
				</div>
			</div>

			<!-- API调用结果 -->
			<div v-if="apiResult" class="mt-8 bg-white rounded-lg shadow-lg p-6">
				<h2 class="text-xl font-semibold text-gray-900 mb-4">API调用结果</h2>
				<pre class="bg-gray-100 p-4 rounded-lg text-sm overflow-x-auto">{{
					apiResult
				}}</pre>
			</div>
		</div>
	</div>
</template>

<script setup lang="ts">
/**
 * 登录系统演示页面
 * 展示完整的登录功能和JWT认证流程
 * <AUTHOR>
 */

import { ref, computed } from "vue";
import { useRouter } from "vue-router";
import { Button } from "@/components/ui/button";
import LoginForm from "@/components/auth/LoginForm.vue";
import { useAuth } from "@/stores/auth";
import { AuthApi } from "@/api/auth";

/**
 * 组件状态
 */
const router = useRouter();
const auth = useAuth();
const apiResult = ref("");

/**
 * 计算属性
 */
const isAuthenticated = computed(() => auth.isAuthenticated.value);
const user = computed(() => auth.user.value);
const isLoading = computed(() => auth.isLoading.value);
const userPermissions = computed(() => auth.userPermissions.value);

const statusText = computed(() => {
	if (isAuthenticated.value) {
		return "已登录";
	} else if (auth.token.value) {
		return "Token无效";
	} else {
		return "未登录";
	}
});

const statusBgClass = computed(() => {
	if (isAuthenticated.value) {
		return "bg-green-50 border-green-200";
	} else if (auth.token.value) {
		return "bg-yellow-50 border-yellow-200";
	} else {
		return "bg-gray-50 border-gray-200";
	}
});

const statusDotClass = computed(() => {
	if (isAuthenticated.value) {
		return "bg-green-400";
	} else if (auth.token.value) {
		return "bg-yellow-400";
	} else {
		return "bg-gray-400";
	}
});

/**
 * 测试API调用
 */
const testApiCall = async () => {
	try {
		apiResult.value = "API调用中...";

		// 测试获取当前用户信息
		const userInfo = await AuthApi.getCurrentUser();

		apiResult.value = JSON.stringify(
			{
				success: true,
				data: userInfo,
				timestamp: new Date().toISOString(),
			},
			null,
			2
		);
	} catch (error: any) {
		apiResult.value = JSON.stringify(
			{
				success: false,
				error: error.message,
				timestamp: new Date().toISOString(),
			},
			null,
			2
		);
	}
};

/**
 * 登出
 */
const logout = async () => {
	try {
		await auth.logout();
		apiResult.value = "";
	} catch (error) {
		console.error("登出失败:", error);
	}
};
</script>
