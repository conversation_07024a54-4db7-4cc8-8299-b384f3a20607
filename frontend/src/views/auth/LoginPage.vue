<template>
	<div
		class="min-h-screen flex items-center justify-center bg-gray-50 py-12 px-4 sm:px-6 lg:px-8"
	>
		<div class="max-w-md w-full space-y-8">
			<!-- 页面头部 -->
			<div class="text-center">
				<div
					class="mx-auto h-12 w-12 flex items-center justify-center bg-primary rounded-lg"
				>
					<svg
						class="h-8 w-8 text-white"
						fill="none"
						stroke="currentColor"
						viewBox="0 0 24 24"
					>
						<path
							stroke-linecap="round"
							stroke-linejoin="round"
							stroke-width="2"
							d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-4m-5 0H9m0 0H5m0 0v-4"
						/>
					</svg>
				</div>
				<h2 class="mt-6 text-3xl font-bold text-gray-900">9Wings ERP 系统</h2>
				<p class="mt-2 text-sm text-gray-600">外贸订单管理系统</p>
			</div>

			<!-- 登录表单 -->
			<div class="flex justify-center">
				<LoginForm />
			</div>

			<!-- 页面底部信息 -->
			<div class="text-center">
				<div
					class="flex items-center justify-center space-x-4 text-sm text-gray-500"
				>
					<span>系统特性</span>
					<div class="flex space-x-2">
						<span
							class="px-2 py-1 bg-blue-100 text-blue-800 rounded-full text-xs"
							>订单管理</span
						>
						<span
							class="px-2 py-1 bg-green-100 text-green-800 rounded-full text-xs"
							>库存管理</span
						>
						<span
							class="px-2 py-1 bg-purple-100 text-purple-800 rounded-full text-xs"
							>平台同步</span
						>
					</div>
				</div>

				<div class="mt-4 text-xs text-gray-400">
					<p>© 2024 9Wings ERP. All rights reserved.</p>
					<p class="mt-1">Powered by Vue 3 + TypeScript + Tailwind CSS</p>
				</div>
			</div>
		</div>

		<!-- 背景装饰 -->
		<div class="fixed inset-0 -z-10 overflow-hidden">
			<div
				class="absolute -top-40 -right-32 w-96 h-96 bg-primary/5 rounded-full blur-3xl"
			></div>
			<div
				class="absolute -bottom-40 -left-32 w-96 h-96 bg-blue-500/5 rounded-full blur-3xl"
			></div>
		</div>

		<!-- 开发环境提示 -->
		<div v-if="isDevelopment" class="fixed top-4 right-4 z-50">
			<div
				class="bg-yellow-100 border border-yellow-400 text-yellow-700 px-4 py-3 rounded-lg shadow-lg max-w-sm"
			>
				<div class="flex items-center space-x-2">
					<svg class="h-5 w-5" fill="currentColor" viewBox="0 0 20 20">
						<path
							fill-rule="evenodd"
							d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z"
							clip-rule="evenodd"
						/>
					</svg>
					<span class="font-medium">开发模式</span>
				</div>
				<div class="mt-2 text-sm">
					<p>测试账号:</p>
					<p><strong>用户名:</strong> admin</p>
					<p><strong>密码:</strong> admin123</p>
				</div>
			</div>
		</div>
	</div>
</template>

<script setup lang="ts">
/**
 * 登录页面
 * 包含登录表单和页面布局
 * <AUTHOR>
 */

import { computed } from "vue";
import { useAuth } from "@/stores/auth";
import { useRouter } from "vue-router";
import LoginForm from "@/components/auth/LoginForm.vue";

/**
 * 页面状态
 */
const auth = useAuth();
const router = useRouter();

/**
 * 计算属性
 */
const isDevelopment = computed(() => {
	return process.env.NODE_ENV === "development";
});

/**
 * 页面元数据
 */
import { onMounted } from "vue";

onMounted(() => {
	// 设置页面标题
	document.title = "登录 - 9Wings ERP系统";

	// 如果已经登录，直接跳转
	if (auth.isAuthenticated.value) {
		router.push("/home");
	}
});
</script>

<style scoped>
/* 自定义样式 */
.bg-primary {
	background-color: hsl(var(--primary));
}

.text-primary {
	color: hsl(var(--primary));
}

/* 确保背景装饰在最底层 */
.fixed.-z-10 {
	z-index: -10;
}

/* 响应式设计 */
@media (max-width: 640px) {
	.min-h-screen {
		min-height: 100vh;
		min-height: 100dvh; /* 移动端动态视口高度 */
	}
}
</style>
