/** * 订单列表组件 * <AUTHOR> *
显示所有订单的列表视图，支持订单筛选、状态管理和基础操作 *
参考ProductList.vue的设计风格，保持界面一致性 */

<template>
	<div class="order-list">
		<!-- 页面头部 - 固定高度 -->
		<div class="header-section">
			<div
				class="flex flex-col lg:flex-row lg:justify-between lg:items-center gap-4"
			>
				<!-- 筛选条件区域 -->
				<div class="flex flex-wrap items-center gap-3">
					<!-- 状态筛选 -->
					<Select
						v-model="queryParams.status"
						@update:model-value="handleFilterChange"
					>
						<SelectTrigger class="w-[160px]">
							<SelectValue placeholder="全部状态" />
						</SelectTrigger>
						<SelectContent>
							<SelectItem :value="undefined">全部状态</SelectItem>
							<SelectItem value="new">未接单</SelectItem>
							<SelectItem value="accepted">已接单</SelectItem>
							<SelectItem value="purchased">已采购</SelectItem>
							<SelectItem value="shipped">已打单</SelectItem>
						</SelectContent>
					</Select>

					<!-- 店铺筛选 -->
					<Select
						v-model="queryParams.shop_id"
						@update:model-value="handleFilterChange"
					>
						<SelectTrigger class="w-[160px]">
							<SelectValue placeholder="全部店铺" />
						</SelectTrigger>
						<SelectContent>
							<SelectItem :value="undefined">全部店铺</SelectItem>
							<SelectItem v-for="shop in shops" :key="shop.id" :value="shop.id">
								{{ shop.name }}
							</SelectItem>
						</SelectContent>
					</Select>

					<!-- 日期筛选 -->
					<DateRangePicker
						v-model="dateRangeValue"
						@change="handleDateRangeChange"
						placeholder="选择日期范围"
					/>
				</div>

				<!-- 操作按钮区域 -->
				<div class="flex flex-wrap items-center gap-3">
					<!-- 同步按钮 -->
					<Button
						@click="handleSyncOrders"
						:disabled="loading"
						variant="default"
						class="bg-green-600 hover:bg-green-700"
					>
						同步订单
					</Button>

					<!-- 刷新按钮 -->
					<Button @click="refreshData" :disabled="loading" variant="default">
						{{ loading ? "加载中..." : "刷新" }}
					</Button>

					<!-- 清除筛选 -->
					<Button @click="handleClearFilters" variant="secondary">
						清除筛选
					</Button>
				</div>
			</div>
		</div>

		<!-- 表格区域 - 占据剩余空间，可滚动 -->
		<div class="table-section">
			<Table class="min-w-[1600px]">
				<TableHeader class="sticky top-0 z-10 bg-gray-50">
					<TableRow>
						<TableHead
							class="w-[200px] min-w-[200px] px-6 py-3 text-xs font-medium text-gray-500 uppercase tracking-wider"
						>
							订单信息
						</TableHead>
						<TableHead
							class="w-[180px] min-w-[180px] px-6 py-3 text-xs font-medium text-gray-500 uppercase tracking-wider"
						>
							客户信息
						</TableHead>
						<TableHead
							class="w-[120px] min-w-[120px] px-6 py-3 text-xs font-medium text-gray-500 uppercase tracking-wider"
						>
							订单状态
						</TableHead>
						<TableHead
							class="w-[140px] min-w-[140px] px-6 py-3 text-xs font-medium text-gray-500 uppercase tracking-wider"
						>
							金额信息
						</TableHead>
						<TableHead
							class="w-[140px] min-w-[140px] px-6 py-3 text-xs font-medium text-gray-500 uppercase tracking-wider"
						>
							采购信息
						</TableHead>
						<TableHead
							class="w-[150px] min-w-[150px] px-6 py-3 text-xs font-medium text-gray-500 uppercase tracking-wider"
						>
							店铺信息
						</TableHead>
						<TableHead
							class="w-[140px] min-w-[140px] px-6 py-3 text-xs font-medium text-gray-500 uppercase tracking-wider"
						>
							发货日期
						</TableHead>
						<TableHead
							class="w-[120px] min-w-[180px] sticky right-0 bg-gray-50 z-15 shadow-[-4px_0_8px_-2px_rgba(0,0,0,0.1)] px-6 py-3 text-xs font-medium text-gray-500 uppercase tracking-wider"
						>
							操作
						</TableHead>
					</TableRow>
				</TableHeader>
				<TableBody>
					<!-- 加载状态 -->
					<TableRow v-if="loading">
						<TableCell colspan="8" class="h-24 text-center">
							<div class="flex justify-center items-center space-x-2">
								<div
									class="animate-spin rounded-full h-6 w-6 border-b-2 border-blue-600"
								></div>
								<span class="text-gray-500">加载中...</span>
							</div>
						</TableCell>
					</TableRow>

					<!-- 空状态 -->
					<TableRow v-else-if="!orders.length">
						<TableCell colspan="8" class="h-24 text-center text-gray-500">
							暂无订单数据
						</TableCell>
					</TableRow>

					<!-- 订单数据行 -->
					<TableRow
						v-else
						v-for="order in orders"
						:key="order.id"
						class="hover:bg-gray-50"
					>
						<!-- 订单信息 -->
						<TableCell class="whitespace-normal px-6 py-4">
							<div class="text-sm font-medium text-gray-900">
								{{ order.posting_number }}
							</div>
							<div class="text-sm text-gray-500">
								外部ID: {{ order.external_id }}
							</div>
							<div class="text-xs text-gray-400">订单ID: {{ order.id }}</div>
						</TableCell>

						<!-- 客户信息 -->
						<TableCell class="whitespace-normal px-6 py-4">
							<div class="text-sm font-medium text-gray-900">
								{{ order.customer_name || "未知客户" }}
							</div>
							<div class="text-sm text-gray-500">
								订单号: {{ order.posting_number }}
							</div>
							<div class="text-xs text-gray-400">
								外部ID: {{ order.external_id }}
							</div>
						</TableCell>

						<!-- 订单状态 -->
						<TableCell class="px-6 py-4">
							<Badge :variant="getStatusBadgeVariant(order.status)">
								{{ getStatusText(order.status) }}
							</Badge>
						</TableCell>

						<!-- 金额信息 -->
						<TableCell class="whitespace-normal px-6 py-4 text-sm">
							<div class="text-gray-900 font-bold">
								订单: ¥{{ order.total_amount.toFixed(2) }}
							</div>
							<div
								v-if="order.items && order.items.length > 0"
								class="text-blue-600 text-xs"
							>
								商品数: {{ order.items.length }}
							</div>
						</TableCell>

						<!-- 采购信息 -->
						<TableCell class="whitespace-normal px-6 py-4 text-sm">
							<div
								v-if="order.purchase_amount > 0"
								class="text-gray-900 font-bold"
							>
								¥{{ order.purchase_amount.toFixed(2) }}
							</div>
							<div v-else class="text-gray-400">未填写</div>
							<div
								v-if="order.purchase_amount > 0 && order.total_amount > 0"
								:class="getProfitClass(order)"
								class="text-xs font-medium"
							>
								利润: ¥{{
									(order.total_amount - order.purchase_amount).toFixed(2)
								}}
							</div>
						</TableCell>

						<!-- 店铺信息 -->
						<TableCell class="px-6 py-4 text-sm">
							<div class="text-gray-900 font-medium">
								{{ order.shop_name }}
							</div>
							<div class="text-gray-500 text-xs">ID: {{ order.shop_id }}</div>
						</TableCell>

						<!-- 发货日期 -->
						<TableCell class="px-6 py-4 text-sm text-gray-500">
							{{ formatDate(order.shipment_date) }}
						</TableCell>

						<!-- 操作 -->
						<TableCell
							class="sticky right-0 bg-white z-5 shadow-[-4px_0_8px_-2px_rgba(0,0,0,0.1)] px-6 py-4"
						>
							<div class="flex space-x-2">
								<Button
									@click="handleViewOrder(order)"
									variant="ghost"
									size="sm"
									class="text-blue-600 hover:text-blue-900"
								>
									查看
								</Button>
								<Button
									v-if="order.status === 'new'"
									@click="handleAcceptOrder(order)"
									variant="ghost"
									size="sm"
									class="text-green-600 hover:text-green-900"
								>
									接单
								</Button>
								<Button
									v-if="order.status === 'accepted'"
									@click="handleEditPurchase(order)"
									variant="ghost"
									size="sm"
									class="text-orange-600 hover:text-orange-900"
								>
									填写采购
								</Button>
								<Button
									v-if="order.status === 'purchased'"
									@click="handlePrintOrder(order)"
									variant="ghost"
									size="sm"
									class="text-purple-600 hover:text-purple-900"
								>
									打印运单
								</Button>
								<Button
									@click="handleSyncSingleOrder(order)"
									variant="ghost"
									size="sm"
									class="text-gray-600 hover:text-gray-900"
								>
									同步
								</Button>
							</div>
						</TableCell>
					</TableRow>
				</TableBody>
			</Table>
		</div>

		<!-- 分页组件 - 固定在底部 -->
		<div v-if="(queryParams.total || 0) > 0" class="pagination-section">
			<div class="flex items-center justify-between">
				<div class="text-sm text-gray-700">
					显示第 {{ (queryParams.page - 1) * queryParams.page_size + 1 }} -
					{{
						Math.min(
							queryParams.page * queryParams.page_size,
							queryParams.total || 0
						)
					}}
					条， 共 {{ queryParams.total || 0 }} 条记录
				</div>
				<div class="flex items-center space-x-2">
					<!-- 首页 -->
					<Button
						@click="changePage(1)"
						:disabled="queryParams.page <= 1"
						variant="outline"
						size="sm"
					>
						首页
					</Button>

					<!-- 上一页 -->
					<Button
						@click="changePage(queryParams.page - 1)"
						:disabled="queryParams.page <= 1"
						variant="outline"
						size="sm"
					>
						上一页
					</Button>

					<!-- 页码按钮 -->
					<div class="flex space-x-1">
						<Button
							v-for="page in visiblePages"
							:key="page"
							@click="changePage(page)"
							:variant="page === queryParams.page ? 'default' : 'outline'"
							size="sm"
						>
							{{ page }}
						</Button>
					</div>

					<!-- 页码跳转 -->
					<div class="flex items-center space-x-2 ml-4">
						<span class="text-sm text-gray-500">跳转到</span>
						<Input
							v-model.number="jumpPage"
							@keydown.enter="handleJumpPage"
							type="number"
							:min="1"
							:max="totalPages"
							class="w-16 text-center"
							placeholder="页码"
						/>
						<Button @click="handleJumpPage" variant="outline" size="sm">
							跳转
						</Button>
					</div>

					<!-- 下一页 -->
					<Button
						@click="changePage(queryParams.page + 1)"
						:disabled="queryParams.page >= totalPages"
						variant="outline"
						size="sm"
					>
						下一页
					</Button>

					<!-- 尾页 -->
					<Button
						@click="changePage(totalPages)"
						:disabled="queryParams.page >= totalPages"
						variant="outline"
						size="sm"
					>
						尾页
					</Button>

					<!-- 每页条数选择 -->
					<div class="flex items-center space-x-2 ml-4">
						<span class="text-sm text-gray-500">每页</span>
						<Select
							v-model.number="queryParams.page_size"
							@update:model-value="changePageSize"
						>
							<SelectTrigger class="w-[80px]">
								<SelectValue />
							</SelectTrigger>
							<SelectContent>
								<SelectItem :value="10">10条</SelectItem>
								<SelectItem :value="20">20条</SelectItem>
								<SelectItem :value="50">50条</SelectItem>
								<SelectItem :value="100">100条</SelectItem>
							</SelectContent>
						</Select>
					</div>
				</div>
			</div>
		</div>
	</div>

	<!-- 订单详情弹窗 -->
	<OrderDetailModal
		v-if="showOrderDetail"
		:order="selectedOrder"
		@close="showOrderDetail = false"
		@refresh="loadOrders"
	/>

	<!-- 采购信息编辑弹窗 -->
	<PurchaseEditModal
		v-if="showPurchaseEdit"
		:order="selectedOrder"
		@close="showPurchaseEdit = false"
		@refresh="loadOrders"
	/>
</template>

<script setup lang="ts">
import { ref, reactive, computed, onMounted } from "vue";
import { OrderApi } from "@/api/orders";
import { ShopApi } from "@/api/shops";
import type {
	IOrderResponse,
	IOrderQueryParams,
	TOrderStatus,
	IShopResponse,
	IPageResponse,
} from "@/types/api";
import OrderDetailModal from "@/components/orders/OrderDetailModal.vue";
import PurchaseEditModal from "@/components/orders/PurchaseEditModal.vue";

// shadcn-vue组件导入
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Badge } from "@/components/ui/badge";
import {
	Select,
	SelectContent,
	SelectItem,
	SelectTrigger,
	SelectValue,
} from "@/components/ui/select";
import {
	Table,
	TableBody,
	TableCell,
	TableHead,
	TableHeader,
	TableRow,
} from "@/components/ui/table";
import { DateRangePicker } from "@/components/ui/date-picker";

// 定义页面内部使用的查询参数类型
interface IPageQueryParams {
	page: number;
	page_size: number;
	status?: TOrderStatus;
	shop_id?: number;
	start_date?: string;
	end_date?: string;
	total: number;
}

// 响应式数据
const loading = ref<boolean>(false);
const orders = ref<IOrderResponse[]>([]);
const shops = ref<IShopResponse[]>([]);
const jumpPage = ref<string>("");

// 查询参数 - 统一的分页状态管理
const queryParams = reactive<IPageQueryParams>({
	page: 1,
	page_size: 20,
	status: undefined,
	shop_id: undefined,
	start_date: "",
	end_date: "",
	total: 0,
});

// 弹窗状态
const showOrderDetail = ref<boolean>(false);
const showPurchaseEdit = ref<boolean>(false);
const selectedOrder = ref<IOrderResponse | null>(null);

// 日期范围选择器的值
const dateRangeValue = ref<{ start?: Date; end?: Date }>({
	start: undefined,
	end: undefined,
});

// 是否为开发环境 - 使用条件判断避免import.meta警告
const isDev =
	typeof window !== "undefined" && window.location.hostname === "localhost";

/**
 * 计算总页数
 * <AUTHOR>
 */
const totalPages = computed(() => {
	const total = queryParams.total;
	const pageSize = queryParams.page_size;
	return Math.ceil(total / pageSize);
});

/**
 * 计算可见的页码数组
 * <AUTHOR>
 */
const visiblePages = computed(() => {
	const total = totalPages.value;
	const current = queryParams.page;
	const delta = 2; // 当前页前后显示的页数

	let start = Math.max(1, current - delta);
	let end = Math.min(total, current + delta);

	// 如果开始页离第一页太近，则从第一页开始
	if (start <= 3) {
		start = 1;
		end = Math.min(total, start + 4);
	}

	// 如果结束页离最后一页太近，则到最后一页结束
	if (end >= total - 2) {
		end = total;
		start = Math.max(1, end - 4);
	}

	const pages: number[] = [];
	for (let i = start; i <= end; i++) {
		pages.push(i);
	}

	return pages;
});

/**
 * 加载订单列表数据
 * <AUTHOR>
 */
const loadOrders = async (): Promise<void> => {
	try {
		loading.value = true;
		// 准备查询参数，移除undefined和empty值
		const params: IOrderQueryParams = {};
		if (queryParams.shop_id) params.shop_id = queryParams.shop_id;
		if (queryParams.status) params.status = queryParams.status;
		if (queryParams.start_date?.trim())
			params.start_date = queryParams.start_date.trim();
		if (queryParams.end_date?.trim())
			params.end_date = queryParams.end_date.trim();
		params.page = queryParams.page;
		params.page_size = queryParams.page_size;

		const response: IPageResponse<IOrderResponse> = await OrderApi.getOrders(
			params
		);
		console.log("获取订单成功", response);
		orders.value = response.data;
		queryParams.total = response.total;
	} catch (error) {
		console.error("加载订单列表失败:", error);
		// 在开发环境下提供更详细的错误信息
		if (isDev) {
			console.warn("请确保后端服务正在运行在 http://localhost:8080");
			console.warn(
				"可以访问 http://localhost:8080/swagger/index.html 查看API文档"
			);
		}
		// 重置数据
		orders.value = [];
		queryParams.total = 0;
	} finally {
		loading.value = false;
	}
};

/**
 * 加载店铺列表
 * <AUTHOR>
 */
const loadShops = async (): Promise<void> => {
	try {
		const response = await ShopApi.getShops();
		shops.value = response.data;
	} catch (error) {
		console.error("加载店铺列表失败:", error);
		shops.value = [];
	}
};

/**
 * 刷新所有数据
 * <AUTHOR>
 */
const refreshData = async (): Promise<void> => {
	await loadOrders();
};

/**
 * 切换页码
 * <AUTHOR>
 */
const changePage = (page: number): void => {
	if (page < 1 || page > totalPages.value || page === queryParams.page) {
		return;
	}
	queryParams.page = page;
	loadOrders();
};

/**
 * 处理页码跳转
 * <AUTHOR>
 */
const handleJumpPage = (): void => {
	const pageNum = Number(jumpPage.value);
	if (jumpPage.value && pageNum >= 1 && pageNum <= totalPages.value) {
		changePage(pageNum);
		jumpPage.value = ""; // 清空输入框
	}
};

/**
 * 改变每页条数
 * <AUTHOR>
 */
const changePageSize = (): void => {
	queryParams.page = 1; // 重置到第一页
	loadOrders();
};

/**
 * 获取状态文本
 * <AUTHOR>
 */
const getStatusText = (status: TOrderStatus): string => {
	const statusMap: Record<TOrderStatus, string> = {
		new: "未接单",
		accepted: "已接单",
		purchased: "已采购",
		shipped: "已打单",
	};
	return statusMap[status] || status;
};

/**
 * 获取状态Badge变体
 * <AUTHOR>
 */
const getStatusBadgeVariant = (
	status: TOrderStatus
): "default" | "secondary" | "destructive" | "outline" => {
	const variantMap: Record<
		TOrderStatus,
		"default" | "secondary" | "destructive" | "outline"
	> = {
		new: "outline",
		accepted: "secondary",
		purchased: "default",
		shipped: "default",
	};
	return variantMap[status] || "outline";
};

/**
 * 获取利润样式类
 * <AUTHOR>
 */
const getProfitClass = (order: IOrderResponse): string => {
	const profit = order.total_amount - order.purchase_amount;
	if (profit > 0) {
		return "text-green-600";
	} else if (profit < 0) {
		return "text-red-600";
	}
	return "text-gray-600";
};

/**
 * 格式化日期
 * <AUTHOR>
 */
const formatDate = (dateString: string): string => {
	if (!dateString) return "-";
	const date = new Date(dateString);
	return date.toLocaleDateString("zh-CN", {
		year: "numeric",
		month: "2-digit",
		day: "2-digit",
	});
};

/**
 * 处理日期范围变化
 * <AUTHOR>
 */
const handleDateRangeChange = (value: { start?: Date; end?: Date }): void => {
	// 防止重复调用
	if (loading.value) {
		return;
	}

	const newStartDate = value.start
		? value.start.toISOString().split("T")[0]
		: "";
	const newEndDate = value.end ? value.end.toISOString().split("T")[0] : "";

	// 检查是否真的有变化
	if (
		newStartDate === queryParams.start_date &&
		newEndDate === queryParams.end_date
	) {
		return;
	}

	// 将日期转换为字符串格式
	queryParams.start_date = newStartDate;
	queryParams.end_date = newEndDate;

	// 重置到第一页并重新加载数据
	queryParams.page = 1;
	loadOrders();
};

/**
 * 处理过滤条件变化
 * <AUTHOR>
 */
const handleFilterChange = (): void => {
	queryParams.page = 1; // 重置到第一页
	loadOrders();
};

/**
 * 清除筛选条件
 * <AUTHOR>
 */
const handleClearFilters = (): void => {
	queryParams.status = undefined;
	queryParams.shop_id = undefined;
	queryParams.start_date = "";
	queryParams.end_date = "";
	queryParams.page = 1;

	// 清除日期范围选择器的值
	dateRangeValue.value = {
		start: undefined,
		end: undefined,
	};

	loadOrders();
};

/**
 * 同步订单
 * <AUTHOR>
 */
const handleSyncOrders = async (): Promise<void> => {
	try {
		loading.value = true;
		await OrderApi.syncAllShopsOrders();
		await loadOrders();
		console.log("订单同步成功");
	} catch (error) {
		console.error("订单同步失败:", error);
	} finally {
		loading.value = false;
	}
};

/**
 * 查看订单详情
 * <AUTHOR>
 */
const handleViewOrder = (order: IOrderResponse): void => {
	selectedOrder.value = order;
	showOrderDetail.value = true;
};

/**
 * 接单操作
 * <AUTHOR>
 */
const handleAcceptOrder = async (order: IOrderResponse): Promise<void> => {
	try {
		await OrderApi.updateOrder(order.id, { status: "accepted" });
		await loadOrders();
		console.log("接单成功");
	} catch (error) {
		console.error("接单失败:", error);
	}
};

/**
 * 编辑采购信息
 * <AUTHOR>
 */
const handleEditPurchase = (order: IOrderResponse): void => {
	selectedOrder.value = order;
	showPurchaseEdit.value = true;
};

/**
 * 打印运单
 * <AUTHOR>
 */
const handlePrintOrder = async (order: IOrderResponse): Promise<void> => {
	try {
		await OrderApi.updateOrder(order.id, { status: "shipped" });
		await loadOrders();
		console.log("打印运单成功");
	} catch (error) {
		console.error("打印运单失败:", error);
	}
};

/**
 * 同步单个订单
 * <AUTHOR>
 */
const handleSyncSingleOrder = async (order: IOrderResponse): Promise<void> => {
	try {
		await OrderApi.syncOrder(order.id);
		await loadOrders();
		console.log("订单同步成功");
	} catch (error) {
		console.error("订单同步失败:", error);
	}
};

// 组件挂载时加载数据
onMounted(async () => {
	await loadShops();
	await refreshData();
});
</script>

<style scoped>
/**
 * 订单列表页面样式
 * <AUTHOR>
 * 使用 shadcn-vue Table 组件的优化样式，保持响应式设计和良好的用户体验
 * 参考ProductList.vue的设计风格，保持界面一致性
 */

/* 主容器 - 填充全部可用空间 */
.order-list {
	display: flex;
	flex-direction: column;
	flex: 1;
	height: 100%; /* 确保占满父容器高度 */
	min-height: 0; /* 关键：确保flex子项可以收缩 */
	width: 100%;
	background-color: #ffffff;
	overflow: hidden; /* 页面本身不滚动 */
}

/* 头部区域 - 固定高度 */
.header-section {
	flex-shrink: 0;
	padding: 1.5rem;
	background-color: #ffffff;
	border-bottom: 1px solid #e5e7eb;
	box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1);
}

/* 表格区域 - 占据剩余空间，可滚动 */
.table-section {
	flex: 1;
	min-height: 0; /* 关键：确保可以收缩 */
	overflow: auto; /* 允许水平和垂直滚动 */
	background-color: #ffffff;
}

/* shadcn-vue Table 组件样式优化 */
.table-section :deep([data-slot="table-container"]) {
	height: 100%;
	overflow: auto;
}

.table-section :deep([data-slot="table"]) {
	border-collapse: collapse;
	min-width: 1600px; /* 确保表格有足够的宽度 */
}

/* 表格头部固定样式 */
.table-section :deep(thead) {
	position: sticky;
	top: 0;
	z-index: 10;
	background-color: #f9fafb;
}

/* 表格单元格基础样式 */
.table-section :deep(th),
.table-section :deep(td) {
	padding: 1rem 1.5rem;
	text-align: left;
	border-bottom: 1px solid #e5e7eb;
	vertical-align: top; /* 顶部对齐 */
}

/* 表格头部样式 */
.table-section :deep(th) {
	font-size: 0.75rem;
	font-weight: 500;
	color: #6b7280;
	text-transform: uppercase;
	letter-spacing: 0.05em;
	background-color: #f9fafb;
}

/* 表格行悬停效果 */
.table-section :deep(tbody tr:hover) {
	background-color: #f9fafb;
}

/* 操作列在行悬停时的背景色 */
.table-section :deep(tbody tr:hover td:last-child) {
	background-color: #f9fafb; /* 与行悬停背景色一致 */
}

/* 自定义滚动条样式 */
.table-section::-webkit-scrollbar {
	width: 6px;
	height: 6px; /* 水平滚动条高度 */
}

.table-section::-webkit-scrollbar-track {
	background: #f1f5f9;
	border-radius: 3px;
}

.table-section::-webkit-scrollbar-thumb {
	background: #cbd5e1;
	border-radius: 3px;
}

.table-section::-webkit-scrollbar-thumb:hover {
	background: #94a3b8;
}

/* 滚动条交叉点样式 */
.table-section::-webkit-scrollbar-corner {
	background: #f1f5f9;
}

/* 加载动画 */
@keyframes spin {
	to {
		transform: rotate(360deg);
	}
}

.animate-spin {
	animation: spin 1s linear infinite;
}

/* 分页区域 - 固定在底部 */
.pagination-section {
	flex-shrink: 0;
	padding: 1rem 1.5rem;
	background-color: #ffffff;
	border-top: 1px solid #e5e7eb;
	box-shadow: 0 -1px 3px 0 rgba(0, 0, 0, 0.1);
}

/* 响应式优化 */
@media (max-width: 768px) {
	.header-section {
		padding: 1rem;
	}

	.pagination-section {
		padding: 0.75rem 1rem;
	}

	/* 移动端表格最小宽度调整 */
	.table-section :deep([data-slot="table"]) {
		min-width: 1400px; /* 移动端稍微减少最小宽度 */
	}

	.table-section :deep(th),
	.table-section :deep(td) {
		padding: 0.75rem 1rem;
		font-size: 0.875rem;
	}
}

@media (max-width: 480px) {
	.header-section {
		padding: 0.75rem;
	}

	/* 超小屏幕表格最小宽度 */
	.table-section :deep([data-slot="table"]) {
		min-width: 1200px;
	}

	.table-section :deep(th),
	.table-section :deep(td) {
		padding: 0.5rem 0.75rem;
		font-size: 0.75rem;
	}
}

/* 状态徽章样式优化 */
.table-section :deep(.inline-flex) {
	display: inline-flex;
	align-items: center;
	justify-content: center;
	white-space: nowrap;
}

/* 操作按钮样式优化 */
.table-section :deep(button) {
	transition: all 0.2s ease-in-out;
	font-weight: 500;
}

.table-section :deep(button:hover) {
	text-decoration: underline;
}

/* 金额信息样式优化 */
.table-section :deep(.font-bold) {
	font-weight: 600;
}

/* 利润样式 */
.table-section :deep(.text-red-600) {
	color: #dc2626;
	font-weight: 600;
}

.table-section :deep(.text-green-600) {
	color: #16a34a;
	font-weight: 600;
}

.table-section :deep(.text-gray-600) {
	color: #4b5563;
	font-weight: 600;
}

/* 状态特定样式 */
.table-section :deep(.text-blue-600) {
	color: #2563eb;
}

.table-section :deep(.text-orange-600) {
	color: #ea580c;
}

.table-section :deep(.text-purple-600) {
	color: #9333ea;
}
</style>
