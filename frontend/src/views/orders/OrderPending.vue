/** * 待处理订单组件 * <AUTHOR> * * 显示所有状态为"未接单"的订单列表 *
提供快速接单功能 */

<template>
	<div class="order-pending">
		<!-- 页面标题 -->
		<div class="flex justify-between items-center mb-6">
			<div>
				<h2 class="text-2xl font-semibold text-gray-900">待处理订单</h2>
				<p class="mt-1 text-gray-600">显示所有未接单的订单，可以快速接单处理</p>
			</div>
			<div class="flex space-x-4">
				<button
					@click="handleRefresh"
					:disabled="isLoading"
					class="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 disabled:bg-gray-400"
				>
					{{ isLoading ? "加载中..." : "刷新列表" }}
				</button>
				<button
					@click="handleBatchAccept"
					:disabled="isLoading || selectedOrders.length === 0"
					class="px-4 py-2 bg-green-600 text-white rounded-md hover:bg-green-700 disabled:bg-gray-400"
				>
					批量接单 ({{ selectedOrders.length }})
				</button>
			</div>
		</div>

		<!-- 统计信息卡片 -->
		<div class="grid grid-cols-1 md:grid-cols-4 gap-6 mb-6">
			<div class="bg-white rounded-lg shadow p-6">
				<div class="flex items-center">
					<div class="flex-shrink-0">
						<div
							class="w-8 h-8 bg-yellow-100 rounded-full flex items-center justify-center"
						>
							<svg
								class="w-5 h-5 text-yellow-600"
								fill="none"
								stroke="currentColor"
								viewBox="0 0 24 24"
							>
								<path
									stroke-linecap="round"
									stroke-linejoin="round"
									stroke-width="2"
									d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"
								></path>
							</svg>
						</div>
					</div>
					<div class="ml-4">
						<p class="text-sm font-medium text-gray-500">待处理订单</p>
						<p class="text-2xl font-semibold text-gray-900">
							{{ orders.length }}
						</p>
					</div>
				</div>
			</div>

			<div class="bg-white rounded-lg shadow p-6">
				<div class="flex items-center">
					<div class="flex-shrink-0">
						<div
							class="w-8 h-8 bg-green-100 rounded-full flex items-center justify-center"
						>
							<svg
								class="w-5 h-5 text-green-600"
								fill="none"
								stroke="currentColor"
								viewBox="0 0 24 24"
							>
								<path
									stroke-linecap="round"
									stroke-linejoin="round"
									stroke-width="2"
									d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1"
								></path>
							</svg>
						</div>
					</div>
					<div class="ml-4">
						<p class="text-sm font-medium text-gray-500">总金额</p>
						<p class="text-2xl font-semibold text-gray-900">
							¥{{ totalAmount.toFixed(2) }}
						</p>
					</div>
				</div>
			</div>

			<div class="bg-white rounded-lg shadow p-6">
				<div class="flex items-center">
					<div class="flex-shrink-0">
						<div
							class="w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center"
						>
							<svg
								class="w-5 h-5 text-blue-600"
								fill="none"
								stroke="currentColor"
								viewBox="0 0 24 24"
							>
								<path
									stroke-linecap="round"
									stroke-linejoin="round"
									stroke-width="2"
									d="M16 11V7a4 4 0 00-8 0v4M5 9h14l1 12H4L5 9z"
								></path>
							</svg>
						</div>
					</div>
					<div class="ml-4">
						<p class="text-sm font-medium text-gray-500">商品数量</p>
						<p class="text-2xl font-semibold text-gray-900">{{ totalItems }}</p>
					</div>
				</div>
			</div>

			<div class="bg-white rounded-lg shadow p-6">
				<div class="flex items-center">
					<div class="flex-shrink-0">
						<div
							class="w-8 h-8 bg-purple-100 rounded-full flex items-center justify-center"
						>
							<svg
								class="w-5 h-5 text-purple-600"
								fill="none"
								stroke="currentColor"
								viewBox="0 0 24 24"
							>
								<path
									stroke-linecap="round"
									stroke-linejoin="round"
									stroke-width="2"
									d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4"
								></path>
							</svg>
						</div>
					</div>
					<div class="ml-4">
						<p class="text-sm font-medium text-gray-500">涉及店铺</p>
						<p class="text-2xl font-semibold text-gray-900">
							{{ uniqueShops.length }}
						</p>
					</div>
				</div>
			</div>
		</div>

		<!-- 订单列表 -->
		<div class="bg-white shadow rounded-lg">
			<div class="px-4 py-5 sm:p-6">
				<div class="overflow-x-auto">
					<table class="min-w-full divide-y divide-gray-200">
						<thead class="bg-gray-50">
							<tr>
								<th class="px-6 py-3 text-left">
									<input
										type="checkbox"
										:checked="isAllSelected"
										@change="toggleSelectAll"
										class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
									/>
								</th>
								<th
									class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"
								>
									订单信息
								</th>
								<th
									class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"
								>
									客户信息
								</th>
								<th
									class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"
								>
									商品信息
								</th>
								<th
									class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"
								>
									订单金额
								</th>
								<th
									class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"
								>
									发货日期
								</th>
								<th
									class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"
								>
									操作
								</th>
							</tr>
						</thead>
						<tbody class="bg-white divide-y divide-gray-200">
							<!-- 加载状态 -->
							<tr v-if="isLoading && orders.length === 0">
								<td colspan="7" class="px-6 py-8 text-center text-gray-500">
									<div class="flex items-center justify-center">
										<div
											class="animate-spin rounded-full h-6 w-6 border-b-2 border-blue-600 mr-2"
										></div>
										加载中...
									</div>
								</td>
							</tr>

							<!-- 无数据状态 -->
							<tr v-else-if="!isLoading && orders.length === 0">
								<td colspan="7" class="px-6 py-8 text-center text-gray-500">
									<div class="flex flex-col items-center">
										<svg
											class="w-12 h-12 text-gray-400 mb-4"
											fill="none"
											stroke="currentColor"
											viewBox="0 0 24 24"
										>
											<path
												stroke-linecap="round"
												stroke-linejoin="round"
												stroke-width="2"
												d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"
											></path>
										</svg>
										<p class="text-lg font-medium text-gray-900 mb-1">
											暂无待处理订单
										</p>
										<p class="text-gray-500">所有订单都已处理完毕</p>
									</div>
								</td>
							</tr>

							<!-- 订单数据行 -->
							<tr
								v-else
								v-for="order in orders"
								:key="order.id"
								class="hover:bg-gray-50"
							>
								<td class="px-6 py-4 whitespace-nowrap">
									<input
										type="checkbox"
										:checked="selectedOrders.includes(order.id)"
										@change="toggleSelectOrder(order.id)"
										class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
									/>
								</td>
								<td class="px-6 py-4 whitespace-nowrap">
									<div class="flex flex-col">
										<div class="text-sm font-medium text-gray-900">
											{{ order.posting_number }}
										</div>
										<div class="text-sm text-gray-500">
											ID: {{ order.external_id }}
										</div>
										<div class="text-xs text-gray-400">
											{{ order.shop_name }}
										</div>
									</div>
								</td>
								<td class="px-6 py-4 whitespace-nowrap">
									<div class="text-sm font-medium text-gray-900">
										{{ order.customer_name }}
									</div>
								</td>
								<td class="px-6 py-4 whitespace-nowrap">
									<div class="text-sm text-gray-900">
										{{ order.items.length }} 件商品
									</div>
									<div class="text-xs text-gray-500">
										{{
											order.items
												.slice(0, 2)
												.map((item) => item.name)
												.join(", ")
										}}
										<span v-if="order.items.length > 2">等</span>
									</div>
								</td>
								<td class="px-6 py-4 whitespace-nowrap">
									<div class="text-sm font-semibold text-green-600">
										¥{{ order.total_amount.toFixed(2) }}
									</div>
								</td>
								<td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
									{{ formatDate(order.shipment_date) }}
								</td>
								<td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
									<div class="flex space-x-2">
										<button
											@click="handleViewOrder(order)"
											class="text-blue-600 hover:text-blue-900 transition-colors"
										>
											查看
										</button>
										<button
											@click="handleAcceptOrder(order)"
											class="text-green-600 hover:text-green-900 transition-colors"
										>
											接单
										</button>
									</div>
								</td>
							</tr>
						</tbody>
					</table>
				</div>
			</div>
		</div>

		<!-- 分页组件 -->
		<div
			v-if="pagination.total > 0"
			class="mt-6 flex items-center justify-between"
		>
			<div class="text-sm text-gray-700">
				显示第 {{ (pagination.page - 1) * pagination.page_size + 1 }} -
				{{
					Math.min(pagination.page * pagination.page_size, pagination.total)
				}}
				条， 共 {{ pagination.total }} 条记录
			</div>
			<div class="flex space-x-2">
				<button
					@click="handlePageChange(pagination.page - 1)"
					:disabled="pagination.page <= 1"
					class="px-3 py-2 text-sm font-medium text-gray-500 bg-white border border-gray-300 rounded-md hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
				>
					上一页
				</button>
				<span class="px-3 py-2 text-sm font-medium text-gray-900">
					第 {{ pagination.page }} /
					{{ Math.ceil(pagination.total / pagination.page_size) }} 页
				</span>
				<button
					@click="handlePageChange(pagination.page + 1)"
					:disabled="
						pagination.page >=
						Math.ceil(pagination.total / pagination.page_size)
					"
					class="px-3 py-2 text-sm font-medium text-gray-500 bg-white border border-gray-300 rounded-md hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
				>
					下一页
				</button>
			</div>
		</div>
	</div>

	<!-- 订单详情弹窗 -->
	<OrderDetailModal
		v-if="showOrderDetail"
		:order="selectedOrder"
		@close="showOrderDetail = false"
		@refresh="loadOrders"
	/>
</template>

<script setup lang="ts">
import { ref, reactive, computed, onMounted } from "vue";
import { OrderApi } from "@/api/orders";
import type { IOrderResponse, IPageResponse } from "@/types/api";
import OrderDetailModal from "@/components/orders/OrderDetailModal.vue";

// ============ 响应式数据 ============

// 加载状态
const isLoading = ref<boolean>(false);

// 订单列表数据
const orders = ref<IOrderResponse[]>([]);

// 选中的订单ID列表
const selectedOrders = ref<number[]>([]);

// 分页信息
const pagination = reactive({
	page: 1,
	page_size: 20,
	total: 0,
});

// 弹窗状态
const showOrderDetail = ref<boolean>(false);
const selectedOrder = ref<IOrderResponse | null>(null);

// ============ 计算属性 ============

/**
 * 总金额
 * <AUTHOR>
 */
const totalAmount = computed(() => {
	return orders.value.reduce((total, order) => total + order.total_amount, 0);
});

/**
 * 总商品数量
 * <AUTHOR>
 */
const totalItems = computed(() => {
	return orders.value.reduce((total, order) => total + order.items.length, 0);
});

/**
 * 唯一店铺列表
 * <AUTHOR>
 */
const uniqueShops = computed(() => {
	const shops = orders.value.map((order) => order.shop_name);
	return [...new Set(shops)];
});

/**
 * 是否全选
 * <AUTHOR>
 */
const isAllSelected = computed(() => {
	return (
		orders.value.length > 0 &&
		selectedOrders.value.length === orders.value.length
	);
});

// ============ 生命周期 ============

onMounted(() => {
	loadOrders();
});

// ============ 方法定义 ============

/**
 * 加载待处理订单列表
 * <AUTHOR>
 */
const loadOrders = async (): Promise<void> => {
	try {
		isLoading.value = true;

		const response: IPageResponse<IOrderResponse> = await OrderApi.getOrders({
			status: "new", // 只加载未接单的订单
			page: pagination.page,
			page_size: pagination.page_size,
		});

		orders.value = response.data;
		pagination.page = response.page;
		pagination.page_size = response.page_size;
		pagination.total = response.total;

		// 清除选中状态
		selectedOrders.value = [];
	} catch (error) {
		console.error("加载待处理订单失败:", error);
		orders.value = [];
	} finally {
		isLoading.value = false;
	}
};

/**
 * 刷新订单列表
 * <AUTHOR>
 */
const handleRefresh = (): void => {
	pagination.page = 1;
	loadOrders();
};

/**
 * 处理分页变化
 * <AUTHOR>
 */
const handlePageChange = (page: number): void => {
	if (page >= 1 && page <= Math.ceil(pagination.total / pagination.page_size)) {
		pagination.page = page;
		loadOrders();
	}
};

/**
 * 切换订单选中状态
 * <AUTHOR>
 */
const toggleSelectOrder = (orderId: number): void => {
	const index = selectedOrders.value.indexOf(orderId);
	if (index > -1) {
		selectedOrders.value.splice(index, 1);
	} else {
		selectedOrders.value.push(orderId);
	}
};

/**
 * 切换全选状态
 * <AUTHOR>
 */
const toggleSelectAll = (): void => {
	if (isAllSelected.value) {
		selectedOrders.value = [];
	} else {
		selectedOrders.value = orders.value.map((order) => order.id);
	}
};

/**
 * 查看订单详情
 * <AUTHOR>
 */
const handleViewOrder = (order: IOrderResponse): void => {
	selectedOrder.value = order;
	showOrderDetail.value = true;
};

/**
 * 接受单个订单
 * <AUTHOR>
 */
const handleAcceptOrder = async (order: IOrderResponse): Promise<void> => {
	try {
		await OrderApi.updateOrder(order.id, { status: "accepted" });
		await loadOrders();
		alert("订单已接受");
	} catch (error) {
		console.error("接受订单失败:", error);
		alert("接受订单失败，请稍后重试");
	}
};

/**
 * 批量接受订单
 * <AUTHOR>
 */
const handleBatchAccept = async (): Promise<void> => {
	if (selectedOrders.value.length === 0) return;

	try {
		isLoading.value = true;

		const updatePromises = selectedOrders.value.map((orderId) =>
			OrderApi.updateOrder(orderId, { status: "accepted" })
		);

		await Promise.all(updatePromises);
		await loadOrders();

		alert(`成功接受 ${selectedOrders.value.length} 个订单`);
	} catch (error) {
		console.error("批量接受订单失败:", error);
		alert("批量接受订单失败，请稍后重试");
	} finally {
		isLoading.value = false;
	}
};

/**
 * 格式化日期显示
 * <AUTHOR>
 */
const formatDate = (dateString: string): string => {
	if (!dateString) return "-";
	const date = new Date(dateString);
	return date.toLocaleDateString("zh-CN");
};
</script>

<style scoped>
/* 待处理订单页面样式 */
.order-pending {
	/* 组件样式 */
}

/* 表格行hover效果 */
tbody tr:hover {
	background-color: #f9fafb;
}

/* 复选框样式 */
input[type="checkbox"]:checked {
	background-color: #3b82f6;
	border-color: #3b82f6;
}

/* 响应式设计 */
@media (max-width: 768px) {
	.order-pending .grid-cols-4 {
		grid-template-columns: 1fr 1fr;
	}

	.order-pending table {
		font-size: 0.875rem;
	}
}
</style>
