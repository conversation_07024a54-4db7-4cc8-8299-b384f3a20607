/** * 订单管理布局组件 * <AUTHOR> *
订单管理模块的布局容器组件，使用flex布局自适应父容器剩余高度 *
提供统一的页面结构和导航，后续的layout组件可以直接使用而无需考虑全局布局的高度计算
*/

<template>
	<div class="order-layout">
		<div class="container mx-auto p-6 h-full">
			<!-- 子路由渲染区域 -->
			<router-view />
		</div>
	</div>
</template>

<script setup lang="ts">
// 订单管理布局组件的逻辑处理
</script>

<style scoped>
.order-layout {
	display: flex;
	flex-direction: column;
	flex: 1;
	min-height: 0; /* 关键：确保flex子项可以收缩 */
	background-color: #fafafa;
}

.order-layout .container {
	display: flex;
	flex-direction: column;
	flex: 1;
	min-height: 0;
}
</style>
