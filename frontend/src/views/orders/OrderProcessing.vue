/** * 正在处理订单组件 * <AUTHOR> * *
显示正在处理中的订单（已接单、已采购状态） * 支持采购信息编辑、订单状态管理 */

<template>
	<div class="order-processing">
		<!-- 页面标题 -->
		<div class="flex justify-between items-center mb-6">
			<div>
				<h2 class="text-2xl font-semibold text-gray-900">正在处理订单</h2>
				<p class="mt-1 text-gray-600">显示已接单和已采购状态的订单</p>
			</div>
			<div class="flex space-x-4">
				<!-- 状态筛选 -->
				<select
					v-model="statusFilter"
					@change="handleStatusFilterChange"
					class="px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
				>
					<option value="">全部处理中</option>
					<option value="accepted">已接单</option>
					<option value="purchased">已采购</option>
				</select>

				<button
					@click="handleRefresh"
					:disabled="isLoading"
					class="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 disabled:bg-gray-400"
				>
					{{ isLoading ? "加载中..." : "刷新列表" }}
				</button>
			</div>
		</div>

		<!-- 统计信息卡片 -->
		<div class="grid grid-cols-1 md:grid-cols-4 gap-6 mb-6">
			<div class="bg-white rounded-lg shadow p-6">
				<div class="flex items-center">
					<div class="flex-shrink-0">
						<div
							class="w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center"
						>
							<svg
								class="w-5 h-5 text-blue-600"
								fill="none"
								stroke="currentColor"
								viewBox="0 0 24 24"
							>
								<path
									stroke-linecap="round"
									stroke-linejoin="round"
									stroke-width="2"
									d="M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2"
								></path>
							</svg>
						</div>
					</div>
					<div class="ml-4">
						<p class="text-sm font-medium text-gray-500">处理中订单</p>
						<p class="text-2xl font-semibold text-gray-900">
							{{ orders.length }}
						</p>
					</div>
				</div>
			</div>

			<div class="bg-white rounded-lg shadow p-6">
				<div class="flex items-center">
					<div class="flex-shrink-0">
						<div
							class="w-8 h-8 bg-orange-100 rounded-full flex items-center justify-center"
						>
							<svg
								class="w-5 h-5 text-orange-600"
								fill="none"
								stroke="currentColor"
								viewBox="0 0 24 24"
							>
								<path
									stroke-linecap="round"
									stroke-linejoin="round"
									stroke-width="2"
									d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"
								></path>
							</svg>
						</div>
					</div>
					<div class="ml-4">
						<p class="text-sm font-medium text-gray-500">已接单</p>
						<p class="text-2xl font-semibold text-gray-900">
							{{ acceptedOrdersCount }}
						</p>
					</div>
				</div>
			</div>

			<div class="bg-white rounded-lg shadow p-6">
				<div class="flex items-center">
					<div class="flex-shrink-0">
						<div
							class="w-8 h-8 bg-green-100 rounded-full flex items-center justify-center"
						>
							<svg
								class="w-5 h-5 text-green-600"
								fill="none"
								stroke="currentColor"
								viewBox="0 0 24 24"
							>
								<path
									stroke-linecap="round"
									stroke-linejoin="round"
									stroke-width="2"
									d="M16 11V7a4 4 0 00-8 0v4M5 9h14l1 12H4L5 9z"
								></path>
							</svg>
						</div>
					</div>
					<div class="ml-4">
						<p class="text-sm font-medium text-gray-500">已采购</p>
						<p class="text-2xl font-semibold text-gray-900">
							{{ purchasedOrdersCount }}
						</p>
					</div>
				</div>
			</div>

			<div class="bg-white rounded-lg shadow p-6">
				<div class="flex items-center">
					<div class="flex-shrink-0">
						<div
							class="w-8 h-8 bg-purple-100 rounded-full flex items-center justify-center"
						>
							<svg
								class="w-5 h-5 text-purple-600"
								fill="none"
								stroke="currentColor"
								viewBox="0 0 24 24"
							>
								<path
									stroke-linecap="round"
									stroke-linejoin="round"
									stroke-width="2"
									d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1"
								></path>
							</svg>
						</div>
					</div>
					<div class="ml-4">
						<p class="text-sm font-medium text-gray-500">总金额</p>
						<p class="text-2xl font-semibold text-gray-900">
							¥{{ totalAmount.toFixed(2) }}
						</p>
					</div>
				</div>
			</div>
		</div>

		<!-- 订单列表 -->
		<div class="bg-white shadow rounded-lg">
			<div class="px-4 py-5 sm:p-6">
				<div class="overflow-x-auto">
					<table class="min-w-full divide-y divide-gray-200">
						<thead class="bg-gray-50">
							<tr>
								<th
									class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"
								>
									订单信息
								</th>
								<th
									class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"
								>
									客户信息
								</th>
								<th
									class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"
								>
									订单状态
								</th>
								<th
									class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"
								>
									订单金额
								</th>
								<th
									class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"
								>
									采购金额
								</th>
								<th
									class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"
								>
									预估利润
								</th>
								<th
									class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"
								>
									操作
								</th>
							</tr>
						</thead>
						<tbody class="bg-white divide-y divide-gray-200">
							<!-- 加载状态 -->
							<tr v-if="isLoading && orders.length === 0">
								<td colspan="7" class="px-6 py-8 text-center text-gray-500">
									<div class="flex items-center justify-center">
										<div
											class="animate-spin rounded-full h-6 w-6 border-b-2 border-blue-600 mr-2"
										></div>
										加载中...
									</div>
								</td>
							</tr>

							<!-- 无数据状态 -->
							<tr v-else-if="!isLoading && orders.length === 0">
								<td colspan="7" class="px-6 py-8 text-center text-gray-500">
									<div class="flex flex-col items-center">
										<svg
											class="w-12 h-12 text-gray-400 mb-4"
											fill="none"
											stroke="currentColor"
											viewBox="0 0 24 24"
										>
											<path
												stroke-linecap="round"
												stroke-linejoin="round"
												stroke-width="2"
												d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"
											></path>
										</svg>
										<p class="text-lg font-medium text-gray-900 mb-1">
											暂无处理中订单
										</p>
										<p class="text-gray-500">当前没有需要处理的订单</p>
									</div>
								</td>
							</tr>

							<!-- 订单数据行 -->
							<tr
								v-else
								v-for="order in orders"
								:key="order.id"
								class="hover:bg-gray-50"
							>
								<td class="px-6 py-4 whitespace-nowrap">
									<div class="flex flex-col">
										<div class="text-sm font-medium text-gray-900">
											{{ order.posting_number }}
										</div>
										<div class="text-sm text-gray-500">
											ID: {{ order.external_id }}
										</div>
										<div class="text-xs text-gray-400">
											{{ order.shop_name }}
										</div>
									</div>
								</td>
								<td class="px-6 py-4 whitespace-nowrap">
									<div class="text-sm font-medium text-gray-900">
										{{ order.customer_name }}
									</div>
									<div class="text-sm text-gray-500">
										{{ order.items.length }} 件商品
									</div>
								</td>
								<td class="px-6 py-4 whitespace-nowrap">
									<span
										:class="getStatusBadgeClass(order.status)"
										class="inline-flex px-2 py-1 text-xs font-semibold rounded-full"
									>
										{{ getStatusText(order.status) }}
									</span>
								</td>
								<td class="px-6 py-4 whitespace-nowrap">
									<div class="text-sm font-semibold text-green-600">
										¥{{ order.total_amount.toFixed(2) }}
									</div>
								</td>
								<td class="px-6 py-4 whitespace-nowrap">
									<div
										v-if="order.purchase_amount > 0"
										class="text-sm font-medium text-red-600"
									>
										¥{{ order.purchase_amount.toFixed(2) }}
									</div>
									<div v-else class="text-sm text-gray-400">未设置</div>
								</td>
								<td class="px-6 py-4 whitespace-nowrap">
									<div
										v-if="order.purchase_amount > 0"
										class="text-sm font-medium"
										:class="
											getProfitClass(order.total_amount - order.purchase_amount)
										"
									>
										¥{{
											(order.total_amount - order.purchase_amount).toFixed(2)
										}}
									</div>
									<div v-else class="text-sm text-gray-400">-</div>
								</td>
								<td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
									<div class="flex space-x-2">
										<button
											@click="handleViewOrder(order)"
											class="text-blue-600 hover:text-blue-900 transition-colors"
										>
											查看
										</button>
										<button
											v-if="order.status === 'accepted'"
											@click="handleEditPurchase(order)"
											class="text-orange-600 hover:text-orange-900 transition-colors"
										>
											设置采购
										</button>
										<button
											v-if="order.status === 'purchased'"
											@click="handlePrintOrder(order)"
											class="text-purple-600 hover:text-purple-900 transition-colors"
										>
											打印运单
										</button>
									</div>
								</td>
							</tr>
						</tbody>
					</table>
				</div>
			</div>
		</div>

		<!-- 分页组件 -->
		<div
			v-if="pagination.total > 0"
			class="mt-6 flex items-center justify-between"
		>
			<div class="text-sm text-gray-700">
				显示第 {{ (pagination.page - 1) * pagination.page_size + 1 }} -
				{{
					Math.min(pagination.page * pagination.page_size, pagination.total)
				}}
				条， 共 {{ pagination.total }} 条记录
			</div>
			<div class="flex space-x-2">
				<button
					@click="handlePageChange(pagination.page - 1)"
					:disabled="pagination.page <= 1"
					class="px-3 py-2 text-sm font-medium text-gray-500 bg-white border border-gray-300 rounded-md hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
				>
					上一页
				</button>
				<span class="px-3 py-2 text-sm font-medium text-gray-900">
					第 {{ pagination.page }} /
					{{ Math.ceil(pagination.total / pagination.page_size) }} 页
				</span>
				<button
					@click="handlePageChange(pagination.page + 1)"
					:disabled="
						pagination.page >=
						Math.ceil(pagination.total / pagination.page_size)
					"
					class="px-3 py-2 text-sm font-medium text-gray-500 bg-white border border-gray-300 rounded-md hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
				>
					下一页
				</button>
			</div>
		</div>
	</div>

	<!-- 订单详情弹窗 -->
	<OrderDetailModal
		v-if="showOrderDetail"
		:order="selectedOrder"
		@close="showOrderDetail = false"
		@refresh="loadOrders"
	/>

	<!-- 采购信息编辑弹窗 -->
	<PurchaseEditModal
		v-if="showPurchaseEdit"
		:order="selectedOrder"
		@close="showPurchaseEdit = false"
		@refresh="loadOrders"
	/>
</template>

<script setup lang="ts">
import { ref, reactive, computed, onMounted } from "vue";
import { OrderApi } from "@/api/orders";
import type { IOrderResponse, IPageResponse, TOrderStatus } from "@/types/api";
import OrderDetailModal from "@/components/orders/OrderDetailModal.vue";
import PurchaseEditModal from "@/components/orders/PurchaseEditModal.vue";

// ============ 响应式数据 ============

// 加载状态
const isLoading = ref<boolean>(false);

// 订单列表数据
const orders = ref<IOrderResponse[]>([]);

// 状态筛选
const statusFilter = ref<string>("");

// 分页信息
const pagination = reactive({
	page: 1,
	page_size: 20,
	total: 0,
});

// 弹窗状态
const showOrderDetail = ref<boolean>(false);
const showPurchaseEdit = ref<boolean>(false);
const selectedOrder = ref<IOrderResponse | null>(null);

// ============ 计算属性 ============

/**
 * 总金额
 * <AUTHOR>
 */
const totalAmount = computed(() => {
	return orders.value.reduce((total, order) => total + order.total_amount, 0);
});

/**
 * 已接单订单数量
 * <AUTHOR>
 */
const acceptedOrdersCount = computed(() => {
	return orders.value.filter((order) => order.status === "accepted").length;
});

/**
 * 已采购订单数量
 * <AUTHOR>
 */
const purchasedOrdersCount = computed(() => {
	return orders.value.filter((order) => order.status === "purchased").length;
});

// ============ 生命周期 ============

onMounted(() => {
	loadOrders();
});

// ============ 方法定义 ============

/**
 * 加载正在处理的订单列表
 * <AUTHOR>
 */
const loadOrders = async (): Promise<void> => {
	try {
		isLoading.value = true;

		// 构建查询参数
		const queryParams: any = {
			page: pagination.page,
			page_size: pagination.page_size,
		};

		// 如果有状态筛选，添加状态参数
		if (statusFilter.value) {
			queryParams.status = statusFilter.value;
		} else {
			// 没有筛选时，获取accepted和purchased状态的订单
			// 这里我们需要分别请求两种状态，然后合并结果
			// 或者让后端支持多状态查询
			queryParams.status = "accepted"; // 暂时先只显示已接单的
		}

		const response: IPageResponse<IOrderResponse> = await OrderApi.getOrders(
			queryParams
		);

		orders.value = response.data;
		pagination.page = response.page;
		pagination.page_size = response.page_size;
		pagination.total = response.total;
	} catch (error) {
		console.error("加载正在处理订单失败:", error);
		orders.value = [];
	} finally {
		isLoading.value = false;
	}
};

/**
 * 刷新订单列表
 * <AUTHOR>
 */
const handleRefresh = (): void => {
	pagination.page = 1;
	loadOrders();
};

/**
 * 处理状态筛选变化
 * <AUTHOR>
 */
const handleStatusFilterChange = (): void => {
	pagination.page = 1;
	loadOrders();
};

/**
 * 处理分页变化
 * <AUTHOR>
 */
const handlePageChange = (page: number): void => {
	if (page >= 1 && page <= Math.ceil(pagination.total / pagination.page_size)) {
		pagination.page = page;
		loadOrders();
	}
};

/**
 * 查看订单详情
 * <AUTHOR>
 */
const handleViewOrder = (order: IOrderResponse): void => {
	selectedOrder.value = order;
	showOrderDetail.value = true;
};

/**
 * 编辑采购信息
 * <AUTHOR>
 */
const handleEditPurchase = (order: IOrderResponse): void => {
	selectedOrder.value = order;
	showPurchaseEdit.value = true;
};

/**
 * 打印运单
 * <AUTHOR>
 */
const handlePrintOrder = async (order: IOrderResponse): Promise<void> => {
	try {
		// 更新订单状态为已打单
		await OrderApi.updateOrder(order.id, { status: "shipped" });

		// 这里可以调用打印服务或打开打印页面
		window.print();

		await loadOrders();
		alert("运单已打印，订单状态已更新");
	} catch (error) {
		console.error("打印运单失败:", error);
		alert("打印运单失败，请稍后重试");
	}
};

/**
 * 获取状态显示文本
 * <AUTHOR>
 */
const getStatusText = (status: TOrderStatus): string => {
	const statusMap: Record<TOrderStatus, string> = {
		new: "未接单",
		accepted: "已接单",
		purchased: "已采购",
		shipped: "已打单",
	};
	return statusMap[status] || status;
};

/**
 * 获取状态样式类
 * <AUTHOR>
 */
const getStatusBadgeClass = (status: TOrderStatus): string => {
	const classMap: Record<TOrderStatus, string> = {
		new: "bg-yellow-100 text-yellow-800",
		accepted: "bg-blue-100 text-blue-800",
		purchased: "bg-green-100 text-green-800",
		shipped: "bg-purple-100 text-purple-800",
	};
	return classMap[status] || "bg-gray-100 text-gray-800";
};

/**
 * 获取利润颜色样式
 * <AUTHOR>
 */
const getProfitClass = (profit: number): string => {
	if (profit > 0) return "text-green-600";
	if (profit < 0) return "text-red-600";
	return "text-gray-600";
};
</script>

<style scoped>
/* 正在处理订单页面样式 */
.order-processing {
	/* 组件样式 */
}

/* 表格行hover效果 */
tbody tr:hover {
	background-color: #f9fafb;
}

/* 响应式设计 */
@media (max-width: 768px) {
	.order-processing .grid-cols-4 {
		grid-template-columns: 1fr 1fr;
	}

	.order-processing table {
		font-size: 0.875rem;
	}
}
</style>
