<template>
	<div class="inventory-layout">
		<div class="container mx-auto p-6 h-full">
			<div class="mb-6">
				<h1 class="text-3xl font-bold text-gray-900">库存管理</h1>
				<p class="mt-2 text-gray-600">管理库存信息、入库和出库记录</p>
			</div>
			<router-view />
		</div>
	</div>
</template>

<script setup lang="ts">
/**
 * 库存管理布局组件
 * <AUTHOR>
 * 使用flex布局自适应父容器剩余高度，确保在有header的情况下正确显示
 * 采用flex布局，后续的layout组件可以直接使用而无需考虑全局布局的高度计算
 */
</script>

<style scoped>
.inventory-layout {
	display: flex;
	flex-direction: column;
	flex: 1;
	min-height: 0; /* 关键：确保flex子项可以收缩 */
	background-color: #fafafa;
}

.inventory-layout .container {
	display: flex;
	flex-direction: column;
	flex: 1;
	min-height: 0;
}
</style>
