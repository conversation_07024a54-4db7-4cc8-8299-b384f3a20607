/**
 * 应用主入口文件
 * 初始化Vue应用、路由和认证状态
 * <AUTHOR>
 */

import { createApp } from "vue";

import "./index.css";
import App from "./App.vue";
import router from "./router";
import "./types";
import { useAuth } from "./stores/auth.js";

/**
 * 启动应用
 * 流程:
 * 1. 创建Vue应用实例
 * 2. 初始化认证状态 (在挂载前完成，防止页面闪烁)
 * 3. 使用路由
 * 4. 挂载应用
 */
const startApp = async () => {
	const app = createApp(App);
	const auth = useAuth();

	try {
		// 必须在挂载应用前完成认证状态的初始化
		console.log("正在初始化认证状态...");
		await auth.initAuth();
		console.log("认证状态初始化完成");

		// 使用路由
		app.use(router);

		// 挂载应用
		app.mount("#app");

		console.log("9Wings ERP系统已成功启动");
	} catch (error) {
		console.error("应用启动失败:", error);
		// 可以在此处渲染一个全局的错误提示页面
	}
};

// 启动应用
startApp();
