/**
 * 用户认证状态管理
 * 管理用户登录状态、token和用户信息
 * <AUTHOR>
 */

import { ref, computed } from "vue";
import { AuthApi, UserApi } from "../api/auth.js";
import type {
	IUser,
	ILoginParams,
	ITokenResponse,
	IUserResponse,
} from "../types/api.js";
import { JwtUtils, TokenStorage } from "../utils/jwt.js";

/**
 * 是否启用Mock登录
 * 通过环境变量 VITE_MOCK_LOGIN 控制
 */
const IS_MOCK_ENABLED = import.meta.env.VITE_MOCK_LOGIN === "true";

/**
 * Token存储键名
 */
const TOKEN_KEY = "erp_token";
const REFRESH_TOKEN_KEY = "erp_refresh_token";
const USER_KEY = "erp_user";

/**
 * 用户认证状态
 */
const token = ref<string | null>(TokenStorage.getToken());
const refreshToken = ref<string | null>(TokenStorage.getRefreshToken());
const user = ref<IUser | null>(null);
const isLoading = ref(false);
const isAuthInitialized = ref(false);
const isRouteLoading = ref(false);

// 初始化时从localStorage恢复用户信息
const storedUser = localStorage.getItem(USER_KEY);
if (storedUser) {
	try {
		user.value = JSON.parse(storedUser);
	} catch (error) {
		console.error("解析用户信息失败:", error);
		localStorage.removeItem(USER_KEY);
	}
}

/**
 * 计算属性：是否已登录
 * 基于JWT token的有效性和用户信息的存在性
 */
const isAuthenticated = computed(() => {
	if (!token.value || !user.value) {
		return false;
	}

	// 使用JWT工具类验证token有效性
	return JwtUtils.isTokenValid(token.value);
});

/**
 * 计算属性：用户角色
 */
const userRole = computed(() => user.value?.role || "");

/**
 * 计算属性：用户权限（简单示例，实际应该从后端获取）
 */
const userPermissions = computed(() => {
	if (!user.value) return [];

	// 根据角色返回不同权限
	switch (user.value.role) {
		case "admin":
			return [
				"order:manage",
				"product:manage",
				"inventory:manage",
				"user:manage",
				"platform:manage",
			];
		case "manager":
			return ["order:manage", "product:manage", "inventory:manage"];
		case "operator":
			return ["order:view", "product:view", "inventory:view"];
		default:
			return [];
	}
});

/**
 * 计算属性：token信息
 */
const tokenInfo = computed(() => {
	if (!token.value) {
		return null;
	}

	return {
		expiration: JwtUtils.getTokenExpiration(token.value),
		remainingTime: JwtUtils.getTokenRemainingTime(token.value),
		needsRefresh: JwtUtils.needsRefresh(token.value),
		formatExpiration: JwtUtils.formatTokenExpiration(token.value),
	};
});

/**
 * 保存token到localStorage
 */
const saveToken = (tokenValue: string, refreshTokenValue?: string) => {
	token.value = tokenValue;
	TokenStorage.saveToken(tokenValue, refreshTokenValue);

	// 兼容旧的存储方式
	localStorage.setItem(TOKEN_KEY, tokenValue);
	if (refreshTokenValue) {
		refreshToken.value = refreshTokenValue;
		localStorage.setItem(REFRESH_TOKEN_KEY, refreshTokenValue);
	}
};

/**
 * 保存用户信息到localStorage
 */
const saveUser = (userInfo: IUser) => {
	user.value = userInfo;
	localStorage.setItem(USER_KEY, JSON.stringify(userInfo));
};

/**
 * 清除认证信息
 */
const clearAuth = () => {
	token.value = null;
	refreshToken.value = null;
	user.value = null;

	// 使用JWT工具类清除
	TokenStorage.clearTokens();

	// 兼容旧的存储方式
	localStorage.removeItem(TOKEN_KEY);
	localStorage.removeItem(REFRESH_TOKEN_KEY);
	localStorage.removeItem(USER_KEY);
};

/**
 * Mock登录实现
 * @param loginParams
 * @returns
 */
const _mockLogin = async (
	loginParams: ILoginParams
): Promise<ITokenResponse> => {
	console.warn("⚠️ 您当前处于Mock登录模式！");
	// 1. 创建一个假的JWT令牌 (永不过期)
	const fakeToken =
		"eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJzdWIiOiIxIiwibmFtZSI6Ik1vY2sgQWRtaW4iLCJyb2xlIjoiYWRtaW4iLCJpYXQiOjE1MTYyMzkwMjIsImV4cCI6OTk5OTk5OTk5OX0.WU8h3s_3DqA_s9Qh7Y_b0g0x_Jz_b-d_a-A-B-C-D-E";

	// 2. 创建一个模拟的用户信息, 严格遵循 IUserResponse 类型
	const mockUser: IUserResponse = {
		id: 1,
		username: loginParams.username,
		name: "超级管理员(Mock)",
		mobile: "13800138000",
		role: "admin",
	};

	// 3. 模拟API成功响应
	const response: ITokenResponse = {
		token: fakeToken,
		user: mockUser,
	};

	console.log("Mock登录成功, 生成的Token:", response.token);
	console.log("Mock用户信息:", response.user);

	// 4. 保存认证信息
	saveToken(response.token);
	// 由于 IUser 继承自 IUserResponse 且没有添加新属性，这里可以直接断言
	saveUser(response.user as IUser);

	console.log("用户认证信息已保存");
	return response;
};

/**
 * 真实API登录实现
 * @param loginParams
 * @returns
 */
const _realLogin = async (
	loginParams: ILoginParams
): Promise<ITokenResponse> => {
	// 调用登录API
	const response = await AuthApi.login(loginParams);
	console.log("登录成功:", response);

	// 验证返回的token
	if (!response.token || !JwtUtils.isTokenValid(response.token)) {
		throw new Error("返回的token无效");
	}

	// 保存认证信息
	saveToken(response.token);
	saveUser(response.user as IUser);

	console.log("用户认证信息已保存");
	return response;
};

/**
 * 用户登录
 */
const login = async (loginParams: ILoginParams): Promise<ITokenResponse> => {
	try {
		isLoading.value = true;
		if (IS_MOCK_ENABLED) {
			return await _mockLogin(loginParams);
		} else {
			return await _realLogin(loginParams);
		}
	} catch (error) {
		console.error("登录失败:", error);
		clearAuth();
		throw error;
	} finally {
		isLoading.value = false;
	}
};

/**
 * 用户登出
 */
const logout = async (): Promise<void> => {
	try {
		isLoading.value = true;

		// 调用登出API（可选）
		if (token.value) {
			try {
				await AuthApi.logout();
			} catch (error) {
				console.warn("登出API调用失败:", error);
			}
		}

		// 清除本地认证信息
		clearAuth();

		console.log("用户已登出");
	} catch (error) {
		console.error("登出失败:", error);
		// 即使API调用失败，也要清除本地信息
		clearAuth();
	} finally {
		isLoading.value = false;
	}
};

/**
 * 刷新token（如果后端支持）
 * 目前后端没有提供刷新token接口，此方法暂时返回false
 */
const refresh = async (): Promise<boolean> => {
	if (!refreshToken.value) {
		console.warn("没有refreshToken，无法刷新");
		return false;
	}

	try {
		// TODO: 当后端提供刷新token接口时，取消注释以下代码
		// const response = await AuthApi.refreshToken(refreshToken.value);
		// saveToken(response.token, response.refreshToken);
		// return true;

		console.warn("后端暂未提供刷新token接口");
		return false;
	} catch (error) {
		console.error("刷新token失败:", error);
		clearAuth();
		return false;
	}
};

/**
 * 获取当前用户信息
 */
const fetchCurrentUser = async (): Promise<IUser | null> => {
	if (!token.value) {
		return null;
	}

	try {
		const userInfo = await UserApi.getCurrentUser();
		saveUser(userInfo);
		return userInfo;
	} catch (error) {
		console.error("获取用户信息失败:", error);
		clearAuth();
		return null;
	}
};

/**
 * 检查用户是否有指定权限
 */
const hasPermission = (permission: string): boolean => {
	return userPermissions.value.includes(permission);
};

/**
 * 检查用户是否有指定角色
 */
const hasRole = (role: string): boolean => {
	return userRole.value === role;
};

/**
 * 初始化认证状态
 * 从本地存储中恢复用户状态，并验证token有效性
 */
const initAuth = async (): Promise<boolean> => {
	try {
		const storedToken = TokenStorage.getToken();

		if (!storedToken) {
			console.log("没有存储的token");
			return false;
		}

		// 使用JWT工具类验证token
		if (!JwtUtils.isTokenValid(storedToken)) {
			console.log("存储的token无效或已过期");
			clearAuth();
			return false;
		}

		// 从token中提取用户信息
		const userFromToken = JwtUtils.getUserFromToken(storedToken);
		if (!userFromToken) {
			console.log("无法从token中提取用户信息");
			clearAuth();
			return false;
		}

		// 设置token
		token.value = storedToken;

		// 如果有存储的完整用户信息，使用存储的信息；否则使用token中的基本信息
		if (!user.value) {
			// 从token构造基本用户信息
			const basicUserInfo: IUser = {
				id: parseInt(userFromToken.id),
				username: userFromToken.username,
				role: userFromToken.role,
				name: userFromToken.username, // 默认使用username作为name
				mobile: "", // 这些字段需要从完整的用户接口获取
			};

			user.value = basicUserInfo;

			// 尝试获取完整的用户信息
			try {
				const fullUserInfo = await fetchCurrentUser();
				if (fullUserInfo) {
					saveUser(fullUserInfo);
				}
			} catch (error) {
				console.warn("获取完整用户信息失败，使用基本信息:", error);
			}
		}

		console.log("认证状态初始化成功");
		return true;
	} catch (error) {
		console.error("初始化认证状态失败:", error);
		clearAuth();
		return false;
	} finally {
		// 确保无论成功或失败，都将初始化状态标记为完成
		isAuthInitialized.value = true;
	}
};

/**
 * 检查token是否需要刷新
 */
const checkTokenRefresh = async (): Promise<void> => {
	if (!token.value) {
		return;
	}

	if (JwtUtils.needsRefresh(token.value)) {
		console.log("Token即将过期，尝试刷新");
		const refreshed = await refresh();
		if (!refreshed) {
			console.log("Token刷新失败，需要重新登录");
			clearAuth();
		}
	}
};

/**
 * 设置路由加载状态
 */
const setRouteLoading = (status: boolean) => {
	isRouteLoading.value = status;
};

/**
 * 导出认证组合式函数
 */
export const useAuth = () => {
	return {
		// 状态
		token: computed(() => token.value),
		refreshToken: computed(() => refreshToken.value),
		user: computed(() => user.value),
		isLoading: computed(() => isLoading.value),
		isAuthenticated,
		isAuthInitialized: computed(() => isAuthInitialized.value),
		isRouteLoading: computed(() => isRouteLoading.value),
		userRole,
		userPermissions,
		tokenInfo,

		// 方法
		login,
		logout,
		refresh,
		fetchCurrentUser,
		hasPermission,
		hasRole,
		initAuth,
		checkTokenRefresh,
		clearAuth,
		setRouteLoading,

		// 工具方法
		getTokenInfo: () => TokenStorage.getTokenInfo(),
		isTokenExpired: (tokenToCheck?: string) => {
			const targetToken = tokenToCheck || token.value;
			return targetToken ? JwtUtils.isTokenExpired(targetToken) : true;
		},
	};
};

/**
 * 全局认证状态（用于非组件中访问）
 */
export const authState = {
	token,
	refreshToken,
	user,
	isLoading,
	isAuthenticated,
	isAuthInitialized,
	isRouteLoading,
	userRole,
	userPermissions,
};
