/**
 * 路由工具函数
 * <AUTHOR>
 *
 * 用于将路由结构转换为侧边栏导航结构的工具函数
 * 包含图标映射、路由过滤和数据格式转换功能
 */

import { type RouteRecordRaw } from "vue-router";
import {
	type LucideIcon,
	Home,
	ShoppingCart,
	List,
	Settings,
	Package,
	Warehouse,
} from "lucide-vue-next";

/**
 * 侧边栏导航项接口
 */
export interface INavMainItem {
	title: string;
	url: string;
	icon?: LucideIcon;
	isActive?: boolean;
	items?: {
		title: string;
		url: string;
	}[];
}

/**
 * 图标映射表
 * 根据路由的meta.icon字段映射到对应的lucide图标组件
 */
const iconMap: Record<string, LucideIcon> = {
	"home-filled": Home,
	"shopping-cart": ShoppingCart,
	list: List,
	settings: Settings,
	package: Package,
	warehouse: Warehouse,
};

/**
 * 将路由记录转换为侧边栏导航项
 * @param route - 路由记录
 * @param parentPath - 父路由路径，用于构造完整的子路由路径
 * @returns 侧边栏导航项或null（如果不应显示在菜单中）
 */
function convertRouteToNavItem(
	route: RouteRecordRaw,
	parentPath: string = ""
): INavMainItem | null {
	// 检查路由是否应该显示在菜单中
	if (!route.meta?.isMenu) {
		return null;
	}

	const title =
		(route.meta?.title as string) || (route.name as string) || "未命名";
	const iconKey = route.meta?.icon as string;
	const icon = iconKey ? iconMap[iconKey] : undefined;

	// 构造完整路径
	const fullPath = parentPath
		? `${parentPath}/${route.path}`.replace(/\/+/g, "/")
		: route.path;

	const navItem: INavMainItem = {
		title,
		url: fullPath,
		icon,
		isActive: false, // 可以根据当前路由动态设置
	};

	// 处理子路由
	// 如果是首页，不处理子路由
	// 如果是其他模块且有子路由，则展示子路由
	if (route.children && route.children.length > 0 && route.path !== "/") {
		const childItems = route.children
			.map((child) => convertRouteToNavItem(child, fullPath))
			.filter((item): item is INavMainItem => item !== null);

		if (childItems.length > 0) {
			navItem.items = childItems.map((child) => ({
				title: child.title,
				url: child.url,
			}));
		}
	}
	return navItem;
}

/**
 * 判断路由是否为顶级路由
 * @param route - 路由记录
 * @returns 是否为顶级路由
 */
function isTopLevelRoute(route: RouteRecordRaw): boolean {
	// 顶级路由的特征：
	// 1. path 不包含参数（如 :id）
	// 2. path 以 / 开头且只有一层（如 /order, /product）或者是根路径 /
	// 3. 不是 404 路由或通配符路由

	if (!route.path) return false;

	// 排除 404 和通配符路由
	if (route.path.includes("*") || route.path === "/404") return false;

	// 根路径是顶级路由
	if (route.path === "/") return true;

	// 检查是否为一级路径（如 /order, /product, /inventory）
	const pathParts = route.path.split("/").filter((part) => part !== "");
	return pathParts.length === 1 && !route.path.includes(":");
}

/**
 * 将路由数组转换为侧边栏导航数组
 * @param routes - 路由记录数组
 * @returns 侧边栏导航项数组
 */
export function convertRoutesToNavMain(
	routes: RouteRecordRaw[]
): INavMainItem[] {
	// 只处理顶级路由，避免子路由被当作顶级导航项
	const topLevelRoutes = routes.filter((route) => isTopLevelRoute(route));

	return topLevelRoutes
		.map((route) => convertRouteToNavItem(route))
		.filter((item): item is INavMainItem => item !== null);
}

/**
 * 根据当前路由路径设置激活状态
 * @param navItems - 侧边栏导航项数组
 * @param currentPath - 当前路由路径
 * @returns 更新激活状态后的导航项数组
 */
export function setActiveNavItem(
	navItems: INavMainItem[],
	currentPath: string
): INavMainItem[] {
	return navItems.map((item) => ({
		...item,
		isActive:
			item.url === currentPath ||
			(item.items && item.items.some((child) => child.url === currentPath)),
		items: item.items
			? item.items.map((child) => ({
					...child,
			  }))
			: undefined,
	}));
}
