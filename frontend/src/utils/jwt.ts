/**
 * JWT工具类
 * 提供JWT token的解析、验证和管理功能
 * <AUTHOR>
 */

/**
 * JWT payload接口
 */
export interface IJwtPayload {
	sub: string; // 用户ID
	username: string; // 用户名
	role: string; // 用户角色
	exp: number; // 过期时间戳
	iat: number; // 签发时间戳
	iss?: string; // 签发者
}

/**
 * JWT工具类
 */
export class JwtUtils {
	/**
	 * 解析JWT token
	 * @param token JWT token字符串
	 * @returns 解析后的payload，如果解析失败则返回null
	 */
	static parseToken(token: string): IJwtPayload | null {
		try {
			if (!token || typeof token !== "string") {
				return null;
			}

			// JWT由三部分组成：header.payload.signature
			const parts = token.split(".");
			if (parts.length !== 3) {
				console.warn("JWT格式不正确");
				return null;
			}

			// 解码payload部分（base64url编码）
			const payload = parts[1];
			const decodedPayload = this.base64UrlDecode(payload);

			if (!decodedPayload) {
				console.warn("JWT payload解码失败");
				return null;
			}

			const parsedPayload = JSON.parse(decodedPayload) as IJwtPayload;

			// 基本验证
			if (!parsedPayload.sub || !parsedPayload.exp) {
				console.warn("JWT payload缺少必要字段");
				return null;
			}

			return parsedPayload;
		} catch (error) {
			console.error("解析JWT token失败:", error);
			return null;
		}
	}

	/**
	 * 检查token是否过期
	 * @param token JWT token字符串
	 * @returns true表示已过期，false表示未过期
	 */
	static isTokenExpired(token: string): boolean {
		const payload = this.parseToken(token);
		if (!payload) {
			return true; // 无法解析的token视为过期
		}

		// 获取当前时间戳（秒）
		const currentTime = Math.floor(Date.now() / 1000);

		// 添加5分钟的缓冲时间，提前判断过期
		const bufferTime = 5 * 60; // 5分钟

		return payload.exp <= currentTime + bufferTime;
	}

	/**
	 * 验证token是否有效
	 * @param token JWT token字符串
	 * @returns true表示有效，false表示无效
	 */
	static isTokenValid(token: string): boolean {
		if (!token) {
			return false;
		}

		// 检查token格式
		const payload = this.parseToken(token);
		if (!payload) {
			return false;
		}

		// 检查是否过期
		if (this.isTokenExpired(token)) {
			return false;
		}

		return true;
	}

	/**
	 * 获取token的过期时间
	 * @param token JWT token字符串
	 * @returns 过期时间的Date对象，如果无法获取则返回null
	 */
	static getTokenExpiration(token: string): Date | null {
		const payload = this.parseToken(token);
		if (!payload) {
			return null;
		}

		return new Date(payload.exp * 1000);
	}

	/**
	 * 获取token的剩余有效时间（秒）
	 * @param token JWT token字符串
	 * @returns 剩余秒数，如果已过期或无效则返回0
	 */
	static getTokenRemainingTime(token: string): number {
		const payload = this.parseToken(token);
		if (!payload) {
			return 0;
		}

		const currentTime = Math.floor(Date.now() / 1000);
		const remainingTime = payload.exp - currentTime;

		return Math.max(0, remainingTime);
	}

	/**
	 * 从token中提取用户信息
	 * @param token JWT token字符串
	 * @returns 用户信息对象，如果无法提取则返回null
	 */
	static getUserFromToken(token: string): {
		id: string;
		username: string;
		role: string;
	} | null {
		const payload = this.parseToken(token);
		if (!payload) {
			return null;
		}

		return {
			id: payload.sub,
			username: payload.username,
			role: payload.role,
		};
	}

	/**
	 * Base64URL解码
	 * @param input base64url编码的字符串
	 * @returns 解码后的字符串
	 */
	private static base64UrlDecode(input: string): string | null {
		try {
			// 将base64url转换为base64
			let base64 = input.replace(/-/g, "+").replace(/_/g, "/");

			// 添加padding
			while (base64.length % 4) {
				base64 += "=";
			}

			// 解码
			return atob(base64);
		} catch (error) {
			console.error("Base64URL解码失败:", error);
			return null;
		}
	}

	/**
	 * 格式化token过期时间为可读字符串
	 * @param token JWT token字符串
	 * @returns 格式化的时间字符串
	 */
	static formatTokenExpiration(token: string): string {
		const expiration = this.getTokenExpiration(token);
		if (!expiration) {
			return "无效token";
		}

		const now = new Date();
		const diff = expiration.getTime() - now.getTime();

		if (diff <= 0) {
			return "已过期";
		}

		const hours = Math.floor(diff / (1000 * 60 * 60));
		const minutes = Math.floor((diff % (1000 * 60 * 60)) / (1000 * 60));

		if (hours > 0) {
			return `${hours}小时${minutes}分钟后过期`;
		} else {
			return `${minutes}分钟后过期`;
		}
	}

	/**
	 * 检查token是否需要刷新
	 * @param token JWT token字符串
	 * @param refreshThreshold 刷新阈值（秒），默认30分钟
	 * @returns true表示需要刷新
	 */
	static needsRefresh(
		token: string,
		refreshThreshold: number = 30 * 60
	): boolean {
		const remainingTime = this.getTokenRemainingTime(token);
		return remainingTime > 0 && remainingTime <= refreshThreshold;
	}
}

/**
 * Token存储管理类
 */
export class TokenStorage {
	private static readonly TOKEN_KEY = "erp_token";
	private static readonly REFRESH_TOKEN_KEY = "erp_refresh_token";
	private static readonly TOKEN_EXPIRY_KEY = "erp_token_expiry";

	/**
	 * 保存token
	 * @param token JWT token
	 * @param refreshToken 刷新token（可选）
	 */
	static saveToken(token: string, refreshToken?: string): void {
		try {
			localStorage.setItem(this.TOKEN_KEY, token);

			if (refreshToken) {
				localStorage.setItem(this.REFRESH_TOKEN_KEY, refreshToken);
			}

			// 保存过期时间
			const expiration = JwtUtils.getTokenExpiration(token);
			if (expiration) {
				localStorage.setItem(this.TOKEN_EXPIRY_KEY, expiration.toISOString());
			}
		} catch (error) {
			console.error("保存token失败:", error);
		}
	}

	/**
	 * 获取token
	 * @returns token字符串，如果不存在则返回null
	 */
	static getToken(): string | null {
		try {
			return localStorage.getItem(this.TOKEN_KEY);
		} catch (error) {
			console.error("获取token失败:", error);
			return null;
		}
	}

	/**
	 * 获取刷新token
	 * @returns 刷新token字符串，如果不存在则返回null
	 */
	static getRefreshToken(): string | null {
		try {
			return localStorage.getItem(this.REFRESH_TOKEN_KEY);
		} catch (error) {
			console.error("获取刷新token失败:", error);
			return null;
		}
	}

	/**
	 * 清除所有token
	 */
	static clearTokens(): void {
		try {
			localStorage.removeItem(this.TOKEN_KEY);
			localStorage.removeItem(this.REFRESH_TOKEN_KEY);
			localStorage.removeItem(this.TOKEN_EXPIRY_KEY);
		} catch (error) {
			console.error("清除token失败:", error);
		}
	}

	/**
	 * 检查本地存储的token是否有效
	 * @returns true表示有效，false表示无效
	 */
	static isStoredTokenValid(): boolean {
		const token = this.getToken();
		if (!token) {
			return false;
		}

		return JwtUtils.isTokenValid(token);
	}

	/**
	 * 获取token信息摘要
	 * @returns token信息对象
	 */
	static getTokenInfo(): {
		hasToken: boolean;
		isValid: boolean;
		user: { id: string; username: string; role: string } | null;
		expiration: Date | null;
		remainingTime: number;
	} {
		const token = this.getToken();

		if (!token) {
			return {
				hasToken: false,
				isValid: false,
				user: null,
				expiration: null,
				remainingTime: 0,
			};
		}

		const isValid = JwtUtils.isTokenValid(token);
		const user = JwtUtils.getUserFromToken(token);
		const expiration = JwtUtils.getTokenExpiration(token);
		const remainingTime = JwtUtils.getTokenRemainingTime(token);

		return {
			hasToken: true,
			isValid,
			user,
			expiration,
			remainingTime,
		};
	}
}
