import axios, {
	type AxiosInstance,
	type AxiosRequestConfig,
	type AxiosResponse,
	type InternalAxiosRequestConfig,
} from "axios";

/**
 * API响应的基础结构
 * <AUTHOR>
 */
export interface IApiResponse<T = any> {
	code: number;
	message: string;
	data: T;
	success: boolean;
}

/**
 * 请求配置接口
 * <AUTHOR>
 */
export interface IRequestConfig extends AxiosRequestConfig {
	showLoading?: boolean;
	showErrorMessage?: boolean;
}

/**
 * 获取API基础URL
 * 优先使用环境变量，如果没有则使用默认值
 * <AUTHOR>
 */
const getApiBaseUrl = (): string => {
	// 开发环境下使用代理路径，包含API版本号
	if (import.meta.env.DEV) {
		return "/api/v1";
	}

	// 生产环境下可以使用环境变量配置
	return import.meta.env.VITE_API_BASE_URL || "/api/v1";
};

/**
 * 创建axios实例
 * <AUTHOR>
 */
const createAxiosInstance = (): AxiosInstance => {
	const instance = axios.create({
		baseURL: getApiBaseUrl(), // 动态获取后端API基础路径
		timeout: 10000, // 请求超时时间
		headers: {
			"Content-Type": "application/json",
		},
	});

	// 请求拦截器
	instance.interceptors.request.use(
		(config: InternalAxiosRequestConfig) => {
			// 添加token到请求头
			const token = localStorage.getItem("erp_token");
			if (token) {
				config.headers.Authorization = `Bearer ${token}`;
			}

			// 显示加载状态（可选）
			const requestConfig = config as IRequestConfig;
			if (requestConfig.showLoading) {
				// 这里可以集成loading组件
				console.log("开始请求，显示loading...");
			}

			// 开发环境下打印请求信息
			if (import.meta.env.DEV) {
				console.log(
					`🚀 API请求: ${config.method?.toUpperCase()} ${config.baseURL}${
						config.url
					}`
				);
			}

			return config;
		},
		(error) => {
			console.error("请求错误:", error);
			return Promise.reject(error);
		}
	);

	// 响应拦截器
	instance.interceptors.response.use(
		(response: AxiosResponse<IApiResponse>) => {
			const { data } = response;

			// 隐藏加载状态
			console.log("请求完成，隐藏loading...");

			// 开发环境下打印响应信息
			if (import.meta.env.DEV) {
				console.log(
					`✅ API响应: ${response.status} ${response.config.url}`,
					data
				);
			}

			// 检查业务状态码
			if (data.code === 200 || data.code === 201) {
				return response;
			} else {
				// 业务错误处理
				const errorMessage = data.message || "请求失败";
				console.error("业务错误:", errorMessage);

				// 显示错误消息（可选）
				const requestConfig = response.config as IRequestConfig;
				if (requestConfig.showErrorMessage !== false) {
					// 这里可以集成消息提示组件
					console.error("错误提示:", errorMessage);
				}

				return Promise.reject(new Error(errorMessage));
			}
		},
		(error) => {
			console.log("请求完成，隐藏loading...");

			// HTTP状态码错误处理
			const { response } = error;
			let errorMessage = "网络错误";

			if (response) {
				switch (response.status) {
					case 401:
						errorMessage = "未授权，请重新登录";
						// 处理token过期，清除认证信息
						localStorage.removeItem("erp_token");
						localStorage.removeItem("erp_refresh_token");
						localStorage.removeItem("erp_user");

						// 在开发环境中，如果当前不在登录页，则跳转到登录页
						if (
							import.meta.env.DEV &&
							!window.location.pathname.includes("/login")
						) {
							console.warn("Token已过期，需要重新登录");
							// 这里可以通过事件或其他方式通知应用状态更新
							window.location.href = "/login";
						}
						break;
					case 403:
						errorMessage = "禁止访问";
						break;
					case 404:
						errorMessage = "请求的资源不存在";
						break;
					case 500:
						errorMessage = "服务器内部错误";
						break;
					default:
						errorMessage = `请求失败: ${response.status}`;
				}
			} else if (error.code === "ECONNABORTED") {
				errorMessage = "请求超时";
			} else if (error.message === "Network Error") {
				errorMessage = "网络连接失败，请检查网络或后端服务是否正常运行";
			}

			console.error("❌ HTTP错误:", errorMessage);

			// 开发环境下提供更详细的错误信息
			if (import.meta.env.DEV) {
				console.error("详细错误信息:", error);
				console.error("请确保后端服务正在运行在 http://localhost:8080");
			}

			return Promise.reject(new Error(errorMessage));
		}
	);

	return instance;
};

// 创建axios实例
const request = createAxiosInstance();

/**
 * 通用请求方法
 * <AUTHOR>
 */
export class ApiRequest {
	/**
	 * GET请求
	 * @param url 请求地址
	 * @param config 请求配置
	 */
	static async get<T = any>(
		url: string,
		config?: IRequestConfig
	): Promise<IApiResponse<T>> {
		const response = await request.get<IApiResponse<T>>(url, config);
		return response.data;
	}

	/**
	 * POST请求
	 * @param url 请求地址
	 * @param data 请求数据
	 * @param config 请求配置
	 */
	static async post<T = any>(
		url: string,
		data?: any,
		config?: IRequestConfig
	): Promise<IApiResponse<T>> {
		const response = await request.post<IApiResponse<T>>(url, data, config);
		return response.data;
	}

	/**
	 * PUT请求
	 * @param url 请求地址
	 * @param data 请求数据
	 * @param config 请求配置
	 */
	static async put<T = any>(
		url: string,
		data?: any,
		config?: IRequestConfig
	): Promise<IApiResponse<T>> {
		const response = await request.put<IApiResponse<T>>(url, data, config);
		return response.data;
	}

	/**
	 * DELETE请求
	 * @param url 请求地址
	 * @param config 请求配置
	 */
	static async delete<T = any>(
		url: string,
		config?: IRequestConfig
	): Promise<IApiResponse<T>> {
		const response = await request.delete<IApiResponse<T>>(url, config);
		return response.data;
	}

	/**
	 * PATCH请求
	 * @param url 请求地址
	 * @param data 请求数据
	 * @param config 请求配置
	 */
	static async patch<T = any>(
		url: string,
		data?: any,
		config?: IRequestConfig
	): Promise<IApiResponse<T>> {
		const response = await request.patch<IApiResponse<T>>(url, data, config);
		return response.data;
	}
}

export default request;
