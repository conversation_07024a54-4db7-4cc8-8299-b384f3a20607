<script setup lang="ts">
/**
 * 应用侧边栏组件
 * <AUTHOR>
 * 
 * 基于路由配置动态生成侧边栏导航的组件
 * 支持团队切换、导航菜单、项目列表和用户信息展示
 */

import type { SidebarProps } from '@/components/ui/sidebar'
import {
  Sidebar,
  SidebarContent,
  SidebarFooter,
  SidebarHeader,
  SidebarRail,
} from '@/components/ui/sidebar'
import {
  AudioWaveform,
  Command,
  Frame,
  GalleryVerticalEnd,
  Map,
  PieChart,
} from 'lucide-vue-next'
import { computed } from 'vue'
import { useRoute } from 'vue-router'
import router from '@/router'
import { convertRoutesToNavMain, setActiveNavItem } from '@/utils/routeUtils'
import TeamSwitcher from './TeamSwitcher.vue'
import NavMain from './NavMain.vue'
import NavProjects from './NavProjects.vue'
import NavUser from './NavUser.vue'

const props = withDefaults(defineProps<SidebarProps>(), {
  collapsible: 'icon',
})

const route = useRoute()

// 基础数据配置
const data = {
  user: {
    name: '9Wings ERP',
    email: '<EMAIL>',
    avatar: '/avatars/wings-logo.jpg',
  },
  teams: [
    {
      name: '9Wings ERP',
      logo: GalleryVerticalEnd,
      plan: 'Enterprise',
    },
    {
      name: '9Wings Test',
      logo: AudioWaveform,
      plan: 'Development',
    },
    {
      name: 'Wings Demo',
      logo: Command,
      plan: 'Demo',
    },
  ],
  projects: [
    {
      name: '订单管理',
      url: '/order',
      icon: Frame,
    },
    {
      name: '数据分析',
      url: '/analytics',
      icon: PieChart,
    },
    {
      name: '系统设置',
      url: '/settings',
      icon: Map,
    },
  ],
}

// 动态生成navMain - 基于路由配置
const navMain = computed(() => {
  const routes = router.getRoutes()
  const navItems = convertRoutesToNavMain(routes.filter(route => route.path !== '/:pathMatch(.*)' && route.path !== '/404'))
  return setActiveNavItem(navItems, route.path)
})
</script>

<template>
  <Sidebar v-bind="props">
    <SidebarHeader>
      <TeamSwitcher :teams="data.teams" />
    </SidebarHeader>
    <SidebarContent>
      <NavMain :items="navMain" />
      <NavProjects :projects="data.projects" />
    </SidebarContent>
    <SidebarFooter>
      <NavUser :user="data.user" />
    </SidebarFooter>
    <SidebarRail />
  </Sidebar>
</template>