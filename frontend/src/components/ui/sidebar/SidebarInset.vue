<script setup lang="ts">
import type { HTMLAttributes } from "vue";
import { cn } from "@/lib/utils";

/**
 * 侧边栏内容区域组件
 * <AUTHOR>
 *
 * 主要内容显示区域，支持响应式布局
 * 确保在不同屏幕尺寸下都能正确显示内容
 * 只隐藏横向溢出，保留纵向滚动功能
 */

const props = defineProps<{
	class?: HTMLAttributes["class"];
}>();
</script>

<template>
	<main
		data-slot="sidebar-inset"
		:class="
			cn(
				'bg-background relative flex w-full flex-1 flex-col min-w-0 overflow-x-hidden',
				'md:peer-data-[variant=inset]:m-2 md:peer-data-[variant=inset]:ml-0 md:peer-data-[variant=inset]:rounded-xl md:peer-data-[variant=inset]:shadow-sm md:peer-data-[variant=inset]:peer-data-[state=collapsed]:ml-2',
				props.class
			)
		"
	>
		<slot />
	</main>
</template>

<style scoped>
/**
 * 侧边栏内容区域样式
 * <AUTHOR>
 * 确保主内容区域在各种屏幕尺寸下都能正确显示
 * 只隐藏横向溢出，保留纵向滚动功能
 */

/* 确保主内容区域不会超出可用空间，但允许纵向滚动 */
main[data-slot="sidebar-inset"] {
	max-width: 100%;
	box-sizing: border-box;
	/* 只隐藏横向溢出 */
	overflow-x: hidden;
}

/* 在移动设备上优化布局 */
@media (max-width: 768px) {
	main[data-slot="sidebar-inset"] {
		width: 100%;
		margin: 0;
		border-radius: 0;
	}
}
</style>
