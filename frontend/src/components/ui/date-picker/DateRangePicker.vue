/** * Date Range Picker 组件 * <AUTHOR> * 基于 reka-ui 的 RangeCalendar
组件实现的日期范围选择器 * 支持开始日期和结束日期的选择，提供友好的用户界面 */

<script setup lang="ts">
import type { DateRange } from "reka-ui";
import {
	CalendarDate,
	DateFormatter,
	getLocalTimeZone,
	type DateValue,
} from "@internationalized/date";

import { CalendarIcon } from "lucide-vue-next";
import { type Ref, ref, watch, nextTick } from "vue";
import { cn } from "@/lib/utils";
import { Button } from "@/components/ui/button";
import {
	Popover,
	PopoverContent,
	PopoverTrigger,
} from "@/components/ui/popover";
import { RangeCalendar } from "@/components/ui/range-calendar";

// 定义日期范围类型（兼容外部使用的 Date 类型）
interface IDateRange {
	start?: Date;
	end?: Date;
}

// 定义组件属性
interface IDateRangePickerProps {
	modelValue?: IDateRange;
	placeholder?: string;
	disabled?: boolean;
}

// 定义组件事件
interface IDateRangePickerEmits {
	(e: "update:modelValue", value: IDateRange): void;
	(e: "change", value: IDateRange): void;
}

// 组件属性和事件
const props = withDefaults(defineProps<IDateRangePickerProps>(), {
	placeholder: "选择日期范围",
	disabled: false,
});

const emit = defineEmits<IDateRangePickerEmits>();

// 日期格式化器 - 使用更简洁的格式
const df = new DateFormatter("zh-CN", {
	year: "numeric",
	month: "2-digit",
	day: "2-digit",
});

// 将 JavaScript Date 转换为 CalendarDate
const jsToCalendarDate = (date: Date): CalendarDate => {
	return new CalendarDate(
		date.getFullYear(),
		date.getMonth() + 1,
		date.getDate()
	);
};

// 将 DateValue 转换为 JavaScript Date
const dateValueToJs = (date: DateValue): Date => {
	return date.toDate(getLocalTimeZone());
};

// 初始化内部值
const initValue = (): DateRange => {
	if (props.modelValue?.start && props.modelValue?.end) {
		return {
			start: jsToCalendarDate(props.modelValue.start),
			end: jsToCalendarDate(props.modelValue.end),
		};
	} else if (props.modelValue?.start) {
		return {
			start: jsToCalendarDate(props.modelValue.start),
			end: jsToCalendarDate(props.modelValue.start),
		};
	} else {
		// 默认值：今天到今天
		const today = new Date();
		const calendarToday = jsToCalendarDate(today);
		return {
			start: calendarToday,
			end: calendarToday,
		};
	}
};

// 内部日期范围值
const value = ref(initValue()) as Ref<DateRange>;

// 添加标志位防止循环更新
const isInternalUpdate = ref(false);

// 监听内部值变化，向外发送事件
watch(
	value,
	(newValue) => {
		if (isInternalUpdate.value) {
			return; // 如果是内部更新，不发送事件
		}

		const jsDateRange: IDateRange = {
			start: newValue.start ? dateValueToJs(newValue.start) : undefined,
			end: newValue.end ? dateValueToJs(newValue.end) : undefined,
		};
		emit("update:modelValue", jsDateRange);
		emit("change", jsDateRange);
	},
	{ deep: true }
);

// 监听外部传入的值变化
watch(
	() => props.modelValue,
	(newValue, oldValue) => {
		// 避免不必要的更新
		if (
			newValue?.start?.getTime() === oldValue?.start?.getTime() &&
			newValue?.end?.getTime() === oldValue?.end?.getTime()
		) {
			return;
		}

		isInternalUpdate.value = true;
		if (newValue) {
			value.value = initValue();
		} else {
			// 如果外部传入 undefined，重置为默认值
			const today = new Date();
			const calendarToday = jsToCalendarDate(today);
			value.value = {
				start: calendarToday,
				end: calendarToday,
			};
		}
		// 下一个 tick 后重置标志位
		nextTick(() => {
			isInternalUpdate.value = false;
		});
	},
	{ deep: true }
);

// 处理开始日期变化
const handleStartValueUpdate = (startDate: DateValue | undefined) => {
	if (startDate) {
		value.value.start = startDate;
	}
};
</script>

<template>
	<Popover>
		<PopoverTrigger as-child>
			<Button
				variant="outline"
				:class="
					cn(
						'w-[280px] justify-start text-left font-normal',
						!value.start && 'text-muted-foreground'
					)
				"
				:disabled="disabled"
			>
				<CalendarIcon class="mr-2 h-4 w-4 flex-shrink-0" />
				<span class="truncate">
					<template v-if="value.start">
						<template v-if="value.end">
							{{ df.format(value.start.toDate(getLocalTimeZone())) }} -
							{{ df.format(value.end.toDate(getLocalTimeZone())) }}
						</template>

						<template v-else>
							{{ df.format(value.start.toDate(getLocalTimeZone())) }}
						</template>
					</template>
					<template v-else>
						{{ placeholder }}
					</template>
				</span>
			</Button>
		</PopoverTrigger>
		<PopoverContent class="w-[500px] p-0">
			<RangeCalendar
				v-model="value"
				initial-focus
				:number-of-months="2"
				@update:start-value="handleStartValueUpdate"
			/>
		</PopoverContent>
	</Popover>
</template>
