<script setup lang="ts">
import type { HTMLAttributes } from "vue";

/**
 * 页面容器组件
 * <AUTHOR>
 *
 * 提供统一的页面布局容器，确保页面内容不会超出父组件宽度
 * 所有页面组件应该使用这个容器来包装内容
 * 只隐藏横向溢出，保留纵向滚动功能
 */

interface IPageContainerProps {
	class?: HTMLAttributes["class"];
	/** 是否显示页面padding */
	noPadding?: boolean;
	/** 是否允许垂直滚动 */
	scrollable?: boolean;
}

const props = withDefaults(defineProps<IPageContainerProps>(), {
	noPadding: false,
	scrollable: true,
});
</script>

<template>
	<div
		:class="[
			'w-full max-w-full min-w-0 box-border overflow-x-hidden',
			{
				'p-4 md:p-6': !props.noPadding,
				'p-2 sm:p-4': props.noPadding, // 为noPadding情况提供最小padding
				'overflow-y-auto': props.scrollable,
				'overflow-y-hidden': !props.scrollable,
			},
			props.class,
		]"
	>
		<div class="w-full max-w-full min-w-0 space-y-4">
			<slot />
		</div>
	</div>
</template>

<style scoped>
/**
 * 页面容器样式
 * <AUTHOR>
 * 确保容器内的所有内容都不会超出宽度限制
 * 只隐藏横向溢出，保留纵向滚动功能
 */

/* 确保容器内的所有子元素都遵循宽度限制 */
:deep(*) {
	max-width: 100%;
	box-sizing: border-box;
}

/* 特别处理常见的宽内容元素 */
:deep(table) {
	width: 100%;
	max-width: 100%;
	table-layout: fixed;
	overflow-x: auto;
	display: block;
	white-space: nowrap;
}

:deep(table th),
:deep(table td) {
	word-wrap: break-word;
	word-break: break-all;
	overflow: hidden;
	text-overflow: ellipsis;
	max-width: 200px;
}

:deep(img) {
	max-width: 100%;
	height: auto;
}

:deep(pre),
:deep(code) {
	max-width: 100%;
	overflow-x: auto;
	word-wrap: break-word;
	white-space: pre-wrap;
}

/* Card组件样式优化 */
:deep(.card) {
	max-width: 100%;
	overflow-x: hidden;
}

/* Grid布局优化 */
:deep(.grid) {
	max-width: 100%;
	overflow-x: hidden;
}

/* Flex布局优化 */
:deep(.flex) {
	min-width: 0;
	max-width: 100%;
}

/* 滚动条样式优化 */
:deep(.overflow-y-auto) {
	scrollbar-width: thin;
	scrollbar-color: rgba(156, 163, 175, 0.5) transparent;
}

:deep(.overflow-y-auto::-webkit-scrollbar) {
	width: 6px;
}

:deep(.overflow-y-auto::-webkit-scrollbar-track) {
	background: transparent;
}

:deep(.overflow-y-auto::-webkit-scrollbar-thumb) {
	background-color: rgba(156, 163, 175, 0.5);
	border-radius: 3px;
}

:deep(.overflow-y-auto::-webkit-scrollbar-thumb:hover) {
	background-color: rgba(156, 163, 175, 0.7);
}

/* 移动端优化 */
@media (max-width: 768px) {
	:deep(table th),
	:deep(table td) {
		max-width: 120px;
		font-size: 0.875rem;
		padding: 0.25rem;
	}

	:deep(.grid) {
		grid-template-columns: 1fr;
	}

	:deep(.flex) {
		flex-wrap: wrap;
	}
}

@media (max-width: 480px) {
	:deep(table th),
	:deep(table td) {
		max-width: 80px;
		font-size: 0.75rem;
		padding: 0.125rem;
	}
}
</style>
