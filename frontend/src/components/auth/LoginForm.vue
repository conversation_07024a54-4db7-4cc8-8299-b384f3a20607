<template>
	<Card class="w-full max-w-sm">
		<CardHeader>
			<CardTitle class="text-2xl">登录</CardTitle>
			<CardDescription> 请输入您的用户名和密码来访问系统 </CardDescription>
		</CardHeader>
		<CardContent class="grid gap-4">
			<form @submit="onSubmit" class="space-y-4">
				<FormField v-slot="{ componentField }" name="username">
					<FormItem>
						<FormLabel>用户名</FormLabel>
						<FormControl>
							<Input
								type="text"
								placeholder="请输入用户名"
								v-bind="componentField"
								:disabled="isLoading"
							/>
						</FormControl>
						<FormMessage />
					</FormItem>
				</FormField>

				<FormField v-slot="{ componentField }" name="password">
					<FormItem>
						<FormLabel>密码</FormLabel>
						<FormControl>
							<Input
								type="password"
								placeholder="请输入密码"
								v-bind="componentField"
								:disabled="isLoading"
							/>
						</FormControl>
						<FormMessage />
					</FormItem>
				</FormField>

				<div class="flex items-center space-x-2">
					<input
						id="remember"
						type="checkbox"
						v-model="rememberMe"
						class="h-4 w-4 text-primary focus:ring-primary border-gray-300 rounded"
						:disabled="isLoading"
					/>
					<label for="remember" class="text-sm text-gray-600"> 记住我 </label>
				</div>

				<Button type="submit" class="w-full" :disabled="isLoading">
					<template v-if="isLoading">
						<div class="flex items-center space-x-2">
							<div
								class="animate-spin rounded-full h-4 w-4 border-b-2 border-white"
							></div>
							<span>登录中...</span>
						</div>
					</template>
					<template v-else> 登录 </template>
				</Button>
			</form>

			<!-- 错误提示 -->
			<div
				v-if="errorMessage"
				class="p-3 text-sm text-red-600 bg-red-50 border border-red-200 rounded-md"
			>
				{{ errorMessage }}
			</div>

			<!-- 成功提示 -->
			<div
				v-if="successMessage"
				class="p-3 text-sm text-green-600 bg-green-50 border border-green-200 rounded-md"
			>
				{{ successMessage }}
			</div>
		</CardContent>
		<CardFooter class="flex flex-col space-y-2">
			<div class="text-sm text-gray-600 text-center">
				<a
					href="#"
					class="text-primary hover:underline"
					@click.prevent="handleForgotPassword"
				>
					忘记密码？
				</a>
			</div>
			<div class="text-xs text-gray-500 text-center">9Wings ERP系统 v1.0</div>
		</CardFooter>
	</Card>
</template>

<script setup lang="ts">
/**
 * 登录表单组件
 * 使用shadcn-vue构建的登录界面
 * <AUTHOR>
 */

import { ref, computed } from "vue";
import { useForm } from "vee-validate";
import { toTypedSchema } from "@vee-validate/zod";
import * as z from "zod";
import { useRouter } from "vue-router";

import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import {
	Card,
	CardContent,
	CardDescription,
	CardFooter,
	CardHeader,
	CardTitle,
} from "@/components/ui/card";
import {
	FormControl,
	FormField,
	FormItem,
	FormLabel,
	FormMessage,
} from "@/components/ui/form";

import { useAuth } from "@/stores/auth";
import type { ILoginParams } from "@/types/api";

/**
 * 表单验证规则
 */
const loginSchema = toTypedSchema(
	z.object({
		username: z
			.string()
			.min(1, { message: "请输入用户名" })
			.min(3, { message: "用户名至少3个字符" })
			.max(50, { message: "用户名不能超过50个字符" }),
		password: z
			.string()
			.min(1, { message: "请输入密码" })
			.min(6, { message: "密码至少6个字符" })
			.max(100, { message: "密码不能超过100个字符" }),
	})
);

/**
 * 初始化表单
 */
const form = useForm({
	validationSchema: loginSchema,
	initialValues: {
		username: "",
		password: "",
	},
});

/**
 * 组件状态
 */
const router = useRouter();
const auth = useAuth();
const rememberMe = ref(false);
const errorMessage = ref("");
const successMessage = ref("");

/**
 * 计算属性
 */
const isLoading = computed(() => auth.isLoading.value);

/**
 * 清除消息
 */
const clearMessages = () => {
	errorMessage.value = "";
	successMessage.value = "";
};

/**
 * 处理登录表单提交
 */
const onSubmit = form.handleSubmit(async (values) => {
	try {
		clearMessages();

		const loginParams: ILoginParams = {
			username: values.username,
			password: values.password,
		};

		// 调用登录方法
		const response = await auth.login(loginParams);

		// 如果选择了记住我，可以设置更长的过期时间（这里只是示例）
		if (rememberMe.value) {
			console.log("用户选择了记住我");
			// 可以在这里设置更长的token过期时间或其他逻辑
		}

		successMessage.value = `欢迎回来，${response.user.username}！`;

		// 登录成功后跳转到目标页面或首页
		setTimeout(() => {
			const redirectPath =
				(router.currentRoute.value.query.redirect as string) || "/home";
			console.log(`登录成功，跳转到: ${redirectPath}`);
			router.push(redirectPath);
		}, 1000);
	} catch (error: any) {
		console.error("登录失败:", error);

		// 显示错误信息
		if (error.message) {
			errorMessage.value = error.message;
		} else {
			errorMessage.value = "登录失败，请检查用户名和密码";
		}
	}
});

/**
 * 处理忘记密码
 */
const handleForgotPassword = () => {
	// 这里可以跳转到忘记密码页面或显示重置密码模态框
	console.log("忘记密码功能待实现");
	errorMessage.value = "";
	successMessage.value = "忘记密码功能正在开发中...";
};

/**
 * 组件挂载时的处理
 */
import { onMounted } from "vue";

onMounted(() => {
	// 如果已经登录，直接跳转到目标页面或首页
	if (auth.isAuthenticated.value) {
		const redirectPath =
			(router.currentRoute.value.query.redirect as string) || "/home";
		console.log(`用户已登录，跳转到: ${redirectPath}`);
		router.push(redirectPath);
	}
});
</script>

<style scoped>
/* 额外的样式定制 */
.animate-spin {
	animation: spin 1s linear infinite;
}

@keyframes spin {
	from {
		transform: rotate(0deg);
	}
	to {
		transform: rotate(360deg);
	}
}
</style>
