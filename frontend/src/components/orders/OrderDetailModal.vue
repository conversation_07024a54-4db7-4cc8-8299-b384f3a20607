/** * 订单详情弹窗组件 * <AUTHOR> * 使用官方 shadcn-vue Dialog
组件实现的订单详情弹窗，显示订单的详细信息 */

<template>
	<Dialog :open="true" @update:open="handleClose">
		<DialogContent class="max-w-6xl max-h-[90vh] overflow-y-auto">
			<DialogHeader>
				<DialogTitle class="flex items-center gap-2">
					<Package class="h-5 w-5 text-blue-600" />
					订单详情
				</DialogTitle>
				<DialogDescription>
					查看订单 {{ order?.posting_number }} 的详细信息
				</DialogDescription>
			</DialogHeader>

			<div v-if="order" class="space-y-6">
				<!-- 基本信息卡片 -->
				<Card>
					<CardHeader>
						<CardTitle class="flex items-center gap-2">
							<Info class="h-4 w-4" />
							基本信息
						</CardTitle>
					</CardHeader>
					<CardContent>
						<div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
							<div class="space-y-2">
								<Label class="text-sm font-medium text-muted-foreground">
									订单号
								</Label>
								<div class="flex items-center gap-2">
									<Badge variant="outline" class="font-mono">
										{{ order.posting_number }}
									</Badge>
									<Button
										variant="ghost"
										size="sm"
										@click="copyToClipboard(order.posting_number)"
									>
										<Copy class="h-3 w-3" />
									</Button>
								</div>
							</div>

							<div class="space-y-2">
								<Label class="text-sm font-medium text-muted-foreground">
									外部ID
								</Label>
								<div class="flex items-center gap-2">
									<Badge variant="outline" class="font-mono">
										{{ order.external_id }}
									</Badge>
									<Button
										variant="ghost"
										size="sm"
										@click="copyToClipboard(String(order.external_id))"
									>
										<Copy class="h-3 w-3" />
									</Button>
								</div>
							</div>

							<div class="space-y-2">
								<Label class="text-sm font-medium text-muted-foreground">
									客户姓名
								</Label>
								<p class="text-sm font-medium">
									{{ order.customer_name || "未知客户" }}
								</p>
							</div>

							<div class="space-y-2">
								<Label class="text-sm font-medium text-muted-foreground">
									订单状态
								</Label>
								<Badge :variant="getStatusBadgeVariant(order.status)">
									{{ getStatusText(order.status) }}
								</Badge>
							</div>

							<div class="space-y-2">
								<Label class="text-sm font-medium text-muted-foreground">
									店铺信息
								</Label>
								<div class="space-y-1">
									<p class="text-sm font-medium">{{ order.shop_name }}</p>
									<p class="text-xs text-muted-foreground">
										ID: {{ order.shop_id }}
									</p>
								</div>
							</div>

							<div class="space-y-2">
								<Label class="text-sm font-medium text-muted-foreground">
									发货日期
								</Label>
								<p class="text-sm">{{ formatDate(order.shipment_date) }}</p>
							</div>
						</div>
					</CardContent>
				</Card>

				<!-- 金额信息卡片 -->
				<Card>
					<CardHeader>
						<CardTitle class="flex items-center gap-2">
							<DollarSign class="h-4 w-4" />
							金额信息
						</CardTitle>
					</CardHeader>
					<CardContent>
						<div class="grid grid-cols-1 md:grid-cols-3 gap-6">
							<div class="text-center p-4 bg-green-50 rounded-lg">
								<div class="text-2xl font-bold text-green-600">
									¥{{ order.total_amount.toFixed(2) }}
								</div>
								<div class="text-sm text-muted-foreground">订单总额</div>
							</div>

							<div class="text-center p-4 bg-orange-50 rounded-lg">
								<div class="text-2xl font-bold text-orange-600">
									{{
										order.purchase_amount > 0
											? `¥${order.purchase_amount.toFixed(2)}`
											: "-"
									}}
								</div>
								<div class="text-sm text-muted-foreground">采购金额</div>
							</div>

							<div
								v-if="order.purchase_amount > 0"
								class="text-center p-4 rounded-lg"
								:class="getProfitBgClass(order)"
							>
								<div
									class="text-2xl font-bold"
									:class="getProfitTextClass(order)"
								>
									¥{{ (order.total_amount - order.purchase_amount).toFixed(2) }}
								</div>
								<div class="text-sm text-muted-foreground">预估利润</div>
							</div>
						</div>
					</CardContent>
				</Card>

				<!-- 时间信息卡片 -->
				<Card>
					<CardHeader>
						<CardTitle class="flex items-center gap-2">
							<Clock class="h-4 w-4" />
							时间信息
						</CardTitle>
					</CardHeader>
					<CardContent>
						<div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
							<div class="space-y-2">
								<Label class="text-sm font-medium text-muted-foreground">
									发货日期
								</Label>
								<p class="text-sm">{{ formatDate(order.shipment_date) }}</p>
							</div>

							<div class="space-y-2">
								<Label class="text-sm font-medium text-muted-foreground">
									交付日期
								</Label>
								<p class="text-sm">{{ formatDate(order.delivering_date) }}</p>
							</div>

							<div class="space-y-2">
								<Label class="text-sm font-medium text-muted-foreground">
									处理时间
								</Label>
								<p class="text-sm">{{ formatDate(order.in_process_at) }}</p>
							</div>

							<div class="space-y-2">
								<Label class="text-sm font-medium text-muted-foreground">
									包装时间
								</Label>
								<p class="text-sm">{{ formatDate(order.packaging_at) }}</p>
							</div>

							<div class="space-y-2">
								<Label class="text-sm font-medium text-muted-foreground">
									装袋时间
								</Label>
								<p class="text-sm">{{ formatDate(order.bagging_at) }}</p>
							</div>

							<div class="space-y-2">
								<Label class="text-sm font-medium text-muted-foreground">
									打印时间
								</Label>
								<p class="text-sm">{{ formatDate(order.printed_at) }}</p>
							</div>
						</div>
					</CardContent>
				</Card>

				<!-- 商品列表卡片 -->
				<Card>
					<CardHeader>
						<CardTitle class="flex items-center gap-2">
							<ShoppingCart class="h-4 w-4" />
							商品列表
							<Badge variant="secondary" class="ml-2">
								{{ order.items?.length || 0 }} 件商品
							</Badge>
						</CardTitle>
					</CardHeader>
					<CardContent>
						<div class="overflow-x-auto">
							<Table>
								<TableHeader>
									<TableRow>
										<TableHead>商品名称</TableHead>
										<TableHead>SKU</TableHead>
										<TableHead class="text-center">数量</TableHead>
										<TableHead class="text-right">单价</TableHead>
										<TableHead class="text-right">采购价</TableHead>
										<TableHead class="text-center">状态</TableHead>
									</TableRow>
								</TableHeader>
								<TableBody>
									<TableRow
										v-for="item in order.items"
										:key="item.id"
										class="hover:bg-muted/50"
									>
										<TableCell class="font-medium">
											{{ item.name }}
										</TableCell>
										<TableCell>
											<Badge variant="outline" class="font-mono text-xs">
												{{ item.external_sku }}
											</Badge>
										</TableCell>
										<TableCell class="text-center">
											<Badge variant="secondary">
												{{ item.quantity }}
											</Badge>
										</TableCell>
										<TableCell class="text-right font-medium">
											{{ item.currency }} {{ item.price.toFixed(2) }}
										</TableCell>
										<TableCell class="text-right">
											<span
												v-if="item.purchase_price > 0"
												class="font-medium text-orange-600"
											>
												¥{{ item.purchase_price.toFixed(2) }}
											</span>
											<span v-else class="text-muted-foreground">-</span>
										</TableCell>
										<TableCell class="text-center">
											<Badge variant="outline" class="text-xs">
												{{ item.status }}
											</Badge>
										</TableCell>
									</TableRow>
								</TableBody>
							</Table>
						</div>
					</CardContent>
				</Card>

				<!-- 采购信息卡片 -->
				<Card v-if="hasPurchaseInfo">
					<CardHeader>
						<CardTitle class="flex items-center gap-2">
							<Truck class="h-4 w-4" />
							采购信息
						</CardTitle>
					</CardHeader>
					<CardContent>
						<div class="space-y-6">
							<div
								v-for="item in order.items.filter((item) => item.purchase_from)"
								:key="item.id"
								class="border rounded-lg p-4 bg-muted/30"
							>
								<div class="font-medium text-lg mb-3 flex items-center gap-2">
									<Package class="h-4 w-4" />
									{{ item.name }}
								</div>
								<div
									class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4"
								>
									<div class="space-y-1">
										<Label class="text-xs text-muted-foreground">
											采购来源
										</Label>
										<p class="text-sm font-medium">{{ item.purchase_from }}</p>
									</div>

									<div class="space-y-1">
										<Label class="text-xs text-muted-foreground">
											采购单号
										</Label>
										<p class="text-sm font-mono">
											{{ item.purchase_order_id || "-" }}
										</p>
									</div>

									<div class="space-y-1">
										<Label class="text-xs text-muted-foreground">
											快递单号
										</Label>
										<p class="text-sm font-mono">
											{{ item.purchase_express_number || "-" }}
										</p>
									</div>

									<div class="space-y-1">
										<Label class="text-xs text-muted-foreground">
											支付时间
										</Label>
										<p class="text-sm">{{ formatDate(item.paid_at) }}</p>
									</div>

									<div class="space-y-1">
										<Label class="text-xs text-muted-foreground">
											收货时间
										</Label>
										<p class="text-sm">{{ formatDate(item.received_at) }}</p>
									</div>

									<div class="space-y-1">
										<Label class="text-xs text-muted-foreground">
											发货时间
										</Label>
										<p class="text-sm">{{ formatDate(item.shipped_at) }}</p>
									</div>
								</div>
							</div>
						</div>
					</CardContent>
				</Card>
			</div>

			<DialogFooter class="gap-2">
				<Button
					v-if="order && order.status === 'purchased'"
					@click="handlePrintOrder"
					class="bg-purple-600 hover:bg-purple-700"
				>
					<Printer class="h-4 w-4 mr-2" />
					打印运单
				</Button>

				<Button @click="handleSyncOrder" variant="outline">
					<RefreshCw class="h-4 w-4 mr-2" />
					同步订单
				</Button>

				<Button @click="() => emit('close')" variant="secondary"> 关闭 </Button>
			</DialogFooter>
		</DialogContent>
	</Dialog>
</template>

<script setup lang="ts">
import { computed } from "vue";
import { OrderApi } from "@/api/orders";
import type { IOrderResponse, TOrderStatus } from "@/types/api";

// shadcn-vue 组件导入
import {
	Dialog,
	DialogContent,
	DialogDescription,
	DialogFooter,
	DialogHeader,
	DialogTitle,
} from "@/components/ui/dialog";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Label } from "@/components/ui/label";
import {
	Table,
	TableBody,
	TableCell,
	TableHead,
	TableHeader,
	TableRow,
} from "@/components/ui/table";

// 图标导入
import {
	Package,
	Info,
	DollarSign,
	Clock,
	ShoppingCart,
	Truck,
	Printer,
	RefreshCw,
	Copy,
} from "lucide-vue-next";

// ============ Props 定义 ============

interface IProps {
	order: IOrderResponse | null;
}

const props = defineProps<IProps>();

// ============ Events 定义 ============

const emit = defineEmits<{
	close: [];
	refresh: [];
}>();

// ============ 计算属性 ============

/**
 * 是否有采购信息
 * <AUTHOR>
 */
const hasPurchaseInfo = computed(() => {
	return props.order?.items?.some((item) => item.purchase_from) || false;
});

// ============ 方法定义 ============

/**
 * 关闭弹窗
 * <AUTHOR>
 */
const handleClose = (open: boolean): void => {
	if (!open) {
		emit("close");
	}
};

/**
 * 获取状态显示文本
 * <AUTHOR>
 */
const getStatusText = (status: TOrderStatus): string => {
	const statusMap: Record<TOrderStatus, string> = {
		new: "未接单",
		accepted: "已接单",
		purchased: "已采购",
		shipped: "已打单",
	};
	return statusMap[status] || status;
};

/**
 * 获取状态Badge变体
 * <AUTHOR>
 */
const getStatusBadgeVariant = (
	status: TOrderStatus
): "default" | "secondary" | "destructive" | "outline" => {
	const variantMap: Record<
		TOrderStatus,
		"default" | "secondary" | "destructive" | "outline"
	> = {
		new: "outline",
		accepted: "secondary",
		purchased: "default",
		shipped: "default",
	};
	return variantMap[status] || "outline";
};

/**
 * 获取利润背景样式类
 * <AUTHOR>
 */
const getProfitBgClass = (order: IOrderResponse): string => {
	const profit = order.total_amount - order.purchase_amount;
	if (profit > 0) {
		return "bg-green-50";
	} else if (profit < 0) {
		return "bg-red-50";
	}
	return "bg-gray-50";
};

/**
 * 获取利润文字样式类
 * <AUTHOR>
 */
const getProfitTextClass = (order: IOrderResponse): string => {
	const profit = order.total_amount - order.purchase_amount;
	if (profit > 0) {
		return "text-green-600";
	} else if (profit < 0) {
		return "text-red-600";
	}
	return "text-gray-600";
};

/**
 * 格式化日期显示
 * <AUTHOR>
 */
const formatDate = (dateString: string): string => {
	if (!dateString) return "-";
	const date = new Date(dateString);
	return date.toLocaleString("zh-CN", {
		year: "numeric",
		month: "2-digit",
		day: "2-digit",
		hour: "2-digit",
		minute: "2-digit",
	});
};

/**
 * 复制到剪贴板
 * <AUTHOR>
 */
const copyToClipboard = async (text: string): Promise<void> => {
	try {
		await navigator.clipboard.writeText(text);
		console.log("已复制到剪贴板:", text);
	} catch (error) {
		console.error("复制失败:", error);
	}
};

/**
 * 打印订单
 * <AUTHOR>
 */
const handlePrintOrder = async (): Promise<void> => {
	if (!props.order) return;

	try {
		await OrderApi.updateOrder(props.order.id, { status: "shipped" });
		window.print();
		emit("refresh");
		emit("close");
		console.log("运单已打印，订单状态已更新");
	} catch (error) {
		console.error("打印运单失败:", error);
	}
};

/**
 * 同步订单
 * <AUTHOR>
 */
const handleSyncOrder = async (): Promise<void> => {
	if (!props.order) return;

	try {
		const result = await OrderApi.syncOrder(props.order.id);
		console.log("同步结果:", result);
		emit("refresh");
		console.log("订单同步完成");
	} catch (error) {
		console.error("同步订单失败:", error);
	}
};
</script>

<style scoped>
/**
 * 订单详情弹窗样式
 * <AUTHOR>
 * 使用官方 shadcn-vue 组件的样式，确保良好的视觉效果和用户体验
 */

/* 确保弹窗内容区域的滚动条样式 */
:deep(.overflow-y-auto) {
	scrollbar-width: thin;
	scrollbar-color: #cbd5e1 #f1f5f9;
}

:deep(.overflow-y-auto::-webkit-scrollbar) {
	width: 6px;
}

:deep(.overflow-y-auto::-webkit-scrollbar-track) {
	background: #f1f5f9;
	border-radius: 3px;
}

:deep(.overflow-y-auto::-webkit-scrollbar-thumb) {
	background: #cbd5e1;
	border-radius: 3px;
}

:deep(.overflow-y-auto::-webkit-scrollbar-thumb:hover) {
	background: #94a3b8;
}

/* 表格样式优化 */
:deep(.table-container) {
	border-radius: 0.5rem;
	overflow: hidden;
}

/* 卡片间距优化 */
.space-y-6 > * + * {
	margin-top: 1.5rem;
}

/* 响应式优化 */
@media (max-width: 768px) {
	:deep([data-slot="dialog-content"]) {
		max-width: 95vw;
		max-height: 95vh;
	}
}
</style>
