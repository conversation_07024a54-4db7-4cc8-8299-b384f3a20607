/** * 采购信息编辑弹窗组件 * <AUTHOR> * * 用于编辑订单商品的采购信息 */

<template>
	<div
		class="fixed inset-0 z-50 overflow-y-auto"
		aria-labelledby="modal-title"
		role="dialog"
		aria-modal="true"
	>
		<!-- 遮罩层 -->
		<div
			class="flex items-center justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0"
		>
			<div
				class="fixed inset-0 bg-gray-500 bg-opacity-75 transition-opacity"
				@click="$emit('close')"
			></div>

			<!-- 弹窗内容 -->
			<div
				class="inline-block align-bottom bg-white rounded-lg text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-4xl sm:w-full"
			>
				<!-- 头部 -->
				<div
					class="bg-white px-4 pt-5 pb-4 sm:p-6 sm:pb-4 border-b border-gray-200"
				>
					<div class="flex items-center justify-between">
						<h3
							class="text-lg leading-6 font-medium text-gray-900"
							id="modal-title"
						>
							编辑采购信息 - 订单号: {{ order?.posting_number }}
						</h3>
						<button
							@click="$emit('close')"
							class="bg-white rounded-md text-gray-400 hover:text-gray-600 focus:outline-none focus:ring-2 focus:ring-blue-500"
						>
							<span class="sr-only">关闭</span>
							<svg
								class="h-6 w-6"
								fill="none"
								viewBox="0 0 24 24"
								stroke="currentColor"
							>
								<path
									stroke-linecap="round"
									stroke-linejoin="round"
									stroke-width="2"
									d="M6 18L18 6M6 6l12 12"
								/>
							</svg>
						</button>
					</div>
				</div>

				<!-- 内容区域 -->
				<div class="bg-white px-4 py-5 sm:p-6">
					<form @submit.prevent="handleSubmit">
						<div v-if="order" class="space-y-6">
							<!-- 订单基本信息 -->
							<div class="bg-gray-50 rounded-lg p-4">
								<h4 class="text-lg font-medium text-gray-900 mb-3">订单信息</h4>
								<div class="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm">
									<div>
										<span class="text-gray-500">客户:</span>
										<span class="ml-2 text-gray-900">{{
											order.customer_name
										}}</span>
									</div>
									<div>
										<span class="text-gray-500">订单总额:</span>
										<span class="ml-2 font-semibold text-green-600"
											>¥{{ order.total_amount.toFixed(2) }}</span
										>
									</div>
									<div>
										<span class="text-gray-500">当前状态:</span>
										<span
											:class="getStatusBadgeClass(order.status)"
											class="ml-2 inline-flex px-2 py-1 text-xs font-semibold rounded-full"
										>
											{{ getStatusText(order.status) }}
										</span>
									</div>
								</div>
							</div>

							<!-- 商品采购信息表单 -->
							<div class="space-y-4">
								<h4 class="text-lg font-medium text-gray-900">商品采购信息</h4>

								<div
									v-for="(item, index) in purchaseItems"
									:key="item.id"
									class="border border-gray-200 rounded-lg p-4 space-y-4"
								>
									<!-- 商品基本信息 -->
									<div
										class="flex items-center justify-between bg-gray-50 p-3 rounded"
									>
										<div>
											<h5 class="font-medium text-gray-900">{{ item.name }}</h5>
											<div class="text-sm text-gray-500">
												SKU: {{ item.external_sku }} | 数量:
												{{ item.quantity }} | 单价: {{ item.currency }}
												{{ item.price.toFixed(2) }}
											</div>
										</div>
										<div class="text-right">
											<div class="text-sm text-gray-500">小计</div>
											<div class="font-medium text-gray-900">
												{{ item.currency }}
												{{ (item.price * item.quantity).toFixed(2) }}
											</div>
										</div>
									</div>

									<!-- 采购信息表单 -->
									<div class="grid grid-cols-1 md:grid-cols-2 gap-4">
										<div>
											<label
												class="block text-sm font-medium text-gray-700 mb-1"
												>采购价格 *</label
											>
											<div class="relative">
												<input
													v-model.number="item.purchase_price"
													type="number"
													step="0.01"
													min="0"
													required
													class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
													placeholder="请输入采购价格"
												/>
												<span
													class="absolute right-3 top-2 text-gray-500 text-sm"
													>¥</span
												>
											</div>
										</div>

										<div>
											<label
												class="block text-sm font-medium text-gray-700 mb-1"
												>采购来源</label
											>
											<input
												v-model="item.purchase_from"
												type="text"
												class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
												placeholder="如：淘宝、1688、京东等"
											/>
										</div>

										<div>
											<label
												class="block text-sm font-medium text-gray-700 mb-1"
												>采购订单号</label
											>
											<input
												v-model="item.purchase_order_id"
												type="text"
												class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
												placeholder="采购平台的订单号"
											/>
										</div>

										<div>
											<label
												class="block text-sm font-medium text-gray-700 mb-1"
												>快递单号</label
											>
											<input
												v-model="item.purchase_express_number"
												type="text"
												class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
												placeholder="物流快递单号"
											/>
										</div>
									</div>

									<!-- 利润显示 -->
									<div
										v-if="item.purchase_price > 0"
										class="bg-blue-50 p-3 rounded"
									>
										<div class="flex justify-between items-center">
											<span class="text-sm text-gray-600">单品利润:</span>
											<span
												class="font-medium"
												:class="
													getProfitClass(
														item.price * item.quantity - item.purchase_price
													)
												"
											>
												¥{{
													(
														item.price * item.quantity -
														item.purchase_price
													).toFixed(2)
												}}
											</span>
										</div>
									</div>
								</div>
							</div>

							<!-- 总计信息 -->
							<div class="bg-gray-50 rounded-lg p-4">
								<div class="grid grid-cols-1 md:grid-cols-3 gap-4">
									<div class="text-center">
										<div class="text-sm text-gray-500">采购总金额</div>
										<div class="text-lg font-semibold text-red-600">
											¥{{ totalPurchaseAmount.toFixed(2) }}
										</div>
									</div>
									<div class="text-center">
										<div class="text-sm text-gray-500">订单总金额</div>
										<div class="text-lg font-semibold text-green-600">
											¥{{ order.total_amount.toFixed(2) }}
										</div>
									</div>
									<div class="text-center">
										<div class="text-sm text-gray-500">预估总利润</div>
										<div
											class="text-lg font-semibold"
											:class="
												getProfitClass(order.total_amount - totalPurchaseAmount)
											"
										>
											¥{{
												(order.total_amount - totalPurchaseAmount).toFixed(2)
											}}
										</div>
									</div>
								</div>
							</div>
						</div>
					</form>
				</div>

				<!-- 底部按钮 -->
				<div class="bg-gray-50 px-4 py-3 sm:px-6 sm:flex sm:flex-row-reverse">
					<button
						@click="handleSubmit"
						:disabled="isSubmitting || !isFormValid"
						class="w-full inline-flex justify-center rounded-md border border-transparent shadow-sm px-4 py-2 bg-blue-600 text-base font-medium text-white hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 disabled:bg-gray-400 disabled:cursor-not-allowed sm:ml-3 sm:w-auto sm:text-sm"
					>
						{{ isSubmitting ? "保存中..." : "保存采购信息" }}
					</button>
					<button
						@click="$emit('close')"
						:disabled="isSubmitting"
						class="mt-3 w-full inline-flex justify-center rounded-md border border-gray-300 shadow-sm px-4 py-2 bg-white text-base font-medium text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-blue-500 disabled:opacity-50 sm:mt-0 sm:w-auto sm:text-sm"
					>
						取消
					</button>
				</div>
			</div>
		</div>
	</div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from "vue";
import { OrderApi } from "@/api/orders";
import type {
	IOrderResponse,
	IOrderItemResponse,
	TOrderStatus,
} from "@/types/api";

// ============ Props 定义 ============

interface IProps {
	order: IOrderResponse | null;
}

const props = defineProps<IProps>();

// ============ Events 定义 ============

const emit = defineEmits<{
	close: [];
	refresh: [];
}>();

// ============ 响应式数据 ============

// 表单状态
const isSubmitting = ref<boolean>(false);

// 采购商品信息列表
const purchaseItems = ref<IOrderItemResponse[]>([]);

// ============ 计算属性 ============

/**
 * 采购总金额
 * <AUTHOR>
 */
const totalPurchaseAmount = computed(() => {
	return purchaseItems.value.reduce(
		(total, item) => total + (item.purchase_price || 0),
		0
	);
});

/**
 * 表单是否有效
 * <AUTHOR>
 */
const isFormValid = computed(() => {
	return purchaseItems.value.every((item) => item.purchase_price > 0);
});

// ============ 生命周期 ============

onMounted(() => {
	if (props.order) {
		// 深拷贝商品列表，避免直接修改props
		purchaseItems.value = JSON.parse(JSON.stringify(props.order.items));
	}
});

// ============ 方法定义 ============

/**
 * 获取状态显示文本
 * <AUTHOR>
 */
const getStatusText = (status: TOrderStatus): string => {
	const statusMap: Record<TOrderStatus, string> = {
		new: "未接单",
		accepted: "已接单",
		purchased: "已采购",
		shipped: "已打单",
	};
	return statusMap[status] || status;
};

/**
 * 获取状态样式类
 * <AUTHOR>
 */
const getStatusBadgeClass = (status: TOrderStatus): string => {
	const classMap: Record<TOrderStatus, string> = {
		new: "bg-yellow-100 text-yellow-800",
		accepted: "bg-blue-100 text-blue-800",
		purchased: "bg-green-100 text-green-800",
		shipped: "bg-purple-100 text-purple-800",
	};
	return classMap[status] || "bg-gray-100 text-gray-800";
};

/**
 * 获取利润颜色样式
 * <AUTHOR>
 */
const getProfitClass = (profit: number): string => {
	if (profit > 0) return "text-green-600";
	if (profit < 0) return "text-red-600";
	return "text-gray-600";
};

/**
 * 提交表单
 * <AUTHOR>
 */
const handleSubmit = async (): Promise<void> => {
	if (!props.order || !isFormValid.value || isSubmitting.value) return;

	try {
		isSubmitting.value = true;

		// 更新每个商品的采购信息
		const updatePromises = purchaseItems.value.map((item) =>
			OrderApi.updateOrderItem(props.order!.id, item.id, {
				purchase_price: item.purchase_price,
				purchase_from: item.purchase_from || "",
				purchase_order_id: item.purchase_order_id || "",
				purchase_express_number: item.purchase_express_number || "",
			})
		);

		await Promise.all(updatePromises);

		// 更新订单状态为已采购，并设置采购总金额
		await OrderApi.updateOrder(props.order.id, {
			status: "purchased",
			purchase_amount: totalPurchaseAmount.value,
		});

		emit("refresh");
		emit("close");
		alert("采购信息保存成功，订单状态已更新为已采购");
	} catch (error) {
		console.error("保存采购信息失败:", error);
		alert("保存采购信息失败，请稍后重试");
	} finally {
		isSubmitting.value = false;
	}
};
</script>

<style scoped>
/* 表单样式 */
.form-section {
	@reference border border-gray-200 rounded-lg p-4 space-y-4;
}

.form-row {
	@reference grid grid-cols-1 md:grid-cols-2 gap-4;
}

.form-field {
	@reference space-y-1;
}

.form-label {
	@reference block text-sm font-medium text-gray-700;
}

.form-input {
	@reference w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500;
}

.form-input:invalid {
	@reference border-red-300 focus:ring-red-500;
}

/* 动画样式 */
.fade-enter-active,
.fade-leave-active {
	transition: opacity 0.3s;
}

.fade-enter-from,
.fade-leave-to {
	opacity: 0;
}
</style>
