# 组件使用说明

## PageContainer 组件

### 概述

`PageContainer` 是一个通用的页面容器组件，用于确保页面内容不会超出父组件的宽度，防止出现横向滚动条，同时保留纵向滚动功能。所有页面组件都应该使用这个容器来包装内容。

### 功能特性

- ✅ **宽度控制**: 确保内容不会超出父容器宽度
- ✅ **纵向滚动**: 保留完整的纵向滚动功能
- ✅ **横向限制**: 只隐藏横向溢出，避免水平滚动条
- ✅ **响应式布局**: 支持多种屏幕尺寸
- ✅ **溢出处理**: 自动处理表格、图片等可能导致溢出的元素
- ✅ **自定义配置**: 支持padding和滚动配置

### 基本用法

```vue
<template>
  <PageContainer>
    <!-- 您的页面内容 -->
    <div>页面内容</div>
  </PageContainer>
</template>

<script setup lang="ts">
import PageContainer from '@/components/PageContainer.vue'
</script>
```

### 高级配置

```vue
<template>
  <!-- 无内边距的页面容器 -->
  <PageContainer :no-padding="true">
    <div>无padding的内容</div>
  </PageContainer>

  <!-- 禁用垂直滚动的页面容器 -->
  <PageContainer :scrollable="false">
    <div>不可滚动的内容</div>
  </PageContainer>

  <!-- 自定义样式类 -->
  <PageContainer class="bg-gray-50">
    <div>带背景色的内容</div>
  </PageContainer>
</template>
```

### Props

| 属性名 | 类型 | 默认值 | 说明 |
|--------|------|--------|------|
| `class` | `string` | - | 自定义CSS类名 |
| `noPadding` | `boolean` | `false` | 是否移除内边距（仍会保留最小padding） |
| `scrollable` | `boolean` | `true` | 是否允许垂直滚动 |

### 滚动机制说明

#### 纵向滚动
- **默认启用**: 当内容超出容器高度时，自动出现纵向滚动条
- **可配置**: 通过 `scrollable` 属性控制是否允许滚动
- **样式优化**: 自定义滚动条样式，更美观

#### 横向限制
- **严格控制**: 所有内容都不会超出容器宽度
- **自动处理**: 表格、图片等元素会自动适应容器宽度
- **无滚动条**: 永远不会出现横向滚动条

### 自动处理的元素

PageContainer 会自动处理以下可能导致宽度溢出的元素：

#### 表格
```vue
<PageContainer>
  <!-- 表格会自动添加溢出滚动和固定布局 -->
  <table>
    <thead>
      <tr>
        <th>列1</th>
        <th>列2</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td>数据1</td>
        <td>数据2</td>
      </tr>
    </tbody>
  </table>
</PageContainer>
```

#### 图片
```vue
<PageContainer>
  <!-- 图片会自动限制最大宽度 -->
  <img src="/large-image.jpg" alt="大图片" />
</PageContainer>
```

#### 代码块
```vue
<PageContainer>
  <!-- 代码块会自动添加水平滚动 -->
  <pre><code>很长的代码内容...</code></pre>
</PageContainer>
```

### 响应式断点

| 断点 | 屏幕宽度 | 优化内容 |
|------|----------|----------|
| 移动端 | ≤ 768px | 减少表格列宽，调整字体大小 |
| 超小屏 | ≤ 480px | 进一步优化表格显示 |

### 最佳实践

#### ✅ 推荐做法

```vue
<template>
  <PageContainer>
    <!-- 使用Tailwind CSS的响应式类 -->
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
      <div class="card">内容1</div>
      <div class="card">内容2</div>
      <div class="card">内容3</div>
    </div>
    
    <!-- 长内容会自动滚动 -->
    <div class="space-y-4">
      <div v-for="item in 100" :key="item" class="p-4 border rounded">
        项目 {{ item }}
      </div>
    </div>
  </PageContainer>
</template>
```

#### ❌ 避免的做法

```vue
<template>
  <!-- 不要在PageContainer外部添加固定宽度 -->
  <div style="width: 1200px;">
    <PageContainer>
      <div>内容</div>
    </PageContainer>
  </div>
  
  <!-- 不要强制设置overflow: hidden -->
  <PageContainer class="overflow-hidden">
    <div>这会阻止滚动</div>
  </PageContainer>
</template>
```

### 与现有组件集成

#### 与shadcn-vue组件配合使用

```vue
<template>
  <PageContainer>
    <!-- Card组件会自动适应容器宽度 -->
    <Card>
      <CardHeader>
        <CardTitle>标题</CardTitle>
      </CardHeader>
      <CardContent>
        内容
      </CardContent>
    </Card>
  </PageContainer>
</template>
```

#### 与图表组件配合使用

```vue
<template>
  <PageContainer>
    <!-- G2Plot图表会自动适应容器宽度 -->
    <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
      <div class="chart-container">
        <div id="chart1" class="w-full h-[300px]"></div>
      </div>
      <div class="chart-container">
        <div id="chart2" class="w-full h-[300px]"></div>
      </div>
    </div>
  </PageContainer>
</template>
```

### 滚动条样式

PageContainer 使用了自定义的滚动条样式：

- **宽度**: 6px（细滚动条）
- **颜色**: 半透明灰色
- **交互**: 悬停时颜色加深
- **兼容性**: 支持 Webkit 和 Firefox

### 故障排除

#### 问题：内容仍然超出容器宽度

**解决方案：**
1. 检查是否有元素使用了固定宽度（如 `width: 1200px`）
2. 确认是否有表格没有设置 `table-layout: fixed`
3. 检查是否有绝对定位的元素

#### 问题：纵向滚动不工作

**解决方案：**
1. 确认 `scrollable` 属性设置为 `true`（默认值）
2. 检查是否有父容器设置了 `overflow: hidden`
3. 确认容器有明确的高度限制

#### 问题：图表不能正确调整大小

**解决方案：**
1. 确保图表容器使用了 `w-full` 类
2. 为图表设置 `autoFit: true` 配置
3. 在容器大小变化时调用图表的 `render()` 方法

### 架构说明

整个滚动系统采用多层防护机制：

1. **全局层面**: `index.css` 设置基础的溢出控制
2. **布局层面**: `App.vue` 确保主布局正确处理滚动
3. **组件层面**: `PageContainer` 提供页面级别的滚动控制
4. **内容层面**: 自动处理各种可能导致溢出的元素

### 更新日志

- **v1.1.0**: 修复纵向滚动问题，完善滚动条样式
- **v1.0.0**: 初始版本，基础宽度控制功能

---

**维护者**: 前端开发团队  
**最后更新**: 2024年  
**相关文件**: `frontend/src/components/PageContainer.vue` 