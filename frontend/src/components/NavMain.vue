<script setup lang="ts">
/**
 * 主导航组件
 * <AUTHOR>
 *
 * 渲染侧边栏主导航菜单，首页单独展示，其他模块通过折叠菜单展示子路由
 * 支持可折叠的菜单项和直接导航项
 */

import { ChevronRight, type LucideIcon } from "lucide-vue-next";
import {
	Collapsible,
	CollapsibleContent,
	CollapsibleTrigger,
} from "@/components/ui/collapsible";
import {
	SidebarGroup,
	SidebarGroupLabel,
	SidebarMenu,
	SidebarMenuButton,
	SidebarMenuItem,
	SidebarMenuSub,
	SidebarMenuSubButton,
	SidebarMenuSubItem,
} from "@/components/ui/sidebar";
import { onMounted } from "vue";

const props = defineProps<{
	items: {
		title: string;
		url: string;
		icon?: LucideIcon;
		isActive?: boolean;
		items?: {
			title: string;
			url: string;
		}[];
	}[];
}>();

onMounted(() => {
	console.log(props.items);
});
</script>

<template>
	<SidebarGroup>
		<SidebarGroupLabel>导航菜单</SidebarGroupLabel>
		<SidebarMenu>
			<template v-for="item in items" :key="item.title">
				<!-- 有子菜单的导航项（使用Collapsible） -->
				<Collapsible
					v-if="item.items && item.items.length > 0"
					as-child
					:default-open="item.isActive"
					class="group/collapsible"
				>
					<SidebarMenuItem>
						<CollapsibleTrigger as-child>
							<SidebarMenuButton :tooltip="item.title">
								<component :is="item.icon" v-if="item.icon" />
								<span>{{ item.title }}</span>
								<ChevronRight
									class="ml-auto transition-transform duration-200 group-data-[state=open]/collapsible:rotate-90"
								/>
							</SidebarMenuButton>
						</CollapsibleTrigger>
						<CollapsibleContent>
							<SidebarMenuSub>
								<SidebarMenuSubItem
									v-for="subItem in item.items"
									:key="subItem.title"
								>
									<SidebarMenuSubButton as-child>
										<router-link :to="subItem.url">
											<span>{{ subItem.title }}</span>
										</router-link>
									</SidebarMenuSubButton>
								</SidebarMenuSubItem>
							</SidebarMenuSub>
						</CollapsibleContent>
					</SidebarMenuItem>
				</Collapsible>

				<!-- 没有子菜单的导航项（如首页，直接可点击） -->
				<SidebarMenuItem v-else>
					<SidebarMenuButton
						as-child
						:tooltip="item.title"
						:data-active="item.isActive"
					>
						<router-link :to="item.url">
							<component :is="item.icon" v-if="item.icon" />
							<span>{{ item.title }}</span>
						</router-link>
					</SidebarMenuButton>
				</SidebarMenuItem>
			</template>
		</SidebarMenu>
	</SidebarGroup>
</template>
