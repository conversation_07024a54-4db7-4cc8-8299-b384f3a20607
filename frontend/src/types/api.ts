/**
 * API相关的TypeScript类型定义
 * 基于Swagger文档 http://localhost:8080/swagger/doc.json 生成
 * <AUTHOR>
 */

// ============ 基础响应类型 ============

/**
 * API响应基础类型
 * <AUTHOR>
 */
export interface IApiResponse<T = any> {
	code: number;
	message: string;
	data: T;
}

/**
 * API错误响应类型
 * <AUTHOR>
 */
export interface IApiErrorResponse {
	code: number;
	message: string;
	errors?: IFieldError[];
}

/**
 * 字段错误类型
 * <AUTHOR>
 */
export interface IFieldError {
	field: string;
	message: string;
}

/**
 * 分页响应类型
 * <AUTHOR>
 */
export interface IPageResponse<T = any> {
	data: T[];
	page: number;
	page_size: number;
	total: number;
}

// ============ 认证相关类型 ============

/**
 * 登录请求参数
 * <AUTHOR>
 */
export interface ILoginRequest {
	username: string;
	password: string;
}

/**
 * 重置密码请求参数
 * <AUTHOR>
 */
export interface IResetPasswordRequest {
	mobile: string;
	password: string;
}

/**
 * Token响应类型
 * <AUTHOR>
 */
export interface ITokenResponse {
	token: string;
	user: IUserResponse;
}

// ============ 用户相关类型 ============

/**
 * 用户响应类型
 * <AUTHOR>
 */
export interface IUserResponse {
	id: number;
	username: string;
	name: string;
	mobile: string;
	role: string;
}

/**
 * 用户创建请求
 * <AUTHOR>
 */
export interface IUserCreateRequest {
	username: string;
	name: string;
	mobile: string;
	password: string;
	role?: "admin" | "user";
}

/**
 * 用户更新请求
 * <AUTHOR>
 */
export interface IUserUpdateRequest {
	name?: string;
	mobile?: string;
	password?: string;
	role?: "admin" | "user";
}

// ============ 店铺相关类型 ============

/**
 * 店铺响应类型
 * <AUTHOR>
 */
export interface IShopResponse {
	id: number;
	name: string;
	platform: string;
	client_id: number;
	owner_id: number;
	owner_name: string;
	is_active: boolean;
}

/**
 * 店铺创建请求
 * <AUTHOR>
 */
export interface IShopCreateRequest {
	name: string;
	platform: "ozon";
	client_id: number;
	api_key: string;
	owner_id?: number;
}

/**
 * 店铺更新请求
 * <AUTHOR>
 */
export interface IShopUpdateRequest {
	name?: string;
	platform?: "ozon";
	client_id?: number;
	api_key?: string;
	owner_id?: number;
	is_active?: boolean;
}

// ============ 商品相关类型 ============

/**
 * 商品响应类型
 * <AUTHOR>
 */
export interface IProductResponse {
	id: number;
	sku: number;
	name: string;
	external_id: string;
	offer_id: string;
	image_url: string;
	price: number;
	old_price: number;
	market_price: number;
	min_price: number;
	currency: string;
	stock: number;
	shop_id: number;
	shop_name: string;
	is_active: boolean;
	original_data: string;
	created_at: string;
	updated_at: string;
}

/**
 * 商品更新请求
 * <AUTHOR>
 */
export interface IProductUpdateRequest {
	name?: string;
	offer_id?: string;
	image_url?: string;
	price?: number;
	old_price?: number;
	market_price?: number;
	min_price?: number;
	currency?: string;
	sku?: number;
	is_active?: boolean;
}

/**
 * 商品查询参数
 * <AUTHOR>
 */
export interface IProductQueryParams {
	shop_id?: number;
	sku?: number;
	keyword?: string;
	is_active?: boolean;
	page?: number;
	page_size?: number;
}

// ============ 订单相关类型 ============

/**
 * 订单状态枚举
 * <AUTHOR>
 */
export type TOrderStatus = "new" | "accepted" | "purchased" | "shipped";

/**
 * 订单响应类型
 * <AUTHOR>
 */
export interface IOrderResponse {
	id: number;
	external_id: number;
	posting_number: string;
	customer_name: string;
	status: TOrderStatus;
	total_amount: number;
	purchase_amount: number;
	shop_id: number;
	shop_name: string;
	shipment_date: string;
	delivering_date: string;
	in_process_at: string;
	packaging_at: string;
	bagging_at: string;
	printed_at: string;
	items: IOrderItemResponse[];
}

/**
 * 订单项响应类型
 * <AUTHOR>
 */
export interface IOrderItemResponse {
	id: number;
	order_id: number;
	product_id: number;
	external_sku: number;
	name: string;
	quantity: number;
	price: number;
	currency: string;
	status: string;
	purchase_price: number;
	purchase_from: string;
	purchase_order_id: string;
	purchase_express_number: string;
	paid_at: string;
	received_at: string;
	shipped_at: string;
	created_at: string;
}

/**
 * 订单查询参数
 * <AUTHOR>
 */
export interface IOrderQueryParams {
	shop_id?: number;
	status?: TOrderStatus;
	start_date?: string;
	end_date?: string;
	page?: number;
	page_size?: number;
}

/**
 * 订单更新请求
 * <AUTHOR>
 */
export interface IOrderUpdateRequest {
	status?: TOrderStatus;
	purchase_amount?: number;
}

/**
 * 订单项更新请求
 * <AUTHOR>
 */
export interface IOrderItemUpdateRequest {
	purchase_price: number;
	purchase_from?: string;
	purchase_order_id?: string;
	purchase_express_number?: string;
}

// ============ 同步响应类型 ============

/**
 * 订单同步响应类型
 * <AUTHOR>
 */
export interface IOrderSyncResponse {
	count: number;
}

/**
 * 批量同步响应类型
 * <AUTHOR>
 */
export interface IBatchSyncResponse {
	total_shops: number;
	success_shops: number;
	failed_shops: number;
	total_orders: number;
	failed_shop_ids: number[];
}

/**
 * 商品同步响应类型
 * <AUTHOR>
 */
export interface IProductSyncResponse {
	[key: string]: number;
}

// ============ 查询参数类型 ============

/**
 * 店铺查询参数
 * <AUTHOR>
 */
export interface IShopQueryParams {
	only_mine?: boolean;
}

/**
 * 基础分页参数
 * <AUTHOR>
 */
export interface IPaginationParams {
	page?: number;
	page_size?: number;
}

// ============ 废弃的类型（保持兼容） ============

/**
 * @deprecated 请使用新的类型定义
 */
export interface IPaginationResponse<T> {
	data: T[];
	total: number;
	page: number;
	page_size: number;
}

/**
 * @deprecated 请使用 TOrderStatus
 */
export enum EOrderStatus {
	PENDING = "new",
	ACCEPTED = "accepted",
	PURCHASED = "purchased",
	PRINTED = "shipped",
}

/**
 * @deprecated 请使用 IOrderResponse
 */
export interface IOrder extends IOrderResponse {}

/**
 * @deprecated 请使用 IProductResponse
 */
export interface IProduct extends IProductResponse {}

/**
 * @deprecated 请使用 IUserResponse
 */
export interface IUser extends IUserResponse {}

/**
 * @deprecated 请使用 ILoginRequest
 */
export interface ILoginParams extends ILoginRequest {}

/**
 * @deprecated 请使用 ITokenResponse
 */
export interface ILoginResponse extends ITokenResponse {
	refreshToken?: string;
}
