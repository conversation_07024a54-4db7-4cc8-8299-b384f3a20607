/**
 * 面包屑导航相关类型定义
 * <AUTHOR>
 *
 * 定义面包屑组件中使用的数据类型
 */

/** 面包屑项目接口 */
export interface IBreadcrumbItem {
	/** 显示名称 */
	name: string;
	/** 路由路径 */
	path: string;
	/** 是否为最后一项 */
	isLast: boolean;
}

/** 路由元信息扩展 */
declare module "vue-router" {
	interface RouteMeta {
		/** 页面标题 */
		title?: string;
		/** 图标名称 */
		icon?: string;
		/** 是否在菜单中显示 */
		isMenu?: boolean;
		/** 是否需要认证 */
		requiresAuth?: boolean;
		/** 布局类型 */
		layout?: "auth" | "main";
		/** 角色权限 */
		roles?: string | string[];
		/** 具体权限 */
		permissions?: string | string[];
	}
}
