import { Component } from "vue";
import { RouteRecordRaw } from "vue-router";

// 菜单项类型
export interface MenuItem {
	title: string;
	path: string;
	icon?: string | Component | undefined;
	children?: MenuItem[];
}

// 根据路由生成菜单
export const generateMenuFromRoutes = (
	routes: readonly RouteRecordRaw[]
): MenuItem[] => {
	return routes
		.filter((route) => route.meta?.isMenu)
		.map((route) => {
			const menuItem: MenuItem = {
				title: (route.meta?.title as string) || "",
				path: route.path,
				icon: route.meta?.icon,
			};

			if (route.children && route.children.length > 0) {
				const children = route.children
					.filter((child) => child.meta?.isMenu)
					.map((child) => ({
						title: (child.meta?.title as string) || "",
						path: `${route.path}/${child.path}`,
						icon: child.meta?.icon,
					}));

				if (children.length > 0) {
					menuItem.children = children;
				}
			}

			return menuItem;
		});
};
