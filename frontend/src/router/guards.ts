/**
 * 路由守卫
 * 处理认证逻辑和页面权限控制
 * <AUTHOR>
 */

import type { NavigationGuardNext, RouteLocationNormalized } from "vue-router";
import { useAuth } from "../stores/auth.js";
import { JwtUtils, TokenStorage } from "../utils/jwt.js";

/**
 * 需要认证的路由列表
 */
const AUTH_REQUIRED_ROUTES = [
	"/home",
	"/order",
	"/product",
	"/inventory",
	"/settings",
	"/profile",
];

/**
 * 公开访问的路由列表
 */
const PUBLIC_ROUTES = ["/login", "/forgot-password", "/reset-password", "/404"];

/**
 * 判断路由是否需要认证
 */
const requiresAuth = (path: string): boolean => {
	// 检查是否为需要认证的路由
	const needsAuth = AUTH_REQUIRED_ROUTES.some((route) =>
		path.startsWith(route)
	);

	// 如果路由元信息中明确设置了requiresAuth，以元信息为准
	return needsAuth;
};

/**
 * 判断路由是否为公开路由
 */
const isPublicRoute = (path: string): boolean => {
	return PUBLIC_ROUTES.includes(path) || path === "/";
};

/**
 * 快速JWT验证
 * 不依赖Vue组合式API，可以在路由守卫中使用
 */
const quickJwtValidation = (): {
	hasValidToken: boolean;
	tokenInfo: any;
} => {
	const token = TokenStorage.getToken();

	if (!token) {
		return { hasValidToken: false, tokenInfo: null };
	}

	const isValid = JwtUtils.isTokenValid(token);
	const tokenInfo = isValid ? TokenStorage.getTokenInfo() : null;

	return { hasValidToken: isValid, tokenInfo };
};

/**
 * 认证守卫
 * 检查用户是否已登录，验证JWT token有效性
 */
export const authGuard = async (
	to: RouteLocationNormalized,
	from: RouteLocationNormalized,
	next: NavigationGuardNext
) => {
	const auth = useAuth();

	try {
		console.log(`路由守卫: ${from.path} -> ${to.path}`);

		// 快速JWT验证
		const { hasValidToken, tokenInfo } = quickJwtValidation();

		// 如果是登录页面
		if (to.path === "/login") {
			// 如果已有有效token，重定向到目标页面或首页
			if (hasValidToken && auth.isAuthenticated.value) {
				const redirectPath = (to.query.redirect as string) || "/home";
				console.log(`用户已登录，重定向到: ${redirectPath}`);
				next(redirectPath);
				return;
			}
			// 没有有效token，允许访问登录页
			next();
			return;
		}

		// 如果是公开路由，直接放行
		if (isPublicRoute(to.path)) {
			next();
			return;
		}

		// 检查路由是否需要认证
		if (!requiresAuth(to.path) && !to.meta?.requiresAuth) {
			next();
			return;
		}

		// 需要认证的路由，检查JWT token
		if (!hasValidToken) {
			console.log("Token无效或不存在，重定向到登录页");
			// 清除无效的认证信息
			auth.clearAuth();

			// 保存用户想要访问的页面，登录后重定向
			const redirect = to.fullPath !== "/login" ? to.fullPath : undefined;
			next({
				path: "/login",
				query: redirect ? { redirect } : undefined,
			});
			return;
		}

		// 检查token是否即将过期，尝试刷新
		if (tokenInfo && JwtUtils.needsRefresh(TokenStorage.getToken()!)) {
			console.log("Token即将过期，尝试刷新");
			try {
				await auth.checkTokenRefresh();
			} catch (error) {
				console.warn("Token刷新失败，继续使用当前token:", error);
			}
		}

		// 如果用户信息不存在，尝试初始化
		if (!auth.user.value) {
			console.log("用户信息不存在，尝试初始化认证状态");
			const initSuccess = await auth.initAuth();
			if (!initSuccess) {
				console.log("认证状态初始化失败，重定向到登录页");
				const redirect = to.fullPath !== "/login" ? to.fullPath : undefined;
				next({
					path: "/login",
					query: redirect ? { redirect } : undefined,
				});
				return;
			}
		}

		// 检查路由权限
		if (await checkRoutePermission(to, auth)) {
			console.log(`权限检查通过，允许访问: ${to.path}`);
			next();
		} else {
			console.log(`权限不足，重定向到首页: ${to.path}`);
			// 权限不足，重定向到首页
			next("/home");
		}
	} catch (error) {
		console.error("路由守卫错误:", error);
		// 发生错误时，清除认证状态并重定向到登录页
		auth.clearAuth();
		next("/login");
	}
};

/**
 * 检查路由权限
 */
const checkRoutePermission = async (
	route: RouteLocationNormalized,
	auth: ReturnType<typeof useAuth>
): Promise<boolean> => {
	// 如果路由元信息中没有权限要求，默认允许访问
	if (!route.meta?.permissions && !route.meta?.roles) {
		return true;
	}

	// 检查角色权限
	if (route.meta.roles) {
		const requiredRoles = Array.isArray(route.meta.roles)
			? route.meta.roles
			: [route.meta.roles];

		const hasRole = requiredRoles.some((role: string) => auth.hasRole(role));
		if (!hasRole) {
			console.log(
				`角色权限不足: 需要 ${requiredRoles.join("或")}, 当前 ${
					auth.userRole.value
				}`
			);
			return false;
		}
	}

	// 检查具体权限
	if (route.meta.permissions) {
		const requiredPermissions = Array.isArray(route.meta.permissions)
			? route.meta.permissions
			: [route.meta.permissions];

		const hasPermission = requiredPermissions.some((permission: string) =>
			auth.hasPermission(permission)
		);
		if (!hasPermission) {
			console.log(`权限不足: 需要 ${requiredPermissions.join("或")}`);
			return false;
		}
	}

	// 根据路由path进行基本权限检查（兼容旧逻辑）
	const path = route.path;
	const routePermissions: Record<string, string[]> = {
		"/order": ["order:view", "order:manage"],
		"/product": ["product:view", "product:manage"],
		"/inventory": ["inventory:view", "inventory:manage"],
		"/settings": ["user:manage"],
		"/users": ["user:manage"],
		"/platforms": ["platform:manage"],
	};

	// 检查用户是否有访问权限
	for (const [routePath, permissions] of Object.entries(routePermissions)) {
		if (path.startsWith(routePath)) {
			// 用户只要有其中一个权限就可以访问
			const hasAccess = permissions.some((permission) =>
				auth.hasPermission(permission)
			);
			if (!hasAccess) {
				console.log(
					`路径权限不足: ${path}, 需要权限: ${permissions.join("或")}`
				);
				return false;
			}
		}
	}

	// 默认允许访问（如仪表板等通用页面）
	return true;
};

/**
 * 页面标题守卫
 * 根据路由设置页面标题
 */
export const titleGuard = (
	to: RouteLocationNormalized,
	from: RouteLocationNormalized,
	next: NavigationGuardNext
) => {
	// 优先使用路由元信息中的标题
	let title = to.meta?.title as string;

	// 如果没有元信息标题，使用路径映射
	if (!title) {
		const titleMap: Record<string, string> = {
			"/": "首页",
			"/home": "首页",
			"/login": "登录",
			"/order": "订单管理",
			"/product": "商品管理",
			"/inventory": "库存管理",
			"/settings": "系统设置",
			"/profile": "个人资料",
		};
		title = titleMap[to.path] || "9Wings ERP系统";
	}

	// 设置页面标题
	document.title = `${title} - 9Wings ERP系统`;

	next();
};

/**
 * 路由加载状态守卫
 * 在路由切换时，显示/隐藏加载动画
 */
export const loadingGuard = (
	to: RouteLocationNormalized,
	from: RouteLocationNormalized,
	next: NavigationGuardNext
) => {
	const auth = useAuth();
	auth.setRouteLoading(true);
	next();
};

/**
 * 导航后置钩子
 * 在路由完成后执行
 */
export const afterEachHook = (
	to: RouteLocationNormalized,
	from: RouteLocationNormalized
) => {
	// 导航完成后，隐藏加载动画
	const auth = useAuth();
	auth.setRouteLoading(false);

	// 在这里可以添加页面浏览量统计等逻辑
	console.log(`导航完成: ${from.path} -> ${to.path}`);

	// 滚动到页面顶部（可选）
	window.scrollTo(0, 0);

	// 记录路由访问日志（开发环境）
	if (process.env.NODE_ENV === "development") {
		console.log(`路由变更完成: ${from.path} -> ${to.path}`);

		// 输出token状态（仅开发环境）
		const tokenInfo = TokenStorage.getTokenInfo();
		if (tokenInfo.hasToken) {
			console.log("Token状态:", {
				isValid: tokenInfo.isValid,
				user: tokenInfo.user?.username,
				remainingTime: `${Math.floor(tokenInfo.remainingTime / 60)}分钟`,
			});
		}
	}
};

/**
 * 错误处理守卫
 */
export const errorGuard = (
	error: Error,
	to: RouteLocationNormalized,
	from: RouteLocationNormalized
) => {
	console.error("路由错误:", error);

	// 如果是认证相关错误，清除状态并重定向到登录页
	if (error.message.includes("认证") || error.message.includes("token")) {
		const auth = useAuth();
		auth.clearAuth();
		return "/login";
	}

	// 其他错误重定向到404页面
	return "/404";
};

/**
 * 导出路由守卫配置
 */
export const routeGuards = {
	beforeEach: [authGuard, titleGuard, loadingGuard],
	afterEach: [afterEachHook],
	onError: [errorGuard],
};
