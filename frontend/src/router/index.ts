/**
 * Vue Router 配置
 * <AUTHOR>
 *
 * 定义Wings ERP系统的路由配置
 * 包含页面路由、权限控制和页面标题设置
 */

import { createRouter, createWebHistory, RouteRecordRaw } from "vue-router";
import { authGuard, titleGuard, afterEachHook, loadingGuard } from "./guards";

const routes: Array<RouteRecordRaw> = [
	// 登录页面 - 使用认证布局
	{
		path: "/login",
		name: "Login",
		component: () => import("../views/auth/LoginPage.vue"),
		meta: {
			title: "登录",
			layout: "auth", // 使用认证布局，无侧边栏
			isMenu: false,
			requiresAuth: false,
		},
	},
	// 首页 - 使用主应用布局
	{
		path: "/home",
		name: "Home",
		component: () => import("../views/Home.vue"),
		meta: {
			title: "首页",
			icon: "home",
			isMenu: true,
			requiresAuth: true,
		},
	},
	// 首页重定向到Home
	{
		path: "/",
		redirect: "/home",
	},
	{
		path: "/order",
		name: "Order",
		component: () => import("../views/orders/OrderLayout.vue"),
		meta: {
			title: "订单管理",
			icon: "shopping-cart",
			isMenu: true,
			requiresAuth: true,
		},
		children: [
			{
				path: "list",
				name: "OrderList",
				component: () => import("../views/orders/OrderList.vue"),
				meta: {
					title: "订单列表",
					icon: "list",
					isMenu: true,
					requiresAuth: true,
				},
			},
			{
				path: "pending",
				name: "OrderPending",
				component: () => import("../views/orders/OrderPending.vue"),
				meta: {
					title: "待处理订单",
					icon: "package",
					isMenu: true,
					requiresAuth: true,
				},
			},
			{
				path: "processing",
				name: "OrderProcessing",
				component: () => import("../views/orders/OrderProcessing.vue"),
				meta: {
					title: "处理中订单",
					icon: "settings",
					isMenu: true,
					requiresAuth: true,
				},
			},
			{
				path: "completed",
				name: "OrderCompleted",
				component: () => import("../views/orders/OrderCompleted.vue"),
				meta: {
					title: "已完成订单",
					icon: "warehouse",
					isMenu: true,
					requiresAuth: true,
				},
			},
		],
	},
	{
		path: "/product",
		name: "Product",
		component: () => import("../views/products/ProductLayout.vue"),
		meta: {
			title: "商品管理",
			icon: "package",
			isMenu: true,
			requiresAuth: true,
		},
		children: [
			{
				path: "list",
				name: "ProductList",
				component: () => import("../views/products/ProductList.vue"),
				meta: {
					title: "商品列表",
					icon: "list",
					isMenu: true,
					requiresAuth: true,
				},
			},
		],
	},
	{
		path: "/inventory",
		name: "Inventory",
		component: () => import("../views/inventory/InventoryLayout.vue"),
		meta: {
			title: "库存管理",
			icon: "warehouse",
			isMenu: true,
			requiresAuth: true,
		},
		children: [
			{
				path: "list",
				name: "InventoryList",
				component: () => import("../views/inventory/InventoryList.vue"),
				meta: {
					title: "库存列表",
					icon: "list",
					isMenu: true,
					requiresAuth: true,
				},
			},
			{
				path: "inbound",
				name: "InventoryInbound",
				component: () => import("../views/inventory/InventoryInbound.vue"),
				meta: {
					title: "入库记录",
					icon: "package",
					isMenu: true,
					requiresAuth: true,
				},
			},
			{
				path: "outbound",
				name: "InventoryOutbound",
				component: () => import("../views/inventory/InventoryOutbound.vue"),
				meta: {
					title: "出库记录",
					icon: "warehouse",
					isMenu: true,
					requiresAuth: true,
				},
			},
		],
	},
	// 404页面 - 使用认证布局
	{
		path: "/404",
		name: "NotFound",
		component: () => import("../views/NotFound.vue"),
		meta: {
			title: "404 Not Found",
			layout: "auth", // 使用认证布局，无侧边栏
			isMenu: false,
			requiresAuth: false,
		},
	},
	// 将匹配所有路径并重定向到404页面
	{
		path: "/:pathMatch(.*)*",
		redirect: "/404",
	},
];

const router = createRouter({
	history: createWebHistory(),
	routes,
});

// 应用路由守卫
router.beforeEach(loadingGuard);
router.beforeEach(authGuard);
router.beforeEach(titleGuard);
router.afterEach(afterEachHook);

export default router;
