<template>
	<!-- 主应用布局：包含侧边栏和面包屑导航 -->
	<div class="w-full h-full flex flex-col overflow-hidden">
		<SidebarProvider class="h-full w-full flex flex-1">
			<AppSidebar />
			<SidebarInset class="min-w-0 flex-1 overflow-hidden flex flex-col">
				<!-- 头部导航和面包屑 -->
				<header
					class="flex h-16 shrink-0 items-center gap-2 transition-[width,height] ease-linear group-has-[[data-collapsible=icon]]/sidebar-wrapper:h-12 min-w-0"
				>
					<div class="flex items-center gap-2 px-2 sm:px-4 min-w-0 w-full">
						<SidebarTrigger class="shrink-0 -ml-1" />
						<Separator orientation="vertical" class="mr-2 h-4 shrink-0" />
						<!-- 面包屑容器 -->
						<div class="min-w-0 flex-1">
							<Breadcrumb>
								<BreadcrumbList class="flex-wrap">
									<template
										v-for="(item, index) in breadcrumbItems"
										:key="item.path"
									>
										<BreadcrumbItem v-if="!item.isLast" class="hidden sm:block">
											<BreadcrumbLink
												href="#"
												@click.prevent="handleBreadcrumbClick(item.path)"
												class="cursor-pointer hover:text-primary text-sm"
											>
												{{ item.name }}
											</BreadcrumbLink>
										</BreadcrumbItem>
										<BreadcrumbSeparator
											v-if="!item.isLast && index < breadcrumbItems.length - 1"
											class="hidden sm:block"
										/>
										<BreadcrumbItem v-if="item.isLast">
											<BreadcrumbPage class="text-sm truncate">{{
												item.name
											}}</BreadcrumbPage>
										</BreadcrumbItem>
									</template>
								</BreadcrumbList>
							</Breadcrumb>
						</div>
					</div>
				</header>
				<!-- 主内容区域 -->
				<div class="flex flex-1 flex-col min-w-0 max-w-full overflow-hidden">
					<div class="flex-1 w-full max-w-full min-w-0 overflow-hidden">
						<RouterView class="w-full max-w-full min-w-0 h-full" />
					</div>
				</div>
			</SidebarInset>
		</SidebarProvider>
	</div>
</template>

<script setup lang="ts">
/**
 * 主应用布局组件
 * 包含侧边栏、面包屑导航等主应用UI元素
 * 用于需要登录后的页面
 * <AUTHOR>
 */

import {
	SidebarInset,
	SidebarProvider,
	SidebarTrigger,
} from "@/components/ui/sidebar";
import {
	Breadcrumb,
	BreadcrumbItem,
	BreadcrumbLink,
	BreadcrumbList,
	BreadcrumbPage,
	BreadcrumbSeparator,
} from "@/components/ui/breadcrumb";
import { Separator } from "@/components/ui/separator";
import AppSidebar from "@/components/AppSidebar.vue";
import { computed } from "vue";
import { useRoute, useRouter } from "vue-router";
import type { IBreadcrumbItem } from "@/types/breadcrumb";

/**
 * 面包屑导航逻辑
 */
const route = useRoute();
const router = useRouter();

// 生成面包屑数据
const breadcrumbItems = computed((): IBreadcrumbItem[] => {
	const items: IBreadcrumbItem[] = [];

	// 获取匹配的路由记录
	const matched = route.matched.filter((item) => item.meta?.title);

	matched.forEach((routeRecord, index) => {
		const isLast = index === matched.length - 1;
		const title = routeRecord.meta?.title as string;

		// 构建正确的路径
		let path = routeRecord.path;

		// 如果是子路由，需要拼接完整路径
		if (index > 0 && !path.startsWith("/")) {
			const parentPath = matched[index - 1].path;
			path = `${parentPath}/${path}`;
		}

		// 如果路径中有参数，使用当前路由的实际路径
		if (isLast) {
			path = route.path;
		}

		items.push({
			name: title,
			path: path,
			isLast,
		});
	});

	return items;
});

// 面包屑点击处理
const handleBreadcrumbClick = (path: string): void => {
	if (path !== route.path) {
		router.push(path);
	}
};
</script>

<style scoped>
/**
 * 主应用布局样式
 * 继承自原App.vue的样式
 */

/* 确保容器不会超出视口 */
.sidebar-provider {
	max-width: 100vw;
}

/* RouterView容器样式优化 - 禁止滚动 */
:deep(.router-view-container) {
	max-width: 100%;
	overflow: hidden;
	box-sizing: border-box;
}

/* 确保RouterView渲染的组件不会超出容器 */
:deep(.router-view-container > *) {
	max-width: 100%;
	overflow-x: hidden;
	box-sizing: border-box;
}

/* 在超小屏幕上进一步优化间距 */
@media (max-width: 640px) {
	.breadcrumb-list {
		gap: 0.25rem;
	}
}

/* 确保任何固定宽度的元素都不会超出容器 */
:deep(*) {
	max-width: 100%;
	box-sizing: border-box;
}

/* 移除可能导致页面滚动的样式 */
:deep(table) {
	max-width: 100%;
	table-layout: fixed;
}

:deep(img) {
	max-width: 100%;
	height: auto;
}

:deep(pre) {
	max-width: 100%;
	word-wrap: break-word;
}
</style>
