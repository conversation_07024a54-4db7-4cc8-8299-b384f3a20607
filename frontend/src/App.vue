<script setup lang="ts">
/**
 * 应用根组件
 * 根据路由meta信息动态选择布局组件
 * 等待认证初始化或路由加载完成后再渲染，防止页面闪烁
 * <AUTHOR>
 */

import { computed } from "vue";
import { useRoute } from "vue-router";
import AuthLayout from "@/layouts/AuthLayout.vue";
import MainLayout from "@/layouts/MainLayout.vue";
import { useAuth } from "@/stores/auth";

/**
 * 认证和路由逻辑
 */
const auth = useAuth();
const route = useRoute();

/**
 * 计算当前应该使用的布局组件
 */
const currentLayout = computed(() => {
	// 如果路由meta中指定了layout，使用指定的布局
	const layoutType = route.meta?.layout;

	if (layoutType === "auth") {
		return AuthLayout;
	}

	// 默认使用主应用布局
	return MainLayout;
});
</script>

<template>
	<!-- 根布局 -->
	<!-- 等待认证状态初始化或路由加载完成再渲染，防止页面闪烁 -->
	<template v-if="auth.isAuthInitialized.value && !auth.isRouteLoading.value">
		<component :is="currentLayout" />
	</template>
	<!-- 初始化或路由跳转期间显示加载动画 -->
	<template v-else>
		<div class="fixed inset-0 flex items-center justify-center bg-background">
			<div class="flex flex-col items-center gap-4">
				<div
					class="w-12 h-12 rounded-full animate-spin border-4 border-dashed border-primary border-t-transparent"
				></div>
				<p class="text-muted-foreground">正在加载系统...</p>
			</div>
		</div>
	</template>
</template>

<style>
/**
 * 全局样式
 * 确保应用在各种屏幕尺寸下都能正确显示
 */

/* 重置默认样式 */
*,
*::before,
*::after {
	box-sizing: border-box;
}

html,
body,
#app {
	margin: 0;
	padding: 0;
	width: 100%;
	height: 100%;
	overflow: hidden;
}

/* 确保应用容器占满全屏 */
#app {
	min-height: 100vh;
	min-height: 100dvh; /* 移动端动态视口高度 */
}

/* 全局字体和基础样式 */
body {
	font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto,
		"Helvetica Neue", Arial, sans-serif;
	line-height: 1.6;
	color: #333;
	background-color: #fff;
}

/* 响应式设计 */
@media (max-width: 640px) {
	body {
		font-size: 14px;
	}
}
</style>
