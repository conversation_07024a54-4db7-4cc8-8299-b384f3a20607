/**
 * 店铺相关API接口
 * 基于Swagger文档 /shops/* 路径实现
 * <AUTHOR>
 */

import { ApiRequest } from "../utils/request.js";
import type {
	IShopResponse,
	IShopCreateRequest,
	IShopUpdateRequest,
	IShopQueryParams,
	IPageResponse,
} from "../types/api.js";

/**
 * 店铺API类
 * 封装所有店铺相关的接口请求
 * <AUTHOR>
 */
export class ShopApi {
	/**
	 * 获取店铺列表
	 * 对应接口: GET /api/v1/shops
	 * @param params 查询参数
	 * @returns 店铺列表
	 */
	static async getShops(
		params?: IShopQueryParams
	): Promise<IPageResponse<IShopResponse>> {
		const response = await ApiRequest.get<IPageResponse<IShopResponse>>(
			"/shops",
			{ params }
		);
		return response.data;
	}

	/**
	 * 获取店铺详情
	 * 对应接口: GET /api/v1/shops/{id}
	 * @param id 店铺ID
	 * @returns 店铺详情
	 */
	static async getShopById(id: number): Promise<IShopResponse> {
		const response = await ApiRequest.get<IShopResponse>(`/shops/${id}`);
		return response.data;
	}

	/**
	 * 创建店铺
	 * 对应接口: POST /api/v1/shops
	 * @param data 店铺数据
	 * @returns 创建结果
	 */
	static async createShop(data: IShopCreateRequest): Promise<IShopResponse> {
		const response = await ApiRequest.post<IShopResponse>("/shops", data);
		return response.data;
	}

	/**
	 * 更新店铺信息
	 * 对应接口: PUT /api/v1/shops/{id}
	 * @param id 店铺ID
	 * @param data 更新数据
	 * @returns 更新结果
	 */
	static async updateShop(
		id: number,
		data: IShopUpdateRequest
	): Promise<IShopResponse> {
		const response = await ApiRequest.put<IShopResponse>(`/shops/${id}`, data);
		return response.data;
	}

	/**
	 * 删除店铺
	 * 对应接口: DELETE /api/v1/shops/{id}
	 * @param id 店铺ID
	 * @returns 删除结果
	 */
	static async deleteShop(id: number): Promise<void> {
		await ApiRequest.delete<void>(`/shops/${id}`);
	}
}
