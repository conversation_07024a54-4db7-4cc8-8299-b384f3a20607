/**
 * 订单相关API接口
 * 基于Swagger文档 /orders/* 路径实现
 * <AUTHOR>
 */

import { ApiRequest } from "../utils/request.js";
import type {
	IPageResponse,
	IOrderResponse,
	IOrderQueryParams,
	IOrderUpdateRequest,
	IOrderItemUpdateRequest,
	IOrderSyncResponse,
	IBatchSyncResponse,
} from "../types/api.js";

/**
 * 订单API类
 * 封装所有订单相关的接口请求
 * <AUTHOR>
 */
export class OrderApi {
	/**
	 * 获取订单列表
	 * 对应接口: GET /api/v1/orders
	 * @param params 查询参数
	 * @returns 订单列表
	 */
	static async getOrders(
		params?: IOrderQueryParams
	): Promise<IPageResponse<IOrderResponse>> {
		const response = await ApiRequest.get<IPageResponse<IOrderResponse>>(
			"/orders",
			{ params }
		);
		return response.data;
	}

	/**
	 * 获取订单详情
	 * 对应接口: GET /api/v1/orders/{id}
	 * @param id 订单ID
	 * @returns 订单详情
	 */
	static async getOrderById(id: number): Promise<IOrderResponse> {
		const response = await ApiRequest.get<IOrderResponse>(`/orders/${id}`);
		return response.data;
	}

	/**
	 * 更新订单信息
	 * 对应接口: PUT /api/v1/orders/{id}
	 * @param id 订单ID
	 * @param data 更新数据
	 * @returns 更新结果
	 */
	static async updateOrder(
		id: number,
		data: IOrderUpdateRequest
	): Promise<void> {
		await ApiRequest.put<void>(`/orders/${id}`, data);
	}

	/**
	 * 更新订单项信息
	 * 对应接口: PUT /api/v1/orders/{id}/items/{itemId}
	 * @param orderId 订单ID
	 * @param itemId 订单项ID
	 * @param data 更新数据
	 * @returns 更新结果
	 */
	static async updateOrderItem(
		orderId: number,
		itemId: number,
		data: IOrderItemUpdateRequest
	): Promise<void> {
		await ApiRequest.put<void>(`/orders/${orderId}/items/${itemId}`, data);
	}

	/**
	 * 同步单个订单
	 * 对应接口: POST /api/v1/orders/{id}/sync
	 * @param id 订单ID
	 * @returns 同步结果
	 */
	static async syncOrder(id: number): Promise<IOrderSyncResponse> {
		const response = await ApiRequest.post<IOrderSyncResponse>(
			`/orders/${id}/sync`
		);
		return response.data;
	}

	/**
	 * 同步指定店铺的订单
	 * 对应接口: POST /api/v1/orders/sync/shop/{id}
	 * @param shopId 店铺ID
	 * @returns 同步结果
	 */
	static async syncShopOrders(shopId: number): Promise<IOrderSyncResponse> {
		const response = await ApiRequest.post<IOrderSyncResponse>(
			`/orders/sync/shop/${shopId}`
		);
		return response.data;
	}

	/**
	 * 同步所有店铺的订单
	 * 对应接口: POST /api/v1/orders/sync/shops
	 * @returns 批量同步结果
	 */
	static async syncAllShopsOrders(): Promise<IBatchSyncResponse> {
		const response = await ApiRequest.post<IBatchSyncResponse>(
			"/orders/sync/shops"
		);
		return response.data;
	}
}
