/**
 * 商品相关API接口
 * 基于Swagger文档 /products/* 路径实现
 * <AUTHOR>
 */

import { ApiRequest } from "../utils/request.js";
import type {
	IPageResponse,
	IProductResponse,
	IProductQueryParams,
	IProductUpdateRequest,
	IProductSyncResponse,
} from "../types/api.js";

/**
 * 商品API类
 * 封装所有商品相关的接口请求
 * <AUTHOR>
 */
export class ProductApi {
	/**
	 * 获取商品列表
	 * 对应接口: GET /api/v1/products
	 * @param params 查询参数
	 * @returns 商品列表
	 */
	static async getProducts(
		params?: IProductQueryParams
	): Promise<IPageResponse<IProductResponse>> {
		const response = await ApiRequest.get<IPageResponse<IProductResponse>>(
			"/products",
			{ params }
		);
		return response.data;
	}

	/**
	 * 获取商品详情
	 * 对应接口: GET /api/v1/products/{id}
	 * @param id 商品ID
	 * @returns 商品详情
	 */
	static async getProductById(id: number): Promise<IProductResponse> {
		const response = await ApiRequest.get<IProductResponse>(`/products/${id}`);
		return response.data;
	}

	/**
	 * 更新商品信息
	 * 对应接口: PUT /api/v1/products/{id}
	 * @param id 商品ID
	 * @param data 更新数据
	 * @returns 更新结果
	 */
	static async updateProduct(
		id: number,
		data: IProductUpdateRequest
	): Promise<void> {
		await ApiRequest.put<void>(`/products/${id}`, data);
	}

	/**
	 * 同步指定店铺的商品
	 * 对应接口: POST /api/v1/products/sync/shop/{id}
	 * @param shopId 店铺ID
	 * @returns 同步结果
	 */
	static async syncShopProducts(shopId: number): Promise<IProductSyncResponse> {
		const response = await ApiRequest.post<IProductSyncResponse>(
			`/products/sync/shop/${shopId}`
		);
		return response.data;
	}

	/**
	 * 同步所有店铺的商品
	 * 对应接口: POST /api/v1/products/sync/shops
	 * @returns 同步结果
	 */
	static async syncAllShopsProducts(): Promise<IProductSyncResponse> {
		const response = await ApiRequest.post<IProductSyncResponse>(
			"/products/sync/shops"
		);
		return response.data;
	}

	/**
	 * 同步库存（未使用）
	 * 对应接口: POST /api/v1/products/sync/stocks
	 * @param shopId 店铺ID
	 * @returns 同步结果
	 */
	static async syncStocks(shopId: number): Promise<IProductSyncResponse> {
		const response = await ApiRequest.post<IProductSyncResponse>(
			"/products/sync/stocks",
			null,
			{ params: { shop_id: shopId } }
		);
		return response.data;
	}
}
