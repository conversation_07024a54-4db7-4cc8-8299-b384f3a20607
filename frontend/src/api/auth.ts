/**
 * 用户认证相关API接口
 * 基于Swagger文档 /auth/* 路径实现
 * <AUTHOR>
 */

import { ApiRequest } from "../utils/request.js";
import type {
	IApiResponse,
	ILoginRequest,
	ITokenResponse,
	IResetPasswordRequest,
	IUserResponse,
	IUserCreateRequest,
	IUserUpdateRequest,
} from "../types/api.js";

/**
 * 认证API类
 * 封装所有用户认证相关的接口请求
 * <AUTHOR>
 */
export class AuthApi {
	/**
	 * 用户登录
	 * 对应接口: POST /api/v1/auth/login
	 * @param params 登录参数
	 * @returns 登录响应数据
	 */
	static async login(params: ILoginRequest): Promise<ITokenResponse> {
		const response = await ApiRequest.post<ITokenResponse>(
			"/auth/login",
			params
		);
		return response.data;
	}

	/**
	 * 用户登出
	 * 对应接口: POST /api/v1/auth/logout
	 * @returns 登出结果
	 */
	static async logout(): Promise<void> {
		await ApiRequest.post<void>("/auth/logout");
	}

	/**
	 * 重置密码
	 * 对应接口: POST /api/v1/auth/reset-password
	 * @param params 重置密码参数
	 * @returns 重置结果
	 */
	static async resetPassword(params: IResetPasswordRequest): Promise<void> {
		await ApiRequest.post<void>("/auth/reset-password", params);
	}
}

/**
 * 用户管理API类
 * 封装用户CRUD操作相关的接口请求
 * <AUTHOR>
 */
export class UserApi {
	/**
	 * 获取用户列表
	 * 对应接口: GET /api/v1/users
	 * @returns 用户列表
	 */
	static async getUsers(): Promise<IUserResponse[]> {
		const response = await ApiRequest.get<IUserResponse[]>("/users");
		return response.data;
	}

	/**
	 * 创建用户
	 * 对应接口: POST /api/v1/users
	 * @param userData 用户信息
	 * @returns 创建的用户信息
	 */
	static async createUser(
		userData: IUserCreateRequest
	): Promise<IUserResponse> {
		const response = await ApiRequest.post<IUserResponse>("/users", userData);
		return response.data;
	}

	/**
	 * 获取当前用户信息
	 * 对应接口: GET /api/v1/users/me
	 * @returns 当前用户信息
	 */
	static async getCurrentUser(): Promise<IUserResponse> {
		const response = await ApiRequest.get<IUserResponse>("/users/me");
		return response.data;
	}

	/**
	 * 获取指定用户信息
	 * 对应接口: GET /api/v1/users/{id}
	 * @param id 用户ID
	 * @returns 用户信息
	 */
	static async getUserById(id: number): Promise<IUserResponse> {
		const response = await ApiRequest.get<IUserResponse>(`/users/${id}`);
		return response.data;
	}

	/**
	 * 更新用户信息
	 * 对应接口: PUT /api/v1/users/{id}
	 * @param id 用户ID
	 * @param userData 更新的用户信息
	 * @returns 更新后的用户信息
	 */
	static async updateUser(
		id: number,
		userData: IUserUpdateRequest
	): Promise<IUserResponse> {
		const response = await ApiRequest.put<IUserResponse>(
			`/users/${id}`,
			userData
		);
		return response.data;
	}

	/**
	 * 删除用户
	 * 对应接口: DELETE /api/v1/users/{id}
	 * @param id 用户ID
	 * @returns 删除结果
	 */
	static async deleteUser(id: number): Promise<void> {
		await ApiRequest.delete<void>(`/users/${id}`);
	}
}
