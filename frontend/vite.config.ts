import { defineConfig } from "vite";
import vue from "@vitejs/plugin-vue";

import path from "node:path";
import tailwindcss from "@tailwindcss/vite";

// https://vite.dev/config/
export default defineConfig({
	plugins: [vue(), tailwindcss()],
	resolve: {
		alias: {
			"@": path.resolve(__dirname, "./src"),
		},
	},
	// 开发服务器配置
	server: {
		port: 5173, // 前端开发服务器端口
		host: true, // 允许外部访问
		// API代理配置
		proxy: {
			// 将 /api 请求代理到后端服务器的 /api/v1
			'/api': {
				target: 'http://localhost:8080', // 后端服务器地址
				changeOrigin: true, // 改变请求源
				secure: false, // 如果是https接口，需要配置这个参数
				// 重写路径：/api/* -> /api/v1/*（后端使用版本化API）
				// rewrite: (path) => path.replace(/^\/api/, '/api/v1'),
				configure: (proxy, options) => {
					// 代理配置钩子，可以添加额外配置
					proxy.on('error', (err, req, res) => {
						console.log('代理错误:', err);
					});
					proxy.on('proxyReq', (proxyReq, req, res) => {
						console.log(`代理请求: ${req.method} ${req.url} -> ${proxyReq.path}`);
					});
					proxy.on('proxyRes', (proxyRes, req, res) => {
						console.log(`代理响应: ${proxyRes.statusCode} ${req.url}`);
					});
				},
			},
		},
	},
});
