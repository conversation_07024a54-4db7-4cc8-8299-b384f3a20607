---
description: 
globs: 
alwaysApply: true
---
# 前端接口规范文档

## 概述

本文档描述了9Wings ERP系统前端与后端接口交互的规范和标准。前端接口完全基于后端提供的Swagger API标准进行开发和维护。

## Swagger接口访问

### 接口文档地址

#### 1. Swagger UI界面
- **地址**: `http://localhost:8080/swagger/index.html`
- **用途**: 提供可视化的API文档界面，支持在线测试接口
- **功能**: 
  - 查看所有可用的API接口
  - 查看接口参数和响应格式
  - 在线测试接口功能
  - 查看接口请求和响应示例

#### 2. Swagger JSON数据
- **地址**: `http://localhost:8080/swagger/doc.json`
- **用途**: 获取完整的API接口JSON结构数据
- **功能**:
  - 自动生成前端API类型定义
  - 自动生成前端API调用方法
  - 集成到前端构建流程中

## 前端接口开发规范

### 1. 接口类型定义

所有接口类型定义应基于Swagger文档自动生成或手动对照编写：

```typescript
/**
 * 用户信息接口类型定义
 * <AUTHOR>
 * 根据Swagger文档定义的用户相关接口类型
 */
interface IUser {
  id: number;
  username: string;
  email: string;
  createdAt: string;
  updatedAt: string;
}

/**
 * API响应基础类型
 * <AUTHOR>
 * 统一的API响应格式，对应后端Swagger定义
 */
interface IApiResponse<T> {
  code: number;
  message: string;
  data: T;
}

/**
 * 分页响应类型
 * <AUTHOR>
 * 分页查询的响应格式
 */
interface IPaginationResponse<T> {
  items: T[];
  total: number;
  page: number;
  pageSize: number;
}
```

### 2. 接口调用方法

#### 基础HTTP客户端配置

```typescript
/**
 * HTTP客户端配置
 * <AUTHOR>
 * 配置基础的HTTP请求客户端，对接Swagger定义的接口
 */
import axios from 'axios';

const apiClient = axios.create({
  baseURL: 'http://localhost:8080/api',
  timeout: 10000,
  headers: {
    'Content-Type': 'application/json',
  },
});

// 请求拦截器
apiClient.interceptors.request.use(
  (config) => {
    // 添加认证token等通用处理
    const token = localStorage.getItem('authToken');
    if (token) {
      config.headers.Authorization = `Bearer ${token}`;
    }
    return config;
  },
  (error) => {
    return Promise.reject(error);
  }
);

// 响应拦截器
apiClient.interceptors.response.use(
  (response) => {
    return response.data;
  },
  (error) => {
    // 统一错误处理
    console.error('API Error:', error);
    return Promise.reject(error);
  }
);
```

#### 接口服务类

```typescript
/**
 * 用户接口服务类
 * <AUTHOR>
 * 封装用户相关的API调用方法，严格对应Swagger文档定义
 */
class UserService {
  /**
   * 获取用户列表
   * 对应Swagger接口: GET /api/users
   */
  static async getUsers(params?: {
    page?: number;
    pageSize?: number;
    keyword?: string;
  }): Promise<IApiResponse<IPaginationResponse<IUser>>> {
    return apiClient.get('/users', { params });
  }

  /**
   * 获取用户详情
   * 对应Swagger接口: GET /api/users/{id}
   */
  static async getUserById(id: number): Promise<IApiResponse<IUser>> {
    return apiClient.get(`/users/${id}`);
  }

  /**
   * 创建用户
   * 对应Swagger接口: POST /api/users
   */
  static async createUser(userData: Omit<IUser, 'id' | 'createdAt' | 'updatedAt'>): Promise<IApiResponse<IUser>> {
    return apiClient.post('/users', userData);
  }

  /**
   * 更新用户
   * 对应Swagger接口: PUT /api/users/{id}
   */
  static async updateUser(id: number, userData: Partial<IUser>): Promise<IApiResponse<IUser>> {
    return apiClient.put(`/users/${id}`, userData);
  }

  /**
   * 删除用户
   * 对应Swagger接口: DELETE /api/users/{id}
   */
  static async deleteUser(id: number): Promise<IApiResponse<void>> {
    return apiClient.delete(`/users/${id}`);
  }
}
```

### 3. 接口开发流程

#### 步骤1: 查看Swagger文档
1. 访问 `http://localhost:8080/swagger/index.html`
2. 找到需要对接的API接口
3. 查看接口的请求参数、响应格式、状态码等信息

#### 步骤2: 定义TypeScript类型
根据Swagger文档中的schema定义，创建对应的TypeScript接口类型

#### 步骤3: 实现接口调用方法
按照统一的服务类模式，实现具体的API调用方法

#### 步骤4: 错误处理
确保所有接口调用都有适当的错误处理机制

### 4. 接口命名规范

#### URL路径映射
```typescript
/**
 * 接口路径常量定义
 * <AUTHOR>
 * 统一管理所有API接口路径，与Swagger文档保持一致
 */
export const API_ENDPOINTS = {
  // 用户管理
  USERS: '/users',
  USER_DETAIL: '/users/:id',
  
  // 订单管理
  ORDERS: '/orders',
  ORDER_DETAIL: '/orders/:id',
  ORDER_STATUS: '/orders/:id/status',
  
  // 商品管理
  PRODUCTS: '/products',
  PRODUCT_DETAIL: '/products/:id',
  PRODUCT_INVENTORY: '/products/:id/inventory',
  
  // 库存管理
  INVENTORY: '/inventory',
  INVENTORY_IN: '/inventory/in',
  INVENTORY_OUT: '/inventory/out',
} as const;
```

#### 服务类命名
- 服务类使用 `Service` 后缀：`UserService`、`OrderService`
- 方法名使用动词+名词格式：`getUsers`、`createOrder`、`updateProduct`

### 5. 错误处理规范

#### 统一错误类型定义
```typescript
/**
 * API错误类型定义
 * <AUTHOR>
 * 统一的错误处理类型，对应Swagger文档中的错误响应格式
 */
interface IApiError {
  code: number;
  message: string;
  details?: string;
  timestamp: string;
}

/**
 * 错误处理工具类
 * <AUTHOR>
 * 提供统一的错误处理方法
 */
class ErrorHandler {
  static handle(error: IApiError): void {
    switch (error.code) {
      case 400:
        console.error('请求参数错误:', error.message);
        break;
      case 401:
        console.error('认证失败:', error.message);
        // 跳转到登录页面
        break;
      case 403:
        console.error('权限不足:', error.message);
        break;
      case 404:
        console.error('资源不存在:', error.message);
        break;
      case 500:
        console.error('服务器内部错误:', error.message);
        break;
      default:
        console.error('未知错误:', error.message);
    }
  }
}
```

## 开发工具和集成

### 1. 推荐工具

#### VSCode插件
- **Swagger Viewer**: 在VSCode中查看Swagger文档
- **REST Client**: 直接在VSCode中测试API接口

#### 自动化工具
- **swagger-typescript-api**: 自动从Swagger JSON生成TypeScript类型定义
- **openapi-generator**: 生成API客户端代码

### 2. 集成到构建流程

#### package.json配置
```json
{
  "scripts": {
    "api:generate": "swagger-typescript-api -p http://localhost:8080/swagger/doc.json -o ./src/api/generated",
    "api:update": "pnpm api:generate && prettier --write ./src/api/generated/**/*.ts"
  },
  "devDependencies": {
    "swagger-typescript-api": "^latest"
  }
}
```

#### 自动化生成API类型
```bash
# 从Swagger文档生成TypeScript类型定义
pnpm api:generate

# 生成并格式化代码
pnpm api:update
```

## 注意事项

### 1. 版本管理
- 当后端API发生变化时，及时更新前端接口类型定义
- 使用版本控制跟踪接口变更历史

### 2. 测试建议
- 在开发过程中，先通过Swagger UI测试接口
- 确保接口调用参数和响应格式正确

### 3. 性能优化
- 合理使用接口缓存机制
- 避免不必要的重复接口调用
- 实现接口防抖和节流

### 4. 安全考虑
- 所有敏感操作接口都需要认证
- 前端需要妥善处理认证token
- 避免在前端暴露敏感信息

## 维护和更新

当后端Swagger文档更新时，前端需要：

1. 查看 `http://localhost:8080/swagger/index.html` 了解变更内容
2. 下载最新的 `http://localhost:8080/swagger/doc.json`
3. 更新前端类型定义和接口调用方法
4. 测试相关功能确保兼容性
5. 更新相关文档

---

**文档维护者**: 前端开发团队  
**最后更新**: 2024年  
**Swagger版本**: 基于后端最新版本 