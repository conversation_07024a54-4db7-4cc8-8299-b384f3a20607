---
description: 
globs: 
alwaysApply: true
---
# 9wings ERP系统

## 项目需求文档

### 应用整体概览
- 这是一个SaaS模式的ERP系统,主要是提供用户对外贸订单的管理,解决获取平台订单列表、仓库打印运单、采购信息汇总的问题.

### 技术栈和API
- **前端**
  - 使用Typescript作为开发语言
  - 使用shadcn-vue作为主要的UI框架,所有组件优先使用框架 [https://www.shadcn-vue.com/docs/introduction.html]
  - 使用tailwindcss4作为辅助css的样式,不使用自定义class,全部使用tailwindcss语法.
  - 使用AntV G2Plot2作为图表框架[https://g2plot.antv.antgroup.com/examples]
  - 使用vue-router4 [https://router.vuejs.org/zh/guide/]
  - 使用Vite6作为打包工具
- **后端**
  - 使用Go语言开发
  - 使用jwt-go进行用户认证
  - 使用Casbin进行权限校验
- **API规范**
  - 使用Swagger作为后端提供给前端的API规范
- **通用**
  - 使用eslint和prettier作为代码规范工具
  - 使用pnpm作为包管理工具

### 权限设计
- **认证机制**: 通过`jwt-go`生成和验证Token, 实现无状态的用户认证。
- **授权模型**: 采用RBAC（基于角色的访问控制）模型，通过`Casbin`实现。
  - **角色（Role）**: 定义系统中的不同角色，如管理员、运营、采购员。
  - **权限（Permission）**: 定义具体的操作权限，如查看订单、编辑商品、打印运单等。
  - **用户（User）**: 用户可以被分配一个或多个角色，从而继承角色的所有权限。
  - **Casbin策略**: 访问控制策略存储在数据库中，由Casbin引擎根据`{sub, obj, act}`（用户/角色, 访问资源, 操作）的模式进行权限验证。

### 项目目录结构
- frontend/ - 前端Vue3项目
  - src/ - 源代码目录
  - public/ - 静态资源目录
  - index.html - 入口HTML文件
  - vite.config.ts - Vite配置
  - nginx.conf - Nginx配置（用于生产环境）
  - Dockerfile - 前端Docker构建文件
  - package.json - 项目依赖配置

### CI/CD流程
- 使用GitLab CI配置自动化部署
- 当代码提交到主分支时触发自动部署

### 核心功能
1. 订单管理
- 通过API获取订单信息(平台货件,图片,颜色,尺寸,物流追踪号码,物流状态,面单)
- 后台订单状态分为未接单、已接单、已采购、已打单，可以筛选显示
订单可填入采购价格
- 填入采购价格后，将订单状态更改为已采购
- 点击"打印运单按钮"后，将订单状态更改为已打单

2. 商品管理
- 展示全部商品的基础信息,并对商品的重要字段做颜色突出显示.
- 商品库存概念,描述商品的剩余数量.

3. 库存管理
- 展示入库记录,包括数量和采购成本.
- 展示出库记录,包括数量和销售金额.