---
description: 
globs: *.vue
alwaysApply: false
---
# 9Wings ERP 页面构建指南

## 概述

本文档详细描述了9Wings ERP系统前端页面的构建方式和开发规范。项目严格遵循shadcn-vue组件库和Tailwind CSS的使用规范，确保代码的一致性和可维护性。

## 技术栈

### 核心框架
- **Vue 3**: 使用Composition API进行组件开发
- **TypeScript**: 提供类型安全和更好的开发体验
- **Vite 6**: 现代化的构建工具
- **Vue Router 4**: 路由管理

### UI框架和样式
- **shadcn-vue**: 主要UI组件库 [https://www.shadcn-vue.com/docs/introduction.html]
- **Tailwind CSS 4**: 原子化CSS框架，禁止使用自定义class
- **class-variance-authority**: 组件变体管理

### 图表和可视化
- **AntV G2Plot 2**: 数据可视化图表库 [https://g2plot.antv.antgroup.com/examples]

## 页面构建原则

### 1. 组件优先原则
```typescript
/**
 * 组件使用优先级
 * <AUTHOR>
 * 严格按照优先级选择组件，确保UI一致性
 */

// 1. 优先使用shadcn-vue组件
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Input } from '@/components/ui/input'

// 2. 其次使用Radix Vue原始组件
import { DialogRoot, DialogTrigger } from 'radix-vue'

// 3. 最后考虑自定义组件（基于shadcn-vue构建）
import { CustomDataTable } from '@/components/custom/data-table'
```

### 2. 样式开发规范
```vue
<template>
  <!-- ✅ 正确：使用Tailwind CSS原子类 -->
  <div class="flex items-center justify-between p-4 bg-white rounded-lg shadow-sm">
    <h1 class="text-2xl font-bold text-gray-900">页面标题</h1>
    <Button class="ml-4">操作按钮</Button>
  </div>

  <!-- ❌ 错误：禁止使用自定义class -->
  <div class="custom-header">
    <h1 class="custom-title">页面标题</h1>
  </div>
</template>

<style scoped>
/* ❌ 禁止定义自定义样式类 */
.custom-header {
  display: flex;
  /* ... */
}
</style>
```

## 页面布局结构

### 1. 整体布局架构
```vue
<template>
  <!-- 
    主布局结构
    <AUTHOR>
    使用shadcn-vue的Sidebar组件构建整体布局
  -->
  <div class="min-h-screen bg-gray-50">
    <!-- 侧边栏 -->
    <Sidebar class="fixed left-0 top-0 h-full w-64 bg-white border-r">
      <SidebarContent>
        <SidebarGroup>
          <SidebarGroupLabel>导航菜单</SidebarGroupLabel>
          <SidebarGroupContent>
            <SidebarMenu>
              <SidebarMenuItem v-for="item in menuItems" :key="item.id">
                <SidebarMenuButton :href="item.href">
                  {{ item.title }}
                </SidebarMenuButton>
              </SidebarMenuItem>
            </SidebarMenu>
          </SidebarGroupContent>
        </SidebarGroup>
      </SidebarContent>
    </Sidebar>

    <!-- 主内容区域 -->
    <div class="ml-64">
      <!-- 顶部导航栏 -->
      <header class="sticky top-0 z-40 bg-white border-b px-6 py-4">
        <div class="flex items-center justify-between">
          <Breadcrumb>
            <BreadcrumbList>
              <BreadcrumbItem>
                <BreadcrumbLink href="/">首页</BreadcrumbLink>
              </BreadcrumbItem>
              <BreadcrumbSeparator />
              <BreadcrumbItem>
                <BreadcrumbPage>当前页面</BreadcrumbPage>
              </BreadcrumbItem>
            </BreadcrumbList>
          </Breadcrumb>

          <div class="flex items-center space-x-4">
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button variant="ghost" size="sm">
                  <Avatar class="h-8 w-8">
                    <AvatarImage src="/avatar.jpg" />
                    <AvatarFallback>用户</AvatarFallback>
                  </Avatar>
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent>
                <DropdownMenuItem>个人设置</DropdownMenuItem>
                <DropdownMenuItem>退出登录</DropdownMenuItem>
              </DropdownMenuContent>
            </DropdownMenu>
          </div>
        </div>
      </header>

      <!-- 页面内容 -->
      <main class="p-6">
        <router-view />
      </main>
    </div>
  </div>
</template>
```

### 2. 页面容器结构
```vue
<template>
  <!-- 
    标准页面容器
    <AUTHOR>
    每个页面都应该遵循这个基础结构
  -->
  <div class="space-y-6">
    <!-- 页面头部 -->
    <div class="flex items-center justify-between">
      <div>
        <h1 class="text-3xl font-bold tracking-tight">页面标题</h1>
        <p class="text-muted-foreground">页面描述信息</p>
      </div>
      <div class="flex items-center space-x-2">
        <Button variant="outline">次要操作</Button>
        <Button>主要操作</Button>
      </div>
    </div>

    <!-- 筛选和搜索区域 -->
    <Card>
      <CardHeader>
        <CardTitle>筛选条件</CardTitle>
      </CardHeader>
      <CardContent>
        <div class="grid grid-cols-1 md:grid-cols-3 lg:grid-cols-4 gap-4">
          <div class="space-y-2">
            <Label htmlFor="search">搜索</Label>
            <Input id="search" placeholder="请输入关键词" />
          </div>
          <!-- 更多筛选条件 -->
        </div>
      </CardContent>
    </Card>

    <!-- 主要内容区域 -->
    <Card>
      <CardHeader>
        <CardTitle>数据列表</CardTitle>
      </CardHeader>
      <CardContent>
        <!-- 数据表格或其他内容 -->
      </CardContent>
    </Card>
  </div>
</template>
```

## 核心页面构建模式

### 1. 订单管理页面
```vue
<template>
  <!-- 
    订单管理页面
    <AUTHOR>
    展示订单列表，支持筛选、搜索和状态管理
  -->
  <div class="space-y-6">
    <!-- 页面头部 -->
    <div class="flex items-center justify-between">
      <div>
        <h1 class="text-3xl font-bold tracking-tight">订单管理</h1>
        <p class="text-muted-foreground">管理所有平台订单信息</p>
      </div>
      <Button @click="refreshOrders">
        <RefreshCw class="mr-2 h-4 w-4" />
        刷新订单
      </Button>
    </div>

    <!-- 筛选区域 -->
    <Card>
      <CardHeader>
        <CardTitle>筛选条件</CardTitle>
      </CardHeader>
      <CardContent>
        <div class="grid grid-cols-1 md:grid-cols-4 gap-4">
          <!-- 订单状态筛选 -->
          <div class="space-y-2">
            <Label>订单状态</Label>
            <Select v-model="filters.status">
              <SelectTrigger>
                <SelectValue placeholder="选择状态" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="pending">未接单</SelectItem>
                <SelectItem value="accepted">已接单</SelectItem>
                <SelectItem value="purchased">已采购</SelectItem>
                <SelectItem value="printed">已打单</SelectItem>
              </SelectContent>
            </Select>
          </div>

          <!-- 日期范围 -->
          <div class="space-y-2">
            <Label>订单日期</Label>
            <Popover>
              <PopoverTrigger asChild>
                <Button variant="outline" class="w-full justify-start text-left font-normal">
                  <CalendarIcon class="mr-2 h-4 w-4" />
                  选择日期范围
                </Button>
              </PopoverTrigger>
              <PopoverContent class="w-auto p-0">
                <Calendar v-model="filters.dateRange" mode="range" />
              </PopoverContent>
            </Popover>
          </div>

          <!-- 搜索框 -->
          <div class="space-y-2">
            <Label>搜索</Label>
            <Input 
              v-model="filters.keyword" 
              placeholder="订单号、商品名称"
              class="w-full"
            />
          </div>

          <!-- 操作按钮 -->
          <div class="flex items-end">
            <Button @click="applyFilters" class="w-full">
              <Search class="mr-2 h-4 w-4" />
              搜索
            </Button>
          </div>
        </div>
      </CardContent>
    </Card>

    <!-- 订单列表 -->
    <Card>
      <CardHeader>
        <div class="flex items-center justify-between">
          <CardTitle>订单列表</CardTitle>
          <div class="flex items-center space-x-2">
            <Badge variant="secondary">总计: {{ orders.length }}</Badge>
          </div>
        </div>
      </CardHeader>
      <CardContent>
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead>订单号</TableHead>
              <TableHead>商品信息</TableHead>
              <TableHead>订单状态</TableHead>
              <TableHead>采购价格</TableHead>
              <TableHead>物流状态</TableHead>
              <TableHead>操作</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            <TableRow v-for="order in orders" :key="order.id">
              <TableCell class="font-medium">{{ order.orderNumber }}</TableCell>
              <TableCell>
                <div class="flex items-center space-x-3">
                  <img 
                    :src="order.productImage" 
                    :alt="order.productName"
                    class="h-12 w-12 rounded-md object-cover"
                  />
                  <div>
                    <p class="font-medium">{{ order.productName }}</p>
                    <p class="text-sm text-muted-foreground">
                      {{ order.color }} / {{ order.size }}
                    </p>
                  </div>
                </div>
              </TableCell>
              <TableCell>
                <Badge :variant="getStatusVariant(order.status)">
                  {{ getStatusText(order.status) }}
                </Badge>
              </TableCell>
              <TableCell>
                <div v-if="order.status === 'accepted'">
                  <Input 
                    v-model="order.purchasePrice"
                    type="number"
                    placeholder="输入采购价格"
                    class="w-24"
                  />
                </div>
                <div v-else>
                  {{ order.purchasePrice ? `¥${order.purchasePrice}` : '-' }}
                </div>
              </TableCell>
              <TableCell>
                <div class="flex items-center space-x-2">
                  <Badge variant="outline">{{ order.shippingStatus }}</Badge>
                  <Button 
                    v-if="order.trackingNumber"
                    variant="ghost" 
                    size="sm"
                    @click="trackOrder(order.trackingNumber)"
                  >
                    <Truck class="h-4 w-4" />
                  </Button>
                </div>
              </TableCell>
              <TableCell>
                <div class="flex items-center space-x-2">
                  <!-- 根据订单状态显示不同操作 -->
                  <Button 
                    v-if="order.status === 'pending'"
                    size="sm"
                    @click="acceptOrder(order.id)"
                  >
                    接单
                  </Button>
                  
                  <Button 
                    v-if="order.status === 'accepted' && order.purchasePrice"
                    size="sm"
                    @click="markAsPurchased(order.id)"
                  >
                    确认采购
                  </Button>
                  
                  <Button 
                    v-if="order.status === 'purchased'"
                    size="sm"
                    @click="printShippingLabel(order.id)"
                  >
                    <Printer class="mr-2 h-4 w-4" />
                    打印运单
                  </Button>

                  <DropdownMenu>
                    <DropdownMenuTrigger asChild>
                      <Button variant="ghost" size="sm">
                        <MoreHorizontal class="h-4 w-4" />
                      </Button>
                    </DropdownMenuTrigger>
                    <DropdownMenuContent>
                      <DropdownMenuItem @click="viewOrderDetail(order.id)">
                        查看详情
                      </DropdownMenuItem>
                      <DropdownMenuItem @click="editOrder(order.id)">
                        编辑订单
                      </DropdownMenuItem>
                    </DropdownMenuContent>
                  </DropdownMenu>
                </div>
              </TableCell>
            </TableRow>
          </TableBody>
        </Table>

        <!-- 分页 -->
        <div class="flex items-center justify-between mt-4">
          <div class="text-sm text-muted-foreground">
            显示 {{ (currentPage - 1) * pageSize + 1 }} 到 {{ Math.min(currentPage * pageSize, totalOrders) }} 条，共 {{ totalOrders }} 条
          </div>
          <div class="flex items-center space-x-2">
            <Button 
              variant="outline" 
              size="sm"
              :disabled="currentPage === 1"
              @click="currentPage--"
            >
              上一页
            </Button>
            <Button 
              variant="outline" 
              size="sm"
              :disabled="currentPage * pageSize >= totalOrders"
              @click="currentPage++"
            >
              下一页
            </Button>
          </div>
        </div>
      </CardContent>
    </Card>
  </div>
</template>
```

### 2. 商品管理页面
```vue
<template>
  <!-- 
    商品管理页面
    <AUTHOR>
    展示商品信息和库存状态，支持重要字段的颜色突出显示
  -->
  <div class="space-y-6">
    <!-- 页面头部 -->
    <div class="flex items-center justify-between">
      <div>
        <h1 class="text-3xl font-bold tracking-tight">商品管理</h1>
        <p class="text-muted-foreground">管理商品信息和库存状态</p>
      </div>
      <div class="flex items-center space-x-2">
        <Button variant="outline">
          <Download class="mr-2 h-4 w-4" />
          导出数据
        </Button>
        <Button>
          <Plus class="mr-2 h-4 w-4" />
          添加商品
        </Button>
      </div>
    </div>

    <!-- 统计卡片 -->
    <div class="grid grid-cols-1 md:grid-cols-4 gap-4">
      <Card>
        <CardHeader class="flex flex-row items-center justify-between space-y-0 pb-2">
          <CardTitle class="text-sm font-medium">总商品数</CardTitle>
          <Package class="h-4 w-4 text-muted-foreground" />
        </CardHeader>
        <CardContent>
          <div class="text-2xl font-bold">{{ stats.totalProducts }}</div>
          <p class="text-xs text-muted-foreground">
            +2.1% 较上月
          </p>
        </CardContent>
      </Card>

      <Card>
        <CardHeader class="flex flex-row items-center justify-between space-y-0 pb-2">
          <CardTitle class="text-sm font-medium">库存不足</CardTitle>
          <AlertTriangle class="h-4 w-4 text-orange-500" />
        </CardHeader>
        <CardContent>
          <div class="text-2xl font-bold text-orange-600">{{ stats.lowStock }}</div>
          <p class="text-xs text-muted-foreground">
            需要补货
          </p>
        </CardContent>
      </Card>

      <Card>
        <CardHeader class="flex flex-row items-center justify-between space-y-0 pb-2">
          <CardTitle class="text-sm font-medium">缺货商品</CardTitle>
          <XCircle class="h-4 w-4 text-red-500" />
        </CardHeader>
        <CardContent>
          <div class="text-2xl font-bold text-red-600">{{ stats.outOfStock }}</div>
          <p class="text-xs text-muted-foreground">
            急需补货
          </p>
        </CardContent>
      </Card>

      <Card>
        <CardHeader class="flex flex-row items-center justify-between space-y-0 pb-2">
          <CardTitle class="text-sm font-medium">库存总值</CardTitle>
          <DollarSign class="h-4 w-4 text-muted-foreground" />
        </CardHeader>
        <CardContent>
          <div class="text-2xl font-bold">¥{{ stats.totalValue.toLocaleString() }}</div>
          <p class="text-xs text-muted-foreground">
            +5.2% 较上月
          </p>
        </CardContent>
      </Card>
    </div>

    <!-- 商品列表 -->
    <Card>
      <CardHeader>
        <div class="flex items-center justify-between">
          <CardTitle>商品列表</CardTitle>
          <div class="flex items-center space-x-2">
            <Input 
              placeholder="搜索商品..." 
              class="w-64"
              v-model="searchKeyword"
            />
            <Select v-model="categoryFilter">
              <SelectTrigger class="w-40">
                <SelectValue placeholder="商品分类" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">全部分类</SelectItem>
                <SelectItem value="electronics">电子产品</SelectItem>
                <SelectItem value="clothing">服装</SelectItem>
                <SelectItem value="home">家居用品</SelectItem>
              </SelectContent>
            </Select>
          </div>
        </div>
      </CardHeader>
      <CardContent>
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead>商品信息</TableHead>
              <TableHead>SKU</TableHead>
              <TableHead>分类</TableHead>
              <TableHead>库存数量</TableHead>
              <TableHead>成本价格</TableHead>
              <TableHead>销售价格</TableHead>
              <TableHead>库存状态</TableHead>
              <TableHead>操作</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            <TableRow v-for="product in products" :key="product.id">
              <TableCell>
                <div class="flex items-center space-x-3">
                  <img 
                    :src="product.image" 
                    :alt="product.name"
                    class="h-12 w-12 rounded-md object-cover"
                  />
                  <div>
                    <p class="font-medium">{{ product.name }}</p>
                    <p class="text-sm text-muted-foreground">{{ product.description }}</p>
                  </div>
                </div>
              </TableCell>
              <TableCell>
                <code class="bg-muted px-2 py-1 rounded text-sm">{{ product.sku }}</code>
              </TableCell>
              <TableCell>
                <Badge variant="outline">{{ product.category }}</Badge>
              </TableCell>
              <TableCell>
                <!-- 库存数量颜色突出显示 -->
                <span 
                  :class="{
                    'text-red-600 font-bold': product.stock === 0,
                    'text-orange-600 font-semibold': product.stock > 0 && product.stock <= product.lowStockThreshold,
                    'text-green-600': product.stock > product.lowStockThreshold
                  }"
                >
                  {{ product.stock }}
                </span>
              </TableCell>
              <TableCell>
                <!-- 成本价格突出显示 -->
                <span class="font-semibold text-blue-600">
                  ¥{{ product.costPrice.toFixed(2) }}
                </span>
              </TableCell>
              <TableCell>
                <!-- 销售价格突出显示 -->
                <span class="font-semibold text-green-600">
                  ¥{{ product.salePrice.toFixed(2) }}
                </span>
              </TableCell>
              <TableCell>
                <Badge 
                  :variant="getStockStatusVariant(product.stock, product.lowStockThreshold)"
                >
                  {{ getStockStatusText(product.stock, product.lowStockThreshold) }}
                </Badge>
              </TableCell>
              <TableCell>
                <div class="flex items-center space-x-2">
                  <Button variant="ghost" size="sm" @click="editProduct(product.id)">
                    <Edit class="h-4 w-4" />
                  </Button>
                  <Button variant="ghost" size="sm" @click="viewInventoryHistory(product.id)">
                    <History class="h-4 w-4" />
                  </Button>
                  <DropdownMenu>
                    <DropdownMenuTrigger asChild>
                      <Button variant="ghost" size="sm">
                        <MoreHorizontal class="h-4 w-4" />
                      </Button>
                    </DropdownMenuTrigger>
                    <DropdownMenuContent>
                      <DropdownMenuItem @click="adjustStock(product.id)">
                        调整库存
                      </DropdownMenuItem>
                      <DropdownMenuItem @click="viewSalesHistory(product.id)">
                        销售记录
                      </DropdownMenuItem>
                      <DropdownMenuSeparator />
                      <DropdownMenuItem class="text-red-600" @click="deleteProduct(product.id)">
                        删除商品
                      </DropdownMenuItem>
                    </DropdownMenuContent>
                  </DropdownMenu>
                </div>
              </TableCell>
            </TableRow>
          </TableBody>
        </Table>
      </CardContent>
    </Card>
  </div>
</template>
```

### 3. 库存管理页面
```vue
<template>
  <!-- 
    库存管理页面
    <AUTHOR>
    展示入库和出库记录，支持数据可视化
  -->
  <div class="space-y-6">
    <!-- 页面头部 -->
    <div class="flex items-center justify-between">
      <div>
        <h1 class="text-3xl font-bold tracking-tight">库存管理</h1>
        <p class="text-muted-foreground">管理商品入库和出库记录</p>
      </div>
      <div class="flex items-center space-x-2">
        <Button variant="outline">
          <TrendingUp class="mr-2 h-4 w-4" />
          库存报表
        </Button>
        <Button>
          <Plus class="mr-2 h-4 w-4" />
          新增入库
        </Button>
      </div>
    </div>

    <!-- 库存概览图表 -->
    <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
      <Card>
        <CardHeader>
          <CardTitle>入库趋势</CardTitle>
        </CardHeader>
        <CardContent>
          <div id="inbound-chart" class="h-64"></div>
        </CardContent>
      </Card>

      <Card>
        <CardHeader>
          <CardTitle>出库趋势</CardTitle>
        </CardHeader>
        <CardContent>
          <div id="outbound-chart" class="h-64"></div>
        </CardContent>
      </Card>
    </div>

    <!-- 操作选项卡 -->
    <Tabs default-value="inbound" class="w-full">
      <TabsList class="grid w-full grid-cols-2">
        <TabsTrigger value="inbound">入库记录</TabsTrigger>
        <TabsTrigger value="outbound">出库记录</TabsTrigger>
      </TabsList>

      <!-- 入库记录 -->
      <TabsContent value="inbound" class="space-y-4">
        <Card>
          <CardHeader>
            <div class="flex items-center justify-between">
              <CardTitle>入库记录</CardTitle>
              <Button size="sm">
                <Plus class="mr-2 h-4 w-4" />
                新增入库
              </Button>
            </div>
          </CardHeader>
          <CardContent>
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>入库日期</TableHead>
                  <TableHead>商品信息</TableHead>
                  <TableHead>入库数量</TableHead>
                  <TableHead>采购成本</TableHead>
                  <TableHead>供应商</TableHead>
                  <TableHead>操作人</TableHead>
                  <TableHead>操作</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                <TableRow v-for="record in inboundRecords" :key="record.id">
                  <TableCell>{{ formatDate(record.date) }}</TableCell>
                  <TableCell>
                    <div class="flex items-center space-x-3">
                      <img 
                        :src="record.product.image" 
                        :alt="record.product.name"
                        class="h-10 w-10 rounded object-cover"
                      />
                      <div>
                        <p class="font-medium">{{ record.product.name }}</p>
                        <p class="text-sm text-muted-foreground">{{ record.product.sku }}</p>
                      </div>
                    </div>
                  </TableCell>
                  <TableCell>
                    <!-- 入库数量突出显示 -->
                    <span class="font-semibold text-green-600">
                      +{{ record.quantity }}
                    </span>
                  </TableCell>
                  <TableCell>
                    <!-- 采购成本突出显示 -->
                    <span class="font-semibold text-blue-600">
                      ¥{{ (record.unitCost * record.quantity).toFixed(2) }}
                    </span>
                    <div class="text-sm text-muted-foreground">
                      单价: ¥{{ record.unitCost.toFixed(2) }}
                    </div>
                  </TableCell>
                  <TableCell>
                    <Badge variant="outline">{{ record.supplier }}</Badge>
                  </TableCell>
                  <TableCell>{{ record.operator }}</TableCell>
                  <TableCell>
                    <div class="flex items-center space-x-2">
                      <Button variant="ghost" size="sm" @click="editInboundRecord(record.id)">
                        <Edit class="h-4 w-4" />
                      </Button>
                      <Button variant="ghost" size="sm" @click="viewInboundDetail(record.id)">
                        <Eye class="h-4 w-4" />
                      </Button>
                    </div>
                  </TableCell>
                </TableRow>
              </TableBody>
            </Table>
          </CardContent>
        </Card>
      </TabsContent>

      <!-- 出库记录 -->
      <TabsContent value="outbound" class="space-y-4">
        <Card>
          <CardHeader>
            <div class="flex items-center justify-between">
              <CardTitle>出库记录</CardTitle>
              <Button size="sm">
                <Minus class="mr-2 h-4 w-4" />
                新增出库
              </Button>
            </div>
          </CardHeader>
          <CardContent>
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>出库日期</TableHead>
                  <TableHead>商品信息</TableHead>
                  <TableHead>出库数量</TableHead>
                  <TableHead>销售金额</TableHead>
                  <TableHead>订单号</TableHead>
                  <TableHead>操作人</TableHead>
                  <TableHead>操作</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                <TableRow v-for="record in outboundRecords" :key="record.id">
                  <TableCell>{{ formatDate(record.date) }}</TableCell>
                  <TableCell>
                    <div class="flex items-center space-x-3">
                      <img 
                        :src="record.product.image" 
                        :alt="record.product.name"
                        class="h-10 w-10 rounded object-cover"
                      />
                      <div>
                        <p class="font-medium">{{ record.product.name }}</p>
                        <p class="text-sm text-muted-foreground">{{ record.product.sku }}</p>
                      </div>
                    </div>
                  </TableCell>
                  <TableCell>
                    <!-- 出库数量突出显示 -->
                    <span class="font-semibold text-red-600">
                      -{{ record.quantity }}
                    </span>
                  </TableCell>
                  <TableCell>
                    <!-- 销售金额突出显示 -->
                    <span class="font-semibold text-green-600">
                      ¥{{ (record.unitPrice * record.quantity).toFixed(2) }}
                    </span>
                    <div class="text-sm text-muted-foreground">
                      单价: ¥{{ record.unitPrice.toFixed(2) }}
                    </div>
                  </TableCell>
                  <TableCell>
                    <Button variant="link" size="sm" @click="viewOrder(record.orderId)">
                      {{ record.orderNumber }}
                    </Button>
                  </TableCell>
                  <TableCell>{{ record.operator }}</TableCell>
                  <TableCell>
                    <div class="flex items-center space-x-2">
                      <Button variant="ghost" size="sm" @click="editOutboundRecord(record.id)">
                        <Edit class="h-4 w-4" />
                      </Button>
                      <Button variant="ghost" size="sm" @click="viewOutboundDetail(record.id)">
                        <Eye class="h-4 w-4" />
                      </Button>
                    </div>
                  </TableCell>
                </TableRow>
              </TableBody>
            </Table>
          </CardContent>
        </Card>
      </TabsContent>
    </Tabs>
  </div>
</template>
```

## 表单页面构建

### 1. 表单布局规范
```vue
<template>
  <!-- 
    标准表单页面
    <AUTHOR>
    使用shadcn-vue的Form组件构建表单
  -->
  <div class="max-w-2xl mx-auto space-y-6">
    <Card>
      <CardHeader>
        <CardTitle>表单标题</CardTitle>
        <CardDescription>表单描述信息</CardDescription>
      </CardHeader>
      <CardContent>
        <form @submit="onSubmit" class="space-y-6">
          <!-- 基础信息组 -->
          <div class="space-y-4">
            <h3 class="text-lg font-medium">基础信息</h3>
            <Separator />
            
            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
              <FormField v-slot="{ componentField }" name="name">
                <FormItem>
                  <FormLabel>商品名称</FormLabel>
                  <FormControl>
                    <Input placeholder="请输入商品名称" v-bind="componentField" />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              </FormField>

              <FormField v-slot="{ componentField }" name="sku">
                <FormItem>
                  <FormLabel>SKU编码</FormLabel>
                  <FormControl>
                    <Input placeholder="请输入SKU编码" v-bind="componentField" />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              </FormField>
            </div>

            <FormField v-slot="{ componentField }" name="description">
              <FormItem>
                <FormLabel>商品描述</FormLabel>
                <FormControl>
                  <Textarea 
                    placeholder="请输入商品描述" 
                    class="min-h-[100px]"
                    v-bind="componentField" 
                  />
                </FormControl>
                <FormMessage />
              </FormItem>
            </FormField>
          </div>

          <!-- 价格信息组 -->
          <div class="space-y-4">
            <h3 class="text-lg font-medium">价格信息</h3>
            <Separator />
            
            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
              <FormField v-slot="{ componentField }" name="costPrice">
                <FormItem>
                  <FormLabel>成本价格</FormLabel>
                  <FormControl>
                    <Input 
                      type="number" 
                      step="0.01"
                      placeholder="0.00" 
                      v-bind="componentField" 
                    />
                  </FormControl>
                  <FormDescription>商品的采购成本价格</FormDescription>
                  <FormMessage />
                </FormItem>
              </FormField>

              <FormField v-slot="{ componentField }" name="salePrice">
                <FormItem>
                  <FormLabel>销售价格</FormLabel>
                  <FormControl>
                    <Input 
                      type="number" 
                      step="0.01"
                      placeholder="0.00" 
                      v-bind="componentField" 
                    />
                  </FormControl>
                  <FormDescription>商品的销售价格</FormDescription>
                  <FormMessage />
                </FormItem>
              </FormField>
            </div>
          </div>

          <!-- 操作按钮 -->
          <div class="flex items-center justify-end space-x-2 pt-4">
            <Button type="button" variant="outline" @click="goBack">
              取消
            </Button>
            <Button type="submit" :disabled="isSubmitting">
              <Loader2 v-if="isSubmitting" class="mr-2 h-4 w-4 animate-spin" />
              保存
            </Button>
          </div>
        </form>
      </CardContent>
    </Card>
  </div>
</template>
```

## 数据可视化集成

### 1. AntV G2Plot图表集成
```vue
<template>
  <!-- 
    数据可视化页面
    <AUTHOR>
    使用AntV G2Plot创建图表组件
  -->
  <div class="space-y-6">
    <!-- 图表容器 -->
    <Card>
      <CardHeader>
        <CardTitle>销售趋势分析</CardTitle>
      </CardHeader>
      <CardContent>
        <div id="sales-trend-chart" class="h-80"></div>
      </CardContent>
    </Card>

    <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
      <Card>
        <CardHeader>
          <CardTitle>商品分类占比</CardTitle>
        </CardHeader>
        <CardContent>
          <div id="category-pie-chart" class="h-64"></div>
        </CardContent>
      </Card>

      <Card>
        <CardHeader>
          <CardTitle>库存状态分布</CardTitle>
        </CardHeader>
        <CardContent>
          <div id="stock-status-chart" class="h-64"></div>
        </CardContent>
      </Card>
    </div>
  </div>
</template>

<script setup lang="ts">
import { Line, Pie, Column } from '@antv/g2plot'
import { onMounted, ref } from 'vue'

/**
 * 图表组件逻辑
 * <AUTHOR>
 * 使用AntV G2Plot创建各种图表
 */

const salesData = ref([
  { date: '2024-01', sales: 120000 },
  { date: '2024-02', sales: 150000 },
  { date: '2024-03', sales: 180000 },
  // ... 更多数据
])

onMounted(() => {
  // 销售趋势折线图
  const linePlot = new Line('sales-trend-chart', {
    data: salesData.value,
    xField: 'date',
    yField: 'sales',
    smooth: true,
    color: '#1890ff',
    point: {
      size: 4,
      shape: 'circle',
    },
    label: {
      style: {
        fill: '#aaa',
      },
    },
  })
  linePlot.render()

  // 商品分类饼图
  const piePlot = new Pie('category-pie-chart', {
    data: categoryData.value,
    angleField: 'value',
    colorField: 'category',
    radius: 0.8,
    label: {
      type: 'outer',
      content: '{name} {percentage}',
    },
  })
  piePlot.render()

  // 库存状态柱状图
  const columnPlot = new Column('stock-status-chart', {
    data: stockStatusData.value,
    xField: 'status',
    yField: 'count',
    color: ({ status }) => {
      if (status === '充足') return '#52c41a'
      if (status === '不足') return '#faad14'
      return '#ff4d4f'
    },
  })
  columnPlot.render()
})
</script>
```

## 响应式设计规范

### 1. 断点使用
```vue
<template>
  <!-- 
    响应式布局示例
    <AUTHOR>
    使用Tailwind CSS的响应式断点
  -->
  <div class="space-y-6">
    <!-- 移动端单列，平板双列，桌面四列 -->
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
      <Card v-for="item in items" :key="item.id">
        <!-- 卡片内容 -->
      </Card>
    </div>

    <!-- 移动端隐藏侧边栏，桌面端显示 -->
    <div class="flex">
      <aside class="hidden lg:block w-64 bg-white border-r">
        <!-- 侧边栏内容 -->
      </aside>
      <main class="flex-1 p-4 lg:p-6">
        <!-- 主要内容 -->
      </main>
    </div>

    <!-- 移动端使用Sheet，桌面端使用Dialog -->
    <Sheet v-if="isMobile">
      <SheetTrigger asChild>
        <Button>打开菜单</Button>
      </SheetTrigger>
      <SheetContent>
        <!-- 移动端菜单内容 -->
      </SheetContent>
    </Sheet>

    <Dialog v-else>
      <DialogTrigger asChild>
        <Button>打开对话框</Button>
      </DialogTrigger>
      <DialogContent>
        <!-- 桌面端对话框内容 -->
      </DialogContent>
    </Dialog>
  </div>
</template>
```

## 性能优化建议

### 1. 组件懒加载
```typescript
/**
 * 路由懒加载配置
 * <AUTHOR>
 * 使用动态导入实现组件懒加载
 */
import { createRouter, createWebHistory } from 'vue-router'

const router = createRouter({
  history: createWebHistory(),
  routes: [
    {
      path: '/orders',
      name: 'Orders',
      component: () => import('@/views/orders/OrderList.vue'),
    },
    {
      path: '/products',
      name: 'Products',
      component: () => import('@/views/products/ProductList.vue'),
    },
    {
      path: '/inventory',
      name: 'Inventory',
      component: () => import('@/views/inventory/InventoryManagement.vue'),
    },
  ],
})
```

### 2. 虚拟滚动
```vue
<template>
  <!-- 
    大数据量表格优化
    <AUTHOR>
    使用虚拟滚动处理大量数据
  -->
  <Card>
    <CardContent>
      <div class="h-96 overflow-auto">
        <Table>
          <TableHeader class="sticky top-0 bg-white">
            <TableRow>
              <TableHead>列标题</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            <TableRow 
              v-for="item in visibleItems" 
              :key="item.id"
              class="h-12"
            >
              <TableCell>{{ item.name }}</TableCell>
            </TableRow>
          </TableBody>
        </Table>
      </div>
    </CardContent>
  </Card>
</template>
```

## 总结

本文档详细描述了9Wings ERP系统的页面构建规范和最佳实践：

1. **严格遵循shadcn-vue组件库**：优先使用框架提供的组件
2. **禁止自定义CSS类**：全部使用Tailwind CSS原子类
3. **统一的页面结构**：标准化的布局和组件组织方式
4. **响应式设计**：适配不同设备和屏幕尺寸
5. **性能优化**：懒加载、虚拟滚动等优化策略
6. **类型安全**：完整的TypeScript类型定义
7. **可维护性**：清晰的代码结构和注释规范

通过遵循这些规范，确保项目的代码质量、用户体验和可维护性。