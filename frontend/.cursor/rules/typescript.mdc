---
description: 
globs: 
alwaysApply: true
---
# TypeScript 开发规范

## 1. 类型定义规范

### 1.1 基本类型注解
- 优先使用类型注解而不是类型断言
```typescript
// ✅ 推荐
const name: string = "<PERSON>";

// ❌ 避免
const name = "<PERSON>" as string;
```

### 1.2 接口和类型别名
- 接口名称使用大写字母 I 作为前缀
- 类型别名使用大写字母 T 作为前缀
```typescript
// ✅ 推荐
interface IUser {
  id: number;
  name: string;
}

type TResponse<T> = {
  data: T;
  status: number;
};
```

### 1.3 泛型使用
- 泛型参数使用有意义的名称
- 单个字母泛型参数仅用于简单场景
```typescript
// ✅ 推荐
function getData<TData>(api: string): Promise<TData> { ... }

// ❌ 避免
function getData<T>(api: string): Promise<T> { ... }
```

## 2. 命名规范

### 2.1 文件命名
- 组件文件使用 PascalCase：`UserProfile.tsx`
- 工具/服务文件使用 camelCase：`userService.ts`
- 类型定义文件使用 `.d.ts` 后缀：`api.d.ts`

### 2.2 变量命名
- 使用有意义的描述性名称
- 布尔值变量使用 is/has/should 等前缀
```typescript
// ✅ 推荐
const isLoading: boolean = true;
const hasPermission: boolean = false;

// ❌ 避免
const loading: boolean = true;
```

## 3. 代码组织

### 3.1 导入顺序
1. 第三方库导入
2. 绝对路径导入（使用 @/ 别名）
3. 相对路径导入
```typescript
// 绝对路径
import { md5 } from '@/core/utils';

// 相对路径
import { wxPost } from './request';
```

### 3.2 导出规范
- 优先使用命名导出而不是默认导出
```typescript
// ✅ 推荐
export const sum = (a: number, b: number): number => a + b;

// ❌ 避免
export default (a: number, b: number): number => a + b;
```

## 4. 错误处理

### 4.1 异步代码
- 使用 async/await 配合 try/catch
- 定义统一的错误处理类型
```typescript
interface IApiError {
  code: number;
  message: string;
}

async function fetchData(): Promise<void> {
  try {
    const response = await api.get('/data');
    return response.data;
  } catch (error: unknown) {
    const apiError = error as IApiError;
    handleError(apiError);
  }
}
```

## 5. 注释规范

### 5.1 文档注释
- 为公共 API 和复杂函数添加 JSDoc 注释
```typescript
/**
 * 计算两个数字的和
 * @param a - 第一个数字
 * @param b - 第二个数字
 * @returns 两个数字的和
 */
function sum(a: number, b: number): number {
  return a + b;
}
```

### 5.2 代码注释
- 只对复杂的业务逻辑添加注释
- 注释应该解释"为什么"而不是"是什么"

## 6. 项目配置

### 6.1 强制的编译选项
```json
{
  "compilerOptions": {
    "strict": true,
    "noImplicitAny": true,
    "strictNullChecks": true,
    "esModuleInterop": true
  }
}
```

### 6.2 ESLint 规则
- 启用 TypeScript 特定的 lint 规则
- 使用 Prettier 进行代码格式化
- 警告 any 的使用
- 警告未使用的变量

## 7. 最佳实践

### 7.1 null 和 undefined
- 优先使用 undefined 而不是 null
- 使用可选链操作符 (?.) 处理可能的 undefined 值

### 7.2 类型断言
- 只在确实必要时使用类型断言
- 优先使用类型收窄而不是类型断言
```typescript
// ✅ 推荐
if (typeof value === "string") {
  console.log(value.toUpperCase());
}

// ❌ 避免
console.log((value as string).toUpperCase());
```

### 7.3 常量管理
- 使用 const enum 定义常量
- 相关常量组织在一起
```typescript
const enum HttpStatus {
  OK = 200,
  NOT_FOUND = 404,
  SERVER_ERROR = 500
}
```

## 8. 性能考虑

### 8.1 类型优化
- 避免过度使用泛型
- 合理使用类型推断
- 避免不必要的类型计算

### 8.2 构建优化
- 使用 `skipLibCheck` 加快构建
- 合理配置 `include` 和 `exclude`