{"name": "frontend", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vue-tsc -b && vite build", "preview": "vite preview"}, "dependencies": {"@antv/g2plot": "^2.4.33", "@element-plus/icons-vue": "^2.3.1", "@internationalized/date": "^3.8.1", "@tailwindcss/vite": "^4.1.7", "@tanstack/vue-table": "^8.21.3", "@vee-validate/zod": "^4.15.0", "@vueuse/core": "^13.2.0", "axios": "^1.9.0", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "date-fns": "^4.1.0", "element-plus": "^2.9.10", "lucide-vue-next": "^0.511.0", "radix-vue": "^1.9.17", "reka-ui": "^2.2.1", "tailwind-merge": "^3.3.0", "tailwindcss": "^4.1.7", "tw-animate-css": "^1.3.0", "vee-validate": "^4.15.0", "vue": "^3.5.13", "vue-router": "4", "zod": "^3.25.28"}, "devDependencies": {"@iconify-json/tabler": "^1.2.18", "@iconify/vue": "^5.0.0", "@types/node": "^22.15.21", "@vitejs/plugin-vue": "^5.2.3", "@vue/tsconfig": "^0.7.0", "autoprefixer": "^10.4.21", "postcss": "^8.5.3", "typescript": "~5.8.3", "unplugin-icons": "^22.1.0", "vite": "^6.3.5", "vue-tsc": "^2.2.8"}, "packageManager": "pnpm@10.11.0+sha512.6540583f41cc5f628eb3d9773ecee802f4f9ef9923cc45b69890fb47991d4b092964694ec3a4f738a420c918a333062c8b925d312f42e4f0c263eb603551f977"}