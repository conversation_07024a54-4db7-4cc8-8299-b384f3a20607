{
	"compilerOptions": {
		"target": "ES2022",
		"module": "NodeNext",
		"lib": ["ES2022", "DOM"],
		"strict": true,
		"noImplicitAny": true,
		"strictNullChecks": true,
		"esModuleInterop": true,
		"skipLibCheck": true,
		"moduleResolution": "NodeNext",
		"allowSyntheticDefaultImports": true,
		"sourceMap": true,
		"outDir": "./dist",
		"baseUrl": ".",
		"paths": {
			"@/*": ["./src/*"]
		},
		"types": ["vite/client"]
	},
	"include": [
		"src/**/*",
		"vite.config.ts", // 显式包含vite.config.ts
		"env.d.ts",
		"plugins"
	],
	"exclude": ["node_modules", "dist"]
}
