# ERP系统Docker部署说明

本文档描述如何使用Docker Compose部署ERP系统的前端和后端。

## 准备工作

1. 确保服务器已安装Docker和Docker Compose
   ```bash
   # 检查Docker版本
   docker --version
   
   # 检查Docker Compose版本
   docker compose version
   ```

2. 将项目代码克隆到服务器上
   ```bash
   git clone <项目仓库地址> erp-system
   cd erp-system
   ```

## 部署步骤

1. 根据需要修改`docker-compose.yml`文件中的环境变量
   - 数据库用户名、密码
   - 数据库名称
   - 其他配置参数

2. 启动所有服务
   ```bash
   docker compose up -d
   ```

3. 检查服务状态
   ```bash
   docker compose ps
   ```

4. 查看服务日志
   ```bash
   # 查看所有服务日志
   docker compose logs
   
   # 查看特定服务日志
   docker compose logs frontend
   docker compose logs backend
   docker compose logs db
   
   # 持续查看日志
   docker compose logs -f
   ```

## 访问服务

- 前端: `http://<服务器IP>`
- 后端API: `http://<服务器IP>:8080`
- 数据库: `<服务器IP>:3306`（注意：生产环境建议不要直接暴露数据库端口）

## 常用操作

1. 停止所有服务
   ```bash
   docker compose down
   ```

2. 重启特定服务
   ```bash
   docker compose restart frontend
   docker compose restart backend
   ```

3. 更新镜像并重新部署
   ```bash
   git pull
   docker compose down
   docker compose build
   docker compose up -d
   ```

4. 备份数据库
   ```bash
   docker exec -it erp-system_db_1 mysqldump -u erp_user -perp_password erp_db > backup.sql
   ```

## 注意事项

1. 生产环境中应配置适当的安全策略，如使用HTTPS、限制数据库访问等
2. 定期备份数据库数据
3. 注意保护敏感的环境变量，不要将含有密码的配置文件提交到代码仓库

## 问题排查

1. 如果无法访问前端，检查
   - 防火墙设置
   - Nginx配置
   - 前端构建日志

2. 如果后端API无响应，检查
   - 后端服务日志
   - 数据库连接配置
   - API路由配置

3. 如果数据库连接失败，检查
   - 数据库服务状态
   - 用户名和密码配置
   - 网络连接设置 