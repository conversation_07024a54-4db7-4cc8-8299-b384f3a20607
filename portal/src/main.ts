/**
 * Vue3应用主入口文件
 * <AUTHOR>
 * 初始化Vue应用、路由和全局样式
 */
import { createApp } from "vue";
import { createRouter, createWebHistory } from "vue-router";
import App from "./App.vue";
import "./styles/globals.css";

// 导入页面组件
import HomeView from "./views/HomeView.vue";

/**
 * 路由配置
 * <AUTHOR>
 * 定义应用的路由规则
 */
const routes = [
	{
		path: "/",
		name: "Home",
		component: HomeView,
	},
];

/**
 * 创建路由实例
 * <AUTHOR>
 * 使用HTML5历史模式的路由
 */
const router = createRouter({
	history: createWebHistory(),
	routes,
});

/**
 * 创建并挂载Vue应用
 * <AUTHOR>
 * 配置路由并挂载到DOM
 */
const app = createApp(App);
app.use(router);
app.mount("#app");
