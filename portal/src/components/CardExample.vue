<template>
	<div class="p-6 space-y-6">
		<!-- 基础 Card 示例 -->
		<Card class="w-96">
			<CardHeader>
				<CardTitle>卡片标题</CardTitle>
				<CardDescription>这是卡片的描述信息</CardDescription>
			</CardHeader>
			<CardContent>
				<p>这里是卡片的主要内容。您可以在这里放置任何需要的内容。</p>
			</CardContent>
			<CardFooter>
				<p class="text-sm text-muted-foreground">卡片底部信息</p>
			</CardFooter>
		</Card>

		<!-- 项目创建卡片示例（参考官网） -->
		<Card class="w-96">
			<CardHeader>
				<CardTitle>创建项目</CardTitle>
				<CardDescription>一键部署您的新项目</CardDescription>
			</CardHeader>
			<CardContent class="space-y-4">
				<div class="space-y-2">
					<label class="text-sm font-medium">名称</label>
					<input
						class="w-full px-3 py-2 border border-border rounded-md"
						placeholder="输入项目名称"
					/>
				</div>
				<div class="space-y-2">
					<label class="text-sm font-medium">框架</label>
					<select class="w-full px-3 py-2 border border-border rounded-md">
						<option>选择框架</option>
						<option>Vue.js</option>
						<option>React</option>
						<option>Next.js</option>
					</select>
				</div>
			</CardContent>
			<CardFooter class="flex justify-between">
				<button class="px-4 py-2 border border-border rounded-md">取消</button>
				<button class="px-4 py-2 bg-primary text-primary-foreground rounded-md">
					部署
				</button>
			</CardFooter>
		</Card>

		<!-- 通知卡片示例 -->
		<Card class="w-96">
			<CardHeader>
				<CardTitle>通知</CardTitle>
				<CardDescription>您有 3 条未读消息</CardDescription>
			</CardHeader>
			<CardContent class="space-y-4">
				<div class="flex items-center space-x-4">
					<div class="w-2 h-2 bg-blue-500 rounded-full"></div>
					<div class="flex-1">
						<p class="text-sm font-medium">您的通话已确认</p>
						<p class="text-xs text-muted-foreground">1小时前</p>
					</div>
				</div>
				<div class="flex items-center space-x-4">
					<div class="w-2 h-2 bg-green-500 rounded-full"></div>
					<div class="flex-1">
						<p class="text-sm font-medium">您有新消息！</p>
						<p class="text-xs text-muted-foreground">1小时前</p>
					</div>
				</div>
				<div class="flex items-center space-x-4">
					<div class="w-2 h-2 bg-orange-500 rounded-full"></div>
					<div class="flex-1">
						<p class="text-sm font-medium">您的订阅即将到期</p>
						<p class="text-xs text-muted-foreground">2小时前</p>
					</div>
				</div>
			</CardContent>
			<CardFooter>
				<button
					class="w-full px-4 py-2 border border-border rounded-md text-sm"
				>
					标记全部为已读
				</button>
			</CardFooter>
		</Card>
	</div>
</template>

<script setup lang="ts">
import {
	Card,
	CardContent,
	CardDescription,
	CardFooter,
	CardHeader,
	CardTitle,
} from "@/components/ui/card";
</script>
