<!--
  首页组件
  <AUTHOR>
  展示shadcn-vue组件库和TailwindCSS样式的示例页面
-->
<template>
	<div class="container mx-auto p-6">
		<!-- 页面标题 -->
		<div class="mb-8">
			<h1 class="text-4xl font-bold tracking-tight text-foreground">
				Welcome to Portal
			</h1>
			<p class="text-xl text-muted-foreground mt-2">
				基于 Vue3 + Vite6 + ShadCN-Vue + TailwindCSS 构建的现代化页面
			</p>
		</div>

		<!-- 功能卡片网格 -->
		<div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mb-8">
			<!-- 技术栈卡片 -->
			<div
				class="bg-card border border-border rounded-lg p-6 shadow-sm hover:shadow-md transition-shadow"
			>
				<div class="flex items-center mb-4">
					<div
						class="h-8 w-8 bg-primary rounded-md flex items-center justify-center"
					>
						<span class="text-primary-foreground text-sm font-bold">V</span>
					</div>
					<h3 class="text-lg font-semibold ml-3 text-card-foreground">Vue 3</h3>
				</div>
				<p class="text-muted-foreground text-sm">
					使用最新的 Composition API 和 setup 语法，提供更好的开发体验
				</p>
			</div>

			<!-- Vite卡片 -->
			<div
				class="bg-card border border-border rounded-lg p-6 shadow-sm hover:shadow-md transition-shadow"
			>
				<div class="flex items-center mb-4">
					<div
						class="h-8 w-8 bg-accent rounded-md flex items-center justify-center"
					>
						<span class="text-accent-foreground text-sm font-bold">⚡</span>
					</div>
					<h3 class="text-lg font-semibold ml-3 text-card-foreground">
						Vite 6
					</h3>
				</div>
				<p class="text-muted-foreground text-sm">
					极速的构建工具，提供快速的热重载和优化的生产构建
				</p>
			</div>

			<!-- ShadCN-Vue卡片 -->
			<div
				class="bg-card border border-border rounded-lg p-6 shadow-sm hover:shadow-md transition-shadow"
			>
				<div class="flex items-center mb-4">
					<div
						class="h-8 w-8 bg-secondary rounded-md flex items-center justify-center"
					>
						<span class="text-secondary-foreground text-sm font-bold">UI</span>
					</div>
					<h3 class="text-lg font-semibold ml-3 text-card-foreground">
						ShadCN-Vue
					</h3>
				</div>
				<p class="text-muted-foreground text-sm">
					美观且功能强大的组件库，提供一致的设计语言
				</p>
			</div>
		</div>

		<!-- 操作按钮区域 -->
		<div class="flex flex-wrap gap-4 mb-8">
			<button
				@click="handlePrimaryAction"
				class="inline-flex items-center justify-center rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 bg-primary text-primary-foreground hover:bg-primary/90 h-10 px-4 py-2"
			>
				主要操作
			</button>

			<button
				@click="handleSecondaryAction"
				class="inline-flex items-center justify-center rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 border border-input bg-background hover:bg-accent hover:text-accent-foreground h-10 px-4 py-2"
			>
				次要操作
			</button>

			<button
				@click="handleDestructiveAction"
				class="inline-flex items-center justify-center rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 bg-destructive text-destructive-foreground hover:bg-destructive/90 h-10 px-4 py-2"
			>
				删除操作
			</button>
		</div>

		<!-- 状态展示区域 -->
		<div class="bg-muted rounded-lg p-6">
			<h3 class="text-lg font-semibold mb-4 text-foreground">当前状态</h3>
			<div class="space-y-2">
				<p class="text-sm text-muted-foreground">
					<span class="font-medium">点击次数:</span> {{ clickCount }}
				</p>
				<p class="text-sm text-muted-foreground">
					<span class="font-medium">最后操作:</span> {{ lastAction || "无" }}
				</p>
				<p class="text-sm text-muted-foreground">
					<span class="font-medium">时间:</span> {{ currentTime }}
				</p>
			</div>
		</div>
	</div>
</template>

<script setup lang="ts">
/**
 * 首页组件逻辑
 * <AUTHOR>
 * 使用Vue3 Composition API实现交互功能
 */
import { ref, onMounted, onUnmounted } from "vue";

// 响应式数据定义
const clickCount = ref<number>(0);
const lastAction = ref<string>("");
const currentTime = ref<string>("");

// 定时器引用
let timer: number | null = null;

/**
 * 主要操作处理函数
 * <AUTHOR>
 * 处理主要按钮点击事件
 */
const handlePrimaryAction = (): void => {
	clickCount.value++;
	lastAction.value = "主要操作";
	console.log("执行主要操作");
};

/**
 * 次要操作处理函数
 * <AUTHOR>
 * 处理次要按钮点击事件
 */
const handleSecondaryAction = (): void => {
	clickCount.value++;
	lastAction.value = "次要操作";
	console.log("执行次要操作");
};

/**
 * 危险操作处理函数
 * <AUTHOR>
 * 处理删除按钮点击事件
 */
const handleDestructiveAction = (): void => {
	clickCount.value++;
	lastAction.value = "删除操作";
	console.log("执行删除操作");
};

/**
 * 更新当前时间
 * <AUTHOR>
 * 格式化并更新显示的时间
 */
const updateTime = (): void => {
	currentTime.value = new Date().toLocaleString("zh-CN");
};

/**
 * 组件挂载时的初始化操作
 * <AUTHOR>
 * 设置定时器更新时间
 */
onMounted(() => {
	updateTime();
	timer = window.setInterval(updateTime, 1000);
});

/**
 * 组件卸载时的清理操作
 * <AUTHOR>
 * 清除定时器防止内存泄漏
 */
onUnmounted(() => {
	if (timer) {
		clearInterval(timer);
	}
});
</script>
