<!--
  9-Wings电商平台主页
  <AUTHOR>
  展示俄罗斯三大电商平台的专业介绍
-->
<template>
	<div
		class="min-h-screen bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-50"
	>
		<!-- 头部导航 -->
		<header
			class="bg-white/80 backdrop-blur-md border-b border-slate-200 sticky top-0 z-50"
		>
			<div class="py-4 px-6">
				<div class="flex items-center justify-between">
					<div>
						<h1 class="text-2xl font-bold text-slate-900">9-Wings</h1>
						<p class="text-sm text-blue-600 font-medium">专业电商渠道服务商</p>
					</div>
				</div>
			</div>
		</header>

		<!-- 主要内容区域 -->
		<main class="w-full py-6 flex flex-col justify-center">
			<!-- 标题区域 -->
			<div class="text-center mb-16">
				<h2 class="text-4xl md:text-5xl font-bold text-slate-900 mb-6">
					俄罗斯电商平台
					<span
						class="bg-gradient-to-r from-blue-600 to-indigo-600 bg-clip-text text-transparent"
					>
						专业导航
					</span>
				</h2>
				<p class="text-xl text-slate-600 max-w-3xl mx-auto leading-relaxed">
					深耕俄罗斯电商市场，为您精选三大核心平台，助力跨境电商业务腾飞
				</p>
			</div>

			<!-- 平台介绍卡片 -->
			<section
				id="platforms"
				class="flex-1 flex flex-row justify-around mb-6 px-4 gap-2"
			>
				<!-- Ozon平台 -->
				<Card
					class="flex-1 transition-all duration-300 hover:shadow-lg hover:-translate-y-1"
				>
					<CardHeader class="text-center">
						<div class="flex justify-center items-center mb-4 h-16">
							<img
								src="/images/Ozon_logo.png"
								alt="Ozon Logo"
								class="h-10 w-auto max-w-[100px] object-contain transition-transform duration-300 hover:scale-110"
							/>
						</div>
						<CardDescription>俄罗斯全品类电商领航者</CardDescription>
					</CardHeader>
					<CardContent class="text-center space-y-2">
						<p>• 成立于1998年，最大综合电商平台</p>
						<p>• SKU超1亿，覆盖全品类</p>
						<p>• 超6000万月活用户</p>
					</CardContent>
					<CardFooter class="justify-center">
						<span class="text-sm text-muted-foreground">跨境商家首选</span>
					</CardFooter>
				</Card>

				<!-- Wildberries平台 -->
				<Card
					class="flex-1 transition-all duration-300 hover:shadow-lg hover:-translate-y-1"
				>
					<CardHeader class="text-center">
						<div class="flex justify-center items-center mb-4 h-16">
							<img
								src="/images/Wildberries_logo.png"
								alt="Wildberries Logo"
								class="h-10 w-auto max-w-[100px] object-contain transition-transform duration-300 hover:scale-110"
							/>
						</div>
						<CardDescription>时尚与性价比之王</CardDescription>
					</CardHeader>
					<CardContent class="text-center space-y-2">
						<p>• 俄罗斯订单量第一（2023）</p>
						<p>• 主打时尚品类，高性价比</p>
						<p>• 70%女性用户，深耕下沉市场</p>
					</CardContent>
					<CardFooter class="justify-center">
						<span class="text-sm text-muted-foreground">时尚品类优选</span>
					</CardFooter>
				</Card>

				<!-- Yandex Market平台 -->
				<Card
					class="flex-1 transition-all duration-300 hover:shadow-lg hover:-translate-y-1"
				>
					<CardHeader class="text-center">
						<div class="flex justify-center items-center mb-4 h-16">
							<img
								src="/images/Yandex_Market_logo.png"
								alt="Yandex Market Logo"
								class="h-10 w-auto max-w-[100px] object-contain transition-transform duration-300 hover:scale-110"
							/>
						</div>
						<CardDescription>技术驱动的智能购物平台</CardDescription>
					</CardHeader>
					<CardContent class="text-center space-y-2">
						<p>• 背靠Yandex搜索引擎技术</p>
						<p>• AI比价引擎，聚合10万+卖家</p>
						<p>• 电子产品与家电重点品类</p>
					</CardContent>
					<CardFooter class="justify-center">
						<span class="text-sm text-muted-foreground">智能科技领先</span>
					</CardFooter>
				</Card>
			</section>

			<!-- 服务优势 -->
			<section
				id="services"
				class="relative bg-gradient-to-br from-slate-900 via-blue-900 to-indigo-900 rounded-3xl p-8 mb-16 overflow-hidden"
			>
				<!-- 背景装饰 -->
				<div
					class="absolute inset-0 bg-gradient-to-r from-blue-600/10 to-purple-600/10"
				></div>
				<div class="absolute top-0 left-0 w-full h-full">
					<div
						class="absolute top-10 left-10 w-20 h-20 bg-blue-400/20 rounded-full blur-xl"
					></div>
					<div
						class="absolute bottom-10 right-10 w-32 h-32 bg-purple-400/20 rounded-full blur-2xl"
					></div>
					<div
						class="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-40 h-40 bg-indigo-400/10 rounded-full blur-3xl"
					></div>
				</div>

				<div class="relative z-10 text-center mb-12">
					<h3
						class="text-4xl font-bold bg-gradient-to-r from-white via-blue-100 to-indigo-200 bg-clip-text text-transparent mb-6"
					>
						为什么选择9-Wings
					</h3>
					<div
						class="w-24 h-1 bg-gradient-to-r from-blue-400 to-purple-400 mx-auto mb-6 rounded-full"
					></div>
					<p class="text-slate-300 text-lg max-w-2xl mx-auto leading-relaxed">
						专业团队，专业服务，助您在俄罗斯电商市场获得成功
					</p>
				</div>
				<div class="relative z-10 flex justify-between space-x-6">
					<Card
						class="flex-1 bg-white/10 backdrop-blur-sm border-white/20 group hover:bg-white/20 transition-all duration-300 hover:-translate-y-2"
					>
						<CardHeader>
							<div
								class="w-16 h-16 bg-gradient-to-br from-blue-400 to-cyan-400 rounded-2xl flex items-center justify-center mx-auto mb-4 shadow-lg group-hover:shadow-blue-400/50 group-hover:scale-110 transition-all duration-300"
							>
								<span class="text-white text-2xl">🎯</span>
							</div>
							<CardTitle class="text-white text-center">精准定位</CardTitle>
						</CardHeader>
						<CardContent>
							<p class="text-slate-300 text-sm text-center leading-relaxed">
								深度分析俄罗斯市场，为您量身定制解决方案
							</p>
						</CardContent>
					</Card>

					<Card
						class="flex-1 bg-white/10 backdrop-blur-sm border-white/20 group hover:bg-white/20 transition-all duration-300 hover:-translate-y-2"
					>
						<CardHeader>
							<div
								class="w-16 h-16 bg-gradient-to-br from-purple-400 to-pink-400 rounded-2xl flex items-center justify-center mx-auto mb-4 shadow-lg group-hover:shadow-purple-400/50 group-hover:scale-110 transition-all duration-300"
							>
								<span class="text-white text-2xl">🚀</span>
							</div>
							<CardTitle class="text-white text-center">快速上线</CardTitle>
						</CardHeader>
						<CardContent>
							<p class="text-slate-300 text-sm text-center leading-relaxed">
								专业团队支持，助您快速进入俄罗斯电商市场
							</p>
						</CardContent>
					</Card>

					<Card
						class="flex-1 bg-white/10 backdrop-blur-sm border-white/20 group hover:bg-white/20 transition-all duration-300 hover:-translate-y-2"
					>
						<CardHeader>
							<div
								class="w-16 h-16 bg-gradient-to-br from-green-400 to-emerald-400 rounded-2xl flex items-center justify-center mx-auto mb-4 shadow-lg group-hover:shadow-green-400/50 group-hover:scale-110 transition-all duration-300"
							>
								<span class="text-white text-2xl">📈</span>
							</div>
							<CardTitle class="text-white text-center">持续增长</CardTitle>
						</CardHeader>
						<CardContent>
							<p class="text-slate-300 text-sm text-center leading-relaxed">
								数据驱动运营优化，实现业务持续增长
							</p>
						</CardContent>
					</Card>

					<Card
						class="flex-1 bg-white/10 backdrop-blur-sm border-white/20 group hover:bg-white/20 transition-all duration-300 hover:-translate-y-2"
					>
						<CardHeader>
							<div
								class="w-16 h-16 bg-gradient-to-br from-orange-400 to-red-400 rounded-2xl flex items-center justify-center mx-auto mb-4 shadow-lg group-hover:shadow-orange-400/50 group-hover:scale-110 transition-all duration-300"
							>
								<span class="text-white text-2xl">🤝</span>
							</div>
							<CardTitle class="text-white text-center">全程护航</CardTitle>
						</CardHeader>
						<CardContent>
							<p class="text-slate-300 text-sm text-center leading-relaxed">
								7×24专业服务，为您的业务保驾护航
							</p>
						</CardContent>
					</Card>
				</div>
			</section>
		</main>

		<!-- 固定底部备案信息 -->
		<footer class="bg-slate-900 text-white py-6 mt-auto">
			<div class="container mx-auto px-6 text-center">
				<p class="text-slate-400 text-sm mb-2">
					专业电商渠道服务商 | 助力中国品牌出海俄罗斯
				</p>
				<p class="text-slate-500 text-xs">
					<a
						href="https://beian.miit.gov.cn"
						class="hover:text-white transition-colors"
						>粤ICP备2025416570号-1</a
					>
				</p>
			</div>
		</footer>

		<router-view />
	</div>
</template>

<script setup lang="ts">
/**
 * 9-Wings电商平台主页逻辑
 * <AUTHOR>
 * 展示俄罗斯三大电商平台的专业介绍
 */
import {
	Card,
	CardContent,
	CardDescription,
	CardFooter,
	CardHeader,
	CardTitle,
} from "./components/ui/card";
</script>

<style scoped>
/* 平滑滚动 */
html {
	scroll-behavior: smooth;
}
</style>
