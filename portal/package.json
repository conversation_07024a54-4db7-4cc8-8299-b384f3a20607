{"name": "portal", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vue-tsc --noEmit && vite build", "preview": "vite preview"}, "dependencies": {"@vueuse/core": "^11.3.0", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "lucide-vue-next": "^0.468.0", "radix-vue": "^1.9.7", "tailwind-merge": "^2.6.0", "tw-animate-css": "^1.3.4", "vue": "^3.5.13", "vue-router": "^4.5.0"}, "devDependencies": {"@tailwindcss/vite": "^4.0.0", "@types/node": "^22.10.2", "@vitejs/plugin-vue": "^5.2.1", "autoprefixer": "^10.4.20", "postcss": "^8.5.1", "tailwindcss": "^4.0.0", "typescript": "~5.6.3", "vite": "^6.0.3", "vue-tsc": "^2.1.10"}}