# 依赖文件
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*
pnpm-debug.log*

# 构建输出
dist/
dist-ssr/
*.local

# 编辑器和IDE
.vscode/
.idea/
*.swp
*.swo
*~

# 操作系统
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# 环境变量
.env
.env.local
.env.development.local
.env.test.local
.env.production.local

# 日志文件
logs/
*.log

# 缓存文件
.cache/
.parcel-cache/
.eslintcache

# TypeScript
*.tsbuildinfo

# Vite
vite.config.js.timestamp-*
vite.config.ts.timestamp-* 