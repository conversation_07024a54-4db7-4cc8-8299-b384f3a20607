---
description: 
globs: 
alwaysApply: false
---
# Portal - 现代化Vue3应用

基于 Vue3 + Vite6 + ShadCN-Vue + TailwindCSS 构建的现代化单页应用。

## 技术栈

- **Vue 3** - 采用 Composition API 和 `<script setup>` 语法
- **Vite 6** - 极速的构建工具和开发服务器
- **TypeScript** - 类型安全的JavaScript超集
- **TailwindCSS 4** - 实用优先的CSS框架
- **ShadCN-Vue** - 基于Radix Vue的美观组件库
- **Vue Router 4** - Vue.js官方路由管理器

## 项目结构

```
portal/
├── src/
│   ├── views/          # 页面组件
│   ├── styles/         # 全局样式
│   ├── App.vue         # 根组件
│   ├── main.ts         # 应用入口
│   └── shims-vue.d.ts  # Vue类型声明
├── public/             # 静态资源
├── index.html          # HTML模板
├── vite.config.ts      # Vite配置
├── tailwind.config.js  # TailwindCSS配置
├── tsconfig.json       # TypeScript配置
└── package.json        # 项目依赖
```

## 开发指南

### 安装依赖

```bash
pnpm install
```

### 启动开发服务器

```bash
pnpm dev
```

应用将在 `http://localhost:3000` 启动。

### 构建生产版本

```bash
pnpm build
```

### 预览构建结果

```bash
pnpm preview
```

## 功能特性

- ✅ Vue3 Composition API
- ✅ TypeScript支持
- ✅ 热重载开发体验
- ✅ TailwindCSS实用样式
- ✅ ShadCN-Vue组件系统
- ✅ 响应式设计
- ✅ 深色/浅色主题支持
- ✅ 现代化构建工具链

## 开发规范

- 使用 `<script setup>` 语法编写组件
- 遵循 TypeScript 类型安全原则
- 使用 TailwindCSS 类名而非自定义CSS
- 优先使用 ShadCN-Vue 组件
- 代码注释标注作者为 `<AUTHOR>

## 浏览器支持

- Chrome >= 87
- Firefox >= 78
- Safari >= 14
- Edge >= 88

## 作者

**Holk** - 项目开发与维护

---

更多信息请参考各个技术栈的官方文档。